<!doctype html>
<html class="no-js" lang="en" data-content_root="../../../">
  <head><meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width,initial-scale=1"/>
    <meta name="color-scheme" content="light dark"><link rel="index" title="Index" href="../../../genindex.html" /><link rel="search" title="Search" href="../../../search.html" />

    <!-- Generated with Sphinx 7.2.6 and Furo 2023.09.10 -->
        <title>nodriver.cdp.security - nodriver documentation</title>
      <link rel="stylesheet" type="text/css" href="../../../_static/pygments.css?v=fa44fd50" />
    <link rel="stylesheet" type="text/css" href="../../../_static/styles/furo.css?v=135e06be" />
    <link rel="stylesheet" type="text/css" href="../../../_static/styles/furo-extensions.css?v=36a5483c" />
    <link rel="stylesheet" type="text/css" href="../../../_static/./custom.css?v=3934642d" />
    
    


<style>
  body {
    --color-code-background: #eeffcc;
  --color-code-foreground: black;
  
  }
  @media not print {
    body[data-theme="dark"] {
      --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8eefba;
  --color-background-primary: #111112;
  --color-problematic: #e7aeae;
  
    }
    @media (prefers-color-scheme: dark) {
      body:not([data-theme="light"]) {
        --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8eefba;
  --color-background-primary: #111112;
  --color-problematic: #e7aeae;
  
      }
    }
  }
</style></head>
  <body>
    
    <script>
      document.body.dataset.theme = localStorage.getItem("theme") || "auto";
    </script>
    

<svg xmlns="http://www.w3.org/2000/svg" style="display: none;">
  <symbol id="svg-toc" viewBox="0 0 24 24">
    <title>Contents</title>
    <svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 1024 1024">
      <path d="M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM115.4 518.9L271.7 642c5.8 4.6 14.4.5 14.4-6.9V388.9c0-7.4-8.5-11.5-14.4-6.9L115.4 505.1a8.74 8.74 0 0 0 0 13.8z"/>
    </svg>
  </symbol>
  <symbol id="svg-menu" viewBox="0 0 24 24">
    <title>Menu</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-menu">
      <line x1="3" y1="12" x2="21" y2="12"></line>
      <line x1="3" y1="6" x2="21" y2="6"></line>
      <line x1="3" y1="18" x2="21" y2="18"></line>
    </svg>
  </symbol>
  <symbol id="svg-arrow-right" viewBox="0 0 24 24">
    <title>Expand</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-chevron-right">
      <polyline points="9 18 15 12 9 6"></polyline>
    </svg>
  </symbol>
  <symbol id="svg-sun" viewBox="0 0 24 24">
    <title>Light mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="feather-sun">
      <circle cx="12" cy="12" r="5"></circle>
      <line x1="12" y1="1" x2="12" y2="3"></line>
      <line x1="12" y1="21" x2="12" y2="23"></line>
      <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
      <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
      <line x1="1" y1="12" x2="3" y2="12"></line>
      <line x1="21" y1="12" x2="23" y2="12"></line>
      <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
      <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
    </svg>
  </symbol>
  <symbol id="svg-moon" viewBox="0 0 24 24">
    <title>Dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-moon">
      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
      <path d="M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z" />
    </svg>
  </symbol>
  <symbol id="svg-sun-half" viewBox="0 0 24 24">
    <title>Auto light/dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-shadow">
      <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
      <circle cx="12" cy="12" r="9" />
      <path d="M13 12h5" />
      <path d="M13 15h4" />
      <path d="M13 18h1" />
      <path d="M13 9h4" />
      <path d="M13 6h1" />
    </svg>
  </symbol>
</svg>

<input type="checkbox" class="sidebar-toggle" name="__navigation" id="__navigation">
<input type="checkbox" class="sidebar-toggle" name="__toc" id="__toc">
<label class="overlay sidebar-overlay" for="__navigation">
  <div class="visually-hidden">Hide navigation sidebar</div>
</label>
<label class="overlay toc-overlay" for="__toc">
  <div class="visually-hidden">Hide table of contents sidebar</div>
</label>



<div class="page">
  <header class="mobile-header">
    <div class="header-left">
      <label class="nav-overlay-icon" for="__navigation">
        <div class="visually-hidden">Toggle site navigation sidebar</div>
        <i class="icon"><svg><use href="#svg-menu"></use></svg></i>
      </label>
    </div>
    <div class="header-center">
      <a href="../../../index.html"><div class="brand">nodriver  documentation</div></a>
    </div>
    <div class="header-right">
      <div class="theme-toggle-container theme-toggle-header">
        <button class="theme-toggle">
          <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
          <svg class="theme-icon-when-auto"><use href="#svg-sun-half"></use></svg>
          <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
          <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
        </button>
      </div>
      <label class="toc-overlay-icon toc-header-icon no-toc" for="__toc">
        <div class="visually-hidden">Toggle table of contents sidebar</div>
        <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
      </label>
    </div>
  </header>
  <aside class="sidebar-drawer">
    <div class="sidebar-container">
      
      <div class="sidebar-sticky"><div class="sidebar-scroll"><a class="sidebar-brand" href="../../../index.html">
  
  
  <span class="sidebar-brand-text">nodriver  documentation</span>
  
</a><form class="sidebar-search-container" method="get" action="../../../search.html" role="search">
  <input class="sidebar-search" placeholder="Search" name="q" aria-label="Search">
  <input type="hidden" name="check_keywords" value="yes">
  <input type="hidden" name="area" value="default">
</form>
<div id="searchbox"></div><div class="sidebar-tree">
  <ul>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html">Quickstart guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#usage-example">usage example</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#more-complete-example">More complete example</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#custom-starting-options">Custom starting options</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#alternative-custom-options">Alternative custom options</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#proxies-socks5-authenticated-too">Proxies (socks5, authenticated too)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#concrete-example">Concrete example</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/classes/browser.html">Browser class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/classes/tab.html">Tab class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/classes/element.html">Element class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/classes/others_and_helpers.html">Other classes and Helper classes</a></li>
</ul>
<ul>
<li class="toctree-l1 has-children"><a class="reference internal" href="../../../nodriver/cdp.html">CDP object</a><input class="toctree-checkbox" id="toctree-checkbox-1" name="toctree-checkbox-1" role="switch" type="checkbox"/><label for="toctree-checkbox-1"><div class="visually-hidden">Toggle navigation of CDP object</div><i class="icon"><svg><use href="#svg-arrow-right"></use></svg></i></label><ul>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/accessibility.html">Accessibility</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/animation.html">Animation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/audits.html">Audits</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/autofill.html">Autofill</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/background_service.html">BackgroundService</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/bluetooth_emulation.html">BluetoothEmulation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/browser.html">Browser</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/cache_storage.html">CacheStorage</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/cast.html">Cast</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/console.html">Console</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/css.html">CSS</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/debugger.html">Debugger</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/device_access.html">DeviceAccess</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/device_orientation.html">DeviceOrientation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/dom.html">DOM</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/dom_debugger.html">DOMDebugger</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/dom_snapshot.html">DOMSnapshot</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/dom_storage.html">DOMStorage</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/emulation.html">Emulation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/event_breakpoints.html">EventBreakpoints</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/extensions.html">Extensions</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/fed_cm.html">FedCm</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/fetch.html">Fetch</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/file_system.html">FileSystem</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/headless_experimental.html">HeadlessExperimental</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/heap_profiler.html">HeapProfiler</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/indexed_db.html">IndexedDB</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/input_.html">Input</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/inspector.html">Inspector</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/io.html">IO</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/layer_tree.html">LayerTree</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/log.html">Log</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/media.html">Media</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/memory.html">Memory</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/network.html">Network</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/overlay.html">Overlay</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/page.html">Page</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/performance.html">Performance</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/performance_timeline.html">PerformanceTimeline</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/preload.html">Preload</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/profiler.html">Profiler</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/pwa.html">PWA</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/runtime.html">Runtime</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/schema.html">Schema</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/security.html">Security</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/service_worker.html">ServiceWorker</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/storage.html">Storage</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/system_info.html">SystemInfo</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/target.html">Target</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/tethering.html">Tethering</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/tracing.html">Tracing</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/web_audio.html">WebAudio</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/web_authn.html">WebAuthn</a></li>
</ul>
</li>
</ul>

</div>
</div>
      </div>
      
    </div>
  </aside>
  <div class="main">
    <div class="content">
      <div class="article-container">
        <a href="#" class="back-to-top muted-link">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
            <path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8v12z"></path>
          </svg>
          <span>Back to top</span>
        </a>
        <div class="content-icon-container">
          <div class="theme-toggle-container theme-toggle-content">
            <button class="theme-toggle">
              <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
              <svg class="theme-icon-when-auto"><use href="#svg-sun-half"></use></svg>
              <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
              <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
            </button>
          </div>
          <label class="toc-overlay-icon toc-content-icon no-toc" for="__toc">
            <div class="visually-hidden">Toggle table of contents sidebar</div>
            <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
          </label>
        </div>
        <article role="main">
          <h1>Source code for nodriver.cdp.security</h1><div class="highlight"><pre>
<span></span><span class="c1"># DO NOT EDIT THIS FILE!</span>
<span class="c1">#</span>
<span class="c1"># This file is generated from the CDP specification. If you need to make</span>
<span class="c1"># changes, edit the generator and regenerate all of the modules.</span>
<span class="c1">#</span>
<span class="c1"># CDP domain: Security</span>

<span class="kn">from</span> <span class="nn">__future__</span> <span class="kn">import</span> <span class="n">annotations</span>

<span class="kn">import</span> <span class="nn">enum</span>
<span class="kn">import</span> <span class="nn">typing</span>
<span class="kn">from</span> <span class="nn">dataclasses</span> <span class="kn">import</span> <span class="n">dataclass</span>

<span class="kn">from</span> <span class="nn">deprecated.sphinx</span> <span class="kn">import</span> <span class="n">deprecated</span>  <span class="c1"># type: ignore</span>

<span class="kn">from</span> <span class="nn">.</span> <span class="kn">import</span> <span class="n">network</span>
<span class="kn">from</span> <span class="nn">.util</span> <span class="kn">import</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">event_class</span>


<div class="viewcode-block" id="CertificateId">
<a class="viewcode-back" href="../../../nodriver/cdp/security.html#nodriver.cdp.security.CertificateId">[docs]</a>
<span class="k">class</span> <span class="nc">CertificateId</span><span class="p">(</span><span class="nb">int</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    An internal certificate ID value.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">int</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">int</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">CertificateId</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span>

    <span class="k">def</span> <span class="fm">__repr__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="s2">&quot;CertificateId(</span><span class="si">{}</span><span class="s2">)&quot;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="fm">__repr__</span><span class="p">())</span></div>



<div class="viewcode-block" id="MixedContentType">
<a class="viewcode-back" href="../../../nodriver/cdp/security.html#nodriver.cdp.security.MixedContentType">[docs]</a>
<span class="k">class</span> <span class="nc">MixedContentType</span><span class="p">(</span><span class="n">enum</span><span class="o">.</span><span class="n">Enum</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    A description of mixed content (HTTP resources on HTTPS pages), as defined by</span>
<span class="sd">    https://www.w3.org/TR/mixed-content/#categories</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">BLOCKABLE</span> <span class="o">=</span> <span class="s2">&quot;blockable&quot;</span>
    <span class="n">OPTIONALLY_BLOCKABLE</span> <span class="o">=</span> <span class="s2">&quot;optionally-blockable&quot;</span>
    <span class="n">NONE</span> <span class="o">=</span> <span class="s2">&quot;none&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">MixedContentType</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span></div>



<div class="viewcode-block" id="SecurityState">
<a class="viewcode-back" href="../../../nodriver/cdp/security.html#nodriver.cdp.security.SecurityState">[docs]</a>
<span class="k">class</span> <span class="nc">SecurityState</span><span class="p">(</span><span class="n">enum</span><span class="o">.</span><span class="n">Enum</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    The security level of a page or resource.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">UNKNOWN</span> <span class="o">=</span> <span class="s2">&quot;unknown&quot;</span>
    <span class="n">NEUTRAL</span> <span class="o">=</span> <span class="s2">&quot;neutral&quot;</span>
    <span class="n">INSECURE</span> <span class="o">=</span> <span class="s2">&quot;insecure&quot;</span>
    <span class="n">SECURE</span> <span class="o">=</span> <span class="s2">&quot;secure&quot;</span>
    <span class="n">INFO</span> <span class="o">=</span> <span class="s2">&quot;info&quot;</span>
    <span class="n">INSECURE_BROKEN</span> <span class="o">=</span> <span class="s2">&quot;insecure-broken&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">SecurityState</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span></div>



<div class="viewcode-block" id="CertificateSecurityState">
<a class="viewcode-back" href="../../../nodriver/cdp/security.html#nodriver.cdp.security.CertificateSecurityState">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">CertificateSecurityState</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Details about the security state of the page certificate.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: Protocol name (e.g. &quot;TLS 1.2&quot; or &quot;QUIC&quot;).</span>
    <span class="n">protocol</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: Key Exchange used by the connection, or the empty string if not applicable.</span>
    <span class="n">key_exchange</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: Cipher name.</span>
    <span class="n">cipher</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: Page certificate.</span>
    <span class="n">certificate</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span>

    <span class="c1">#: Certificate subject name.</span>
    <span class="n">subject_name</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: Name of the issuing CA.</span>
    <span class="n">issuer</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: Certificate valid from date.</span>
    <span class="n">valid_from</span><span class="p">:</span> <span class="n">network</span><span class="o">.</span><span class="n">TimeSinceEpoch</span>

    <span class="c1">#: Certificate valid to (expiration) date</span>
    <span class="n">valid_to</span><span class="p">:</span> <span class="n">network</span><span class="o">.</span><span class="n">TimeSinceEpoch</span>

    <span class="c1">#: True if the certificate uses a weak signature algorithm.</span>
    <span class="n">certificate_has_weak_signature</span><span class="p">:</span> <span class="nb">bool</span>

    <span class="c1">#: True if the certificate has a SHA1 signature in the chain.</span>
    <span class="n">certificate_has_sha1_signature</span><span class="p">:</span> <span class="nb">bool</span>

    <span class="c1">#: True if modern SSL</span>
    <span class="n">modern_ssl</span><span class="p">:</span> <span class="nb">bool</span>

    <span class="c1">#: True if the connection is using an obsolete SSL protocol.</span>
    <span class="n">obsolete_ssl_protocol</span><span class="p">:</span> <span class="nb">bool</span>

    <span class="c1">#: True if the connection is using an obsolete SSL key exchange.</span>
    <span class="n">obsolete_ssl_key_exchange</span><span class="p">:</span> <span class="nb">bool</span>

    <span class="c1">#: True if the connection is using an obsolete SSL cipher.</span>
    <span class="n">obsolete_ssl_cipher</span><span class="p">:</span> <span class="nb">bool</span>

    <span class="c1">#: True if the connection is using an obsolete SSL signature.</span>
    <span class="n">obsolete_ssl_signature</span><span class="p">:</span> <span class="nb">bool</span>

    <span class="c1">#: (EC)DH group used by the connection, if applicable.</span>
    <span class="n">key_exchange_group</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: TLS MAC. Note that AEAD ciphers do not have separate MACs.</span>
    <span class="n">mac</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: The highest priority network error code, if the certificate has an error.</span>
    <span class="n">certificate_network_error</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;protocol&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">protocol</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;keyExchange&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">key_exchange</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;cipher&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">cipher</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;certificate&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">certificate</span><span class="p">]</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;subjectName&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">subject_name</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;issuer&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">issuer</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;validFrom&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">valid_from</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;validTo&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">valid_to</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;certificateHasWeakSignature&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">certificate_has_weak_signature</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;certificateHasSha1Signature&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">certificate_has_sha1_signature</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;modernSSL&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">modern_ssl</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;obsoleteSslProtocol&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">obsolete_ssl_protocol</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;obsoleteSslKeyExchange&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">obsolete_ssl_key_exchange</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;obsoleteSslCipher&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">obsolete_ssl_cipher</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;obsoleteSslSignature&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">obsolete_ssl_signature</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">key_exchange_group</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;keyExchangeGroup&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">key_exchange_group</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">mac</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;mac&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">mac</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">certificate_network_error</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;certificateNetworkError&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">certificate_network_error</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">CertificateSecurityState</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">protocol</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;protocol&quot;</span><span class="p">]),</span>
            <span class="n">key_exchange</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;keyExchange&quot;</span><span class="p">]),</span>
            <span class="n">cipher</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;cipher&quot;</span><span class="p">]),</span>
            <span class="n">certificate</span><span class="o">=</span><span class="p">[</span><span class="nb">str</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;certificate&quot;</span><span class="p">]],</span>
            <span class="n">subject_name</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;subjectName&quot;</span><span class="p">]),</span>
            <span class="n">issuer</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;issuer&quot;</span><span class="p">]),</span>
            <span class="n">valid_from</span><span class="o">=</span><span class="n">network</span><span class="o">.</span><span class="n">TimeSinceEpoch</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;validFrom&quot;</span><span class="p">]),</span>
            <span class="n">valid_to</span><span class="o">=</span><span class="n">network</span><span class="o">.</span><span class="n">TimeSinceEpoch</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;validTo&quot;</span><span class="p">]),</span>
            <span class="n">certificate_has_weak_signature</span><span class="o">=</span><span class="nb">bool</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;certificateHasWeakSignature&quot;</span><span class="p">]),</span>
            <span class="n">certificate_has_sha1_signature</span><span class="o">=</span><span class="nb">bool</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;certificateHasSha1Signature&quot;</span><span class="p">]),</span>
            <span class="n">modern_ssl</span><span class="o">=</span><span class="nb">bool</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;modernSSL&quot;</span><span class="p">]),</span>
            <span class="n">obsolete_ssl_protocol</span><span class="o">=</span><span class="nb">bool</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;obsoleteSslProtocol&quot;</span><span class="p">]),</span>
            <span class="n">obsolete_ssl_key_exchange</span><span class="o">=</span><span class="nb">bool</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;obsoleteSslKeyExchange&quot;</span><span class="p">]),</span>
            <span class="n">obsolete_ssl_cipher</span><span class="o">=</span><span class="nb">bool</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;obsoleteSslCipher&quot;</span><span class="p">]),</span>
            <span class="n">obsolete_ssl_signature</span><span class="o">=</span><span class="nb">bool</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;obsoleteSslSignature&quot;</span><span class="p">]),</span>
            <span class="n">key_exchange_group</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;keyExchangeGroup&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;keyExchangeGroup&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">mac</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;mac&quot;</span><span class="p">])</span> <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;mac&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span> <span class="k">else</span> <span class="kc">None</span><span class="p">,</span>
            <span class="n">certificate_network_error</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;certificateNetworkError&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;certificateNetworkError&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="SafetyTipStatus">
<a class="viewcode-back" href="../../../nodriver/cdp/security.html#nodriver.cdp.security.SafetyTipStatus">[docs]</a>
<span class="k">class</span> <span class="nc">SafetyTipStatus</span><span class="p">(</span><span class="n">enum</span><span class="o">.</span><span class="n">Enum</span><span class="p">):</span>
    <span class="n">BAD_REPUTATION</span> <span class="o">=</span> <span class="s2">&quot;badReputation&quot;</span>
    <span class="n">LOOKALIKE</span> <span class="o">=</span> <span class="s2">&quot;lookalike&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">SafetyTipStatus</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span></div>



<div class="viewcode-block" id="SafetyTipInfo">
<a class="viewcode-back" href="../../../nodriver/cdp/security.html#nodriver.cdp.security.SafetyTipInfo">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">SafetyTipInfo</span><span class="p">:</span>
    <span class="c1">#: Describes whether the page triggers any safety tips or reputation warnings. Default is unknown.</span>
    <span class="n">safety_tip_status</span><span class="p">:</span> <span class="n">SafetyTipStatus</span>

    <span class="c1">#: The URL the safety tip suggested (&quot;Did you mean?&quot;). Only filled in for lookalike matches.</span>
    <span class="n">safe_url</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;safetyTipStatus&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">safety_tip_status</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">safe_url</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;safeUrl&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">safe_url</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">SafetyTipInfo</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">safety_tip_status</span><span class="o">=</span><span class="n">SafetyTipStatus</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;safetyTipStatus&quot;</span><span class="p">]),</span>
            <span class="n">safe_url</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;safeUrl&quot;</span><span class="p">])</span> <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;safeUrl&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span> <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="VisibleSecurityState">
<a class="viewcode-back" href="../../../nodriver/cdp/security.html#nodriver.cdp.security.VisibleSecurityState">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">VisibleSecurityState</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Security state information about the page.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: The security level of the page.</span>
    <span class="n">security_state</span><span class="p">:</span> <span class="n">SecurityState</span>

    <span class="c1">#: Array of security state issues ids.</span>
    <span class="n">security_state_issue_ids</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span>

    <span class="c1">#: Security state details about the page certificate.</span>
    <span class="n">certificate_security_state</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">CertificateSecurityState</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: The type of Safety Tip triggered on the page. Note that this field will be set even if the Safety Tip UI was not actually shown.</span>
    <span class="n">safety_tip_info</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">SafetyTipInfo</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;securityState&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">security_state</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;securityStateIssueIds&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">security_state_issue_ids</span><span class="p">]</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">certificate_security_state</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;certificateSecurityState&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">certificate_security_state</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">safety_tip_info</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;safetyTipInfo&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">safety_tip_info</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">VisibleSecurityState</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">security_state</span><span class="o">=</span><span class="n">SecurityState</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;securityState&quot;</span><span class="p">]),</span>
            <span class="n">security_state_issue_ids</span><span class="o">=</span><span class="p">[</span><span class="nb">str</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;securityStateIssueIds&quot;</span><span class="p">]],</span>
            <span class="n">certificate_security_state</span><span class="o">=</span><span class="p">(</span>
                <span class="n">CertificateSecurityState</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;certificateSecurityState&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;certificateSecurityState&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">safety_tip_info</span><span class="o">=</span><span class="p">(</span>
                <span class="n">SafetyTipInfo</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;safetyTipInfo&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;safetyTipInfo&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="SecurityStateExplanation">
<a class="viewcode-back" href="../../../nodriver/cdp/security.html#nodriver.cdp.security.SecurityStateExplanation">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">SecurityStateExplanation</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    An explanation of an factor contributing to the security state.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: Security state representing the severity of the factor being explained.</span>
    <span class="n">security_state</span><span class="p">:</span> <span class="n">SecurityState</span>

    <span class="c1">#: Title describing the type of factor.</span>
    <span class="n">title</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: Short phrase describing the type of factor.</span>
    <span class="n">summary</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: Full text explanation of the factor.</span>
    <span class="n">description</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: The type of mixed content described by the explanation.</span>
    <span class="n">mixed_content_type</span><span class="p">:</span> <span class="n">MixedContentType</span>

    <span class="c1">#: Page certificate.</span>
    <span class="n">certificate</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span>

    <span class="c1">#: Recommendations to fix any issues.</span>
    <span class="n">recommendations</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">]]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;securityState&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">security_state</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;title&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">title</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;summary&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">summary</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;description&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">description</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;mixedContentType&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">mixed_content_type</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;certificate&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">certificate</span><span class="p">]</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">recommendations</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;recommendations&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">recommendations</span><span class="p">]</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">SecurityStateExplanation</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">security_state</span><span class="o">=</span><span class="n">SecurityState</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;securityState&quot;</span><span class="p">]),</span>
            <span class="n">title</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;title&quot;</span><span class="p">]),</span>
            <span class="n">summary</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;summary&quot;</span><span class="p">]),</span>
            <span class="n">description</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;description&quot;</span><span class="p">]),</span>
            <span class="n">mixed_content_type</span><span class="o">=</span><span class="n">MixedContentType</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;mixedContentType&quot;</span><span class="p">]),</span>
            <span class="n">certificate</span><span class="o">=</span><span class="p">[</span><span class="nb">str</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;certificate&quot;</span><span class="p">]],</span>
            <span class="n">recommendations</span><span class="o">=</span><span class="p">(</span>
                <span class="p">[</span><span class="nb">str</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;recommendations&quot;</span><span class="p">]]</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;recommendations&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="InsecureContentStatus">
<a class="viewcode-back" href="../../../nodriver/cdp/security.html#nodriver.cdp.security.InsecureContentStatus">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">InsecureContentStatus</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Information about insecure content on the page.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: Always false.</span>
    <span class="n">ran_mixed_content</span><span class="p">:</span> <span class="nb">bool</span>

    <span class="c1">#: Always false.</span>
    <span class="n">displayed_mixed_content</span><span class="p">:</span> <span class="nb">bool</span>

    <span class="c1">#: Always false.</span>
    <span class="n">contained_mixed_form</span><span class="p">:</span> <span class="nb">bool</span>

    <span class="c1">#: Always false.</span>
    <span class="n">ran_content_with_cert_errors</span><span class="p">:</span> <span class="nb">bool</span>

    <span class="c1">#: Always false.</span>
    <span class="n">displayed_content_with_cert_errors</span><span class="p">:</span> <span class="nb">bool</span>

    <span class="c1">#: Always set to unknown.</span>
    <span class="n">ran_insecure_content_style</span><span class="p">:</span> <span class="n">SecurityState</span>

    <span class="c1">#: Always set to unknown.</span>
    <span class="n">displayed_insecure_content_style</span><span class="p">:</span> <span class="n">SecurityState</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;ranMixedContent&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">ran_mixed_content</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;displayedMixedContent&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">displayed_mixed_content</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;containedMixedForm&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">contained_mixed_form</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;ranContentWithCertErrors&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">ran_content_with_cert_errors</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;displayedContentWithCertErrors&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">displayed_content_with_cert_errors</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;ranInsecureContentStyle&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">ran_insecure_content_style</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;displayedInsecureContentStyle&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">(</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">displayed_insecure_content_style</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="p">)</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">InsecureContentStatus</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">ran_mixed_content</span><span class="o">=</span><span class="nb">bool</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;ranMixedContent&quot;</span><span class="p">]),</span>
            <span class="n">displayed_mixed_content</span><span class="o">=</span><span class="nb">bool</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;displayedMixedContent&quot;</span><span class="p">]),</span>
            <span class="n">contained_mixed_form</span><span class="o">=</span><span class="nb">bool</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;containedMixedForm&quot;</span><span class="p">]),</span>
            <span class="n">ran_content_with_cert_errors</span><span class="o">=</span><span class="nb">bool</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;ranContentWithCertErrors&quot;</span><span class="p">]),</span>
            <span class="n">displayed_content_with_cert_errors</span><span class="o">=</span><span class="nb">bool</span><span class="p">(</span>
                <span class="n">json</span><span class="p">[</span><span class="s2">&quot;displayedContentWithCertErrors&quot;</span><span class="p">]</span>
            <span class="p">),</span>
            <span class="n">ran_insecure_content_style</span><span class="o">=</span><span class="n">SecurityState</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span>
                <span class="n">json</span><span class="p">[</span><span class="s2">&quot;ranInsecureContentStyle&quot;</span><span class="p">]</span>
            <span class="p">),</span>
            <span class="n">displayed_insecure_content_style</span><span class="o">=</span><span class="n">SecurityState</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span>
                <span class="n">json</span><span class="p">[</span><span class="s2">&quot;displayedInsecureContentStyle&quot;</span><span class="p">]</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="CertificateErrorAction">
<a class="viewcode-back" href="../../../nodriver/cdp/security.html#nodriver.cdp.security.CertificateErrorAction">[docs]</a>
<span class="k">class</span> <span class="nc">CertificateErrorAction</span><span class="p">(</span><span class="n">enum</span><span class="o">.</span><span class="n">Enum</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    The action to take when a certificate error occurs. continue will continue processing the</span>
<span class="sd">    request and cancel will cancel the request.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">CONTINUE</span> <span class="o">=</span> <span class="s2">&quot;continue&quot;</span>
    <span class="n">CANCEL</span> <span class="o">=</span> <span class="s2">&quot;cancel&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">CertificateErrorAction</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span></div>



<div class="viewcode-block" id="disable">
<a class="viewcode-back" href="../../../nodriver/cdp/security.html#nodriver.cdp.security.disable">[docs]</a>
<span class="k">def</span> <span class="nf">disable</span><span class="p">()</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Disables tracking security state changes.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Security.disable&quot;</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span></div>



<div class="viewcode-block" id="enable">
<a class="viewcode-back" href="../../../nodriver/cdp/security.html#nodriver.cdp.security.enable">[docs]</a>
<span class="k">def</span> <span class="nf">enable</span><span class="p">()</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Enables tracking security state changes.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Security.enable&quot;</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span></div>



<div class="viewcode-block" id="set_ignore_certificate_errors">
<a class="viewcode-back" href="../../../nodriver/cdp/security.html#nodriver.cdp.security.set_ignore_certificate_errors">[docs]</a>
<span class="k">def</span> <span class="nf">set_ignore_certificate_errors</span><span class="p">(</span>
        <span class="n">ignore</span><span class="p">:</span> <span class="nb">bool</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Enable/disable whether all certificate errors should be ignored.</span>

<span class="sd">    :param ignore: If true, all certificate errors will be ignored.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;ignore&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">ignore</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Security.setIgnoreCertificateErrors&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span></div>



<div class="viewcode-block" id="handle_certificate_error">
<a class="viewcode-back" href="../../../nodriver/cdp/security.html#nodriver.cdp.security.handle_certificate_error">[docs]</a>
<span class="nd">@deprecated</span><span class="p">(</span><span class="n">version</span><span class="o">=</span><span class="s2">&quot;1.3&quot;</span><span class="p">)</span>
<span class="k">def</span> <span class="nf">handle_certificate_error</span><span class="p">(</span>
        <span class="n">event_id</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span> <span class="n">action</span><span class="p">:</span> <span class="n">CertificateErrorAction</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Handles a certificate error that fired a certificateError event.</span>

<span class="sd">    .. deprecated:: 1.3</span>

<span class="sd">    :param event_id: The ID of the event.</span>
<span class="sd">    :param action: The action to take on the certificate error.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;eventId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">event_id</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;action&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">action</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Security.handleCertificateError&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span></div>



<div class="viewcode-block" id="set_override_certificate_errors">
<a class="viewcode-back" href="../../../nodriver/cdp/security.html#nodriver.cdp.security.set_override_certificate_errors">[docs]</a>
<span class="nd">@deprecated</span><span class="p">(</span><span class="n">version</span><span class="o">=</span><span class="s2">&quot;1.3&quot;</span><span class="p">)</span>
<span class="k">def</span> <span class="nf">set_override_certificate_errors</span><span class="p">(</span>
        <span class="n">override</span><span class="p">:</span> <span class="nb">bool</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Enable/disable overriding certificate errors. If enabled, all certificate error events need to</span>
<span class="sd">    be handled by the DevTools client and should be answered with ``handleCertificateError`` commands.</span>

<span class="sd">    .. deprecated:: 1.3</span>

<span class="sd">    :param override: If true, certificate errors will be overridden.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;override&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">override</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Security.setOverrideCertificateErrors&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span></div>



<div class="viewcode-block" id="CertificateError">
<a class="viewcode-back" href="../../../nodriver/cdp/security.html#nodriver.cdp.security.CertificateError">[docs]</a>
<span class="nd">@deprecated</span><span class="p">(</span><span class="n">version</span><span class="o">=</span><span class="s2">&quot;1.3&quot;</span><span class="p">)</span>
<span class="nd">@event_class</span><span class="p">(</span><span class="s2">&quot;Security.certificateError&quot;</span><span class="p">)</span>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">CertificateError</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    There is a certificate error. If overriding certificate errors is enabled, then it should be</span>
<span class="sd">    handled with the ``handleCertificateError`` command. Note: this event does not fire if the</span>
<span class="sd">    certificate error has been allowed internally. Only one client per target should override</span>
<span class="sd">    certificate errors at the same time.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: The ID of the event.</span>
    <span class="n">event_id</span><span class="p">:</span> <span class="nb">int</span>
    <span class="c1">#: The type of the error.</span>
    <span class="n">error_type</span><span class="p">:</span> <span class="nb">str</span>
    <span class="c1">#: The url that was requested.</span>
    <span class="n">request_url</span><span class="p">:</span> <span class="nb">str</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">CertificateError</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">event_id</span><span class="o">=</span><span class="nb">int</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;eventId&quot;</span><span class="p">]),</span>
            <span class="n">error_type</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;errorType&quot;</span><span class="p">]),</span>
            <span class="n">request_url</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;requestURL&quot;</span><span class="p">]),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="VisibleSecurityStateChanged">
<a class="viewcode-back" href="../../../nodriver/cdp/security.html#nodriver.cdp.security.VisibleSecurityStateChanged">[docs]</a>
<span class="nd">@event_class</span><span class="p">(</span><span class="s2">&quot;Security.visibleSecurityStateChanged&quot;</span><span class="p">)</span>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">VisibleSecurityStateChanged</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    **EXPERIMENTAL**</span>

<span class="sd">    The security state of the page changed.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: Security state information about the page.</span>
    <span class="n">visible_security_state</span><span class="p">:</span> <span class="n">VisibleSecurityState</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">VisibleSecurityStateChanged</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">visible_security_state</span><span class="o">=</span><span class="n">VisibleSecurityState</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span>
                <span class="n">json</span><span class="p">[</span><span class="s2">&quot;visibleSecurityState&quot;</span><span class="p">]</span>
            <span class="p">)</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="SecurityStateChanged">
<a class="viewcode-back" href="../../../nodriver/cdp/security.html#nodriver.cdp.security.SecurityStateChanged">[docs]</a>
<span class="nd">@deprecated</span><span class="p">(</span><span class="n">version</span><span class="o">=</span><span class="s2">&quot;1.3&quot;</span><span class="p">)</span>
<span class="nd">@event_class</span><span class="p">(</span><span class="s2">&quot;Security.securityStateChanged&quot;</span><span class="p">)</span>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">SecurityStateChanged</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    The security state of the page changed. No longer being sent.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: Security state.</span>
    <span class="n">security_state</span><span class="p">:</span> <span class="n">SecurityState</span>
    <span class="c1">#: True if the page was loaded over cryptographic transport such as HTTPS.</span>
    <span class="n">scheme_is_cryptographic</span><span class="p">:</span> <span class="nb">bool</span>
    <span class="c1">#: Previously a list of explanations for the security state. Now always</span>
    <span class="c1">#: empty.</span>
    <span class="n">explanations</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">SecurityStateExplanation</span><span class="p">]</span>
    <span class="c1">#: Information about insecure content on the page.</span>
    <span class="n">insecure_content_status</span><span class="p">:</span> <span class="n">InsecureContentStatus</span>
    <span class="c1">#: Overrides user-visible description of the state. Always omitted.</span>
    <span class="n">summary</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">SecurityStateChanged</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">security_state</span><span class="o">=</span><span class="n">SecurityState</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;securityState&quot;</span><span class="p">]),</span>
            <span class="n">scheme_is_cryptographic</span><span class="o">=</span><span class="nb">bool</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;schemeIsCryptographic&quot;</span><span class="p">]),</span>
            <span class="n">explanations</span><span class="o">=</span><span class="p">[</span>
                <span class="n">SecurityStateExplanation</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;explanations&quot;</span><span class="p">]</span>
            <span class="p">],</span>
            <span class="n">insecure_content_status</span><span class="o">=</span><span class="n">InsecureContentStatus</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span>
                <span class="n">json</span><span class="p">[</span><span class="s2">&quot;insecureContentStatus&quot;</span><span class="p">]</span>
            <span class="p">),</span>
            <span class="n">summary</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;summary&quot;</span><span class="p">])</span> <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;summary&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span> <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>

</pre></div>
        </article>
      </div>
      <footer>
        
        <div class="related-pages">
          
          
        </div>
        <div class="bottom-of-page">
          <div class="left-details">
            <div class="copyright">
                Copyright &#169; 2023, Author
            </div>
            Made with <a href="https://www.sphinx-doc.org/">Sphinx</a> and <a class="muted-link" href="https://pradyunsg.me">@pradyunsg</a>'s
            
            <a href="https://github.com/pradyunsg/furo">Furo</a>
            
          </div>
          <div class="right-details">
            
          </div>
        </div>
        
      </footer>
    </div>
    <aside class="toc-drawer no-toc">
      
      
      
    </aside>
  </div>
</div><script src="../../../_static/documentation_options.js?v=5929fcd5"></script>
    <script src="../../../_static/doctools.js?v=888ff710"></script>
    <script src="../../../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../../../_static/scripts/furo.js?v=32e29ea5"></script>
    </body>
</html>