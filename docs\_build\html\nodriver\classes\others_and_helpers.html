<!doctype html>
<html class="no-js" lang="en" data-content_root="../../">
  <head><meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width,initial-scale=1"/>
    <meta name="color-scheme" content="light dark"><meta name="viewport" content="width=device-width, initial-scale=1" />
<link rel="index" title="Index" href="../../genindex.html" /><link rel="search" title="Search" href="../../search.html" /><link rel="next" title="CDP object" href="../cdp.html" /><link rel="prev" title="Element class" href="element.html" />

    <!-- Generated with Sphinx 7.2.6 and Furo 2023.09.10 -->
        <title>Other classes and Helper classes - nodriver documentation</title>
      <link rel="stylesheet" type="text/css" href="../../_static/pygments.css?v=fa44fd50" />
    <link rel="stylesheet" type="text/css" href="../../_static/styles/furo.css?v=135e06be" />
    <link rel="stylesheet" type="text/css" href="../../_static/styles/furo-extensions.css?v=36a5483c" />
    <link rel="stylesheet" type="text/css" href="../../_static/./custom.css?v=3934642d" />
    
    


<style>
  body {
    --color-code-background: #eeffcc;
  --color-code-foreground: black;
  
  }
  @media not print {
    body[data-theme="dark"] {
      --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8eefba;
  --color-background-primary: #111112;
  --color-problematic: #e7aeae;
  
    }
    @media (prefers-color-scheme: dark) {
      body:not([data-theme="light"]) {
        --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8eefba;
  --color-background-primary: #111112;
  --color-problematic: #e7aeae;
  
      }
    }
  }
</style></head>
  <body>
    
    <script>
      document.body.dataset.theme = localStorage.getItem("theme") || "auto";
    </script>
    

<svg xmlns="http://www.w3.org/2000/svg" style="display: none;">
  <symbol id="svg-toc" viewBox="0 0 24 24">
    <title>Contents</title>
    <svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 1024 1024">
      <path d="M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM115.4 518.9L271.7 642c5.8 4.6 14.4.5 14.4-6.9V388.9c0-7.4-8.5-11.5-14.4-6.9L115.4 505.1a8.74 8.74 0 0 0 0 13.8z"/>
    </svg>
  </symbol>
  <symbol id="svg-menu" viewBox="0 0 24 24">
    <title>Menu</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-menu">
      <line x1="3" y1="12" x2="21" y2="12"></line>
      <line x1="3" y1="6" x2="21" y2="6"></line>
      <line x1="3" y1="18" x2="21" y2="18"></line>
    </svg>
  </symbol>
  <symbol id="svg-arrow-right" viewBox="0 0 24 24">
    <title>Expand</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-chevron-right">
      <polyline points="9 18 15 12 9 6"></polyline>
    </svg>
  </symbol>
  <symbol id="svg-sun" viewBox="0 0 24 24">
    <title>Light mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="feather-sun">
      <circle cx="12" cy="12" r="5"></circle>
      <line x1="12" y1="1" x2="12" y2="3"></line>
      <line x1="12" y1="21" x2="12" y2="23"></line>
      <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
      <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
      <line x1="1" y1="12" x2="3" y2="12"></line>
      <line x1="21" y1="12" x2="23" y2="12"></line>
      <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
      <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
    </svg>
  </symbol>
  <symbol id="svg-moon" viewBox="0 0 24 24">
    <title>Dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-moon">
      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
      <path d="M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z" />
    </svg>
  </symbol>
  <symbol id="svg-sun-half" viewBox="0 0 24 24">
    <title>Auto light/dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-shadow">
      <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
      <circle cx="12" cy="12" r="9" />
      <path d="M13 12h5" />
      <path d="M13 15h4" />
      <path d="M13 18h1" />
      <path d="M13 9h4" />
      <path d="M13 6h1" />
    </svg>
  </symbol>
</svg>

<input type="checkbox" class="sidebar-toggle" name="__navigation" id="__navigation">
<input type="checkbox" class="sidebar-toggle" name="__toc" id="__toc">
<label class="overlay sidebar-overlay" for="__navigation">
  <div class="visually-hidden">Hide navigation sidebar</div>
</label>
<label class="overlay toc-overlay" for="__toc">
  <div class="visually-hidden">Hide table of contents sidebar</div>
</label>



<div class="page">
  <header class="mobile-header">
    <div class="header-left">
      <label class="nav-overlay-icon" for="__navigation">
        <div class="visually-hidden">Toggle site navigation sidebar</div>
        <i class="icon"><svg><use href="#svg-menu"></use></svg></i>
      </label>
    </div>
    <div class="header-center">
      <a href="../../index.html"><div class="brand">nodriver  documentation</div></a>
    </div>
    <div class="header-right">
      <div class="theme-toggle-container theme-toggle-header">
        <button class="theme-toggle">
          <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
          <svg class="theme-icon-when-auto"><use href="#svg-sun-half"></use></svg>
          <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
          <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
        </button>
      </div>
      <label class="toc-overlay-icon toc-header-icon" for="__toc">
        <div class="visually-hidden">Toggle table of contents sidebar</div>
        <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
      </label>
    </div>
  </header>
  <aside class="sidebar-drawer">
    <div class="sidebar-container">
      
      <div class="sidebar-sticky"><div class="sidebar-scroll"><a class="sidebar-brand" href="../../index.html">
  
  
  <span class="sidebar-brand-text">nodriver  documentation</span>
  
</a><form class="sidebar-search-container" method="get" action="../../search.html" role="search">
  <input class="sidebar-search" placeholder="Search" name="q" aria-label="Search">
  <input type="hidden" name="check_keywords" value="yes">
  <input type="hidden" name="area" value="default">
</form>
<div id="searchbox"></div><div class="sidebar-tree">
  <ul>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html">Quickstart guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#usage-example">usage example</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#more-complete-example">More complete example</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#custom-starting-options">Custom starting options</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#alternative-custom-options">Alternative custom options</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#proxies-socks5-authenticated-too">Proxies (socks5, authenticated too)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#concrete-example">Concrete example</a></li>
</ul>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="browser.html">Browser class</a></li>
<li class="toctree-l1"><a class="reference internal" href="tab.html">Tab class</a></li>
<li class="toctree-l1"><a class="reference internal" href="element.html">Element class</a></li>
<li class="toctree-l1 current current-page"><a class="current reference internal" href="#">Other classes and Helper classes</a></li>
</ul>
<ul>
<li class="toctree-l1 has-children"><a class="reference internal" href="../cdp.html">CDP object</a><input class="toctree-checkbox" id="toctree-checkbox-1" name="toctree-checkbox-1" role="switch" type="checkbox"/><label for="toctree-checkbox-1"><div class="visually-hidden">Toggle navigation of CDP object</div><i class="icon"><svg><use href="#svg-arrow-right"></use></svg></i></label><ul>
<li class="toctree-l2"><a class="reference internal" href="../cdp/accessibility.html">Accessibility</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/animation.html">Animation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/audits.html">Audits</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/autofill.html">Autofill</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/background_service.html">BackgroundService</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/bluetooth_emulation.html">BluetoothEmulation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/browser.html">Browser</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/cache_storage.html">CacheStorage</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/cast.html">Cast</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/console.html">Console</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/css.html">CSS</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/debugger.html">Debugger</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/device_access.html">DeviceAccess</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/device_orientation.html">DeviceOrientation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/dom.html">DOM</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/dom_debugger.html">DOMDebugger</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/dom_snapshot.html">DOMSnapshot</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/dom_storage.html">DOMStorage</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/emulation.html">Emulation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/event_breakpoints.html">EventBreakpoints</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/extensions.html">Extensions</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/fed_cm.html">FedCm</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/fetch.html">Fetch</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/file_system.html">FileSystem</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/headless_experimental.html">HeadlessExperimental</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/heap_profiler.html">HeapProfiler</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/indexed_db.html">IndexedDB</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/input_.html">Input</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/inspector.html">Inspector</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/io.html">IO</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/layer_tree.html">LayerTree</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/log.html">Log</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/media.html">Media</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/memory.html">Memory</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/network.html">Network</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/overlay.html">Overlay</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/page.html">Page</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/performance.html">Performance</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/performance_timeline.html">PerformanceTimeline</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/preload.html">Preload</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/profiler.html">Profiler</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/pwa.html">PWA</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/runtime.html">Runtime</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/schema.html">Schema</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/security.html">Security</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/service_worker.html">ServiceWorker</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/storage.html">Storage</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/system_info.html">SystemInfo</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/target.html">Target</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/tethering.html">Tethering</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/tracing.html">Tracing</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/web_audio.html">WebAudio</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/web_authn.html">WebAuthn</a></li>
</ul>
</li>
</ul>

</div>
</div>
      </div>
      
    </div>
  </aside>
  <div class="main">
    <div class="content">
      <div class="article-container">
        <a href="#" class="back-to-top muted-link">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
            <path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8v12z"></path>
          </svg>
          <span>Back to top</span>
        </a>
        <div class="content-icon-container">
          
<div class="theme-toggle-container theme-toggle-content">
            <button class="theme-toggle">
              <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
              <svg class="theme-icon-when-auto"><use href="#svg-sun-half"></use></svg>
              <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
              <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
            </button>
          </div>
          <label class="toc-overlay-icon toc-content-icon" for="__toc">
            <div class="visually-hidden">Toggle table of contents sidebar</div>
            <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
          </label>
        </div>
        <article role="main">
          <section id="other-classes-and-helper-classes">
<h1>Other classes and Helper classes<a class="headerlink" href="#other-classes-and-helper-classes" title="Link to this heading">#</a></h1>
<section id="config-class">
<h2>Config class<a class="headerlink" href="#config-class" title="Link to this heading">#</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="nodriver.Config">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">Config</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">user_data_dir</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">headless</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">browser_executable_path</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">browser_args</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">sandbox</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">lang</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'en-US'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">host</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">port</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">expert</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwargs</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/config.html#Config"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Config" title="Link to this definition">#</a></dt>
<dd><p>Config object</p>
<dl class="py property">
<dt class="sig sig-object py" id="nodriver.Config.browser_args">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">browser_args</span></span><a class="headerlink" href="#nodriver.Config.browser_args" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="nodriver.Config.user_data_dir">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">user_data_dir</span></span><a class="headerlink" href="#nodriver.Config.user_data_dir" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="nodriver.Config.uses_custom_data_dir">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">uses_custom_data_dir</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><span class="pre">bool</span></a></em><a class="headerlink" href="#nodriver.Config.uses_custom_data_dir" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Config.add_extension">
<span class="sig-name descname"><span class="pre">add_extension</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">extension_path</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/config.html#Config.add_extension"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Config.add_extension" title="Link to this definition">#</a></dt>
<dd><p>adds an extension to load, you could point extension_path
to a folder (containing the manifest), or extension file (crx)</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>extension_path</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.TypeVar" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">TypeVar</span></code></a>(<code class="docutils literal notranslate"><span class="pre">PathLike</span></code>, bound= <a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a> | <a class="reference external" href="https://docs.python.org/3/library/pathlib.html#pathlib.Path" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Path</span></code></a>)</span>) – </p>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p></p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p></p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Config.add_argument">
<span class="sig-name descname"><span class="pre">add_argument</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">arg</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/config.html#Config.add_argument"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Config.add_argument" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

</section>
<section id="contradict-class">
<h2>ContraDict class<a class="headerlink" href="#contradict-class" title="Link to this heading">#</a></h2>
<p>Many components in this package are built using a
base class of <a class="reference internal" href="#id0" title="nodriver.core._contradict.ContraDict"><code class="xref any py py-class docutils literal notranslate"><span class="pre">nodriver.core._contradict.ContraDict</span></code></a>.</p>
<p>It’s nothing more than a dictionary which has attribute access AND
is JSON serializable.</p>
<dl class="py class">
<dt class="sig sig-object py" id="nodriver.core._contradict.ContraDict">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">ContraDict</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwargs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#nodriver.core._contradict.ContraDict" title="Link to this definition">#</a></dt>
<dd><p>directly inherited from dict</p>
<p>accessible by attribute. o.x == o[‘x’]
This works also for all corner cases.</p>
<p>native json.dumps and json.loads work with it</p>
<p>names like “keys”, “update”, “values” etc won’t overwrite the methods,
but will just be available using dict lookup notation obj[‘items’] instead of obj.items</p>
<p>all key names are converted to snake_case
hyphen’s (-), dot’s (.) or whitespaces are replaced by underscore (_)</p>
<p>autocomplete works even if the objects comes from a list</p>
<p>recursive action. dict assignments will be converted too.</p>
<dl class="py method">
<dt class="sig sig-object py" id="nodriver.core._contradict.ContraDict.clear">
<span class="sig-name descname"><span class="pre">clear</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">None.</span>&#160; <span class="pre">Remove</span> <span class="pre">all</span> <span class="pre">items</span> <span class="pre">from</span> <span class="pre">D.</span></span></span><a class="headerlink" href="#nodriver.core._contradict.ContraDict.clear" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.core._contradict.ContraDict.copy">
<span class="sig-name descname"><span class="pre">copy</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">a</span> <span class="pre">shallow</span> <span class="pre">copy</span> <span class="pre">of</span> <span class="pre">D</span></span></span><a class="headerlink" href="#nodriver.core._contradict.ContraDict.copy" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.core._contradict.ContraDict.fromkeys">
<span class="sig-name descname"><span class="pre">fromkeys</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#nodriver.core._contradict.ContraDict.fromkeys" title="Link to this definition">#</a></dt>
<dd><p>Create a new dictionary with keys from iterable and values set to value.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.core._contradict.ContraDict.get">
<span class="sig-name descname"><span class="pre">get</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">default</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#nodriver.core._contradict.ContraDict.get" title="Link to this definition">#</a></dt>
<dd><p>Return the value for key if key is in the dictionary, else default.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.core._contradict.ContraDict.items">
<span class="sig-name descname"><span class="pre">items</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">a</span> <span class="pre">set-like</span> <span class="pre">object</span> <span class="pre">providing</span> <span class="pre">a</span> <span class="pre">view</span> <span class="pre">on</span> <span class="pre">D's</span> <span class="pre">items</span></span></span><a class="headerlink" href="#nodriver.core._contradict.ContraDict.items" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.core._contradict.ContraDict.keys">
<span class="sig-name descname"><span class="pre">keys</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">a</span> <span class="pre">set-like</span> <span class="pre">object</span> <span class="pre">providing</span> <span class="pre">a</span> <span class="pre">view</span> <span class="pre">on</span> <span class="pre">D's</span> <span class="pre">keys</span></span></span><a class="headerlink" href="#nodriver.core._contradict.ContraDict.keys" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.core._contradict.ContraDict.pop">
<span class="sig-name descname"><span class="pre">pop</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">k</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">d</span></span></em><span class="optional">]</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">v,</span> <span class="pre">remove</span> <span class="pre">specified</span> <span class="pre">key</span> <span class="pre">and</span> <span class="pre">return</span> <span class="pre">the</span> <span class="pre">corresponding</span> <span class="pre">value.</span></span></span><a class="headerlink" href="#nodriver.core._contradict.ContraDict.pop" title="Link to this definition">#</a></dt>
<dd><p>If the key is not found, return the default if given; otherwise,
raise a KeyError.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.core._contradict.ContraDict.popitem">
<span class="sig-name descname"><span class="pre">popitem</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#nodriver.core._contradict.ContraDict.popitem" title="Link to this definition">#</a></dt>
<dd><p>Remove and return a (key, value) pair as a 2-tuple.</p>
<p>Pairs are returned in LIFO (last-in, first-out) order.
Raises KeyError if the dict is empty.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.core._contradict.ContraDict.setdefault">
<span class="sig-name descname"><span class="pre">setdefault</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">default</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#nodriver.core._contradict.ContraDict.setdefault" title="Link to this definition">#</a></dt>
<dd><p>Insert key with a value of default if key is not in the dictionary.</p>
<p>Return the value for key if key is in the dictionary, else default.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.core._contradict.ContraDict.update">
<span class="sig-name descname"><span class="pre">update</span></span><span class="sig-paren">(</span><span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">E</span></span></em>, <span class="optional">]</span><em class="sig-param"><span class="n"><span class="pre">**F</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">None.</span>&#160; <span class="pre">Update</span> <span class="pre">D</span> <span class="pre">from</span> <span class="pre">dict/iterable</span> <span class="pre">E</span> <span class="pre">and</span> <span class="pre">F.</span></span></span><a class="headerlink" href="#nodriver.core._contradict.ContraDict.update" title="Link to this definition">#</a></dt>
<dd><p>If E is present and has a .keys() method, then does:  for k in E: D[k] = E[k]
If E is present and lacks a .keys() method, then does:  for k, v in E: D[k] = v
In either case, this is followed by: for k in F:  D[k] = F[k]</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.core._contradict.ContraDict.values">
<span class="sig-name descname"><span class="pre">values</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">an</span> <span class="pre">object</span> <span class="pre">providing</span> <span class="pre">a</span> <span class="pre">view</span> <span class="pre">on</span> <span class="pre">D's</span> <span class="pre">values</span></span></span><a class="headerlink" href="#nodriver.core._contradict.ContraDict.values" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

</section>
<section id="module-nodriver.core._contradict">
<span id="helper-functions"></span><h2>Helper functions<a class="headerlink" href="#module-nodriver.core._contradict" title="Link to this heading">#</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="nodriver.core._contradict.cdict">
<span class="sig-name descname"><span class="pre">cdict</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwargs</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/_contradict.html#cdict"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.core._contradict.cdict" title="Link to this definition">#</a></dt>
<dd><p>factory function</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="id0">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">ContraDict</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwargs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#id0" title="Link to this definition">#</a></dt>
<dd><p>directly inherited from dict</p>
<p>accessible by attribute. o.x == o[‘x’]
This works also for all corner cases.</p>
<p>native json.dumps and json.loads work with it</p>
<p>names like “keys”, “update”, “values” etc won’t overwrite the methods,
but will just be available using dict lookup notation obj[‘items’] instead of obj.items</p>
<p>all key names are converted to snake_case
hyphen’s (-), dot’s (.) or whitespaces are replaced by underscore (_)</p>
<p>autocomplete works even if the objects comes from a list</p>
<p>recursive action. dict assignments will be converted too.</p>
<dl class="py method">
<dt class="sig sig-object py" id="id1">
<span class="sig-name descname"><span class="pre">clear</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">None.</span>&#160; <span class="pre">Remove</span> <span class="pre">all</span> <span class="pre">items</span> <span class="pre">from</span> <span class="pre">D.</span></span></span><a class="headerlink" href="#id1" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="id2">
<span class="sig-name descname"><span class="pre">copy</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">a</span> <span class="pre">shallow</span> <span class="pre">copy</span> <span class="pre">of</span> <span class="pre">D</span></span></span><a class="headerlink" href="#id2" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="id3">
<span class="sig-name descname"><span class="pre">fromkeys</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#id3" title="Link to this definition">#</a></dt>
<dd><p>Create a new dictionary with keys from iterable and values set to value.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="id4">
<span class="sig-name descname"><span class="pre">get</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">default</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#id4" title="Link to this definition">#</a></dt>
<dd><p>Return the value for key if key is in the dictionary, else default.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="id5">
<span class="sig-name descname"><span class="pre">items</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">a</span> <span class="pre">set-like</span> <span class="pre">object</span> <span class="pre">providing</span> <span class="pre">a</span> <span class="pre">view</span> <span class="pre">on</span> <span class="pre">D's</span> <span class="pre">items</span></span></span><a class="headerlink" href="#id5" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="id6">
<span class="sig-name descname"><span class="pre">keys</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">a</span> <span class="pre">set-like</span> <span class="pre">object</span> <span class="pre">providing</span> <span class="pre">a</span> <span class="pre">view</span> <span class="pre">on</span> <span class="pre">D's</span> <span class="pre">keys</span></span></span><a class="headerlink" href="#id6" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="id7">
<span class="sig-name descname"><span class="pre">pop</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">k</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">d</span></span></em><span class="optional">]</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">v,</span> <span class="pre">remove</span> <span class="pre">specified</span> <span class="pre">key</span> <span class="pre">and</span> <span class="pre">return</span> <span class="pre">the</span> <span class="pre">corresponding</span> <span class="pre">value.</span></span></span><a class="headerlink" href="#id7" title="Link to this definition">#</a></dt>
<dd><p>If the key is not found, return the default if given; otherwise,
raise a KeyError.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="id8">
<span class="sig-name descname"><span class="pre">popitem</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#id8" title="Link to this definition">#</a></dt>
<dd><p>Remove and return a (key, value) pair as a 2-tuple.</p>
<p>Pairs are returned in LIFO (last-in, first-out) order.
Raises KeyError if the dict is empty.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="id9">
<span class="sig-name descname"><span class="pre">setdefault</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">default</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#id9" title="Link to this definition">#</a></dt>
<dd><p>Insert key with a value of default if key is not in the dictionary.</p>
<p>Return the value for key if key is in the dictionary, else default.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="id10">
<span class="sig-name descname"><span class="pre">update</span></span><span class="sig-paren">(</span><span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">E</span></span></em>, <span class="optional">]</span><em class="sig-param"><span class="n"><span class="pre">**F</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">None.</span>&#160; <span class="pre">Update</span> <span class="pre">D</span> <span class="pre">from</span> <span class="pre">dict/iterable</span> <span class="pre">E</span> <span class="pre">and</span> <span class="pre">F.</span></span></span><a class="headerlink" href="#id10" title="Link to this definition">#</a></dt>
<dd><p>If E is present and has a .keys() method, then does:  for k in E: D[k] = E[k]
If E is present and lacks a .keys() method, then does:  for k, v in E: D[k] = v
In either case, this is followed by: for k in F:  D[k] = F[k]</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="id11">
<span class="sig-name descname"><span class="pre">values</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">an</span> <span class="pre">object</span> <span class="pre">providing</span> <span class="pre">a</span> <span class="pre">view</span> <span class="pre">on</span> <span class="pre">D's</span> <span class="pre">values</span></span></span><a class="headerlink" href="#id11" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

</section>
</section>

        </article>
      </div>
      <footer>
        
        <div class="related-pages">
          <a class="next-page" href="../cdp.html">
              <div class="page-info">
                <div class="context">
                  <span>Next</span>
                </div>
                <div class="title">CDP object</div>
              </div>
              <svg class="furo-related-icon"><use href="#svg-arrow-right"></use></svg>
            </a>
          <a class="prev-page" href="element.html">
              <svg class="furo-related-icon"><use href="#svg-arrow-right"></use></svg>
              <div class="page-info">
                <div class="context">
                  <span>Previous</span>
                </div>
                
                <div class="title">Element class</div>
                
              </div>
            </a>
        </div>
        <div class="bottom-of-page">
          <div class="left-details">
            <div class="copyright">
                Copyright &#169; 2023, Author
            </div>
            Made with <a href="https://www.sphinx-doc.org/">Sphinx</a> and <a class="muted-link" href="https://pradyunsg.me">@pradyunsg</a>'s
            
            <a href="https://github.com/pradyunsg/furo">Furo</a>
            
          </div>
          <div class="right-details">
            
          </div>
        </div>
        
      </footer>
    </div>
    <aside class="toc-drawer">
      
      
      <div class="toc-sticky toc-scroll">
        <div class="toc-title-container">
          <span class="toc-title">
            On this page
          </span>
        </div>
        <div class="toc-tree-container">
          <div class="toc-tree">
            <ul>
<li><a class="reference internal" href="#">Other classes and Helper classes</a><ul>
<li><a class="reference internal" href="#config-class">Config class</a><ul>
<li><a class="reference internal" href="#nodriver.Config"><code class="docutils literal notranslate"><span class="pre">Config</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.Config.browser_args"><code class="docutils literal notranslate"><span class="pre">Config.browser_args</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Config.user_data_dir"><code class="docutils literal notranslate"><span class="pre">Config.user_data_dir</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Config.uses_custom_data_dir"><code class="docutils literal notranslate"><span class="pre">Config.uses_custom_data_dir</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Config.add_extension"><code class="docutils literal notranslate"><span class="pre">Config.add_extension()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Config.add_argument"><code class="docutils literal notranslate"><span class="pre">Config.add_argument()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#contradict-class">ContraDict class</a><ul>
<li><a class="reference internal" href="#nodriver.core._contradict.ContraDict"><code class="docutils literal notranslate"><span class="pre">ContraDict</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.core._contradict.ContraDict.clear"><code class="docutils literal notranslate"><span class="pre">ContraDict.clear()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.core._contradict.ContraDict.copy"><code class="docutils literal notranslate"><span class="pre">ContraDict.copy()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.core._contradict.ContraDict.fromkeys"><code class="docutils literal notranslate"><span class="pre">ContraDict.fromkeys()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.core._contradict.ContraDict.get"><code class="docutils literal notranslate"><span class="pre">ContraDict.get()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.core._contradict.ContraDict.items"><code class="docutils literal notranslate"><span class="pre">ContraDict.items()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.core._contradict.ContraDict.keys"><code class="docutils literal notranslate"><span class="pre">ContraDict.keys()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.core._contradict.ContraDict.pop"><code class="docutils literal notranslate"><span class="pre">ContraDict.pop()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.core._contradict.ContraDict.popitem"><code class="docutils literal notranslate"><span class="pre">ContraDict.popitem()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.core._contradict.ContraDict.setdefault"><code class="docutils literal notranslate"><span class="pre">ContraDict.setdefault()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.core._contradict.ContraDict.update"><code class="docutils literal notranslate"><span class="pre">ContraDict.update()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.core._contradict.ContraDict.values"><code class="docutils literal notranslate"><span class="pre">ContraDict.values()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-nodriver.core._contradict">Helper functions</a><ul>
<li><a class="reference internal" href="#nodriver.core._contradict.cdict"><code class="docutils literal notranslate"><span class="pre">cdict()</span></code></a></li>
<li><a class="reference internal" href="#id0"><code class="docutils literal notranslate"><span class="pre">ContraDict</span></code></a><ul>
<li><a class="reference internal" href="#id1"><code class="docutils literal notranslate"><span class="pre">ContraDict.clear()</span></code></a></li>
<li><a class="reference internal" href="#id2"><code class="docutils literal notranslate"><span class="pre">ContraDict.copy()</span></code></a></li>
<li><a class="reference internal" href="#id3"><code class="docutils literal notranslate"><span class="pre">ContraDict.fromkeys()</span></code></a></li>
<li><a class="reference internal" href="#id4"><code class="docutils literal notranslate"><span class="pre">ContraDict.get()</span></code></a></li>
<li><a class="reference internal" href="#id5"><code class="docutils literal notranslate"><span class="pre">ContraDict.items()</span></code></a></li>
<li><a class="reference internal" href="#id6"><code class="docutils literal notranslate"><span class="pre">ContraDict.keys()</span></code></a></li>
<li><a class="reference internal" href="#id7"><code class="docutils literal notranslate"><span class="pre">ContraDict.pop()</span></code></a></li>
<li><a class="reference internal" href="#id8"><code class="docutils literal notranslate"><span class="pre">ContraDict.popitem()</span></code></a></li>
<li><a class="reference internal" href="#id9"><code class="docutils literal notranslate"><span class="pre">ContraDict.setdefault()</span></code></a></li>
<li><a class="reference internal" href="#id10"><code class="docutils literal notranslate"><span class="pre">ContraDict.update()</span></code></a></li>
<li><a class="reference internal" href="#id11"><code class="docutils literal notranslate"><span class="pre">ContraDict.values()</span></code></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>

          </div>
        </div>
      </div>
      
      
    </aside>
  </div>
</div><script src="../../_static/documentation_options.js?v=5929fcd5"></script>
    <script src="../../_static/doctools.js?v=888ff710"></script>
    <script src="../../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../../_static/scripts/furo.js?v=32e29ea5"></script>
    </body>
</html>