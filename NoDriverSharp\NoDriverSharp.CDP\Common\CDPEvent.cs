using System.Text.Json;
using System.Text.Json.Serialization;

namespace NoDriverSharp.CDP.Common;

/// <summary>
/// Base class for all Chrome DevTools Protocol events
/// </summary>
public abstract class CDPEvent
{
    /// <summary>
    /// The method name for this CDP event
    /// </summary>
    [JsonPropertyName("method")]
    public abstract string Method { get; }

    /// <summary>
    /// Parameters for this event
    /// </summary>
    [JsonPropertyName("params")]
    public virtual object? Params { get; set; }
}

/// <summary>
/// Generic CDP event with typed parameters
/// </summary>
/// <typeparam name="TParams">Type of the parameters</typeparam>
public abstract class CDPEvent<TParams> : CDPEvent where TParams : class
{
    /// <summary>
    /// Typed parameters for this event
    /// </summary>
    [JsonPropertyName("params")]
    public new TParams? TypedParams { get; set; }

    /// <summary>
    /// Base parameters property (for serialization)
    /// </summary>
    [JsonIgnore]
    public override object? Params
    {
        get => TypedParams;
        set => TypedParams = (TParams?)value;
    }
}

/// <summary>
/// Response from a CDP command
/// </summary>
public class CDPResponse
{
    /// <summary>
    /// Command ID this response is for
    /// </summary>
    [JsonPropertyName("id")]
    public int Id { get; set; }

    /// <summary>
    /// Result data if successful
    /// </summary>
    [JsonPropertyName("result")]
    public JsonElement? Result { get; set; }

    /// <summary>
    /// Error information if failed
    /// </summary>
    [JsonPropertyName("error")]
    public CDPError? Error { get; set; }

    /// <summary>
    /// Whether this response indicates success
    /// </summary>
    [JsonIgnore]
    public bool IsSuccess => Error == null;
}

/// <summary>
/// CDP error information
/// </summary>
public class CDPError
{
    /// <summary>
    /// Error code
    /// </summary>
    [JsonPropertyName("code")]
    public int Code { get; set; }

    /// <summary>
    /// Error message
    /// </summary>
    [JsonPropertyName("message")]
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// Additional error data
    /// </summary>
    [JsonPropertyName("data")]
    public JsonElement? Data { get; set; }
}
