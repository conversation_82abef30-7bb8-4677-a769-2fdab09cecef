<!doctype html>
<html class="no-js" lang="en" data-content_root="../../../">
  <head><meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width,initial-scale=1"/>
    <meta name="color-scheme" content="light dark"><link rel="index" title="Index" href="../../../genindex.html" /><link rel="search" title="Search" href="../../../search.html" />

    <!-- Generated with Sphinx 7.2.6 and Furo 2023.09.10 -->
        <title>nodriver.cdp.css - nodriver documentation</title>
      <link rel="stylesheet" type="text/css" href="../../../_static/pygments.css?v=fa44fd50" />
    <link rel="stylesheet" type="text/css" href="../../../_static/styles/furo.css?v=135e06be" />
    <link rel="stylesheet" type="text/css" href="../../../_static/styles/furo-extensions.css?v=36a5483c" />
    <link rel="stylesheet" type="text/css" href="../../../_static/./custom.css?v=3934642d" />
    
    


<style>
  body {
    --color-code-background: #eeffcc;
  --color-code-foreground: black;
  
  }
  @media not print {
    body[data-theme="dark"] {
      --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8eefba;
  --color-background-primary: #111112;
  --color-problematic: #e7aeae;
  
    }
    @media (prefers-color-scheme: dark) {
      body:not([data-theme="light"]) {
        --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8eefba;
  --color-background-primary: #111112;
  --color-problematic: #e7aeae;
  
      }
    }
  }
</style></head>
  <body>
    
    <script>
      document.body.dataset.theme = localStorage.getItem("theme") || "auto";
    </script>
    

<svg xmlns="http://www.w3.org/2000/svg" style="display: none;">
  <symbol id="svg-toc" viewBox="0 0 24 24">
    <title>Contents</title>
    <svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 1024 1024">
      <path d="M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM115.4 518.9L271.7 642c5.8 4.6 14.4.5 14.4-6.9V388.9c0-7.4-8.5-11.5-14.4-6.9L115.4 505.1a8.74 8.74 0 0 0 0 13.8z"/>
    </svg>
  </symbol>
  <symbol id="svg-menu" viewBox="0 0 24 24">
    <title>Menu</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-menu">
      <line x1="3" y1="12" x2="21" y2="12"></line>
      <line x1="3" y1="6" x2="21" y2="6"></line>
      <line x1="3" y1="18" x2="21" y2="18"></line>
    </svg>
  </symbol>
  <symbol id="svg-arrow-right" viewBox="0 0 24 24">
    <title>Expand</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-chevron-right">
      <polyline points="9 18 15 12 9 6"></polyline>
    </svg>
  </symbol>
  <symbol id="svg-sun" viewBox="0 0 24 24">
    <title>Light mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="feather-sun">
      <circle cx="12" cy="12" r="5"></circle>
      <line x1="12" y1="1" x2="12" y2="3"></line>
      <line x1="12" y1="21" x2="12" y2="23"></line>
      <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
      <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
      <line x1="1" y1="12" x2="3" y2="12"></line>
      <line x1="21" y1="12" x2="23" y2="12"></line>
      <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
      <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
    </svg>
  </symbol>
  <symbol id="svg-moon" viewBox="0 0 24 24">
    <title>Dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-moon">
      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
      <path d="M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z" />
    </svg>
  </symbol>
  <symbol id="svg-sun-half" viewBox="0 0 24 24">
    <title>Auto light/dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-shadow">
      <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
      <circle cx="12" cy="12" r="9" />
      <path d="M13 12h5" />
      <path d="M13 15h4" />
      <path d="M13 18h1" />
      <path d="M13 9h4" />
      <path d="M13 6h1" />
    </svg>
  </symbol>
</svg>

<input type="checkbox" class="sidebar-toggle" name="__navigation" id="__navigation">
<input type="checkbox" class="sidebar-toggle" name="__toc" id="__toc">
<label class="overlay sidebar-overlay" for="__navigation">
  <div class="visually-hidden">Hide navigation sidebar</div>
</label>
<label class="overlay toc-overlay" for="__toc">
  <div class="visually-hidden">Hide table of contents sidebar</div>
</label>



<div class="page">
  <header class="mobile-header">
    <div class="header-left">
      <label class="nav-overlay-icon" for="__navigation">
        <div class="visually-hidden">Toggle site navigation sidebar</div>
        <i class="icon"><svg><use href="#svg-menu"></use></svg></i>
      </label>
    </div>
    <div class="header-center">
      <a href="../../../index.html"><div class="brand">nodriver  documentation</div></a>
    </div>
    <div class="header-right">
      <div class="theme-toggle-container theme-toggle-header">
        <button class="theme-toggle">
          <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
          <svg class="theme-icon-when-auto"><use href="#svg-sun-half"></use></svg>
          <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
          <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
        </button>
      </div>
      <label class="toc-overlay-icon toc-header-icon no-toc" for="__toc">
        <div class="visually-hidden">Toggle table of contents sidebar</div>
        <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
      </label>
    </div>
  </header>
  <aside class="sidebar-drawer">
    <div class="sidebar-container">
      
      <div class="sidebar-sticky"><div class="sidebar-scroll"><a class="sidebar-brand" href="../../../index.html">
  
  
  <span class="sidebar-brand-text">nodriver  documentation</span>
  
</a><form class="sidebar-search-container" method="get" action="../../../search.html" role="search">
  <input class="sidebar-search" placeholder="Search" name="q" aria-label="Search">
  <input type="hidden" name="check_keywords" value="yes">
  <input type="hidden" name="area" value="default">
</form>
<div id="searchbox"></div><div class="sidebar-tree">
  <ul>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html">Quickstart guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#usage-example">usage example</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#more-complete-example">More complete example</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#custom-starting-options">Custom starting options</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#alternative-custom-options">Alternative custom options</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#proxies-socks5-authenticated-too">Proxies (socks5, authenticated too)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#concrete-example">Concrete example</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/classes/browser.html">Browser class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/classes/tab.html">Tab class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/classes/element.html">Element class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/classes/others_and_helpers.html">Other classes and Helper classes</a></li>
</ul>
<ul>
<li class="toctree-l1 has-children"><a class="reference internal" href="../../../nodriver/cdp.html">CDP object</a><input class="toctree-checkbox" id="toctree-checkbox-1" name="toctree-checkbox-1" role="switch" type="checkbox"/><label for="toctree-checkbox-1"><div class="visually-hidden">Toggle navigation of CDP object</div><i class="icon"><svg><use href="#svg-arrow-right"></use></svg></i></label><ul>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/accessibility.html">Accessibility</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/animation.html">Animation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/audits.html">Audits</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/autofill.html">Autofill</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/background_service.html">BackgroundService</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/bluetooth_emulation.html">BluetoothEmulation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/browser.html">Browser</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/cache_storage.html">CacheStorage</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/cast.html">Cast</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/console.html">Console</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/css.html">CSS</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/debugger.html">Debugger</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/device_access.html">DeviceAccess</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/device_orientation.html">DeviceOrientation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/dom.html">DOM</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/dom_debugger.html">DOMDebugger</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/dom_snapshot.html">DOMSnapshot</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/dom_storage.html">DOMStorage</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/emulation.html">Emulation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/event_breakpoints.html">EventBreakpoints</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/extensions.html">Extensions</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/fed_cm.html">FedCm</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/fetch.html">Fetch</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/file_system.html">FileSystem</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/headless_experimental.html">HeadlessExperimental</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/heap_profiler.html">HeapProfiler</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/indexed_db.html">IndexedDB</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/input_.html">Input</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/inspector.html">Inspector</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/io.html">IO</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/layer_tree.html">LayerTree</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/log.html">Log</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/media.html">Media</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/memory.html">Memory</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/network.html">Network</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/overlay.html">Overlay</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/page.html">Page</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/performance.html">Performance</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/performance_timeline.html">PerformanceTimeline</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/preload.html">Preload</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/profiler.html">Profiler</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/pwa.html">PWA</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/runtime.html">Runtime</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/schema.html">Schema</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/security.html">Security</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/service_worker.html">ServiceWorker</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/storage.html">Storage</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/system_info.html">SystemInfo</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/target.html">Target</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/tethering.html">Tethering</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/tracing.html">Tracing</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/web_audio.html">WebAudio</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/web_authn.html">WebAuthn</a></li>
</ul>
</li>
</ul>

</div>
</div>
      </div>
      
    </div>
  </aside>
  <div class="main">
    <div class="content">
      <div class="article-container">
        <a href="#" class="back-to-top muted-link">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
            <path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8v12z"></path>
          </svg>
          <span>Back to top</span>
        </a>
        <div class="content-icon-container">
          <div class="theme-toggle-container theme-toggle-content">
            <button class="theme-toggle">
              <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
              <svg class="theme-icon-when-auto"><use href="#svg-sun-half"></use></svg>
              <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
              <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
            </button>
          </div>
          <label class="toc-overlay-icon toc-content-icon no-toc" for="__toc">
            <div class="visually-hidden">Toggle table of contents sidebar</div>
            <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
          </label>
        </div>
        <article role="main">
          <h1>Source code for nodriver.cdp.css</h1><div class="highlight"><pre>
<span></span><span class="c1"># DO NOT EDIT THIS FILE!</span>
<span class="c1">#</span>
<span class="c1"># This file is generated from the CDP specification. If you need to make</span>
<span class="c1"># changes, edit the generator and regenerate all of the modules.</span>
<span class="c1">#</span>
<span class="c1"># CDP domain: CSS (experimental)</span>

<span class="kn">from</span> <span class="nn">__future__</span> <span class="kn">import</span> <span class="n">annotations</span>

<span class="kn">import</span> <span class="nn">enum</span>
<span class="kn">import</span> <span class="nn">typing</span>
<span class="kn">from</span> <span class="nn">dataclasses</span> <span class="kn">import</span> <span class="n">dataclass</span>

<span class="kn">from</span> <span class="nn">.</span> <span class="kn">import</span> <span class="n">dom</span><span class="p">,</span> <span class="n">page</span>
<span class="kn">from</span> <span class="nn">.util</span> <span class="kn">import</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">event_class</span>


<div class="viewcode-block" id="StyleSheetId">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.StyleSheetId">[docs]</a>
<span class="k">class</span> <span class="nc">StyleSheetId</span><span class="p">(</span><span class="nb">str</span><span class="p">):</span>
    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">StyleSheetId</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span>

    <span class="k">def</span> <span class="fm">__repr__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="s2">&quot;StyleSheetId(</span><span class="si">{}</span><span class="s2">)&quot;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="fm">__repr__</span><span class="p">())</span></div>



<div class="viewcode-block" id="StyleSheetOrigin">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.StyleSheetOrigin">[docs]</a>
<span class="k">class</span> <span class="nc">StyleSheetOrigin</span><span class="p">(</span><span class="n">enum</span><span class="o">.</span><span class="n">Enum</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Stylesheet type: &quot;injected&quot; for stylesheets injected via extension, &quot;user-agent&quot; for user-agent</span>
<span class="sd">    stylesheets, &quot;inspector&quot; for stylesheets created by the inspector (i.e. those holding the &quot;via</span>
<span class="sd">    inspector&quot; rules), &quot;regular&quot; for regular stylesheets.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">INJECTED</span> <span class="o">=</span> <span class="s2">&quot;injected&quot;</span>
    <span class="n">USER_AGENT</span> <span class="o">=</span> <span class="s2">&quot;user-agent&quot;</span>
    <span class="n">INSPECTOR</span> <span class="o">=</span> <span class="s2">&quot;inspector&quot;</span>
    <span class="n">REGULAR</span> <span class="o">=</span> <span class="s2">&quot;regular&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">StyleSheetOrigin</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span></div>



<div class="viewcode-block" id="PseudoElementMatches">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.PseudoElementMatches">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">PseudoElementMatches</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    CSS rule collection for a single pseudo style.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: Pseudo element type.</span>
    <span class="n">pseudo_type</span><span class="p">:</span> <span class="n">dom</span><span class="o">.</span><span class="n">PseudoType</span>

    <span class="c1">#: Matches of CSS rules applicable to the pseudo style.</span>
    <span class="n">matches</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">RuleMatch</span><span class="p">]</span>

    <span class="c1">#: Pseudo element custom ident.</span>
    <span class="n">pseudo_identifier</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;pseudoType&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">pseudo_type</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;matches&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">matches</span><span class="p">]</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">pseudo_identifier</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;pseudoIdentifier&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">pseudo_identifier</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">PseudoElementMatches</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">pseudo_type</span><span class="o">=</span><span class="n">dom</span><span class="o">.</span><span class="n">PseudoType</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;pseudoType&quot;</span><span class="p">]),</span>
            <span class="n">matches</span><span class="o">=</span><span class="p">[</span><span class="n">RuleMatch</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;matches&quot;</span><span class="p">]],</span>
            <span class="n">pseudo_identifier</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;pseudoIdentifier&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;pseudoIdentifier&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="CSSAnimationStyle">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.CSSAnimationStyle">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">CSSAnimationStyle</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    CSS style coming from animations with the name of the animation.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: The style coming from the animation.</span>
    <span class="n">style</span><span class="p">:</span> <span class="n">CSSStyle</span>

    <span class="c1">#: The name of the animation.</span>
    <span class="n">name</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;style&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">style</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">name</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;name&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">name</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">CSSAnimationStyle</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">style</span><span class="o">=</span><span class="n">CSSStyle</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;style&quot;</span><span class="p">]),</span>
            <span class="n">name</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;name&quot;</span><span class="p">])</span> <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;name&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span> <span class="k">else</span> <span class="kc">None</span><span class="p">,</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="InheritedStyleEntry">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.InheritedStyleEntry">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">InheritedStyleEntry</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Inherited CSS rule collection from ancestor node.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: Matches of CSS rules matching the ancestor node in the style inheritance chain.</span>
    <span class="n">matched_css_rules</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">RuleMatch</span><span class="p">]</span>

    <span class="c1">#: The ancestor node&#39;s inline style, if any, in the style inheritance chain.</span>
    <span class="n">inline_style</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">CSSStyle</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;matchedCSSRules&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">matched_css_rules</span><span class="p">]</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">inline_style</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;inlineStyle&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">inline_style</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">InheritedStyleEntry</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">matched_css_rules</span><span class="o">=</span><span class="p">[</span><span class="n">RuleMatch</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;matchedCSSRules&quot;</span><span class="p">]],</span>
            <span class="n">inline_style</span><span class="o">=</span><span class="p">(</span>
                <span class="n">CSSStyle</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;inlineStyle&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;inlineStyle&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="InheritedAnimatedStyleEntry">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.InheritedAnimatedStyleEntry">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">InheritedAnimatedStyleEntry</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Inherited CSS style collection for animated styles from ancestor node.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: Styles coming from the animations of the ancestor, if any, in the style inheritance chain.</span>
    <span class="n">animation_styles</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">CSSAnimationStyle</span><span class="p">]]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: The style coming from the transitions of the ancestor, if any, in the style inheritance chain.</span>
    <span class="n">transitions_style</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">CSSStyle</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">animation_styles</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;animationStyles&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">animation_styles</span><span class="p">]</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">transitions_style</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;transitionsStyle&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">transitions_style</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">InheritedAnimatedStyleEntry</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">animation_styles</span><span class="o">=</span><span class="p">(</span>
                <span class="p">[</span><span class="n">CSSAnimationStyle</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;animationStyles&quot;</span><span class="p">]]</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;animationStyles&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">transitions_style</span><span class="o">=</span><span class="p">(</span>
                <span class="n">CSSStyle</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;transitionsStyle&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;transitionsStyle&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="InheritedPseudoElementMatches">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.InheritedPseudoElementMatches">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">InheritedPseudoElementMatches</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Inherited pseudo element matches from pseudos of an ancestor node.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: Matches of pseudo styles from the pseudos of an ancestor node.</span>
    <span class="n">pseudo_elements</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">PseudoElementMatches</span><span class="p">]</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;pseudoElements&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">pseudo_elements</span><span class="p">]</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">InheritedPseudoElementMatches</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">pseudo_elements</span><span class="o">=</span><span class="p">[</span>
                <span class="n">PseudoElementMatches</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;pseudoElements&quot;</span><span class="p">]</span>
            <span class="p">],</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="RuleMatch">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.RuleMatch">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">RuleMatch</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Match data for a CSS rule.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: CSS rule in the match.</span>
    <span class="n">rule</span><span class="p">:</span> <span class="n">CSSRule</span>

    <span class="c1">#: Matching selector indices in the rule&#39;s selectorList selectors (0-based).</span>
    <span class="n">matching_selectors</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="nb">int</span><span class="p">]</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;rule&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">rule</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;matchingSelectors&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">matching_selectors</span><span class="p">]</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">RuleMatch</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">rule</span><span class="o">=</span><span class="n">CSSRule</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;rule&quot;</span><span class="p">]),</span>
            <span class="n">matching_selectors</span><span class="o">=</span><span class="p">[</span><span class="nb">int</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;matchingSelectors&quot;</span><span class="p">]],</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="Value">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.Value">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">Value</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Data for a simple selector (these are delimited by commas in a selector list).</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: Value text.</span>
    <span class="n">text</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: Value range in the underlying resource (if available).</span>
    <span class="n">range_</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">SourceRange</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: Specificity of the selector.</span>
    <span class="n">specificity</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">Specificity</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;text&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">text</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">range_</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;range&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">range_</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">specificity</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;specificity&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">specificity</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Value</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">text</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;text&quot;</span><span class="p">]),</span>
            <span class="n">range_</span><span class="o">=</span><span class="p">(</span>
                <span class="n">SourceRange</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;range&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;range&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">specificity</span><span class="o">=</span><span class="p">(</span>
                <span class="n">Specificity</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;specificity&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;specificity&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="Specificity">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.Specificity">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">Specificity</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Specificity:</span>
<span class="sd">    https://drafts.csswg.org/selectors/#specificity-rules</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: The a component, which represents the number of ID selectors.</span>
    <span class="n">a</span><span class="p">:</span> <span class="nb">int</span>

    <span class="c1">#: The b component, which represents the number of class selectors, attributes selectors, and</span>
    <span class="c1">#: pseudo-classes.</span>
    <span class="n">b</span><span class="p">:</span> <span class="nb">int</span>

    <span class="c1">#: The c component, which represents the number of type selectors and pseudo-elements.</span>
    <span class="n">c</span><span class="p">:</span> <span class="nb">int</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;a&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">a</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;b&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">b</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;c&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">c</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Specificity</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">a</span><span class="o">=</span><span class="nb">int</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;a&quot;</span><span class="p">]),</span>
            <span class="n">b</span><span class="o">=</span><span class="nb">int</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;b&quot;</span><span class="p">]),</span>
            <span class="n">c</span><span class="o">=</span><span class="nb">int</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;c&quot;</span><span class="p">]),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="SelectorList">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.SelectorList">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">SelectorList</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Selector list data.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: Selectors in the list.</span>
    <span class="n">selectors</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">Value</span><span class="p">]</span>

    <span class="c1">#: Rule selector text.</span>
    <span class="n">text</span><span class="p">:</span> <span class="nb">str</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;selectors&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">selectors</span><span class="p">]</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;text&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">text</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">SelectorList</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">selectors</span><span class="o">=</span><span class="p">[</span><span class="n">Value</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;selectors&quot;</span><span class="p">]],</span>
            <span class="n">text</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;text&quot;</span><span class="p">]),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="CSSStyleSheetHeader">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.CSSStyleSheetHeader">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">CSSStyleSheetHeader</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    CSS stylesheet metainformation.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: The stylesheet identifier.</span>
    <span class="n">style_sheet_id</span><span class="p">:</span> <span class="n">StyleSheetId</span>

    <span class="c1">#: Owner frame identifier.</span>
    <span class="n">frame_id</span><span class="p">:</span> <span class="n">page</span><span class="o">.</span><span class="n">FrameId</span>

    <span class="c1">#: Stylesheet resource URL. Empty if this is a constructed stylesheet created using</span>
    <span class="c1">#: new CSSStyleSheet() (but non-empty if this is a constructed stylesheet imported</span>
    <span class="c1">#: as a CSS module script).</span>
    <span class="n">source_url</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: Stylesheet origin.</span>
    <span class="n">origin</span><span class="p">:</span> <span class="n">StyleSheetOrigin</span>

    <span class="c1">#: Stylesheet title.</span>
    <span class="n">title</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: Denotes whether the stylesheet is disabled.</span>
    <span class="n">disabled</span><span class="p">:</span> <span class="nb">bool</span>

    <span class="c1">#: Whether this stylesheet is created for STYLE tag by parser. This flag is not set for</span>
    <span class="c1">#: document.written STYLE tags.</span>
    <span class="n">is_inline</span><span class="p">:</span> <span class="nb">bool</span>

    <span class="c1">#: Whether this stylesheet is mutable. Inline stylesheets become mutable</span>
    <span class="c1">#: after they have been modified via CSSOM API.</span>
    <span class="c1">#: ``&lt;link&gt;`` element&#39;s stylesheets become mutable only if DevTools modifies them.</span>
    <span class="c1">#: Constructed stylesheets (new CSSStyleSheet()) are mutable immediately after creation.</span>
    <span class="n">is_mutable</span><span class="p">:</span> <span class="nb">bool</span>

    <span class="c1">#: True if this stylesheet is created through new CSSStyleSheet() or imported as a</span>
    <span class="c1">#: CSS module script.</span>
    <span class="n">is_constructed</span><span class="p">:</span> <span class="nb">bool</span>

    <span class="c1">#: Line offset of the stylesheet within the resource (zero based).</span>
    <span class="n">start_line</span><span class="p">:</span> <span class="nb">float</span>

    <span class="c1">#: Column offset of the stylesheet within the resource (zero based).</span>
    <span class="n">start_column</span><span class="p">:</span> <span class="nb">float</span>

    <span class="c1">#: Size of the content (in characters).</span>
    <span class="n">length</span><span class="p">:</span> <span class="nb">float</span>

    <span class="c1">#: Line offset of the end of the stylesheet within the resource (zero based).</span>
    <span class="n">end_line</span><span class="p">:</span> <span class="nb">float</span>

    <span class="c1">#: Column offset of the end of the stylesheet within the resource (zero based).</span>
    <span class="n">end_column</span><span class="p">:</span> <span class="nb">float</span>

    <span class="c1">#: URL of source map associated with the stylesheet (if any).</span>
    <span class="n">source_map_url</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: The backend id for the owner node of the stylesheet.</span>
    <span class="n">owner_node</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">dom</span><span class="o">.</span><span class="n">BackendNodeId</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: Whether the sourceURL field value comes from the sourceURL comment.</span>
    <span class="n">has_source_url</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">bool</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: If the style sheet was loaded from a network resource, this indicates when the resource failed to load</span>
    <span class="n">loading_failed</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">bool</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">style_sheet_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;frameId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">frame_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;sourceURL&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">source_url</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;origin&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">origin</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;title&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">title</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;disabled&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">disabled</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;isInline&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">is_inline</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;isMutable&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">is_mutable</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;isConstructed&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">is_constructed</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;startLine&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">start_line</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;startColumn&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">start_column</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;length&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">length</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;endLine&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">end_line</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;endColumn&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">end_column</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">source_map_url</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;sourceMapURL&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">source_map_url</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">owner_node</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;ownerNode&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">owner_node</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">has_source_url</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;hasSourceURL&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">has_source_url</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">loading_failed</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;loadingFailed&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">loading_failed</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">CSSStyleSheetHeader</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">style_sheet_id</span><span class="o">=</span><span class="n">StyleSheetId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">]),</span>
            <span class="n">frame_id</span><span class="o">=</span><span class="n">page</span><span class="o">.</span><span class="n">FrameId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;frameId&quot;</span><span class="p">]),</span>
            <span class="n">source_url</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;sourceURL&quot;</span><span class="p">]),</span>
            <span class="n">origin</span><span class="o">=</span><span class="n">StyleSheetOrigin</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;origin&quot;</span><span class="p">]),</span>
            <span class="n">title</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;title&quot;</span><span class="p">]),</span>
            <span class="n">disabled</span><span class="o">=</span><span class="nb">bool</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;disabled&quot;</span><span class="p">]),</span>
            <span class="n">is_inline</span><span class="o">=</span><span class="nb">bool</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;isInline&quot;</span><span class="p">]),</span>
            <span class="n">is_mutable</span><span class="o">=</span><span class="nb">bool</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;isMutable&quot;</span><span class="p">]),</span>
            <span class="n">is_constructed</span><span class="o">=</span><span class="nb">bool</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;isConstructed&quot;</span><span class="p">]),</span>
            <span class="n">start_line</span><span class="o">=</span><span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;startLine&quot;</span><span class="p">]),</span>
            <span class="n">start_column</span><span class="o">=</span><span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;startColumn&quot;</span><span class="p">]),</span>
            <span class="n">length</span><span class="o">=</span><span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;length&quot;</span><span class="p">]),</span>
            <span class="n">end_line</span><span class="o">=</span><span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;endLine&quot;</span><span class="p">]),</span>
            <span class="n">end_column</span><span class="o">=</span><span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;endColumn&quot;</span><span class="p">]),</span>
            <span class="n">source_map_url</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;sourceMapURL&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;sourceMapURL&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">owner_node</span><span class="o">=</span><span class="p">(</span>
                <span class="n">dom</span><span class="o">.</span><span class="n">BackendNodeId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;ownerNode&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;ownerNode&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">has_source_url</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">bool</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;hasSourceURL&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;hasSourceURL&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">loading_failed</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">bool</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;loadingFailed&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;loadingFailed&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="CSSRule">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.CSSRule">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">CSSRule</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    CSS rule representation.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: Rule selector data.</span>
    <span class="n">selector_list</span><span class="p">:</span> <span class="n">SelectorList</span>

    <span class="c1">#: Parent stylesheet&#39;s origin.</span>
    <span class="n">origin</span><span class="p">:</span> <span class="n">StyleSheetOrigin</span>

    <span class="c1">#: Associated style declaration.</span>
    <span class="n">style</span><span class="p">:</span> <span class="n">CSSStyle</span>

    <span class="c1">#: The css style sheet identifier (absent for user agent stylesheet and user-specified</span>
    <span class="c1">#: stylesheet rules) this rule came from.</span>
    <span class="n">style_sheet_id</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">StyleSheetId</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: Array of selectors from ancestor style rules, sorted by distance from the current rule.</span>
    <span class="n">nesting_selectors</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">]]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: Media list array (for rules involving media queries). The array enumerates media queries</span>
    <span class="c1">#: starting with the innermost one, going outwards.</span>
    <span class="n">media</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">CSSMedia</span><span class="p">]]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: Container query list array (for rules involving container queries).</span>
    <span class="c1">#: The array enumerates container queries starting with the innermost one, going outwards.</span>
    <span class="n">container_queries</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">CSSContainerQuery</span><span class="p">]]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: @supports CSS at-rule array.</span>
    <span class="c1">#: The array enumerates @supports at-rules starting with the innermost one, going outwards.</span>
    <span class="n">supports</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">CSSSupports</span><span class="p">]]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: Cascade layer array. Contains the layer hierarchy that this rule belongs to starting</span>
    <span class="c1">#: with the innermost layer and going outwards.</span>
    <span class="n">layers</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">CSSLayer</span><span class="p">]]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: @scope CSS at-rule array.</span>
    <span class="c1">#: The array enumerates @scope at-rules starting with the innermost one, going outwards.</span>
    <span class="n">scopes</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">CSSScope</span><span class="p">]]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: The array keeps the types of ancestor CSSRules from the innermost going outwards.</span>
    <span class="n">rule_types</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">CSSRuleType</span><span class="p">]]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: @starting-style CSS at-rule array.</span>
    <span class="c1">#: The array enumerates @starting-style at-rules starting with the innermost one, going outwards.</span>
    <span class="n">starting_styles</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">CSSStartingStyle</span><span class="p">]]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;selectorList&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">selector_list</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;origin&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">origin</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;style&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">style</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">style_sheet_id</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">style_sheet_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">nesting_selectors</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;nestingSelectors&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">nesting_selectors</span><span class="p">]</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">media</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;media&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">media</span><span class="p">]</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">container_queries</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;containerQueries&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">container_queries</span><span class="p">]</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">supports</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;supports&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">supports</span><span class="p">]</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">layers</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;layers&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">layers</span><span class="p">]</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">scopes</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;scopes&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">scopes</span><span class="p">]</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">rule_types</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;ruleTypes&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">rule_types</span><span class="p">]</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">starting_styles</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;startingStyles&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">starting_styles</span><span class="p">]</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">CSSRule</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">selector_list</span><span class="o">=</span><span class="n">SelectorList</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;selectorList&quot;</span><span class="p">]),</span>
            <span class="n">origin</span><span class="o">=</span><span class="n">StyleSheetOrigin</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;origin&quot;</span><span class="p">]),</span>
            <span class="n">style</span><span class="o">=</span><span class="n">CSSStyle</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;style&quot;</span><span class="p">]),</span>
            <span class="n">style_sheet_id</span><span class="o">=</span><span class="p">(</span>
                <span class="n">StyleSheetId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">nesting_selectors</span><span class="o">=</span><span class="p">(</span>
                <span class="p">[</span><span class="nb">str</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;nestingSelectors&quot;</span><span class="p">]]</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;nestingSelectors&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">media</span><span class="o">=</span><span class="p">(</span>
                <span class="p">[</span><span class="n">CSSMedia</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;media&quot;</span><span class="p">]]</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;media&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">container_queries</span><span class="o">=</span><span class="p">(</span>
                <span class="p">[</span><span class="n">CSSContainerQuery</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;containerQueries&quot;</span><span class="p">]]</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;containerQueries&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">supports</span><span class="o">=</span><span class="p">(</span>
                <span class="p">[</span><span class="n">CSSSupports</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;supports&quot;</span><span class="p">]]</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;supports&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">layers</span><span class="o">=</span><span class="p">(</span>
                <span class="p">[</span><span class="n">CSSLayer</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;layers&quot;</span><span class="p">]]</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;layers&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">scopes</span><span class="o">=</span><span class="p">(</span>
                <span class="p">[</span><span class="n">CSSScope</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;scopes&quot;</span><span class="p">]]</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;scopes&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">rule_types</span><span class="o">=</span><span class="p">(</span>
                <span class="p">[</span><span class="n">CSSRuleType</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;ruleTypes&quot;</span><span class="p">]]</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;ruleTypes&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">starting_styles</span><span class="o">=</span><span class="p">(</span>
                <span class="p">[</span><span class="n">CSSStartingStyle</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;startingStyles&quot;</span><span class="p">]]</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;startingStyles&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="CSSRuleType">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.CSSRuleType">[docs]</a>
<span class="k">class</span> <span class="nc">CSSRuleType</span><span class="p">(</span><span class="n">enum</span><span class="o">.</span><span class="n">Enum</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Enum indicating the type of a CSS rule, used to represent the order of a style rule&#39;s ancestors.</span>
<span class="sd">    This list only contains rule types that are collected during the ancestor rule collection.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">MEDIA_RULE</span> <span class="o">=</span> <span class="s2">&quot;MediaRule&quot;</span>
    <span class="n">SUPPORTS_RULE</span> <span class="o">=</span> <span class="s2">&quot;SupportsRule&quot;</span>
    <span class="n">CONTAINER_RULE</span> <span class="o">=</span> <span class="s2">&quot;ContainerRule&quot;</span>
    <span class="n">LAYER_RULE</span> <span class="o">=</span> <span class="s2">&quot;LayerRule&quot;</span>
    <span class="n">SCOPE_RULE</span> <span class="o">=</span> <span class="s2">&quot;ScopeRule&quot;</span>
    <span class="n">STYLE_RULE</span> <span class="o">=</span> <span class="s2">&quot;StyleRule&quot;</span>
    <span class="n">STARTING_STYLE_RULE</span> <span class="o">=</span> <span class="s2">&quot;StartingStyleRule&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">CSSRuleType</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span></div>



<div class="viewcode-block" id="RuleUsage">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.RuleUsage">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">RuleUsage</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    CSS coverage information.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: The css style sheet identifier (absent for user agent stylesheet and user-specified</span>
    <span class="c1">#: stylesheet rules) this rule came from.</span>
    <span class="n">style_sheet_id</span><span class="p">:</span> <span class="n">StyleSheetId</span>

    <span class="c1">#: Offset of the start of the rule (including selector) from the beginning of the stylesheet.</span>
    <span class="n">start_offset</span><span class="p">:</span> <span class="nb">float</span>

    <span class="c1">#: Offset of the end of the rule body from the beginning of the stylesheet.</span>
    <span class="n">end_offset</span><span class="p">:</span> <span class="nb">float</span>

    <span class="c1">#: Indicates whether the rule was actually used by some element in the page.</span>
    <span class="n">used</span><span class="p">:</span> <span class="nb">bool</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">style_sheet_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;startOffset&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">start_offset</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;endOffset&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">end_offset</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;used&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">used</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">RuleUsage</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">style_sheet_id</span><span class="o">=</span><span class="n">StyleSheetId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">]),</span>
            <span class="n">start_offset</span><span class="o">=</span><span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;startOffset&quot;</span><span class="p">]),</span>
            <span class="n">end_offset</span><span class="o">=</span><span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;endOffset&quot;</span><span class="p">]),</span>
            <span class="n">used</span><span class="o">=</span><span class="nb">bool</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;used&quot;</span><span class="p">]),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="SourceRange">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.SourceRange">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">SourceRange</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Text range within a resource. All numbers are zero-based.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: Start line of range.</span>
    <span class="n">start_line</span><span class="p">:</span> <span class="nb">int</span>

    <span class="c1">#: Start column of range (inclusive).</span>
    <span class="n">start_column</span><span class="p">:</span> <span class="nb">int</span>

    <span class="c1">#: End line of range</span>
    <span class="n">end_line</span><span class="p">:</span> <span class="nb">int</span>

    <span class="c1">#: End column of range (exclusive).</span>
    <span class="n">end_column</span><span class="p">:</span> <span class="nb">int</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;startLine&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">start_line</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;startColumn&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">start_column</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;endLine&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">end_line</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;endColumn&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">end_column</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">SourceRange</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">start_line</span><span class="o">=</span><span class="nb">int</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;startLine&quot;</span><span class="p">]),</span>
            <span class="n">start_column</span><span class="o">=</span><span class="nb">int</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;startColumn&quot;</span><span class="p">]),</span>
            <span class="n">end_line</span><span class="o">=</span><span class="nb">int</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;endLine&quot;</span><span class="p">]),</span>
            <span class="n">end_column</span><span class="o">=</span><span class="nb">int</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;endColumn&quot;</span><span class="p">]),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="ShorthandEntry">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.ShorthandEntry">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">ShorthandEntry</span><span class="p">:</span>
    <span class="c1">#: Shorthand name.</span>
    <span class="n">name</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: Shorthand value.</span>
    <span class="n">value</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: Whether the property has &quot;!important&quot; annotation (implies ``false`` if absent).</span>
    <span class="n">important</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">bool</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;name&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">name</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;value&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">important</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;important&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">important</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">ShorthandEntry</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">name</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;name&quot;</span><span class="p">]),</span>
            <span class="n">value</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;value&quot;</span><span class="p">]),</span>
            <span class="n">important</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">bool</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;important&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;important&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="CSSComputedStyleProperty">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.CSSComputedStyleProperty">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">CSSComputedStyleProperty</span><span class="p">:</span>
    <span class="c1">#: Computed style property name.</span>
    <span class="n">name</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: Computed style property value.</span>
    <span class="n">value</span><span class="p">:</span> <span class="nb">str</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;name&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">name</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;value&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">CSSComputedStyleProperty</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">name</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;name&quot;</span><span class="p">]),</span>
            <span class="n">value</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;value&quot;</span><span class="p">]),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="CSSStyle">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.CSSStyle">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">CSSStyle</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    CSS style representation.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: CSS properties in the style.</span>
    <span class="n">css_properties</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">CSSProperty</span><span class="p">]</span>

    <span class="c1">#: Computed values for all shorthands found in the style.</span>
    <span class="n">shorthand_entries</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">ShorthandEntry</span><span class="p">]</span>

    <span class="c1">#: The css style sheet identifier (absent for user agent stylesheet and user-specified</span>
    <span class="c1">#: stylesheet rules) this rule came from.</span>
    <span class="n">style_sheet_id</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">StyleSheetId</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: Style declaration text (if available).</span>
    <span class="n">css_text</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: Style declaration range in the enclosing stylesheet (if available).</span>
    <span class="n">range_</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">SourceRange</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;cssProperties&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">css_properties</span><span class="p">]</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;shorthandEntries&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">shorthand_entries</span><span class="p">]</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">style_sheet_id</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">style_sheet_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">css_text</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;cssText&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">css_text</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">range_</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;range&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">range_</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">CSSStyle</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">css_properties</span><span class="o">=</span><span class="p">[</span><span class="n">CSSProperty</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;cssProperties&quot;</span><span class="p">]],</span>
            <span class="n">shorthand_entries</span><span class="o">=</span><span class="p">[</span>
                <span class="n">ShorthandEntry</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;shorthandEntries&quot;</span><span class="p">]</span>
            <span class="p">],</span>
            <span class="n">style_sheet_id</span><span class="o">=</span><span class="p">(</span>
                <span class="n">StyleSheetId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">css_text</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;cssText&quot;</span><span class="p">])</span> <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;cssText&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span> <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">range_</span><span class="o">=</span><span class="p">(</span>
                <span class="n">SourceRange</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;range&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;range&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="CSSProperty">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.CSSProperty">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">CSSProperty</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    CSS property declaration data.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: The property name.</span>
    <span class="n">name</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: The property value.</span>
    <span class="n">value</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: Whether the property has &quot;!important&quot; annotation (implies ``false`` if absent).</span>
    <span class="n">important</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">bool</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: Whether the property is implicit (implies ``false`` if absent).</span>
    <span class="n">implicit</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">bool</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: The full property text as specified in the style.</span>
    <span class="n">text</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: Whether the property is understood by the browser (implies ``true`` if absent).</span>
    <span class="n">parsed_ok</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">bool</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: Whether the property is disabled by the user (present for source-based properties only).</span>
    <span class="n">disabled</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">bool</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: The entire property range in the enclosing style declaration (if available).</span>
    <span class="n">range_</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">SourceRange</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: Parsed longhand components of this property if it is a shorthand.</span>
    <span class="c1">#: This field will be empty if the given property is not a shorthand.</span>
    <span class="n">longhand_properties</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">CSSProperty</span><span class="p">]]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;name&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">name</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;value&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">important</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;important&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">important</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">implicit</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;implicit&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">implicit</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">text</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;text&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">text</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">parsed_ok</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;parsedOk&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">parsed_ok</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">disabled</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;disabled&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">disabled</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">range_</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;range&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">range_</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">longhand_properties</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;longhandProperties&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">longhand_properties</span><span class="p">]</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">CSSProperty</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">name</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;name&quot;</span><span class="p">]),</span>
            <span class="n">value</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;value&quot;</span><span class="p">]),</span>
            <span class="n">important</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">bool</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;important&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;important&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">implicit</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">bool</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;implicit&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;implicit&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">text</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;text&quot;</span><span class="p">])</span> <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;text&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span> <span class="k">else</span> <span class="kc">None</span><span class="p">,</span>
            <span class="n">parsed_ok</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">bool</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;parsedOk&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;parsedOk&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">disabled</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">bool</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;disabled&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;disabled&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">range_</span><span class="o">=</span><span class="p">(</span>
                <span class="n">SourceRange</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;range&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;range&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">longhand_properties</span><span class="o">=</span><span class="p">(</span>
                <span class="p">[</span><span class="n">CSSProperty</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;longhandProperties&quot;</span><span class="p">]]</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;longhandProperties&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="CSSMedia">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.CSSMedia">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">CSSMedia</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    CSS media rule descriptor.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: Media query text.</span>
    <span class="n">text</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: Source of the media query: &quot;mediaRule&quot; if specified by a @media rule, &quot;importRule&quot; if</span>
    <span class="c1">#: specified by an @import rule, &quot;linkedSheet&quot; if specified by a &quot;media&quot; attribute in a linked</span>
    <span class="c1">#: stylesheet&#39;s LINK tag, &quot;inlineSheet&quot; if specified by a &quot;media&quot; attribute in an inline</span>
    <span class="c1">#: stylesheet&#39;s STYLE tag.</span>
    <span class="n">source</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: URL of the document containing the media query description.</span>
    <span class="n">source_url</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: The associated rule (@media or @import) header range in the enclosing stylesheet (if</span>
    <span class="c1">#: available).</span>
    <span class="n">range_</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">SourceRange</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: Identifier of the stylesheet containing this object (if exists).</span>
    <span class="n">style_sheet_id</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">StyleSheetId</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: Array of media queries.</span>
    <span class="n">media_list</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">MediaQuery</span><span class="p">]]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;text&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">text</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;source&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">source</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">source_url</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;sourceURL&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">source_url</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">range_</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;range&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">range_</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">style_sheet_id</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">style_sheet_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">media_list</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;mediaList&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">media_list</span><span class="p">]</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">CSSMedia</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">text</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;text&quot;</span><span class="p">]),</span>
            <span class="n">source</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;source&quot;</span><span class="p">]),</span>
            <span class="n">source_url</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;sourceURL&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;sourceURL&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">range_</span><span class="o">=</span><span class="p">(</span>
                <span class="n">SourceRange</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;range&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;range&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">style_sheet_id</span><span class="o">=</span><span class="p">(</span>
                <span class="n">StyleSheetId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">media_list</span><span class="o">=</span><span class="p">(</span>
                <span class="p">[</span><span class="n">MediaQuery</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;mediaList&quot;</span><span class="p">]]</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;mediaList&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="MediaQuery">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.MediaQuery">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">MediaQuery</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Media query descriptor.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: Array of media query expressions.</span>
    <span class="n">expressions</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">MediaQueryExpression</span><span class="p">]</span>

    <span class="c1">#: Whether the media query condition is satisfied.</span>
    <span class="n">active</span><span class="p">:</span> <span class="nb">bool</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;expressions&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">expressions</span><span class="p">]</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;active&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">active</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">MediaQuery</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">expressions</span><span class="o">=</span><span class="p">[</span>
                <span class="n">MediaQueryExpression</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;expressions&quot;</span><span class="p">]</span>
            <span class="p">],</span>
            <span class="n">active</span><span class="o">=</span><span class="nb">bool</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;active&quot;</span><span class="p">]),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="MediaQueryExpression">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.MediaQueryExpression">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">MediaQueryExpression</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Media query expression descriptor.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: Media query expression value.</span>
    <span class="n">value</span><span class="p">:</span> <span class="nb">float</span>

    <span class="c1">#: Media query expression units.</span>
    <span class="n">unit</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: Media query expression feature.</span>
    <span class="n">feature</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: The associated range of the value text in the enclosing stylesheet (if available).</span>
    <span class="n">value_range</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">SourceRange</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: Computed length of media query expression (if applicable).</span>
    <span class="n">computed_length</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">float</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;value&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;unit&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">unit</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;feature&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">feature</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">value_range</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;valueRange&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">value_range</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">computed_length</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;computedLength&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">computed_length</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">MediaQueryExpression</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">value</span><span class="o">=</span><span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;value&quot;</span><span class="p">]),</span>
            <span class="n">unit</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;unit&quot;</span><span class="p">]),</span>
            <span class="n">feature</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;feature&quot;</span><span class="p">]),</span>
            <span class="n">value_range</span><span class="o">=</span><span class="p">(</span>
                <span class="n">SourceRange</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;valueRange&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;valueRange&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">computed_length</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;computedLength&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;computedLength&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="CSSContainerQuery">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.CSSContainerQuery">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">CSSContainerQuery</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    CSS container query rule descriptor.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: Container query text.</span>
    <span class="n">text</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: The associated rule header range in the enclosing stylesheet (if</span>
    <span class="c1">#: available).</span>
    <span class="n">range_</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">SourceRange</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: Identifier of the stylesheet containing this object (if exists).</span>
    <span class="n">style_sheet_id</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">StyleSheetId</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: Optional name for the container.</span>
    <span class="n">name</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: Optional physical axes queried for the container.</span>
    <span class="n">physical_axes</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">dom</span><span class="o">.</span><span class="n">PhysicalAxes</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: Optional logical axes queried for the container.</span>
    <span class="n">logical_axes</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">dom</span><span class="o">.</span><span class="n">LogicalAxes</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: true if the query contains scroll-state() queries.</span>
    <span class="n">queries_scroll_state</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">bool</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;text&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">text</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">range_</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;range&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">range_</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">style_sheet_id</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">style_sheet_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">name</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;name&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">name</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">physical_axes</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;physicalAxes&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">physical_axes</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">logical_axes</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;logicalAxes&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">logical_axes</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">queries_scroll_state</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;queriesScrollState&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">queries_scroll_state</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">CSSContainerQuery</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">text</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;text&quot;</span><span class="p">]),</span>
            <span class="n">range_</span><span class="o">=</span><span class="p">(</span>
                <span class="n">SourceRange</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;range&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;range&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">style_sheet_id</span><span class="o">=</span><span class="p">(</span>
                <span class="n">StyleSheetId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">name</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;name&quot;</span><span class="p">])</span> <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;name&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span> <span class="k">else</span> <span class="kc">None</span><span class="p">,</span>
            <span class="n">physical_axes</span><span class="o">=</span><span class="p">(</span>
                <span class="n">dom</span><span class="o">.</span><span class="n">PhysicalAxes</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;physicalAxes&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;physicalAxes&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">logical_axes</span><span class="o">=</span><span class="p">(</span>
                <span class="n">dom</span><span class="o">.</span><span class="n">LogicalAxes</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;logicalAxes&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;logicalAxes&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">queries_scroll_state</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">bool</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;queriesScrollState&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;queriesScrollState&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="CSSSupports">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.CSSSupports">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">CSSSupports</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    CSS Supports at-rule descriptor.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: Supports rule text.</span>
    <span class="n">text</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: Whether the supports condition is satisfied.</span>
    <span class="n">active</span><span class="p">:</span> <span class="nb">bool</span>

    <span class="c1">#: The associated rule header range in the enclosing stylesheet (if</span>
    <span class="c1">#: available).</span>
    <span class="n">range_</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">SourceRange</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: Identifier of the stylesheet containing this object (if exists).</span>
    <span class="n">style_sheet_id</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">StyleSheetId</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;text&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">text</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;active&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">active</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">range_</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;range&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">range_</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">style_sheet_id</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">style_sheet_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">CSSSupports</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">text</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;text&quot;</span><span class="p">]),</span>
            <span class="n">active</span><span class="o">=</span><span class="nb">bool</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;active&quot;</span><span class="p">]),</span>
            <span class="n">range_</span><span class="o">=</span><span class="p">(</span>
                <span class="n">SourceRange</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;range&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;range&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">style_sheet_id</span><span class="o">=</span><span class="p">(</span>
                <span class="n">StyleSheetId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="CSSScope">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.CSSScope">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">CSSScope</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    CSS Scope at-rule descriptor.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: Scope rule text.</span>
    <span class="n">text</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: The associated rule header range in the enclosing stylesheet (if</span>
    <span class="c1">#: available).</span>
    <span class="n">range_</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">SourceRange</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: Identifier of the stylesheet containing this object (if exists).</span>
    <span class="n">style_sheet_id</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">StyleSheetId</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;text&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">text</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">range_</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;range&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">range_</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">style_sheet_id</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">style_sheet_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">CSSScope</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">text</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;text&quot;</span><span class="p">]),</span>
            <span class="n">range_</span><span class="o">=</span><span class="p">(</span>
                <span class="n">SourceRange</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;range&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;range&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">style_sheet_id</span><span class="o">=</span><span class="p">(</span>
                <span class="n">StyleSheetId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="CSSLayer">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.CSSLayer">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">CSSLayer</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    CSS Layer at-rule descriptor.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: Layer name.</span>
    <span class="n">text</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: The associated rule header range in the enclosing stylesheet (if</span>
    <span class="c1">#: available).</span>
    <span class="n">range_</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">SourceRange</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: Identifier of the stylesheet containing this object (if exists).</span>
    <span class="n">style_sheet_id</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">StyleSheetId</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;text&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">text</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">range_</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;range&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">range_</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">style_sheet_id</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">style_sheet_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">CSSLayer</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">text</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;text&quot;</span><span class="p">]),</span>
            <span class="n">range_</span><span class="o">=</span><span class="p">(</span>
                <span class="n">SourceRange</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;range&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;range&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">style_sheet_id</span><span class="o">=</span><span class="p">(</span>
                <span class="n">StyleSheetId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="CSSStartingStyle">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.CSSStartingStyle">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">CSSStartingStyle</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    CSS Starting Style at-rule descriptor.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: The associated rule header range in the enclosing stylesheet (if</span>
    <span class="c1">#: available).</span>
    <span class="n">range_</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">SourceRange</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: Identifier of the stylesheet containing this object (if exists).</span>
    <span class="n">style_sheet_id</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">StyleSheetId</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">range_</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;range&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">range_</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">style_sheet_id</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">style_sheet_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">CSSStartingStyle</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">range_</span><span class="o">=</span><span class="p">(</span>
                <span class="n">SourceRange</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;range&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;range&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">style_sheet_id</span><span class="o">=</span><span class="p">(</span>
                <span class="n">StyleSheetId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="CSSLayerData">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.CSSLayerData">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">CSSLayerData</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    CSS Layer data.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: Layer name.</span>
    <span class="n">name</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: Layer order. The order determines the order of the layer in the cascade order.</span>
    <span class="c1">#: A higher number has higher priority in the cascade order.</span>
    <span class="n">order</span><span class="p">:</span> <span class="nb">float</span>

    <span class="c1">#: Direct sub-layers</span>
    <span class="n">sub_layers</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">CSSLayerData</span><span class="p">]]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;name&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">name</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;order&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">order</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">sub_layers</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;subLayers&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">sub_layers</span><span class="p">]</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">CSSLayerData</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">name</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;name&quot;</span><span class="p">]),</span>
            <span class="n">order</span><span class="o">=</span><span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;order&quot;</span><span class="p">]),</span>
            <span class="n">sub_layers</span><span class="o">=</span><span class="p">(</span>
                <span class="p">[</span><span class="n">CSSLayerData</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;subLayers&quot;</span><span class="p">]]</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;subLayers&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="PlatformFontUsage">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.PlatformFontUsage">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">PlatformFontUsage</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Information about amount of glyphs that were rendered with given font.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: Font&#39;s family name reported by platform.</span>
    <span class="n">family_name</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: Font&#39;s PostScript name reported by platform.</span>
    <span class="n">post_script_name</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: Indicates if the font was downloaded or resolved locally.</span>
    <span class="n">is_custom_font</span><span class="p">:</span> <span class="nb">bool</span>

    <span class="c1">#: Amount of glyphs that were rendered with this font.</span>
    <span class="n">glyph_count</span><span class="p">:</span> <span class="nb">float</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;familyName&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">family_name</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;postScriptName&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">post_script_name</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;isCustomFont&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">is_custom_font</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;glyphCount&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">glyph_count</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">PlatformFontUsage</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">family_name</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;familyName&quot;</span><span class="p">]),</span>
            <span class="n">post_script_name</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;postScriptName&quot;</span><span class="p">]),</span>
            <span class="n">is_custom_font</span><span class="o">=</span><span class="nb">bool</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;isCustomFont&quot;</span><span class="p">]),</span>
            <span class="n">glyph_count</span><span class="o">=</span><span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;glyphCount&quot;</span><span class="p">]),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="FontVariationAxis">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.FontVariationAxis">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">FontVariationAxis</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Information about font variation axes for variable fonts</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: The font-variation-setting tag (a.k.a. &quot;axis tag&quot;).</span>
    <span class="n">tag</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: Human-readable variation name in the default language (normally, &quot;en&quot;).</span>
    <span class="n">name</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: The minimum value (inclusive) the font supports for this tag.</span>
    <span class="n">min_value</span><span class="p">:</span> <span class="nb">float</span>

    <span class="c1">#: The maximum value (inclusive) the font supports for this tag.</span>
    <span class="n">max_value</span><span class="p">:</span> <span class="nb">float</span>

    <span class="c1">#: The default value.</span>
    <span class="n">default_value</span><span class="p">:</span> <span class="nb">float</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;tag&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">tag</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;name&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">name</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;minValue&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">min_value</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;maxValue&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">max_value</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;defaultValue&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">default_value</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">FontVariationAxis</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">tag</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;tag&quot;</span><span class="p">]),</span>
            <span class="n">name</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;name&quot;</span><span class="p">]),</span>
            <span class="n">min_value</span><span class="o">=</span><span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;minValue&quot;</span><span class="p">]),</span>
            <span class="n">max_value</span><span class="o">=</span><span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;maxValue&quot;</span><span class="p">]),</span>
            <span class="n">default_value</span><span class="o">=</span><span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;defaultValue&quot;</span><span class="p">]),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="FontFace">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.FontFace">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">FontFace</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Properties of a web font: https://www.w3.org/TR/2008/REC-CSS2-20080411/fonts.html#font-descriptions</span>
<span class="sd">    and additional information such as platformFontFamily and fontVariationAxes.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: The font-family.</span>
    <span class="n">font_family</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: The font-style.</span>
    <span class="n">font_style</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: The font-variant.</span>
    <span class="n">font_variant</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: The font-weight.</span>
    <span class="n">font_weight</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: The font-stretch.</span>
    <span class="n">font_stretch</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: The font-display.</span>
    <span class="n">font_display</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: The unicode-range.</span>
    <span class="n">unicode_range</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: The src.</span>
    <span class="n">src</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: The resolved platform font family</span>
    <span class="n">platform_font_family</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: Available variation settings (a.k.a. &quot;axes&quot;).</span>
    <span class="n">font_variation_axes</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">FontVariationAxis</span><span class="p">]]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;fontFamily&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">font_family</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;fontStyle&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">font_style</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;fontVariant&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">font_variant</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;fontWeight&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">font_weight</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;fontStretch&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">font_stretch</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;fontDisplay&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">font_display</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;unicodeRange&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">unicode_range</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;src&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">src</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;platformFontFamily&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">platform_font_family</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">font_variation_axes</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;fontVariationAxes&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">font_variation_axes</span><span class="p">]</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">FontFace</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">font_family</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;fontFamily&quot;</span><span class="p">]),</span>
            <span class="n">font_style</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;fontStyle&quot;</span><span class="p">]),</span>
            <span class="n">font_variant</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;fontVariant&quot;</span><span class="p">]),</span>
            <span class="n">font_weight</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;fontWeight&quot;</span><span class="p">]),</span>
            <span class="n">font_stretch</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;fontStretch&quot;</span><span class="p">]),</span>
            <span class="n">font_display</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;fontDisplay&quot;</span><span class="p">]),</span>
            <span class="n">unicode_range</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;unicodeRange&quot;</span><span class="p">]),</span>
            <span class="n">src</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;src&quot;</span><span class="p">]),</span>
            <span class="n">platform_font_family</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;platformFontFamily&quot;</span><span class="p">]),</span>
            <span class="n">font_variation_axes</span><span class="o">=</span><span class="p">(</span>
                <span class="p">[</span><span class="n">FontVariationAxis</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;fontVariationAxes&quot;</span><span class="p">]]</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;fontVariationAxes&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="CSSTryRule">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.CSSTryRule">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">CSSTryRule</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    CSS try rule representation.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: Parent stylesheet&#39;s origin.</span>
    <span class="n">origin</span><span class="p">:</span> <span class="n">StyleSheetOrigin</span>

    <span class="c1">#: Associated style declaration.</span>
    <span class="n">style</span><span class="p">:</span> <span class="n">CSSStyle</span>

    <span class="c1">#: The css style sheet identifier (absent for user agent stylesheet and user-specified</span>
    <span class="c1">#: stylesheet rules) this rule came from.</span>
    <span class="n">style_sheet_id</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">StyleSheetId</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;origin&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">origin</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;style&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">style</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">style_sheet_id</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">style_sheet_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">CSSTryRule</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">origin</span><span class="o">=</span><span class="n">StyleSheetOrigin</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;origin&quot;</span><span class="p">]),</span>
            <span class="n">style</span><span class="o">=</span><span class="n">CSSStyle</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;style&quot;</span><span class="p">]),</span>
            <span class="n">style_sheet_id</span><span class="o">=</span><span class="p">(</span>
                <span class="n">StyleSheetId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="CSSPositionTryRule">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.CSSPositionTryRule">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">CSSPositionTryRule</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    CSS @position-try rule representation.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: The prelude dashed-ident name</span>
    <span class="n">name</span><span class="p">:</span> <span class="n">Value</span>

    <span class="c1">#: Parent stylesheet&#39;s origin.</span>
    <span class="n">origin</span><span class="p">:</span> <span class="n">StyleSheetOrigin</span>

    <span class="c1">#: Associated style declaration.</span>
    <span class="n">style</span><span class="p">:</span> <span class="n">CSSStyle</span>

    <span class="n">active</span><span class="p">:</span> <span class="nb">bool</span>

    <span class="c1">#: The css style sheet identifier (absent for user agent stylesheet and user-specified</span>
    <span class="c1">#: stylesheet rules) this rule came from.</span>
    <span class="n">style_sheet_id</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">StyleSheetId</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;name&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">name</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;origin&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">origin</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;style&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">style</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;active&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">active</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">style_sheet_id</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">style_sheet_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">CSSPositionTryRule</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">name</span><span class="o">=</span><span class="n">Value</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;name&quot;</span><span class="p">]),</span>
            <span class="n">origin</span><span class="o">=</span><span class="n">StyleSheetOrigin</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;origin&quot;</span><span class="p">]),</span>
            <span class="n">style</span><span class="o">=</span><span class="n">CSSStyle</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;style&quot;</span><span class="p">]),</span>
            <span class="n">active</span><span class="o">=</span><span class="nb">bool</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;active&quot;</span><span class="p">]),</span>
            <span class="n">style_sheet_id</span><span class="o">=</span><span class="p">(</span>
                <span class="n">StyleSheetId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="CSSKeyframesRule">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.CSSKeyframesRule">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">CSSKeyframesRule</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    CSS keyframes rule representation.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: Animation name.</span>
    <span class="n">animation_name</span><span class="p">:</span> <span class="n">Value</span>

    <span class="c1">#: List of keyframes.</span>
    <span class="n">keyframes</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">CSSKeyframeRule</span><span class="p">]</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;animationName&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">animation_name</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;keyframes&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">keyframes</span><span class="p">]</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">CSSKeyframesRule</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">animation_name</span><span class="o">=</span><span class="n">Value</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;animationName&quot;</span><span class="p">]),</span>
            <span class="n">keyframes</span><span class="o">=</span><span class="p">[</span><span class="n">CSSKeyframeRule</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;keyframes&quot;</span><span class="p">]],</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="CSSPropertyRegistration">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.CSSPropertyRegistration">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">CSSPropertyRegistration</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Representation of a custom property registration through CSS.registerProperty</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">property_name</span><span class="p">:</span> <span class="nb">str</span>

    <span class="n">inherits</span><span class="p">:</span> <span class="nb">bool</span>

    <span class="n">syntax</span><span class="p">:</span> <span class="nb">str</span>

    <span class="n">initial_value</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">Value</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;propertyName&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">property_name</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;inherits&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">inherits</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;syntax&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">syntax</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">initial_value</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;initialValue&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">initial_value</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">CSSPropertyRegistration</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">property_name</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;propertyName&quot;</span><span class="p">]),</span>
            <span class="n">inherits</span><span class="o">=</span><span class="nb">bool</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;inherits&quot;</span><span class="p">]),</span>
            <span class="n">syntax</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;syntax&quot;</span><span class="p">]),</span>
            <span class="n">initial_value</span><span class="o">=</span><span class="p">(</span>
                <span class="n">Value</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;initialValue&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;initialValue&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="CSSFontPaletteValuesRule">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.CSSFontPaletteValuesRule">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">CSSFontPaletteValuesRule</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    CSS font-palette-values rule representation.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: Parent stylesheet&#39;s origin.</span>
    <span class="n">origin</span><span class="p">:</span> <span class="n">StyleSheetOrigin</span>

    <span class="c1">#: Associated font palette name.</span>
    <span class="n">font_palette_name</span><span class="p">:</span> <span class="n">Value</span>

    <span class="c1">#: Associated style declaration.</span>
    <span class="n">style</span><span class="p">:</span> <span class="n">CSSStyle</span>

    <span class="c1">#: The css style sheet identifier (absent for user agent stylesheet and user-specified</span>
    <span class="c1">#: stylesheet rules) this rule came from.</span>
    <span class="n">style_sheet_id</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">StyleSheetId</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;origin&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">origin</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;fontPaletteName&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">font_palette_name</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;style&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">style</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">style_sheet_id</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">style_sheet_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">CSSFontPaletteValuesRule</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">origin</span><span class="o">=</span><span class="n">StyleSheetOrigin</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;origin&quot;</span><span class="p">]),</span>
            <span class="n">font_palette_name</span><span class="o">=</span><span class="n">Value</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;fontPaletteName&quot;</span><span class="p">]),</span>
            <span class="n">style</span><span class="o">=</span><span class="n">CSSStyle</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;style&quot;</span><span class="p">]),</span>
            <span class="n">style_sheet_id</span><span class="o">=</span><span class="p">(</span>
                <span class="n">StyleSheetId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="CSSPropertyRule">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.CSSPropertyRule">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">CSSPropertyRule</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    CSS property at-rule representation.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: Parent stylesheet&#39;s origin.</span>
    <span class="n">origin</span><span class="p">:</span> <span class="n">StyleSheetOrigin</span>

    <span class="c1">#: Associated property name.</span>
    <span class="n">property_name</span><span class="p">:</span> <span class="n">Value</span>

    <span class="c1">#: Associated style declaration.</span>
    <span class="n">style</span><span class="p">:</span> <span class="n">CSSStyle</span>

    <span class="c1">#: The css style sheet identifier (absent for user agent stylesheet and user-specified</span>
    <span class="c1">#: stylesheet rules) this rule came from.</span>
    <span class="n">style_sheet_id</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">StyleSheetId</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;origin&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">origin</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;propertyName&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">property_name</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;style&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">style</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">style_sheet_id</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">style_sheet_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">CSSPropertyRule</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">origin</span><span class="o">=</span><span class="n">StyleSheetOrigin</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;origin&quot;</span><span class="p">]),</span>
            <span class="n">property_name</span><span class="o">=</span><span class="n">Value</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;propertyName&quot;</span><span class="p">]),</span>
            <span class="n">style</span><span class="o">=</span><span class="n">CSSStyle</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;style&quot;</span><span class="p">]),</span>
            <span class="n">style_sheet_id</span><span class="o">=</span><span class="p">(</span>
                <span class="n">StyleSheetId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="CSSFunctionParameter">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.CSSFunctionParameter">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">CSSFunctionParameter</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    CSS function argument representation.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: The parameter name.</span>
    <span class="n">name</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: The parameter type.</span>
    <span class="n">type_</span><span class="p">:</span> <span class="nb">str</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;name&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">name</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;type&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">type_</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">CSSFunctionParameter</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">name</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;name&quot;</span><span class="p">]),</span>
            <span class="n">type_</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;type&quot;</span><span class="p">]),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="CSSFunctionConditionNode">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.CSSFunctionConditionNode">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">CSSFunctionConditionNode</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    CSS function conditional block representation.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: Block body.</span>
    <span class="n">children</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">CSSFunctionNode</span><span class="p">]</span>

    <span class="c1">#: The condition text.</span>
    <span class="n">condition_text</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: Media query for this conditional block. Only one type of condition should be set.</span>
    <span class="n">media</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">CSSMedia</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: Container query for this conditional block. Only one type of condition should be set.</span>
    <span class="n">container_queries</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">CSSContainerQuery</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: @supports CSS at-rule condition. Only one type of condition should be set.</span>
    <span class="n">supports</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">CSSSupports</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;children&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">children</span><span class="p">]</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;conditionText&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">condition_text</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">media</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;media&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">media</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">container_queries</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;containerQueries&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">container_queries</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">supports</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;supports&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">supports</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">CSSFunctionConditionNode</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">children</span><span class="o">=</span><span class="p">[</span><span class="n">CSSFunctionNode</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;children&quot;</span><span class="p">]],</span>
            <span class="n">condition_text</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;conditionText&quot;</span><span class="p">]),</span>
            <span class="n">media</span><span class="o">=</span><span class="p">(</span>
                <span class="n">CSSMedia</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;media&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;media&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">container_queries</span><span class="o">=</span><span class="p">(</span>
                <span class="n">CSSContainerQuery</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;containerQueries&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;containerQueries&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">supports</span><span class="o">=</span><span class="p">(</span>
                <span class="n">CSSSupports</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;supports&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;supports&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="CSSFunctionNode">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.CSSFunctionNode">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">CSSFunctionNode</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Section of the body of a CSS function rule.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: A conditional block. If set, style should not be set.</span>
    <span class="n">condition</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">CSSFunctionConditionNode</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: Values set by this node. If set, condition should not be set.</span>
    <span class="n">style</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">CSSStyle</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">condition</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;condition&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">condition</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">style</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;style&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">style</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">CSSFunctionNode</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">condition</span><span class="o">=</span><span class="p">(</span>
                <span class="n">CSSFunctionConditionNode</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;condition&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;condition&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">style</span><span class="o">=</span><span class="p">(</span>
                <span class="n">CSSStyle</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;style&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;style&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="CSSFunctionRule">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.CSSFunctionRule">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">CSSFunctionRule</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    CSS function at-rule representation.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: Name of the function.</span>
    <span class="n">name</span><span class="p">:</span> <span class="n">Value</span>

    <span class="c1">#: Parent stylesheet&#39;s origin.</span>
    <span class="n">origin</span><span class="p">:</span> <span class="n">StyleSheetOrigin</span>

    <span class="c1">#: List of parameters.</span>
    <span class="n">parameters</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">CSSFunctionParameter</span><span class="p">]</span>

    <span class="c1">#: Function body.</span>
    <span class="n">children</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">CSSFunctionNode</span><span class="p">]</span>

    <span class="c1">#: The css style sheet identifier (absent for user agent stylesheet and user-specified</span>
    <span class="c1">#: stylesheet rules) this rule came from.</span>
    <span class="n">style_sheet_id</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">StyleSheetId</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;name&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">name</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;origin&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">origin</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;parameters&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">parameters</span><span class="p">]</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;children&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">children</span><span class="p">]</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">style_sheet_id</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">style_sheet_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">CSSFunctionRule</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">name</span><span class="o">=</span><span class="n">Value</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;name&quot;</span><span class="p">]),</span>
            <span class="n">origin</span><span class="o">=</span><span class="n">StyleSheetOrigin</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;origin&quot;</span><span class="p">]),</span>
            <span class="n">parameters</span><span class="o">=</span><span class="p">[</span><span class="n">CSSFunctionParameter</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;parameters&quot;</span><span class="p">]],</span>
            <span class="n">children</span><span class="o">=</span><span class="p">[</span><span class="n">CSSFunctionNode</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;children&quot;</span><span class="p">]],</span>
            <span class="n">style_sheet_id</span><span class="o">=</span><span class="p">(</span>
                <span class="n">StyleSheetId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="CSSKeyframeRule">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.CSSKeyframeRule">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">CSSKeyframeRule</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    CSS keyframe rule representation.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: Parent stylesheet&#39;s origin.</span>
    <span class="n">origin</span><span class="p">:</span> <span class="n">StyleSheetOrigin</span>

    <span class="c1">#: Associated key text.</span>
    <span class="n">key_text</span><span class="p">:</span> <span class="n">Value</span>

    <span class="c1">#: Associated style declaration.</span>
    <span class="n">style</span><span class="p">:</span> <span class="n">CSSStyle</span>

    <span class="c1">#: The css style sheet identifier (absent for user agent stylesheet and user-specified</span>
    <span class="c1">#: stylesheet rules) this rule came from.</span>
    <span class="n">style_sheet_id</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">StyleSheetId</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;origin&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">origin</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;keyText&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">key_text</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;style&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">style</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">style_sheet_id</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">style_sheet_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">CSSKeyframeRule</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">origin</span><span class="o">=</span><span class="n">StyleSheetOrigin</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;origin&quot;</span><span class="p">]),</span>
            <span class="n">key_text</span><span class="o">=</span><span class="n">Value</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;keyText&quot;</span><span class="p">]),</span>
            <span class="n">style</span><span class="o">=</span><span class="n">CSSStyle</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;style&quot;</span><span class="p">]),</span>
            <span class="n">style_sheet_id</span><span class="o">=</span><span class="p">(</span>
                <span class="n">StyleSheetId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="StyleDeclarationEdit">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.StyleDeclarationEdit">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">StyleDeclarationEdit</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    A descriptor of operation to mutate style declaration text.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: The css style sheet identifier.</span>
    <span class="n">style_sheet_id</span><span class="p">:</span> <span class="n">StyleSheetId</span>

    <span class="c1">#: The range of the style text in the enclosing stylesheet.</span>
    <span class="n">range_</span><span class="p">:</span> <span class="n">SourceRange</span>

    <span class="c1">#: New style text.</span>
    <span class="n">text</span><span class="p">:</span> <span class="nb">str</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">style_sheet_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;range&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">range_</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;text&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">text</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">StyleDeclarationEdit</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">style_sheet_id</span><span class="o">=</span><span class="n">StyleSheetId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">]),</span>
            <span class="n">range_</span><span class="o">=</span><span class="n">SourceRange</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;range&quot;</span><span class="p">]),</span>
            <span class="n">text</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;text&quot;</span><span class="p">]),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="add_rule">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.add_rule">[docs]</a>
<span class="k">def</span> <span class="nf">add_rule</span><span class="p">(</span>
        <span class="n">style_sheet_id</span><span class="p">:</span> <span class="n">StyleSheetId</span><span class="p">,</span>
        <span class="n">rule_text</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span>
        <span class="n">location</span><span class="p">:</span> <span class="n">SourceRange</span><span class="p">,</span>
        <span class="n">node_for_property_syntax_validation</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">dom</span><span class="o">.</span><span class="n">NodeId</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">CSSRule</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Inserts a new rule with the given ``ruleText`` in a stylesheet with given ``styleSheetId``, at the</span>
<span class="sd">    position specified by ``location``.</span>

<span class="sd">    :param style_sheet_id: The css style sheet identifier where a new rule should be inserted.</span>
<span class="sd">    :param rule_text: The text of a new rule.</span>
<span class="sd">    :param location: Text position of a new rule in the target style sheet.</span>
<span class="sd">    :param node_for_property_syntax_validation: **(EXPERIMENTAL)** *(Optional)* NodeId for the DOM node in whose context custom property declarations for registered properties should be validated. If omitted, declarations in the new rule text can only be validated statically, which may produce incorrect results if the declaration contains a var() for example.</span>
<span class="sd">    :returns: The newly created rule.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">style_sheet_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;ruleText&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">rule_text</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;location&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">location</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="k">if</span> <span class="n">node_for_property_syntax_validation</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;nodeForPropertySyntaxValidation&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">(</span>
            <span class="n">node_for_property_syntax_validation</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="p">)</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;CSS.addRule&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span>
    <span class="k">return</span> <span class="n">CSSRule</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;rule&quot;</span><span class="p">])</span></div>



<div class="viewcode-block" id="collect_class_names">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.collect_class_names">[docs]</a>
<span class="k">def</span> <span class="nf">collect_class_names</span><span class="p">(</span>
        <span class="n">style_sheet_id</span><span class="p">:</span> <span class="n">StyleSheetId</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">]]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Returns all class names from specified stylesheet.</span>

<span class="sd">    :param style_sheet_id:</span>
<span class="sd">    :returns: Class name list.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">style_sheet_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;CSS.collectClassNames&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span>
    <span class="k">return</span> <span class="p">[</span><span class="nb">str</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;classNames&quot;</span><span class="p">]]</span></div>



<div class="viewcode-block" id="create_style_sheet">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.create_style_sheet">[docs]</a>
<span class="k">def</span> <span class="nf">create_style_sheet</span><span class="p">(</span>
        <span class="n">frame_id</span><span class="p">:</span> <span class="n">page</span><span class="o">.</span><span class="n">FrameId</span><span class="p">,</span> <span class="n">force</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">bool</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">StyleSheetId</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Creates a new special &quot;via-inspector&quot; stylesheet in the frame with given ``frameId``.</span>

<span class="sd">    :param frame_id: Identifier of the frame where &quot;via-inspector&quot; stylesheet should be created.</span>
<span class="sd">    :param force: *(Optional)* If true, creates a new stylesheet for every call. If false, returns a stylesheet previously created by a call with force=false for the frame&#39;s document if it exists or creates a new stylesheet (default: false).</span>
<span class="sd">    :returns: Identifier of the created &quot;via-inspector&quot; stylesheet.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;frameId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">frame_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="k">if</span> <span class="n">force</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;force&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">force</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;CSS.createStyleSheet&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span>
    <span class="k">return</span> <span class="n">StyleSheetId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">])</span></div>



<div class="viewcode-block" id="disable">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.disable">[docs]</a>
<span class="k">def</span> <span class="nf">disable</span><span class="p">()</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Disables the CSS agent for the given page.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;CSS.disable&quot;</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span></div>



<div class="viewcode-block" id="enable">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.enable">[docs]</a>
<span class="k">def</span> <span class="nf">enable</span><span class="p">()</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Enables the CSS agent for the given page. Clients should not assume that the CSS agent has been</span>
<span class="sd">    enabled until the result of this command is received.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;CSS.enable&quot;</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span></div>



<div class="viewcode-block" id="force_pseudo_state">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.force_pseudo_state">[docs]</a>
<span class="k">def</span> <span class="nf">force_pseudo_state</span><span class="p">(</span>
        <span class="n">node_id</span><span class="p">:</span> <span class="n">dom</span><span class="o">.</span><span class="n">NodeId</span><span class="p">,</span> <span class="n">forced_pseudo_classes</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Ensures that the given node will have specified pseudo-classes whenever its style is computed by</span>
<span class="sd">    the browser.</span>

<span class="sd">    :param node_id: The element id for which to force the pseudo state.</span>
<span class="sd">    :param forced_pseudo_classes: Element pseudo classes to force when computing the element&#39;s style.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;nodeId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">node_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;forcedPseudoClasses&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">forced_pseudo_classes</span><span class="p">]</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;CSS.forcePseudoState&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span></div>



<div class="viewcode-block" id="force_starting_style">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.force_starting_style">[docs]</a>
<span class="k">def</span> <span class="nf">force_starting_style</span><span class="p">(</span>
        <span class="n">node_id</span><span class="p">:</span> <span class="n">dom</span><span class="o">.</span><span class="n">NodeId</span><span class="p">,</span> <span class="n">forced</span><span class="p">:</span> <span class="nb">bool</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Ensures that the given node is in its starting-style state.</span>

<span class="sd">    :param node_id: The element id for which to force the starting-style state.</span>
<span class="sd">    :param forced: Boolean indicating if this is on or off.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;nodeId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">node_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;forced&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">forced</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;CSS.forceStartingStyle&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span></div>



<div class="viewcode-block" id="get_background_colors">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.get_background_colors">[docs]</a>
<span class="k">def</span> <span class="nf">get_background_colors</span><span class="p">(</span>
        <span class="n">node_id</span><span class="p">:</span> <span class="n">dom</span><span class="o">.</span><span class="n">NodeId</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span>
    <span class="n">T_JSON_DICT</span><span class="p">,</span>
    <span class="n">T_JSON_DICT</span><span class="p">,</span>
    <span class="n">typing</span><span class="o">.</span><span class="n">Tuple</span><span class="p">[</span>
        <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">]],</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">],</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span>
    <span class="p">],</span>
<span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    :param node_id: Id of the node to get background colors for.</span>
<span class="sd">    :returns: A tuple with the following items:</span>

<span class="sd">        0. **backgroundColors** - *(Optional)* The range of background colors behind this element, if it contains any visible text. If no visible text is present, this will be undefined. In the case of a flat background color, this will consist of simply that color. In the case of a gradient, this will consist of each of the color stops. For anything more complicated, this will be an empty array. Images will be ignored (as if the image had failed to load).</span>
<span class="sd">        1. **computedFontSize** - *(Optional)* The computed font size for this node, as a CSS computed value string (e.g. &#39;12px&#39;).</span>
<span class="sd">        2. **computedFontWeight** - *(Optional)* The computed font weight for this node, as a CSS computed value string (e.g. &#39;normal&#39; or &#39;100&#39;).</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;nodeId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">node_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;CSS.getBackgroundColors&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span>
    <span class="k">return</span> <span class="p">(</span>
        <span class="p">(</span>
            <span class="p">[</span><span class="nb">str</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;backgroundColors&quot;</span><span class="p">]]</span>
            <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;backgroundColors&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
            <span class="k">else</span> <span class="kc">None</span>
        <span class="p">),</span>
        <span class="p">(</span>
            <span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;computedFontSize&quot;</span><span class="p">])</span>
            <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;computedFontSize&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
            <span class="k">else</span> <span class="kc">None</span>
        <span class="p">),</span>
        <span class="p">(</span>
            <span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;computedFontWeight&quot;</span><span class="p">])</span>
            <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;computedFontWeight&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
            <span class="k">else</span> <span class="kc">None</span>
        <span class="p">),</span>
    <span class="p">)</span></div>



<div class="viewcode-block" id="get_computed_style_for_node">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.get_computed_style_for_node">[docs]</a>
<span class="k">def</span> <span class="nf">get_computed_style_for_node</span><span class="p">(</span>
        <span class="n">node_id</span><span class="p">:</span> <span class="n">dom</span><span class="o">.</span><span class="n">NodeId</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">CSSComputedStyleProperty</span><span class="p">]]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Returns the computed style for a DOM node identified by ``nodeId``.</span>

<span class="sd">    :param node_id:</span>
<span class="sd">    :returns: Computed style for the specified DOM node.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;nodeId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">node_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;CSS.getComputedStyleForNode&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span>
    <span class="k">return</span> <span class="p">[</span><span class="n">CSSComputedStyleProperty</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;computedStyle&quot;</span><span class="p">]]</span></div>



<div class="viewcode-block" id="resolve_values">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.resolve_values">[docs]</a>
<span class="k">def</span> <span class="nf">resolve_values</span><span class="p">(</span>
        <span class="n">values</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">],</span>
        <span class="n">node_id</span><span class="p">:</span> <span class="n">dom</span><span class="o">.</span><span class="n">NodeId</span><span class="p">,</span>
        <span class="n">property_name</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
        <span class="n">pseudo_type</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">dom</span><span class="o">.</span><span class="n">PseudoType</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
        <span class="n">pseudo_identifier</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">]]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Resolve the specified values in the context of the provided element.</span>
<span class="sd">    For example, a value of &#39;1em&#39; is evaluated according to the computed</span>
<span class="sd">    &#39;font-size&#39; of the element and a value &#39;calc(1px + 2px)&#39; will be</span>
<span class="sd">    resolved to &#39;3px&#39;.</span>

<span class="sd">    :param values: Substitution functions (var()/env()/attr()) and cascade-dependent keywords (revert/revert-layer) do not work.</span>
<span class="sd">    :param node_id: Id of the node in whose context the expression is evaluated</span>
<span class="sd">    :param property_name: *(Optional)* Only longhands and custom property names are accepted.</span>
<span class="sd">    :param pseudo_type: **(EXPERIMENTAL)** *(Optional)* Pseudo element type, only works for pseudo elements that generate elements in the tree, such as ::before and ::after.</span>
<span class="sd">    :param pseudo_identifier: **(EXPERIMENTAL)** *(Optional)* Pseudo element custom ident.</span>
<span class="sd">    :returns:</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;values&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">values</span><span class="p">]</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;nodeId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">node_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="k">if</span> <span class="n">property_name</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;propertyName&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">property_name</span>
    <span class="k">if</span> <span class="n">pseudo_type</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;pseudoType&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">pseudo_type</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="k">if</span> <span class="n">pseudo_identifier</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;pseudoIdentifier&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">pseudo_identifier</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;CSS.resolveValues&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span>
    <span class="k">return</span> <span class="p">[</span><span class="nb">str</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;results&quot;</span><span class="p">]]</span></div>



<div class="viewcode-block" id="get_longhand_properties">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.get_longhand_properties">[docs]</a>
<span class="k">def</span> <span class="nf">get_longhand_properties</span><span class="p">(</span>
        <span class="n">shorthand_name</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">value</span><span class="p">:</span> <span class="nb">str</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">CSSProperty</span><span class="p">]]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>


<span class="sd">    **EXPERIMENTAL**</span>

<span class="sd">    :param shorthand_name:</span>
<span class="sd">    :param value:</span>
<span class="sd">    :returns:</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;shorthandName&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">shorthand_name</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;value&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">value</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;CSS.getLonghandProperties&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span>
    <span class="k">return</span> <span class="p">[</span><span class="n">CSSProperty</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;longhandProperties&quot;</span><span class="p">]]</span></div>



<div class="viewcode-block" id="get_inline_styles_for_node">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.get_inline_styles_for_node">[docs]</a>
<span class="k">def</span> <span class="nf">get_inline_styles_for_node</span><span class="p">(</span>
        <span class="n">node_id</span><span class="p">:</span> <span class="n">dom</span><span class="o">.</span><span class="n">NodeId</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span>
    <span class="n">T_JSON_DICT</span><span class="p">,</span>
    <span class="n">T_JSON_DICT</span><span class="p">,</span>
    <span class="n">typing</span><span class="o">.</span><span class="n">Tuple</span><span class="p">[</span><span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">CSSStyle</span><span class="p">],</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">CSSStyle</span><span class="p">]],</span>
<span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Returns the styles defined inline (explicitly in the &quot;style&quot; attribute and implicitly, using DOM</span>
<span class="sd">    attributes) for a DOM node identified by ``nodeId``.</span>

<span class="sd">    :param node_id:</span>
<span class="sd">    :returns: A tuple with the following items:</span>

<span class="sd">        0. **inlineStyle** - *(Optional)* Inline style for the specified DOM node.</span>
<span class="sd">        1. **attributesStyle** - *(Optional)* Attribute-defined element style (e.g. resulting from &quot;width=20 height=100%&quot;).</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;nodeId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">node_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;CSS.getInlineStylesForNode&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span>
    <span class="k">return</span> <span class="p">(</span>
        <span class="p">(</span>
            <span class="n">CSSStyle</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;inlineStyle&quot;</span><span class="p">])</span>
            <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;inlineStyle&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
            <span class="k">else</span> <span class="kc">None</span>
        <span class="p">),</span>
        <span class="p">(</span>
            <span class="n">CSSStyle</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;attributesStyle&quot;</span><span class="p">])</span>
            <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;attributesStyle&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
            <span class="k">else</span> <span class="kc">None</span>
        <span class="p">),</span>
    <span class="p">)</span></div>



<div class="viewcode-block" id="get_animated_styles_for_node">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.get_animated_styles_for_node">[docs]</a>
<span class="k">def</span> <span class="nf">get_animated_styles_for_node</span><span class="p">(</span>
        <span class="n">node_id</span><span class="p">:</span> <span class="n">dom</span><span class="o">.</span><span class="n">NodeId</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span>
    <span class="n">T_JSON_DICT</span><span class="p">,</span>
    <span class="n">T_JSON_DICT</span><span class="p">,</span>
    <span class="n">typing</span><span class="o">.</span><span class="n">Tuple</span><span class="p">[</span>
        <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">CSSAnimationStyle</span><span class="p">]],</span>
        <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">CSSStyle</span><span class="p">],</span>
        <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">InheritedAnimatedStyleEntry</span><span class="p">]],</span>
    <span class="p">],</span>
<span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Returns the styles coming from animations &amp; transitions</span>
<span class="sd">    including the animation &amp; transition styles coming from inheritance chain.</span>

<span class="sd">    **EXPERIMENTAL**</span>

<span class="sd">    :param node_id:</span>
<span class="sd">    :returns: A tuple with the following items:</span>

<span class="sd">        0. **animationStyles** - *(Optional)* Styles coming from animations.</span>
<span class="sd">        1. **transitionsStyle** - *(Optional)* Style coming from transitions.</span>
<span class="sd">        2. **inherited** - *(Optional)* Inherited style entries for animationsStyle and transitionsStyle from the inheritance chain of the element.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;nodeId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">node_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;CSS.getAnimatedStylesForNode&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span>
    <span class="k">return</span> <span class="p">(</span>
        <span class="p">(</span>
            <span class="p">[</span><span class="n">CSSAnimationStyle</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;animationStyles&quot;</span><span class="p">]]</span>
            <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;animationStyles&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
            <span class="k">else</span> <span class="kc">None</span>
        <span class="p">),</span>
        <span class="p">(</span>
            <span class="n">CSSStyle</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;transitionsStyle&quot;</span><span class="p">])</span>
            <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;transitionsStyle&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
            <span class="k">else</span> <span class="kc">None</span>
        <span class="p">),</span>
        <span class="p">(</span>
            <span class="p">[</span><span class="n">InheritedAnimatedStyleEntry</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;inherited&quot;</span><span class="p">]]</span>
            <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;inherited&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
            <span class="k">else</span> <span class="kc">None</span>
        <span class="p">),</span>
    <span class="p">)</span></div>



<div class="viewcode-block" id="get_matched_styles_for_node">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.get_matched_styles_for_node">[docs]</a>
<span class="k">def</span> <span class="nf">get_matched_styles_for_node</span><span class="p">(</span>
        <span class="n">node_id</span><span class="p">:</span> <span class="n">dom</span><span class="o">.</span><span class="n">NodeId</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span>
    <span class="n">T_JSON_DICT</span><span class="p">,</span>
    <span class="n">T_JSON_DICT</span><span class="p">,</span>
    <span class="n">typing</span><span class="o">.</span><span class="n">Tuple</span><span class="p">[</span>
        <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">CSSStyle</span><span class="p">],</span>
        <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">CSSStyle</span><span class="p">],</span>
        <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">RuleMatch</span><span class="p">]],</span>
        <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">PseudoElementMatches</span><span class="p">]],</span>
        <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">InheritedStyleEntry</span><span class="p">]],</span>
        <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">InheritedPseudoElementMatches</span><span class="p">]],</span>
        <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">CSSKeyframesRule</span><span class="p">]],</span>
        <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">CSSPositionTryRule</span><span class="p">]],</span>
        <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">int</span><span class="p">],</span>
        <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">CSSPropertyRule</span><span class="p">]],</span>
        <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">CSSPropertyRegistration</span><span class="p">]],</span>
        <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">CSSFontPaletteValuesRule</span><span class="p">],</span>
        <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">dom</span><span class="o">.</span><span class="n">NodeId</span><span class="p">],</span>
        <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">CSSFunctionRule</span><span class="p">]],</span>
    <span class="p">],</span>
<span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Returns requested styles for a DOM node identified by ``nodeId``.</span>

<span class="sd">    :param node_id:</span>
<span class="sd">    :returns: A tuple with the following items:</span>

<span class="sd">        0. **inlineStyle** - *(Optional)* Inline style for the specified DOM node.</span>
<span class="sd">        1. **attributesStyle** - *(Optional)* Attribute-defined element style (e.g. resulting from &quot;width=20 height=100%&quot;).</span>
<span class="sd">        2. **matchedCSSRules** - *(Optional)* CSS rules matching this node, from all applicable stylesheets.</span>
<span class="sd">        3. **pseudoElements** - *(Optional)* Pseudo style matches for this node.</span>
<span class="sd">        4. **inherited** - *(Optional)* A chain of inherited styles (from the immediate node parent up to the DOM tree root).</span>
<span class="sd">        5. **inheritedPseudoElements** - *(Optional)* A chain of inherited pseudo element styles (from the immediate node parent up to the DOM tree root).</span>
<span class="sd">        6. **cssKeyframesRules** - *(Optional)* A list of CSS keyframed animations matching this node.</span>
<span class="sd">        7. **cssPositionTryRules** - *(Optional)* A list of CSS @position-try rules matching this node, based on the position-try-fallbacks property.</span>
<span class="sd">        8. **activePositionFallbackIndex** - *(Optional)* Index of the active fallback in the applied position-try-fallback property, will not be set if there is no active position-try fallback.</span>
<span class="sd">        9. **cssPropertyRules** - *(Optional)* A list of CSS at-property rules matching this node.</span>
<span class="sd">        10. **cssPropertyRegistrations** - *(Optional)* A list of CSS property registrations matching this node.</span>
<span class="sd">        11. **cssFontPaletteValuesRule** - *(Optional)* A font-palette-values rule matching this node.</span>
<span class="sd">        12. **parentLayoutNodeId** - *(Optional)* Id of the first parent element that does not have display: contents.</span>
<span class="sd">        13. **cssFunctionRules** - *(Optional)* A list of CSS at-function rules referenced by styles of this node.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;nodeId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">node_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;CSS.getMatchedStylesForNode&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span>
    <span class="k">return</span> <span class="p">(</span>
        <span class="p">(</span>
            <span class="n">CSSStyle</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;inlineStyle&quot;</span><span class="p">])</span>
            <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;inlineStyle&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
            <span class="k">else</span> <span class="kc">None</span>
        <span class="p">),</span>
        <span class="p">(</span>
            <span class="n">CSSStyle</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;attributesStyle&quot;</span><span class="p">])</span>
            <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;attributesStyle&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
            <span class="k">else</span> <span class="kc">None</span>
        <span class="p">),</span>
        <span class="p">(</span>
            <span class="p">[</span><span class="n">RuleMatch</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;matchedCSSRules&quot;</span><span class="p">]]</span>
            <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;matchedCSSRules&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
            <span class="k">else</span> <span class="kc">None</span>
        <span class="p">),</span>
        <span class="p">(</span>
            <span class="p">[</span><span class="n">PseudoElementMatches</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;pseudoElements&quot;</span><span class="p">]]</span>
            <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;pseudoElements&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
            <span class="k">else</span> <span class="kc">None</span>
        <span class="p">),</span>
        <span class="p">(</span>
            <span class="p">[</span><span class="n">InheritedStyleEntry</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;inherited&quot;</span><span class="p">]]</span>
            <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;inherited&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
            <span class="k">else</span> <span class="kc">None</span>
        <span class="p">),</span>
        <span class="p">(</span>
            <span class="p">[</span>
                <span class="n">InheritedPseudoElementMatches</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span>
                <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;inheritedPseudoElements&quot;</span><span class="p">]</span>
            <span class="p">]</span>
            <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;inheritedPseudoElements&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
            <span class="k">else</span> <span class="kc">None</span>
        <span class="p">),</span>
        <span class="p">(</span>
            <span class="p">[</span><span class="n">CSSKeyframesRule</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;cssKeyframesRules&quot;</span><span class="p">]]</span>
            <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;cssKeyframesRules&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
            <span class="k">else</span> <span class="kc">None</span>
        <span class="p">),</span>
        <span class="p">(</span>
            <span class="p">[</span><span class="n">CSSPositionTryRule</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;cssPositionTryRules&quot;</span><span class="p">]]</span>
            <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;cssPositionTryRules&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
            <span class="k">else</span> <span class="kc">None</span>
        <span class="p">),</span>
        <span class="p">(</span>
            <span class="nb">int</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;activePositionFallbackIndex&quot;</span><span class="p">])</span>
            <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;activePositionFallbackIndex&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
            <span class="k">else</span> <span class="kc">None</span>
        <span class="p">),</span>
        <span class="p">(</span>
            <span class="p">[</span><span class="n">CSSPropertyRule</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;cssPropertyRules&quot;</span><span class="p">]]</span>
            <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;cssPropertyRules&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
            <span class="k">else</span> <span class="kc">None</span>
        <span class="p">),</span>
        <span class="p">(</span>
            <span class="p">[</span>
                <span class="n">CSSPropertyRegistration</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span>
                <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;cssPropertyRegistrations&quot;</span><span class="p">]</span>
            <span class="p">]</span>
            <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;cssPropertyRegistrations&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
            <span class="k">else</span> <span class="kc">None</span>
        <span class="p">),</span>
        <span class="p">(</span>
            <span class="n">CSSFontPaletteValuesRule</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;cssFontPaletteValuesRule&quot;</span><span class="p">])</span>
            <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;cssFontPaletteValuesRule&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
            <span class="k">else</span> <span class="kc">None</span>
        <span class="p">),</span>
        <span class="p">(</span>
            <span class="n">dom</span><span class="o">.</span><span class="n">NodeId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;parentLayoutNodeId&quot;</span><span class="p">])</span>
            <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;parentLayoutNodeId&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
            <span class="k">else</span> <span class="kc">None</span>
        <span class="p">),</span>
        <span class="p">(</span>
            <span class="p">[</span><span class="n">CSSFunctionRule</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;cssFunctionRules&quot;</span><span class="p">]]</span>
            <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;cssFunctionRules&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
            <span class="k">else</span> <span class="kc">None</span>
        <span class="p">),</span>
    <span class="p">)</span></div>



<div class="viewcode-block" id="get_media_queries">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.get_media_queries">[docs]</a>
<span class="k">def</span> <span class="nf">get_media_queries</span><span class="p">()</span> <span class="o">-&gt;</span> <span class="p">(</span>
        <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">CSSMedia</span><span class="p">]]</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Returns all media queries parsed by the rendering engine.</span>

<span class="sd">    :returns:</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;CSS.getMediaQueries&quot;</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span>
    <span class="k">return</span> <span class="p">[</span><span class="n">CSSMedia</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;medias&quot;</span><span class="p">]]</span></div>



<div class="viewcode-block" id="get_platform_fonts_for_node">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.get_platform_fonts_for_node">[docs]</a>
<span class="k">def</span> <span class="nf">get_platform_fonts_for_node</span><span class="p">(</span>
        <span class="n">node_id</span><span class="p">:</span> <span class="n">dom</span><span class="o">.</span><span class="n">NodeId</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">PlatformFontUsage</span><span class="p">]]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Requests information about platform fonts which we used to render child TextNodes in the given</span>
<span class="sd">    node.</span>

<span class="sd">    :param node_id:</span>
<span class="sd">    :returns: Usage statistics for every employed platform font.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;nodeId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">node_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;CSS.getPlatformFontsForNode&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span>
    <span class="k">return</span> <span class="p">[</span><span class="n">PlatformFontUsage</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;fonts&quot;</span><span class="p">]]</span></div>



<div class="viewcode-block" id="get_style_sheet_text">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.get_style_sheet_text">[docs]</a>
<span class="k">def</span> <span class="nf">get_style_sheet_text</span><span class="p">(</span>
        <span class="n">style_sheet_id</span><span class="p">:</span> <span class="n">StyleSheetId</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="nb">str</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Returns the current textual content for a stylesheet.</span>

<span class="sd">    :param style_sheet_id:</span>
<span class="sd">    :returns: The stylesheet text.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">style_sheet_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;CSS.getStyleSheetText&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span>
    <span class="k">return</span> <span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;text&quot;</span><span class="p">])</span></div>



<div class="viewcode-block" id="get_layers_for_node">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.get_layers_for_node">[docs]</a>
<span class="k">def</span> <span class="nf">get_layers_for_node</span><span class="p">(</span>
        <span class="n">node_id</span><span class="p">:</span> <span class="n">dom</span><span class="o">.</span><span class="n">NodeId</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">CSSLayerData</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Returns all layers parsed by the rendering engine for the tree scope of a node.</span>
<span class="sd">    Given a DOM element identified by nodeId, getLayersForNode returns the root</span>
<span class="sd">    layer for the nearest ancestor document or shadow root. The layer root contains</span>
<span class="sd">    the full layer tree for the tree scope and their ordering.</span>

<span class="sd">    **EXPERIMENTAL**</span>

<span class="sd">    :param node_id:</span>
<span class="sd">    :returns:</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;nodeId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">node_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;CSS.getLayersForNode&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span>
    <span class="k">return</span> <span class="n">CSSLayerData</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;rootLayer&quot;</span><span class="p">])</span></div>



<div class="viewcode-block" id="get_location_for_selector">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.get_location_for_selector">[docs]</a>
<span class="k">def</span> <span class="nf">get_location_for_selector</span><span class="p">(</span>
        <span class="n">style_sheet_id</span><span class="p">:</span> <span class="n">StyleSheetId</span><span class="p">,</span> <span class="n">selector_text</span><span class="p">:</span> <span class="nb">str</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">SourceRange</span><span class="p">]]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Given a CSS selector text and a style sheet ID, getLocationForSelector</span>
<span class="sd">    returns an array of locations of the CSS selector in the style sheet.</span>

<span class="sd">    **EXPERIMENTAL**</span>

<span class="sd">    :param style_sheet_id:</span>
<span class="sd">    :param selector_text:</span>
<span class="sd">    :returns:</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">style_sheet_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;selectorText&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">selector_text</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;CSS.getLocationForSelector&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span>
    <span class="k">return</span> <span class="p">[</span><span class="n">SourceRange</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;ranges&quot;</span><span class="p">]]</span></div>



<div class="viewcode-block" id="track_computed_style_updates_for_node">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.track_computed_style_updates_for_node">[docs]</a>
<span class="k">def</span> <span class="nf">track_computed_style_updates_for_node</span><span class="p">(</span>
        <span class="n">node_id</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">dom</span><span class="o">.</span><span class="n">NodeId</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Starts tracking the given node for the computed style updates</span>
<span class="sd">    and whenever the computed style is updated for node, it queues</span>
<span class="sd">    a ``computedStyleUpdated`` event with throttling.</span>
<span class="sd">    There can only be 1 node tracked for computed style updates</span>
<span class="sd">    so passing a new node id removes tracking from the previous node.</span>
<span class="sd">    Pass ``undefined`` to disable tracking.</span>

<span class="sd">    **EXPERIMENTAL**</span>

<span class="sd">    :param node_id: *(Optional)*</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="k">if</span> <span class="n">node_id</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;nodeId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">node_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;CSS.trackComputedStyleUpdatesForNode&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span></div>



<div class="viewcode-block" id="track_computed_style_updates">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.track_computed_style_updates">[docs]</a>
<span class="k">def</span> <span class="nf">track_computed_style_updates</span><span class="p">(</span>
        <span class="n">properties_to_track</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">CSSComputedStyleProperty</span><span class="p">],</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Starts tracking the given computed styles for updates. The specified array of properties</span>
<span class="sd">    replaces the one previously specified. Pass empty array to disable tracking.</span>
<span class="sd">    Use takeComputedStyleUpdates to retrieve the list of nodes that had properties modified.</span>
<span class="sd">    The changes to computed style properties are only tracked for nodes pushed to the front-end</span>
<span class="sd">    by the DOM agent. If no changes to the tracked properties occur after the node has been pushed</span>
<span class="sd">    to the front-end, no updates will be issued for the node.</span>

<span class="sd">    **EXPERIMENTAL**</span>

<span class="sd">    :param properties_to_track:</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;propertiesToTrack&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">properties_to_track</span><span class="p">]</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;CSS.trackComputedStyleUpdates&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span></div>



<div class="viewcode-block" id="take_computed_style_updates">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.take_computed_style_updates">[docs]</a>
<span class="k">def</span> <span class="nf">take_computed_style_updates</span><span class="p">()</span> <span class="o">-&gt;</span> <span class="p">(</span>
        <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">dom</span><span class="o">.</span><span class="n">NodeId</span><span class="p">]]</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Polls the next batch of computed style updates.</span>

<span class="sd">    **EXPERIMENTAL**</span>

<span class="sd">    :returns: The list of node Ids that have their tracked computed styles updated.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;CSS.takeComputedStyleUpdates&quot;</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span>
    <span class="k">return</span> <span class="p">[</span><span class="n">dom</span><span class="o">.</span><span class="n">NodeId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;nodeIds&quot;</span><span class="p">]]</span></div>



<div class="viewcode-block" id="set_effective_property_value_for_node">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.set_effective_property_value_for_node">[docs]</a>
<span class="k">def</span> <span class="nf">set_effective_property_value_for_node</span><span class="p">(</span>
        <span class="n">node_id</span><span class="p">:</span> <span class="n">dom</span><span class="o">.</span><span class="n">NodeId</span><span class="p">,</span> <span class="n">property_name</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">value</span><span class="p">:</span> <span class="nb">str</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Find a rule with the given active property for the given node and set the new value for this</span>
<span class="sd">    property</span>

<span class="sd">    :param node_id: The element id for which to set property.</span>
<span class="sd">    :param property_name:</span>
<span class="sd">    :param value:</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;nodeId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">node_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;propertyName&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">property_name</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;value&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">value</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;CSS.setEffectivePropertyValueForNode&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span></div>



<div class="viewcode-block" id="set_property_rule_property_name">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.set_property_rule_property_name">[docs]</a>
<span class="k">def</span> <span class="nf">set_property_rule_property_name</span><span class="p">(</span>
        <span class="n">style_sheet_id</span><span class="p">:</span> <span class="n">StyleSheetId</span><span class="p">,</span> <span class="n">range_</span><span class="p">:</span> <span class="n">SourceRange</span><span class="p">,</span> <span class="n">property_name</span><span class="p">:</span> <span class="nb">str</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">Value</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Modifies the property rule property name.</span>

<span class="sd">    :param style_sheet_id:</span>
<span class="sd">    :param range_:</span>
<span class="sd">    :param property_name:</span>
<span class="sd">    :returns: The resulting key text after modification.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">style_sheet_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;range&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">range_</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;propertyName&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">property_name</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;CSS.setPropertyRulePropertyName&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span>
    <span class="k">return</span> <span class="n">Value</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;propertyName&quot;</span><span class="p">])</span></div>



<div class="viewcode-block" id="set_keyframe_key">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.set_keyframe_key">[docs]</a>
<span class="k">def</span> <span class="nf">set_keyframe_key</span><span class="p">(</span>
        <span class="n">style_sheet_id</span><span class="p">:</span> <span class="n">StyleSheetId</span><span class="p">,</span> <span class="n">range_</span><span class="p">:</span> <span class="n">SourceRange</span><span class="p">,</span> <span class="n">key_text</span><span class="p">:</span> <span class="nb">str</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">Value</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Modifies the keyframe rule key text.</span>

<span class="sd">    :param style_sheet_id:</span>
<span class="sd">    :param range_:</span>
<span class="sd">    :param key_text:</span>
<span class="sd">    :returns: The resulting key text after modification.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">style_sheet_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;range&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">range_</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;keyText&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">key_text</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;CSS.setKeyframeKey&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span>
    <span class="k">return</span> <span class="n">Value</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;keyText&quot;</span><span class="p">])</span></div>



<div class="viewcode-block" id="set_media_text">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.set_media_text">[docs]</a>
<span class="k">def</span> <span class="nf">set_media_text</span><span class="p">(</span>
        <span class="n">style_sheet_id</span><span class="p">:</span> <span class="n">StyleSheetId</span><span class="p">,</span> <span class="n">range_</span><span class="p">:</span> <span class="n">SourceRange</span><span class="p">,</span> <span class="n">text</span><span class="p">:</span> <span class="nb">str</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">CSSMedia</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Modifies the rule selector.</span>

<span class="sd">    :param style_sheet_id:</span>
<span class="sd">    :param range_:</span>
<span class="sd">    :param text:</span>
<span class="sd">    :returns: The resulting CSS media rule after modification.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">style_sheet_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;range&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">range_</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;text&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">text</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;CSS.setMediaText&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span>
    <span class="k">return</span> <span class="n">CSSMedia</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;media&quot;</span><span class="p">])</span></div>



<div class="viewcode-block" id="set_container_query_text">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.set_container_query_text">[docs]</a>
<span class="k">def</span> <span class="nf">set_container_query_text</span><span class="p">(</span>
        <span class="n">style_sheet_id</span><span class="p">:</span> <span class="n">StyleSheetId</span><span class="p">,</span> <span class="n">range_</span><span class="p">:</span> <span class="n">SourceRange</span><span class="p">,</span> <span class="n">text</span><span class="p">:</span> <span class="nb">str</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">CSSContainerQuery</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Modifies the expression of a container query.</span>

<span class="sd">    **EXPERIMENTAL**</span>

<span class="sd">    :param style_sheet_id:</span>
<span class="sd">    :param range_:</span>
<span class="sd">    :param text:</span>
<span class="sd">    :returns: The resulting CSS container query rule after modification.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">style_sheet_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;range&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">range_</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;text&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">text</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;CSS.setContainerQueryText&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span>
    <span class="k">return</span> <span class="n">CSSContainerQuery</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;containerQuery&quot;</span><span class="p">])</span></div>



<div class="viewcode-block" id="set_supports_text">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.set_supports_text">[docs]</a>
<span class="k">def</span> <span class="nf">set_supports_text</span><span class="p">(</span>
        <span class="n">style_sheet_id</span><span class="p">:</span> <span class="n">StyleSheetId</span><span class="p">,</span> <span class="n">range_</span><span class="p">:</span> <span class="n">SourceRange</span><span class="p">,</span> <span class="n">text</span><span class="p">:</span> <span class="nb">str</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">CSSSupports</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Modifies the expression of a supports at-rule.</span>

<span class="sd">    **EXPERIMENTAL**</span>

<span class="sd">    :param style_sheet_id:</span>
<span class="sd">    :param range_:</span>
<span class="sd">    :param text:</span>
<span class="sd">    :returns: The resulting CSS Supports rule after modification.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">style_sheet_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;range&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">range_</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;text&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">text</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;CSS.setSupportsText&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span>
    <span class="k">return</span> <span class="n">CSSSupports</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;supports&quot;</span><span class="p">])</span></div>



<div class="viewcode-block" id="set_scope_text">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.set_scope_text">[docs]</a>
<span class="k">def</span> <span class="nf">set_scope_text</span><span class="p">(</span>
        <span class="n">style_sheet_id</span><span class="p">:</span> <span class="n">StyleSheetId</span><span class="p">,</span> <span class="n">range_</span><span class="p">:</span> <span class="n">SourceRange</span><span class="p">,</span> <span class="n">text</span><span class="p">:</span> <span class="nb">str</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">CSSScope</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Modifies the expression of a scope at-rule.</span>

<span class="sd">    **EXPERIMENTAL**</span>

<span class="sd">    :param style_sheet_id:</span>
<span class="sd">    :param range_:</span>
<span class="sd">    :param text:</span>
<span class="sd">    :returns: The resulting CSS Scope rule after modification.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">style_sheet_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;range&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">range_</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;text&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">text</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;CSS.setScopeText&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span>
    <span class="k">return</span> <span class="n">CSSScope</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;scope&quot;</span><span class="p">])</span></div>



<div class="viewcode-block" id="set_rule_selector">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.set_rule_selector">[docs]</a>
<span class="k">def</span> <span class="nf">set_rule_selector</span><span class="p">(</span>
        <span class="n">style_sheet_id</span><span class="p">:</span> <span class="n">StyleSheetId</span><span class="p">,</span> <span class="n">range_</span><span class="p">:</span> <span class="n">SourceRange</span><span class="p">,</span> <span class="n">selector</span><span class="p">:</span> <span class="nb">str</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">SelectorList</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Modifies the rule selector.</span>

<span class="sd">    :param style_sheet_id:</span>
<span class="sd">    :param range_:</span>
<span class="sd">    :param selector:</span>
<span class="sd">    :returns: The resulting selector list after modification.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">style_sheet_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;range&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">range_</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;selector&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">selector</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;CSS.setRuleSelector&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span>
    <span class="k">return</span> <span class="n">SelectorList</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;selectorList&quot;</span><span class="p">])</span></div>



<div class="viewcode-block" id="set_style_sheet_text">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.set_style_sheet_text">[docs]</a>
<span class="k">def</span> <span class="nf">set_style_sheet_text</span><span class="p">(</span>
        <span class="n">style_sheet_id</span><span class="p">:</span> <span class="n">StyleSheetId</span><span class="p">,</span> <span class="n">text</span><span class="p">:</span> <span class="nb">str</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Sets the new stylesheet text.</span>

<span class="sd">    :param style_sheet_id:</span>
<span class="sd">    :param text:</span>
<span class="sd">    :returns: *(Optional)* URL of source map associated with script (if any).</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">style_sheet_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;text&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">text</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;CSS.setStyleSheetText&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span>
    <span class="k">return</span> <span class="p">(</span>
        <span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;sourceMapURL&quot;</span><span class="p">])</span>
        <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;sourceMapURL&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
        <span class="k">else</span> <span class="kc">None</span>
    <span class="p">)</span></div>



<div class="viewcode-block" id="set_style_texts">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.set_style_texts">[docs]</a>
<span class="k">def</span> <span class="nf">set_style_texts</span><span class="p">(</span>
        <span class="n">edits</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">StyleDeclarationEdit</span><span class="p">],</span>
        <span class="n">node_for_property_syntax_validation</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">dom</span><span class="o">.</span><span class="n">NodeId</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">CSSStyle</span><span class="p">]]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Applies specified style edits one after another in the given order.</span>

<span class="sd">    :param edits:</span>
<span class="sd">    :param node_for_property_syntax_validation: **(EXPERIMENTAL)** *(Optional)* NodeId for the DOM node in whose context custom property declarations for registered properties should be validated. If omitted, declarations in the new rule text can only be validated statically, which may produce incorrect results if the declaration contains a var() for example.</span>
<span class="sd">    :returns: The resulting styles after modification.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;edits&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">edits</span><span class="p">]</span>
    <span class="k">if</span> <span class="n">node_for_property_syntax_validation</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;nodeForPropertySyntaxValidation&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">(</span>
            <span class="n">node_for_property_syntax_validation</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="p">)</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;CSS.setStyleTexts&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span>
    <span class="k">return</span> <span class="p">[</span><span class="n">CSSStyle</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;styles&quot;</span><span class="p">]]</span></div>



<div class="viewcode-block" id="start_rule_usage_tracking">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.start_rule_usage_tracking">[docs]</a>
<span class="k">def</span> <span class="nf">start_rule_usage_tracking</span><span class="p">()</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Enables the selector recording.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;CSS.startRuleUsageTracking&quot;</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span></div>



<div class="viewcode-block" id="stop_rule_usage_tracking">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.stop_rule_usage_tracking">[docs]</a>
<span class="k">def</span> <span class="nf">stop_rule_usage_tracking</span><span class="p">()</span> <span class="o">-&gt;</span> <span class="p">(</span>
        <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">RuleUsage</span><span class="p">]]</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Stop tracking rule usage and return the list of rules that were used since last call to</span>
<span class="sd">    ``takeCoverageDelta`` (or since start of coverage instrumentation).</span>

<span class="sd">    :returns:</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;CSS.stopRuleUsageTracking&quot;</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span>
    <span class="k">return</span> <span class="p">[</span><span class="n">RuleUsage</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;ruleUsage&quot;</span><span class="p">]]</span></div>



<div class="viewcode-block" id="take_coverage_delta">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.take_coverage_delta">[docs]</a>
<span class="k">def</span> <span class="nf">take_coverage_delta</span><span class="p">()</span> <span class="o">-&gt;</span> <span class="p">(</span>
        <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span>
            <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">typing</span><span class="o">.</span><span class="n">Tuple</span><span class="p">[</span><span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">RuleUsage</span><span class="p">],</span> <span class="nb">float</span><span class="p">]</span>
        <span class="p">]</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Obtain list of rules that became used since last call to this method (or since start of coverage</span>
<span class="sd">    instrumentation).</span>

<span class="sd">    :returns: A tuple with the following items:</span>

<span class="sd">        0. **coverage** -</span>
<span class="sd">        1. **timestamp** - Monotonically increasing time, in seconds.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;CSS.takeCoverageDelta&quot;</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span>
    <span class="k">return</span> <span class="p">(</span>
        <span class="p">[</span><span class="n">RuleUsage</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;coverage&quot;</span><span class="p">]],</span>
        <span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;timestamp&quot;</span><span class="p">]),</span>
    <span class="p">)</span></div>



<div class="viewcode-block" id="set_local_fonts_enabled">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.set_local_fonts_enabled">[docs]</a>
<span class="k">def</span> <span class="nf">set_local_fonts_enabled</span><span class="p">(</span>
        <span class="n">enabled</span><span class="p">:</span> <span class="nb">bool</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Enables/disables rendering of local CSS fonts (enabled by default).</span>

<span class="sd">    **EXPERIMENTAL**</span>

<span class="sd">    :param enabled: Whether rendering of local fonts is enabled.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;enabled&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">enabled</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;CSS.setLocalFontsEnabled&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span></div>



<div class="viewcode-block" id="FontsUpdated">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.FontsUpdated">[docs]</a>
<span class="nd">@event_class</span><span class="p">(</span><span class="s2">&quot;CSS.fontsUpdated&quot;</span><span class="p">)</span>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">FontsUpdated</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Fires whenever a web font is updated.  A non-empty font parameter indicates a successfully loaded</span>
<span class="sd">    web font.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: The web font that has loaded.</span>
    <span class="n">font</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">FontFace</span><span class="p">]</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">FontsUpdated</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">font</span><span class="o">=</span><span class="p">(</span>
                <span class="n">FontFace</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;font&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;font&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">)</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="MediaQueryResultChanged">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.MediaQueryResultChanged">[docs]</a>
<span class="nd">@event_class</span><span class="p">(</span><span class="s2">&quot;CSS.mediaQueryResultChanged&quot;</span><span class="p">)</span>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">MediaQueryResultChanged</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Fires whenever a MediaQuery result changes (for example, after a browser window has been</span>
<span class="sd">    resized.) The current implementation considers only viewport-dependent media features.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">MediaQueryResultChanged</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">()</span></div>



<div class="viewcode-block" id="StyleSheetAdded">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.StyleSheetAdded">[docs]</a>
<span class="nd">@event_class</span><span class="p">(</span><span class="s2">&quot;CSS.styleSheetAdded&quot;</span><span class="p">)</span>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">StyleSheetAdded</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Fired whenever an active document stylesheet is added.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: Added stylesheet metainfo.</span>
    <span class="n">header</span><span class="p">:</span> <span class="n">CSSStyleSheetHeader</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">StyleSheetAdded</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">header</span><span class="o">=</span><span class="n">CSSStyleSheetHeader</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;header&quot;</span><span class="p">]))</span></div>



<div class="viewcode-block" id="StyleSheetChanged">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.StyleSheetChanged">[docs]</a>
<span class="nd">@event_class</span><span class="p">(</span><span class="s2">&quot;CSS.styleSheetChanged&quot;</span><span class="p">)</span>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">StyleSheetChanged</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Fired whenever a stylesheet is changed as a result of the client operation.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">style_sheet_id</span><span class="p">:</span> <span class="n">StyleSheetId</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">StyleSheetChanged</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">style_sheet_id</span><span class="o">=</span><span class="n">StyleSheetId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">]))</span></div>



<div class="viewcode-block" id="StyleSheetRemoved">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.StyleSheetRemoved">[docs]</a>
<span class="nd">@event_class</span><span class="p">(</span><span class="s2">&quot;CSS.styleSheetRemoved&quot;</span><span class="p">)</span>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">StyleSheetRemoved</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Fired whenever an active document stylesheet is removed.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: Identifier of the removed stylesheet.</span>
    <span class="n">style_sheet_id</span><span class="p">:</span> <span class="n">StyleSheetId</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">StyleSheetRemoved</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">style_sheet_id</span><span class="o">=</span><span class="n">StyleSheetId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;styleSheetId&quot;</span><span class="p">]))</span></div>



<div class="viewcode-block" id="ComputedStyleUpdated">
<a class="viewcode-back" href="../../../nodriver/cdp/css.html#nodriver.cdp.css.ComputedStyleUpdated">[docs]</a>
<span class="nd">@event_class</span><span class="p">(</span><span class="s2">&quot;CSS.computedStyleUpdated&quot;</span><span class="p">)</span>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">ComputedStyleUpdated</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    **EXPERIMENTAL**</span>


<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: The node id that has updated computed styles.</span>
    <span class="n">node_id</span><span class="p">:</span> <span class="n">dom</span><span class="o">.</span><span class="n">NodeId</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">ComputedStyleUpdated</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">node_id</span><span class="o">=</span><span class="n">dom</span><span class="o">.</span><span class="n">NodeId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;nodeId&quot;</span><span class="p">]))</span></div>

</pre></div>
        </article>
      </div>
      <footer>
        
        <div class="related-pages">
          
          
        </div>
        <div class="bottom-of-page">
          <div class="left-details">
            <div class="copyright">
                Copyright &#169; 2023, Author
            </div>
            Made with <a href="https://www.sphinx-doc.org/">Sphinx</a> and <a class="muted-link" href="https://pradyunsg.me">@pradyunsg</a>'s
            
            <a href="https://github.com/pradyunsg/furo">Furo</a>
            
          </div>
          <div class="right-details">
            
          </div>
        </div>
        
      </footer>
    </div>
    <aside class="toc-drawer no-toc">
      
      
      
    </aside>
  </div>
</div><script src="../../../_static/documentation_options.js?v=5929fcd5"></script>
    <script src="../../../_static/doctools.js?v=888ff710"></script>
    <script src="../../../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../../../_static/scripts/furo.js?v=32e29ea5"></script>
    </body>
</html>