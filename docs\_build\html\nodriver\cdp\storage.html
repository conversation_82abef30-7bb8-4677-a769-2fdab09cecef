<!doctype html>
<html class="no-js" lang="en" data-content_root="../../">
  <head><meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width,initial-scale=1"/>
    <meta name="color-scheme" content="light dark"><meta name="viewport" content="width=device-width, initial-scale=1" />
<link rel="index" title="Index" href="../../genindex.html" /><link rel="search" title="Search" href="../../search.html" /><link rel="next" title="SystemInfo" href="system_info.html" /><link rel="prev" title="ServiceWorker" href="service_worker.html" />

    <!-- Generated with Sphinx 7.2.6 and Furo 2023.09.10 -->
        <title>Storage - nodriver documentation</title>
      <link rel="stylesheet" type="text/css" href="../../_static/pygments.css?v=fa44fd50" />
    <link rel="stylesheet" type="text/css" href="../../_static/styles/furo.css?v=135e06be" />
    <link rel="stylesheet" type="text/css" href="../../_static/styles/furo-extensions.css?v=36a5483c" />
    <link rel="stylesheet" type="text/css" href="../../_static/./custom.css?v=3934642d" />
    
    


<style>
  body {
    --color-code-background: #eeffcc;
  --color-code-foreground: black;
  
  }
  @media not print {
    body[data-theme="dark"] {
      --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8eefba;
  --color-background-primary: #111112;
  --color-problematic: #e7aeae;
  
    }
    @media (prefers-color-scheme: dark) {
      body:not([data-theme="light"]) {
        --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8eefba;
  --color-background-primary: #111112;
  --color-problematic: #e7aeae;
  
      }
    }
  }
</style></head>
  <body>
    
    <script>
      document.body.dataset.theme = localStorage.getItem("theme") || "auto";
    </script>
    

<svg xmlns="http://www.w3.org/2000/svg" style="display: none;">
  <symbol id="svg-toc" viewBox="0 0 24 24">
    <title>Contents</title>
    <svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 1024 1024">
      <path d="M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM115.4 518.9L271.7 642c5.8 4.6 14.4.5 14.4-6.9V388.9c0-7.4-8.5-11.5-14.4-6.9L115.4 505.1a8.74 8.74 0 0 0 0 13.8z"/>
    </svg>
  </symbol>
  <symbol id="svg-menu" viewBox="0 0 24 24">
    <title>Menu</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-menu">
      <line x1="3" y1="12" x2="21" y2="12"></line>
      <line x1="3" y1="6" x2="21" y2="6"></line>
      <line x1="3" y1="18" x2="21" y2="18"></line>
    </svg>
  </symbol>
  <symbol id="svg-arrow-right" viewBox="0 0 24 24">
    <title>Expand</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-chevron-right">
      <polyline points="9 18 15 12 9 6"></polyline>
    </svg>
  </symbol>
  <symbol id="svg-sun" viewBox="0 0 24 24">
    <title>Light mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="feather-sun">
      <circle cx="12" cy="12" r="5"></circle>
      <line x1="12" y1="1" x2="12" y2="3"></line>
      <line x1="12" y1="21" x2="12" y2="23"></line>
      <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
      <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
      <line x1="1" y1="12" x2="3" y2="12"></line>
      <line x1="21" y1="12" x2="23" y2="12"></line>
      <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
      <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
    </svg>
  </symbol>
  <symbol id="svg-moon" viewBox="0 0 24 24">
    <title>Dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-moon">
      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
      <path d="M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z" />
    </svg>
  </symbol>
  <symbol id="svg-sun-half" viewBox="0 0 24 24">
    <title>Auto light/dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-shadow">
      <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
      <circle cx="12" cy="12" r="9" />
      <path d="M13 12h5" />
      <path d="M13 15h4" />
      <path d="M13 18h1" />
      <path d="M13 9h4" />
      <path d="M13 6h1" />
    </svg>
  </symbol>
</svg>

<input type="checkbox" class="sidebar-toggle" name="__navigation" id="__navigation">
<input type="checkbox" class="sidebar-toggle" name="__toc" id="__toc">
<label class="overlay sidebar-overlay" for="__navigation">
  <div class="visually-hidden">Hide navigation sidebar</div>
</label>
<label class="overlay toc-overlay" for="__toc">
  <div class="visually-hidden">Hide table of contents sidebar</div>
</label>



<div class="page">
  <header class="mobile-header">
    <div class="header-left">
      <label class="nav-overlay-icon" for="__navigation">
        <div class="visually-hidden">Toggle site navigation sidebar</div>
        <i class="icon"><svg><use href="#svg-menu"></use></svg></i>
      </label>
    </div>
    <div class="header-center">
      <a href="../../index.html"><div class="brand">nodriver  documentation</div></a>
    </div>
    <div class="header-right">
      <div class="theme-toggle-container theme-toggle-header">
        <button class="theme-toggle">
          <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
          <svg class="theme-icon-when-auto"><use href="#svg-sun-half"></use></svg>
          <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
          <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
        </button>
      </div>
      <label class="toc-overlay-icon toc-header-icon" for="__toc">
        <div class="visually-hidden">Toggle table of contents sidebar</div>
        <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
      </label>
    </div>
  </header>
  <aside class="sidebar-drawer">
    <div class="sidebar-container">
      
      <div class="sidebar-sticky"><div class="sidebar-scroll"><a class="sidebar-brand" href="../../index.html">
  
  
  <span class="sidebar-brand-text">nodriver  documentation</span>
  
</a><form class="sidebar-search-container" method="get" action="../../search.html" role="search">
  <input class="sidebar-search" placeholder="Search" name="q" aria-label="Search">
  <input type="hidden" name="check_keywords" value="yes">
  <input type="hidden" name="area" value="default">
</form>
<div id="searchbox"></div><div class="sidebar-tree">
  <ul>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html">Quickstart guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#usage-example">usage example</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#more-complete-example">More complete example</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#custom-starting-options">Custom starting options</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#alternative-custom-options">Alternative custom options</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#proxies-socks5-authenticated-too">Proxies (socks5, authenticated too)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#concrete-example">Concrete example</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../classes/browser.html">Browser class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../classes/tab.html">Tab class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../classes/element.html">Element class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../classes/others_and_helpers.html">Other classes and Helper classes</a></li>
</ul>
<ul class="current">
<li class="toctree-l1 current has-children"><a class="reference internal" href="../cdp.html">CDP object</a><input checked="" class="toctree-checkbox" id="toctree-checkbox-1" name="toctree-checkbox-1" role="switch" type="checkbox"/><label for="toctree-checkbox-1"><div class="visually-hidden">Toggle navigation of CDP object</div><i class="icon"><svg><use href="#svg-arrow-right"></use></svg></i></label><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="accessibility.html">Accessibility</a></li>
<li class="toctree-l2"><a class="reference internal" href="animation.html">Animation</a></li>
<li class="toctree-l2"><a class="reference internal" href="audits.html">Audits</a></li>
<li class="toctree-l2"><a class="reference internal" href="autofill.html">Autofill</a></li>
<li class="toctree-l2"><a class="reference internal" href="background_service.html">BackgroundService</a></li>
<li class="toctree-l2"><a class="reference internal" href="bluetooth_emulation.html">BluetoothEmulation</a></li>
<li class="toctree-l2"><a class="reference internal" href="browser.html">Browser</a></li>
<li class="toctree-l2"><a class="reference internal" href="cache_storage.html">CacheStorage</a></li>
<li class="toctree-l2"><a class="reference internal" href="cast.html">Cast</a></li>
<li class="toctree-l2"><a class="reference internal" href="console.html">Console</a></li>
<li class="toctree-l2"><a class="reference internal" href="css.html">CSS</a></li>
<li class="toctree-l2"><a class="reference internal" href="debugger.html">Debugger</a></li>
<li class="toctree-l2"><a class="reference internal" href="device_access.html">DeviceAccess</a></li>
<li class="toctree-l2"><a class="reference internal" href="device_orientation.html">DeviceOrientation</a></li>
<li class="toctree-l2"><a class="reference internal" href="dom.html">DOM</a></li>
<li class="toctree-l2"><a class="reference internal" href="dom_debugger.html">DOMDebugger</a></li>
<li class="toctree-l2"><a class="reference internal" href="dom_snapshot.html">DOMSnapshot</a></li>
<li class="toctree-l2"><a class="reference internal" href="dom_storage.html">DOMStorage</a></li>
<li class="toctree-l2"><a class="reference internal" href="emulation.html">Emulation</a></li>
<li class="toctree-l2"><a class="reference internal" href="event_breakpoints.html">EventBreakpoints</a></li>
<li class="toctree-l2"><a class="reference internal" href="extensions.html">Extensions</a></li>
<li class="toctree-l2"><a class="reference internal" href="fed_cm.html">FedCm</a></li>
<li class="toctree-l2"><a class="reference internal" href="fetch.html">Fetch</a></li>
<li class="toctree-l2"><a class="reference internal" href="file_system.html">FileSystem</a></li>
<li class="toctree-l2"><a class="reference internal" href="headless_experimental.html">HeadlessExperimental</a></li>
<li class="toctree-l2"><a class="reference internal" href="heap_profiler.html">HeapProfiler</a></li>
<li class="toctree-l2"><a class="reference internal" href="indexed_db.html">IndexedDB</a></li>
<li class="toctree-l2"><a class="reference internal" href="input_.html">Input</a></li>
<li class="toctree-l2"><a class="reference internal" href="inspector.html">Inspector</a></li>
<li class="toctree-l2"><a class="reference internal" href="io.html">IO</a></li>
<li class="toctree-l2"><a class="reference internal" href="layer_tree.html">LayerTree</a></li>
<li class="toctree-l2"><a class="reference internal" href="log.html">Log</a></li>
<li class="toctree-l2"><a class="reference internal" href="media.html">Media</a></li>
<li class="toctree-l2"><a class="reference internal" href="memory.html">Memory</a></li>
<li class="toctree-l2"><a class="reference internal" href="network.html">Network</a></li>
<li class="toctree-l2"><a class="reference internal" href="overlay.html">Overlay</a></li>
<li class="toctree-l2"><a class="reference internal" href="page.html">Page</a></li>
<li class="toctree-l2"><a class="reference internal" href="performance.html">Performance</a></li>
<li class="toctree-l2"><a class="reference internal" href="performance_timeline.html">PerformanceTimeline</a></li>
<li class="toctree-l2"><a class="reference internal" href="preload.html">Preload</a></li>
<li class="toctree-l2"><a class="reference internal" href="profiler.html">Profiler</a></li>
<li class="toctree-l2"><a class="reference internal" href="pwa.html">PWA</a></li>
<li class="toctree-l2"><a class="reference internal" href="runtime.html">Runtime</a></li>
<li class="toctree-l2"><a class="reference internal" href="schema.html">Schema</a></li>
<li class="toctree-l2"><a class="reference internal" href="security.html">Security</a></li>
<li class="toctree-l2"><a class="reference internal" href="service_worker.html">ServiceWorker</a></li>
<li class="toctree-l2 current current-page"><a class="current reference internal" href="#">Storage</a></li>
<li class="toctree-l2"><a class="reference internal" href="system_info.html">SystemInfo</a></li>
<li class="toctree-l2"><a class="reference internal" href="target.html">Target</a></li>
<li class="toctree-l2"><a class="reference internal" href="tethering.html">Tethering</a></li>
<li class="toctree-l2"><a class="reference internal" href="tracing.html">Tracing</a></li>
<li class="toctree-l2"><a class="reference internal" href="web_audio.html">WebAudio</a></li>
<li class="toctree-l2"><a class="reference internal" href="web_authn.html">WebAuthn</a></li>
</ul>
</li>
</ul>

</div>
</div>
      </div>
      
    </div>
  </aside>
  <div class="main">
    <div class="content">
      <div class="article-container">
        <a href="#" class="back-to-top muted-link">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
            <path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8v12z"></path>
          </svg>
          <span>Back to top</span>
        </a>
        <div class="content-icon-container">
          
<div class="theme-toggle-container theme-toggle-content">
            <button class="theme-toggle">
              <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
              <svg class="theme-icon-when-auto"><use href="#svg-sun-half"></use></svg>
              <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
              <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
            </button>
          </div>
          <label class="toc-overlay-icon toc-content-icon" for="__toc">
            <div class="visually-hidden">Toggle table of contents sidebar</div>
            <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
          </label>
        </div>
        <article role="main">
          <section id="storage">
<h1>Storage<a class="headerlink" href="#storage" title="Link to this heading">#</a></h1>
<p><em>This CDP domain is experimental.</em></p>
<ul class="simple" id="module-nodriver.cdp.storage">
<li><p><a class="reference internal" href="#types">Types</a></p></li>
<li><p><a class="reference internal" href="#commands">Commands</a></p></li>
<li><p><a class="reference internal" href="#events">Events</a></p></li>
</ul>
<section id="types">
<h2>Types<a class="headerlink" href="#types" title="Link to this heading">#</a></h2>
<p>Generally, you do not need to instantiate CDP types
yourself. Instead, the API creates objects for you as return
values from commands, and then you can use those objects as
arguments to other commands.</p>
<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.storage.SerializedStorageKey">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">SerializedStorageKey</span></span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#SerializedStorageKey"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.SerializedStorageKey" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.storage.StorageType">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">StorageType</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">names</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">module</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">qualname</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">boundary</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#StorageType"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.StorageType" title="Link to this definition">#</a></dt>
<dd><p>Enum of possible storage types.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.StorageType.COOKIES">
<span class="sig-name descname"><span class="pre">COOKIES</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'cookies'</span></em><a class="headerlink" href="#nodriver.cdp.storage.StorageType.COOKIES" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.StorageType.FILE_SYSTEMS">
<span class="sig-name descname"><span class="pre">FILE_SYSTEMS</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'file_systems'</span></em><a class="headerlink" href="#nodriver.cdp.storage.StorageType.FILE_SYSTEMS" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.StorageType.INDEXEDDB">
<span class="sig-name descname"><span class="pre">INDEXEDDB</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'indexeddb'</span></em><a class="headerlink" href="#nodriver.cdp.storage.StorageType.INDEXEDDB" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.StorageType.LOCAL_STORAGE">
<span class="sig-name descname"><span class="pre">LOCAL_STORAGE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'local_storage'</span></em><a class="headerlink" href="#nodriver.cdp.storage.StorageType.LOCAL_STORAGE" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.StorageType.SHADER_CACHE">
<span class="sig-name descname"><span class="pre">SHADER_CACHE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'shader_cache'</span></em><a class="headerlink" href="#nodriver.cdp.storage.StorageType.SHADER_CACHE" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.StorageType.WEBSQL">
<span class="sig-name descname"><span class="pre">WEBSQL</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'websql'</span></em><a class="headerlink" href="#nodriver.cdp.storage.StorageType.WEBSQL" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.StorageType.SERVICE_WORKERS">
<span class="sig-name descname"><span class="pre">SERVICE_WORKERS</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'service_workers'</span></em><a class="headerlink" href="#nodriver.cdp.storage.StorageType.SERVICE_WORKERS" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.StorageType.CACHE_STORAGE">
<span class="sig-name descname"><span class="pre">CACHE_STORAGE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'cache_storage'</span></em><a class="headerlink" href="#nodriver.cdp.storage.StorageType.CACHE_STORAGE" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.StorageType.INTEREST_GROUPS">
<span class="sig-name descname"><span class="pre">INTEREST_GROUPS</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'interest_groups'</span></em><a class="headerlink" href="#nodriver.cdp.storage.StorageType.INTEREST_GROUPS" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.StorageType.SHARED_STORAGE">
<span class="sig-name descname"><span class="pre">SHARED_STORAGE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'shared_storage'</span></em><a class="headerlink" href="#nodriver.cdp.storage.StorageType.SHARED_STORAGE" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.StorageType.STORAGE_BUCKETS">
<span class="sig-name descname"><span class="pre">STORAGE_BUCKETS</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'storage_buckets'</span></em><a class="headerlink" href="#nodriver.cdp.storage.StorageType.STORAGE_BUCKETS" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.StorageType.ALL_">
<span class="sig-name descname"><span class="pre">ALL_</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'all'</span></em><a class="headerlink" href="#nodriver.cdp.storage.StorageType.ALL_" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.StorageType.OTHER">
<span class="sig-name descname"><span class="pre">OTHER</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'other'</span></em><a class="headerlink" href="#nodriver.cdp.storage.StorageType.OTHER" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.storage.UsageForType">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">UsageForType</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">storage_type</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">usage</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#UsageForType"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.UsageForType" title="Link to this definition">#</a></dt>
<dd><p>Usage for a storage type.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.UsageForType.storage_type">
<span class="sig-name descname"><span class="pre">storage_type</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.storage.StorageType" title="nodriver.cdp.storage.StorageType"><code class="xref py py-class docutils literal notranslate"><span class="pre">StorageType</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.UsageForType.storage_type" title="Link to this definition">#</a></dt>
<dd><p>Name of storage type.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.UsageForType.usage">
<span class="sig-name descname"><span class="pre">usage</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.UsageForType.usage" title="Link to this definition">#</a></dt>
<dd><p>Storage usage (bytes).</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.storage.TrustTokens">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">TrustTokens</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">issuer_origin</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">count</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#TrustTokens"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.TrustTokens" title="Link to this definition">#</a></dt>
<dd><p>Pair of issuer origin and number of available (signed, but not used) Trust
Tokens from that issuer.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.TrustTokens.issuer_origin">
<span class="sig-name descname"><span class="pre">issuer_origin</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.TrustTokens.issuer_origin" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.TrustTokens.count">
<span class="sig-name descname"><span class="pre">count</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.TrustTokens.count" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.storage.InterestGroupAuctionId">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">InterestGroupAuctionId</span></span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#InterestGroupAuctionId"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.InterestGroupAuctionId" title="Link to this definition">#</a></dt>
<dd><p>Protected audience interest group auction identifier.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.storage.InterestGroupAccessType">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">InterestGroupAccessType</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">names</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">module</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">qualname</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">boundary</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#InterestGroupAccessType"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.InterestGroupAccessType" title="Link to this definition">#</a></dt>
<dd><p>Enum of interest group access types.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.InterestGroupAccessType.JOIN">
<span class="sig-name descname"><span class="pre">JOIN</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'join'</span></em><a class="headerlink" href="#nodriver.cdp.storage.InterestGroupAccessType.JOIN" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.InterestGroupAccessType.LEAVE">
<span class="sig-name descname"><span class="pre">LEAVE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'leave'</span></em><a class="headerlink" href="#nodriver.cdp.storage.InterestGroupAccessType.LEAVE" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.InterestGroupAccessType.UPDATE">
<span class="sig-name descname"><span class="pre">UPDATE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'update'</span></em><a class="headerlink" href="#nodriver.cdp.storage.InterestGroupAccessType.UPDATE" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.InterestGroupAccessType.LOADED">
<span class="sig-name descname"><span class="pre">LOADED</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'loaded'</span></em><a class="headerlink" href="#nodriver.cdp.storage.InterestGroupAccessType.LOADED" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.InterestGroupAccessType.BID">
<span class="sig-name descname"><span class="pre">BID</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'bid'</span></em><a class="headerlink" href="#nodriver.cdp.storage.InterestGroupAccessType.BID" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.InterestGroupAccessType.WIN">
<span class="sig-name descname"><span class="pre">WIN</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'win'</span></em><a class="headerlink" href="#nodriver.cdp.storage.InterestGroupAccessType.WIN" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.InterestGroupAccessType.ADDITIONAL_BID">
<span class="sig-name descname"><span class="pre">ADDITIONAL_BID</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'additionalBid'</span></em><a class="headerlink" href="#nodriver.cdp.storage.InterestGroupAccessType.ADDITIONAL_BID" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.InterestGroupAccessType.ADDITIONAL_BID_WIN">
<span class="sig-name descname"><span class="pre">ADDITIONAL_BID_WIN</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'additionalBidWin'</span></em><a class="headerlink" href="#nodriver.cdp.storage.InterestGroupAccessType.ADDITIONAL_BID_WIN" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.InterestGroupAccessType.TOP_LEVEL_BID">
<span class="sig-name descname"><span class="pre">TOP_LEVEL_BID</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'topLevelBid'</span></em><a class="headerlink" href="#nodriver.cdp.storage.InterestGroupAccessType.TOP_LEVEL_BID" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.InterestGroupAccessType.TOP_LEVEL_ADDITIONAL_BID">
<span class="sig-name descname"><span class="pre">TOP_LEVEL_ADDITIONAL_BID</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'topLevelAdditionalBid'</span></em><a class="headerlink" href="#nodriver.cdp.storage.InterestGroupAccessType.TOP_LEVEL_ADDITIONAL_BID" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.InterestGroupAccessType.CLEAR">
<span class="sig-name descname"><span class="pre">CLEAR</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'clear'</span></em><a class="headerlink" href="#nodriver.cdp.storage.InterestGroupAccessType.CLEAR" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.storage.InterestGroupAuctionEventType">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">InterestGroupAuctionEventType</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">names</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">module</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">qualname</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">boundary</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#InterestGroupAuctionEventType"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.InterestGroupAuctionEventType" title="Link to this definition">#</a></dt>
<dd><p>Enum of auction events.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.InterestGroupAuctionEventType.STARTED">
<span class="sig-name descname"><span class="pre">STARTED</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'started'</span></em><a class="headerlink" href="#nodriver.cdp.storage.InterestGroupAuctionEventType.STARTED" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.InterestGroupAuctionEventType.CONFIG_RESOLVED">
<span class="sig-name descname"><span class="pre">CONFIG_RESOLVED</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'configResolved'</span></em><a class="headerlink" href="#nodriver.cdp.storage.InterestGroupAuctionEventType.CONFIG_RESOLVED" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.storage.InterestGroupAuctionFetchType">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">InterestGroupAuctionFetchType</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">names</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">module</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">qualname</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">boundary</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#InterestGroupAuctionFetchType"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.InterestGroupAuctionFetchType" title="Link to this definition">#</a></dt>
<dd><p>Enum of network fetches auctions can do.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.InterestGroupAuctionFetchType.BIDDER_JS">
<span class="sig-name descname"><span class="pre">BIDDER_JS</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'bidderJs'</span></em><a class="headerlink" href="#nodriver.cdp.storage.InterestGroupAuctionFetchType.BIDDER_JS" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.InterestGroupAuctionFetchType.BIDDER_WASM">
<span class="sig-name descname"><span class="pre">BIDDER_WASM</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'bidderWasm'</span></em><a class="headerlink" href="#nodriver.cdp.storage.InterestGroupAuctionFetchType.BIDDER_WASM" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.InterestGroupAuctionFetchType.SELLER_JS">
<span class="sig-name descname"><span class="pre">SELLER_JS</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'sellerJs'</span></em><a class="headerlink" href="#nodriver.cdp.storage.InterestGroupAuctionFetchType.SELLER_JS" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.InterestGroupAuctionFetchType.BIDDER_TRUSTED_SIGNALS">
<span class="sig-name descname"><span class="pre">BIDDER_TRUSTED_SIGNALS</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'bidderTrustedSignals'</span></em><a class="headerlink" href="#nodriver.cdp.storage.InterestGroupAuctionFetchType.BIDDER_TRUSTED_SIGNALS" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.InterestGroupAuctionFetchType.SELLER_TRUSTED_SIGNALS">
<span class="sig-name descname"><span class="pre">SELLER_TRUSTED_SIGNALS</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'sellerTrustedSignals'</span></em><a class="headerlink" href="#nodriver.cdp.storage.InterestGroupAuctionFetchType.SELLER_TRUSTED_SIGNALS" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.storage.SharedStorageAccessType">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">SharedStorageAccessType</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">names</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">module</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">qualname</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">boundary</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#SharedStorageAccessType"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.SharedStorageAccessType" title="Link to this definition">#</a></dt>
<dd><p>Enum of shared storage access types.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.SharedStorageAccessType.DOCUMENT_ADD_MODULE">
<span class="sig-name descname"><span class="pre">DOCUMENT_ADD_MODULE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'documentAddModule'</span></em><a class="headerlink" href="#nodriver.cdp.storage.SharedStorageAccessType.DOCUMENT_ADD_MODULE" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.SharedStorageAccessType.DOCUMENT_SELECT_URL">
<span class="sig-name descname"><span class="pre">DOCUMENT_SELECT_URL</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'documentSelectURL'</span></em><a class="headerlink" href="#nodriver.cdp.storage.SharedStorageAccessType.DOCUMENT_SELECT_URL" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.SharedStorageAccessType.DOCUMENT_RUN">
<span class="sig-name descname"><span class="pre">DOCUMENT_RUN</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'documentRun'</span></em><a class="headerlink" href="#nodriver.cdp.storage.SharedStorageAccessType.DOCUMENT_RUN" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.SharedStorageAccessType.DOCUMENT_SET">
<span class="sig-name descname"><span class="pre">DOCUMENT_SET</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'documentSet'</span></em><a class="headerlink" href="#nodriver.cdp.storage.SharedStorageAccessType.DOCUMENT_SET" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.SharedStorageAccessType.DOCUMENT_APPEND">
<span class="sig-name descname"><span class="pre">DOCUMENT_APPEND</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'documentAppend'</span></em><a class="headerlink" href="#nodriver.cdp.storage.SharedStorageAccessType.DOCUMENT_APPEND" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.SharedStorageAccessType.DOCUMENT_DELETE">
<span class="sig-name descname"><span class="pre">DOCUMENT_DELETE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'documentDelete'</span></em><a class="headerlink" href="#nodriver.cdp.storage.SharedStorageAccessType.DOCUMENT_DELETE" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.SharedStorageAccessType.DOCUMENT_CLEAR">
<span class="sig-name descname"><span class="pre">DOCUMENT_CLEAR</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'documentClear'</span></em><a class="headerlink" href="#nodriver.cdp.storage.SharedStorageAccessType.DOCUMENT_CLEAR" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.SharedStorageAccessType.DOCUMENT_GET">
<span class="sig-name descname"><span class="pre">DOCUMENT_GET</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'documentGet'</span></em><a class="headerlink" href="#nodriver.cdp.storage.SharedStorageAccessType.DOCUMENT_GET" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.SharedStorageAccessType.WORKLET_SET">
<span class="sig-name descname"><span class="pre">WORKLET_SET</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'workletSet'</span></em><a class="headerlink" href="#nodriver.cdp.storage.SharedStorageAccessType.WORKLET_SET" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.SharedStorageAccessType.WORKLET_APPEND">
<span class="sig-name descname"><span class="pre">WORKLET_APPEND</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'workletAppend'</span></em><a class="headerlink" href="#nodriver.cdp.storage.SharedStorageAccessType.WORKLET_APPEND" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.SharedStorageAccessType.WORKLET_DELETE">
<span class="sig-name descname"><span class="pre">WORKLET_DELETE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'workletDelete'</span></em><a class="headerlink" href="#nodriver.cdp.storage.SharedStorageAccessType.WORKLET_DELETE" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.SharedStorageAccessType.WORKLET_CLEAR">
<span class="sig-name descname"><span class="pre">WORKLET_CLEAR</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'workletClear'</span></em><a class="headerlink" href="#nodriver.cdp.storage.SharedStorageAccessType.WORKLET_CLEAR" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.SharedStorageAccessType.WORKLET_GET">
<span class="sig-name descname"><span class="pre">WORKLET_GET</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'workletGet'</span></em><a class="headerlink" href="#nodriver.cdp.storage.SharedStorageAccessType.WORKLET_GET" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.SharedStorageAccessType.WORKLET_KEYS">
<span class="sig-name descname"><span class="pre">WORKLET_KEYS</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'workletKeys'</span></em><a class="headerlink" href="#nodriver.cdp.storage.SharedStorageAccessType.WORKLET_KEYS" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.SharedStorageAccessType.WORKLET_ENTRIES">
<span class="sig-name descname"><span class="pre">WORKLET_ENTRIES</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'workletEntries'</span></em><a class="headerlink" href="#nodriver.cdp.storage.SharedStorageAccessType.WORKLET_ENTRIES" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.SharedStorageAccessType.WORKLET_LENGTH">
<span class="sig-name descname"><span class="pre">WORKLET_LENGTH</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'workletLength'</span></em><a class="headerlink" href="#nodriver.cdp.storage.SharedStorageAccessType.WORKLET_LENGTH" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.SharedStorageAccessType.WORKLET_REMAINING_BUDGET">
<span class="sig-name descname"><span class="pre">WORKLET_REMAINING_BUDGET</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'workletRemainingBudget'</span></em><a class="headerlink" href="#nodriver.cdp.storage.SharedStorageAccessType.WORKLET_REMAINING_BUDGET" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.SharedStorageAccessType.HEADER_SET">
<span class="sig-name descname"><span class="pre">HEADER_SET</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'headerSet'</span></em><a class="headerlink" href="#nodriver.cdp.storage.SharedStorageAccessType.HEADER_SET" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.SharedStorageAccessType.HEADER_APPEND">
<span class="sig-name descname"><span class="pre">HEADER_APPEND</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'headerAppend'</span></em><a class="headerlink" href="#nodriver.cdp.storage.SharedStorageAccessType.HEADER_APPEND" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.SharedStorageAccessType.HEADER_DELETE">
<span class="sig-name descname"><span class="pre">HEADER_DELETE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'headerDelete'</span></em><a class="headerlink" href="#nodriver.cdp.storage.SharedStorageAccessType.HEADER_DELETE" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.SharedStorageAccessType.HEADER_CLEAR">
<span class="sig-name descname"><span class="pre">HEADER_CLEAR</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'headerClear'</span></em><a class="headerlink" href="#nodriver.cdp.storage.SharedStorageAccessType.HEADER_CLEAR" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.storage.SharedStorageEntry">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">SharedStorageEntry</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#SharedStorageEntry"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.SharedStorageEntry" title="Link to this definition">#</a></dt>
<dd><p>Struct for a single key-value pair in an origin’s shared storage.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.SharedStorageEntry.key">
<span class="sig-name descname"><span class="pre">key</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.SharedStorageEntry.key" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.SharedStorageEntry.value">
<span class="sig-name descname"><span class="pre">value</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.SharedStorageEntry.value" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.storage.SharedStorageMetadata">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">SharedStorageMetadata</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">creation_time</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">length</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">remaining_budget</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">bytes_used</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#SharedStorageMetadata"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.SharedStorageMetadata" title="Link to this definition">#</a></dt>
<dd><p>Details for an origin’s shared storage.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.SharedStorageMetadata.creation_time">
<span class="sig-name descname"><span class="pre">creation_time</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="network.html#nodriver.cdp.network.TimeSinceEpoch" title="nodriver.cdp.network.TimeSinceEpoch"><code class="xref py py-class docutils literal notranslate"><span class="pre">TimeSinceEpoch</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.SharedStorageMetadata.creation_time" title="Link to this definition">#</a></dt>
<dd><p>Time when the origin’s shared storage was last created.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.SharedStorageMetadata.length">
<span class="sig-name descname"><span class="pre">length</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.SharedStorageMetadata.length" title="Link to this definition">#</a></dt>
<dd><p>Number of key-value pairs stored in origin’s shared storage.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.SharedStorageMetadata.remaining_budget">
<span class="sig-name descname"><span class="pre">remaining_budget</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.SharedStorageMetadata.remaining_budget" title="Link to this definition">#</a></dt>
<dd><p>Current amount of bits of entropy remaining in the navigation budget.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.SharedStorageMetadata.bytes_used">
<span class="sig-name descname"><span class="pre">bytes_used</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.SharedStorageMetadata.bytes_used" title="Link to this definition">#</a></dt>
<dd><p>Total number of bytes stored as key-value pairs in origin’s shared
storage.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.storage.SharedStorageReportingMetadata">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">SharedStorageReportingMetadata</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">event_type</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">reporting_url</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#SharedStorageReportingMetadata"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.SharedStorageReportingMetadata" title="Link to this definition">#</a></dt>
<dd><p>Pair of reporting metadata details for a candidate URL for <code class="docutils literal notranslate"><span class="pre">selectURL()</span></code>.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.SharedStorageReportingMetadata.event_type">
<span class="sig-name descname"><span class="pre">event_type</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.SharedStorageReportingMetadata.event_type" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.SharedStorageReportingMetadata.reporting_url">
<span class="sig-name descname"><span class="pre">reporting_url</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.SharedStorageReportingMetadata.reporting_url" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.storage.SharedStorageUrlWithMetadata">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">SharedStorageUrlWithMetadata</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">url</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">reporting_metadata</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#SharedStorageUrlWithMetadata"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.SharedStorageUrlWithMetadata" title="Link to this definition">#</a></dt>
<dd><p>Bundles a candidate URL with its reporting metadata.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.SharedStorageUrlWithMetadata.url">
<span class="sig-name descname"><span class="pre">url</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.SharedStorageUrlWithMetadata.url" title="Link to this definition">#</a></dt>
<dd><p>Spec of candidate URL.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.SharedStorageUrlWithMetadata.reporting_metadata">
<span class="sig-name descname"><span class="pre">reporting_metadata</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.storage.SharedStorageReportingMetadata" title="nodriver.cdp.storage.SharedStorageReportingMetadata"><code class="xref py py-class docutils literal notranslate"><span class="pre">SharedStorageReportingMetadata</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.storage.SharedStorageUrlWithMetadata.reporting_metadata" title="Link to this definition">#</a></dt>
<dd><p>Any associated reporting metadata.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.storage.SharedStorageAccessParams">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">SharedStorageAccessParams</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">script_source_url</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">operation_name</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">serialized_data</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">urls_with_metadata</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">key</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">ignore_if_present</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#SharedStorageAccessParams"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.SharedStorageAccessParams" title="Link to this definition">#</a></dt>
<dd><p>Bundles the parameters for shared storage access events whose
presence/absence can vary according to SharedStorageAccessType.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.SharedStorageAccessParams.script_source_url">
<span class="sig-name descname"><span class="pre">script_source_url</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.storage.SharedStorageAccessParams.script_source_url" title="Link to this definition">#</a></dt>
<dd><p>Spec of the module script URL.
Present only for SharedStorageAccessType.documentAddModule.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.SharedStorageAccessParams.operation_name">
<span class="sig-name descname"><span class="pre">operation_name</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.storage.SharedStorageAccessParams.operation_name" title="Link to this definition">#</a></dt>
<dd><p>Name of the registered operation to be run.
Present only for SharedStorageAccessType.documentRun and
SharedStorageAccessType.documentSelectURL.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.SharedStorageAccessParams.serialized_data">
<span class="sig-name descname"><span class="pre">serialized_data</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.storage.SharedStorageAccessParams.serialized_data" title="Link to this definition">#</a></dt>
<dd><p>The operation’s serialized data in bytes (converted to a string).
Present only for SharedStorageAccessType.documentRun and
SharedStorageAccessType.documentSelectURL.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.SharedStorageAccessParams.urls_with_metadata">
<span class="sig-name descname"><span class="pre">urls_with_metadata</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.storage.SharedStorageUrlWithMetadata" title="nodriver.cdp.storage.SharedStorageUrlWithMetadata"><code class="xref py py-class docutils literal notranslate"><span class="pre">SharedStorageUrlWithMetadata</span></code></a><span class="pre">]]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.storage.SharedStorageAccessParams.urls_with_metadata" title="Link to this definition">#</a></dt>
<dd><p>Array of candidate URLs’ specs, along with any associated metadata.
Present only for SharedStorageAccessType.documentSelectURL.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.SharedStorageAccessParams.key">
<span class="sig-name descname"><span class="pre">key</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.storage.SharedStorageAccessParams.key" title="Link to this definition">#</a></dt>
<dd><p>Key for a specific entry in an origin’s shared storage.
Present only for SharedStorageAccessType.documentSet,
SharedStorageAccessType.documentAppend,
SharedStorageAccessType.documentDelete,
SharedStorageAccessType.workletSet,
SharedStorageAccessType.workletAppend,
SharedStorageAccessType.workletDelete,
SharedStorageAccessType.workletGet,
SharedStorageAccessType.headerSet,
SharedStorageAccessType.headerAppend, and
SharedStorageAccessType.headerDelete.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.SharedStorageAccessParams.value">
<span class="sig-name descname"><span class="pre">value</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.storage.SharedStorageAccessParams.value" title="Link to this definition">#</a></dt>
<dd><p>Value for a specific entry in an origin’s shared storage.
Present only for SharedStorageAccessType.documentSet,
SharedStorageAccessType.documentAppend,
SharedStorageAccessType.workletSet,
SharedStorageAccessType.workletAppend,
SharedStorageAccessType.headerSet, and
SharedStorageAccessType.headerAppend.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.SharedStorageAccessParams.ignore_if_present">
<span class="sig-name descname"><span class="pre">ignore_if_present</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.storage.SharedStorageAccessParams.ignore_if_present" title="Link to this definition">#</a></dt>
<dd><p>Whether or not to set an entry for a key if that key is already present.
Present only for SharedStorageAccessType.documentSet,
SharedStorageAccessType.workletSet, and
SharedStorageAccessType.headerSet.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.storage.StorageBucketsDurability">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">StorageBucketsDurability</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">names</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">module</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">qualname</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">boundary</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#StorageBucketsDurability"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.StorageBucketsDurability" title="Link to this definition">#</a></dt>
<dd><dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.StorageBucketsDurability.RELAXED">
<span class="sig-name descname"><span class="pre">RELAXED</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'relaxed'</span></em><a class="headerlink" href="#nodriver.cdp.storage.StorageBucketsDurability.RELAXED" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.StorageBucketsDurability.STRICT">
<span class="sig-name descname"><span class="pre">STRICT</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'strict'</span></em><a class="headerlink" href="#nodriver.cdp.storage.StorageBucketsDurability.STRICT" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.storage.StorageBucket">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">StorageBucket</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">storage_key</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">name</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#StorageBucket"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.StorageBucket" title="Link to this definition">#</a></dt>
<dd><dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.StorageBucket.storage_key">
<span class="sig-name descname"><span class="pre">storage_key</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.storage.SerializedStorageKey" title="nodriver.cdp.storage.SerializedStorageKey"><code class="xref py py-class docutils literal notranslate"><span class="pre">SerializedStorageKey</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.StorageBucket.storage_key" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.StorageBucket.name">
<span class="sig-name descname"><span class="pre">name</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.storage.StorageBucket.name" title="Link to this definition">#</a></dt>
<dd><p>If not specified, it is the default bucket of the storageKey.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.storage.StorageBucketInfo">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">StorageBucketInfo</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">bucket</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">id_</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">expiration</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">quota</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">persistent</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">durability</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#StorageBucketInfo"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.StorageBucketInfo" title="Link to this definition">#</a></dt>
<dd><dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.StorageBucketInfo.bucket">
<span class="sig-name descname"><span class="pre">bucket</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.storage.StorageBucket" title="nodriver.cdp.storage.StorageBucket"><code class="xref py py-class docutils literal notranslate"><span class="pre">StorageBucket</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.StorageBucketInfo.bucket" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.StorageBucketInfo.id_">
<span class="sig-name descname"><span class="pre">id_</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.StorageBucketInfo.id_" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.StorageBucketInfo.expiration">
<span class="sig-name descname"><span class="pre">expiration</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="network.html#nodriver.cdp.network.TimeSinceEpoch" title="nodriver.cdp.network.TimeSinceEpoch"><code class="xref py py-class docutils literal notranslate"><span class="pre">TimeSinceEpoch</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.StorageBucketInfo.expiration" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.StorageBucketInfo.quota">
<span class="sig-name descname"><span class="pre">quota</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.StorageBucketInfo.quota" title="Link to this definition">#</a></dt>
<dd><p>Storage quota (bytes).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.StorageBucketInfo.persistent">
<span class="sig-name descname"><span class="pre">persistent</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.StorageBucketInfo.persistent" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.StorageBucketInfo.durability">
<span class="sig-name descname"><span class="pre">durability</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.storage.StorageBucketsDurability" title="nodriver.cdp.storage.StorageBucketsDurability"><code class="xref py py-class docutils literal notranslate"><span class="pre">StorageBucketsDurability</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.StorageBucketInfo.durability" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingSourceType">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">AttributionReportingSourceType</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">names</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">module</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">qualname</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">boundary</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#AttributionReportingSourceType"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingSourceType" title="Link to this definition">#</a></dt>
<dd><dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingSourceType.NAVIGATION">
<span class="sig-name descname"><span class="pre">NAVIGATION</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'navigation'</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingSourceType.NAVIGATION" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingSourceType.EVENT">
<span class="sig-name descname"><span class="pre">EVENT</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'event'</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingSourceType.EVENT" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.storage.UnsignedInt64AsBase10">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">UnsignedInt64AsBase10</span></span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#UnsignedInt64AsBase10"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.UnsignedInt64AsBase10" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.storage.UnsignedInt128AsBase16">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">UnsignedInt128AsBase16</span></span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#UnsignedInt128AsBase16"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.UnsignedInt128AsBase16" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.storage.SignedInt64AsBase10">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">SignedInt64AsBase10</span></span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#SignedInt64AsBase10"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.SignedInt64AsBase10" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingFilterDataEntry">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">AttributionReportingFilterDataEntry</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">values</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#AttributionReportingFilterDataEntry"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingFilterDataEntry" title="Link to this definition">#</a></dt>
<dd><dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingFilterDataEntry.key">
<span class="sig-name descname"><span class="pre">key</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingFilterDataEntry.key" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingFilterDataEntry.values">
<span class="sig-name descname"><span class="pre">values</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingFilterDataEntry.values" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingFilterConfig">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">AttributionReportingFilterConfig</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">filter_values</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">lookback_window</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#AttributionReportingFilterConfig"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingFilterConfig" title="Link to this definition">#</a></dt>
<dd><dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingFilterConfig.filter_values">
<span class="sig-name descname"><span class="pre">filter_values</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingFilterDataEntry" title="nodriver.cdp.storage.AttributionReportingFilterDataEntry"><code class="xref py py-class docutils literal notranslate"><span class="pre">AttributionReportingFilterDataEntry</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingFilterConfig.filter_values" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingFilterConfig.lookback_window">
<span class="sig-name descname"><span class="pre">lookback_window</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingFilterConfig.lookback_window" title="Link to this definition">#</a></dt>
<dd><p>duration in seconds</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingFilterPair">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">AttributionReportingFilterPair</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">filters</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">not_filters</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#AttributionReportingFilterPair"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingFilterPair" title="Link to this definition">#</a></dt>
<dd><dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingFilterPair.filters">
<span class="sig-name descname"><span class="pre">filters</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingFilterConfig" title="nodriver.cdp.storage.AttributionReportingFilterConfig"><code class="xref py py-class docutils literal notranslate"><span class="pre">AttributionReportingFilterConfig</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingFilterPair.filters" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingFilterPair.not_filters">
<span class="sig-name descname"><span class="pre">not_filters</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingFilterConfig" title="nodriver.cdp.storage.AttributionReportingFilterConfig"><code class="xref py py-class docutils literal notranslate"><span class="pre">AttributionReportingFilterConfig</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingFilterPair.not_filters" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingAggregationKeysEntry">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">AttributionReportingAggregationKeysEntry</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#AttributionReportingAggregationKeysEntry"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingAggregationKeysEntry" title="Link to this definition">#</a></dt>
<dd><dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingAggregationKeysEntry.key">
<span class="sig-name descname"><span class="pre">key</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingAggregationKeysEntry.key" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingAggregationKeysEntry.value">
<span class="sig-name descname"><span class="pre">value</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.storage.UnsignedInt128AsBase16" title="nodriver.cdp.storage.UnsignedInt128AsBase16"><code class="xref py py-class docutils literal notranslate"><span class="pre">UnsignedInt128AsBase16</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingAggregationKeysEntry.value" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingEventReportWindows">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">AttributionReportingEventReportWindows</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">start</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">ends</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#AttributionReportingEventReportWindows"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingEventReportWindows" title="Link to this definition">#</a></dt>
<dd><dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingEventReportWindows.start">
<span class="sig-name descname"><span class="pre">start</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingEventReportWindows.start" title="Link to this definition">#</a></dt>
<dd><p>duration in seconds</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingEventReportWindows.ends">
<span class="sig-name descname"><span class="pre">ends</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingEventReportWindows.ends" title="Link to this definition">#</a></dt>
<dd><p>duration in seconds</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingTriggerSpec">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">AttributionReportingTriggerSpec</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">trigger_data</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">event_report_windows</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#AttributionReportingTriggerSpec"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingTriggerSpec" title="Link to this definition">#</a></dt>
<dd><dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingTriggerSpec.trigger_data">
<span class="sig-name descname"><span class="pre">trigger_data</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingTriggerSpec.trigger_data" title="Link to this definition">#</a></dt>
<dd><p>number instead of integer because not all uint32 can be represented by
int</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingTriggerSpec.event_report_windows">
<span class="sig-name descname"><span class="pre">event_report_windows</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingEventReportWindows" title="nodriver.cdp.storage.AttributionReportingEventReportWindows"><code class="xref py py-class docutils literal notranslate"><span class="pre">AttributionReportingEventReportWindows</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingTriggerSpec.event_report_windows" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingTriggerDataMatching">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">AttributionReportingTriggerDataMatching</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">names</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">module</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">qualname</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">boundary</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#AttributionReportingTriggerDataMatching"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingTriggerDataMatching" title="Link to this definition">#</a></dt>
<dd><dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingTriggerDataMatching.EXACT">
<span class="sig-name descname"><span class="pre">EXACT</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'exact'</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingTriggerDataMatching.EXACT" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingTriggerDataMatching.MODULUS">
<span class="sig-name descname"><span class="pre">MODULUS</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'modulus'</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingTriggerDataMatching.MODULUS" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingAggregatableDebugReportingData">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">AttributionReportingAggregatableDebugReportingData</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key_piece</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">types</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#AttributionReportingAggregatableDebugReportingData"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingAggregatableDebugReportingData" title="Link to this definition">#</a></dt>
<dd><dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingAggregatableDebugReportingData.key_piece">
<span class="sig-name descname"><span class="pre">key_piece</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.storage.UnsignedInt128AsBase16" title="nodriver.cdp.storage.UnsignedInt128AsBase16"><code class="xref py py-class docutils literal notranslate"><span class="pre">UnsignedInt128AsBase16</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingAggregatableDebugReportingData.key_piece" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingAggregatableDebugReportingData.value">
<span class="sig-name descname"><span class="pre">value</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingAggregatableDebugReportingData.value" title="Link to this definition">#</a></dt>
<dd><p>number instead of integer because not all uint32 can be represented by
int</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingAggregatableDebugReportingData.types">
<span class="sig-name descname"><span class="pre">types</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingAggregatableDebugReportingData.types" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingAggregatableDebugReportingConfig">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">AttributionReportingAggregatableDebugReportingConfig</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key_piece</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">debug_data</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">budget</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">aggregation_coordinator_origin</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#AttributionReportingAggregatableDebugReportingConfig"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingAggregatableDebugReportingConfig" title="Link to this definition">#</a></dt>
<dd><dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingAggregatableDebugReportingConfig.key_piece">
<span class="sig-name descname"><span class="pre">key_piece</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.storage.UnsignedInt128AsBase16" title="nodriver.cdp.storage.UnsignedInt128AsBase16"><code class="xref py py-class docutils literal notranslate"><span class="pre">UnsignedInt128AsBase16</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingAggregatableDebugReportingConfig.key_piece" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingAggregatableDebugReportingConfig.debug_data">
<span class="sig-name descname"><span class="pre">debug_data</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingAggregatableDebugReportingData" title="nodriver.cdp.storage.AttributionReportingAggregatableDebugReportingData"><code class="xref py py-class docutils literal notranslate"><span class="pre">AttributionReportingAggregatableDebugReportingData</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingAggregatableDebugReportingConfig.debug_data" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingAggregatableDebugReportingConfig.budget">
<span class="sig-name descname"><span class="pre">budget</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingAggregatableDebugReportingConfig.budget" title="Link to this definition">#</a></dt>
<dd><p>number instead of integer because not all uint32 can be represented by
int, only present for source registrations</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingAggregatableDebugReportingConfig.aggregation_coordinator_origin">
<span class="sig-name descname"><span class="pre">aggregation_coordinator_origin</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingAggregatableDebugReportingConfig.aggregation_coordinator_origin" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionScopesData">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">AttributionScopesData</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">values</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">limit</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">max_event_states</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#AttributionScopesData"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.AttributionScopesData" title="Link to this definition">#</a></dt>
<dd><dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionScopesData.values">
<span class="sig-name descname"><span class="pre">values</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionScopesData.values" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionScopesData.limit">
<span class="sig-name descname"><span class="pre">limit</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionScopesData.limit" title="Link to this definition">#</a></dt>
<dd><p>number instead of integer because not all uint32 can be represented by
int</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionScopesData.max_event_states">
<span class="sig-name descname"><span class="pre">max_event_states</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionScopesData.max_event_states" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingSourceRegistration">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">AttributionReportingSourceRegistration</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">time</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">expiry</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">trigger_specs</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">aggregatable_report_window</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type_</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">source_origin</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">reporting_origin</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">destination_sites</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">event_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">priority</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">filter_data</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">aggregation_keys</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">trigger_data_matching</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">destination_limit_priority</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">aggregatable_debug_reporting_config</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">max_event_level_reports</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">debug_key</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">scopes_data</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#AttributionReportingSourceRegistration"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingSourceRegistration" title="Link to this definition">#</a></dt>
<dd><dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingSourceRegistration.time">
<span class="sig-name descname"><span class="pre">time</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="network.html#nodriver.cdp.network.TimeSinceEpoch" title="nodriver.cdp.network.TimeSinceEpoch"><code class="xref py py-class docutils literal notranslate"><span class="pre">TimeSinceEpoch</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingSourceRegistration.time" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingSourceRegistration.expiry">
<span class="sig-name descname"><span class="pre">expiry</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingSourceRegistration.expiry" title="Link to this definition">#</a></dt>
<dd><p>duration in seconds</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingSourceRegistration.trigger_specs">
<span class="sig-name descname"><span class="pre">trigger_specs</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingTriggerSpec" title="nodriver.cdp.storage.AttributionReportingTriggerSpec"><code class="xref py py-class docutils literal notranslate"><span class="pre">AttributionReportingTriggerSpec</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingSourceRegistration.trigger_specs" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingSourceRegistration.aggregatable_report_window">
<span class="sig-name descname"><span class="pre">aggregatable_report_window</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingSourceRegistration.aggregatable_report_window" title="Link to this definition">#</a></dt>
<dd><p>duration in seconds</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingSourceRegistration.type_">
<span class="sig-name descname"><span class="pre">type_</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingSourceType" title="nodriver.cdp.storage.AttributionReportingSourceType"><code class="xref py py-class docutils literal notranslate"><span class="pre">AttributionReportingSourceType</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingSourceRegistration.type_" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingSourceRegistration.source_origin">
<span class="sig-name descname"><span class="pre">source_origin</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingSourceRegistration.source_origin" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingSourceRegistration.reporting_origin">
<span class="sig-name descname"><span class="pre">reporting_origin</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingSourceRegistration.reporting_origin" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingSourceRegistration.destination_sites">
<span class="sig-name descname"><span class="pre">destination_sites</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingSourceRegistration.destination_sites" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingSourceRegistration.event_id">
<span class="sig-name descname"><span class="pre">event_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.storage.UnsignedInt64AsBase10" title="nodriver.cdp.storage.UnsignedInt64AsBase10"><code class="xref py py-class docutils literal notranslate"><span class="pre">UnsignedInt64AsBase10</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingSourceRegistration.event_id" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingSourceRegistration.priority">
<span class="sig-name descname"><span class="pre">priority</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.storage.SignedInt64AsBase10" title="nodriver.cdp.storage.SignedInt64AsBase10"><code class="xref py py-class docutils literal notranslate"><span class="pre">SignedInt64AsBase10</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingSourceRegistration.priority" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingSourceRegistration.filter_data">
<span class="sig-name descname"><span class="pre">filter_data</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingFilterDataEntry" title="nodriver.cdp.storage.AttributionReportingFilterDataEntry"><code class="xref py py-class docutils literal notranslate"><span class="pre">AttributionReportingFilterDataEntry</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingSourceRegistration.filter_data" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingSourceRegistration.aggregation_keys">
<span class="sig-name descname"><span class="pre">aggregation_keys</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingAggregationKeysEntry" title="nodriver.cdp.storage.AttributionReportingAggregationKeysEntry"><code class="xref py py-class docutils literal notranslate"><span class="pre">AttributionReportingAggregationKeysEntry</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingSourceRegistration.aggregation_keys" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingSourceRegistration.trigger_data_matching">
<span class="sig-name descname"><span class="pre">trigger_data_matching</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingTriggerDataMatching" title="nodriver.cdp.storage.AttributionReportingTriggerDataMatching"><code class="xref py py-class docutils literal notranslate"><span class="pre">AttributionReportingTriggerDataMatching</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingSourceRegistration.trigger_data_matching" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingSourceRegistration.destination_limit_priority">
<span class="sig-name descname"><span class="pre">destination_limit_priority</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.storage.SignedInt64AsBase10" title="nodriver.cdp.storage.SignedInt64AsBase10"><code class="xref py py-class docutils literal notranslate"><span class="pre">SignedInt64AsBase10</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingSourceRegistration.destination_limit_priority" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingSourceRegistration.aggregatable_debug_reporting_config">
<span class="sig-name descname"><span class="pre">aggregatable_debug_reporting_config</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingAggregatableDebugReportingConfig" title="nodriver.cdp.storage.AttributionReportingAggregatableDebugReportingConfig"><code class="xref py py-class docutils literal notranslate"><span class="pre">AttributionReportingAggregatableDebugReportingConfig</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingSourceRegistration.aggregatable_debug_reporting_config" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingSourceRegistration.max_event_level_reports">
<span class="sig-name descname"><span class="pre">max_event_level_reports</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingSourceRegistration.max_event_level_reports" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingSourceRegistration.debug_key">
<span class="sig-name descname"><span class="pre">debug_key</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.storage.UnsignedInt64AsBase10" title="nodriver.cdp.storage.UnsignedInt64AsBase10"><code class="xref py py-class docutils literal notranslate"><span class="pre">UnsignedInt64AsBase10</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingSourceRegistration.debug_key" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingSourceRegistration.scopes_data">
<span class="sig-name descname"><span class="pre">scopes_data</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.storage.AttributionScopesData" title="nodriver.cdp.storage.AttributionScopesData"><code class="xref py py-class docutils literal notranslate"><span class="pre">AttributionScopesData</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingSourceRegistration.scopes_data" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingSourceRegistrationResult">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">AttributionReportingSourceRegistrationResult</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">names</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">module</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">qualname</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">boundary</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#AttributionReportingSourceRegistrationResult"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingSourceRegistrationResult" title="Link to this definition">#</a></dt>
<dd><dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingSourceRegistrationResult.SUCCESS">
<span class="sig-name descname"><span class="pre">SUCCESS</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'success'</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingSourceRegistrationResult.SUCCESS" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingSourceRegistrationResult.INTERNAL_ERROR">
<span class="sig-name descname"><span class="pre">INTERNAL_ERROR</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'internalError'</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingSourceRegistrationResult.INTERNAL_ERROR" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingSourceRegistrationResult.INSUFFICIENT_SOURCE_CAPACITY">
<span class="sig-name descname"><span class="pre">INSUFFICIENT_SOURCE_CAPACITY</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'insufficientSourceCapacity'</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingSourceRegistrationResult.INSUFFICIENT_SOURCE_CAPACITY" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingSourceRegistrationResult.INSUFFICIENT_UNIQUE_DESTINATION_CAPACITY">
<span class="sig-name descname"><span class="pre">INSUFFICIENT_UNIQUE_DESTINATION_CAPACITY</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'insufficientUniqueDestinationCapacity'</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingSourceRegistrationResult.INSUFFICIENT_UNIQUE_DESTINATION_CAPACITY" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingSourceRegistrationResult.EXCESSIVE_REPORTING_ORIGINS">
<span class="sig-name descname"><span class="pre">EXCESSIVE_REPORTING_ORIGINS</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'excessiveReportingOrigins'</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingSourceRegistrationResult.EXCESSIVE_REPORTING_ORIGINS" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingSourceRegistrationResult.PROHIBITED_BY_BROWSER_POLICY">
<span class="sig-name descname"><span class="pre">PROHIBITED_BY_BROWSER_POLICY</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'prohibitedByBrowserPolicy'</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingSourceRegistrationResult.PROHIBITED_BY_BROWSER_POLICY" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingSourceRegistrationResult.SUCCESS_NOISED">
<span class="sig-name descname"><span class="pre">SUCCESS_NOISED</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'successNoised'</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingSourceRegistrationResult.SUCCESS_NOISED" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingSourceRegistrationResult.DESTINATION_REPORTING_LIMIT_REACHED">
<span class="sig-name descname"><span class="pre">DESTINATION_REPORTING_LIMIT_REACHED</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'destinationReportingLimitReached'</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingSourceRegistrationResult.DESTINATION_REPORTING_LIMIT_REACHED" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingSourceRegistrationResult.DESTINATION_GLOBAL_LIMIT_REACHED">
<span class="sig-name descname"><span class="pre">DESTINATION_GLOBAL_LIMIT_REACHED</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'destinationGlobalLimitReached'</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingSourceRegistrationResult.DESTINATION_GLOBAL_LIMIT_REACHED" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingSourceRegistrationResult.DESTINATION_BOTH_LIMITS_REACHED">
<span class="sig-name descname"><span class="pre">DESTINATION_BOTH_LIMITS_REACHED</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'destinationBothLimitsReached'</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingSourceRegistrationResult.DESTINATION_BOTH_LIMITS_REACHED" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingSourceRegistrationResult.REPORTING_ORIGINS_PER_SITE_LIMIT_REACHED">
<span class="sig-name descname"><span class="pre">REPORTING_ORIGINS_PER_SITE_LIMIT_REACHED</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'reportingOriginsPerSiteLimitReached'</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingSourceRegistrationResult.REPORTING_ORIGINS_PER_SITE_LIMIT_REACHED" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingSourceRegistrationResult.EXCEEDS_MAX_CHANNEL_CAPACITY">
<span class="sig-name descname"><span class="pre">EXCEEDS_MAX_CHANNEL_CAPACITY</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'exceedsMaxChannelCapacity'</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingSourceRegistrationResult.EXCEEDS_MAX_CHANNEL_CAPACITY" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingSourceRegistrationResult.EXCEEDS_MAX_SCOPES_CHANNEL_CAPACITY">
<span class="sig-name descname"><span class="pre">EXCEEDS_MAX_SCOPES_CHANNEL_CAPACITY</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'exceedsMaxScopesChannelCapacity'</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingSourceRegistrationResult.EXCEEDS_MAX_SCOPES_CHANNEL_CAPACITY" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingSourceRegistrationResult.EXCEEDS_MAX_TRIGGER_STATE_CARDINALITY">
<span class="sig-name descname"><span class="pre">EXCEEDS_MAX_TRIGGER_STATE_CARDINALITY</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'exceedsMaxTriggerStateCardinality'</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingSourceRegistrationResult.EXCEEDS_MAX_TRIGGER_STATE_CARDINALITY" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingSourceRegistrationResult.EXCEEDS_MAX_EVENT_STATES_LIMIT">
<span class="sig-name descname"><span class="pre">EXCEEDS_MAX_EVENT_STATES_LIMIT</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'exceedsMaxEventStatesLimit'</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingSourceRegistrationResult.EXCEEDS_MAX_EVENT_STATES_LIMIT" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingSourceRegistrationResult.DESTINATION_PER_DAY_REPORTING_LIMIT_REACHED">
<span class="sig-name descname"><span class="pre">DESTINATION_PER_DAY_REPORTING_LIMIT_REACHED</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'destinationPerDayReportingLimitReached'</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingSourceRegistrationResult.DESTINATION_PER_DAY_REPORTING_LIMIT_REACHED" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingSourceRegistrationTimeConfig">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">AttributionReportingSourceRegistrationTimeConfig</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">names</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">module</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">qualname</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">boundary</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#AttributionReportingSourceRegistrationTimeConfig"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingSourceRegistrationTimeConfig" title="Link to this definition">#</a></dt>
<dd><dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingSourceRegistrationTimeConfig.INCLUDE">
<span class="sig-name descname"><span class="pre">INCLUDE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'include'</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingSourceRegistrationTimeConfig.INCLUDE" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingSourceRegistrationTimeConfig.EXCLUDE">
<span class="sig-name descname"><span class="pre">EXCLUDE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'exclude'</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingSourceRegistrationTimeConfig.EXCLUDE" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingAggregatableValueDictEntry">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">AttributionReportingAggregatableValueDictEntry</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">filtering_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#AttributionReportingAggregatableValueDictEntry"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingAggregatableValueDictEntry" title="Link to this definition">#</a></dt>
<dd><dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingAggregatableValueDictEntry.key">
<span class="sig-name descname"><span class="pre">key</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingAggregatableValueDictEntry.key" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingAggregatableValueDictEntry.value">
<span class="sig-name descname"><span class="pre">value</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingAggregatableValueDictEntry.value" title="Link to this definition">#</a></dt>
<dd><p>number instead of integer because not all uint32 can be represented by
int</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingAggregatableValueDictEntry.filtering_id">
<span class="sig-name descname"><span class="pre">filtering_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.storage.UnsignedInt64AsBase10" title="nodriver.cdp.storage.UnsignedInt64AsBase10"><code class="xref py py-class docutils literal notranslate"><span class="pre">UnsignedInt64AsBase10</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingAggregatableValueDictEntry.filtering_id" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingAggregatableValueEntry">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">AttributionReportingAggregatableValueEntry</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">values</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">filters</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#AttributionReportingAggregatableValueEntry"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingAggregatableValueEntry" title="Link to this definition">#</a></dt>
<dd><dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingAggregatableValueEntry.values">
<span class="sig-name descname"><span class="pre">values</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingAggregatableValueDictEntry" title="nodriver.cdp.storage.AttributionReportingAggregatableValueDictEntry"><code class="xref py py-class docutils literal notranslate"><span class="pre">AttributionReportingAggregatableValueDictEntry</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingAggregatableValueEntry.values" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingAggregatableValueEntry.filters">
<span class="sig-name descname"><span class="pre">filters</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingFilterPair" title="nodriver.cdp.storage.AttributionReportingFilterPair"><code class="xref py py-class docutils literal notranslate"><span class="pre">AttributionReportingFilterPair</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingAggregatableValueEntry.filters" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingEventTriggerData">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">AttributionReportingEventTriggerData</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">priority</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">filters</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">dedup_key</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#AttributionReportingEventTriggerData"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingEventTriggerData" title="Link to this definition">#</a></dt>
<dd><dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingEventTriggerData.data">
<span class="sig-name descname"><span class="pre">data</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.storage.UnsignedInt64AsBase10" title="nodriver.cdp.storage.UnsignedInt64AsBase10"><code class="xref py py-class docutils literal notranslate"><span class="pre">UnsignedInt64AsBase10</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingEventTriggerData.data" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingEventTriggerData.priority">
<span class="sig-name descname"><span class="pre">priority</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.storage.SignedInt64AsBase10" title="nodriver.cdp.storage.SignedInt64AsBase10"><code class="xref py py-class docutils literal notranslate"><span class="pre">SignedInt64AsBase10</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingEventTriggerData.priority" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingEventTriggerData.filters">
<span class="sig-name descname"><span class="pre">filters</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingFilterPair" title="nodriver.cdp.storage.AttributionReportingFilterPair"><code class="xref py py-class docutils literal notranslate"><span class="pre">AttributionReportingFilterPair</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingEventTriggerData.filters" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingEventTriggerData.dedup_key">
<span class="sig-name descname"><span class="pre">dedup_key</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.storage.UnsignedInt64AsBase10" title="nodriver.cdp.storage.UnsignedInt64AsBase10"><code class="xref py py-class docutils literal notranslate"><span class="pre">UnsignedInt64AsBase10</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingEventTriggerData.dedup_key" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingAggregatableTriggerData">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">AttributionReportingAggregatableTriggerData</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key_piece</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">source_keys</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">filters</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#AttributionReportingAggregatableTriggerData"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingAggregatableTriggerData" title="Link to this definition">#</a></dt>
<dd><dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingAggregatableTriggerData.key_piece">
<span class="sig-name descname"><span class="pre">key_piece</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.storage.UnsignedInt128AsBase16" title="nodriver.cdp.storage.UnsignedInt128AsBase16"><code class="xref py py-class docutils literal notranslate"><span class="pre">UnsignedInt128AsBase16</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingAggregatableTriggerData.key_piece" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingAggregatableTriggerData.source_keys">
<span class="sig-name descname"><span class="pre">source_keys</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingAggregatableTriggerData.source_keys" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingAggregatableTriggerData.filters">
<span class="sig-name descname"><span class="pre">filters</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingFilterPair" title="nodriver.cdp.storage.AttributionReportingFilterPair"><code class="xref py py-class docutils literal notranslate"><span class="pre">AttributionReportingFilterPair</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingAggregatableTriggerData.filters" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingAggregatableDedupKey">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">AttributionReportingAggregatableDedupKey</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">filters</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">dedup_key</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#AttributionReportingAggregatableDedupKey"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingAggregatableDedupKey" title="Link to this definition">#</a></dt>
<dd><dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingAggregatableDedupKey.filters">
<span class="sig-name descname"><span class="pre">filters</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingFilterPair" title="nodriver.cdp.storage.AttributionReportingFilterPair"><code class="xref py py-class docutils literal notranslate"><span class="pre">AttributionReportingFilterPair</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingAggregatableDedupKey.filters" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingAggregatableDedupKey.dedup_key">
<span class="sig-name descname"><span class="pre">dedup_key</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.storage.UnsignedInt64AsBase10" title="nodriver.cdp.storage.UnsignedInt64AsBase10"><code class="xref py py-class docutils literal notranslate"><span class="pre">UnsignedInt64AsBase10</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingAggregatableDedupKey.dedup_key" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingTriggerRegistration">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">AttributionReportingTriggerRegistration</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">filters</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">aggregatable_dedup_keys</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">event_trigger_data</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">aggregatable_trigger_data</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">aggregatable_values</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">aggregatable_filtering_id_max_bytes</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">debug_reporting</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">source_registration_time_config</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">aggregatable_debug_reporting_config</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">scopes</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">debug_key</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">aggregation_coordinator_origin</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">trigger_context_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#AttributionReportingTriggerRegistration"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingTriggerRegistration" title="Link to this definition">#</a></dt>
<dd><dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingTriggerRegistration.filters">
<span class="sig-name descname"><span class="pre">filters</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingFilterPair" title="nodriver.cdp.storage.AttributionReportingFilterPair"><code class="xref py py-class docutils literal notranslate"><span class="pre">AttributionReportingFilterPair</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingTriggerRegistration.filters" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingTriggerRegistration.aggregatable_dedup_keys">
<span class="sig-name descname"><span class="pre">aggregatable_dedup_keys</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingAggregatableDedupKey" title="nodriver.cdp.storage.AttributionReportingAggregatableDedupKey"><code class="xref py py-class docutils literal notranslate"><span class="pre">AttributionReportingAggregatableDedupKey</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingTriggerRegistration.aggregatable_dedup_keys" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingTriggerRegistration.event_trigger_data">
<span class="sig-name descname"><span class="pre">event_trigger_data</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingEventTriggerData" title="nodriver.cdp.storage.AttributionReportingEventTriggerData"><code class="xref py py-class docutils literal notranslate"><span class="pre">AttributionReportingEventTriggerData</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingTriggerRegistration.event_trigger_data" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingTriggerRegistration.aggregatable_trigger_data">
<span class="sig-name descname"><span class="pre">aggregatable_trigger_data</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingAggregatableTriggerData" title="nodriver.cdp.storage.AttributionReportingAggregatableTriggerData"><code class="xref py py-class docutils literal notranslate"><span class="pre">AttributionReportingAggregatableTriggerData</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingTriggerRegistration.aggregatable_trigger_data" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingTriggerRegistration.aggregatable_values">
<span class="sig-name descname"><span class="pre">aggregatable_values</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingAggregatableValueEntry" title="nodriver.cdp.storage.AttributionReportingAggregatableValueEntry"><code class="xref py py-class docutils literal notranslate"><span class="pre">AttributionReportingAggregatableValueEntry</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingTriggerRegistration.aggregatable_values" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingTriggerRegistration.aggregatable_filtering_id_max_bytes">
<span class="sig-name descname"><span class="pre">aggregatable_filtering_id_max_bytes</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingTriggerRegistration.aggregatable_filtering_id_max_bytes" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingTriggerRegistration.debug_reporting">
<span class="sig-name descname"><span class="pre">debug_reporting</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingTriggerRegistration.debug_reporting" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingTriggerRegistration.source_registration_time_config">
<span class="sig-name descname"><span class="pre">source_registration_time_config</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingSourceRegistrationTimeConfig" title="nodriver.cdp.storage.AttributionReportingSourceRegistrationTimeConfig"><code class="xref py py-class docutils literal notranslate"><span class="pre">AttributionReportingSourceRegistrationTimeConfig</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingTriggerRegistration.source_registration_time_config" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingTriggerRegistration.aggregatable_debug_reporting_config">
<span class="sig-name descname"><span class="pre">aggregatable_debug_reporting_config</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingAggregatableDebugReportingConfig" title="nodriver.cdp.storage.AttributionReportingAggregatableDebugReportingConfig"><code class="xref py py-class docutils literal notranslate"><span class="pre">AttributionReportingAggregatableDebugReportingConfig</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingTriggerRegistration.aggregatable_debug_reporting_config" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingTriggerRegistration.scopes">
<span class="sig-name descname"><span class="pre">scopes</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingTriggerRegistration.scopes" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingTriggerRegistration.debug_key">
<span class="sig-name descname"><span class="pre">debug_key</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.storage.UnsignedInt64AsBase10" title="nodriver.cdp.storage.UnsignedInt64AsBase10"><code class="xref py py-class docutils literal notranslate"><span class="pre">UnsignedInt64AsBase10</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingTriggerRegistration.debug_key" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingTriggerRegistration.aggregation_coordinator_origin">
<span class="sig-name descname"><span class="pre">aggregation_coordinator_origin</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingTriggerRegistration.aggregation_coordinator_origin" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingTriggerRegistration.trigger_context_id">
<span class="sig-name descname"><span class="pre">trigger_context_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingTriggerRegistration.trigger_context_id" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingEventLevelResult">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">AttributionReportingEventLevelResult</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">names</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">module</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">qualname</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">boundary</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#AttributionReportingEventLevelResult"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingEventLevelResult" title="Link to this definition">#</a></dt>
<dd><dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingEventLevelResult.SUCCESS">
<span class="sig-name descname"><span class="pre">SUCCESS</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'success'</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingEventLevelResult.SUCCESS" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingEventLevelResult.SUCCESS_DROPPED_LOWER_PRIORITY">
<span class="sig-name descname"><span class="pre">SUCCESS_DROPPED_LOWER_PRIORITY</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'successDroppedLowerPriority'</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingEventLevelResult.SUCCESS_DROPPED_LOWER_PRIORITY" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingEventLevelResult.INTERNAL_ERROR">
<span class="sig-name descname"><span class="pre">INTERNAL_ERROR</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'internalError'</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingEventLevelResult.INTERNAL_ERROR" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingEventLevelResult.NO_CAPACITY_FOR_ATTRIBUTION_DESTINATION">
<span class="sig-name descname"><span class="pre">NO_CAPACITY_FOR_ATTRIBUTION_DESTINATION</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'noCapacityForAttributionDestination'</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingEventLevelResult.NO_CAPACITY_FOR_ATTRIBUTION_DESTINATION" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingEventLevelResult.NO_MATCHING_SOURCES">
<span class="sig-name descname"><span class="pre">NO_MATCHING_SOURCES</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'noMatchingSources'</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingEventLevelResult.NO_MATCHING_SOURCES" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingEventLevelResult.DEDUPLICATED">
<span class="sig-name descname"><span class="pre">DEDUPLICATED</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'deduplicated'</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingEventLevelResult.DEDUPLICATED" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingEventLevelResult.EXCESSIVE_ATTRIBUTIONS">
<span class="sig-name descname"><span class="pre">EXCESSIVE_ATTRIBUTIONS</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'excessiveAttributions'</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingEventLevelResult.EXCESSIVE_ATTRIBUTIONS" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingEventLevelResult.PRIORITY_TOO_LOW">
<span class="sig-name descname"><span class="pre">PRIORITY_TOO_LOW</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'priorityTooLow'</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingEventLevelResult.PRIORITY_TOO_LOW" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingEventLevelResult.NEVER_ATTRIBUTED_SOURCE">
<span class="sig-name descname"><span class="pre">NEVER_ATTRIBUTED_SOURCE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'neverAttributedSource'</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingEventLevelResult.NEVER_ATTRIBUTED_SOURCE" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingEventLevelResult.EXCESSIVE_REPORTING_ORIGINS">
<span class="sig-name descname"><span class="pre">EXCESSIVE_REPORTING_ORIGINS</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'excessiveReportingOrigins'</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingEventLevelResult.EXCESSIVE_REPORTING_ORIGINS" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingEventLevelResult.NO_MATCHING_SOURCE_FILTER_DATA">
<span class="sig-name descname"><span class="pre">NO_MATCHING_SOURCE_FILTER_DATA</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'noMatchingSourceFilterData'</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingEventLevelResult.NO_MATCHING_SOURCE_FILTER_DATA" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingEventLevelResult.PROHIBITED_BY_BROWSER_POLICY">
<span class="sig-name descname"><span class="pre">PROHIBITED_BY_BROWSER_POLICY</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'prohibitedByBrowserPolicy'</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingEventLevelResult.PROHIBITED_BY_BROWSER_POLICY" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingEventLevelResult.NO_MATCHING_CONFIGURATIONS">
<span class="sig-name descname"><span class="pre">NO_MATCHING_CONFIGURATIONS</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'noMatchingConfigurations'</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingEventLevelResult.NO_MATCHING_CONFIGURATIONS" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingEventLevelResult.EXCESSIVE_REPORTS">
<span class="sig-name descname"><span class="pre">EXCESSIVE_REPORTS</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'excessiveReports'</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingEventLevelResult.EXCESSIVE_REPORTS" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingEventLevelResult.FALSELY_ATTRIBUTED_SOURCE">
<span class="sig-name descname"><span class="pre">FALSELY_ATTRIBUTED_SOURCE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'falselyAttributedSource'</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingEventLevelResult.FALSELY_ATTRIBUTED_SOURCE" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingEventLevelResult.REPORT_WINDOW_PASSED">
<span class="sig-name descname"><span class="pre">REPORT_WINDOW_PASSED</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'reportWindowPassed'</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingEventLevelResult.REPORT_WINDOW_PASSED" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingEventLevelResult.NOT_REGISTERED">
<span class="sig-name descname"><span class="pre">NOT_REGISTERED</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'notRegistered'</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingEventLevelResult.NOT_REGISTERED" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingEventLevelResult.REPORT_WINDOW_NOT_STARTED">
<span class="sig-name descname"><span class="pre">REPORT_WINDOW_NOT_STARTED</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'reportWindowNotStarted'</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingEventLevelResult.REPORT_WINDOW_NOT_STARTED" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingEventLevelResult.NO_MATCHING_TRIGGER_DATA">
<span class="sig-name descname"><span class="pre">NO_MATCHING_TRIGGER_DATA</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'noMatchingTriggerData'</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingEventLevelResult.NO_MATCHING_TRIGGER_DATA" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingAggregatableResult">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">AttributionReportingAggregatableResult</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">names</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">module</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">qualname</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">boundary</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#AttributionReportingAggregatableResult"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingAggregatableResult" title="Link to this definition">#</a></dt>
<dd><dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingAggregatableResult.SUCCESS">
<span class="sig-name descname"><span class="pre">SUCCESS</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'success'</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingAggregatableResult.SUCCESS" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingAggregatableResult.INTERNAL_ERROR">
<span class="sig-name descname"><span class="pre">INTERNAL_ERROR</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'internalError'</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingAggregatableResult.INTERNAL_ERROR" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingAggregatableResult.NO_CAPACITY_FOR_ATTRIBUTION_DESTINATION">
<span class="sig-name descname"><span class="pre">NO_CAPACITY_FOR_ATTRIBUTION_DESTINATION</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'noCapacityForAttributionDestination'</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingAggregatableResult.NO_CAPACITY_FOR_ATTRIBUTION_DESTINATION" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingAggregatableResult.NO_MATCHING_SOURCES">
<span class="sig-name descname"><span class="pre">NO_MATCHING_SOURCES</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'noMatchingSources'</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingAggregatableResult.NO_MATCHING_SOURCES" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingAggregatableResult.EXCESSIVE_ATTRIBUTIONS">
<span class="sig-name descname"><span class="pre">EXCESSIVE_ATTRIBUTIONS</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'excessiveAttributions'</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingAggregatableResult.EXCESSIVE_ATTRIBUTIONS" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingAggregatableResult.EXCESSIVE_REPORTING_ORIGINS">
<span class="sig-name descname"><span class="pre">EXCESSIVE_REPORTING_ORIGINS</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'excessiveReportingOrigins'</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingAggregatableResult.EXCESSIVE_REPORTING_ORIGINS" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingAggregatableResult.NO_HISTOGRAMS">
<span class="sig-name descname"><span class="pre">NO_HISTOGRAMS</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'noHistograms'</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingAggregatableResult.NO_HISTOGRAMS" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingAggregatableResult.INSUFFICIENT_BUDGET">
<span class="sig-name descname"><span class="pre">INSUFFICIENT_BUDGET</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'insufficientBudget'</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingAggregatableResult.INSUFFICIENT_BUDGET" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingAggregatableResult.INSUFFICIENT_NAMED_BUDGET">
<span class="sig-name descname"><span class="pre">INSUFFICIENT_NAMED_BUDGET</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'insufficientNamedBudget'</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingAggregatableResult.INSUFFICIENT_NAMED_BUDGET" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingAggregatableResult.NO_MATCHING_SOURCE_FILTER_DATA">
<span class="sig-name descname"><span class="pre">NO_MATCHING_SOURCE_FILTER_DATA</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'noMatchingSourceFilterData'</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingAggregatableResult.NO_MATCHING_SOURCE_FILTER_DATA" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingAggregatableResult.NOT_REGISTERED">
<span class="sig-name descname"><span class="pre">NOT_REGISTERED</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'notRegistered'</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingAggregatableResult.NOT_REGISTERED" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingAggregatableResult.PROHIBITED_BY_BROWSER_POLICY">
<span class="sig-name descname"><span class="pre">PROHIBITED_BY_BROWSER_POLICY</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'prohibitedByBrowserPolicy'</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingAggregatableResult.PROHIBITED_BY_BROWSER_POLICY" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingAggregatableResult.DEDUPLICATED">
<span class="sig-name descname"><span class="pre">DEDUPLICATED</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'deduplicated'</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingAggregatableResult.DEDUPLICATED" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingAggregatableResult.REPORT_WINDOW_PASSED">
<span class="sig-name descname"><span class="pre">REPORT_WINDOW_PASSED</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'reportWindowPassed'</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingAggregatableResult.REPORT_WINDOW_PASSED" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingAggregatableResult.EXCESSIVE_REPORTS">
<span class="sig-name descname"><span class="pre">EXCESSIVE_REPORTS</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'excessiveReports'</span></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingAggregatableResult.EXCESSIVE_REPORTS" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.storage.RelatedWebsiteSet">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">RelatedWebsiteSet</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">primary_sites</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">associated_sites</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">service_sites</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#RelatedWebsiteSet"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.RelatedWebsiteSet" title="Link to this definition">#</a></dt>
<dd><p>A single Related Website Set object.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.RelatedWebsiteSet.primary_sites">
<span class="sig-name descname"><span class="pre">primary_sites</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.storage.RelatedWebsiteSet.primary_sites" title="Link to this definition">#</a></dt>
<dd><p>The primary site of this set, along with the ccTLDs if there is any.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.RelatedWebsiteSet.associated_sites">
<span class="sig-name descname"><span class="pre">associated_sites</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.storage.RelatedWebsiteSet.associated_sites" title="Link to this definition">#</a></dt>
<dd><p>The associated sites of this set, along with the ccTLDs if there is any.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.RelatedWebsiteSet.service_sites">
<span class="sig-name descname"><span class="pre">service_sites</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.storage.RelatedWebsiteSet.service_sites" title="Link to this definition">#</a></dt>
<dd><p>The service sites of this set, along with the ccTLDs if there is any.</p>
</dd></dl>

</dd></dl>

</section>
<section id="commands">
<h2>Commands<a class="headerlink" href="#commands" title="Link to this heading">#</a></h2>
<p>Each command is a generator function. The return
type <code class="docutils literal notranslate"><span class="pre">Generator[x,</span> <span class="pre">y,</span> <span class="pre">z]</span></code> indicates that the generator
<em>yields</em> arguments of type <code class="docutils literal notranslate"><span class="pre">x</span></code>, it must be resumed with
an argument of type <code class="docutils literal notranslate"><span class="pre">y</span></code>, and it returns type <code class="docutils literal notranslate"><span class="pre">z</span></code>. In
this library, types <code class="docutils literal notranslate"><span class="pre">x</span></code> and <code class="docutils literal notranslate"><span class="pre">y</span></code> are the same for all
commands, and <code class="docutils literal notranslate"><span class="pre">z</span></code> is the return type you should pay attention
to. For more information, see
<a class="reference internal" href="../../readme.html#getting-started-commands"><span class="std std-ref">Getting Started: Commands</span></a>.</p>
<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.storage.clear_cookies">
<span class="sig-name descname"><span class="pre">clear_cookies</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">browser_context_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#clear_cookies"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.clear_cookies" title="Link to this definition">#</a></dt>
<dd><p>Clears cookies.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>browser_context_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="browser.html#nodriver.cdp.browser.BrowserContextID" title="nodriver.cdp.browser.BrowserContextID"><code class="xref py py-class docutils literal notranslate"><span class="pre">BrowserContextID</span></code></a>]</span>) – <em>(Optional)</em> Browser context to use when called on the browser endpoint.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.storage.clear_data_for_origin">
<span class="sig-name descname"><span class="pre">clear_data_for_origin</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">origin</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">storage_types</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#clear_data_for_origin"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.clear_data_for_origin" title="Link to this definition">#</a></dt>
<dd><p>Clears storage for origin.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>origin</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – Security origin.</p></li>
<li><p><strong>storage_types</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – Comma separated list of StorageType to clear.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.storage.clear_data_for_storage_key">
<span class="sig-name descname"><span class="pre">clear_data_for_storage_key</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">storage_key</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">storage_types</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#clear_data_for_storage_key"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.clear_data_for_storage_key" title="Link to this definition">#</a></dt>
<dd><p>Clears storage for storage key.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>storage_key</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – Storage key.</p></li>
<li><p><strong>storage_types</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – Comma separated list of StorageType to clear.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.storage.clear_shared_storage_entries">
<span class="sig-name descname"><span class="pre">clear_shared_storage_entries</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">owner_origin</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#clear_shared_storage_entries"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.clear_shared_storage_entries" title="Link to this definition">#</a></dt>
<dd><p>Clears all entries for a given origin’s shared storage.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>owner_origin</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – </p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.storage.clear_trust_tokens">
<span class="sig-name descname"><span class="pre">clear_trust_tokens</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">issuer_origin</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#clear_trust_tokens"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.clear_trust_tokens" title="Link to this definition">#</a></dt>
<dd><p>Removes all Trust Tokens issued by the provided issuerOrigin.
Leaves other stored data, including the issuer’s Redemption Records, intact.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>issuer_origin</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – </p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>True if any tokens were deleted, false otherwise.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.storage.delete_shared_storage_entry">
<span class="sig-name descname"><span class="pre">delete_shared_storage_entry</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">owner_origin</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">key</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#delete_shared_storage_entry"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.delete_shared_storage_entry" title="Link to this definition">#</a></dt>
<dd><p>Deletes entry for <code class="docutils literal notranslate"><span class="pre">key</span></code> (if it exists) for a given origin’s shared storage.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>owner_origin</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – </p></li>
<li><p><strong>key</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – </p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.storage.delete_storage_bucket">
<span class="sig-name descname"><span class="pre">delete_storage_bucket</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">bucket</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#delete_storage_bucket"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.delete_storage_bucket" title="Link to this definition">#</a></dt>
<dd><p>Deletes the Storage Bucket with the given storage key and bucket name.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>bucket</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.storage.StorageBucket" title="nodriver.cdp.storage.StorageBucket"><code class="xref py py-class docutils literal notranslate"><span class="pre">StorageBucket</span></code></a></span>) – </p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.storage.get_affected_urls_for_third_party_cookie_metadata">
<span class="sig-name descname"><span class="pre">get_affected_urls_for_third_party_cookie_metadata</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">first_party_url</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">third_party_urls</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#get_affected_urls_for_third_party_cookie_metadata"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.get_affected_urls_for_third_party_cookie_metadata" title="Link to this definition">#</a></dt>
<dd><p>Returns the list of URLs from a page and its embedded resources that match
existing grace period URL pattern rules.
<a class="reference external" href="https://developers.google.com/privacy-sandbox/cookies/temporary-exceptions/grace-period">https://developers.google.com/privacy-sandbox/cookies/temporary-exceptions/grace-period</a></p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>first_party_url</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – The URL of the page currently being visited.</p></li>
<li><p><strong>third_party_urls</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]</span>) – The list of embedded resource URLs from the page.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>Array of matching URLs. If there is a primary pattern match for the first- party URL, only the first-party URL is returned in the array.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.storage.get_cookies">
<span class="sig-name descname"><span class="pre">get_cookies</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">browser_context_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#get_cookies"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.get_cookies" title="Link to this definition">#</a></dt>
<dd><p>Returns all browser cookies.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>browser_context_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="browser.html#nodriver.cdp.browser.BrowserContextID" title="nodriver.cdp.browser.BrowserContextID"><code class="xref py py-class docutils literal notranslate"><span class="pre">BrowserContextID</span></code></a>]</span>) – <em>(Optional)</em> Browser context to use when called on the browser endpoint.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="network.html#nodriver.cdp.network.Cookie" title="nodriver.cdp.network.Cookie"><code class="xref py py-class docutils literal notranslate"><span class="pre">Cookie</span></code></a>]]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>Array of cookie objects.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.storage.get_interest_group_details">
<span class="sig-name descname"><span class="pre">get_interest_group_details</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">owner_origin</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#get_interest_group_details"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.get_interest_group_details" title="Link to this definition">#</a></dt>
<dd><p>Gets details for a named interest group.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>owner_origin</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – </p></li>
<li><p><strong>name</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – </p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">dict</span></code></a>]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>This largely corresponds to: <a class="reference external" href="https://wicg.github.io/turtledove/#dictdef-generatebidinterestgroup">https://wicg.github.io/turtledove/#dictdef-generatebidinterestgroup</a> but has absolute expirationTime instead of relative lifetimeMs and also adds joiningOrigin.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.storage.get_related_website_sets">
<span class="sig-name descname"><span class="pre">get_related_website_sets</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#get_related_website_sets"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.get_related_website_sets" title="Link to this definition">#</a></dt>
<dd><p>Returns the effective Related Website Sets in use by this profile for the browser
session. The effective Related Website Sets will not change during a browser session.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.storage.RelatedWebsiteSet" title="nodriver.cdp.storage.RelatedWebsiteSet"><code class="xref py py-class docutils literal notranslate"><span class="pre">RelatedWebsiteSet</span></code></a>]]</span></p>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.storage.get_shared_storage_entries">
<span class="sig-name descname"><span class="pre">get_shared_storage_entries</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">owner_origin</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#get_shared_storage_entries"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.get_shared_storage_entries" title="Link to this definition">#</a></dt>
<dd><p>Gets the entries in an given origin’s shared storage.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>owner_origin</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – </p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.storage.SharedStorageEntry" title="nodriver.cdp.storage.SharedStorageEntry"><code class="xref py py-class docutils literal notranslate"><span class="pre">SharedStorageEntry</span></code></a>]]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.storage.get_shared_storage_metadata">
<span class="sig-name descname"><span class="pre">get_shared_storage_metadata</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">owner_origin</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#get_shared_storage_metadata"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.get_shared_storage_metadata" title="Link to this definition">#</a></dt>
<dd><p>Gets metadata for an origin’s shared storage.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>owner_origin</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – </p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference internal" href="#nodriver.cdp.storage.SharedStorageMetadata" title="nodriver.cdp.storage.SharedStorageMetadata"><code class="xref py py-class docutils literal notranslate"><span class="pre">SharedStorageMetadata</span></code></a>]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.storage.get_storage_key_for_frame">
<span class="sig-name descname"><span class="pre">get_storage_key_for_frame</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">frame_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#get_storage_key_for_frame"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.get_storage_key_for_frame" title="Link to this definition">#</a></dt>
<dd><p>Returns a storage key given a frame id.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>frame_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="page.html#nodriver.cdp.page.FrameId" title="nodriver.cdp.page.FrameId"><code class="xref py py-class docutils literal notranslate"><span class="pre">FrameId</span></code></a></span>) – </p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference internal" href="#nodriver.cdp.storage.SerializedStorageKey" title="nodriver.cdp.storage.SerializedStorageKey"><code class="xref py py-class docutils literal notranslate"><span class="pre">SerializedStorageKey</span></code></a>]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.storage.get_trust_tokens">
<span class="sig-name descname"><span class="pre">get_trust_tokens</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#get_trust_tokens"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.get_trust_tokens" title="Link to this definition">#</a></dt>
<dd><p>Returns the number of stored Trust Tokens per issuer for the
current browsing context.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.storage.TrustTokens" title="nodriver.cdp.storage.TrustTokens"><code class="xref py py-class docutils literal notranslate"><span class="pre">TrustTokens</span></code></a>]]</span></p>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.storage.get_usage_and_quota">
<span class="sig-name descname"><span class="pre">get_usage_and_quota</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">origin</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#get_usage_and_quota"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.get_usage_and_quota" title="Link to this definition">#</a></dt>
<dd><p>Returns usage and quota in bytes.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>origin</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – Security origin.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Tuple" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Tuple</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.storage.UsageForType" title="nodriver.cdp.storage.UsageForType"><code class="xref py py-class docutils literal notranslate"><span class="pre">UsageForType</span></code></a>]]]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p><p>A tuple with the following items:</p>
<ol class="arabic simple" start="0">
<li><p><strong>usage</strong> - Storage usage (bytes).</p></li>
<li><p><strong>quota</strong> - Storage quota (bytes).</p></li>
<li><p><strong>overrideActive</strong> - Whether or not the origin has an active storage quota override</p></li>
<li><p><strong>usageBreakdown</strong> - Storage usage per type (bytes).</p></li>
</ol>
</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.storage.override_quota_for_origin">
<span class="sig-name descname"><span class="pre">override_quota_for_origin</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">origin</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">quota_size</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#override_quota_for_origin"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.override_quota_for_origin" title="Link to this definition">#</a></dt>
<dd><p>Override quota for the specified origin</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>origin</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – Security origin.</p></li>
<li><p><strong>quota_size</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a>]</span>) – <em>(Optional)</em> The quota size (in bytes) to override the original quota with. If this is called multiple times, the overridden quota will be equal to the quotaSize provided in the final call. If this is called without specifying a quotaSize, the quota will be reset to the default value for the specified origin. If this is called multiple times with different origins, the override will be maintained for each origin until it is disabled (called without a quotaSize).</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.storage.reset_shared_storage_budget">
<span class="sig-name descname"><span class="pre">reset_shared_storage_budget</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">owner_origin</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#reset_shared_storage_budget"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.reset_shared_storage_budget" title="Link to this definition">#</a></dt>
<dd><p>Resets the budget for <code class="docutils literal notranslate"><span class="pre">ownerOrigin</span></code> by clearing all budget withdrawals.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>owner_origin</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – </p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.storage.run_bounce_tracking_mitigations">
<span class="sig-name descname"><span class="pre">run_bounce_tracking_mitigations</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#run_bounce_tracking_mitigations"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.run_bounce_tracking_mitigations" title="Link to this definition">#</a></dt>
<dd><p>Deletes state for sites identified as potential bounce trackers, immediately.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]]</span></p>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.storage.send_pending_attribution_reports">
<span class="sig-name descname"><span class="pre">send_pending_attribution_reports</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#send_pending_attribution_reports"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.send_pending_attribution_reports" title="Link to this definition">#</a></dt>
<dd><p>Sends all pending Attribution Reports immediately, regardless of their
scheduled report time.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a>]</span></p>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>The number of reports that were sent.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.storage.set_attribution_reporting_local_testing_mode">
<span class="sig-name descname"><span class="pre">set_attribution_reporting_local_testing_mode</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">enabled</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#set_attribution_reporting_local_testing_mode"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.set_attribution_reporting_local_testing_mode" title="Link to this definition">#</a></dt>
<dd><p><a class="reference external" href="https://wicg.github.io/attribution-reporting-api/">https://wicg.github.io/attribution-reporting-api/</a></p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>enabled</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a></span>) – If enabled, noise is suppressed and reports are sent immediately.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.storage.set_attribution_reporting_tracking">
<span class="sig-name descname"><span class="pre">set_attribution_reporting_tracking</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">enable</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#set_attribution_reporting_tracking"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.set_attribution_reporting_tracking" title="Link to this definition">#</a></dt>
<dd><p>Enables/disables issuing of Attribution Reporting events.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>enable</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a></span>) – </p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.storage.set_cookies">
<span class="sig-name descname"><span class="pre">set_cookies</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cookies</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">browser_context_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#set_cookies"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.set_cookies" title="Link to this definition">#</a></dt>
<dd><p>Sets given cookies.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>cookies</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="network.html#nodriver.cdp.network.CookieParam" title="nodriver.cdp.network.CookieParam"><code class="xref py py-class docutils literal notranslate"><span class="pre">CookieParam</span></code></a>]</span>) – Cookies to be set.</p></li>
<li><p><strong>browser_context_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="browser.html#nodriver.cdp.browser.BrowserContextID" title="nodriver.cdp.browser.BrowserContextID"><code class="xref py py-class docutils literal notranslate"><span class="pre">BrowserContextID</span></code></a>]</span>) – <em>(Optional)</em> Browser context to use when called on the browser endpoint.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.storage.set_interest_group_auction_tracking">
<span class="sig-name descname"><span class="pre">set_interest_group_auction_tracking</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">enable</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#set_interest_group_auction_tracking"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.set_interest_group_auction_tracking" title="Link to this definition">#</a></dt>
<dd><p>Enables/Disables issuing of interestGroupAuctionEventOccurred and
interestGroupAuctionNetworkRequestCreated.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>enable</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a></span>) – </p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.storage.set_interest_group_tracking">
<span class="sig-name descname"><span class="pre">set_interest_group_tracking</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">enable</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#set_interest_group_tracking"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.set_interest_group_tracking" title="Link to this definition">#</a></dt>
<dd><p>Enables/Disables issuing of interestGroupAccessed events.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>enable</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a></span>) – </p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.storage.set_shared_storage_entry">
<span class="sig-name descname"><span class="pre">set_shared_storage_entry</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">owner_origin</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">key</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">ignore_if_present</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#set_shared_storage_entry"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.set_shared_storage_entry" title="Link to this definition">#</a></dt>
<dd><p>Sets entry with <code class="docutils literal notranslate"><span class="pre">key</span></code> and <code class="docutils literal notranslate"><span class="pre">value</span></code> for a given origin’s shared storage.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>owner_origin</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – </p></li>
<li><p><strong>key</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – </p></li>
<li><p><strong>value</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – </p></li>
<li><p><strong>ignore_if_present</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <em>(Optional)</em> If <code class="docutils literal notranslate"><span class="pre">`ignoreIfPresent``</span></code> is included and true, then only sets the entry if <code class="docutils literal notranslate"><span class="pre">``key`</span></code> doesn’t already exist.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.storage.set_shared_storage_tracking">
<span class="sig-name descname"><span class="pre">set_shared_storage_tracking</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">enable</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#set_shared_storage_tracking"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.set_shared_storage_tracking" title="Link to this definition">#</a></dt>
<dd><p>Enables/disables issuing of sharedStorageAccessed events.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>enable</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a></span>) – </p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.storage.set_storage_bucket_tracking">
<span class="sig-name descname"><span class="pre">set_storage_bucket_tracking</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">storage_key</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">enable</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#set_storage_bucket_tracking"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.set_storage_bucket_tracking" title="Link to this definition">#</a></dt>
<dd><p>Set tracking for a storage key’s buckets.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>storage_key</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – </p></li>
<li><p><strong>enable</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a></span>) – </p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.storage.track_cache_storage_for_origin">
<span class="sig-name descname"><span class="pre">track_cache_storage_for_origin</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">origin</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#track_cache_storage_for_origin"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.track_cache_storage_for_origin" title="Link to this definition">#</a></dt>
<dd><p>Registers origin to be notified when an update occurs to its cache storage list.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>origin</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – Security origin.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.storage.track_cache_storage_for_storage_key">
<span class="sig-name descname"><span class="pre">track_cache_storage_for_storage_key</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">storage_key</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#track_cache_storage_for_storage_key"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.track_cache_storage_for_storage_key" title="Link to this definition">#</a></dt>
<dd><p>Registers storage key to be notified when an update occurs to its cache storage list.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>storage_key</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – Storage key.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.storage.track_indexed_db_for_origin">
<span class="sig-name descname"><span class="pre">track_indexed_db_for_origin</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">origin</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#track_indexed_db_for_origin"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.track_indexed_db_for_origin" title="Link to this definition">#</a></dt>
<dd><p>Registers origin to be notified when an update occurs to its IndexedDB.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>origin</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – Security origin.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.storage.track_indexed_db_for_storage_key">
<span class="sig-name descname"><span class="pre">track_indexed_db_for_storage_key</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">storage_key</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#track_indexed_db_for_storage_key"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.track_indexed_db_for_storage_key" title="Link to this definition">#</a></dt>
<dd><p>Registers storage key to be notified when an update occurs to its IndexedDB.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>storage_key</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – Storage key.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.storage.untrack_cache_storage_for_origin">
<span class="sig-name descname"><span class="pre">untrack_cache_storage_for_origin</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">origin</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#untrack_cache_storage_for_origin"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.untrack_cache_storage_for_origin" title="Link to this definition">#</a></dt>
<dd><p>Unregisters origin from receiving notifications for cache storage.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>origin</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – Security origin.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.storage.untrack_cache_storage_for_storage_key">
<span class="sig-name descname"><span class="pre">untrack_cache_storage_for_storage_key</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">storage_key</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#untrack_cache_storage_for_storage_key"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.untrack_cache_storage_for_storage_key" title="Link to this definition">#</a></dt>
<dd><p>Unregisters storage key from receiving notifications for cache storage.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>storage_key</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – Storage key.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.storage.untrack_indexed_db_for_origin">
<span class="sig-name descname"><span class="pre">untrack_indexed_db_for_origin</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">origin</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#untrack_indexed_db_for_origin"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.untrack_indexed_db_for_origin" title="Link to this definition">#</a></dt>
<dd><p>Unregisters origin from receiving notifications for IndexedDB.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>origin</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – Security origin.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.storage.untrack_indexed_db_for_storage_key">
<span class="sig-name descname"><span class="pre">untrack_indexed_db_for_storage_key</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">storage_key</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#untrack_indexed_db_for_storage_key"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.untrack_indexed_db_for_storage_key" title="Link to this definition">#</a></dt>
<dd><p>Unregisters storage key from receiving notifications for IndexedDB.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>storage_key</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – Storage key.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

</section>
<section id="events">
<h2>Events<a class="headerlink" href="#events" title="Link to this heading">#</a></h2>
<p>Generally, you do not need to instantiate CDP events
yourself. Instead, the API creates events for you and then
you use the event’s attributes.</p>
<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.storage.CacheStorageContentUpdated">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">CacheStorageContentUpdated</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">origin</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">storage_key</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">bucket_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">cache_name</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#CacheStorageContentUpdated"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.CacheStorageContentUpdated" title="Link to this definition">#</a></dt>
<dd><p>A cache’s contents have been modified.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.CacheStorageContentUpdated.origin">
<span class="sig-name descname"><span class="pre">origin</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.CacheStorageContentUpdated.origin" title="Link to this definition">#</a></dt>
<dd><p>Origin to update.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.CacheStorageContentUpdated.storage_key">
<span class="sig-name descname"><span class="pre">storage_key</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.CacheStorageContentUpdated.storage_key" title="Link to this definition">#</a></dt>
<dd><p>Storage key to update.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.CacheStorageContentUpdated.bucket_id">
<span class="sig-name descname"><span class="pre">bucket_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.CacheStorageContentUpdated.bucket_id" title="Link to this definition">#</a></dt>
<dd><p>Storage bucket to update.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.CacheStorageContentUpdated.cache_name">
<span class="sig-name descname"><span class="pre">cache_name</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.CacheStorageContentUpdated.cache_name" title="Link to this definition">#</a></dt>
<dd><p>Name of cache in origin.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.storage.CacheStorageListUpdated">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">CacheStorageListUpdated</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">origin</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">storage_key</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">bucket_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#CacheStorageListUpdated"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.CacheStorageListUpdated" title="Link to this definition">#</a></dt>
<dd><p>A cache has been added/deleted.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.CacheStorageListUpdated.origin">
<span class="sig-name descname"><span class="pre">origin</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.CacheStorageListUpdated.origin" title="Link to this definition">#</a></dt>
<dd><p>Origin to update.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.CacheStorageListUpdated.storage_key">
<span class="sig-name descname"><span class="pre">storage_key</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.CacheStorageListUpdated.storage_key" title="Link to this definition">#</a></dt>
<dd><p>Storage key to update.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.CacheStorageListUpdated.bucket_id">
<span class="sig-name descname"><span class="pre">bucket_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.CacheStorageListUpdated.bucket_id" title="Link to this definition">#</a></dt>
<dd><p>Storage bucket to update.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.storage.IndexedDBContentUpdated">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">IndexedDBContentUpdated</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">origin</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">storage_key</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">bucket_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">database_name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">object_store_name</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#IndexedDBContentUpdated"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.IndexedDBContentUpdated" title="Link to this definition">#</a></dt>
<dd><p>The origin’s IndexedDB object store has been modified.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.IndexedDBContentUpdated.origin">
<span class="sig-name descname"><span class="pre">origin</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.IndexedDBContentUpdated.origin" title="Link to this definition">#</a></dt>
<dd><p>Origin to update.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.IndexedDBContentUpdated.storage_key">
<span class="sig-name descname"><span class="pre">storage_key</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.IndexedDBContentUpdated.storage_key" title="Link to this definition">#</a></dt>
<dd><p>Storage key to update.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.IndexedDBContentUpdated.bucket_id">
<span class="sig-name descname"><span class="pre">bucket_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.IndexedDBContentUpdated.bucket_id" title="Link to this definition">#</a></dt>
<dd><p>Storage bucket to update.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.IndexedDBContentUpdated.database_name">
<span class="sig-name descname"><span class="pre">database_name</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.IndexedDBContentUpdated.database_name" title="Link to this definition">#</a></dt>
<dd><p>Database to update.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.IndexedDBContentUpdated.object_store_name">
<span class="sig-name descname"><span class="pre">object_store_name</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.IndexedDBContentUpdated.object_store_name" title="Link to this definition">#</a></dt>
<dd><p>ObjectStore to update.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.storage.IndexedDBListUpdated">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">IndexedDBListUpdated</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">origin</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">storage_key</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">bucket_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#IndexedDBListUpdated"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.IndexedDBListUpdated" title="Link to this definition">#</a></dt>
<dd><p>The origin’s IndexedDB database list has been modified.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.IndexedDBListUpdated.origin">
<span class="sig-name descname"><span class="pre">origin</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.IndexedDBListUpdated.origin" title="Link to this definition">#</a></dt>
<dd><p>Origin to update.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.IndexedDBListUpdated.storage_key">
<span class="sig-name descname"><span class="pre">storage_key</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.IndexedDBListUpdated.storage_key" title="Link to this definition">#</a></dt>
<dd><p>Storage key to update.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.IndexedDBListUpdated.bucket_id">
<span class="sig-name descname"><span class="pre">bucket_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.IndexedDBListUpdated.bucket_id" title="Link to this definition">#</a></dt>
<dd><p>Storage bucket to update.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.storage.InterestGroupAccessed">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">InterestGroupAccessed</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">access_time</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type_</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">owner_origin</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">component_seller_origin</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">bid</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">bid_currency</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">unique_auction_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#InterestGroupAccessed"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.InterestGroupAccessed" title="Link to this definition">#</a></dt>
<dd><p>One of the interest groups was accessed. Note that these events are global
to all targets sharing an interest group store.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.InterestGroupAccessed.access_time">
<span class="sig-name descname"><span class="pre">access_time</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="network.html#nodriver.cdp.network.TimeSinceEpoch" title="nodriver.cdp.network.TimeSinceEpoch"><code class="xref py py-class docutils literal notranslate"><span class="pre">TimeSinceEpoch</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.InterestGroupAccessed.access_time" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.InterestGroupAccessed.type_">
<span class="sig-name descname"><span class="pre">type_</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.storage.InterestGroupAccessType" title="nodriver.cdp.storage.InterestGroupAccessType"><code class="xref py py-class docutils literal notranslate"><span class="pre">InterestGroupAccessType</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.InterestGroupAccessed.type_" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.InterestGroupAccessed.owner_origin">
<span class="sig-name descname"><span class="pre">owner_origin</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.InterestGroupAccessed.owner_origin" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.InterestGroupAccessed.name">
<span class="sig-name descname"><span class="pre">name</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.InterestGroupAccessed.name" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.InterestGroupAccessed.component_seller_origin">
<span class="sig-name descname"><span class="pre">component_seller_origin</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.storage.InterestGroupAccessed.component_seller_origin" title="Link to this definition">#</a></dt>
<dd><p>For topLevelBid/topLevelAdditionalBid, and when appropriate,
win and additionalBidWin</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.InterestGroupAccessed.bid">
<span class="sig-name descname"><span class="pre">bid</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.storage.InterestGroupAccessed.bid" title="Link to this definition">#</a></dt>
<dd><p>For bid or somethingBid event, if done locally and not on a server.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.InterestGroupAccessed.bid_currency">
<span class="sig-name descname"><span class="pre">bid_currency</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.storage.InterestGroupAccessed.bid_currency" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.InterestGroupAccessed.unique_auction_id">
<span class="sig-name descname"><span class="pre">unique_auction_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.storage.InterestGroupAuctionId" title="nodriver.cdp.storage.InterestGroupAuctionId"><code class="xref py py-class docutils literal notranslate"><span class="pre">InterestGroupAuctionId</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.storage.InterestGroupAccessed.unique_auction_id" title="Link to this definition">#</a></dt>
<dd><p>For non-global events — links to interestGroupAuctionEvent</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.storage.InterestGroupAuctionEventOccurred">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">InterestGroupAuctionEventOccurred</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">event_time</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type_</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">unique_auction_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">parent_auction_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">auction_config</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#InterestGroupAuctionEventOccurred"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.InterestGroupAuctionEventOccurred" title="Link to this definition">#</a></dt>
<dd><p>An auction involving interest groups is taking place. These events are
target-specific.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.InterestGroupAuctionEventOccurred.event_time">
<span class="sig-name descname"><span class="pre">event_time</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="network.html#nodriver.cdp.network.TimeSinceEpoch" title="nodriver.cdp.network.TimeSinceEpoch"><code class="xref py py-class docutils literal notranslate"><span class="pre">TimeSinceEpoch</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.InterestGroupAuctionEventOccurred.event_time" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.InterestGroupAuctionEventOccurred.type_">
<span class="sig-name descname"><span class="pre">type_</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.storage.InterestGroupAuctionEventType" title="nodriver.cdp.storage.InterestGroupAuctionEventType"><code class="xref py py-class docutils literal notranslate"><span class="pre">InterestGroupAuctionEventType</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.InterestGroupAuctionEventOccurred.type_" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.InterestGroupAuctionEventOccurred.unique_auction_id">
<span class="sig-name descname"><span class="pre">unique_auction_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.storage.InterestGroupAuctionId" title="nodriver.cdp.storage.InterestGroupAuctionId"><code class="xref py py-class docutils literal notranslate"><span class="pre">InterestGroupAuctionId</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.InterestGroupAuctionEventOccurred.unique_auction_id" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.InterestGroupAuctionEventOccurred.parent_auction_id">
<span class="sig-name descname"><span class="pre">parent_auction_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.storage.InterestGroupAuctionId" title="nodriver.cdp.storage.InterestGroupAuctionId"><code class="xref py py-class docutils literal notranslate"><span class="pre">InterestGroupAuctionId</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.storage.InterestGroupAuctionEventOccurred.parent_auction_id" title="Link to this definition">#</a></dt>
<dd><p>Set for child auctions.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.InterestGroupAuctionEventOccurred.auction_config">
<span class="sig-name descname"><span class="pre">auction_config</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">dict</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.storage.InterestGroupAuctionEventOccurred.auction_config" title="Link to this definition">#</a></dt>
<dd><p>Set for started and configResolved</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.storage.InterestGroupAuctionNetworkRequestCreated">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">InterestGroupAuctionNetworkRequestCreated</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">type_</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">request_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">auctions</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#InterestGroupAuctionNetworkRequestCreated"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.InterestGroupAuctionNetworkRequestCreated" title="Link to this definition">#</a></dt>
<dd><p>Specifies which auctions a particular network fetch may be related to, and
in what role. Note that it is not ordered with respect to
Network.requestWillBeSent (but will happen before loadingFinished
loadingFailed).</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.InterestGroupAuctionNetworkRequestCreated.type_">
<span class="sig-name descname"><span class="pre">type_</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.storage.InterestGroupAuctionFetchType" title="nodriver.cdp.storage.InterestGroupAuctionFetchType"><code class="xref py py-class docutils literal notranslate"><span class="pre">InterestGroupAuctionFetchType</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.InterestGroupAuctionNetworkRequestCreated.type_" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.InterestGroupAuctionNetworkRequestCreated.request_id">
<span class="sig-name descname"><span class="pre">request_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="network.html#nodriver.cdp.network.RequestId" title="nodriver.cdp.network.RequestId"><code class="xref py py-class docutils literal notranslate"><span class="pre">RequestId</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.InterestGroupAuctionNetworkRequestCreated.request_id" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.InterestGroupAuctionNetworkRequestCreated.auctions">
<span class="sig-name descname"><span class="pre">auctions</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.storage.InterestGroupAuctionId" title="nodriver.cdp.storage.InterestGroupAuctionId"><code class="xref py py-class docutils literal notranslate"><span class="pre">InterestGroupAuctionId</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.storage.InterestGroupAuctionNetworkRequestCreated.auctions" title="Link to this definition">#</a></dt>
<dd><p>This is the set of the auctions using the worklet that issued this
request.  In the case of trusted signals, it’s possible that only some of
them actually care about the keys being queried.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.storage.SharedStorageAccessed">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">SharedStorageAccessed</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">access_time</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type_</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">main_frame_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">owner_origin</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">params</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#SharedStorageAccessed"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.SharedStorageAccessed" title="Link to this definition">#</a></dt>
<dd><p>Shared storage was accessed by the associated page.
The following parameters are included in all events.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.SharedStorageAccessed.access_time">
<span class="sig-name descname"><span class="pre">access_time</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="network.html#nodriver.cdp.network.TimeSinceEpoch" title="nodriver.cdp.network.TimeSinceEpoch"><code class="xref py py-class docutils literal notranslate"><span class="pre">TimeSinceEpoch</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.SharedStorageAccessed.access_time" title="Link to this definition">#</a></dt>
<dd><p>Time of the access.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.SharedStorageAccessed.type_">
<span class="sig-name descname"><span class="pre">type_</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.storage.SharedStorageAccessType" title="nodriver.cdp.storage.SharedStorageAccessType"><code class="xref py py-class docutils literal notranslate"><span class="pre">SharedStorageAccessType</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.SharedStorageAccessed.type_" title="Link to this definition">#</a></dt>
<dd><p>Enum value indicating the Shared Storage API method invoked.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.SharedStorageAccessed.main_frame_id">
<span class="sig-name descname"><span class="pre">main_frame_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="page.html#nodriver.cdp.page.FrameId" title="nodriver.cdp.page.FrameId"><code class="xref py py-class docutils literal notranslate"><span class="pre">FrameId</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.SharedStorageAccessed.main_frame_id" title="Link to this definition">#</a></dt>
<dd><p>DevTools Frame Token for the primary frame tree’s root.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.SharedStorageAccessed.owner_origin">
<span class="sig-name descname"><span class="pre">owner_origin</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.SharedStorageAccessed.owner_origin" title="Link to this definition">#</a></dt>
<dd><p>Serialized origin for the context that invoked the Shared Storage API.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.SharedStorageAccessed.params">
<span class="sig-name descname"><span class="pre">params</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.storage.SharedStorageAccessParams" title="nodriver.cdp.storage.SharedStorageAccessParams"><code class="xref py py-class docutils literal notranslate"><span class="pre">SharedStorageAccessParams</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.SharedStorageAccessed.params" title="Link to this definition">#</a></dt>
<dd><p>The sub-parameters wrapped by <code class="docutils literal notranslate"><span class="pre">params</span></code> are all optional and their
presence/absence depends on <code class="docutils literal notranslate"><span class="pre">type</span></code>.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.storage.StorageBucketCreatedOrUpdated">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">StorageBucketCreatedOrUpdated</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">bucket_info</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#StorageBucketCreatedOrUpdated"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.StorageBucketCreatedOrUpdated" title="Link to this definition">#</a></dt>
<dd><dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.StorageBucketCreatedOrUpdated.bucket_info">
<span class="sig-name descname"><span class="pre">bucket_info</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.storage.StorageBucketInfo" title="nodriver.cdp.storage.StorageBucketInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">StorageBucketInfo</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.StorageBucketCreatedOrUpdated.bucket_info" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.storage.StorageBucketDeleted">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">StorageBucketDeleted</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">bucket_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#StorageBucketDeleted"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.StorageBucketDeleted" title="Link to this definition">#</a></dt>
<dd><dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.StorageBucketDeleted.bucket_id">
<span class="sig-name descname"><span class="pre">bucket_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.StorageBucketDeleted.bucket_id" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingSourceRegistered">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">AttributionReportingSourceRegistered</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">registration</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">result</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#AttributionReportingSourceRegistered"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingSourceRegistered" title="Link to this definition">#</a></dt>
<dd><p><strong>EXPERIMENTAL</strong></p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingSourceRegistered.registration">
<span class="sig-name descname"><span class="pre">registration</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingSourceRegistration" title="nodriver.cdp.storage.AttributionReportingSourceRegistration"><code class="xref py py-class docutils literal notranslate"><span class="pre">AttributionReportingSourceRegistration</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingSourceRegistered.registration" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingSourceRegistered.result">
<span class="sig-name descname"><span class="pre">result</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingSourceRegistrationResult" title="nodriver.cdp.storage.AttributionReportingSourceRegistrationResult"><code class="xref py py-class docutils literal notranslate"><span class="pre">AttributionReportingSourceRegistrationResult</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingSourceRegistered.result" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingTriggerRegistered">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">AttributionReportingTriggerRegistered</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">registration</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">event_level</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">aggregatable</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/storage.html#AttributionReportingTriggerRegistered"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingTriggerRegistered" title="Link to this definition">#</a></dt>
<dd><p><strong>EXPERIMENTAL</strong></p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingTriggerRegistered.registration">
<span class="sig-name descname"><span class="pre">registration</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingTriggerRegistration" title="nodriver.cdp.storage.AttributionReportingTriggerRegistration"><code class="xref py py-class docutils literal notranslate"><span class="pre">AttributionReportingTriggerRegistration</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingTriggerRegistered.registration" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingTriggerRegistered.event_level">
<span class="sig-name descname"><span class="pre">event_level</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingEventLevelResult" title="nodriver.cdp.storage.AttributionReportingEventLevelResult"><code class="xref py py-class docutils literal notranslate"><span class="pre">AttributionReportingEventLevelResult</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingTriggerRegistered.event_level" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.storage.AttributionReportingTriggerRegistered.aggregatable">
<span class="sig-name descname"><span class="pre">aggregatable</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingAggregatableResult" title="nodriver.cdp.storage.AttributionReportingAggregatableResult"><code class="xref py py-class docutils literal notranslate"><span class="pre">AttributionReportingAggregatableResult</span></code></a></em><a class="headerlink" href="#nodriver.cdp.storage.AttributionReportingTriggerRegistered.aggregatable" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

</section>
</section>

        </article>
      </div>
      <footer>
        
        <div class="related-pages">
          <a class="next-page" href="system_info.html">
              <div class="page-info">
                <div class="context">
                  <span>Next</span>
                </div>
                <div class="title">SystemInfo</div>
              </div>
              <svg class="furo-related-icon"><use href="#svg-arrow-right"></use></svg>
            </a>
          <a class="prev-page" href="service_worker.html">
              <svg class="furo-related-icon"><use href="#svg-arrow-right"></use></svg>
              <div class="page-info">
                <div class="context">
                  <span>Previous</span>
                </div>
                
                <div class="title">ServiceWorker</div>
                
              </div>
            </a>
        </div>
        <div class="bottom-of-page">
          <div class="left-details">
            <div class="copyright">
                Copyright &#169; 2023, Author
            </div>
            Made with <a href="https://www.sphinx-doc.org/">Sphinx</a> and <a class="muted-link" href="https://pradyunsg.me">@pradyunsg</a>'s
            
            <a href="https://github.com/pradyunsg/furo">Furo</a>
            
          </div>
          <div class="right-details">
            
          </div>
        </div>
        
      </footer>
    </div>
    <aside class="toc-drawer">
      
      
      <div class="toc-sticky toc-scroll">
        <div class="toc-title-container">
          <span class="toc-title">
            On this page
          </span>
        </div>
        <div class="toc-tree-container">
          <div class="toc-tree">
            <ul>
<li><a class="reference internal" href="#">Storage</a><ul>
<li><a class="reference internal" href="#types">Types</a><ul>
<li><a class="reference internal" href="#nodriver.cdp.storage.SerializedStorageKey"><code class="docutils literal notranslate"><span class="pre">SerializedStorageKey</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.StorageType"><code class="docutils literal notranslate"><span class="pre">StorageType</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.storage.StorageType.COOKIES"><code class="docutils literal notranslate"><span class="pre">StorageType.COOKIES</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.StorageType.FILE_SYSTEMS"><code class="docutils literal notranslate"><span class="pre">StorageType.FILE_SYSTEMS</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.StorageType.INDEXEDDB"><code class="docutils literal notranslate"><span class="pre">StorageType.INDEXEDDB</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.StorageType.LOCAL_STORAGE"><code class="docutils literal notranslate"><span class="pre">StorageType.LOCAL_STORAGE</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.StorageType.SHADER_CACHE"><code class="docutils literal notranslate"><span class="pre">StorageType.SHADER_CACHE</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.StorageType.WEBSQL"><code class="docutils literal notranslate"><span class="pre">StorageType.WEBSQL</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.StorageType.SERVICE_WORKERS"><code class="docutils literal notranslate"><span class="pre">StorageType.SERVICE_WORKERS</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.StorageType.CACHE_STORAGE"><code class="docutils literal notranslate"><span class="pre">StorageType.CACHE_STORAGE</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.StorageType.INTEREST_GROUPS"><code class="docutils literal notranslate"><span class="pre">StorageType.INTEREST_GROUPS</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.StorageType.SHARED_STORAGE"><code class="docutils literal notranslate"><span class="pre">StorageType.SHARED_STORAGE</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.StorageType.STORAGE_BUCKETS"><code class="docutils literal notranslate"><span class="pre">StorageType.STORAGE_BUCKETS</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.StorageType.ALL_"><code class="docutils literal notranslate"><span class="pre">StorageType.ALL_</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.StorageType.OTHER"><code class="docutils literal notranslate"><span class="pre">StorageType.OTHER</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.storage.UsageForType"><code class="docutils literal notranslate"><span class="pre">UsageForType</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.storage.UsageForType.storage_type"><code class="docutils literal notranslate"><span class="pre">UsageForType.storage_type</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.UsageForType.usage"><code class="docutils literal notranslate"><span class="pre">UsageForType.usage</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.storage.TrustTokens"><code class="docutils literal notranslate"><span class="pre">TrustTokens</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.storage.TrustTokens.issuer_origin"><code class="docutils literal notranslate"><span class="pre">TrustTokens.issuer_origin</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.TrustTokens.count"><code class="docutils literal notranslate"><span class="pre">TrustTokens.count</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.storage.InterestGroupAuctionId"><code class="docutils literal notranslate"><span class="pre">InterestGroupAuctionId</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.InterestGroupAccessType"><code class="docutils literal notranslate"><span class="pre">InterestGroupAccessType</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.storage.InterestGroupAccessType.JOIN"><code class="docutils literal notranslate"><span class="pre">InterestGroupAccessType.JOIN</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.InterestGroupAccessType.LEAVE"><code class="docutils literal notranslate"><span class="pre">InterestGroupAccessType.LEAVE</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.InterestGroupAccessType.UPDATE"><code class="docutils literal notranslate"><span class="pre">InterestGroupAccessType.UPDATE</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.InterestGroupAccessType.LOADED"><code class="docutils literal notranslate"><span class="pre">InterestGroupAccessType.LOADED</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.InterestGroupAccessType.BID"><code class="docutils literal notranslate"><span class="pre">InterestGroupAccessType.BID</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.InterestGroupAccessType.WIN"><code class="docutils literal notranslate"><span class="pre">InterestGroupAccessType.WIN</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.InterestGroupAccessType.ADDITIONAL_BID"><code class="docutils literal notranslate"><span class="pre">InterestGroupAccessType.ADDITIONAL_BID</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.InterestGroupAccessType.ADDITIONAL_BID_WIN"><code class="docutils literal notranslate"><span class="pre">InterestGroupAccessType.ADDITIONAL_BID_WIN</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.InterestGroupAccessType.TOP_LEVEL_BID"><code class="docutils literal notranslate"><span class="pre">InterestGroupAccessType.TOP_LEVEL_BID</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.InterestGroupAccessType.TOP_LEVEL_ADDITIONAL_BID"><code class="docutils literal notranslate"><span class="pre">InterestGroupAccessType.TOP_LEVEL_ADDITIONAL_BID</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.InterestGroupAccessType.CLEAR"><code class="docutils literal notranslate"><span class="pre">InterestGroupAccessType.CLEAR</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.storage.InterestGroupAuctionEventType"><code class="docutils literal notranslate"><span class="pre">InterestGroupAuctionEventType</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.storage.InterestGroupAuctionEventType.STARTED"><code class="docutils literal notranslate"><span class="pre">InterestGroupAuctionEventType.STARTED</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.InterestGroupAuctionEventType.CONFIG_RESOLVED"><code class="docutils literal notranslate"><span class="pre">InterestGroupAuctionEventType.CONFIG_RESOLVED</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.storage.InterestGroupAuctionFetchType"><code class="docutils literal notranslate"><span class="pre">InterestGroupAuctionFetchType</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.storage.InterestGroupAuctionFetchType.BIDDER_JS"><code class="docutils literal notranslate"><span class="pre">InterestGroupAuctionFetchType.BIDDER_JS</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.InterestGroupAuctionFetchType.BIDDER_WASM"><code class="docutils literal notranslate"><span class="pre">InterestGroupAuctionFetchType.BIDDER_WASM</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.InterestGroupAuctionFetchType.SELLER_JS"><code class="docutils literal notranslate"><span class="pre">InterestGroupAuctionFetchType.SELLER_JS</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.InterestGroupAuctionFetchType.BIDDER_TRUSTED_SIGNALS"><code class="docutils literal notranslate"><span class="pre">InterestGroupAuctionFetchType.BIDDER_TRUSTED_SIGNALS</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.InterestGroupAuctionFetchType.SELLER_TRUSTED_SIGNALS"><code class="docutils literal notranslate"><span class="pre">InterestGroupAuctionFetchType.SELLER_TRUSTED_SIGNALS</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.storage.SharedStorageAccessType"><code class="docutils literal notranslate"><span class="pre">SharedStorageAccessType</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.storage.SharedStorageAccessType.DOCUMENT_ADD_MODULE"><code class="docutils literal notranslate"><span class="pre">SharedStorageAccessType.DOCUMENT_ADD_MODULE</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.SharedStorageAccessType.DOCUMENT_SELECT_URL"><code class="docutils literal notranslate"><span class="pre">SharedStorageAccessType.DOCUMENT_SELECT_URL</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.SharedStorageAccessType.DOCUMENT_RUN"><code class="docutils literal notranslate"><span class="pre">SharedStorageAccessType.DOCUMENT_RUN</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.SharedStorageAccessType.DOCUMENT_SET"><code class="docutils literal notranslate"><span class="pre">SharedStorageAccessType.DOCUMENT_SET</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.SharedStorageAccessType.DOCUMENT_APPEND"><code class="docutils literal notranslate"><span class="pre">SharedStorageAccessType.DOCUMENT_APPEND</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.SharedStorageAccessType.DOCUMENT_DELETE"><code class="docutils literal notranslate"><span class="pre">SharedStorageAccessType.DOCUMENT_DELETE</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.SharedStorageAccessType.DOCUMENT_CLEAR"><code class="docutils literal notranslate"><span class="pre">SharedStorageAccessType.DOCUMENT_CLEAR</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.SharedStorageAccessType.DOCUMENT_GET"><code class="docutils literal notranslate"><span class="pre">SharedStorageAccessType.DOCUMENT_GET</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.SharedStorageAccessType.WORKLET_SET"><code class="docutils literal notranslate"><span class="pre">SharedStorageAccessType.WORKLET_SET</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.SharedStorageAccessType.WORKLET_APPEND"><code class="docutils literal notranslate"><span class="pre">SharedStorageAccessType.WORKLET_APPEND</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.SharedStorageAccessType.WORKLET_DELETE"><code class="docutils literal notranslate"><span class="pre">SharedStorageAccessType.WORKLET_DELETE</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.SharedStorageAccessType.WORKLET_CLEAR"><code class="docutils literal notranslate"><span class="pre">SharedStorageAccessType.WORKLET_CLEAR</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.SharedStorageAccessType.WORKLET_GET"><code class="docutils literal notranslate"><span class="pre">SharedStorageAccessType.WORKLET_GET</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.SharedStorageAccessType.WORKLET_KEYS"><code class="docutils literal notranslate"><span class="pre">SharedStorageAccessType.WORKLET_KEYS</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.SharedStorageAccessType.WORKLET_ENTRIES"><code class="docutils literal notranslate"><span class="pre">SharedStorageAccessType.WORKLET_ENTRIES</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.SharedStorageAccessType.WORKLET_LENGTH"><code class="docutils literal notranslate"><span class="pre">SharedStorageAccessType.WORKLET_LENGTH</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.SharedStorageAccessType.WORKLET_REMAINING_BUDGET"><code class="docutils literal notranslate"><span class="pre">SharedStorageAccessType.WORKLET_REMAINING_BUDGET</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.SharedStorageAccessType.HEADER_SET"><code class="docutils literal notranslate"><span class="pre">SharedStorageAccessType.HEADER_SET</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.SharedStorageAccessType.HEADER_APPEND"><code class="docutils literal notranslate"><span class="pre">SharedStorageAccessType.HEADER_APPEND</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.SharedStorageAccessType.HEADER_DELETE"><code class="docutils literal notranslate"><span class="pre">SharedStorageAccessType.HEADER_DELETE</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.SharedStorageAccessType.HEADER_CLEAR"><code class="docutils literal notranslate"><span class="pre">SharedStorageAccessType.HEADER_CLEAR</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.storage.SharedStorageEntry"><code class="docutils literal notranslate"><span class="pre">SharedStorageEntry</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.storage.SharedStorageEntry.key"><code class="docutils literal notranslate"><span class="pre">SharedStorageEntry.key</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.SharedStorageEntry.value"><code class="docutils literal notranslate"><span class="pre">SharedStorageEntry.value</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.storage.SharedStorageMetadata"><code class="docutils literal notranslate"><span class="pre">SharedStorageMetadata</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.storage.SharedStorageMetadata.creation_time"><code class="docutils literal notranslate"><span class="pre">SharedStorageMetadata.creation_time</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.SharedStorageMetadata.length"><code class="docutils literal notranslate"><span class="pre">SharedStorageMetadata.length</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.SharedStorageMetadata.remaining_budget"><code class="docutils literal notranslate"><span class="pre">SharedStorageMetadata.remaining_budget</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.SharedStorageMetadata.bytes_used"><code class="docutils literal notranslate"><span class="pre">SharedStorageMetadata.bytes_used</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.storage.SharedStorageReportingMetadata"><code class="docutils literal notranslate"><span class="pre">SharedStorageReportingMetadata</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.storage.SharedStorageReportingMetadata.event_type"><code class="docutils literal notranslate"><span class="pre">SharedStorageReportingMetadata.event_type</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.SharedStorageReportingMetadata.reporting_url"><code class="docutils literal notranslate"><span class="pre">SharedStorageReportingMetadata.reporting_url</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.storage.SharedStorageUrlWithMetadata"><code class="docutils literal notranslate"><span class="pre">SharedStorageUrlWithMetadata</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.storage.SharedStorageUrlWithMetadata.url"><code class="docutils literal notranslate"><span class="pre">SharedStorageUrlWithMetadata.url</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.SharedStorageUrlWithMetadata.reporting_metadata"><code class="docutils literal notranslate"><span class="pre">SharedStorageUrlWithMetadata.reporting_metadata</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.storage.SharedStorageAccessParams"><code class="docutils literal notranslate"><span class="pre">SharedStorageAccessParams</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.storage.SharedStorageAccessParams.script_source_url"><code class="docutils literal notranslate"><span class="pre">SharedStorageAccessParams.script_source_url</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.SharedStorageAccessParams.operation_name"><code class="docutils literal notranslate"><span class="pre">SharedStorageAccessParams.operation_name</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.SharedStorageAccessParams.serialized_data"><code class="docutils literal notranslate"><span class="pre">SharedStorageAccessParams.serialized_data</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.SharedStorageAccessParams.urls_with_metadata"><code class="docutils literal notranslate"><span class="pre">SharedStorageAccessParams.urls_with_metadata</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.SharedStorageAccessParams.key"><code class="docutils literal notranslate"><span class="pre">SharedStorageAccessParams.key</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.SharedStorageAccessParams.value"><code class="docutils literal notranslate"><span class="pre">SharedStorageAccessParams.value</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.SharedStorageAccessParams.ignore_if_present"><code class="docutils literal notranslate"><span class="pre">SharedStorageAccessParams.ignore_if_present</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.storage.StorageBucketsDurability"><code class="docutils literal notranslate"><span class="pre">StorageBucketsDurability</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.storage.StorageBucketsDurability.RELAXED"><code class="docutils literal notranslate"><span class="pre">StorageBucketsDurability.RELAXED</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.StorageBucketsDurability.STRICT"><code class="docutils literal notranslate"><span class="pre">StorageBucketsDurability.STRICT</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.storage.StorageBucket"><code class="docutils literal notranslate"><span class="pre">StorageBucket</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.storage.StorageBucket.storage_key"><code class="docutils literal notranslate"><span class="pre">StorageBucket.storage_key</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.StorageBucket.name"><code class="docutils literal notranslate"><span class="pre">StorageBucket.name</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.storage.StorageBucketInfo"><code class="docutils literal notranslate"><span class="pre">StorageBucketInfo</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.storage.StorageBucketInfo.bucket"><code class="docutils literal notranslate"><span class="pre">StorageBucketInfo.bucket</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.StorageBucketInfo.id_"><code class="docutils literal notranslate"><span class="pre">StorageBucketInfo.id_</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.StorageBucketInfo.expiration"><code class="docutils literal notranslate"><span class="pre">StorageBucketInfo.expiration</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.StorageBucketInfo.quota"><code class="docutils literal notranslate"><span class="pre">StorageBucketInfo.quota</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.StorageBucketInfo.persistent"><code class="docutils literal notranslate"><span class="pre">StorageBucketInfo.persistent</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.StorageBucketInfo.durability"><code class="docutils literal notranslate"><span class="pre">StorageBucketInfo.durability</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingSourceType"><code class="docutils literal notranslate"><span class="pre">AttributionReportingSourceType</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingSourceType.NAVIGATION"><code class="docutils literal notranslate"><span class="pre">AttributionReportingSourceType.NAVIGATION</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingSourceType.EVENT"><code class="docutils literal notranslate"><span class="pre">AttributionReportingSourceType.EVENT</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.storage.UnsignedInt64AsBase10"><code class="docutils literal notranslate"><span class="pre">UnsignedInt64AsBase10</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.UnsignedInt128AsBase16"><code class="docutils literal notranslate"><span class="pre">UnsignedInt128AsBase16</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.SignedInt64AsBase10"><code class="docutils literal notranslate"><span class="pre">SignedInt64AsBase10</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingFilterDataEntry"><code class="docutils literal notranslate"><span class="pre">AttributionReportingFilterDataEntry</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingFilterDataEntry.key"><code class="docutils literal notranslate"><span class="pre">AttributionReportingFilterDataEntry.key</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingFilterDataEntry.values"><code class="docutils literal notranslate"><span class="pre">AttributionReportingFilterDataEntry.values</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingFilterConfig"><code class="docutils literal notranslate"><span class="pre">AttributionReportingFilterConfig</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingFilterConfig.filter_values"><code class="docutils literal notranslate"><span class="pre">AttributionReportingFilterConfig.filter_values</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingFilterConfig.lookback_window"><code class="docutils literal notranslate"><span class="pre">AttributionReportingFilterConfig.lookback_window</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingFilterPair"><code class="docutils literal notranslate"><span class="pre">AttributionReportingFilterPair</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingFilterPair.filters"><code class="docutils literal notranslate"><span class="pre">AttributionReportingFilterPair.filters</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingFilterPair.not_filters"><code class="docutils literal notranslate"><span class="pre">AttributionReportingFilterPair.not_filters</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingAggregationKeysEntry"><code class="docutils literal notranslate"><span class="pre">AttributionReportingAggregationKeysEntry</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingAggregationKeysEntry.key"><code class="docutils literal notranslate"><span class="pre">AttributionReportingAggregationKeysEntry.key</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingAggregationKeysEntry.value"><code class="docutils literal notranslate"><span class="pre">AttributionReportingAggregationKeysEntry.value</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingEventReportWindows"><code class="docutils literal notranslate"><span class="pre">AttributionReportingEventReportWindows</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingEventReportWindows.start"><code class="docutils literal notranslate"><span class="pre">AttributionReportingEventReportWindows.start</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingEventReportWindows.ends"><code class="docutils literal notranslate"><span class="pre">AttributionReportingEventReportWindows.ends</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingTriggerSpec"><code class="docutils literal notranslate"><span class="pre">AttributionReportingTriggerSpec</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingTriggerSpec.trigger_data"><code class="docutils literal notranslate"><span class="pre">AttributionReportingTriggerSpec.trigger_data</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingTriggerSpec.event_report_windows"><code class="docutils literal notranslate"><span class="pre">AttributionReportingTriggerSpec.event_report_windows</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingTriggerDataMatching"><code class="docutils literal notranslate"><span class="pre">AttributionReportingTriggerDataMatching</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingTriggerDataMatching.EXACT"><code class="docutils literal notranslate"><span class="pre">AttributionReportingTriggerDataMatching.EXACT</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingTriggerDataMatching.MODULUS"><code class="docutils literal notranslate"><span class="pre">AttributionReportingTriggerDataMatching.MODULUS</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingAggregatableDebugReportingData"><code class="docutils literal notranslate"><span class="pre">AttributionReportingAggregatableDebugReportingData</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingAggregatableDebugReportingData.key_piece"><code class="docutils literal notranslate"><span class="pre">AttributionReportingAggregatableDebugReportingData.key_piece</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingAggregatableDebugReportingData.value"><code class="docutils literal notranslate"><span class="pre">AttributionReportingAggregatableDebugReportingData.value</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingAggregatableDebugReportingData.types"><code class="docutils literal notranslate"><span class="pre">AttributionReportingAggregatableDebugReportingData.types</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingAggregatableDebugReportingConfig"><code class="docutils literal notranslate"><span class="pre">AttributionReportingAggregatableDebugReportingConfig</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingAggregatableDebugReportingConfig.key_piece"><code class="docutils literal notranslate"><span class="pre">AttributionReportingAggregatableDebugReportingConfig.key_piece</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingAggregatableDebugReportingConfig.debug_data"><code class="docutils literal notranslate"><span class="pre">AttributionReportingAggregatableDebugReportingConfig.debug_data</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingAggregatableDebugReportingConfig.budget"><code class="docutils literal notranslate"><span class="pre">AttributionReportingAggregatableDebugReportingConfig.budget</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingAggregatableDebugReportingConfig.aggregation_coordinator_origin"><code class="docutils literal notranslate"><span class="pre">AttributionReportingAggregatableDebugReportingConfig.aggregation_coordinator_origin</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionScopesData"><code class="docutils literal notranslate"><span class="pre">AttributionScopesData</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionScopesData.values"><code class="docutils literal notranslate"><span class="pre">AttributionScopesData.values</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionScopesData.limit"><code class="docutils literal notranslate"><span class="pre">AttributionScopesData.limit</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionScopesData.max_event_states"><code class="docutils literal notranslate"><span class="pre">AttributionScopesData.max_event_states</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingSourceRegistration"><code class="docutils literal notranslate"><span class="pre">AttributionReportingSourceRegistration</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingSourceRegistration.time"><code class="docutils literal notranslate"><span class="pre">AttributionReportingSourceRegistration.time</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingSourceRegistration.expiry"><code class="docutils literal notranslate"><span class="pre">AttributionReportingSourceRegistration.expiry</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingSourceRegistration.trigger_specs"><code class="docutils literal notranslate"><span class="pre">AttributionReportingSourceRegistration.trigger_specs</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingSourceRegistration.aggregatable_report_window"><code class="docutils literal notranslate"><span class="pre">AttributionReportingSourceRegistration.aggregatable_report_window</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingSourceRegistration.type_"><code class="docutils literal notranslate"><span class="pre">AttributionReportingSourceRegistration.type_</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingSourceRegistration.source_origin"><code class="docutils literal notranslate"><span class="pre">AttributionReportingSourceRegistration.source_origin</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingSourceRegistration.reporting_origin"><code class="docutils literal notranslate"><span class="pre">AttributionReportingSourceRegistration.reporting_origin</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingSourceRegistration.destination_sites"><code class="docutils literal notranslate"><span class="pre">AttributionReportingSourceRegistration.destination_sites</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingSourceRegistration.event_id"><code class="docutils literal notranslate"><span class="pre">AttributionReportingSourceRegistration.event_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingSourceRegistration.priority"><code class="docutils literal notranslate"><span class="pre">AttributionReportingSourceRegistration.priority</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingSourceRegistration.filter_data"><code class="docutils literal notranslate"><span class="pre">AttributionReportingSourceRegistration.filter_data</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingSourceRegistration.aggregation_keys"><code class="docutils literal notranslate"><span class="pre">AttributionReportingSourceRegistration.aggregation_keys</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingSourceRegistration.trigger_data_matching"><code class="docutils literal notranslate"><span class="pre">AttributionReportingSourceRegistration.trigger_data_matching</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingSourceRegistration.destination_limit_priority"><code class="docutils literal notranslate"><span class="pre">AttributionReportingSourceRegistration.destination_limit_priority</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingSourceRegistration.aggregatable_debug_reporting_config"><code class="docutils literal notranslate"><span class="pre">AttributionReportingSourceRegistration.aggregatable_debug_reporting_config</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingSourceRegistration.max_event_level_reports"><code class="docutils literal notranslate"><span class="pre">AttributionReportingSourceRegistration.max_event_level_reports</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingSourceRegistration.debug_key"><code class="docutils literal notranslate"><span class="pre">AttributionReportingSourceRegistration.debug_key</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingSourceRegistration.scopes_data"><code class="docutils literal notranslate"><span class="pre">AttributionReportingSourceRegistration.scopes_data</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingSourceRegistrationResult"><code class="docutils literal notranslate"><span class="pre">AttributionReportingSourceRegistrationResult</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingSourceRegistrationResult.SUCCESS"><code class="docutils literal notranslate"><span class="pre">AttributionReportingSourceRegistrationResult.SUCCESS</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingSourceRegistrationResult.INTERNAL_ERROR"><code class="docutils literal notranslate"><span class="pre">AttributionReportingSourceRegistrationResult.INTERNAL_ERROR</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingSourceRegistrationResult.INSUFFICIENT_SOURCE_CAPACITY"><code class="docutils literal notranslate"><span class="pre">AttributionReportingSourceRegistrationResult.INSUFFICIENT_SOURCE_CAPACITY</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingSourceRegistrationResult.INSUFFICIENT_UNIQUE_DESTINATION_CAPACITY"><code class="docutils literal notranslate"><span class="pre">AttributionReportingSourceRegistrationResult.INSUFFICIENT_UNIQUE_DESTINATION_CAPACITY</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingSourceRegistrationResult.EXCESSIVE_REPORTING_ORIGINS"><code class="docutils literal notranslate"><span class="pre">AttributionReportingSourceRegistrationResult.EXCESSIVE_REPORTING_ORIGINS</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingSourceRegistrationResult.PROHIBITED_BY_BROWSER_POLICY"><code class="docutils literal notranslate"><span class="pre">AttributionReportingSourceRegistrationResult.PROHIBITED_BY_BROWSER_POLICY</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingSourceRegistrationResult.SUCCESS_NOISED"><code class="docutils literal notranslate"><span class="pre">AttributionReportingSourceRegistrationResult.SUCCESS_NOISED</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingSourceRegistrationResult.DESTINATION_REPORTING_LIMIT_REACHED"><code class="docutils literal notranslate"><span class="pre">AttributionReportingSourceRegistrationResult.DESTINATION_REPORTING_LIMIT_REACHED</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingSourceRegistrationResult.DESTINATION_GLOBAL_LIMIT_REACHED"><code class="docutils literal notranslate"><span class="pre">AttributionReportingSourceRegistrationResult.DESTINATION_GLOBAL_LIMIT_REACHED</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingSourceRegistrationResult.DESTINATION_BOTH_LIMITS_REACHED"><code class="docutils literal notranslate"><span class="pre">AttributionReportingSourceRegistrationResult.DESTINATION_BOTH_LIMITS_REACHED</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingSourceRegistrationResult.REPORTING_ORIGINS_PER_SITE_LIMIT_REACHED"><code class="docutils literal notranslate"><span class="pre">AttributionReportingSourceRegistrationResult.REPORTING_ORIGINS_PER_SITE_LIMIT_REACHED</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingSourceRegistrationResult.EXCEEDS_MAX_CHANNEL_CAPACITY"><code class="docutils literal notranslate"><span class="pre">AttributionReportingSourceRegistrationResult.EXCEEDS_MAX_CHANNEL_CAPACITY</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingSourceRegistrationResult.EXCEEDS_MAX_SCOPES_CHANNEL_CAPACITY"><code class="docutils literal notranslate"><span class="pre">AttributionReportingSourceRegistrationResult.EXCEEDS_MAX_SCOPES_CHANNEL_CAPACITY</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingSourceRegistrationResult.EXCEEDS_MAX_TRIGGER_STATE_CARDINALITY"><code class="docutils literal notranslate"><span class="pre">AttributionReportingSourceRegistrationResult.EXCEEDS_MAX_TRIGGER_STATE_CARDINALITY</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingSourceRegistrationResult.EXCEEDS_MAX_EVENT_STATES_LIMIT"><code class="docutils literal notranslate"><span class="pre">AttributionReportingSourceRegistrationResult.EXCEEDS_MAX_EVENT_STATES_LIMIT</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingSourceRegistrationResult.DESTINATION_PER_DAY_REPORTING_LIMIT_REACHED"><code class="docutils literal notranslate"><span class="pre">AttributionReportingSourceRegistrationResult.DESTINATION_PER_DAY_REPORTING_LIMIT_REACHED</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingSourceRegistrationTimeConfig"><code class="docutils literal notranslate"><span class="pre">AttributionReportingSourceRegistrationTimeConfig</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingSourceRegistrationTimeConfig.INCLUDE"><code class="docutils literal notranslate"><span class="pre">AttributionReportingSourceRegistrationTimeConfig.INCLUDE</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingSourceRegistrationTimeConfig.EXCLUDE"><code class="docutils literal notranslate"><span class="pre">AttributionReportingSourceRegistrationTimeConfig.EXCLUDE</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingAggregatableValueDictEntry"><code class="docutils literal notranslate"><span class="pre">AttributionReportingAggregatableValueDictEntry</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingAggregatableValueDictEntry.key"><code class="docutils literal notranslate"><span class="pre">AttributionReportingAggregatableValueDictEntry.key</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingAggregatableValueDictEntry.value"><code class="docutils literal notranslate"><span class="pre">AttributionReportingAggregatableValueDictEntry.value</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingAggregatableValueDictEntry.filtering_id"><code class="docutils literal notranslate"><span class="pre">AttributionReportingAggregatableValueDictEntry.filtering_id</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingAggregatableValueEntry"><code class="docutils literal notranslate"><span class="pre">AttributionReportingAggregatableValueEntry</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingAggregatableValueEntry.values"><code class="docutils literal notranslate"><span class="pre">AttributionReportingAggregatableValueEntry.values</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingAggregatableValueEntry.filters"><code class="docutils literal notranslate"><span class="pre">AttributionReportingAggregatableValueEntry.filters</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingEventTriggerData"><code class="docutils literal notranslate"><span class="pre">AttributionReportingEventTriggerData</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingEventTriggerData.data"><code class="docutils literal notranslate"><span class="pre">AttributionReportingEventTriggerData.data</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingEventTriggerData.priority"><code class="docutils literal notranslate"><span class="pre">AttributionReportingEventTriggerData.priority</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingEventTriggerData.filters"><code class="docutils literal notranslate"><span class="pre">AttributionReportingEventTriggerData.filters</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingEventTriggerData.dedup_key"><code class="docutils literal notranslate"><span class="pre">AttributionReportingEventTriggerData.dedup_key</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingAggregatableTriggerData"><code class="docutils literal notranslate"><span class="pre">AttributionReportingAggregatableTriggerData</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingAggregatableTriggerData.key_piece"><code class="docutils literal notranslate"><span class="pre">AttributionReportingAggregatableTriggerData.key_piece</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingAggregatableTriggerData.source_keys"><code class="docutils literal notranslate"><span class="pre">AttributionReportingAggregatableTriggerData.source_keys</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingAggregatableTriggerData.filters"><code class="docutils literal notranslate"><span class="pre">AttributionReportingAggregatableTriggerData.filters</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingAggregatableDedupKey"><code class="docutils literal notranslate"><span class="pre">AttributionReportingAggregatableDedupKey</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingAggregatableDedupKey.filters"><code class="docutils literal notranslate"><span class="pre">AttributionReportingAggregatableDedupKey.filters</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingAggregatableDedupKey.dedup_key"><code class="docutils literal notranslate"><span class="pre">AttributionReportingAggregatableDedupKey.dedup_key</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingTriggerRegistration"><code class="docutils literal notranslate"><span class="pre">AttributionReportingTriggerRegistration</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingTriggerRegistration.filters"><code class="docutils literal notranslate"><span class="pre">AttributionReportingTriggerRegistration.filters</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingTriggerRegistration.aggregatable_dedup_keys"><code class="docutils literal notranslate"><span class="pre">AttributionReportingTriggerRegistration.aggregatable_dedup_keys</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingTriggerRegistration.event_trigger_data"><code class="docutils literal notranslate"><span class="pre">AttributionReportingTriggerRegistration.event_trigger_data</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingTriggerRegistration.aggregatable_trigger_data"><code class="docutils literal notranslate"><span class="pre">AttributionReportingTriggerRegistration.aggregatable_trigger_data</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingTriggerRegistration.aggregatable_values"><code class="docutils literal notranslate"><span class="pre">AttributionReportingTriggerRegistration.aggregatable_values</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingTriggerRegistration.aggregatable_filtering_id_max_bytes"><code class="docutils literal notranslate"><span class="pre">AttributionReportingTriggerRegistration.aggregatable_filtering_id_max_bytes</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingTriggerRegistration.debug_reporting"><code class="docutils literal notranslate"><span class="pre">AttributionReportingTriggerRegistration.debug_reporting</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingTriggerRegistration.source_registration_time_config"><code class="docutils literal notranslate"><span class="pre">AttributionReportingTriggerRegistration.source_registration_time_config</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingTriggerRegistration.aggregatable_debug_reporting_config"><code class="docutils literal notranslate"><span class="pre">AttributionReportingTriggerRegistration.aggregatable_debug_reporting_config</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingTriggerRegistration.scopes"><code class="docutils literal notranslate"><span class="pre">AttributionReportingTriggerRegistration.scopes</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingTriggerRegistration.debug_key"><code class="docutils literal notranslate"><span class="pre">AttributionReportingTriggerRegistration.debug_key</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingTriggerRegistration.aggregation_coordinator_origin"><code class="docutils literal notranslate"><span class="pre">AttributionReportingTriggerRegistration.aggregation_coordinator_origin</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingTriggerRegistration.trigger_context_id"><code class="docutils literal notranslate"><span class="pre">AttributionReportingTriggerRegistration.trigger_context_id</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingEventLevelResult"><code class="docutils literal notranslate"><span class="pre">AttributionReportingEventLevelResult</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingEventLevelResult.SUCCESS"><code class="docutils literal notranslate"><span class="pre">AttributionReportingEventLevelResult.SUCCESS</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingEventLevelResult.SUCCESS_DROPPED_LOWER_PRIORITY"><code class="docutils literal notranslate"><span class="pre">AttributionReportingEventLevelResult.SUCCESS_DROPPED_LOWER_PRIORITY</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingEventLevelResult.INTERNAL_ERROR"><code class="docutils literal notranslate"><span class="pre">AttributionReportingEventLevelResult.INTERNAL_ERROR</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingEventLevelResult.NO_CAPACITY_FOR_ATTRIBUTION_DESTINATION"><code class="docutils literal notranslate"><span class="pre">AttributionReportingEventLevelResult.NO_CAPACITY_FOR_ATTRIBUTION_DESTINATION</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingEventLevelResult.NO_MATCHING_SOURCES"><code class="docutils literal notranslate"><span class="pre">AttributionReportingEventLevelResult.NO_MATCHING_SOURCES</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingEventLevelResult.DEDUPLICATED"><code class="docutils literal notranslate"><span class="pre">AttributionReportingEventLevelResult.DEDUPLICATED</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingEventLevelResult.EXCESSIVE_ATTRIBUTIONS"><code class="docutils literal notranslate"><span class="pre">AttributionReportingEventLevelResult.EXCESSIVE_ATTRIBUTIONS</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingEventLevelResult.PRIORITY_TOO_LOW"><code class="docutils literal notranslate"><span class="pre">AttributionReportingEventLevelResult.PRIORITY_TOO_LOW</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingEventLevelResult.NEVER_ATTRIBUTED_SOURCE"><code class="docutils literal notranslate"><span class="pre">AttributionReportingEventLevelResult.NEVER_ATTRIBUTED_SOURCE</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingEventLevelResult.EXCESSIVE_REPORTING_ORIGINS"><code class="docutils literal notranslate"><span class="pre">AttributionReportingEventLevelResult.EXCESSIVE_REPORTING_ORIGINS</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingEventLevelResult.NO_MATCHING_SOURCE_FILTER_DATA"><code class="docutils literal notranslate"><span class="pre">AttributionReportingEventLevelResult.NO_MATCHING_SOURCE_FILTER_DATA</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingEventLevelResult.PROHIBITED_BY_BROWSER_POLICY"><code class="docutils literal notranslate"><span class="pre">AttributionReportingEventLevelResult.PROHIBITED_BY_BROWSER_POLICY</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingEventLevelResult.NO_MATCHING_CONFIGURATIONS"><code class="docutils literal notranslate"><span class="pre">AttributionReportingEventLevelResult.NO_MATCHING_CONFIGURATIONS</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingEventLevelResult.EXCESSIVE_REPORTS"><code class="docutils literal notranslate"><span class="pre">AttributionReportingEventLevelResult.EXCESSIVE_REPORTS</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingEventLevelResult.FALSELY_ATTRIBUTED_SOURCE"><code class="docutils literal notranslate"><span class="pre">AttributionReportingEventLevelResult.FALSELY_ATTRIBUTED_SOURCE</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingEventLevelResult.REPORT_WINDOW_PASSED"><code class="docutils literal notranslate"><span class="pre">AttributionReportingEventLevelResult.REPORT_WINDOW_PASSED</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingEventLevelResult.NOT_REGISTERED"><code class="docutils literal notranslate"><span class="pre">AttributionReportingEventLevelResult.NOT_REGISTERED</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingEventLevelResult.REPORT_WINDOW_NOT_STARTED"><code class="docutils literal notranslate"><span class="pre">AttributionReportingEventLevelResult.REPORT_WINDOW_NOT_STARTED</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingEventLevelResult.NO_MATCHING_TRIGGER_DATA"><code class="docutils literal notranslate"><span class="pre">AttributionReportingEventLevelResult.NO_MATCHING_TRIGGER_DATA</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingAggregatableResult"><code class="docutils literal notranslate"><span class="pre">AttributionReportingAggregatableResult</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingAggregatableResult.SUCCESS"><code class="docutils literal notranslate"><span class="pre">AttributionReportingAggregatableResult.SUCCESS</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingAggregatableResult.INTERNAL_ERROR"><code class="docutils literal notranslate"><span class="pre">AttributionReportingAggregatableResult.INTERNAL_ERROR</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingAggregatableResult.NO_CAPACITY_FOR_ATTRIBUTION_DESTINATION"><code class="docutils literal notranslate"><span class="pre">AttributionReportingAggregatableResult.NO_CAPACITY_FOR_ATTRIBUTION_DESTINATION</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingAggregatableResult.NO_MATCHING_SOURCES"><code class="docutils literal notranslate"><span class="pre">AttributionReportingAggregatableResult.NO_MATCHING_SOURCES</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingAggregatableResult.EXCESSIVE_ATTRIBUTIONS"><code class="docutils literal notranslate"><span class="pre">AttributionReportingAggregatableResult.EXCESSIVE_ATTRIBUTIONS</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingAggregatableResult.EXCESSIVE_REPORTING_ORIGINS"><code class="docutils literal notranslate"><span class="pre">AttributionReportingAggregatableResult.EXCESSIVE_REPORTING_ORIGINS</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingAggregatableResult.NO_HISTOGRAMS"><code class="docutils literal notranslate"><span class="pre">AttributionReportingAggregatableResult.NO_HISTOGRAMS</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingAggregatableResult.INSUFFICIENT_BUDGET"><code class="docutils literal notranslate"><span class="pre">AttributionReportingAggregatableResult.INSUFFICIENT_BUDGET</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingAggregatableResult.INSUFFICIENT_NAMED_BUDGET"><code class="docutils literal notranslate"><span class="pre">AttributionReportingAggregatableResult.INSUFFICIENT_NAMED_BUDGET</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingAggregatableResult.NO_MATCHING_SOURCE_FILTER_DATA"><code class="docutils literal notranslate"><span class="pre">AttributionReportingAggregatableResult.NO_MATCHING_SOURCE_FILTER_DATA</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingAggregatableResult.NOT_REGISTERED"><code class="docutils literal notranslate"><span class="pre">AttributionReportingAggregatableResult.NOT_REGISTERED</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingAggregatableResult.PROHIBITED_BY_BROWSER_POLICY"><code class="docutils literal notranslate"><span class="pre">AttributionReportingAggregatableResult.PROHIBITED_BY_BROWSER_POLICY</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingAggregatableResult.DEDUPLICATED"><code class="docutils literal notranslate"><span class="pre">AttributionReportingAggregatableResult.DEDUPLICATED</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingAggregatableResult.REPORT_WINDOW_PASSED"><code class="docutils literal notranslate"><span class="pre">AttributionReportingAggregatableResult.REPORT_WINDOW_PASSED</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingAggregatableResult.EXCESSIVE_REPORTS"><code class="docutils literal notranslate"><span class="pre">AttributionReportingAggregatableResult.EXCESSIVE_REPORTS</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.storage.RelatedWebsiteSet"><code class="docutils literal notranslate"><span class="pre">RelatedWebsiteSet</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.storage.RelatedWebsiteSet.primary_sites"><code class="docutils literal notranslate"><span class="pre">RelatedWebsiteSet.primary_sites</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.RelatedWebsiteSet.associated_sites"><code class="docutils literal notranslate"><span class="pre">RelatedWebsiteSet.associated_sites</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.RelatedWebsiteSet.service_sites"><code class="docutils literal notranslate"><span class="pre">RelatedWebsiteSet.service_sites</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#commands">Commands</a><ul>
<li><a class="reference internal" href="#nodriver.cdp.storage.clear_cookies"><code class="docutils literal notranslate"><span class="pre">clear_cookies()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.clear_data_for_origin"><code class="docutils literal notranslate"><span class="pre">clear_data_for_origin()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.clear_data_for_storage_key"><code class="docutils literal notranslate"><span class="pre">clear_data_for_storage_key()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.clear_shared_storage_entries"><code class="docutils literal notranslate"><span class="pre">clear_shared_storage_entries()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.clear_trust_tokens"><code class="docutils literal notranslate"><span class="pre">clear_trust_tokens()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.delete_shared_storage_entry"><code class="docutils literal notranslate"><span class="pre">delete_shared_storage_entry()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.delete_storage_bucket"><code class="docutils literal notranslate"><span class="pre">delete_storage_bucket()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.get_affected_urls_for_third_party_cookie_metadata"><code class="docutils literal notranslate"><span class="pre">get_affected_urls_for_third_party_cookie_metadata()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.get_cookies"><code class="docutils literal notranslate"><span class="pre">get_cookies()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.get_interest_group_details"><code class="docutils literal notranslate"><span class="pre">get_interest_group_details()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.get_related_website_sets"><code class="docutils literal notranslate"><span class="pre">get_related_website_sets()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.get_shared_storage_entries"><code class="docutils literal notranslate"><span class="pre">get_shared_storage_entries()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.get_shared_storage_metadata"><code class="docutils literal notranslate"><span class="pre">get_shared_storage_metadata()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.get_storage_key_for_frame"><code class="docutils literal notranslate"><span class="pre">get_storage_key_for_frame()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.get_trust_tokens"><code class="docutils literal notranslate"><span class="pre">get_trust_tokens()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.get_usage_and_quota"><code class="docutils literal notranslate"><span class="pre">get_usage_and_quota()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.override_quota_for_origin"><code class="docutils literal notranslate"><span class="pre">override_quota_for_origin()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.reset_shared_storage_budget"><code class="docutils literal notranslate"><span class="pre">reset_shared_storage_budget()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.run_bounce_tracking_mitigations"><code class="docutils literal notranslate"><span class="pre">run_bounce_tracking_mitigations()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.send_pending_attribution_reports"><code class="docutils literal notranslate"><span class="pre">send_pending_attribution_reports()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.set_attribution_reporting_local_testing_mode"><code class="docutils literal notranslate"><span class="pre">set_attribution_reporting_local_testing_mode()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.set_attribution_reporting_tracking"><code class="docutils literal notranslate"><span class="pre">set_attribution_reporting_tracking()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.set_cookies"><code class="docutils literal notranslate"><span class="pre">set_cookies()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.set_interest_group_auction_tracking"><code class="docutils literal notranslate"><span class="pre">set_interest_group_auction_tracking()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.set_interest_group_tracking"><code class="docutils literal notranslate"><span class="pre">set_interest_group_tracking()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.set_shared_storage_entry"><code class="docutils literal notranslate"><span class="pre">set_shared_storage_entry()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.set_shared_storage_tracking"><code class="docutils literal notranslate"><span class="pre">set_shared_storage_tracking()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.set_storage_bucket_tracking"><code class="docutils literal notranslate"><span class="pre">set_storage_bucket_tracking()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.track_cache_storage_for_origin"><code class="docutils literal notranslate"><span class="pre">track_cache_storage_for_origin()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.track_cache_storage_for_storage_key"><code class="docutils literal notranslate"><span class="pre">track_cache_storage_for_storage_key()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.track_indexed_db_for_origin"><code class="docutils literal notranslate"><span class="pre">track_indexed_db_for_origin()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.track_indexed_db_for_storage_key"><code class="docutils literal notranslate"><span class="pre">track_indexed_db_for_storage_key()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.untrack_cache_storage_for_origin"><code class="docutils literal notranslate"><span class="pre">untrack_cache_storage_for_origin()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.untrack_cache_storage_for_storage_key"><code class="docutils literal notranslate"><span class="pre">untrack_cache_storage_for_storage_key()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.untrack_indexed_db_for_origin"><code class="docutils literal notranslate"><span class="pre">untrack_indexed_db_for_origin()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.untrack_indexed_db_for_storage_key"><code class="docutils literal notranslate"><span class="pre">untrack_indexed_db_for_storage_key()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#events">Events</a><ul>
<li><a class="reference internal" href="#nodriver.cdp.storage.CacheStorageContentUpdated"><code class="docutils literal notranslate"><span class="pre">CacheStorageContentUpdated</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.storage.CacheStorageContentUpdated.origin"><code class="docutils literal notranslate"><span class="pre">CacheStorageContentUpdated.origin</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.CacheStorageContentUpdated.storage_key"><code class="docutils literal notranslate"><span class="pre">CacheStorageContentUpdated.storage_key</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.CacheStorageContentUpdated.bucket_id"><code class="docutils literal notranslate"><span class="pre">CacheStorageContentUpdated.bucket_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.CacheStorageContentUpdated.cache_name"><code class="docutils literal notranslate"><span class="pre">CacheStorageContentUpdated.cache_name</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.storage.CacheStorageListUpdated"><code class="docutils literal notranslate"><span class="pre">CacheStorageListUpdated</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.storage.CacheStorageListUpdated.origin"><code class="docutils literal notranslate"><span class="pre">CacheStorageListUpdated.origin</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.CacheStorageListUpdated.storage_key"><code class="docutils literal notranslate"><span class="pre">CacheStorageListUpdated.storage_key</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.CacheStorageListUpdated.bucket_id"><code class="docutils literal notranslate"><span class="pre">CacheStorageListUpdated.bucket_id</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.storage.IndexedDBContentUpdated"><code class="docutils literal notranslate"><span class="pre">IndexedDBContentUpdated</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.storage.IndexedDBContentUpdated.origin"><code class="docutils literal notranslate"><span class="pre">IndexedDBContentUpdated.origin</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.IndexedDBContentUpdated.storage_key"><code class="docutils literal notranslate"><span class="pre">IndexedDBContentUpdated.storage_key</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.IndexedDBContentUpdated.bucket_id"><code class="docutils literal notranslate"><span class="pre">IndexedDBContentUpdated.bucket_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.IndexedDBContentUpdated.database_name"><code class="docutils literal notranslate"><span class="pre">IndexedDBContentUpdated.database_name</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.IndexedDBContentUpdated.object_store_name"><code class="docutils literal notranslate"><span class="pre">IndexedDBContentUpdated.object_store_name</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.storage.IndexedDBListUpdated"><code class="docutils literal notranslate"><span class="pre">IndexedDBListUpdated</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.storage.IndexedDBListUpdated.origin"><code class="docutils literal notranslate"><span class="pre">IndexedDBListUpdated.origin</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.IndexedDBListUpdated.storage_key"><code class="docutils literal notranslate"><span class="pre">IndexedDBListUpdated.storage_key</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.IndexedDBListUpdated.bucket_id"><code class="docutils literal notranslate"><span class="pre">IndexedDBListUpdated.bucket_id</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.storage.InterestGroupAccessed"><code class="docutils literal notranslate"><span class="pre">InterestGroupAccessed</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.storage.InterestGroupAccessed.access_time"><code class="docutils literal notranslate"><span class="pre">InterestGroupAccessed.access_time</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.InterestGroupAccessed.type_"><code class="docutils literal notranslate"><span class="pre">InterestGroupAccessed.type_</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.InterestGroupAccessed.owner_origin"><code class="docutils literal notranslate"><span class="pre">InterestGroupAccessed.owner_origin</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.InterestGroupAccessed.name"><code class="docutils literal notranslate"><span class="pre">InterestGroupAccessed.name</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.InterestGroupAccessed.component_seller_origin"><code class="docutils literal notranslate"><span class="pre">InterestGroupAccessed.component_seller_origin</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.InterestGroupAccessed.bid"><code class="docutils literal notranslate"><span class="pre">InterestGroupAccessed.bid</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.InterestGroupAccessed.bid_currency"><code class="docutils literal notranslate"><span class="pre">InterestGroupAccessed.bid_currency</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.InterestGroupAccessed.unique_auction_id"><code class="docutils literal notranslate"><span class="pre">InterestGroupAccessed.unique_auction_id</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.storage.InterestGroupAuctionEventOccurred"><code class="docutils literal notranslate"><span class="pre">InterestGroupAuctionEventOccurred</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.storage.InterestGroupAuctionEventOccurred.event_time"><code class="docutils literal notranslate"><span class="pre">InterestGroupAuctionEventOccurred.event_time</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.InterestGroupAuctionEventOccurred.type_"><code class="docutils literal notranslate"><span class="pre">InterestGroupAuctionEventOccurred.type_</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.InterestGroupAuctionEventOccurred.unique_auction_id"><code class="docutils literal notranslate"><span class="pre">InterestGroupAuctionEventOccurred.unique_auction_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.InterestGroupAuctionEventOccurred.parent_auction_id"><code class="docutils literal notranslate"><span class="pre">InterestGroupAuctionEventOccurred.parent_auction_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.InterestGroupAuctionEventOccurred.auction_config"><code class="docutils literal notranslate"><span class="pre">InterestGroupAuctionEventOccurred.auction_config</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.storage.InterestGroupAuctionNetworkRequestCreated"><code class="docutils literal notranslate"><span class="pre">InterestGroupAuctionNetworkRequestCreated</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.storage.InterestGroupAuctionNetworkRequestCreated.type_"><code class="docutils literal notranslate"><span class="pre">InterestGroupAuctionNetworkRequestCreated.type_</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.InterestGroupAuctionNetworkRequestCreated.request_id"><code class="docutils literal notranslate"><span class="pre">InterestGroupAuctionNetworkRequestCreated.request_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.InterestGroupAuctionNetworkRequestCreated.auctions"><code class="docutils literal notranslate"><span class="pre">InterestGroupAuctionNetworkRequestCreated.auctions</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.storage.SharedStorageAccessed"><code class="docutils literal notranslate"><span class="pre">SharedStorageAccessed</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.storage.SharedStorageAccessed.access_time"><code class="docutils literal notranslate"><span class="pre">SharedStorageAccessed.access_time</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.SharedStorageAccessed.type_"><code class="docutils literal notranslate"><span class="pre">SharedStorageAccessed.type_</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.SharedStorageAccessed.main_frame_id"><code class="docutils literal notranslate"><span class="pre">SharedStorageAccessed.main_frame_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.SharedStorageAccessed.owner_origin"><code class="docutils literal notranslate"><span class="pre">SharedStorageAccessed.owner_origin</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.SharedStorageAccessed.params"><code class="docutils literal notranslate"><span class="pre">SharedStorageAccessed.params</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.storage.StorageBucketCreatedOrUpdated"><code class="docutils literal notranslate"><span class="pre">StorageBucketCreatedOrUpdated</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.storage.StorageBucketCreatedOrUpdated.bucket_info"><code class="docutils literal notranslate"><span class="pre">StorageBucketCreatedOrUpdated.bucket_info</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.storage.StorageBucketDeleted"><code class="docutils literal notranslate"><span class="pre">StorageBucketDeleted</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.storage.StorageBucketDeleted.bucket_id"><code class="docutils literal notranslate"><span class="pre">StorageBucketDeleted.bucket_id</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingSourceRegistered"><code class="docutils literal notranslate"><span class="pre">AttributionReportingSourceRegistered</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingSourceRegistered.registration"><code class="docutils literal notranslate"><span class="pre">AttributionReportingSourceRegistered.registration</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingSourceRegistered.result"><code class="docutils literal notranslate"><span class="pre">AttributionReportingSourceRegistered.result</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingTriggerRegistered"><code class="docutils literal notranslate"><span class="pre">AttributionReportingTriggerRegistered</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingTriggerRegistered.registration"><code class="docutils literal notranslate"><span class="pre">AttributionReportingTriggerRegistered.registration</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingTriggerRegistered.event_level"><code class="docutils literal notranslate"><span class="pre">AttributionReportingTriggerRegistered.event_level</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.storage.AttributionReportingTriggerRegistered.aggregatable"><code class="docutils literal notranslate"><span class="pre">AttributionReportingTriggerRegistered.aggregatable</span></code></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>

          </div>
        </div>
      </div>
      
      
    </aside>
  </div>
</div><script src="../../_static/documentation_options.js?v=5929fcd5"></script>
    <script src="../../_static/doctools.js?v=888ff710"></script>
    <script src="../../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../../_static/scripts/furo.js?v=32e29ea5"></script>
    </body>
</html>