<!doctype html>
<html class="no-js" lang="en" data-content_root="../../../">
  <head><meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width,initial-scale=1"/>
    <meta name="color-scheme" content="light dark"><link rel="index" title="Index" href="../../../genindex.html" /><link rel="search" title="Search" href="../../../search.html" />

    <!-- Generated with Sphinx 7.2.6 and Furo 2023.09.10 -->
        <title>nodriver.cdp.preload - nodriver documentation</title>
      <link rel="stylesheet" type="text/css" href="../../../_static/pygments.css?v=fa44fd50" />
    <link rel="stylesheet" type="text/css" href="../../../_static/styles/furo.css?v=135e06be" />
    <link rel="stylesheet" type="text/css" href="../../../_static/styles/furo-extensions.css?v=36a5483c" />
    <link rel="stylesheet" type="text/css" href="../../../_static/./custom.css?v=3934642d" />
    
    


<style>
  body {
    --color-code-background: #eeffcc;
  --color-code-foreground: black;
  
  }
  @media not print {
    body[data-theme="dark"] {
      --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8eefba;
  --color-background-primary: #111112;
  --color-problematic: #e7aeae;
  
    }
    @media (prefers-color-scheme: dark) {
      body:not([data-theme="light"]) {
        --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8eefba;
  --color-background-primary: #111112;
  --color-problematic: #e7aeae;
  
      }
    }
  }
</style></head>
  <body>
    
    <script>
      document.body.dataset.theme = localStorage.getItem("theme") || "auto";
    </script>
    

<svg xmlns="http://www.w3.org/2000/svg" style="display: none;">
  <symbol id="svg-toc" viewBox="0 0 24 24">
    <title>Contents</title>
    <svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 1024 1024">
      <path d="M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM115.4 518.9L271.7 642c5.8 4.6 14.4.5 14.4-6.9V388.9c0-7.4-8.5-11.5-14.4-6.9L115.4 505.1a8.74 8.74 0 0 0 0 13.8z"/>
    </svg>
  </symbol>
  <symbol id="svg-menu" viewBox="0 0 24 24">
    <title>Menu</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-menu">
      <line x1="3" y1="12" x2="21" y2="12"></line>
      <line x1="3" y1="6" x2="21" y2="6"></line>
      <line x1="3" y1="18" x2="21" y2="18"></line>
    </svg>
  </symbol>
  <symbol id="svg-arrow-right" viewBox="0 0 24 24">
    <title>Expand</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-chevron-right">
      <polyline points="9 18 15 12 9 6"></polyline>
    </svg>
  </symbol>
  <symbol id="svg-sun" viewBox="0 0 24 24">
    <title>Light mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="feather-sun">
      <circle cx="12" cy="12" r="5"></circle>
      <line x1="12" y1="1" x2="12" y2="3"></line>
      <line x1="12" y1="21" x2="12" y2="23"></line>
      <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
      <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
      <line x1="1" y1="12" x2="3" y2="12"></line>
      <line x1="21" y1="12" x2="23" y2="12"></line>
      <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
      <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
    </svg>
  </symbol>
  <symbol id="svg-moon" viewBox="0 0 24 24">
    <title>Dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-moon">
      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
      <path d="M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z" />
    </svg>
  </symbol>
  <symbol id="svg-sun-half" viewBox="0 0 24 24">
    <title>Auto light/dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-shadow">
      <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
      <circle cx="12" cy="12" r="9" />
      <path d="M13 12h5" />
      <path d="M13 15h4" />
      <path d="M13 18h1" />
      <path d="M13 9h4" />
      <path d="M13 6h1" />
    </svg>
  </symbol>
</svg>

<input type="checkbox" class="sidebar-toggle" name="__navigation" id="__navigation">
<input type="checkbox" class="sidebar-toggle" name="__toc" id="__toc">
<label class="overlay sidebar-overlay" for="__navigation">
  <div class="visually-hidden">Hide navigation sidebar</div>
</label>
<label class="overlay toc-overlay" for="__toc">
  <div class="visually-hidden">Hide table of contents sidebar</div>
</label>



<div class="page">
  <header class="mobile-header">
    <div class="header-left">
      <label class="nav-overlay-icon" for="__navigation">
        <div class="visually-hidden">Toggle site navigation sidebar</div>
        <i class="icon"><svg><use href="#svg-menu"></use></svg></i>
      </label>
    </div>
    <div class="header-center">
      <a href="../../../index.html"><div class="brand">nodriver  documentation</div></a>
    </div>
    <div class="header-right">
      <div class="theme-toggle-container theme-toggle-header">
        <button class="theme-toggle">
          <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
          <svg class="theme-icon-when-auto"><use href="#svg-sun-half"></use></svg>
          <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
          <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
        </button>
      </div>
      <label class="toc-overlay-icon toc-header-icon no-toc" for="__toc">
        <div class="visually-hidden">Toggle table of contents sidebar</div>
        <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
      </label>
    </div>
  </header>
  <aside class="sidebar-drawer">
    <div class="sidebar-container">
      
      <div class="sidebar-sticky"><div class="sidebar-scroll"><a class="sidebar-brand" href="../../../index.html">
  
  
  <span class="sidebar-brand-text">nodriver  documentation</span>
  
</a><form class="sidebar-search-container" method="get" action="../../../search.html" role="search">
  <input class="sidebar-search" placeholder="Search" name="q" aria-label="Search">
  <input type="hidden" name="check_keywords" value="yes">
  <input type="hidden" name="area" value="default">
</form>
<div id="searchbox"></div><div class="sidebar-tree">
  <ul>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html">Quickstart guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#usage-example">usage example</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#more-complete-example">More complete example</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#custom-starting-options">Custom starting options</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#alternative-custom-options">Alternative custom options</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#proxies-socks5-authenticated-too">Proxies (socks5, authenticated too)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#concrete-example">Concrete example</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/classes/browser.html">Browser class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/classes/tab.html">Tab class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/classes/element.html">Element class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/classes/others_and_helpers.html">Other classes and Helper classes</a></li>
</ul>
<ul>
<li class="toctree-l1 has-children"><a class="reference internal" href="../../../nodriver/cdp.html">CDP object</a><input class="toctree-checkbox" id="toctree-checkbox-1" name="toctree-checkbox-1" role="switch" type="checkbox"/><label for="toctree-checkbox-1"><div class="visually-hidden">Toggle navigation of CDP object</div><i class="icon"><svg><use href="#svg-arrow-right"></use></svg></i></label><ul>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/accessibility.html">Accessibility</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/animation.html">Animation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/audits.html">Audits</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/autofill.html">Autofill</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/background_service.html">BackgroundService</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/bluetooth_emulation.html">BluetoothEmulation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/browser.html">Browser</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/cache_storage.html">CacheStorage</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/cast.html">Cast</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/console.html">Console</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/css.html">CSS</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/debugger.html">Debugger</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/device_access.html">DeviceAccess</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/device_orientation.html">DeviceOrientation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/dom.html">DOM</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/dom_debugger.html">DOMDebugger</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/dom_snapshot.html">DOMSnapshot</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/dom_storage.html">DOMStorage</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/emulation.html">Emulation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/event_breakpoints.html">EventBreakpoints</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/extensions.html">Extensions</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/fed_cm.html">FedCm</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/fetch.html">Fetch</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/file_system.html">FileSystem</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/headless_experimental.html">HeadlessExperimental</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/heap_profiler.html">HeapProfiler</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/indexed_db.html">IndexedDB</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/input_.html">Input</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/inspector.html">Inspector</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/io.html">IO</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/layer_tree.html">LayerTree</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/log.html">Log</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/media.html">Media</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/memory.html">Memory</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/network.html">Network</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/overlay.html">Overlay</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/page.html">Page</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/performance.html">Performance</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/performance_timeline.html">PerformanceTimeline</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/preload.html">Preload</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/profiler.html">Profiler</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/pwa.html">PWA</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/runtime.html">Runtime</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/schema.html">Schema</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/security.html">Security</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/service_worker.html">ServiceWorker</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/storage.html">Storage</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/system_info.html">SystemInfo</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/target.html">Target</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/tethering.html">Tethering</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/tracing.html">Tracing</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/web_audio.html">WebAudio</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/web_authn.html">WebAuthn</a></li>
</ul>
</li>
</ul>

</div>
</div>
      </div>
      
    </div>
  </aside>
  <div class="main">
    <div class="content">
      <div class="article-container">
        <a href="#" class="back-to-top muted-link">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
            <path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8v12z"></path>
          </svg>
          <span>Back to top</span>
        </a>
        <div class="content-icon-container">
          <div class="theme-toggle-container theme-toggle-content">
            <button class="theme-toggle">
              <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
              <svg class="theme-icon-when-auto"><use href="#svg-sun-half"></use></svg>
              <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
              <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
            </button>
          </div>
          <label class="toc-overlay-icon toc-content-icon no-toc" for="__toc">
            <div class="visually-hidden">Toggle table of contents sidebar</div>
            <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
          </label>
        </div>
        <article role="main">
          <h1>Source code for nodriver.cdp.preload</h1><div class="highlight"><pre>
<span></span><span class="c1"># DO NOT EDIT THIS FILE!</span>
<span class="c1">#</span>
<span class="c1"># This file is generated from the CDP specification. If you need to make</span>
<span class="c1"># changes, edit the generator and regenerate all of the modules.</span>
<span class="c1">#</span>
<span class="c1"># CDP domain: Preload (experimental)</span>

<span class="kn">from</span> <span class="nn">__future__</span> <span class="kn">import</span> <span class="n">annotations</span>

<span class="kn">import</span> <span class="nn">enum</span>
<span class="kn">import</span> <span class="nn">typing</span>
<span class="kn">from</span> <span class="nn">dataclasses</span> <span class="kn">import</span> <span class="n">dataclass</span>

<span class="kn">from</span> <span class="nn">.</span> <span class="kn">import</span> <span class="n">dom</span><span class="p">,</span> <span class="n">network</span><span class="p">,</span> <span class="n">page</span>
<span class="kn">from</span> <span class="nn">.util</span> <span class="kn">import</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">event_class</span>


<div class="viewcode-block" id="RuleSetId">
<a class="viewcode-back" href="../../../nodriver/cdp/preload.html#nodriver.cdp.preload.RuleSetId">[docs]</a>
<span class="k">class</span> <span class="nc">RuleSetId</span><span class="p">(</span><span class="nb">str</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Unique id</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">RuleSetId</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span>

    <span class="k">def</span> <span class="fm">__repr__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="s2">&quot;RuleSetId(</span><span class="si">{}</span><span class="s2">)&quot;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="fm">__repr__</span><span class="p">())</span></div>



<div class="viewcode-block" id="RuleSet">
<a class="viewcode-back" href="../../../nodriver/cdp/preload.html#nodriver.cdp.preload.RuleSet">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">RuleSet</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Corresponds to SpeculationRuleSet</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">id_</span><span class="p">:</span> <span class="n">RuleSetId</span>

    <span class="c1">#: Identifies a document which the rule set is associated with.</span>
    <span class="n">loader_id</span><span class="p">:</span> <span class="n">network</span><span class="o">.</span><span class="n">LoaderId</span>

    <span class="c1">#: Source text of JSON representing the rule set. If it comes from</span>
    <span class="c1">#: ``&lt;script&gt;`` tag, it is the textContent of the node. Note that it is</span>
    <span class="c1">#: a JSON for valid case.</span>
    <span class="c1">#:</span>
    <span class="c1">#: See also:</span>
    <span class="c1">#: - https://wicg.github.io/nav-speculation/speculation-rules.html</span>
    <span class="c1">#: - https://github.com/WICG/nav-speculation/blob/main/triggers.md</span>
    <span class="n">source_text</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: A speculation rule set is either added through an inline</span>
    <span class="c1">#: ``&lt;script&gt;`` tag or through an external resource via the</span>
    <span class="c1">#: &#39;Speculation-Rules&#39; HTTP header. For the first case, we include</span>
    <span class="c1">#: the BackendNodeId of the relevant ``&lt;script&gt;`` tag. For the second</span>
    <span class="c1">#: case, we include the external URL where the rule set was loaded</span>
    <span class="c1">#: from, and also RequestId if Network domain is enabled.</span>
    <span class="c1">#:</span>
    <span class="c1">#: See also:</span>
    <span class="c1">#: - https://wicg.github.io/nav-speculation/speculation-rules.html#speculation-rules-script</span>
    <span class="c1">#: - https://wicg.github.io/nav-speculation/speculation-rules.html#speculation-rules-header</span>
    <span class="n">backend_node_id</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">dom</span><span class="o">.</span><span class="n">BackendNodeId</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="n">url</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="n">request_id</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">network</span><span class="o">.</span><span class="n">RequestId</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: Error information</span>
    <span class="c1">#: ``errorMessage`` is null iff ``errorType`` is null.</span>
    <span class="n">error_type</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">RuleSetErrorType</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: TODO(https://crbug.com/1425354): Replace this property with structured error.</span>
    <span class="n">error_message</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;id&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">id_</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;loaderId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">loader_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;sourceText&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">source_text</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">backend_node_id</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;backendNodeId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">backend_node_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">url</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;url&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">url</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">request_id</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;requestId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">request_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">error_type</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;errorType&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">error_type</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">error_message</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;errorMessage&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">error_message</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">RuleSet</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">id_</span><span class="o">=</span><span class="n">RuleSetId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;id&quot;</span><span class="p">]),</span>
            <span class="n">loader_id</span><span class="o">=</span><span class="n">network</span><span class="o">.</span><span class="n">LoaderId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;loaderId&quot;</span><span class="p">]),</span>
            <span class="n">source_text</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;sourceText&quot;</span><span class="p">]),</span>
            <span class="n">backend_node_id</span><span class="o">=</span><span class="p">(</span>
                <span class="n">dom</span><span class="o">.</span><span class="n">BackendNodeId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;backendNodeId&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;backendNodeId&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">url</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;url&quot;</span><span class="p">])</span> <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;url&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span> <span class="k">else</span> <span class="kc">None</span><span class="p">,</span>
            <span class="n">request_id</span><span class="o">=</span><span class="p">(</span>
                <span class="n">network</span><span class="o">.</span><span class="n">RequestId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;requestId&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;requestId&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">error_type</span><span class="o">=</span><span class="p">(</span>
                <span class="n">RuleSetErrorType</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;errorType&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;errorType&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">error_message</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;errorMessage&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;errorMessage&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="RuleSetErrorType">
<a class="viewcode-back" href="../../../nodriver/cdp/preload.html#nodriver.cdp.preload.RuleSetErrorType">[docs]</a>
<span class="k">class</span> <span class="nc">RuleSetErrorType</span><span class="p">(</span><span class="n">enum</span><span class="o">.</span><span class="n">Enum</span><span class="p">):</span>
    <span class="n">SOURCE_IS_NOT_JSON_OBJECT</span> <span class="o">=</span> <span class="s2">&quot;SourceIsNotJsonObject&quot;</span>
    <span class="n">INVALID_RULES_SKIPPED</span> <span class="o">=</span> <span class="s2">&quot;InvalidRulesSkipped&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">RuleSetErrorType</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span></div>



<div class="viewcode-block" id="SpeculationAction">
<a class="viewcode-back" href="../../../nodriver/cdp/preload.html#nodriver.cdp.preload.SpeculationAction">[docs]</a>
<span class="k">class</span> <span class="nc">SpeculationAction</span><span class="p">(</span><span class="n">enum</span><span class="o">.</span><span class="n">Enum</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    The type of preloading attempted. It corresponds to</span>
<span class="sd">    mojom::SpeculationAction (although PrefetchWithSubresources is omitted as it</span>
<span class="sd">    isn&#39;t being used by clients).</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">PREFETCH</span> <span class="o">=</span> <span class="s2">&quot;Prefetch&quot;</span>
    <span class="n">PRERENDER</span> <span class="o">=</span> <span class="s2">&quot;Prerender&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">SpeculationAction</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span></div>



<div class="viewcode-block" id="SpeculationTargetHint">
<a class="viewcode-back" href="../../../nodriver/cdp/preload.html#nodriver.cdp.preload.SpeculationTargetHint">[docs]</a>
<span class="k">class</span> <span class="nc">SpeculationTargetHint</span><span class="p">(</span><span class="n">enum</span><span class="o">.</span><span class="n">Enum</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Corresponds to mojom::SpeculationTargetHint.</span>
<span class="sd">    See https://github.com/WICG/nav-speculation/blob/main/triggers.md#window-name-targeting-hints</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">BLANK</span> <span class="o">=</span> <span class="s2">&quot;Blank&quot;</span>
    <span class="n">SELF</span> <span class="o">=</span> <span class="s2">&quot;Self&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">SpeculationTargetHint</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span></div>



<div class="viewcode-block" id="PreloadingAttemptKey">
<a class="viewcode-back" href="../../../nodriver/cdp/preload.html#nodriver.cdp.preload.PreloadingAttemptKey">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">PreloadingAttemptKey</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    A key that identifies a preloading attempt.</span>

<span class="sd">    The url used is the url specified by the trigger (i.e. the initial URL), and</span>
<span class="sd">    not the final url that is navigated to. For example, prerendering allows</span>
<span class="sd">    same-origin main frame navigations during the attempt, but the attempt is</span>
<span class="sd">    still keyed with the initial URL.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">loader_id</span><span class="p">:</span> <span class="n">network</span><span class="o">.</span><span class="n">LoaderId</span>

    <span class="n">action</span><span class="p">:</span> <span class="n">SpeculationAction</span>

    <span class="n">url</span><span class="p">:</span> <span class="nb">str</span>

    <span class="n">target_hint</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">SpeculationTargetHint</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;loaderId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">loader_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;action&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">action</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;url&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">url</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">target_hint</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;targetHint&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">target_hint</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">PreloadingAttemptKey</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">loader_id</span><span class="o">=</span><span class="n">network</span><span class="o">.</span><span class="n">LoaderId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;loaderId&quot;</span><span class="p">]),</span>
            <span class="n">action</span><span class="o">=</span><span class="n">SpeculationAction</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;action&quot;</span><span class="p">]),</span>
            <span class="n">url</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;url&quot;</span><span class="p">]),</span>
            <span class="n">target_hint</span><span class="o">=</span><span class="p">(</span>
                <span class="n">SpeculationTargetHint</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;targetHint&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;targetHint&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="PreloadingAttemptSource">
<a class="viewcode-back" href="../../../nodriver/cdp/preload.html#nodriver.cdp.preload.PreloadingAttemptSource">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">PreloadingAttemptSource</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Lists sources for a preloading attempt, specifically the ids of rule sets</span>
<span class="sd">    that had a speculation rule that triggered the attempt, and the</span>
<span class="sd">    BackendNodeIds of &lt;a href&gt; or &lt;area href&gt; elements that triggered the</span>
<span class="sd">    attempt (in the case of attempts triggered by a document rule). It is</span>
<span class="sd">    possible for multiple rule sets and links to trigger a single attempt.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">key</span><span class="p">:</span> <span class="n">PreloadingAttemptKey</span>

    <span class="n">rule_set_ids</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">RuleSetId</span><span class="p">]</span>

    <span class="n">node_ids</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">dom</span><span class="o">.</span><span class="n">BackendNodeId</span><span class="p">]</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;key&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">key</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;ruleSetIds&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">rule_set_ids</span><span class="p">]</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;nodeIds&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">node_ids</span><span class="p">]</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">PreloadingAttemptSource</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">key</span><span class="o">=</span><span class="n">PreloadingAttemptKey</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;key&quot;</span><span class="p">]),</span>
            <span class="n">rule_set_ids</span><span class="o">=</span><span class="p">[</span><span class="n">RuleSetId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;ruleSetIds&quot;</span><span class="p">]],</span>
            <span class="n">node_ids</span><span class="o">=</span><span class="p">[</span><span class="n">dom</span><span class="o">.</span><span class="n">BackendNodeId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;nodeIds&quot;</span><span class="p">]],</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="PreloadPipelineId">
<a class="viewcode-back" href="../../../nodriver/cdp/preload.html#nodriver.cdp.preload.PreloadPipelineId">[docs]</a>
<span class="k">class</span> <span class="nc">PreloadPipelineId</span><span class="p">(</span><span class="nb">str</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Chrome manages different types of preloads together using a</span>
<span class="sd">    concept of preloading pipeline. For example, if a site uses a</span>
<span class="sd">    SpeculationRules for prerender, Chrome first starts a prefetch and</span>
<span class="sd">    then upgrades it to prerender.</span>

<span class="sd">    CDP events for them are emitted separately but they share</span>
<span class="sd">    ``PreloadPipelineId``.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">PreloadPipelineId</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span>

    <span class="k">def</span> <span class="fm">__repr__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="s2">&quot;PreloadPipelineId(</span><span class="si">{}</span><span class="s2">)&quot;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="fm">__repr__</span><span class="p">())</span></div>



<div class="viewcode-block" id="PrerenderFinalStatus">
<a class="viewcode-back" href="../../../nodriver/cdp/preload.html#nodriver.cdp.preload.PrerenderFinalStatus">[docs]</a>
<span class="k">class</span> <span class="nc">PrerenderFinalStatus</span><span class="p">(</span><span class="n">enum</span><span class="o">.</span><span class="n">Enum</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    List of FinalStatus reasons for Prerender2.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">ACTIVATED</span> <span class="o">=</span> <span class="s2">&quot;Activated&quot;</span>
    <span class="n">DESTROYED</span> <span class="o">=</span> <span class="s2">&quot;Destroyed&quot;</span>
    <span class="n">LOW_END_DEVICE</span> <span class="o">=</span> <span class="s2">&quot;LowEndDevice&quot;</span>
    <span class="n">INVALID_SCHEME_REDIRECT</span> <span class="o">=</span> <span class="s2">&quot;InvalidSchemeRedirect&quot;</span>
    <span class="n">INVALID_SCHEME_NAVIGATION</span> <span class="o">=</span> <span class="s2">&quot;InvalidSchemeNavigation&quot;</span>
    <span class="n">NAVIGATION_REQUEST_BLOCKED_BY_CSP</span> <span class="o">=</span> <span class="s2">&quot;NavigationRequestBlockedByCsp&quot;</span>
    <span class="n">MAIN_FRAME_NAVIGATION</span> <span class="o">=</span> <span class="s2">&quot;MainFrameNavigation&quot;</span>
    <span class="n">MOJO_BINDER_POLICY</span> <span class="o">=</span> <span class="s2">&quot;MojoBinderPolicy&quot;</span>
    <span class="n">RENDERER_PROCESS_CRASHED</span> <span class="o">=</span> <span class="s2">&quot;RendererProcessCrashed&quot;</span>
    <span class="n">RENDERER_PROCESS_KILLED</span> <span class="o">=</span> <span class="s2">&quot;RendererProcessKilled&quot;</span>
    <span class="n">DOWNLOAD</span> <span class="o">=</span> <span class="s2">&quot;Download&quot;</span>
    <span class="n">TRIGGER_DESTROYED</span> <span class="o">=</span> <span class="s2">&quot;TriggerDestroyed&quot;</span>
    <span class="n">NAVIGATION_NOT_COMMITTED</span> <span class="o">=</span> <span class="s2">&quot;NavigationNotCommitted&quot;</span>
    <span class="n">NAVIGATION_BAD_HTTP_STATUS</span> <span class="o">=</span> <span class="s2">&quot;NavigationBadHttpStatus&quot;</span>
    <span class="n">CLIENT_CERT_REQUESTED</span> <span class="o">=</span> <span class="s2">&quot;ClientCertRequested&quot;</span>
    <span class="n">NAVIGATION_REQUEST_NETWORK_ERROR</span> <span class="o">=</span> <span class="s2">&quot;NavigationRequestNetworkError&quot;</span>
    <span class="n">CANCEL_ALL_HOSTS_FOR_TESTING</span> <span class="o">=</span> <span class="s2">&quot;CancelAllHostsForTesting&quot;</span>
    <span class="n">DID_FAIL_LOAD</span> <span class="o">=</span> <span class="s2">&quot;DidFailLoad&quot;</span>
    <span class="n">STOP</span> <span class="o">=</span> <span class="s2">&quot;Stop&quot;</span>
    <span class="n">SSL_CERTIFICATE_ERROR</span> <span class="o">=</span> <span class="s2">&quot;SslCertificateError&quot;</span>
    <span class="n">LOGIN_AUTH_REQUESTED</span> <span class="o">=</span> <span class="s2">&quot;LoginAuthRequested&quot;</span>
    <span class="n">UA_CHANGE_REQUIRES_RELOAD</span> <span class="o">=</span> <span class="s2">&quot;UaChangeRequiresReload&quot;</span>
    <span class="n">BLOCKED_BY_CLIENT</span> <span class="o">=</span> <span class="s2">&quot;BlockedByClient&quot;</span>
    <span class="n">AUDIO_OUTPUT_DEVICE_REQUESTED</span> <span class="o">=</span> <span class="s2">&quot;AudioOutputDeviceRequested&quot;</span>
    <span class="n">MIXED_CONTENT</span> <span class="o">=</span> <span class="s2">&quot;MixedContent&quot;</span>
    <span class="n">TRIGGER_BACKGROUNDED</span> <span class="o">=</span> <span class="s2">&quot;TriggerBackgrounded&quot;</span>
    <span class="n">MEMORY_LIMIT_EXCEEDED</span> <span class="o">=</span> <span class="s2">&quot;MemoryLimitExceeded&quot;</span>
    <span class="n">DATA_SAVER_ENABLED</span> <span class="o">=</span> <span class="s2">&quot;DataSaverEnabled&quot;</span>
    <span class="n">TRIGGER_URL_HAS_EFFECTIVE_URL</span> <span class="o">=</span> <span class="s2">&quot;TriggerUrlHasEffectiveUrl&quot;</span>
    <span class="n">ACTIVATED_BEFORE_STARTED</span> <span class="o">=</span> <span class="s2">&quot;ActivatedBeforeStarted&quot;</span>
    <span class="n">INACTIVE_PAGE_RESTRICTION</span> <span class="o">=</span> <span class="s2">&quot;InactivePageRestriction&quot;</span>
    <span class="n">START_FAILED</span> <span class="o">=</span> <span class="s2">&quot;StartFailed&quot;</span>
    <span class="n">TIMEOUT_BACKGROUNDED</span> <span class="o">=</span> <span class="s2">&quot;TimeoutBackgrounded&quot;</span>
    <span class="n">CROSS_SITE_REDIRECT_IN_INITIAL_NAVIGATION</span> <span class="o">=</span> <span class="s2">&quot;CrossSiteRedirectInInitialNavigation&quot;</span>
    <span class="n">CROSS_SITE_NAVIGATION_IN_INITIAL_NAVIGATION</span> <span class="o">=</span> <span class="p">(</span>
        <span class="s2">&quot;CrossSiteNavigationInInitialNavigation&quot;</span>
    <span class="p">)</span>
    <span class="n">SAME_SITE_CROSS_ORIGIN_REDIRECT_NOT_OPT_IN_IN_INITIAL_NAVIGATION</span> <span class="o">=</span> <span class="p">(</span>
        <span class="s2">&quot;SameSiteCrossOriginRedirectNotOptInInInitialNavigation&quot;</span>
    <span class="p">)</span>
    <span class="n">SAME_SITE_CROSS_ORIGIN_NAVIGATION_NOT_OPT_IN_IN_INITIAL_NAVIGATION</span> <span class="o">=</span> <span class="p">(</span>
        <span class="s2">&quot;SameSiteCrossOriginNavigationNotOptInInInitialNavigation&quot;</span>
    <span class="p">)</span>
    <span class="n">ACTIVATION_NAVIGATION_PARAMETER_MISMATCH</span> <span class="o">=</span> <span class="s2">&quot;ActivationNavigationParameterMismatch&quot;</span>
    <span class="n">ACTIVATED_IN_BACKGROUND</span> <span class="o">=</span> <span class="s2">&quot;ActivatedInBackground&quot;</span>
    <span class="n">EMBEDDER_HOST_DISALLOWED</span> <span class="o">=</span> <span class="s2">&quot;EmbedderHostDisallowed&quot;</span>
    <span class="n">ACTIVATION_NAVIGATION_DESTROYED_BEFORE_SUCCESS</span> <span class="o">=</span> <span class="p">(</span>
        <span class="s2">&quot;ActivationNavigationDestroyedBeforeSuccess&quot;</span>
    <span class="p">)</span>
    <span class="n">TAB_CLOSED_BY_USER_GESTURE</span> <span class="o">=</span> <span class="s2">&quot;TabClosedByUserGesture&quot;</span>
    <span class="n">TAB_CLOSED_WITHOUT_USER_GESTURE</span> <span class="o">=</span> <span class="s2">&quot;TabClosedWithoutUserGesture&quot;</span>
    <span class="n">PRIMARY_MAIN_FRAME_RENDERER_PROCESS_CRASHED</span> <span class="o">=</span> <span class="p">(</span>
        <span class="s2">&quot;PrimaryMainFrameRendererProcessCrashed&quot;</span>
    <span class="p">)</span>
    <span class="n">PRIMARY_MAIN_FRAME_RENDERER_PROCESS_KILLED</span> <span class="o">=</span> <span class="s2">&quot;PrimaryMainFrameRendererProcessKilled&quot;</span>
    <span class="n">ACTIVATION_FRAME_POLICY_NOT_COMPATIBLE</span> <span class="o">=</span> <span class="s2">&quot;ActivationFramePolicyNotCompatible&quot;</span>
    <span class="n">PRELOADING_DISABLED</span> <span class="o">=</span> <span class="s2">&quot;PreloadingDisabled&quot;</span>
    <span class="n">BATTERY_SAVER_ENABLED</span> <span class="o">=</span> <span class="s2">&quot;BatterySaverEnabled&quot;</span>
    <span class="n">ACTIVATED_DURING_MAIN_FRAME_NAVIGATION</span> <span class="o">=</span> <span class="s2">&quot;ActivatedDuringMainFrameNavigation&quot;</span>
    <span class="n">PRELOADING_UNSUPPORTED_BY_WEB_CONTENTS</span> <span class="o">=</span> <span class="s2">&quot;PreloadingUnsupportedByWebContents&quot;</span>
    <span class="n">CROSS_SITE_REDIRECT_IN_MAIN_FRAME_NAVIGATION</span> <span class="o">=</span> <span class="p">(</span>
        <span class="s2">&quot;CrossSiteRedirectInMainFrameNavigation&quot;</span>
    <span class="p">)</span>
    <span class="n">CROSS_SITE_NAVIGATION_IN_MAIN_FRAME_NAVIGATION</span> <span class="o">=</span> <span class="p">(</span>
        <span class="s2">&quot;CrossSiteNavigationInMainFrameNavigation&quot;</span>
    <span class="p">)</span>
    <span class="n">SAME_SITE_CROSS_ORIGIN_REDIRECT_NOT_OPT_IN_IN_MAIN_FRAME_NAVIGATION</span> <span class="o">=</span> <span class="p">(</span>
        <span class="s2">&quot;SameSiteCrossOriginRedirectNotOptInInMainFrameNavigation&quot;</span>
    <span class="p">)</span>
    <span class="n">SAME_SITE_CROSS_ORIGIN_NAVIGATION_NOT_OPT_IN_IN_MAIN_FRAME_NAVIGATION</span> <span class="o">=</span> <span class="p">(</span>
        <span class="s2">&quot;SameSiteCrossOriginNavigationNotOptInInMainFrameNavigation&quot;</span>
    <span class="p">)</span>
    <span class="n">MEMORY_PRESSURE_ON_TRIGGER</span> <span class="o">=</span> <span class="s2">&quot;MemoryPressureOnTrigger&quot;</span>
    <span class="n">MEMORY_PRESSURE_AFTER_TRIGGERED</span> <span class="o">=</span> <span class="s2">&quot;MemoryPressureAfterTriggered&quot;</span>
    <span class="n">PRERENDERING_DISABLED_BY_DEV_TOOLS</span> <span class="o">=</span> <span class="s2">&quot;PrerenderingDisabledByDevTools&quot;</span>
    <span class="n">SPECULATION_RULE_REMOVED</span> <span class="o">=</span> <span class="s2">&quot;SpeculationRuleRemoved&quot;</span>
    <span class="n">ACTIVATED_WITH_AUXILIARY_BROWSING_CONTEXTS</span> <span class="o">=</span> <span class="p">(</span>
        <span class="s2">&quot;ActivatedWithAuxiliaryBrowsingContexts&quot;</span>
    <span class="p">)</span>
    <span class="n">MAX_NUM_OF_RUNNING_EAGER_PRERENDERS_EXCEEDED</span> <span class="o">=</span> <span class="p">(</span>
        <span class="s2">&quot;MaxNumOfRunningEagerPrerendersExceeded&quot;</span>
    <span class="p">)</span>
    <span class="n">MAX_NUM_OF_RUNNING_NON_EAGER_PRERENDERS_EXCEEDED</span> <span class="o">=</span> <span class="p">(</span>
        <span class="s2">&quot;MaxNumOfRunningNonEagerPrerendersExceeded&quot;</span>
    <span class="p">)</span>
    <span class="n">MAX_NUM_OF_RUNNING_EMBEDDER_PRERENDERS_EXCEEDED</span> <span class="o">=</span> <span class="p">(</span>
        <span class="s2">&quot;MaxNumOfRunningEmbedderPrerendersExceeded&quot;</span>
    <span class="p">)</span>
    <span class="n">PRERENDERING_URL_HAS_EFFECTIVE_URL</span> <span class="o">=</span> <span class="s2">&quot;PrerenderingUrlHasEffectiveUrl&quot;</span>
    <span class="n">REDIRECTED_PRERENDERING_URL_HAS_EFFECTIVE_URL</span> <span class="o">=</span> <span class="p">(</span>
        <span class="s2">&quot;RedirectedPrerenderingUrlHasEffectiveUrl&quot;</span>
    <span class="p">)</span>
    <span class="n">ACTIVATION_URL_HAS_EFFECTIVE_URL</span> <span class="o">=</span> <span class="s2">&quot;ActivationUrlHasEffectiveUrl&quot;</span>
    <span class="n">JAVA_SCRIPT_INTERFACE_ADDED</span> <span class="o">=</span> <span class="s2">&quot;JavaScriptInterfaceAdded&quot;</span>
    <span class="n">JAVA_SCRIPT_INTERFACE_REMOVED</span> <span class="o">=</span> <span class="s2">&quot;JavaScriptInterfaceRemoved&quot;</span>
    <span class="n">ALL_PRERENDERING_CANCELED</span> <span class="o">=</span> <span class="s2">&quot;AllPrerenderingCanceled&quot;</span>
    <span class="n">WINDOW_CLOSED</span> <span class="o">=</span> <span class="s2">&quot;WindowClosed&quot;</span>
    <span class="n">SLOW_NETWORK</span> <span class="o">=</span> <span class="s2">&quot;SlowNetwork&quot;</span>
    <span class="n">OTHER_PRERENDERED_PAGE_ACTIVATED</span> <span class="o">=</span> <span class="s2">&quot;OtherPrerenderedPageActivated&quot;</span>
    <span class="n">V8_OPTIMIZER_DISABLED</span> <span class="o">=</span> <span class="s2">&quot;V8OptimizerDisabled&quot;</span>
    <span class="n">PRERENDER_FAILED_DURING_PREFETCH</span> <span class="o">=</span> <span class="s2">&quot;PrerenderFailedDuringPrefetch&quot;</span>
    <span class="n">BROWSING_DATA_REMOVED</span> <span class="o">=</span> <span class="s2">&quot;BrowsingDataRemoved&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">PrerenderFinalStatus</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span></div>



<div class="viewcode-block" id="PreloadingStatus">
<a class="viewcode-back" href="../../../nodriver/cdp/preload.html#nodriver.cdp.preload.PreloadingStatus">[docs]</a>
<span class="k">class</span> <span class="nc">PreloadingStatus</span><span class="p">(</span><span class="n">enum</span><span class="o">.</span><span class="n">Enum</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Preloading status values, see also PreloadingTriggeringOutcome. This</span>
<span class="sd">    status is shared by prefetchStatusUpdated and prerenderStatusUpdated.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">PENDING</span> <span class="o">=</span> <span class="s2">&quot;Pending&quot;</span>
    <span class="n">RUNNING</span> <span class="o">=</span> <span class="s2">&quot;Running&quot;</span>
    <span class="n">READY</span> <span class="o">=</span> <span class="s2">&quot;Ready&quot;</span>
    <span class="n">SUCCESS</span> <span class="o">=</span> <span class="s2">&quot;Success&quot;</span>
    <span class="n">FAILURE</span> <span class="o">=</span> <span class="s2">&quot;Failure&quot;</span>
    <span class="n">NOT_SUPPORTED</span> <span class="o">=</span> <span class="s2">&quot;NotSupported&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">PreloadingStatus</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span></div>



<div class="viewcode-block" id="PrefetchStatus">
<a class="viewcode-back" href="../../../nodriver/cdp/preload.html#nodriver.cdp.preload.PrefetchStatus">[docs]</a>
<span class="k">class</span> <span class="nc">PrefetchStatus</span><span class="p">(</span><span class="n">enum</span><span class="o">.</span><span class="n">Enum</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    TODO(https://crbug.com/1384419): revisit the list of PrefetchStatus and</span>
<span class="sd">    filter out the ones that aren&#39;t necessary to the developers.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">PREFETCH_ALLOWED</span> <span class="o">=</span> <span class="s2">&quot;PrefetchAllowed&quot;</span>
    <span class="n">PREFETCH_FAILED_INELIGIBLE_REDIRECT</span> <span class="o">=</span> <span class="s2">&quot;PrefetchFailedIneligibleRedirect&quot;</span>
    <span class="n">PREFETCH_FAILED_INVALID_REDIRECT</span> <span class="o">=</span> <span class="s2">&quot;PrefetchFailedInvalidRedirect&quot;</span>
    <span class="n">PREFETCH_FAILED_MIME_NOT_SUPPORTED</span> <span class="o">=</span> <span class="s2">&quot;PrefetchFailedMIMENotSupported&quot;</span>
    <span class="n">PREFETCH_FAILED_NET_ERROR</span> <span class="o">=</span> <span class="s2">&quot;PrefetchFailedNetError&quot;</span>
    <span class="n">PREFETCH_FAILED_NON2_XX</span> <span class="o">=</span> <span class="s2">&quot;PrefetchFailedNon2XX&quot;</span>
    <span class="n">PREFETCH_EVICTED_AFTER_BROWSING_DATA_REMOVED</span> <span class="o">=</span> <span class="p">(</span>
        <span class="s2">&quot;PrefetchEvictedAfterBrowsingDataRemoved&quot;</span>
    <span class="p">)</span>
    <span class="n">PREFETCH_EVICTED_AFTER_CANDIDATE_REMOVED</span> <span class="o">=</span> <span class="s2">&quot;PrefetchEvictedAfterCandidateRemoved&quot;</span>
    <span class="n">PREFETCH_EVICTED_FOR_NEWER_PREFETCH</span> <span class="o">=</span> <span class="s2">&quot;PrefetchEvictedForNewerPrefetch&quot;</span>
    <span class="n">PREFETCH_HELDBACK</span> <span class="o">=</span> <span class="s2">&quot;PrefetchHeldback&quot;</span>
    <span class="n">PREFETCH_INELIGIBLE_RETRY_AFTER</span> <span class="o">=</span> <span class="s2">&quot;PrefetchIneligibleRetryAfter&quot;</span>
    <span class="n">PREFETCH_IS_PRIVACY_DECOY</span> <span class="o">=</span> <span class="s2">&quot;PrefetchIsPrivacyDecoy&quot;</span>
    <span class="n">PREFETCH_IS_STALE</span> <span class="o">=</span> <span class="s2">&quot;PrefetchIsStale&quot;</span>
    <span class="n">PREFETCH_NOT_ELIGIBLE_BROWSER_CONTEXT_OFF_THE_RECORD</span> <span class="o">=</span> <span class="p">(</span>
        <span class="s2">&quot;PrefetchNotEligibleBrowserContextOffTheRecord&quot;</span>
    <span class="p">)</span>
    <span class="n">PREFETCH_NOT_ELIGIBLE_DATA_SAVER_ENABLED</span> <span class="o">=</span> <span class="s2">&quot;PrefetchNotEligibleDataSaverEnabled&quot;</span>
    <span class="n">PREFETCH_NOT_ELIGIBLE_EXISTING_PROXY</span> <span class="o">=</span> <span class="s2">&quot;PrefetchNotEligibleExistingProxy&quot;</span>
    <span class="n">PREFETCH_NOT_ELIGIBLE_HOST_IS_NON_UNIQUE</span> <span class="o">=</span> <span class="s2">&quot;PrefetchNotEligibleHostIsNonUnique&quot;</span>
    <span class="n">PREFETCH_NOT_ELIGIBLE_NON_DEFAULT_STORAGE_PARTITION</span> <span class="o">=</span> <span class="p">(</span>
        <span class="s2">&quot;PrefetchNotEligibleNonDefaultStoragePartition&quot;</span>
    <span class="p">)</span>
    <span class="n">PREFETCH_NOT_ELIGIBLE_SAME_SITE_CROSS_ORIGIN_PREFETCH_REQUIRED_PROXY</span> <span class="o">=</span> <span class="p">(</span>
        <span class="s2">&quot;PrefetchNotEligibleSameSiteCrossOriginPrefetchRequiredProxy&quot;</span>
    <span class="p">)</span>
    <span class="n">PREFETCH_NOT_ELIGIBLE_SCHEME_IS_NOT_HTTPS</span> <span class="o">=</span> <span class="s2">&quot;PrefetchNotEligibleSchemeIsNotHttps&quot;</span>
    <span class="n">PREFETCH_NOT_ELIGIBLE_USER_HAS_COOKIES</span> <span class="o">=</span> <span class="s2">&quot;PrefetchNotEligibleUserHasCookies&quot;</span>
    <span class="n">PREFETCH_NOT_ELIGIBLE_USER_HAS_SERVICE_WORKER</span> <span class="o">=</span> <span class="p">(</span>
        <span class="s2">&quot;PrefetchNotEligibleUserHasServiceWorker&quot;</span>
    <span class="p">)</span>
    <span class="n">PREFETCH_NOT_ELIGIBLE_USER_HAS_SERVICE_WORKER_NO_FETCH_HANDLER</span> <span class="o">=</span> <span class="p">(</span>
        <span class="s2">&quot;PrefetchNotEligibleUserHasServiceWorkerNoFetchHandler&quot;</span>
    <span class="p">)</span>
    <span class="n">PREFETCH_NOT_ELIGIBLE_REDIRECT_FROM_SERVICE_WORKER</span> <span class="o">=</span> <span class="p">(</span>
        <span class="s2">&quot;PrefetchNotEligibleRedirectFromServiceWorker&quot;</span>
    <span class="p">)</span>
    <span class="n">PREFETCH_NOT_ELIGIBLE_REDIRECT_TO_SERVICE_WORKER</span> <span class="o">=</span> <span class="p">(</span>
        <span class="s2">&quot;PrefetchNotEligibleRedirectToServiceWorker&quot;</span>
    <span class="p">)</span>
    <span class="n">PREFETCH_NOT_ELIGIBLE_BATTERY_SAVER_ENABLED</span> <span class="o">=</span> <span class="p">(</span>
        <span class="s2">&quot;PrefetchNotEligibleBatterySaverEnabled&quot;</span>
    <span class="p">)</span>
    <span class="n">PREFETCH_NOT_ELIGIBLE_PRELOADING_DISABLED</span> <span class="o">=</span> <span class="s2">&quot;PrefetchNotEligiblePreloadingDisabled&quot;</span>
    <span class="n">PREFETCH_NOT_FINISHED_IN_TIME</span> <span class="o">=</span> <span class="s2">&quot;PrefetchNotFinishedInTime&quot;</span>
    <span class="n">PREFETCH_NOT_STARTED</span> <span class="o">=</span> <span class="s2">&quot;PrefetchNotStarted&quot;</span>
    <span class="n">PREFETCH_NOT_USED_COOKIES_CHANGED</span> <span class="o">=</span> <span class="s2">&quot;PrefetchNotUsedCookiesChanged&quot;</span>
    <span class="n">PREFETCH_PROXY_NOT_AVAILABLE</span> <span class="o">=</span> <span class="s2">&quot;PrefetchProxyNotAvailable&quot;</span>
    <span class="n">PREFETCH_RESPONSE_USED</span> <span class="o">=</span> <span class="s2">&quot;PrefetchResponseUsed&quot;</span>
    <span class="n">PREFETCH_SUCCESSFUL_BUT_NOT_USED</span> <span class="o">=</span> <span class="s2">&quot;PrefetchSuccessfulButNotUsed&quot;</span>
    <span class="n">PREFETCH_NOT_USED_PROBE_FAILED</span> <span class="o">=</span> <span class="s2">&quot;PrefetchNotUsedProbeFailed&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">PrefetchStatus</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span></div>



<div class="viewcode-block" id="PrerenderMismatchedHeaders">
<a class="viewcode-back" href="../../../nodriver/cdp/preload.html#nodriver.cdp.preload.PrerenderMismatchedHeaders">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">PrerenderMismatchedHeaders</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Information of headers to be displayed when the header mismatch occurred.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">header_name</span><span class="p">:</span> <span class="nb">str</span>

    <span class="n">initial_value</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="n">activation_value</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;headerName&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">header_name</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">initial_value</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;initialValue&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">initial_value</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">activation_value</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;activationValue&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">activation_value</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">PrerenderMismatchedHeaders</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">header_name</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;headerName&quot;</span><span class="p">]),</span>
            <span class="n">initial_value</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;initialValue&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;initialValue&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">activation_value</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;activationValue&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;activationValue&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="enable">
<a class="viewcode-back" href="../../../nodriver/cdp/preload.html#nodriver.cdp.preload.enable">[docs]</a>
<span class="k">def</span> <span class="nf">enable</span><span class="p">()</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Preload.enable&quot;</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span></div>



<div class="viewcode-block" id="disable">
<a class="viewcode-back" href="../../../nodriver/cdp/preload.html#nodriver.cdp.preload.disable">[docs]</a>
<span class="k">def</span> <span class="nf">disable</span><span class="p">()</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Preload.disable&quot;</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span></div>



<div class="viewcode-block" id="RuleSetUpdated">
<a class="viewcode-back" href="../../../nodriver/cdp/preload.html#nodriver.cdp.preload.RuleSetUpdated">[docs]</a>
<span class="nd">@event_class</span><span class="p">(</span><span class="s2">&quot;Preload.ruleSetUpdated&quot;</span><span class="p">)</span>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">RuleSetUpdated</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Upsert. Currently, it is only emitted when a rule set added.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">rule_set</span><span class="p">:</span> <span class="n">RuleSet</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">RuleSetUpdated</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">rule_set</span><span class="o">=</span><span class="n">RuleSet</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;ruleSet&quot;</span><span class="p">]))</span></div>



<div class="viewcode-block" id="RuleSetRemoved">
<a class="viewcode-back" href="../../../nodriver/cdp/preload.html#nodriver.cdp.preload.RuleSetRemoved">[docs]</a>
<span class="nd">@event_class</span><span class="p">(</span><span class="s2">&quot;Preload.ruleSetRemoved&quot;</span><span class="p">)</span>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">RuleSetRemoved</span><span class="p">:</span>
    <span class="n">id_</span><span class="p">:</span> <span class="n">RuleSetId</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">RuleSetRemoved</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">id_</span><span class="o">=</span><span class="n">RuleSetId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;id&quot;</span><span class="p">]))</span></div>



<div class="viewcode-block" id="PreloadEnabledStateUpdated">
<a class="viewcode-back" href="../../../nodriver/cdp/preload.html#nodriver.cdp.preload.PreloadEnabledStateUpdated">[docs]</a>
<span class="nd">@event_class</span><span class="p">(</span><span class="s2">&quot;Preload.preloadEnabledStateUpdated&quot;</span><span class="p">)</span>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">PreloadEnabledStateUpdated</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Fired when a preload enabled state is updated.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">disabled_by_preference</span><span class="p">:</span> <span class="nb">bool</span>
    <span class="n">disabled_by_data_saver</span><span class="p">:</span> <span class="nb">bool</span>
    <span class="n">disabled_by_battery_saver</span><span class="p">:</span> <span class="nb">bool</span>
    <span class="n">disabled_by_holdback_prefetch_speculation_rules</span><span class="p">:</span> <span class="nb">bool</span>
    <span class="n">disabled_by_holdback_prerender_speculation_rules</span><span class="p">:</span> <span class="nb">bool</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">PreloadEnabledStateUpdated</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">disabled_by_preference</span><span class="o">=</span><span class="nb">bool</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;disabledByPreference&quot;</span><span class="p">]),</span>
            <span class="n">disabled_by_data_saver</span><span class="o">=</span><span class="nb">bool</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;disabledByDataSaver&quot;</span><span class="p">]),</span>
            <span class="n">disabled_by_battery_saver</span><span class="o">=</span><span class="nb">bool</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;disabledByBatterySaver&quot;</span><span class="p">]),</span>
            <span class="n">disabled_by_holdback_prefetch_speculation_rules</span><span class="o">=</span><span class="nb">bool</span><span class="p">(</span>
                <span class="n">json</span><span class="p">[</span><span class="s2">&quot;disabledByHoldbackPrefetchSpeculationRules&quot;</span><span class="p">]</span>
            <span class="p">),</span>
            <span class="n">disabled_by_holdback_prerender_speculation_rules</span><span class="o">=</span><span class="nb">bool</span><span class="p">(</span>
                <span class="n">json</span><span class="p">[</span><span class="s2">&quot;disabledByHoldbackPrerenderSpeculationRules&quot;</span><span class="p">]</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="PrefetchStatusUpdated">
<a class="viewcode-back" href="../../../nodriver/cdp/preload.html#nodriver.cdp.preload.PrefetchStatusUpdated">[docs]</a>
<span class="nd">@event_class</span><span class="p">(</span><span class="s2">&quot;Preload.prefetchStatusUpdated&quot;</span><span class="p">)</span>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">PrefetchStatusUpdated</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Fired when a prefetch attempt is updated.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">key</span><span class="p">:</span> <span class="n">PreloadingAttemptKey</span>
    <span class="n">pipeline_id</span><span class="p">:</span> <span class="n">PreloadPipelineId</span>
    <span class="c1">#: The frame id of the frame initiating prefetch.</span>
    <span class="n">initiating_frame_id</span><span class="p">:</span> <span class="n">page</span><span class="o">.</span><span class="n">FrameId</span>
    <span class="n">prefetch_url</span><span class="p">:</span> <span class="nb">str</span>
    <span class="n">status</span><span class="p">:</span> <span class="n">PreloadingStatus</span>
    <span class="n">prefetch_status</span><span class="p">:</span> <span class="n">PrefetchStatus</span>
    <span class="n">request_id</span><span class="p">:</span> <span class="n">network</span><span class="o">.</span><span class="n">RequestId</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">PrefetchStatusUpdated</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">key</span><span class="o">=</span><span class="n">PreloadingAttemptKey</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;key&quot;</span><span class="p">]),</span>
            <span class="n">pipeline_id</span><span class="o">=</span><span class="n">PreloadPipelineId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;pipelineId&quot;</span><span class="p">]),</span>
            <span class="n">initiating_frame_id</span><span class="o">=</span><span class="n">page</span><span class="o">.</span><span class="n">FrameId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;initiatingFrameId&quot;</span><span class="p">]),</span>
            <span class="n">prefetch_url</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;prefetchUrl&quot;</span><span class="p">]),</span>
            <span class="n">status</span><span class="o">=</span><span class="n">PreloadingStatus</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;status&quot;</span><span class="p">]),</span>
            <span class="n">prefetch_status</span><span class="o">=</span><span class="n">PrefetchStatus</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;prefetchStatus&quot;</span><span class="p">]),</span>
            <span class="n">request_id</span><span class="o">=</span><span class="n">network</span><span class="o">.</span><span class="n">RequestId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;requestId&quot;</span><span class="p">]),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="PrerenderStatusUpdated">
<a class="viewcode-back" href="../../../nodriver/cdp/preload.html#nodriver.cdp.preload.PrerenderStatusUpdated">[docs]</a>
<span class="nd">@event_class</span><span class="p">(</span><span class="s2">&quot;Preload.prerenderStatusUpdated&quot;</span><span class="p">)</span>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">PrerenderStatusUpdated</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Fired when a prerender attempt is updated.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">key</span><span class="p">:</span> <span class="n">PreloadingAttemptKey</span>
    <span class="n">pipeline_id</span><span class="p">:</span> <span class="n">PreloadPipelineId</span>
    <span class="n">status</span><span class="p">:</span> <span class="n">PreloadingStatus</span>
    <span class="n">prerender_status</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">PrerenderFinalStatus</span><span class="p">]</span>
    <span class="c1">#: This is used to give users more information about the name of Mojo interface</span>
    <span class="c1">#: that is incompatible with prerender and has caused the cancellation of the attempt.</span>
    <span class="n">disallowed_mojo_interface</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span>
    <span class="n">mismatched_headers</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">PrerenderMismatchedHeaders</span><span class="p">]]</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">PrerenderStatusUpdated</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">key</span><span class="o">=</span><span class="n">PreloadingAttemptKey</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;key&quot;</span><span class="p">]),</span>
            <span class="n">pipeline_id</span><span class="o">=</span><span class="n">PreloadPipelineId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;pipelineId&quot;</span><span class="p">]),</span>
            <span class="n">status</span><span class="o">=</span><span class="n">PreloadingStatus</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;status&quot;</span><span class="p">]),</span>
            <span class="n">prerender_status</span><span class="o">=</span><span class="p">(</span>
                <span class="n">PrerenderFinalStatus</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;prerenderStatus&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;prerenderStatus&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">disallowed_mojo_interface</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;disallowedMojoInterface&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;disallowedMojoInterface&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">mismatched_headers</span><span class="o">=</span><span class="p">(</span>
                <span class="p">[</span>
                    <span class="n">PrerenderMismatchedHeaders</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span>
                    <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;mismatchedHeaders&quot;</span><span class="p">]</span>
                <span class="p">]</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;mismatchedHeaders&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="PreloadingAttemptSourcesUpdated">
<a class="viewcode-back" href="../../../nodriver/cdp/preload.html#nodriver.cdp.preload.PreloadingAttemptSourcesUpdated">[docs]</a>
<span class="nd">@event_class</span><span class="p">(</span><span class="s2">&quot;Preload.preloadingAttemptSourcesUpdated&quot;</span><span class="p">)</span>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">PreloadingAttemptSourcesUpdated</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Send a list of sources for all preloading attempts in a document.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">loader_id</span><span class="p">:</span> <span class="n">network</span><span class="o">.</span><span class="n">LoaderId</span>
    <span class="n">preloading_attempt_sources</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">PreloadingAttemptSource</span><span class="p">]</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">PreloadingAttemptSourcesUpdated</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">loader_id</span><span class="o">=</span><span class="n">network</span><span class="o">.</span><span class="n">LoaderId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;loaderId&quot;</span><span class="p">]),</span>
            <span class="n">preloading_attempt_sources</span><span class="o">=</span><span class="p">[</span>
                <span class="n">PreloadingAttemptSource</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span>
                <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;preloadingAttemptSources&quot;</span><span class="p">]</span>
            <span class="p">],</span>
        <span class="p">)</span></div>

</pre></div>
        </article>
      </div>
      <footer>
        
        <div class="related-pages">
          
          
        </div>
        <div class="bottom-of-page">
          <div class="left-details">
            <div class="copyright">
                Copyright &#169; 2023, Author
            </div>
            Made with <a href="https://www.sphinx-doc.org/">Sphinx</a> and <a class="muted-link" href="https://pradyunsg.me">@pradyunsg</a>'s
            
            <a href="https://github.com/pradyunsg/furo">Furo</a>
            
          </div>
          <div class="right-details">
            
          </div>
        </div>
        
      </footer>
    </div>
    <aside class="toc-drawer no-toc">
      
      
      
    </aside>
  </div>
</div><script src="../../../_static/documentation_options.js?v=5929fcd5"></script>
    <script src="../../../_static/doctools.js?v=888ff710"></script>
    <script src="../../../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../../../_static/scripts/furo.js?v=32e29ea5"></script>
    </body>
</html>