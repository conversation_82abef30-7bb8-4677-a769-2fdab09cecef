<!doctype html>
<html class="no-js" lang="en" data-content_root="../../../">
  <head><meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width,initial-scale=1"/>
    <meta name="color-scheme" content="light dark"><link rel="index" title="Index" href="../../../genindex.html" /><link rel="search" title="Search" href="../../../search.html" />

    <!-- Generated with Sphinx 7.2.6 and Furo 2023.09.10 -->
        <title>nodriver.core.tab - nodriver documentation</title>
      <link rel="stylesheet" type="text/css" href="../../../_static/pygments.css?v=fa44fd50" />
    <link rel="stylesheet" type="text/css" href="../../../_static/styles/furo.css?v=135e06be" />
    <link rel="stylesheet" type="text/css" href="../../../_static/styles/furo-extensions.css?v=36a5483c" />
    <link rel="stylesheet" type="text/css" href="../../../_static/./custom.css?v=3934642d" />
    
    


<style>
  body {
    --color-code-background: #eeffcc;
  --color-code-foreground: black;
  
  }
  @media not print {
    body[data-theme="dark"] {
      --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8eefba;
  --color-background-primary: #111112;
  --color-problematic: #e7aeae;
  
    }
    @media (prefers-color-scheme: dark) {
      body:not([data-theme="light"]) {
        --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8eefba;
  --color-background-primary: #111112;
  --color-problematic: #e7aeae;
  
      }
    }
  }
</style></head>
  <body>
    
    <script>
      document.body.dataset.theme = localStorage.getItem("theme") || "auto";
    </script>
    

<svg xmlns="http://www.w3.org/2000/svg" style="display: none;">
  <symbol id="svg-toc" viewBox="0 0 24 24">
    <title>Contents</title>
    <svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 1024 1024">
      <path d="M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM115.4 518.9L271.7 642c5.8 4.6 14.4.5 14.4-6.9V388.9c0-7.4-8.5-11.5-14.4-6.9L115.4 505.1a8.74 8.74 0 0 0 0 13.8z"/>
    </svg>
  </symbol>
  <symbol id="svg-menu" viewBox="0 0 24 24">
    <title>Menu</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-menu">
      <line x1="3" y1="12" x2="21" y2="12"></line>
      <line x1="3" y1="6" x2="21" y2="6"></line>
      <line x1="3" y1="18" x2="21" y2="18"></line>
    </svg>
  </symbol>
  <symbol id="svg-arrow-right" viewBox="0 0 24 24">
    <title>Expand</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-chevron-right">
      <polyline points="9 18 15 12 9 6"></polyline>
    </svg>
  </symbol>
  <symbol id="svg-sun" viewBox="0 0 24 24">
    <title>Light mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="feather-sun">
      <circle cx="12" cy="12" r="5"></circle>
      <line x1="12" y1="1" x2="12" y2="3"></line>
      <line x1="12" y1="21" x2="12" y2="23"></line>
      <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
      <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
      <line x1="1" y1="12" x2="3" y2="12"></line>
      <line x1="21" y1="12" x2="23" y2="12"></line>
      <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
      <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
    </svg>
  </symbol>
  <symbol id="svg-moon" viewBox="0 0 24 24">
    <title>Dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-moon">
      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
      <path d="M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z" />
    </svg>
  </symbol>
  <symbol id="svg-sun-half" viewBox="0 0 24 24">
    <title>Auto light/dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-shadow">
      <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
      <circle cx="12" cy="12" r="9" />
      <path d="M13 12h5" />
      <path d="M13 15h4" />
      <path d="M13 18h1" />
      <path d="M13 9h4" />
      <path d="M13 6h1" />
    </svg>
  </symbol>
</svg>

<input type="checkbox" class="sidebar-toggle" name="__navigation" id="__navigation">
<input type="checkbox" class="sidebar-toggle" name="__toc" id="__toc">
<label class="overlay sidebar-overlay" for="__navigation">
  <div class="visually-hidden">Hide navigation sidebar</div>
</label>
<label class="overlay toc-overlay" for="__toc">
  <div class="visually-hidden">Hide table of contents sidebar</div>
</label>



<div class="page">
  <header class="mobile-header">
    <div class="header-left">
      <label class="nav-overlay-icon" for="__navigation">
        <div class="visually-hidden">Toggle site navigation sidebar</div>
        <i class="icon"><svg><use href="#svg-menu"></use></svg></i>
      </label>
    </div>
    <div class="header-center">
      <a href="../../../index.html"><div class="brand">nodriver  documentation</div></a>
    </div>
    <div class="header-right">
      <div class="theme-toggle-container theme-toggle-header">
        <button class="theme-toggle">
          <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
          <svg class="theme-icon-when-auto"><use href="#svg-sun-half"></use></svg>
          <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
          <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
        </button>
      </div>
      <label class="toc-overlay-icon toc-header-icon no-toc" for="__toc">
        <div class="visually-hidden">Toggle table of contents sidebar</div>
        <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
      </label>
    </div>
  </header>
  <aside class="sidebar-drawer">
    <div class="sidebar-container">
      
      <div class="sidebar-sticky"><div class="sidebar-scroll"><a class="sidebar-brand" href="../../../index.html">
  
  
  <span class="sidebar-brand-text">nodriver  documentation</span>
  
</a><form class="sidebar-search-container" method="get" action="../../../search.html" role="search">
  <input class="sidebar-search" placeholder="Search" name="q" aria-label="Search">
  <input type="hidden" name="check_keywords" value="yes">
  <input type="hidden" name="area" value="default">
</form>
<div id="searchbox"></div><div class="sidebar-tree">
  <ul>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html">Quickstart guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#usage-example">usage example</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#more-complete-example">More complete example</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#custom-starting-options">Custom starting options</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#alternative-custom-options">Alternative custom options</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#proxies-socks5-authenticated-too">Proxies (socks5, authenticated too)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#concrete-example">Concrete example</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/classes/browser.html">Browser class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/classes/tab.html">Tab class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/classes/element.html">Element class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/classes/others_and_helpers.html">Other classes and Helper classes</a></li>
</ul>
<ul>
<li class="toctree-l1 has-children"><a class="reference internal" href="../../../nodriver/cdp.html">CDP object</a><input class="toctree-checkbox" id="toctree-checkbox-1" name="toctree-checkbox-1" role="switch" type="checkbox"/><label for="toctree-checkbox-1"><div class="visually-hidden">Toggle navigation of CDP object</div><i class="icon"><svg><use href="#svg-arrow-right"></use></svg></i></label><ul>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/accessibility.html">Accessibility</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/animation.html">Animation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/audits.html">Audits</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/autofill.html">Autofill</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/background_service.html">BackgroundService</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/bluetooth_emulation.html">BluetoothEmulation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/browser.html">Browser</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/cache_storage.html">CacheStorage</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/cast.html">Cast</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/console.html">Console</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/css.html">CSS</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/debugger.html">Debugger</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/device_access.html">DeviceAccess</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/device_orientation.html">DeviceOrientation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/dom.html">DOM</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/dom_debugger.html">DOMDebugger</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/dom_snapshot.html">DOMSnapshot</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/dom_storage.html">DOMStorage</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/emulation.html">Emulation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/event_breakpoints.html">EventBreakpoints</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/extensions.html">Extensions</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/fed_cm.html">FedCm</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/fetch.html">Fetch</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/file_system.html">FileSystem</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/headless_experimental.html">HeadlessExperimental</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/heap_profiler.html">HeapProfiler</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/indexed_db.html">IndexedDB</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/input_.html">Input</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/inspector.html">Inspector</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/io.html">IO</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/layer_tree.html">LayerTree</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/log.html">Log</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/media.html">Media</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/memory.html">Memory</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/network.html">Network</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/overlay.html">Overlay</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/page.html">Page</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/performance.html">Performance</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/performance_timeline.html">PerformanceTimeline</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/preload.html">Preload</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/profiler.html">Profiler</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/pwa.html">PWA</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/runtime.html">Runtime</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/schema.html">Schema</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/security.html">Security</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/service_worker.html">ServiceWorker</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/storage.html">Storage</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/system_info.html">SystemInfo</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/target.html">Target</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/tethering.html">Tethering</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/tracing.html">Tracing</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/web_audio.html">WebAudio</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/web_authn.html">WebAuthn</a></li>
</ul>
</li>
</ul>

</div>
</div>
      </div>
      
    </div>
  </aside>
  <div class="main">
    <div class="content">
      <div class="article-container">
        <a href="#" class="back-to-top muted-link">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
            <path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8v12z"></path>
          </svg>
          <span>Back to top</span>
        </a>
        <div class="content-icon-container">
          <div class="theme-toggle-container theme-toggle-content">
            <button class="theme-toggle">
              <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
              <svg class="theme-icon-when-auto"><use href="#svg-sun-half"></use></svg>
              <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
              <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
            </button>
          </div>
          <label class="toc-overlay-icon toc-content-icon no-toc" for="__toc">
            <div class="visually-hidden">Toggle table of contents sidebar</div>
            <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
          </label>
        </div>
        <article role="main">
          <h1>Source code for nodriver.core.tab</h1><div class="highlight"><pre>
<span></span><span class="c1"># Copyright 2024 by UltrafunkAmsterdam (https://github.com/UltrafunkAmsterdam)</span>
<span class="c1"># All rights reserved.</span>
<span class="c1"># This file is part of the nodriver package.</span>
<span class="c1"># and is released under the &quot;GNU AFFERO GENERAL PUBLIC LICENSE&quot;.</span>
<span class="c1"># Please see the LICENSE.txt file that should have been included as part of this package.</span>

<span class="kn">from</span> <span class="nn">__future__</span> <span class="kn">import</span> <span class="n">annotations</span>

<span class="kn">import</span> <span class="nn">asyncio</span>
<span class="kn">import</span> <span class="nn">functools</span>
<span class="kn">import</span> <span class="nn">logging</span>
<span class="kn">import</span> <span class="nn">os</span>
<span class="kn">import</span> <span class="nn">pathlib</span>
<span class="kn">import</span> <span class="nn">secrets</span>
<span class="kn">import</span> <span class="nn">typing</span>
<span class="kn">import</span> <span class="nn">warnings</span>
<span class="kn">from</span> <span class="nn">pathlib</span> <span class="kn">import</span> <span class="n">Path</span>
<span class="kn">from</span> <span class="nn">typing</span> <span class="kn">import</span> <span class="n">Any</span><span class="p">,</span> <span class="n">Generator</span><span class="p">,</span> <span class="n">List</span><span class="p">,</span> <span class="n">Optional</span><span class="p">,</span> <span class="n">Tuple</span><span class="p">,</span> <span class="n">Union</span>

<span class="kn">import</span> <span class="nn">nodriver.core.browser</span>

<span class="kn">from</span> <span class="nn">..</span> <span class="kn">import</span> <span class="n">cdp</span>
<span class="kn">from</span> <span class="nn">.</span> <span class="kn">import</span> <span class="n">element</span><span class="p">,</span> <span class="n">util</span>
<span class="kn">from</span> <span class="nn">.config</span> <span class="kn">import</span> <span class="n">PathLike</span>
<span class="kn">from</span> <span class="nn">.connection</span> <span class="kn">import</span> <span class="n">Connection</span><span class="p">,</span> <span class="n">ProtocolException</span>

<span class="n">logger</span> <span class="o">=</span> <span class="n">logging</span><span class="o">.</span><span class="n">getLogger</span><span class="p">(</span><span class="vm">__name__</span><span class="p">)</span>


<div class="viewcode-block" id="Tab">
<a class="viewcode-back" href="../../../nodriver/classes/tab.html#nodriver.Tab">[docs]</a>
<span class="k">class</span> <span class="nc">Tab</span><span class="p">(</span><span class="n">Connection</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    :ref:`tab` is the controlling mechanism/connection to a &#39;target&#39;,</span>
<span class="sd">    for most of us &#39;target&#39; can be read as &#39;tab&#39;. however it could also</span>
<span class="sd">    be an iframe, serviceworker or background script for example,</span>
<span class="sd">    although there isn&#39;t much to control for those.</span>

<span class="sd">    if you open a new window by using :py:meth:`browser.get(..., new_window=True)`</span>
<span class="sd">    your url will open a new window. this window is a &#39;tab&#39;.</span>
<span class="sd">    When you browse to another page, the tab will be the same (it is an browser view).</span>

<span class="sd">    So it&#39;s important to keep some reference to tab objects, in case you&#39;re</span>
<span class="sd">    done interacting with elements and want to operate on the page level again.</span>

<span class="sd">    Custom CDP commands</span>
<span class="sd">    ---------------------------</span>
<span class="sd">    Tab object provide many useful and often-used methods. It is also</span>
<span class="sd">    possible to utilize the included cdp classes to to something totally custom.</span>

<span class="sd">    the cdp package is a set of so-called &quot;domains&quot; with each having methods, events and types.</span>
<span class="sd">    to send a cdp method, for example :py:obj:`cdp.page.navigate`, you&#39;ll have to check</span>
<span class="sd">    whether the method accepts any parameters and whether they are required or not.</span>

<span class="sd">    you can use</span>

<span class="sd">    ```python</span>
<span class="sd">    await tab.send(cdp.page.navigate(url=&#39;https://yoururlhere&#39;))</span>
<span class="sd">    ```</span>

<span class="sd">    so tab.send() accepts a generator object, which is created by calling a cdp method.</span>
<span class="sd">    this way you can build very detailed and customized commands.</span>
<span class="sd">    (note: finding correct command combo&#39;s can be a time consuming task, luckily i added a whole bunch</span>
<span class="sd">    of useful methods, preferably having the same api&#39;s or lookalikes, as in selenium)</span>


<span class="sd">    some useful, often needed and simply required methods</span>
<span class="sd">    ===================================================================</span>


<span class="sd">    :py:meth:`~find`  |  find(text)</span>
<span class="sd">    ----------------------------------------</span>
<span class="sd">    find and returns a single element by text match. by default returns the first element found.</span>
<span class="sd">    much more powerful is the best_match flag, although also much more expensive.</span>
<span class="sd">    when no match is found, it will retry for &lt;timeout&gt; seconds (default: 10), so</span>
<span class="sd">    this is also suitable to use as wait condition.</span>


<span class="sd">    :py:meth:`~find` |  find(text, best_match=True) or find(text, True)</span>
<span class="sd">    ---------------------------------------------------------------------------------</span>
<span class="sd">    Much more powerful (and expensive!!) than the above, is the use of the `find(text, best_match=True)` flag.</span>
<span class="sd">    It will still return 1 element, but when multiple matches are found, picks the one having the</span>
<span class="sd">    most similar text length.</span>
<span class="sd">    How would that help?</span>
<span class="sd">    For example, you search for &quot;login&quot;, you&#39;d probably want the &quot;login&quot; button element,</span>
<span class="sd">    and not thousands of scripts,meta,headings which happens to contain a string of &quot;login&quot;.</span>

<span class="sd">    when no match is found, it will retry for &lt;timeout&gt; seconds (default: 10), so</span>
<span class="sd">    this is also suitable to use as wait condition.</span>


<span class="sd">    :py:meth:`~select` | select(selector)</span>
<span class="sd">    ----------------------------------------</span>
<span class="sd">    find and returns a single element by css selector match.</span>
<span class="sd">    when no match is found, it will retry for &lt;timeout&gt; seconds (default: 10), so</span>
<span class="sd">    this is also suitable to use as wait condition.</span>


<span class="sd">    :py:meth:`~select_all` | select_all(selector)</span>
<span class="sd">    ------------------------------------------------</span>
<span class="sd">    find and returns all elements by css selector match.</span>
<span class="sd">    when no match is found, it will retry for &lt;timeout&gt; seconds (default: 10), so</span>
<span class="sd">    this is also suitable to use as wait condition.</span>


<span class="sd">    await :py:obj:`Tab`</span>
<span class="sd">    ---------------------------</span>
<span class="sd">    calling `await tab` will do a lot of stuff under the hood, and ensures all references</span>
<span class="sd">    are up to date. also it allows for the script to &quot;breathe&quot;, as it is oftentime faster than your browser or</span>
<span class="sd">    webpage. So whenever you get stuck and things crashes or element could not be found, you should probably let</span>
<span class="sd">    it &quot;breathe&quot;  by calling `await page`  and/or `await page.sleep()`</span>

<span class="sd">    also, it&#39;s ensuring :py:obj:`~url` will be updated to the most recent one, which is quite important in some</span>
<span class="sd">    other methods.</span>

<span class="sd">    attempts to find the location of given template image in the current viewport</span>
<span class="sd">    the only real use case for this is bot-detection systems.</span>
<span class="sd">    you can find for example the location of a &#39;verify&#39;-checkbox,</span>
<span class="sd">    which are hidden from dom using shadow-root&#39;s or workers.</span>



<span class="sd">    await :py:obj:`Tab.template_location` (and await :py:obj:`Tab.verify_cf`)</span>
<span class="sd">    ------------------------------------------------------------------------------</span>

<span class="sd">    attempts to find the location of given template image in the current viewport.</span>
<span class="sd">    the only real use case for this is bot-detection systems.</span>
<span class="sd">    you can find, for example the location of a ‘verify’-checkbox, which are hidden from dom</span>
<span class="sd">    using shadow-root’s or/or workers and cannot be controlled by normal methods.</span>

<span class="sd">    template_image can be custom (for example your language, included is english only),</span>
<span class="sd">    but you need to create the template image yourself, which is just a cropped</span>
<span class="sd">    image of the area, see example image, where the target is exactly in the center.</span>
<span class="sd">    template_image can be custom (for example your language), but you need to</span>
<span class="sd">    create the template image yourself, where the target is exactly in the center.</span>


<span class="sd">    example (111x71)</span>
<span class="sd">    ---------</span>
<span class="sd">    this includes the white space on the left, to make the box center</span>

<span class="sd">    .. image:: template_example.png</span>
<span class="sd">        :width: 111</span>
<span class="sd">        :alt: example template image</span>


<span class="sd">    Using other and custom CDP commands</span>
<span class="sd">    ======================================================</span>
<span class="sd">    using the included cdp module, you can easily craft commands, which will always return an generator object.</span>
<span class="sd">    this generator object can be easily sent to the :py:meth:`~send`  method.</span>

<span class="sd">    :py:meth:`~send`</span>
<span class="sd">    ---------------------------</span>
<span class="sd">    this is probably THE most important method, although you won&#39;t ever call it, unless you want to</span>
<span class="sd">    go really custom. the send method accepts a :py:obj:`cdp` command. Each of which can be found in the</span>
<span class="sd">    cdp section.</span>

<span class="sd">    when you import * from this package, cdp will be in your namespace, and contains all domains/actions/events</span>
<span class="sd">    you can act upon.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">browser</span><span class="p">:</span> <span class="n">nodriver</span><span class="o">.</span><span class="n">core</span><span class="o">.</span><span class="n">browser</span><span class="o">.</span><span class="n">Browser</span>
    <span class="n">_download_behavior</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span>
        <span class="bp">self</span><span class="p">,</span>
        <span class="n">websocket_url</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span>
        <span class="n">target</span><span class="p">:</span> <span class="n">cdp</span><span class="o">.</span><span class="n">target</span><span class="o">.</span><span class="n">TargetInfo</span><span class="p">,</span>
        <span class="n">browser</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="s2">&quot;nodriver.Browser&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
        <span class="o">**</span><span class="n">kwargs</span><span class="p">,</span>
    <span class="p">):</span>
        <span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="fm">__init__</span><span class="p">(</span><span class="n">websocket_url</span><span class="p">,</span> <span class="n">target</span><span class="p">,</span> <span class="n">browser</span><span class="p">,</span> <span class="o">**</span><span class="n">kwargs</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_dom</span> <span class="o">=</span> <span class="kc">None</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_window_id</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">inspector_url</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        get the inspector url. this url can be used in another browser to show you the devtools interface for</span>
<span class="sd">        current tab. useful for debugging (and headless)</span>
<span class="sd">        :return:</span>
<span class="sd">        :rtype:</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">return</span> <span class="sa">f</span><span class="s2">&quot;http://</span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">browser</span><span class="o">.</span><span class="n">config</span><span class="o">.</span><span class="n">host</span><span class="si">}</span><span class="s2">:</span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">browser</span><span class="o">.</span><span class="n">config</span><span class="o">.</span><span class="n">port</span><span class="si">}</span><span class="s2">/devtools/inspector.html?ws=</span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">websocket_url</span><span class="p">[</span><span class="mi">5</span><span class="p">:]</span><span class="si">}</span><span class="s2">&quot;</span>

<div class="viewcode-block" id="Tab.inspector_open">
<a class="viewcode-back" href="../../../nodriver/classes/tab.html#nodriver.Tab.inspector_open">[docs]</a>
    <span class="k">def</span> <span class="nf">inspector_open</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="kn">import</span> <span class="nn">webbrowser</span>

        <span class="n">webbrowser</span><span class="o">.</span><span class="n">open</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">inspector_url</span><span class="p">,</span> <span class="n">new</span><span class="o">=</span><span class="mi">2</span><span class="p">)</span></div>


<div class="viewcode-block" id="Tab.open_external_inspector">
<a class="viewcode-back" href="../../../nodriver/classes/tab.html#nodriver.Tab.open_external_inspector">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">open_external_inspector</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        opens the system&#39;s browser containing the devtools inspector page</span>
<span class="sd">        for this tab. could be handy, especially to debug in headless mode.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="kn">import</span> <span class="nn">webbrowser</span>

        <span class="n">webbrowser</span><span class="o">.</span><span class="n">open</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">inspector_url</span><span class="p">)</span></div>


<div class="viewcode-block" id="Tab.feed_cdp">
<a class="viewcode-back" href="../../../nodriver/classes/tab.html#nodriver.Tab.feed_cdp">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">feed_cdp</span><span class="p">(</span>
        <span class="bp">self</span><span class="p">,</span> <span class="n">cmd</span><span class="p">:</span> <span class="n">Generator</span><span class="p">[</span><span class="nb">dict</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="n">Any</span><span class="p">],</span> <span class="nb">dict</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="n">Any</span><span class="p">],</span> <span class="n">Any</span><span class="p">]</span>
    <span class="p">)</span> <span class="o">-&gt;</span> <span class="n">asyncio</span><span class="o">.</span><span class="n">Future</span><span class="p">:</span>
        <span class="k">return</span> <span class="k">await</span> <span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="n">_send_oneshot</span><span class="p">(</span><span class="n">cmd</span><span class="p">)</span></div>



    <span class="k">async</span> <span class="k">def</span> <span class="nf">_prepare_headless</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>

        <span class="k">if</span> <span class="nb">getattr</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="s2">&quot;_prep_headless_done&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">):</span>
            <span class="k">return</span>
        <span class="n">resp</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">_send_oneshot</span><span class="p">(</span>
            <span class="n">cdp</span><span class="o">.</span><span class="n">runtime</span><span class="o">.</span><span class="n">evaluate</span><span class="p">(</span>
                <span class="n">expression</span><span class="o">=</span><span class="s2">&quot;navigator.userAgent&quot;</span><span class="p">,</span>
            <span class="p">)</span>
        <span class="p">)</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="n">resp</span><span class="p">:</span>
            <span class="k">return</span>
        <span class="n">response</span><span class="p">,</span> <span class="n">error</span> <span class="o">=</span> <span class="n">resp</span>
        <span class="k">if</span> <span class="n">response</span> <span class="ow">and</span> <span class="n">response</span><span class="o">.</span><span class="n">value</span><span class="p">:</span>
            <span class="n">ua</span> <span class="o">=</span> <span class="n">response</span><span class="o">.</span><span class="n">value</span>
            <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">_send_oneshot</span><span class="p">(</span>
                <span class="n">cdp</span><span class="o">.</span><span class="n">network</span><span class="o">.</span><span class="n">set_user_agent_override</span><span class="p">(</span>
                    <span class="n">user_agent</span><span class="o">=</span><span class="n">ua</span><span class="o">.</span><span class="n">replace</span><span class="p">(</span><span class="s2">&quot;Headless&quot;</span><span class="p">,</span> <span class="s2">&quot;&quot;</span><span class="p">),</span>
                <span class="p">)</span>
            <span class="p">)</span>
        <span class="nb">setattr</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="s2">&quot;_prep_headless_done&quot;</span><span class="p">,</span> <span class="kc">True</span><span class="p">)</span>

    <span class="k">async</span> <span class="k">def</span> <span class="nf">_prepare_expert</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">if</span> <span class="nb">getattr</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="s2">&quot;_prep_expert_done&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">):</span>
            <span class="k">return</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">browser</span><span class="p">:</span>
            <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">_send_oneshot</span><span class="p">(</span><span class="n">cdp</span><span class="o">.</span><span class="n">page</span><span class="o">.</span><span class="n">enable</span><span class="p">())</span>
            <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">_send_oneshot</span><span class="p">(</span>
                <span class="n">cdp</span><span class="o">.</span><span class="n">page</span><span class="o">.</span><span class="n">add_script_to_evaluate_on_new_document</span><span class="p">(</span>
<span class="w">                    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">                    console.log(&quot;hooking attachShadow&quot;);</span>
<span class="sd">                    Element.prototype._attachShadow = Element.prototype.attachShadow;</span>
<span class="sd">                    Element.prototype.attachShadow = function () {</span>
<span class="sd">                        console.log(&#39;calling hooked attachShadow&#39;)</span>
<span class="sd">                        return this._attachShadow( { mode: &quot;open&quot; } );</span>
<span class="sd">                    };&quot;&quot;&quot;</span>
                <span class="p">)</span>
            <span class="p">)</span>

        <span class="nb">setattr</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="s2">&quot;_prep_expert_done&quot;</span><span class="p">,</span> <span class="kc">True</span><span class="p">)</span>

<div class="viewcode-block" id="Tab.find">
<a class="viewcode-back" href="../../../nodriver/classes/tab.html#nodriver.Tab.find">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">find</span><span class="p">(</span>
        <span class="bp">self</span><span class="p">,</span>
        <span class="n">text</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span>
        <span class="n">best_match</span><span class="p">:</span> <span class="nb">bool</span> <span class="o">=</span> <span class="kc">True</span><span class="p">,</span>
        <span class="n">return_enclosing_element</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
        <span class="n">timeout</span><span class="p">:</span> <span class="n">Union</span><span class="p">[</span><span class="nb">int</span><span class="p">,</span> <span class="nb">float</span><span class="p">]</span> <span class="o">=</span> <span class="mi">10</span><span class="p">,</span>
    <span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        find single element by text</span>
<span class="sd">        can also be used to wait for such element to appear.</span>

<span class="sd">        :param text: text to search for. note: script contents are also considered text</span>
<span class="sd">        :type text: str</span>
<span class="sd">        :param best_match:  :param best_match:  when True (default), it will return the element which has the most</span>
<span class="sd">                                               comparable string length. this could help tremendously, when for example</span>
<span class="sd">                                               you search for &quot;login&quot;, you&#39;d probably want the login button element,</span>
<span class="sd">                                               and not thousands of scripts,meta,headings containing a string of &quot;login&quot;.</span>
<span class="sd">                                               When False, it will return naively just the first match (but is way faster).</span>
<span class="sd">         :type best_match: bool</span>
<span class="sd">         :param return_enclosing_element:</span>
<span class="sd">                 since we deal with nodes instead of elements, the find function most often returns</span>
<span class="sd">                 so called text nodes, which is actually a element of plain text, which is</span>
<span class="sd">                 the somehow imaginary &quot;child&quot; of a &quot;span&quot;, &quot;p&quot;, &quot;script&quot; or any other elements which have text between their opening</span>
<span class="sd">                 and closing tags.</span>
<span class="sd">                 most often when we search by text, we actually aim for the element containing the text instead of</span>
<span class="sd">                 a lousy plain text node, so by default the containing element is returned.</span>

<span class="sd">                 however, there are (why not) exceptions, for example elements that use the &quot;placeholder=&quot; property.</span>
<span class="sd">                 this text is rendered, but is not a pure text node. in that case you can set this flag to False.</span>
<span class="sd">                 since in this case we are probably interested in just that element, and not it&#39;s parent.</span>


<span class="sd">                 # todo, automatically determine node type</span>
<span class="sd">                 # ignore the return_enclosing_element flag if the found node is NOT a text node but a</span>
<span class="sd">                 # regular element (one having a tag) in which case that is exactly what we need.</span>
<span class="sd">         :type return_enclosing_element: bool</span>
<span class="sd">        :param timeout: raise timeout exception when after this many seconds nothing is found.</span>
<span class="sd">        :type timeout: float,int</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">loop</span> <span class="o">=</span> <span class="n">asyncio</span><span class="o">.</span><span class="n">get_running_loop</span><span class="p">()</span>
        <span class="n">start_time</span> <span class="o">=</span> <span class="n">loop</span><span class="o">.</span><span class="n">time</span><span class="p">()</span>

        <span class="n">text</span> <span class="o">=</span> <span class="n">text</span><span class="o">.</span><span class="n">strip</span><span class="p">()</span>

        <span class="n">item</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">find_element_by_text</span><span class="p">(</span>
            <span class="n">text</span><span class="p">,</span> <span class="n">best_match</span><span class="p">,</span> <span class="n">return_enclosing_element</span>
        <span class="p">)</span>
        <span class="k">while</span> <span class="ow">not</span> <span class="n">item</span><span class="p">:</span>
            <span class="k">await</span> <span class="bp">self</span>
            <span class="n">item</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">find_element_by_text</span><span class="p">(</span>
                <span class="n">text</span><span class="p">,</span> <span class="n">best_match</span><span class="p">,</span> <span class="n">return_enclosing_element</span>
            <span class="p">)</span>
            <span class="k">if</span> <span class="n">loop</span><span class="o">.</span><span class="n">time</span><span class="p">()</span> <span class="o">-</span> <span class="n">start_time</span> <span class="o">&gt;</span> <span class="n">timeout</span><span class="p">:</span>
                <span class="k">return</span> <span class="n">item</span>
            <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.5</span><span class="p">)</span>
        <span class="k">return</span> <span class="n">item</span></div>


<div class="viewcode-block" id="Tab.select">
<a class="viewcode-back" href="../../../nodriver/classes/tab.html#nodriver.Tab.select">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">select</span><span class="p">(</span>
        <span class="bp">self</span><span class="p">,</span>
        <span class="n">selector</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span>
        <span class="n">timeout</span><span class="p">:</span> <span class="n">Union</span><span class="p">[</span><span class="nb">int</span><span class="p">,</span> <span class="nb">float</span><span class="p">]</span> <span class="o">=</span> <span class="mi">10</span><span class="p">,</span>
    <span class="p">)</span> <span class="o">-&gt;</span> <span class="n">nodriver</span><span class="o">.</span><span class="n">Element</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        find single element by css selector.</span>
<span class="sd">        can also be used to wait for such element to appear.</span>

<span class="sd">        :param selector: css selector, eg a[href], button[class*=close], a &gt; img[src]</span>
<span class="sd">        :type selector: str</span>

<span class="sd">        :param timeout: raise timeout exception when after this many seconds nothing is found.</span>
<span class="sd">        :type timeout: float,int</span>

<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">loop</span> <span class="o">=</span> <span class="n">asyncio</span><span class="o">.</span><span class="n">get_running_loop</span><span class="p">()</span>
        <span class="n">start_time</span> <span class="o">=</span> <span class="n">loop</span><span class="o">.</span><span class="n">time</span><span class="p">()</span>

        <span class="n">selector</span> <span class="o">=</span> <span class="n">selector</span><span class="o">.</span><span class="n">strip</span><span class="p">()</span>
        <span class="n">item</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">query_selector</span><span class="p">(</span><span class="n">selector</span><span class="p">)</span>

        <span class="k">while</span> <span class="ow">not</span> <span class="n">item</span><span class="p">:</span>
            <span class="k">await</span> <span class="bp">self</span>
            <span class="n">item</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">query_selector</span><span class="p">(</span><span class="n">selector</span><span class="p">)</span>
            <span class="k">if</span> <span class="n">loop</span><span class="o">.</span><span class="n">time</span><span class="p">()</span> <span class="o">-</span> <span class="n">start_time</span> <span class="o">&gt;</span> <span class="n">timeout</span><span class="p">:</span>
                <span class="k">return</span> <span class="n">item</span>
            <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.5</span><span class="p">)</span>
        <span class="k">return</span> <span class="n">item</span></div>


<div class="viewcode-block" id="Tab.find_all">
<a class="viewcode-back" href="../../../nodriver/classes/tab.html#nodriver.Tab.find_all">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">find_all</span><span class="p">(</span>
        <span class="bp">self</span><span class="p">,</span>
        <span class="n">text</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span>
        <span class="n">timeout</span><span class="p">:</span> <span class="n">Union</span><span class="p">[</span><span class="nb">int</span><span class="p">,</span> <span class="nb">float</span><span class="p">]</span> <span class="o">=</span> <span class="mi">10</span><span class="p">,</span>
    <span class="p">)</span> <span class="o">-&gt;</span> <span class="n">List</span><span class="p">[</span><span class="n">nodriver</span><span class="o">.</span><span class="n">Element</span><span class="p">]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        find multiple elements by text</span>
<span class="sd">        can also be used to wait for such element to appear.</span>

<span class="sd">        :param text: text to search for. note: script contents are also considered text</span>
<span class="sd">        :type text: str</span>

<span class="sd">        :param timeout: raise timeout exception when after this many seconds nothing is found.</span>
<span class="sd">        :type timeout: float,int</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">loop</span> <span class="o">=</span> <span class="n">asyncio</span><span class="o">.</span><span class="n">get_running_loop</span><span class="p">()</span>
        <span class="n">now</span> <span class="o">=</span> <span class="n">loop</span><span class="o">.</span><span class="n">time</span><span class="p">()</span>

        <span class="n">text</span> <span class="o">=</span> <span class="n">text</span><span class="o">.</span><span class="n">strip</span><span class="p">()</span>
        <span class="n">items</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">find_elements_by_text</span><span class="p">(</span><span class="n">text</span><span class="p">)</span>

        <span class="k">while</span> <span class="ow">not</span> <span class="n">items</span><span class="p">:</span>
            <span class="k">await</span> <span class="bp">self</span>
            <span class="n">items</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">find_elements_by_text</span><span class="p">(</span><span class="n">text</span><span class="p">)</span>
            <span class="k">if</span> <span class="n">loop</span><span class="o">.</span><span class="n">time</span><span class="p">()</span> <span class="o">-</span> <span class="n">now</span> <span class="o">&gt;</span> <span class="n">timeout</span><span class="p">:</span>
                <span class="k">return</span> <span class="n">items</span>
            <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.5</span><span class="p">)</span>
        <span class="k">return</span> <span class="n">items</span></div>


<div class="viewcode-block" id="Tab.select_all">
<a class="viewcode-back" href="../../../nodriver/classes/tab.html#nodriver.Tab.select_all">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">select_all</span><span class="p">(</span>
        <span class="bp">self</span><span class="p">,</span> <span class="n">selector</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">timeout</span><span class="p">:</span> <span class="n">Union</span><span class="p">[</span><span class="nb">int</span><span class="p">,</span> <span class="nb">float</span><span class="p">]</span> <span class="o">=</span> <span class="mi">10</span><span class="p">,</span> <span class="n">include_frames</span><span class="o">=</span><span class="kc">False</span>
    <span class="p">)</span> <span class="o">-&gt;</span> <span class="n">List</span><span class="p">[</span><span class="n">nodriver</span><span class="o">.</span><span class="n">Element</span><span class="p">]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        find multiple elements by css selector.</span>
<span class="sd">        can also be used to wait for such element to appear.</span>


<span class="sd">        :param selector: css selector, eg a[href], button[class*=close], a &gt; img[src]</span>
<span class="sd">        :type selector: str</span>
<span class="sd">        :param timeout: raise timeout exception when after this many seconds nothing is found.</span>
<span class="sd">        :type timeout: float,int</span>
<span class="sd">        :param include_frames: whether to include results in iframes.</span>
<span class="sd">        :type include_frames: bool</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">loop</span> <span class="o">=</span> <span class="n">asyncio</span><span class="o">.</span><span class="n">get_running_loop</span><span class="p">()</span>
        <span class="n">now</span> <span class="o">=</span> <span class="n">loop</span><span class="o">.</span><span class="n">time</span><span class="p">()</span>
        <span class="n">selector</span> <span class="o">=</span> <span class="n">selector</span><span class="o">.</span><span class="n">strip</span><span class="p">()</span>
        <span class="n">items</span> <span class="o">=</span> <span class="p">[]</span>
        <span class="k">if</span> <span class="n">include_frames</span><span class="p">:</span>
            <span class="n">frames</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">query_selector_all</span><span class="p">(</span><span class="s2">&quot;iframe&quot;</span><span class="p">)</span>
            <span class="c1"># unfortunately, asyncio.gather here is not an option</span>
            <span class="k">for</span> <span class="n">fr</span> <span class="ow">in</span> <span class="n">frames</span><span class="p">:</span>
                <span class="n">items</span><span class="o">.</span><span class="n">extend</span><span class="p">(</span><span class="k">await</span> <span class="n">fr</span><span class="o">.</span><span class="n">query_selector_all</span><span class="p">(</span><span class="n">selector</span><span class="p">))</span>

        <span class="n">items</span><span class="o">.</span><span class="n">extend</span><span class="p">(</span><span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">query_selector_all</span><span class="p">(</span><span class="n">selector</span><span class="p">))</span>
        <span class="k">while</span> <span class="ow">not</span> <span class="n">items</span><span class="p">:</span>
            <span class="k">await</span> <span class="bp">self</span>
            <span class="n">items</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">query_selector_all</span><span class="p">(</span><span class="n">selector</span><span class="p">)</span>
            <span class="k">if</span> <span class="n">loop</span><span class="o">.</span><span class="n">time</span><span class="p">()</span> <span class="o">-</span> <span class="n">now</span> <span class="o">&gt;</span> <span class="n">timeout</span><span class="p">:</span>
                <span class="k">return</span> <span class="n">items</span>
            <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.5</span><span class="p">)</span>
        <span class="k">return</span> <span class="n">items</span></div>


<div class="viewcode-block" id="Tab.sleep">
<a class="viewcode-back" href="../../../nodriver/classes/tab.html#nodriver.Tab.sleep">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">sleep</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">t</span><span class="p">:</span> <span class="nb">float</span> <span class="o">|</span> <span class="nb">int</span> <span class="o">=</span> <span class="mi">1</span><span class="p">):</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">browser</span><span class="p">:</span>
            <span class="k">await</span> <span class="n">asyncio</span><span class="o">.</span><span class="n">wait</span><span class="p">(</span>
                <span class="p">[</span>
                    <span class="n">asyncio</span><span class="o">.</span><span class="n">create_task</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">browser</span><span class="o">.</span><span class="n">update_targets</span><span class="p">()),</span>
                    <span class="n">asyncio</span><span class="o">.</span><span class="n">create_task</span><span class="p">(</span><span class="n">asyncio</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="n">t</span><span class="p">)),</span>
                <span class="p">],</span>
                <span class="n">return_when</span><span class="o">=</span><span class="n">asyncio</span><span class="o">.</span><span class="n">FIRST_COMPLETED</span><span class="p">,</span>
            <span class="p">)</span></div>


<div class="viewcode-block" id="Tab.xpath">
<a class="viewcode-back" href="../../../nodriver/classes/tab.html#nodriver.Tab.xpath">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">xpath</span><span class="p">(</span>
        <span class="bp">self</span><span class="p">,</span> <span class="n">xpath</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">timeout</span><span class="p">:</span> <span class="nb">float</span> <span class="o">=</span> <span class="mf">2.5</span>
    <span class="p">)</span> <span class="o">-&gt;</span> <span class="n">List</span><span class="p">[</span><span class="n">Optional</span><span class="p">[</span><span class="n">nodriver</span><span class="o">.</span><span class="n">Element</span><span class="p">]]:</span>  <span class="c1"># noqa</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        find elements by xpath string.</span>
<span class="sd">        if not immediately found, retries are attempted until :ref:`timeout` is reached (default 2.5 seconds).</span>
<span class="sd">        in case nothing is found, it returns an empty list. It will not raise.</span>
<span class="sd">        this timeout mechanism helps when relying on some element to appear before continuing your script.</span>


<span class="sd">        .. code-block:: python</span>

<span class="sd">             # find all the inline scripts (script elements without src attribute )</span>
<span class="sd">             await tab.xpath(&#39;//script[not(@src)]&#39;)</span>

<span class="sd">             # or here, more complex, but my personal favorite to case-insensitive text search</span>

<span class="sd">             await tab.xpath(&#39;//text()[ contains( translate(., &quot;ABCDEFGHIJKLMNOPQRSTUVWXYZ&quot;, &quot;abcdefghijklmnopqrstuvwxyz&quot;),&quot;test&quot;)]&#39;)</span>


<span class="sd">        :param xpath:</span>
<span class="sd">        :type xpath: str</span>
<span class="sd">        :param timeout: 2.5</span>
<span class="sd">        :type timeout: float</span>
<span class="sd">        :return:List[nodriver.Element] or []</span>
<span class="sd">        :rtype:</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">items</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="n">Optional</span><span class="p">[</span><span class="n">nodriver</span><span class="o">.</span><span class="n">Element</span><span class="p">]]</span> <span class="o">=</span> <span class="p">[]</span>
        <span class="k">try</span><span class="p">:</span>
            <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">send</span><span class="p">(</span><span class="n">cdp</span><span class="o">.</span><span class="n">dom</span><span class="o">.</span><span class="n">enable</span><span class="p">(),</span> <span class="kc">True</span><span class="p">)</span>
            <span class="n">items</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">find_all</span><span class="p">(</span><span class="n">xpath</span><span class="p">,</span> <span class="n">timeout</span><span class="o">=</span><span class="mi">0</span><span class="p">)</span>
            <span class="k">if</span> <span class="ow">not</span> <span class="n">items</span><span class="p">:</span>
                <span class="n">loop</span> <span class="o">=</span> <span class="n">asyncio</span><span class="o">.</span><span class="n">get_running_loop</span><span class="p">()</span>
                <span class="n">start_time</span> <span class="o">=</span> <span class="n">loop</span><span class="o">.</span><span class="n">time</span><span class="p">()</span>
                <span class="k">while</span> <span class="ow">not</span> <span class="n">items</span><span class="p">:</span>
                    <span class="n">items</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">find_all</span><span class="p">(</span><span class="n">xpath</span><span class="p">,</span> <span class="n">timeout</span><span class="o">=</span><span class="mi">0</span><span class="p">)</span>
                    <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.1</span><span class="p">)</span>
                    <span class="k">if</span> <span class="n">loop</span><span class="o">.</span><span class="n">time</span><span class="p">()</span> <span class="o">-</span> <span class="n">start_time</span> <span class="o">&gt;</span> <span class="n">timeout</span><span class="p">:</span>
                        <span class="k">break</span>
        <span class="k">finally</span><span class="p">:</span>
            <span class="k">try</span><span class="p">:</span>
                <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">send</span><span class="p">(</span><span class="n">cdp</span><span class="o">.</span><span class="n">dom</span><span class="o">.</span><span class="n">disable</span><span class="p">(),</span> <span class="kc">True</span><span class="p">)</span>
            <span class="k">except</span> <span class="n">ProtocolException</span><span class="p">:</span>
                <span class="c1"># for some strange reason, the call to dom.disable</span>
                <span class="c1"># sometimes raises an exception that dom is not enabled.</span>
                <span class="k">pass</span>
        <span class="k">return</span> <span class="n">items</span></div>


<div class="viewcode-block" id="Tab.get">
<a class="viewcode-back" href="../../../nodriver/classes/tab.html#nodriver.Tab.get">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">get</span><span class="p">(</span>
        <span class="bp">self</span><span class="p">,</span> <span class="n">url</span><span class="o">=</span><span class="s2">&quot;chrome://welcome&quot;</span><span class="p">,</span> <span class="n">new_tab</span><span class="p">:</span> <span class="nb">bool</span> <span class="o">=</span> <span class="kc">False</span><span class="p">,</span> <span class="n">new_window</span><span class="p">:</span> <span class="nb">bool</span> <span class="o">=</span> <span class="kc">False</span>
    <span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;top level get. utilizes the first tab to retrieve given url.</span>

<span class="sd">        convenience function known from selenium.</span>
<span class="sd">        this function handles waits/sleeps and detects when DOM events fired, so it&#39;s the safest</span>
<span class="sd">        way of navigating.</span>

<span class="sd">        :param url: the url to navigate to</span>
<span class="sd">        :param new_tab: open new tab</span>
<span class="sd">        :param new_window:  open new window</span>
<span class="sd">        :return: Page</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">browser</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">AttributeError</span><span class="p">(</span>
                <span class="s2">&quot;this page/tab has no browser attribute, so you can&#39;t use get()&quot;</span>
            <span class="p">)</span>
        <span class="k">if</span> <span class="n">new_window</span> <span class="ow">and</span> <span class="ow">not</span> <span class="n">new_tab</span><span class="p">:</span>
            <span class="n">new_tab</span> <span class="o">=</span> <span class="kc">True</span>

        <span class="k">if</span> <span class="n">new_tab</span><span class="p">:</span>
            <span class="k">return</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">browser</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="n">url</span><span class="p">,</span> <span class="n">new_tab</span><span class="p">,</span> <span class="n">new_window</span><span class="p">)</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">frame_id</span><span class="p">,</span> <span class="n">loader_id</span><span class="p">,</span> <span class="o">*</span><span class="n">_</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">send</span><span class="p">(</span><span class="n">cdp</span><span class="o">.</span><span class="n">page</span><span class="o">.</span><span class="n">navigate</span><span class="p">(</span><span class="n">url</span><span class="p">))</span>
            <span class="k">await</span> <span class="bp">self</span>
            <span class="k">return</span> <span class="bp">self</span></div>


<div class="viewcode-block" id="Tab.query_selector_all">
<a class="viewcode-back" href="../../../nodriver/classes/tab.html#nodriver.Tab.query_selector_all">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">query_selector_all</span><span class="p">(</span>
        <span class="bp">self</span><span class="p">,</span>
        <span class="n">selector</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span>
        <span class="n">_node</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">Union</span><span class="p">[</span><span class="n">cdp</span><span class="o">.</span><span class="n">dom</span><span class="o">.</span><span class="n">Node</span><span class="p">,</span> <span class="s2">&quot;element.Element&quot;</span><span class="p">]]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
    <span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        equivalent of javascripts document.querySelectorAll.</span>
<span class="sd">        this is considered one of the main methods to use in this package.</span>

<span class="sd">        it returns all matching :py:obj:`nodriver.Element` objects.</span>

<span class="sd">        :param selector: css selector. (first time? =&gt; https://www.w3schools.com/cssref/css_selectors.php )</span>
<span class="sd">        :type selector: str</span>
<span class="sd">        :param _node: internal use</span>
<span class="sd">        :type _node:</span>
<span class="sd">        :return:</span>
<span class="sd">        :rtype:</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">if</span> <span class="ow">not</span> <span class="n">_node</span><span class="p">:</span>
            <span class="n">doc</span><span class="p">:</span> <span class="n">cdp</span><span class="o">.</span><span class="n">dom</span><span class="o">.</span><span class="n">Node</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">send</span><span class="p">(</span><span class="n">cdp</span><span class="o">.</span><span class="n">dom</span><span class="o">.</span><span class="n">get_document</span><span class="p">(</span><span class="o">-</span><span class="mi">1</span><span class="p">,</span> <span class="kc">True</span><span class="p">))</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">doc</span> <span class="o">=</span> <span class="n">_node</span>
            <span class="k">if</span> <span class="n">_node</span><span class="o">.</span><span class="n">node_name</span> <span class="o">==</span> <span class="s2">&quot;IFRAME&quot;</span><span class="p">:</span>
                <span class="n">doc</span> <span class="o">=</span> <span class="n">_node</span><span class="o">.</span><span class="n">content_document</span>

        <span class="n">node_ids</span> <span class="o">=</span> <span class="p">[]</span>

        <span class="k">try</span><span class="p">:</span>
            <span class="n">node_ids</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">send</span><span class="p">(</span>
                <span class="n">cdp</span><span class="o">.</span><span class="n">dom</span><span class="o">.</span><span class="n">query_selector_all</span><span class="p">(</span><span class="n">doc</span><span class="o">.</span><span class="n">node_id</span><span class="p">,</span> <span class="n">selector</span><span class="p">)</span>
            <span class="p">)</span>
        <span class="k">except</span> <span class="ne">AttributeError</span><span class="p">:</span>
            <span class="c1"># has no content_document</span>
            <span class="k">return</span>

        <span class="k">except</span> <span class="n">ProtocolException</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
            <span class="k">if</span> <span class="n">_node</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
                <span class="k">if</span> <span class="s2">&quot;could not find node&quot;</span> <span class="ow">in</span> <span class="n">e</span><span class="o">.</span><span class="n">message</span><span class="o">.</span><span class="n">lower</span><span class="p">():</span>
                    <span class="k">if</span> <span class="nb">getattr</span><span class="p">(</span><span class="n">_node</span><span class="p">,</span> <span class="s2">&quot;__last&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">):</span>
                        <span class="k">del</span> <span class="n">_node</span><span class="o">.</span><span class="n">__last</span>
                        <span class="k">return</span> <span class="p">[]</span>
                    <span class="c1"># if supplied node is not found, the dom has changed since acquiring the element</span>
                    <span class="c1"># therefore we need to update our passed node and try again</span>
                    <span class="k">await</span> <span class="n">_node</span><span class="o">.</span><span class="n">update</span><span class="p">()</span>
                    <span class="n">_node</span><span class="o">.</span><span class="n">__last</span> <span class="o">=</span> <span class="p">(</span>
                        <span class="kc">True</span>  <span class="c1"># make sure this isn&#39;t turned into infinite loop</span>
                    <span class="p">)</span>
                    <span class="k">return</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">query_selector_all</span><span class="p">(</span><span class="n">selector</span><span class="p">,</span> <span class="n">_node</span><span class="p">)</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">send</span><span class="p">(</span><span class="n">cdp</span><span class="o">.</span><span class="n">dom</span><span class="o">.</span><span class="n">disable</span><span class="p">())</span>
                <span class="k">raise</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="n">node_ids</span><span class="p">:</span>
            <span class="k">return</span> <span class="p">[]</span>
        <span class="n">items</span> <span class="o">=</span> <span class="p">[]</span>

        <span class="k">for</span> <span class="n">nid</span> <span class="ow">in</span> <span class="n">node_ids</span><span class="p">:</span>
            <span class="n">node</span> <span class="o">=</span> <span class="n">util</span><span class="o">.</span><span class="n">filter_recurse</span><span class="p">(</span><span class="n">doc</span><span class="p">,</span> <span class="k">lambda</span> <span class="n">n</span><span class="p">:</span> <span class="n">n</span><span class="o">.</span><span class="n">node_id</span> <span class="o">==</span> <span class="n">nid</span><span class="p">)</span>
            <span class="c1"># we pass along the retrieved document tree,</span>
            <span class="c1"># to improve performance</span>
            <span class="k">if</span> <span class="ow">not</span> <span class="n">node</span><span class="p">:</span>
                <span class="k">continue</span>
            <span class="n">elem</span> <span class="o">=</span> <span class="n">element</span><span class="o">.</span><span class="n">create</span><span class="p">(</span><span class="n">node</span><span class="p">,</span> <span class="bp">self</span><span class="p">,</span> <span class="n">doc</span><span class="p">)</span>
            <span class="n">items</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">elem</span><span class="p">)</span>

        <span class="k">return</span> <span class="n">items</span></div>


<div class="viewcode-block" id="Tab.query_selector">
<a class="viewcode-back" href="../../../nodriver/classes/tab.html#nodriver.Tab.query_selector">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">query_selector</span><span class="p">(</span>
        <span class="bp">self</span><span class="p">,</span>
        <span class="n">selector</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span>
        <span class="n">_node</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">Union</span><span class="p">[</span><span class="n">cdp</span><span class="o">.</span><span class="n">dom</span><span class="o">.</span><span class="n">Node</span><span class="p">,</span> <span class="n">element</span><span class="o">.</span><span class="n">Element</span><span class="p">]]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
    <span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        find single element based on css selector string</span>

<span class="sd">        :param selector: css selector(s)</span>
<span class="sd">        :type selector: str</span>
<span class="sd">        :return:</span>
<span class="sd">        :rtype:</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">selector</span> <span class="o">=</span> <span class="n">selector</span><span class="o">.</span><span class="n">strip</span><span class="p">()</span>

        <span class="k">if</span> <span class="ow">not</span> <span class="n">_node</span><span class="p">:</span>
            <span class="n">doc</span><span class="p">:</span> <span class="n">cdp</span><span class="o">.</span><span class="n">dom</span><span class="o">.</span><span class="n">Node</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">send</span><span class="p">(</span><span class="n">cdp</span><span class="o">.</span><span class="n">dom</span><span class="o">.</span><span class="n">get_document</span><span class="p">(</span><span class="o">-</span><span class="mi">1</span><span class="p">,</span> <span class="kc">True</span><span class="p">))</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">doc</span> <span class="o">=</span> <span class="n">_node</span>
            <span class="k">if</span> <span class="n">_node</span><span class="o">.</span><span class="n">node_name</span> <span class="o">==</span> <span class="s2">&quot;IFRAME&quot;</span><span class="p">:</span>
                <span class="n">doc</span> <span class="o">=</span> <span class="n">_node</span><span class="o">.</span><span class="n">content_document</span>
        <span class="n">node_id</span> <span class="o">=</span> <span class="kc">None</span>

        <span class="k">try</span><span class="p">:</span>
            <span class="n">node_id</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">send</span><span class="p">(</span><span class="n">cdp</span><span class="o">.</span><span class="n">dom</span><span class="o">.</span><span class="n">query_selector</span><span class="p">(</span><span class="n">doc</span><span class="o">.</span><span class="n">node_id</span><span class="p">,</span> <span class="n">selector</span><span class="p">))</span>

        <span class="k">except</span> <span class="n">ProtocolException</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
            <span class="k">if</span> <span class="n">_node</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
                <span class="k">if</span> <span class="s2">&quot;could not find node&quot;</span> <span class="ow">in</span> <span class="n">e</span><span class="o">.</span><span class="n">message</span><span class="o">.</span><span class="n">lower</span><span class="p">():</span>
                    <span class="k">if</span> <span class="nb">getattr</span><span class="p">(</span><span class="n">_node</span><span class="p">,</span> <span class="s2">&quot;__last&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">):</span>
                        <span class="k">del</span> <span class="n">_node</span><span class="o">.</span><span class="n">__last</span>
                        <span class="k">return</span> <span class="p">[]</span>
                    <span class="c1"># if supplied node is not found, the dom has changed since acquiring the element</span>
                    <span class="c1"># therefore we need to update our passed node and try again</span>
                    <span class="k">await</span> <span class="n">_node</span><span class="o">.</span><span class="n">update</span><span class="p">()</span>
                    <span class="n">_node</span><span class="o">.</span><span class="n">__last</span> <span class="o">=</span> <span class="p">(</span>
                        <span class="kc">True</span>  <span class="c1"># make sure this isn&#39;t turned into infinite loop</span>
                    <span class="p">)</span>
                    <span class="k">return</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">query_selector</span><span class="p">(</span><span class="n">selector</span><span class="p">,</span> <span class="n">_node</span><span class="p">)</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">send</span><span class="p">(</span><span class="n">cdp</span><span class="o">.</span><span class="n">dom</span><span class="o">.</span><span class="n">disable</span><span class="p">())</span>
                <span class="k">raise</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="n">node_id</span><span class="p">:</span>
            <span class="k">return</span>
        <span class="n">node</span> <span class="o">=</span> <span class="n">util</span><span class="o">.</span><span class="n">filter_recurse</span><span class="p">(</span><span class="n">doc</span><span class="p">,</span> <span class="k">lambda</span> <span class="n">n</span><span class="p">:</span> <span class="n">n</span><span class="o">.</span><span class="n">node_id</span> <span class="o">==</span> <span class="n">node_id</span><span class="p">)</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="n">node</span><span class="p">:</span>
            <span class="k">return</span>
        <span class="k">return</span> <span class="n">element</span><span class="o">.</span><span class="n">create</span><span class="p">(</span><span class="n">node</span><span class="p">,</span> <span class="bp">self</span><span class="p">,</span> <span class="n">doc</span><span class="p">)</span></div>


<div class="viewcode-block" id="Tab.find_elements_by_text">
<a class="viewcode-back" href="../../../nodriver/classes/tab.html#nodriver.Tab.find_elements_by_text">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">find_elements_by_text</span><span class="p">(</span>
        <span class="bp">self</span><span class="p">,</span>
        <span class="n">text</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span>
        <span class="n">tag_hint</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
    <span class="p">)</span> <span class="o">-&gt;</span> <span class="n">List</span><span class="p">[</span><span class="n">element</span><span class="o">.</span><span class="n">Element</span><span class="p">]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        returns element which match the given text.</span>
<span class="sd">        returns element which match the given text.</span>
<span class="sd">        please note: this may (or will) also return any other element (like inline scripts),</span>
<span class="sd">        which happen to contain that text.</span>

<span class="sd">        :param text:</span>
<span class="sd">        :type text:</span>
<span class="sd">        :param tag_hint: when provided, narrows down search to only elements which match given tag eg: a, div, script, span</span>
<span class="sd">        :type tag_hint: str</span>
<span class="sd">        :return:</span>
<span class="sd">        :rtype:</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">text</span> <span class="o">=</span> <span class="n">text</span><span class="o">.</span><span class="n">strip</span><span class="p">()</span>
        <span class="n">doc</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">send</span><span class="p">(</span><span class="n">cdp</span><span class="o">.</span><span class="n">dom</span><span class="o">.</span><span class="n">get_document</span><span class="p">(</span><span class="o">-</span><span class="mi">1</span><span class="p">,</span> <span class="kc">True</span><span class="p">))</span>
        <span class="n">search_id</span><span class="p">,</span> <span class="n">nresult</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">send</span><span class="p">(</span><span class="n">cdp</span><span class="o">.</span><span class="n">dom</span><span class="o">.</span><span class="n">perform_search</span><span class="p">(</span><span class="n">text</span><span class="p">,</span> <span class="kc">True</span><span class="p">))</span>
        <span class="k">if</span> <span class="n">nresult</span><span class="p">:</span>
            <span class="n">node_ids</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">send</span><span class="p">(</span>
                <span class="n">cdp</span><span class="o">.</span><span class="n">dom</span><span class="o">.</span><span class="n">get_search_results</span><span class="p">(</span><span class="n">search_id</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="n">nresult</span><span class="p">)</span>
            <span class="p">)</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">node_ids</span> <span class="o">=</span> <span class="p">[]</span>

        <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">send</span><span class="p">(</span><span class="n">cdp</span><span class="o">.</span><span class="n">dom</span><span class="o">.</span><span class="n">discard_search_results</span><span class="p">(</span><span class="n">search_id</span><span class="p">))</span>

        <span class="n">items</span> <span class="o">=</span> <span class="p">[]</span>
        <span class="k">for</span> <span class="n">nid</span> <span class="ow">in</span> <span class="n">node_ids</span><span class="p">:</span>
            <span class="n">node</span> <span class="o">=</span> <span class="n">util</span><span class="o">.</span><span class="n">filter_recurse</span><span class="p">(</span><span class="n">doc</span><span class="p">,</span> <span class="k">lambda</span> <span class="n">n</span><span class="p">:</span> <span class="n">n</span><span class="o">.</span><span class="n">node_id</span> <span class="o">==</span> <span class="n">nid</span><span class="p">)</span>
            <span class="k">if</span> <span class="ow">not</span> <span class="n">node</span><span class="p">:</span>
                <span class="n">node</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">send</span><span class="p">(</span><span class="n">cdp</span><span class="o">.</span><span class="n">dom</span><span class="o">.</span><span class="n">resolve_node</span><span class="p">(</span><span class="n">node_id</span><span class="o">=</span><span class="n">nid</span><span class="p">))</span>
                <span class="k">if</span> <span class="ow">not</span> <span class="n">node</span><span class="p">:</span>
                    <span class="k">continue</span>
                <span class="c1"># remote_object = await self.send(cdp.dom.resolve_node(backend_node_id=node.backend_node_id))</span>
                <span class="c1"># node_id = await self.send(cdp.dom.request_node(object_id=remote_object.object_id))</span>
            <span class="k">try</span><span class="p">:</span>
                <span class="n">elem</span> <span class="o">=</span> <span class="n">element</span><span class="o">.</span><span class="n">create</span><span class="p">(</span><span class="n">node</span><span class="p">,</span> <span class="bp">self</span><span class="p">,</span> <span class="n">doc</span><span class="p">)</span>
            <span class="k">except</span><span class="p">:</span>  <span class="c1"># noqa</span>
                <span class="k">continue</span>
            <span class="k">if</span> <span class="n">elem</span><span class="o">.</span><span class="n">node_type</span> <span class="o">==</span> <span class="mi">3</span><span class="p">:</span>
                <span class="c1"># if found element is a text node (which is plain text, and useless for our purpose),</span>
                <span class="c1"># we return the parent element of the node (which is often a tag which can have text between their</span>
                <span class="c1"># opening and closing tags (that is most tags, except for example &quot;img&quot; and &quot;video&quot;, &quot;br&quot;)</span>

                <span class="k">if</span> <span class="ow">not</span> <span class="n">elem</span><span class="o">.</span><span class="n">parent</span><span class="p">:</span>
                    <span class="c1"># check if parent actually has a parent and update it to be absolutely sure</span>
                    <span class="k">await</span> <span class="n">elem</span><span class="o">.</span><span class="n">update</span><span class="p">()</span>

                <span class="n">items</span><span class="o">.</span><span class="n">append</span><span class="p">(</span>
                    <span class="n">elem</span><span class="o">.</span><span class="n">parent</span> <span class="ow">or</span> <span class="n">elem</span>
                <span class="p">)</span>  <span class="c1"># when it really has no parent, use the text node itself</span>
                <span class="k">continue</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="c1"># just add the element itself</span>
                <span class="n">items</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">elem</span><span class="p">)</span>

        <span class="c1"># since we already fetched the entire doc, including shadow and frames</span>
        <span class="c1"># let&#39;s also search through the iframes</span>
        <span class="n">iframes</span> <span class="o">=</span> <span class="n">util</span><span class="o">.</span><span class="n">filter_recurse_all</span><span class="p">(</span><span class="n">doc</span><span class="p">,</span> <span class="k">lambda</span> <span class="n">node</span><span class="p">:</span> <span class="n">node</span><span class="o">.</span><span class="n">node_name</span> <span class="o">==</span> <span class="s2">&quot;IFRAME&quot;</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">iframes</span><span class="p">:</span>
            <span class="n">iframes_elems</span> <span class="o">=</span> <span class="p">[</span>
                <span class="n">element</span><span class="o">.</span><span class="n">create</span><span class="p">(</span><span class="n">iframe</span><span class="p">,</span> <span class="bp">self</span><span class="p">,</span> <span class="n">iframe</span><span class="o">.</span><span class="n">content_document</span><span class="p">)</span>
                <span class="k">for</span> <span class="n">iframe</span> <span class="ow">in</span> <span class="n">iframes</span>
            <span class="p">]</span>
            <span class="k">for</span> <span class="n">iframe_elem</span> <span class="ow">in</span> <span class="n">iframes_elems</span><span class="p">:</span>
                <span class="k">if</span> <span class="n">iframe_elem</span><span class="o">.</span><span class="n">content_document</span><span class="p">:</span>
                    <span class="n">iframe_text_nodes</span> <span class="o">=</span> <span class="n">util</span><span class="o">.</span><span class="n">filter_recurse_all</span><span class="p">(</span>
                        <span class="n">iframe_elem</span><span class="p">,</span>
                        <span class="k">lambda</span> <span class="n">node</span><span class="p">:</span> <span class="n">node</span><span class="o">.</span><span class="n">node_type</span> <span class="o">==</span> <span class="mi">3</span>  <span class="c1"># noqa</span>
                        <span class="ow">and</span> <span class="n">text</span><span class="o">.</span><span class="n">lower</span><span class="p">()</span> <span class="ow">in</span> <span class="n">node</span><span class="o">.</span><span class="n">node_value</span><span class="o">.</span><span class="n">lower</span><span class="p">(),</span>
                    <span class="p">)</span>
                    <span class="k">if</span> <span class="n">iframe_text_nodes</span><span class="p">:</span>
                        <span class="n">iframe_text_elems</span> <span class="o">=</span> <span class="p">[</span>
                            <span class="n">element</span><span class="o">.</span><span class="n">create</span><span class="p">(</span><span class="n">text_node</span><span class="p">,</span> <span class="bp">self</span><span class="p">,</span> <span class="n">iframe_elem</span><span class="o">.</span><span class="n">tree</span><span class="p">)</span>
                            <span class="k">for</span> <span class="n">text_node</span> <span class="ow">in</span> <span class="n">iframe_text_nodes</span>
                        <span class="p">]</span>
                        <span class="n">items</span><span class="o">.</span><span class="n">extend</span><span class="p">(</span>
                            <span class="n">text_node</span><span class="o">.</span><span class="n">parent</span> <span class="k">for</span> <span class="n">text_node</span> <span class="ow">in</span> <span class="n">iframe_text_elems</span>
                        <span class="p">)</span>
        <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">send</span><span class="p">(</span><span class="n">cdp</span><span class="o">.</span><span class="n">dom</span><span class="o">.</span><span class="n">disable</span><span class="p">())</span>
        <span class="k">return</span> <span class="n">items</span> <span class="ow">or</span> <span class="p">[]</span></div>


<div class="viewcode-block" id="Tab.find_element_by_text">
<a class="viewcode-back" href="../../../nodriver/classes/tab.html#nodriver.Tab.find_element_by_text">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">find_element_by_text</span><span class="p">(</span>
        <span class="bp">self</span><span class="p">,</span>
        <span class="n">text</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span>
        <span class="n">best_match</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">bool</span><span class="p">]</span> <span class="o">=</span> <span class="kc">False</span><span class="p">,</span>
        <span class="n">return_enclosing_element</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">bool</span><span class="p">]</span> <span class="o">=</span> <span class="kc">True</span><span class="p">,</span>
    <span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Union</span><span class="p">[</span><span class="n">element</span><span class="o">.</span><span class="n">Element</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        finds and returns the first element containing &lt;text&gt;, or best match</span>

<span class="sd">        :param text:</span>
<span class="sd">        :type text:</span>
<span class="sd">        :param best_match:  when True, which is MUCH more expensive (thus much slower),</span>
<span class="sd">                            will find the closest match based on length.</span>
<span class="sd">                            this could help tremendously, when for example you search for &quot;login&quot;, you&#39;d probably want the login button element,</span>
<span class="sd">                            and not thousands of scripts,meta,headings containing a string of &quot;login&quot;.</span>

<span class="sd">        :type best_match: bool</span>
<span class="sd">        :param return_enclosing_element:</span>
<span class="sd">        :type return_enclosing_element:</span>
<span class="sd">        :return:</span>
<span class="sd">        :rtype:</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">doc</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">send</span><span class="p">(</span><span class="n">cdp</span><span class="o">.</span><span class="n">dom</span><span class="o">.</span><span class="n">get_document</span><span class="p">(</span><span class="o">-</span><span class="mi">1</span><span class="p">,</span> <span class="kc">True</span><span class="p">))</span>
        <span class="n">text</span> <span class="o">=</span> <span class="n">text</span><span class="o">.</span><span class="n">strip</span><span class="p">()</span>
        <span class="n">search_id</span><span class="p">,</span> <span class="n">nresult</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">send</span><span class="p">(</span><span class="n">cdp</span><span class="o">.</span><span class="n">dom</span><span class="o">.</span><span class="n">perform_search</span><span class="p">(</span><span class="n">text</span><span class="p">,</span> <span class="kc">True</span><span class="p">))</span>
        <span class="k">if</span> <span class="n">nresult</span><span class="p">:</span>
            <span class="n">node_ids</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">send</span><span class="p">(</span>
                <span class="n">cdp</span><span class="o">.</span><span class="n">dom</span><span class="o">.</span><span class="n">get_search_results</span><span class="p">(</span><span class="n">search_id</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="n">nresult</span><span class="p">)</span>
            <span class="p">)</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">node_ids</span> <span class="o">=</span> <span class="kc">None</span>
        <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">send</span><span class="p">(</span><span class="n">cdp</span><span class="o">.</span><span class="n">dom</span><span class="o">.</span><span class="n">discard_search_results</span><span class="p">(</span><span class="n">search_id</span><span class="p">))</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="n">node_ids</span><span class="p">:</span>
            <span class="n">node_ids</span> <span class="o">=</span> <span class="p">[]</span>
        <span class="n">items</span> <span class="o">=</span> <span class="p">[]</span>
        <span class="k">for</span> <span class="n">nid</span> <span class="ow">in</span> <span class="n">node_ids</span><span class="p">:</span>
            <span class="n">node</span> <span class="o">=</span> <span class="n">util</span><span class="o">.</span><span class="n">filter_recurse</span><span class="p">(</span><span class="n">doc</span><span class="p">,</span> <span class="k">lambda</span> <span class="n">n</span><span class="p">:</span> <span class="n">n</span><span class="o">.</span><span class="n">node_id</span> <span class="o">==</span> <span class="n">nid</span><span class="p">)</span>
            <span class="k">try</span><span class="p">:</span>
                <span class="n">elem</span> <span class="o">=</span> <span class="n">element</span><span class="o">.</span><span class="n">create</span><span class="p">(</span><span class="n">node</span><span class="p">,</span> <span class="bp">self</span><span class="p">,</span> <span class="n">doc</span><span class="p">)</span>
            <span class="k">except</span><span class="p">:</span>  <span class="c1"># noqa</span>
                <span class="k">continue</span>
            <span class="k">if</span> <span class="n">elem</span><span class="o">.</span><span class="n">node_type</span> <span class="o">==</span> <span class="mi">3</span><span class="p">:</span>
                <span class="c1"># if found element is a text node (which is plain text, and useless for our purpose),</span>
                <span class="c1"># we return the parent element of the node (which is often a tag which can have text between their</span>
                <span class="c1"># opening and closing tags (that is most tags, except for example &quot;img&quot; and &quot;video&quot;, &quot;br&quot;)</span>

                <span class="k">if</span> <span class="ow">not</span> <span class="n">elem</span><span class="o">.</span><span class="n">parent</span><span class="p">:</span>
                    <span class="c1"># check if parent actually has a parent and update it to be absolutely sure</span>
                    <span class="k">await</span> <span class="n">elem</span><span class="o">.</span><span class="n">update</span><span class="p">()</span>

                <span class="n">items</span><span class="o">.</span><span class="n">append</span><span class="p">(</span>
                    <span class="n">elem</span><span class="o">.</span><span class="n">parent</span> <span class="ow">or</span> <span class="n">elem</span>
                <span class="p">)</span>  <span class="c1"># when it really has no parent, use the text node itself</span>
                <span class="k">continue</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="c1"># just add the element itself</span>
                <span class="n">items</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">elem</span><span class="p">)</span>

        <span class="c1"># since we already fetched the entire doc, including shadow and frames</span>
        <span class="c1"># let&#39;s also search through the iframes</span>
        <span class="n">iframes</span> <span class="o">=</span> <span class="n">util</span><span class="o">.</span><span class="n">filter_recurse_all</span><span class="p">(</span><span class="n">doc</span><span class="p">,</span> <span class="k">lambda</span> <span class="n">node</span><span class="p">:</span> <span class="n">node</span><span class="o">.</span><span class="n">node_name</span> <span class="o">==</span> <span class="s2">&quot;IFRAME&quot;</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">iframes</span><span class="p">:</span>
            <span class="n">iframes_elems</span> <span class="o">=</span> <span class="p">[</span>
                <span class="n">element</span><span class="o">.</span><span class="n">create</span><span class="p">(</span><span class="n">iframe</span><span class="p">,</span> <span class="bp">self</span><span class="p">,</span> <span class="n">iframe</span><span class="o">.</span><span class="n">content_document</span><span class="p">)</span>
                <span class="k">for</span> <span class="n">iframe</span> <span class="ow">in</span> <span class="n">iframes</span>
            <span class="p">]</span>
            <span class="k">for</span> <span class="n">iframe_elem</span> <span class="ow">in</span> <span class="n">iframes_elems</span><span class="p">:</span>
                <span class="n">iframe_text_nodes</span> <span class="o">=</span> <span class="n">util</span><span class="o">.</span><span class="n">filter_recurse_all</span><span class="p">(</span>
                    <span class="n">iframe_elem</span><span class="p">,</span>
                    <span class="k">lambda</span> <span class="n">node</span><span class="p">:</span> <span class="n">node</span><span class="o">.</span><span class="n">node_type</span> <span class="o">==</span> <span class="mi">3</span>  <span class="c1"># noqa</span>
                    <span class="ow">and</span> <span class="n">text</span><span class="o">.</span><span class="n">lower</span><span class="p">()</span> <span class="ow">in</span> <span class="n">node</span><span class="o">.</span><span class="n">node_value</span><span class="o">.</span><span class="n">lower</span><span class="p">(),</span>
                <span class="p">)</span>
                <span class="k">if</span> <span class="n">iframe_text_nodes</span><span class="p">:</span>
                    <span class="n">iframe_text_elems</span> <span class="o">=</span> <span class="p">[</span>
                        <span class="n">element</span><span class="o">.</span><span class="n">create</span><span class="p">(</span><span class="n">text_node</span><span class="p">,</span> <span class="bp">self</span><span class="p">,</span> <span class="n">iframe_elem</span><span class="o">.</span><span class="n">tree</span><span class="p">)</span>
                        <span class="k">for</span> <span class="n">text_node</span> <span class="ow">in</span> <span class="n">iframe_text_nodes</span>
                    <span class="p">]</span>
                    <span class="n">items</span><span class="o">.</span><span class="n">extend</span><span class="p">(</span><span class="n">text_node</span><span class="o">.</span><span class="n">parent</span> <span class="k">for</span> <span class="n">text_node</span> <span class="ow">in</span> <span class="n">iframe_text_elems</span><span class="p">)</span>

        <span class="k">try</span><span class="p">:</span>
            <span class="k">if</span> <span class="ow">not</span> <span class="n">items</span><span class="p">:</span>
                <span class="k">return</span>
            <span class="k">if</span> <span class="n">best_match</span><span class="p">:</span>
                <span class="n">closest_by_length</span> <span class="o">=</span> <span class="nb">min</span><span class="p">(</span>
                    <span class="n">items</span><span class="p">,</span> <span class="n">key</span><span class="o">=</span><span class="k">lambda</span> <span class="n">el</span><span class="p">:</span> <span class="nb">abs</span><span class="p">(</span><span class="nb">len</span><span class="p">(</span><span class="n">text</span><span class="p">)</span> <span class="o">-</span> <span class="nb">len</span><span class="p">(</span><span class="n">el</span><span class="o">.</span><span class="n">text_all</span><span class="p">))</span>
                <span class="p">)</span>
                <span class="n">elem</span> <span class="o">=</span> <span class="n">closest_by_length</span> <span class="ow">or</span> <span class="n">items</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span>

                <span class="k">return</span> <span class="n">elem</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="c1"># naively just return the first result</span>
                <span class="k">for</span> <span class="n">elem</span> <span class="ow">in</span> <span class="n">items</span><span class="p">:</span>
                    <span class="k">if</span> <span class="n">elem</span><span class="p">:</span>
                        <span class="k">return</span> <span class="n">elem</span>
        <span class="k">finally</span><span class="p">:</span>
            <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">send</span><span class="p">(</span><span class="n">cdp</span><span class="o">.</span><span class="n">dom</span><span class="o">.</span><span class="n">disable</span><span class="p">())</span></div>


<div class="viewcode-block" id="Tab.back">
<a class="viewcode-back" href="../../../nodriver/classes/tab.html#nodriver.Tab.back">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">back</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        history back</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">send</span><span class="p">(</span><span class="n">cdp</span><span class="o">.</span><span class="n">runtime</span><span class="o">.</span><span class="n">evaluate</span><span class="p">(</span><span class="s2">&quot;window.history.back()&quot;</span><span class="p">))</span></div>


<div class="viewcode-block" id="Tab.forward">
<a class="viewcode-back" href="../../../nodriver/classes/tab.html#nodriver.Tab.forward">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">forward</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        history forward</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">send</span><span class="p">(</span><span class="n">cdp</span><span class="o">.</span><span class="n">runtime</span><span class="o">.</span><span class="n">evaluate</span><span class="p">(</span><span class="s2">&quot;window.history.forward()&quot;</span><span class="p">))</span></div>


<div class="viewcode-block" id="Tab.reload">
<a class="viewcode-back" href="../../../nodriver/classes/tab.html#nodriver.Tab.reload">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">reload</span><span class="p">(</span>
        <span class="bp">self</span><span class="p">,</span>
        <span class="n">ignore_cache</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">bool</span><span class="p">]</span> <span class="o">=</span> <span class="kc">True</span><span class="p">,</span>
        <span class="n">script_to_evaluate_on_load</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
    <span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Reloads the page</span>

<span class="sd">        :param ignore_cache: when set to True (default), it ignores cache, and re-downloads the items</span>
<span class="sd">        :type ignore_cache:</span>
<span class="sd">        :param script_to_evaluate_on_load: script to run on load. I actually haven&#39;t experimented with this one, so no guarantees.</span>
<span class="sd">        :type script_to_evaluate_on_load:</span>
<span class="sd">        :return:</span>
<span class="sd">        :rtype:</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">send</span><span class="p">(</span>
            <span class="n">cdp</span><span class="o">.</span><span class="n">page</span><span class="o">.</span><span class="n">reload</span><span class="p">(</span>
                <span class="n">ignore_cache</span><span class="o">=</span><span class="n">ignore_cache</span><span class="p">,</span>
                <span class="n">script_to_evaluate_on_load</span><span class="o">=</span><span class="n">script_to_evaluate_on_load</span><span class="p">,</span>
            <span class="p">),</span>
        <span class="p">)</span></div>


<div class="viewcode-block" id="Tab.evaluate">
<a class="viewcode-back" href="../../../nodriver/classes/tab.html#nodriver.Tab.evaluate">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">evaluate</span><span class="p">(</span>
        <span class="bp">self</span><span class="p">,</span> <span class="n">expression</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">await_promise</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span> <span class="n">return_by_value</span><span class="o">=</span><span class="kc">False</span>
    <span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Union</span><span class="p">[</span>
        <span class="nb">str</span><span class="p">,</span>
        <span class="n">Union</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="n">Any</span><span class="p">],</span>
        <span class="n">Tuple</span><span class="p">[</span><span class="n">cdp</span><span class="o">.</span><span class="n">runtime</span><span class="o">.</span><span class="n">RemoteObject</span><span class="p">,</span> <span class="n">cdp</span><span class="o">.</span><span class="n">runtime</span><span class="o">.</span><span class="n">ExceptionDetails</span> <span class="o">|</span> <span class="kc">None</span><span class="p">],</span>
    <span class="p">]:</span>

        <span class="n">ser</span> <span class="o">=</span> <span class="n">cdp</span><span class="o">.</span><span class="n">runtime</span><span class="o">.</span><span class="n">SerializationOptions</span><span class="p">(</span>
            <span class="n">serialization</span><span class="o">=</span><span class="s2">&quot;deep&quot;</span><span class="p">,</span>
            <span class="n">max_depth</span><span class="o">=</span><span class="mi">10</span><span class="p">,</span>
            <span class="n">additional_parameters</span><span class="o">=</span><span class="p">{</span><span class="s2">&quot;maxNodeDepth&quot;</span><span class="p">:</span> <span class="mi">10</span><span class="p">,</span> <span class="s2">&quot;includeShadowTree&quot;</span><span class="p">:</span> <span class="s2">&quot;all&quot;</span><span class="p">},</span>
        <span class="p">)</span>
        <span class="n">remote_object</span><span class="p">:</span> <span class="n">cdp</span><span class="o">.</span><span class="n">runtime</span><span class="o">.</span><span class="n">RemoteObject</span> <span class="o">=</span> <span class="kc">None</span>
        <span class="n">errors</span><span class="p">:</span> <span class="n">cdp</span><span class="o">.</span><span class="n">runtime</span><span class="o">.</span><span class="n">ExceptionDetails</span> <span class="o">=</span> <span class="kc">None</span>

        <span class="n">remote_object</span><span class="p">,</span> <span class="n">errors</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">send</span><span class="p">(</span>
            <span class="n">cdp</span><span class="o">.</span><span class="n">runtime</span><span class="o">.</span><span class="n">evaluate</span><span class="p">(</span>
                <span class="n">expression</span><span class="o">=</span><span class="n">expression</span><span class="p">,</span>
                <span class="n">user_gesture</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
                <span class="n">await_promise</span><span class="o">=</span><span class="n">await_promise</span><span class="p">,</span>
                <span class="n">return_by_value</span><span class="o">=</span><span class="n">return_by_value</span><span class="p">,</span>
                <span class="n">allow_unsafe_eval_blocked_by_csp</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
                <span class="n">serialization_options</span><span class="o">=</span><span class="n">ser</span><span class="p">,</span>
            <span class="p">)</span>
        <span class="p">)</span>
        <span class="k">if</span> <span class="n">errors</span><span class="p">:</span>
            <span class="k">return</span> <span class="n">errors</span>
        <span class="k">if</span> <span class="n">remote_object</span><span class="p">:</span>
            <span class="k">if</span> <span class="n">return_by_value</span><span class="p">:</span>
                <span class="k">if</span> <span class="n">remote_object</span><span class="o">.</span><span class="n">value</span><span class="p">:</span>
                    <span class="k">return</span> <span class="n">remote_object</span><span class="o">.</span><span class="n">value</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="k">if</span> <span class="n">remote_object</span><span class="o">.</span><span class="n">deep_serialized_value</span><span class="p">:</span>
                    <span class="k">return</span> <span class="n">remote_object</span><span class="o">.</span><span class="n">deep_serialized_value</span><span class="o">.</span><span class="n">value</span>

        <span class="k">return</span> <span class="n">remote_object</span></div>


<div class="viewcode-block" id="Tab.js_dumps">
<a class="viewcode-back" href="../../../nodriver/classes/tab.html#nodriver.Tab.js_dumps">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">js_dumps</span><span class="p">(</span>
        <span class="bp">self</span><span class="p">,</span> <span class="n">obj_name</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">return_by_value</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">bool</span><span class="p">]</span> <span class="o">=</span> <span class="kc">True</span>
    <span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Union</span><span class="p">[</span>
        <span class="n">typing</span><span class="o">.</span><span class="n">Dict</span><span class="p">,</span>
        <span class="n">typing</span><span class="o">.</span><span class="n">Tuple</span><span class="p">[</span><span class="n">cdp</span><span class="o">.</span><span class="n">runtime</span><span class="o">.</span><span class="n">RemoteObject</span><span class="p">,</span> <span class="n">cdp</span><span class="o">.</span><span class="n">runtime</span><span class="o">.</span><span class="n">ExceptionDetails</span><span class="p">],</span>
    <span class="p">]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        dump given js object with its properties and values as a dict</span>

<span class="sd">        note: complex objects might not be serializable, therefore this method is not a &quot;source of thruth&quot;</span>

<span class="sd">        :param obj_name: the js object to dump</span>
<span class="sd">        :type obj_name: str</span>

<span class="sd">        :param return_by_value: if you want an tuple of cdp objects (returnvalue, errors), set this to False</span>
<span class="sd">        :type return_by_value: bool</span>

<span class="sd">        example</span>
<span class="sd">        ------</span>

<span class="sd">        x = await self.js_dumps(&#39;window&#39;)</span>
<span class="sd">        print(x)</span>
<span class="sd">            &#39;...{</span>
<span class="sd">            &#39;pageYOffset&#39;: 0,</span>
<span class="sd">            &#39;visualViewport&#39;: {},</span>
<span class="sd">            &#39;screenX&#39;: 10,</span>
<span class="sd">            &#39;screenY&#39;: 10,</span>
<span class="sd">            &#39;outerWidth&#39;: 1050,</span>
<span class="sd">            &#39;outerHeight&#39;: 832,</span>
<span class="sd">            &#39;devicePixelRatio&#39;: 1,</span>
<span class="sd">            &#39;screenLeft&#39;: 10,</span>
<span class="sd">            &#39;screenTop&#39;: 10,</span>
<span class="sd">            &#39;styleMedia&#39;: {},</span>
<span class="sd">            &#39;onsearch&#39;: None,</span>
<span class="sd">            &#39;isSecureContext&#39;: True,</span>
<span class="sd">            &#39;trustedTypes&#39;: {},</span>
<span class="sd">            &#39;performance&#39;: {&#39;timeOrigin&#39;: 1707823094767.9,</span>
<span class="sd">            &#39;timing&#39;: {&#39;connectStart&#39;: 0,</span>
<span class="sd">            &#39;navigationStart&#39;: 1707823094768,</span>
<span class="sd">            ]...</span>
<span class="sd">            &#39;</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">js_code_a</span> <span class="o">=</span> <span class="p">(</span>
<span class="w">            </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">                                                   function ___dump(obj, _d = 0) {</span>
<span class="sd">                                                       let _typesA = [&#39;object&#39;, &#39;function&#39;];</span>
<span class="sd">                                                       let _typesB = [&#39;number&#39;, &#39;string&#39;, &#39;boolean&#39;];</span>
<span class="sd">                                                       if (_d == 2) {</span>
<span class="sd">                                                           // console.log(&#39;maxdepth reached for &#39;, obj);</span>
<span class="sd">                                                           return</span>
<span class="sd">                                                       }</span>
<span class="sd">                                                       let tmp = {}</span>
<span class="sd">                                                       for (let k in obj) {</span>
<span class="sd">                                                           if (obj[k] == window) continue;</span>
<span class="sd">                                                           let v;</span>
<span class="sd">                                                           try {</span>
<span class="sd">                                                               if (obj[k] === null || obj[k] === undefined || obj[k] === NaN) {</span>
<span class="sd">                                                                    // console.log(&#39;obj[k] is null or undefined or Nan&#39;, k, &#39;=&gt;&#39;, obj[k])</span>
<span class="sd">                                                                   tmp[k] = obj[k];</span>
<span class="sd">                                                                   continue</span>
<span class="sd">                                                               }</span>
<span class="sd">                                                           } catch (e) {</span>
<span class="sd">                                                               tmp[k] = null;</span>
<span class="sd">                                                               continue</span>
<span class="sd">                                                           }</span>
<span class="sd">                        </span>
<span class="sd">                                                           if (_typesB.includes(typeof obj[k])) {</span>
<span class="sd">                                                               tmp[k] = obj[k]</span>
<span class="sd">                                                               continue</span>
<span class="sd">                                                           }</span>
<span class="sd">                        </span>
<span class="sd">                                                           try {</span>
<span class="sd">                                                               if (typeof obj[k] === &#39;function&#39;) {</span>
<span class="sd">                                                                   tmp[k] = obj[k].toString()</span>
<span class="sd">                                                                   continue</span>
<span class="sd">                                                               }</span>
<span class="sd">                        </span>
<span class="sd">                        </span>
<span class="sd">                                                               if (typeof obj[k] === &#39;object&#39;) {</span>
<span class="sd">                                                                   tmp[k] = ___dump(obj[k], _d + 1);</span>
<span class="sd">                                                                   continue</span>
<span class="sd">                                                               }</span>
<span class="sd">                        </span>
<span class="sd">                        </span>
<span class="sd">                                                           } catch (e) {}</span>
<span class="sd">                        </span>
<span class="sd">                                                           try {</span>
<span class="sd">                                                               tmp[k] = JSON.stringify(obj[k])</span>
<span class="sd">                                                               continue</span>
<span class="sd">                                                           } catch (e) {</span>
<span class="sd">                        </span>
<span class="sd">                                                           }</span>
<span class="sd">                                                           try {</span>
<span class="sd">                                                               tmp[k] = obj[k].toString();</span>
<span class="sd">                                                               continue</span>
<span class="sd">                                                           } catch (e) {}</span>
<span class="sd">                                                       }</span>
<span class="sd">                                                       return tmp</span>
<span class="sd">                                                   }</span>
<span class="sd">                        </span>
<span class="sd">                                                   function ___dumpY(obj) {</span>
<span class="sd">                                                       var objKeys = (obj) =&gt; {</span>
<span class="sd">                                                           var [target, result] = [obj, []];</span>
<span class="sd">                                                           while (target !== null) {</span>
<span class="sd">                                                               result = result.concat(Object.getOwnPropertyNames(target));</span>
<span class="sd">                                                               target = Object.getPrototypeOf(target);</span>
<span class="sd">                                                           }</span>
<span class="sd">                                                           return result;</span>
<span class="sd">                                                       }</span>
<span class="sd">                                                       return Object.fromEntries(</span>
<span class="sd">                                                           objKeys(obj).map(_ =&gt; [_, ___dump(obj[_])]))</span>
<span class="sd">                        </span>
<span class="sd">                                                   }</span>
<span class="sd">                                                   ___dumpY( %s )</span>
<span class="sd">                                           &quot;&quot;&quot;</span>
            <span class="o">%</span> <span class="n">obj_name</span>
        <span class="p">)</span>
        <span class="n">js_code_b</span> <span class="o">=</span> <span class="p">(</span>
<span class="w">            </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">                                    ((obj, visited = new WeakSet()) =&gt; {</span>
<span class="sd">                                         if (visited.has(obj)) {</span>
<span class="sd">                                             return {}</span>
<span class="sd">                                         }</span>
<span class="sd">                                         visited.add(obj)</span>
<span class="sd">                                         var result = {}, _tmp;</span>
<span class="sd">                                         for (var i in obj) {</span>
<span class="sd">                                                 try {</span>
<span class="sd">                                                     if (i === &#39;enabledPlugin&#39; || typeof obj[i] === &#39;function&#39;) {</span>
<span class="sd">                                                         continue;</span>
<span class="sd">                                                     } else if (typeof obj[i] === &#39;object&#39;) {</span>
<span class="sd">                                                         _tmp = recurse(obj[i], visited);</span>
<span class="sd">                                                         if (Object.keys(_tmp).length) {</span>
<span class="sd">                                                             result[i] = _tmp;</span>
<span class="sd">                                                         }</span>
<span class="sd">                                                     } else {</span>
<span class="sd">                                                         result[i] = obj[i];</span>
<span class="sd">                                                     }</span>
<span class="sd">                                                 } catch (error) {</span>
<span class="sd">                                                     // console.error(&#39;Error:&#39;, error);</span>
<span class="sd">                                                 }</span>
<span class="sd">                                             }</span>
<span class="sd">                                        return result;</span>
<span class="sd">                                    })(%s)</span>
<span class="sd">                                &quot;&quot;&quot;</span>
            <span class="o">%</span> <span class="n">obj_name</span>
        <span class="p">)</span>

        <span class="c1"># we&#39;re purposely not calling self.evaluate here to prevent infinite loop on certain expressions</span>

        <span class="n">remote_object</span><span class="p">,</span> <span class="n">exception_details</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">send</span><span class="p">(</span>
            <span class="n">cdp</span><span class="o">.</span><span class="n">runtime</span><span class="o">.</span><span class="n">evaluate</span><span class="p">(</span>
                <span class="n">js_code_a</span><span class="p">,</span>
                <span class="n">await_promise</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
                <span class="n">return_by_value</span><span class="o">=</span><span class="n">return_by_value</span><span class="p">,</span>
                <span class="n">allow_unsafe_eval_blocked_by_csp</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
            <span class="p">)</span>
        <span class="p">)</span>
        <span class="k">if</span> <span class="n">exception_details</span><span class="p">:</span>
            <span class="c1"># try second variant</span>

            <span class="n">remote_object</span><span class="p">,</span> <span class="n">exception_details</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">send</span><span class="p">(</span>
                <span class="n">cdp</span><span class="o">.</span><span class="n">runtime</span><span class="o">.</span><span class="n">evaluate</span><span class="p">(</span>
                    <span class="n">js_code_b</span><span class="p">,</span>
                    <span class="n">await_promise</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
                    <span class="n">return_by_value</span><span class="o">=</span><span class="n">return_by_value</span><span class="p">,</span>
                    <span class="n">allow_unsafe_eval_blocked_by_csp</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
                <span class="p">)</span>
            <span class="p">)</span>

        <span class="k">if</span> <span class="n">exception_details</span><span class="p">:</span>
            <span class="k">raise</span> <span class="n">ProtocolException</span><span class="p">(</span><span class="n">exception_details</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">return_by_value</span><span class="p">:</span>
            <span class="k">if</span> <span class="n">remote_object</span><span class="o">.</span><span class="n">value</span><span class="p">:</span>
                <span class="k">return</span> <span class="n">remote_object</span><span class="o">.</span><span class="n">value</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="k">return</span> <span class="n">remote_object</span><span class="p">,</span> <span class="n">exception_details</span></div>


<div class="viewcode-block" id="Tab.close">
<a class="viewcode-back" href="../../../nodriver/classes/tab.html#nodriver.Tab.close">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">close</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        close the current target (ie: tab,window,page)</span>
<span class="sd">        :return:</span>
<span class="sd">        :rtype:</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">target</span> <span class="ow">and</span> <span class="bp">self</span><span class="o">.</span><span class="n">target</span><span class="o">.</span><span class="n">target_id</span><span class="p">:</span>
            <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">send</span><span class="p">(</span><span class="n">cdp</span><span class="o">.</span><span class="n">target</span><span class="o">.</span><span class="n">close_target</span><span class="p">(</span><span class="n">target_id</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">target</span><span class="o">.</span><span class="n">target_id</span><span class="p">))</span></div>


<div class="viewcode-block" id="Tab.get_window">
<a class="viewcode-back" href="../../../nodriver/classes/tab.html#nodriver.Tab.get_window">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">get_window</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">cdp</span><span class="o">.</span><span class="n">browser</span><span class="o">.</span><span class="n">WindowID</span><span class="p">,</span> <span class="n">cdp</span><span class="o">.</span><span class="n">browser</span><span class="o">.</span><span class="n">Bounds</span><span class="p">]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        get the window Bounds</span>
<span class="sd">        :return:</span>
<span class="sd">        :rtype:</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">window_id</span><span class="p">,</span> <span class="n">bounds</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">send</span><span class="p">(</span>
            <span class="n">cdp</span><span class="o">.</span><span class="n">browser</span><span class="o">.</span><span class="n">get_window_for_target</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">target_id</span><span class="p">)</span>
        <span class="p">)</span>
        <span class="k">return</span> <span class="n">window_id</span><span class="p">,</span> <span class="n">bounds</span></div>


<div class="viewcode-block" id="Tab.get_content">
<a class="viewcode-back" href="../../../nodriver/classes/tab.html#nodriver.Tab.get_content">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">get_content</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        gets the current page source content (html)</span>
<span class="sd">        :return:</span>
<span class="sd">        :rtype:</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">doc</span><span class="p">:</span> <span class="n">cdp</span><span class="o">.</span><span class="n">dom</span><span class="o">.</span><span class="n">Node</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">send</span><span class="p">(</span><span class="n">cdp</span><span class="o">.</span><span class="n">dom</span><span class="o">.</span><span class="n">get_document</span><span class="p">(</span><span class="o">-</span><span class="mi">1</span><span class="p">,</span> <span class="kc">True</span><span class="p">))</span>
        <span class="k">return</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">send</span><span class="p">(</span>
            <span class="n">cdp</span><span class="o">.</span><span class="n">dom</span><span class="o">.</span><span class="n">get_outer_html</span><span class="p">(</span><span class="n">backend_node_id</span><span class="o">=</span><span class="n">doc</span><span class="o">.</span><span class="n">backend_node_id</span><span class="p">)</span>
        <span class="p">)</span></div>


<div class="viewcode-block" id="Tab.maximize">
<a class="viewcode-back" href="../../../nodriver/classes/tab.html#nodriver.Tab.maximize">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">maximize</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        maximize page/tab/window</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">return</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">set_window_state</span><span class="p">(</span><span class="n">state</span><span class="o">=</span><span class="s2">&quot;maximize&quot;</span><span class="p">)</span></div>


<div class="viewcode-block" id="Tab.minimize">
<a class="viewcode-back" href="../../../nodriver/classes/tab.html#nodriver.Tab.minimize">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">minimize</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        minimize page/tab/window</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">return</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">set_window_state</span><span class="p">(</span><span class="n">state</span><span class="o">=</span><span class="s2">&quot;minimize&quot;</span><span class="p">)</span></div>


<div class="viewcode-block" id="Tab.fullscreen">
<a class="viewcode-back" href="../../../nodriver/classes/tab.html#nodriver.Tab.fullscreen">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">fullscreen</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        minimize page/tab/window</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">return</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">set_window_state</span><span class="p">(</span><span class="n">state</span><span class="o">=</span><span class="s2">&quot;fullscreen&quot;</span><span class="p">)</span></div>


<div class="viewcode-block" id="Tab.medimize">
<a class="viewcode-back" href="../../../nodriver/classes/tab.html#nodriver.Tab.medimize">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">medimize</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">set_window_state</span><span class="p">(</span><span class="n">state</span><span class="o">=</span><span class="s2">&quot;normal&quot;</span><span class="p">)</span></div>


<div class="viewcode-block" id="Tab.set_window_size">
<a class="viewcode-back" href="../../../nodriver/classes/tab.html#nodriver.Tab.set_window_size">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">set_window_size</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">left</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span> <span class="n">top</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span> <span class="n">width</span><span class="o">=</span><span class="mi">1280</span><span class="p">,</span> <span class="n">height</span><span class="o">=</span><span class="mi">1024</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        set window size and position</span>

<span class="sd">        :param left: pixels from the left of the screen to the window top-left corner</span>
<span class="sd">        :type left:</span>
<span class="sd">        :param top: pixels from the top of the screen to the window top-left corner</span>
<span class="sd">        :type top:</span>
<span class="sd">        :param width: width of the window in pixels</span>
<span class="sd">        :type width:</span>
<span class="sd">        :param height: height of the window in pixels</span>
<span class="sd">        :type height:</span>
<span class="sd">        :return:</span>
<span class="sd">        :rtype:</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">return</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">set_window_state</span><span class="p">(</span><span class="n">left</span><span class="p">,</span> <span class="n">top</span><span class="p">,</span> <span class="n">width</span><span class="p">,</span> <span class="n">height</span><span class="p">)</span></div>


<div class="viewcode-block" id="Tab.activate">
<a class="viewcode-back" href="../../../nodriver/classes/tab.html#nodriver.Tab.activate">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">activate</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        active this target (ie: tab,window,page)</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">send</span><span class="p">(</span><span class="n">cdp</span><span class="o">.</span><span class="n">target</span><span class="o">.</span><span class="n">activate_target</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">target</span><span class="o">.</span><span class="n">target_id</span><span class="p">))</span></div>


<div class="viewcode-block" id="Tab.bring_to_front">
<a class="viewcode-back" href="../../../nodriver/classes/tab.html#nodriver.Tab.bring_to_front">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">bring_to_front</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        alias to self.activate</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">activate</span><span class="p">()</span></div>


<div class="viewcode-block" id="Tab.set_window_state">
<a class="viewcode-back" href="../../../nodriver/classes/tab.html#nodriver.Tab.set_window_state">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">set_window_state</span><span class="p">(</span>
        <span class="bp">self</span><span class="p">,</span> <span class="n">left</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span> <span class="n">top</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span> <span class="n">width</span><span class="o">=</span><span class="mi">1280</span><span class="p">,</span> <span class="n">height</span><span class="o">=</span><span class="mi">720</span><span class="p">,</span> <span class="n">state</span><span class="o">=</span><span class="s2">&quot;normal&quot;</span>
    <span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        sets the window size or state.</span>

<span class="sd">        for state you can provide the full name like minimized, maximized, normal, fullscreen, or</span>
<span class="sd">        something which leads to either of those, like min, mini, mi,  max, ma, maxi, full, fu, no, nor</span>
<span class="sd">        in case state is set other than &quot;normal&quot;, the left, top, width, and height are ignored.</span>

<span class="sd">        :param left:</span>
<span class="sd">            desired offset from left, in pixels</span>
<span class="sd">        :type left: int</span>

<span class="sd">        :param top:</span>
<span class="sd">            desired offset from the top, in pixels</span>
<span class="sd">        :type top: int</span>

<span class="sd">        :param width:</span>
<span class="sd">            desired width in pixels</span>
<span class="sd">        :type width: int</span>

<span class="sd">        :param height:</span>
<span class="sd">            desired height in pixels</span>
<span class="sd">        :type height: int</span>

<span class="sd">        :param state:</span>
<span class="sd">            can be one of the following strings:</span>
<span class="sd">                - normal</span>
<span class="sd">                - fullscreen</span>
<span class="sd">                - maximized</span>
<span class="sd">                - minimized</span>

<span class="sd">        :type state: str</span>

<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">available_states</span> <span class="o">=</span> <span class="p">[</span><span class="s2">&quot;minimized&quot;</span><span class="p">,</span> <span class="s2">&quot;maximized&quot;</span><span class="p">,</span> <span class="s2">&quot;fullscreen&quot;</span><span class="p">,</span> <span class="s2">&quot;normal&quot;</span><span class="p">]</span>
        <span class="n">window_id</span><span class="p">:</span> <span class="n">cdp</span><span class="o">.</span><span class="n">browser</span><span class="o">.</span><span class="n">WindowID</span>
        <span class="n">bounds</span><span class="p">:</span> <span class="n">cdp</span><span class="o">.</span><span class="n">browser</span><span class="o">.</span><span class="n">Bounds</span>
        <span class="p">(</span><span class="n">window_id</span><span class="p">,</span> <span class="n">bounds</span><span class="p">)</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_window</span><span class="p">()</span>

        <span class="k">for</span> <span class="n">state_name</span> <span class="ow">in</span> <span class="n">available_states</span><span class="p">:</span>
            <span class="k">if</span> <span class="nb">all</span><span class="p">(</span><span class="n">x</span> <span class="ow">in</span> <span class="n">state_name</span> <span class="k">for</span> <span class="n">x</span> <span class="ow">in</span> <span class="n">state</span><span class="o">.</span><span class="n">lower</span><span class="p">()):</span>
                <span class="k">break</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">NameError</span><span class="p">(</span>
                <span class="s2">&quot;could not determine any of </span><span class="si">%s</span><span class="s2"> from input &#39;</span><span class="si">%s</span><span class="s2">&#39;&quot;</span>
                <span class="o">%</span> <span class="p">(</span><span class="s2">&quot;,&quot;</span><span class="o">.</span><span class="n">join</span><span class="p">(</span><span class="n">available_states</span><span class="p">),</span> <span class="n">state</span><span class="p">)</span>
            <span class="p">)</span>
        <span class="n">window_state</span> <span class="o">=</span> <span class="nb">getattr</span><span class="p">(</span>
            <span class="n">cdp</span><span class="o">.</span><span class="n">browser</span><span class="o">.</span><span class="n">WindowState</span><span class="p">,</span> <span class="n">state_name</span><span class="o">.</span><span class="n">upper</span><span class="p">(),</span> <span class="n">cdp</span><span class="o">.</span><span class="n">browser</span><span class="o">.</span><span class="n">WindowState</span><span class="o">.</span><span class="n">NORMAL</span>
        <span class="p">)</span>
        <span class="k">if</span> <span class="n">window_state</span> <span class="o">==</span> <span class="n">cdp</span><span class="o">.</span><span class="n">browser</span><span class="o">.</span><span class="n">WindowState</span><span class="o">.</span><span class="n">NORMAL</span><span class="p">:</span>
            <span class="n">bounds</span> <span class="o">=</span> <span class="n">cdp</span><span class="o">.</span><span class="n">browser</span><span class="o">.</span><span class="n">Bounds</span><span class="p">(</span><span class="n">left</span><span class="p">,</span> <span class="n">top</span><span class="p">,</span> <span class="n">width</span><span class="p">,</span> <span class="n">height</span><span class="p">,</span> <span class="n">window_state</span><span class="p">)</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="c1"># min, max, full can only be used when current state == NORMAL</span>
            <span class="c1"># therefore we first switch to NORMAL</span>
            <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">set_window_state</span><span class="p">(</span><span class="n">state</span><span class="o">=</span><span class="s2">&quot;normal&quot;</span><span class="p">)</span>
            <span class="n">bounds</span> <span class="o">=</span> <span class="n">cdp</span><span class="o">.</span><span class="n">browser</span><span class="o">.</span><span class="n">Bounds</span><span class="p">(</span><span class="n">window_state</span><span class="o">=</span><span class="n">window_state</span><span class="p">)</span>

        <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">send</span><span class="p">(</span><span class="n">cdp</span><span class="o">.</span><span class="n">browser</span><span class="o">.</span><span class="n">set_window_bounds</span><span class="p">(</span><span class="n">window_id</span><span class="p">,</span> <span class="n">bounds</span><span class="o">=</span><span class="n">bounds</span><span class="p">))</span></div>


<div class="viewcode-block" id="Tab.scroll_down">
<a class="viewcode-back" href="../../../nodriver/classes/tab.html#nodriver.Tab.scroll_down">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">scroll_down</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">amount</span><span class="o">=</span><span class="mi">25</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        scrolls down maybe</span>

<span class="sd">        :param amount: number in percentage. 25 is a quarter of page, 50 half, and 1000 is 10x the page</span>
<span class="sd">        :type amount: int</span>
<span class="sd">        :return:</span>
<span class="sd">        :rtype:</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">window_id</span><span class="p">:</span> <span class="n">cdp</span><span class="o">.</span><span class="n">browser</span><span class="o">.</span><span class="n">WindowID</span>
        <span class="n">bounds</span><span class="p">:</span> <span class="n">cdp</span><span class="o">.</span><span class="n">browser</span><span class="o">.</span><span class="n">Bounds</span>
        <span class="p">(</span><span class="n">window_id</span><span class="p">,</span> <span class="n">bounds</span><span class="p">)</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_window</span><span class="p">()</span>

        <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">send</span><span class="p">(</span>
            <span class="n">cdp</span><span class="o">.</span><span class="n">input_</span><span class="o">.</span><span class="n">synthesize_scroll_gesture</span><span class="p">(</span>
                <span class="n">x</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span>
                <span class="n">y</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span>
                <span class="n">y_distance</span><span class="o">=-</span><span class="p">(</span><span class="n">bounds</span><span class="o">.</span><span class="n">height</span> <span class="o">*</span> <span class="p">(</span><span class="n">amount</span> <span class="o">/</span> <span class="mi">100</span><span class="p">)),</span>
                <span class="n">y_overscroll</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span>
                <span class="n">x_overscroll</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span>
                <span class="n">prevent_fling</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
                <span class="n">repeat_delay_ms</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span>
                <span class="n">speed</span><span class="o">=</span><span class="mi">7777</span><span class="p">,</span>
            <span class="p">)</span>
        <span class="p">)</span></div>


<div class="viewcode-block" id="Tab.scroll_up">
<a class="viewcode-back" href="../../../nodriver/classes/tab.html#nodriver.Tab.scroll_up">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">scroll_up</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">amount</span><span class="o">=</span><span class="mi">25</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        scrolls up maybe</span>

<span class="sd">        :param amount: number in percentage. 25 is a quarter of page, 50 half, and 1000 is 10x the page</span>
<span class="sd">        :type amount: int</span>

<span class="sd">        :return:</span>
<span class="sd">        :rtype:</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">window_id</span><span class="p">:</span> <span class="n">cdp</span><span class="o">.</span><span class="n">browser</span><span class="o">.</span><span class="n">WindowID</span>
        <span class="n">bounds</span><span class="p">:</span> <span class="n">cdp</span><span class="o">.</span><span class="n">browser</span><span class="o">.</span><span class="n">Bounds</span>
        <span class="p">(</span><span class="n">window_id</span><span class="p">,</span> <span class="n">bounds</span><span class="p">)</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_window</span><span class="p">()</span>

        <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">send</span><span class="p">(</span>
            <span class="n">cdp</span><span class="o">.</span><span class="n">input_</span><span class="o">.</span><span class="n">synthesize_scroll_gesture</span><span class="p">(</span>
                <span class="n">x</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span>
                <span class="n">y</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span>
                <span class="n">y_distance</span><span class="o">=</span><span class="p">(</span><span class="n">bounds</span><span class="o">.</span><span class="n">height</span> <span class="o">*</span> <span class="p">(</span><span class="n">amount</span> <span class="o">/</span> <span class="mi">100</span><span class="p">)),</span>
                <span class="n">x_overscroll</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span>
                <span class="n">prevent_fling</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
                <span class="n">repeat_delay_ms</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span>
                <span class="n">speed</span><span class="o">=</span><span class="mi">7777</span><span class="p">,</span>
            <span class="p">)</span>
        <span class="p">)</span></div>


<div class="viewcode-block" id="Tab.wait">
<a class="viewcode-back" href="../../../nodriver/classes/tab.html#nodriver.Tab.wait">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">wait</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">t</span><span class="p">:</span> <span class="n">Union</span><span class="p">[</span><span class="nb">int</span><span class="p">,</span> <span class="nb">float</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">):</span>

        <span class="c1"># await self.browser.wait()</span>

        <span class="n">loop</span> <span class="o">=</span> <span class="n">asyncio</span><span class="o">.</span><span class="n">get_running_loop</span><span class="p">()</span>
        <span class="n">start</span> <span class="o">=</span> <span class="n">loop</span><span class="o">.</span><span class="n">time</span><span class="p">()</span>
        <span class="n">event</span> <span class="o">=</span> <span class="n">asyncio</span><span class="o">.</span><span class="n">Event</span><span class="p">()</span>
        <span class="n">wait_events</span> <span class="o">=</span> <span class="p">[</span>
            <span class="n">cdp</span><span class="o">.</span><span class="n">page</span><span class="o">.</span><span class="n">FrameStoppedLoading</span><span class="p">,</span>
            <span class="n">cdp</span><span class="o">.</span><span class="n">page</span><span class="o">.</span><span class="n">FrameDetached</span><span class="p">,</span>
            <span class="n">cdp</span><span class="o">.</span><span class="n">page</span><span class="o">.</span><span class="n">FrameNavigated</span><span class="p">,</span>
            <span class="n">cdp</span><span class="o">.</span><span class="n">page</span><span class="o">.</span><span class="n">LifecycleEvent</span><span class="p">,</span>
            <span class="n">cdp</span><span class="o">.</span><span class="n">page</span><span class="o">.</span><span class="n">LoadEventFired</span><span class="p">,</span>
        <span class="p">]</span>

        <span class="n">handler</span> <span class="o">=</span> <span class="k">lambda</span> <span class="n">ev</span><span class="p">:</span> <span class="n">event</span><span class="o">.</span><span class="n">set</span><span class="p">()</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">add_handler</span><span class="p">(</span><span class="n">wait_events</span><span class="p">,</span> <span class="n">handler</span><span class="o">=</span><span class="n">handler</span><span class="p">)</span>
        <span class="k">try</span><span class="p">:</span>
            <span class="k">if</span> <span class="ow">not</span> <span class="n">t</span><span class="p">:</span>
                <span class="n">t</span> <span class="o">=</span> <span class="mf">0.5</span>
                <span class="n">done</span><span class="p">,</span> <span class="n">pending</span> <span class="o">=</span> <span class="k">await</span> <span class="n">asyncio</span><span class="o">.</span><span class="n">wait</span><span class="p">(</span>
                    <span class="p">[</span>
                        <span class="n">asyncio</span><span class="o">.</span><span class="n">ensure_future</span><span class="p">(</span><span class="n">event</span><span class="o">.</span><span class="n">wait</span><span class="p">()),</span>
                        <span class="n">asyncio</span><span class="o">.</span><span class="n">ensure_future</span><span class="p">(</span><span class="n">asyncio</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="n">t</span><span class="p">)),</span>
                    <span class="p">],</span>
                    <span class="n">return_when</span><span class="o">=</span><span class="n">asyncio</span><span class="o">.</span><span class="n">FIRST_COMPLETED</span><span class="p">,</span>
                <span class="p">)</span>

                <span class="p">[</span><span class="n">p</span><span class="o">.</span><span class="n">cancel</span><span class="p">()</span> <span class="k">for</span> <span class="n">p</span> <span class="ow">in</span> <span class="n">pending</span><span class="p">]</span>

        <span class="k">finally</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">remove_handler</span><span class="p">(</span><span class="n">wait_events</span><span class="p">,</span> <span class="n">handler</span><span class="o">=</span><span class="n">handler</span><span class="p">)</span></div>

        <span class="c1">#         await asyncio.wait_for()</span>
        <span class="c1">#     except asyncio.TimeoutError:</span>
        <span class="c1">#         if isinstance(t, (int, float)):</span>
        <span class="c1">#             # explicit time is given, which is now passed</span>
        <span class="c1">#             # so bail out early</span>
        <span class="c1">#             return</span>

    <span class="k">def</span> <span class="fm">__await__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">wait</span><span class="p">()</span><span class="o">.</span><span class="fm">__await__</span><span class="p">()</span>

<div class="viewcode-block" id="Tab.wait_for">
<a class="viewcode-back" href="../../../nodriver/classes/tab.html#nodriver.Tab.wait_for">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">wait_for</span><span class="p">(</span>
        <span class="bp">self</span><span class="p">,</span>
        <span class="n">selector</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="s2">&quot;&quot;</span><span class="p">,</span>
        <span class="n">text</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="s2">&quot;&quot;</span><span class="p">,</span>
        <span class="n">timeout</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">Union</span><span class="p">[</span><span class="nb">int</span><span class="p">,</span> <span class="nb">float</span><span class="p">]]</span> <span class="o">=</span> <span class="mi">10</span><span class="p">,</span>
    <span class="p">)</span> <span class="o">-&gt;</span> <span class="n">element</span><span class="o">.</span><span class="n">Element</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        variant on query_selector_all and find_elements_by_text</span>
<span class="sd">        this variant takes either selector or text, and will block until</span>
<span class="sd">        the requested element(s) are found.</span>

<span class="sd">        it will block for a maximum of &lt;timeout&gt; seconds, after which</span>
<span class="sd">        an TimeoutError will be raised</span>

<span class="sd">        :param selector: css selector</span>
<span class="sd">        :type selector:</span>
<span class="sd">        :param text: text</span>
<span class="sd">        :type text:</span>
<span class="sd">        :param timeout:</span>
<span class="sd">        :type timeout:</span>
<span class="sd">        :return:</span>
<span class="sd">        :rtype: Element</span>
<span class="sd">        :raises: asyncio.TimeoutError</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">loop</span> <span class="o">=</span> <span class="n">asyncio</span><span class="o">.</span><span class="n">get_running_loop</span><span class="p">()</span>
        <span class="n">now</span> <span class="o">=</span> <span class="n">loop</span><span class="o">.</span><span class="n">time</span><span class="p">()</span>
        <span class="k">if</span> <span class="n">selector</span><span class="p">:</span>
            <span class="n">item</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">query_selector</span><span class="p">(</span><span class="n">selector</span><span class="p">)</span>
            <span class="k">while</span> <span class="ow">not</span> <span class="n">item</span><span class="p">:</span>
                <span class="n">item</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">query_selector</span><span class="p">(</span><span class="n">selector</span><span class="p">)</span>
                <span class="k">if</span> <span class="n">loop</span><span class="o">.</span><span class="n">time</span><span class="p">()</span> <span class="o">-</span> <span class="n">now</span> <span class="o">&gt;</span> <span class="n">timeout</span><span class="p">:</span>
                    <span class="k">raise</span> <span class="n">asyncio</span><span class="o">.</span><span class="n">TimeoutError</span><span class="p">(</span>
                        <span class="s2">&quot;time ran out while waiting for </span><span class="si">%s</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="n">selector</span>
                    <span class="p">)</span>
                <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.5</span><span class="p">)</span>
                <span class="c1"># await self.sleep(0.5)</span>
            <span class="k">return</span> <span class="n">item</span>
        <span class="k">if</span> <span class="n">text</span><span class="p">:</span>
            <span class="n">item</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">find_element_by_text</span><span class="p">(</span><span class="n">text</span><span class="p">)</span>
            <span class="k">while</span> <span class="ow">not</span> <span class="n">item</span><span class="p">:</span>
                <span class="n">item</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">find_element_by_text</span><span class="p">(</span><span class="n">text</span><span class="p">)</span>
                <span class="k">if</span> <span class="n">loop</span><span class="o">.</span><span class="n">time</span><span class="p">()</span> <span class="o">-</span> <span class="n">now</span> <span class="o">&gt;</span> <span class="n">timeout</span><span class="p">:</span>
                    <span class="k">raise</span> <span class="n">asyncio</span><span class="o">.</span><span class="n">TimeoutError</span><span class="p">(</span>
                        <span class="s2">&quot;time ran out while waiting for text: </span><span class="si">%s</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="n">text</span>
                    <span class="p">)</span>
                <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.5</span><span class="p">)</span>
            <span class="k">return</span> <span class="n">item</span></div>


<div class="viewcode-block" id="Tab.download_file">
<a class="viewcode-back" href="../../../nodriver/classes/tab.html#nodriver.Tab.download_file">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">download_file</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">url</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">filename</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">PathLike</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        downloads file by given url.</span>

<span class="sd">        :param url: url of the file</span>
<span class="sd">        :param filename: the name for the file. if not specified the name is composed from the url file name</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">_download_behavior</span><span class="p">:</span>
            <span class="n">directory_path</span> <span class="o">=</span> <span class="n">pathlib</span><span class="o">.</span><span class="n">Path</span><span class="o">.</span><span class="n">cwd</span><span class="p">()</span> <span class="o">/</span> <span class="s2">&quot;downloads&quot;</span>
            <span class="n">directory_path</span><span class="o">.</span><span class="n">mkdir</span><span class="p">(</span><span class="n">exist_ok</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
            <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">set_download_path</span><span class="p">(</span><span class="n">directory_path</span><span class="p">)</span>

            <span class="n">warnings</span><span class="o">.</span><span class="n">warn</span><span class="p">(</span>
                <span class="sa">f</span><span class="s2">&quot;no download path set, so creating and using a default of&quot;</span>
                <span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">directory_path</span><span class="si">}</span><span class="s2">&quot;</span>
            <span class="p">)</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="n">filename</span><span class="p">:</span>
            <span class="n">filename</span> <span class="o">=</span> <span class="n">url</span><span class="o">.</span><span class="n">rsplit</span><span class="p">(</span><span class="s2">&quot;/&quot;</span><span class="p">)[</span><span class="o">-</span><span class="mi">1</span><span class="p">]</span>
            <span class="n">filename</span> <span class="o">=</span> <span class="n">filename</span><span class="o">.</span><span class="n">split</span><span class="p">(</span><span class="s2">&quot;?&quot;</span><span class="p">)[</span><span class="mi">0</span><span class="p">]</span>

        <span class="n">code</span> <span class="o">=</span> <span class="s2">&quot;&quot;&quot;</span>
<span class="s2">         (elem) =&gt; {</span>
<span class="s2">            async function _downloadFile(</span>
<span class="s2">              imageSrc,</span>
<span class="s2">              nameOfDownload,</span>
<span class="s2">            ) {</span>
<span class="s2">              const response = await fetch(imageSrc);</span>
<span class="s2">              const blobImage = await response.blob();</span>
<span class="s2">              const href = URL.createObjectURL(blobImage);</span>

<span class="s2">              const anchorElement = document.createElement(&#39;a&#39;);</span>
<span class="s2">              anchorElement.href = href;</span>
<span class="s2">              anchorElement.download = nameOfDownload;</span>

<span class="s2">              document.body.appendChild(anchorElement);</span>
<span class="s2">              anchorElement.click();</span>

<span class="s2">              setTimeout(() =&gt; {</span>
<span class="s2">                document.body.removeChild(anchorElement);</span>
<span class="s2">                window.URL.revokeObjectURL(href);</span>
<span class="s2">                }, 500);</span>
<span class="s2">            }</span>
<span class="s2">            _downloadFile(&#39;</span><span class="si">%s</span><span class="s2">&#39;, &#39;</span><span class="si">%s</span><span class="s2">&#39;)</span>
<span class="s2">            }</span>
<span class="s2">            &quot;&quot;&quot;</span> <span class="o">%</span> <span class="p">(</span>
            <span class="n">url</span><span class="p">,</span>
            <span class="n">filename</span><span class="p">,</span>
        <span class="p">)</span>

        <span class="n">body</span> <span class="o">=</span> <span class="p">(</span><span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">query_selector_all</span><span class="p">(</span><span class="s2">&quot;body&quot;</span><span class="p">))[</span><span class="mi">0</span><span class="p">]</span>
        <span class="k">await</span> <span class="n">body</span><span class="o">.</span><span class="n">update</span><span class="p">()</span>
        <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">send</span><span class="p">(</span>
            <span class="n">cdp</span><span class="o">.</span><span class="n">runtime</span><span class="o">.</span><span class="n">call_function_on</span><span class="p">(</span>
                <span class="n">code</span><span class="p">,</span>
                <span class="n">object_id</span><span class="o">=</span><span class="n">body</span><span class="o">.</span><span class="n">object_id</span><span class="p">,</span>
                <span class="n">arguments</span><span class="o">=</span><span class="p">[</span><span class="n">cdp</span><span class="o">.</span><span class="n">runtime</span><span class="o">.</span><span class="n">CallArgument</span><span class="p">(</span><span class="n">object_id</span><span class="o">=</span><span class="n">body</span><span class="o">.</span><span class="n">object_id</span><span class="p">)],</span>
            <span class="p">)</span>
        <span class="p">)</span>
        <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">wait</span><span class="p">(</span><span class="mf">0.1</span><span class="p">)</span></div>


<div class="viewcode-block" id="Tab.save_screenshot">
<a class="viewcode-back" href="../../../nodriver/classes/tab.html#nodriver.Tab.save_screenshot">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">save_screenshot</span><span class="p">(</span>
        <span class="bp">self</span><span class="p">,</span>
        <span class="n">filename</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">PathLike</span><span class="p">]</span> <span class="o">=</span> <span class="s2">&quot;auto&quot;</span><span class="p">,</span>
        <span class="nb">format</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="s2">&quot;jpeg&quot;</span><span class="p">,</span>
        <span class="n">full_page</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">bool</span><span class="p">]</span> <span class="o">=</span> <span class="kc">False</span><span class="p">,</span>
    <span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Saves a screenshot of the page.</span>
<span class="sd">        This is not the same as :py:obj:`Element.save_screenshot`, which saves a screenshot of a single element only</span>

<span class="sd">        :param filename: uses this as the save path</span>
<span class="sd">        :type filename: PathLike</span>
<span class="sd">        :param format: jpeg or png (defaults to jpeg)</span>
<span class="sd">        :type format: str</span>
<span class="sd">        :param full_page: when False (default) it captures the current viewport. when True, it captures the entire page</span>
<span class="sd">        :type full_page: bool</span>
<span class="sd">        :return: the path/filename of saved screenshot</span>
<span class="sd">        :rtype: str</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="c1"># noqa</span>
        <span class="kn">import</span> <span class="nn">datetime</span>
        <span class="kn">import</span> <span class="nn">urllib.parse</span>

        <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">sleep</span><span class="p">()</span>  <span class="c1"># update the target&#39;s url</span>
        <span class="n">path</span> <span class="o">=</span> <span class="kc">None</span>

        <span class="k">if</span> <span class="nb">format</span><span class="o">.</span><span class="n">lower</span><span class="p">()</span> <span class="ow">in</span> <span class="p">[</span><span class="s2">&quot;jpg&quot;</span><span class="p">,</span> <span class="s2">&quot;jpeg&quot;</span><span class="p">]:</span>
            <span class="n">ext</span> <span class="o">=</span> <span class="s2">&quot;.jpg&quot;</span>
            <span class="nb">format</span> <span class="o">=</span> <span class="s2">&quot;jpeg&quot;</span>

        <span class="k">elif</span> <span class="nb">format</span><span class="o">.</span><span class="n">lower</span><span class="p">()</span> <span class="ow">in</span> <span class="p">[</span><span class="s2">&quot;png&quot;</span><span class="p">]:</span>
            <span class="n">ext</span> <span class="o">=</span> <span class="s2">&quot;.png&quot;</span>
            <span class="nb">format</span> <span class="o">=</span> <span class="s2">&quot;png&quot;</span>

        <span class="k">if</span> <span class="ow">not</span> <span class="n">filename</span> <span class="ow">or</span> <span class="n">filename</span> <span class="o">==</span> <span class="s2">&quot;auto&quot;</span><span class="p">:</span>
            <span class="n">parsed</span> <span class="o">=</span> <span class="n">urllib</span><span class="o">.</span><span class="n">parse</span><span class="o">.</span><span class="n">urlparse</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">target</span><span class="o">.</span><span class="n">url</span><span class="p">)</span>
            <span class="n">parts</span> <span class="o">=</span> <span class="n">parsed</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">split</span><span class="p">(</span><span class="s2">&quot;/&quot;</span><span class="p">)</span>
            <span class="n">last_part</span> <span class="o">=</span> <span class="n">parts</span><span class="p">[</span><span class="o">-</span><span class="mi">1</span><span class="p">]</span>
            <span class="n">last_part</span> <span class="o">=</span> <span class="n">last_part</span><span class="o">.</span><span class="n">rsplit</span><span class="p">(</span><span class="s2">&quot;?&quot;</span><span class="p">,</span> <span class="mi">1</span><span class="p">)[</span><span class="mi">0</span><span class="p">]</span>
            <span class="n">dt_str</span> <span class="o">=</span> <span class="n">datetime</span><span class="o">.</span><span class="n">datetime</span><span class="o">.</span><span class="n">now</span><span class="p">()</span><span class="o">.</span><span class="n">strftime</span><span class="p">(</span><span class="s2">&quot;%Y-%m-</span><span class="si">%d</span><span class="s2">_%H-%M-%S&quot;</span><span class="p">)</span>
            <span class="n">candidate</span> <span class="o">=</span> <span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">parsed</span><span class="o">.</span><span class="n">hostname</span><span class="si">}</span><span class="s2">__</span><span class="si">{</span><span class="n">last_part</span><span class="si">}</span><span class="s2">_</span><span class="si">{</span><span class="n">dt_str</span><span class="si">}</span><span class="s2">&quot;</span>
            <span class="n">path</span> <span class="o">=</span> <span class="n">pathlib</span><span class="o">.</span><span class="n">Path</span><span class="p">(</span><span class="n">candidate</span> <span class="o">+</span> <span class="n">ext</span><span class="p">)</span>  <span class="c1"># noqa</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">path</span> <span class="o">=</span> <span class="n">pathlib</span><span class="o">.</span><span class="n">Path</span><span class="p">(</span><span class="n">filename</span><span class="p">)</span>
        <span class="n">path</span><span class="o">.</span><span class="n">parent</span><span class="o">.</span><span class="n">mkdir</span><span class="p">(</span><span class="n">parents</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span> <span class="n">exist_ok</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
        <span class="n">data</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">send</span><span class="p">(</span>
            <span class="n">cdp</span><span class="o">.</span><span class="n">page</span><span class="o">.</span><span class="n">capture_screenshot</span><span class="p">(</span>
                <span class="n">format_</span><span class="o">=</span><span class="nb">format</span><span class="p">,</span> <span class="n">capture_beyond_viewport</span><span class="o">=</span><span class="n">full_page</span>
            <span class="p">)</span>
        <span class="p">)</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="n">data</span><span class="p">:</span>
            <span class="k">raise</span> <span class="n">ProtocolException</span><span class="p">(</span>
                <span class="s2">&quot;could not take screenshot. most possible cause is the page has not finished loading yet.&quot;</span>
            <span class="p">)</span>
        <span class="kn">import</span> <span class="nn">base64</span>

        <span class="n">data_bytes</span> <span class="o">=</span> <span class="n">base64</span><span class="o">.</span><span class="n">b64decode</span><span class="p">(</span><span class="n">data</span><span class="p">)</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="n">path</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">RuntimeError</span><span class="p">(</span><span class="s2">&quot;invalid filename or path: &#39;</span><span class="si">%s</span><span class="s2">&#39;&quot;</span> <span class="o">%</span> <span class="n">filename</span><span class="p">)</span>
        <span class="n">path</span><span class="o">.</span><span class="n">write_bytes</span><span class="p">(</span><span class="n">data_bytes</span><span class="p">)</span>
        <span class="k">return</span> <span class="nb">str</span><span class="p">(</span><span class="n">path</span><span class="p">)</span></div>


<div class="viewcode-block" id="Tab.set_download_path">
<a class="viewcode-back" href="../../../nodriver/classes/tab.html#nodriver.Tab.set_download_path">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">set_download_path</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">path</span><span class="p">:</span> <span class="n">Union</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="n">PathLike</span><span class="p">]):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        sets the download path and allows downloads</span>
<span class="sd">        this is required for any download function to work (well not entirely, since when unset we set a default folder)</span>

<span class="sd">        :param path:</span>
<span class="sd">        :type path:</span>
<span class="sd">        :return:</span>
<span class="sd">        :rtype:</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">path</span> <span class="o">=</span> <span class="n">Path</span><span class="p">(</span><span class="n">path</span><span class="p">)</span>
        <span class="n">path</span><span class="o">.</span><span class="n">mkdir</span><span class="p">(</span><span class="n">parents</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span> <span class="n">exist_ok</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
        <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">send</span><span class="p">(</span>
            <span class="n">cdp</span><span class="o">.</span><span class="n">browser</span><span class="o">.</span><span class="n">set_download_behavior</span><span class="p">(</span>
                <span class="n">behavior</span><span class="o">=</span><span class="s2">&quot;allow&quot;</span><span class="p">,</span> <span class="n">download_path</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">path</span><span class="o">.</span><span class="n">resolve</span><span class="p">())</span>
            <span class="p">)</span>
        <span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_download_behavior</span> <span class="o">=</span> <span class="p">[</span><span class="s2">&quot;allow&quot;</span><span class="p">,</span> <span class="nb">str</span><span class="p">(</span><span class="n">path</span><span class="o">.</span><span class="n">resolve</span><span class="p">())]</span></div>


<div class="viewcode-block" id="Tab.get_all_linked_sources">
<a class="viewcode-back" href="../../../nodriver/classes/tab.html#nodriver.Tab.get_all_linked_sources">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">get_all_linked_sources</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">List</span><span class="p">[</span><span class="s2">&quot;nodriver.Element&quot;</span><span class="p">]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        get all elements of tag: link, a, img, scripts meta, video, audio</span>

<span class="sd">        :return:</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">all_assets</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">query_selector_all</span><span class="p">(</span><span class="n">selector</span><span class="o">=</span><span class="s2">&quot;a,link,img,script,meta&quot;</span><span class="p">)</span>
        <span class="k">return</span> <span class="p">[</span><span class="n">element</span><span class="o">.</span><span class="n">create</span><span class="p">(</span><span class="n">asset</span><span class="p">,</span> <span class="bp">self</span><span class="p">)</span> <span class="k">for</span> <span class="n">asset</span> <span class="ow">in</span> <span class="n">all_assets</span><span class="p">]</span></div>


<div class="viewcode-block" id="Tab.get_all_urls">
<a class="viewcode-back" href="../../../nodriver/classes/tab.html#nodriver.Tab.get_all_urls">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">get_all_urls</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">absolute</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        convenience function, which returns all links (a,link,img,script,meta)</span>

<span class="sd">        :param absolute: try to build all the links in absolute form instead of &quot;as is&quot;, often relative</span>
<span class="sd">        :return: list of urls</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="kn">import</span> <span class="nn">urllib.parse</span>

        <span class="n">res</span> <span class="o">=</span> <span class="p">[]</span>
        <span class="n">all_assets</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">query_selector_all</span><span class="p">(</span><span class="n">selector</span><span class="o">=</span><span class="s2">&quot;a,link,img,script,meta&quot;</span><span class="p">)</span>
        <span class="k">for</span> <span class="n">asset</span> <span class="ow">in</span> <span class="n">all_assets</span><span class="p">:</span>
            <span class="k">if</span> <span class="ow">not</span> <span class="n">absolute</span><span class="p">:</span>
                <span class="n">res</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">asset</span><span class="o">.</span><span class="n">src</span> <span class="ow">or</span> <span class="n">asset</span><span class="o">.</span><span class="n">href</span><span class="p">)</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="k">for</span> <span class="n">k</span><span class="p">,</span> <span class="n">v</span> <span class="ow">in</span> <span class="n">asset</span><span class="o">.</span><span class="n">attrs</span><span class="o">.</span><span class="n">items</span><span class="p">():</span>
                    <span class="k">if</span> <span class="n">k</span> <span class="ow">in</span> <span class="p">(</span><span class="s2">&quot;src&quot;</span><span class="p">,</span> <span class="s2">&quot;href&quot;</span><span class="p">):</span>
                        <span class="k">if</span> <span class="s2">&quot;#&quot;</span> <span class="ow">in</span> <span class="n">v</span><span class="p">:</span>
                            <span class="k">continue</span>
                        <span class="k">if</span> <span class="ow">not</span> <span class="nb">any</span><span class="p">([</span><span class="n">_</span> <span class="ow">in</span> <span class="n">v</span> <span class="k">for</span> <span class="n">_</span> <span class="ow">in</span> <span class="p">(</span><span class="s2">&quot;http&quot;</span><span class="p">,</span> <span class="s2">&quot;//&quot;</span><span class="p">,</span> <span class="s2">&quot;/&quot;</span><span class="p">)]):</span>
                            <span class="k">continue</span>
                        <span class="n">abs_url</span> <span class="o">=</span> <span class="n">urllib</span><span class="o">.</span><span class="n">parse</span><span class="o">.</span><span class="n">urljoin</span><span class="p">(</span>
                            <span class="s2">&quot;/&quot;</span><span class="o">.</span><span class="n">join</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">url</span><span class="o">.</span><span class="n">rsplit</span><span class="p">(</span><span class="s2">&quot;/&quot;</span><span class="p">)[:</span><span class="mi">3</span><span class="p">]),</span> <span class="n">v</span>
                        <span class="p">)</span>
                        <span class="k">if</span> <span class="ow">not</span> <span class="n">abs_url</span><span class="o">.</span><span class="n">startswith</span><span class="p">((</span><span class="s2">&quot;http&quot;</span><span class="p">,</span> <span class="s2">&quot;//&quot;</span><span class="p">,</span> <span class="s2">&quot;ws&quot;</span><span class="p">)):</span>
                            <span class="k">continue</span>
                        <span class="n">res</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">abs_url</span><span class="p">)</span>
        <span class="k">return</span> <span class="n">res</span></div>


<div class="viewcode-block" id="Tab.get_local_storage">
<a class="viewcode-back" href="../../../nodriver/classes/tab.html#nodriver.Tab.get_local_storage">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">get_local_storage</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        get local storage items as dict of strings (careful!, proper deserialization needs to be done if needed)</span>

<span class="sd">        :return:</span>
<span class="sd">        :rtype:</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">target</span><span class="o">.</span><span class="n">url</span><span class="p">:</span>
            <span class="k">await</span> <span class="bp">self</span>

        <span class="c1"># there must be a better way...</span>
        <span class="n">origin</span> <span class="o">=</span> <span class="s2">&quot;/&quot;</span><span class="o">.</span><span class="n">join</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">url</span><span class="o">.</span><span class="n">split</span><span class="p">(</span><span class="s2">&quot;/&quot;</span><span class="p">,</span> <span class="mi">3</span><span class="p">)[:</span><span class="o">-</span><span class="mi">1</span><span class="p">])</span>

        <span class="n">items</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">send</span><span class="p">(</span>
            <span class="n">cdp</span><span class="o">.</span><span class="n">dom_storage</span><span class="o">.</span><span class="n">get_dom_storage_items</span><span class="p">(</span>
                <span class="n">cdp</span><span class="o">.</span><span class="n">dom_storage</span><span class="o">.</span><span class="n">StorageId</span><span class="p">(</span><span class="n">is_local_storage</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span> <span class="n">security_origin</span><span class="o">=</span><span class="n">origin</span><span class="p">)</span>
            <span class="p">)</span>
        <span class="p">)</span>
        <span class="n">retval</span> <span class="o">=</span> <span class="p">{}</span>
        <span class="k">for</span> <span class="n">item</span> <span class="ow">in</span> <span class="n">items</span><span class="p">:</span>
            <span class="n">retval</span><span class="p">[</span><span class="n">item</span><span class="p">[</span><span class="mi">0</span><span class="p">]]</span> <span class="o">=</span> <span class="n">item</span><span class="p">[</span><span class="mi">1</span><span class="p">]</span>
        <span class="k">return</span> <span class="n">retval</span></div>


<div class="viewcode-block" id="Tab.set_local_storage">
<a class="viewcode-back" href="../../../nodriver/classes/tab.html#nodriver.Tab.set_local_storage">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">set_local_storage</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">items</span><span class="p">:</span> <span class="nb">dict</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        set local storage.</span>
<span class="sd">        dict items must be strings. simple types will be converted to strings automatically.</span>

<span class="sd">        :param items: dict containing {key:str, value:str}</span>
<span class="sd">        :type items: dict[str,str]</span>
<span class="sd">        :return:</span>
<span class="sd">        :rtype:</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">target</span><span class="o">.</span><span class="n">url</span><span class="p">:</span>
            <span class="k">await</span> <span class="bp">self</span>
        <span class="c1"># there must be a better way...</span>
        <span class="n">origin</span> <span class="o">=</span> <span class="s2">&quot;/&quot;</span><span class="o">.</span><span class="n">join</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">url</span><span class="o">.</span><span class="n">split</span><span class="p">(</span><span class="s2">&quot;/&quot;</span><span class="p">,</span> <span class="mi">3</span><span class="p">)[:</span><span class="o">-</span><span class="mi">1</span><span class="p">])</span>

        <span class="k">await</span> <span class="n">asyncio</span><span class="o">.</span><span class="n">gather</span><span class="p">(</span>
            <span class="o">*</span><span class="p">[</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">send</span><span class="p">(</span>
                    <span class="n">cdp</span><span class="o">.</span><span class="n">dom_storage</span><span class="o">.</span><span class="n">set_dom_storage_item</span><span class="p">(</span>
                        <span class="n">storage_id</span><span class="o">=</span><span class="n">cdp</span><span class="o">.</span><span class="n">dom_storage</span><span class="o">.</span><span class="n">StorageId</span><span class="p">(</span>
                            <span class="n">is_local_storage</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span> <span class="n">security_origin</span><span class="o">=</span><span class="n">origin</span>
                        <span class="p">),</span>
                        <span class="n">key</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">key</span><span class="p">),</span>
                        <span class="n">value</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">val</span><span class="p">),</span>
                    <span class="p">)</span>
                <span class="p">)</span>
                <span class="k">for</span> <span class="n">key</span><span class="p">,</span> <span class="n">val</span> <span class="ow">in</span> <span class="n">items</span><span class="o">.</span><span class="n">items</span><span class="p">()</span>
            <span class="p">]</span>
        <span class="p">)</span></div>


    <span class="k">def</span> <span class="fm">__call__</span><span class="p">(</span>
        <span class="bp">self</span><span class="p">,</span>
        <span class="n">text</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="s2">&quot;&quot;</span><span class="p">,</span>
        <span class="n">selector</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="s2">&quot;&quot;</span><span class="p">,</span>
        <span class="n">timeout</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">Union</span><span class="p">[</span><span class="nb">int</span><span class="p">,</span> <span class="nb">float</span><span class="p">]]</span> <span class="o">=</span> <span class="mi">10</span><span class="p">,</span>
    <span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        alias to query_selector_all or find_elements_by_text, depending</span>
<span class="sd">        on whether text= is set or selector= is set</span>

<span class="sd">        :param selector: css selector string</span>
<span class="sd">        :type selector: str</span>
<span class="sd">        :return:</span>
<span class="sd">        :rtype:</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">wait_for</span><span class="p">(</span><span class="n">text</span><span class="p">,</span> <span class="n">selector</span><span class="p">,</span> <span class="n">timeout</span><span class="p">)</span>

<div class="viewcode-block" id="Tab.get_frame_tree">
<a class="viewcode-back" href="../../../nodriver/classes/tab.html#nodriver.Tab.get_frame_tree">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">get_frame_tree</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">cdp</span><span class="o">.</span><span class="n">page</span><span class="o">.</span><span class="n">FrameTree</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        retrieves the frame tree for current tab</span>
<span class="sd">        There seems no real difference between :ref:`Tab.get_frame_resource_tree()`</span>
<span class="sd">        :return:</span>
<span class="sd">        :rtype:</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">tree</span><span class="p">:</span> <span class="n">cdp</span><span class="o">.</span><span class="n">page</span><span class="o">.</span><span class="n">FrameTree</span> <span class="o">=</span> <span class="k">await</span> <span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="n">send</span><span class="p">(</span><span class="n">cdp</span><span class="o">.</span><span class="n">page</span><span class="o">.</span><span class="n">get_frame_tree</span><span class="p">())</span>
        <span class="k">return</span> <span class="n">tree</span></div>


<div class="viewcode-block" id="Tab.get_frame_resource_tree">
<a class="viewcode-back" href="../../../nodriver/classes/tab.html#nodriver.Tab.get_frame_resource_tree">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">get_frame_resource_tree</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">cdp</span><span class="o">.</span><span class="n">page</span><span class="o">.</span><span class="n">FrameResourceTree</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        retrieves the frame resource tree for current tab.</span>
<span class="sd">        There seems no real difference between :ref:`Tab.get_frame_tree()`</span>
<span class="sd">        but still it returns a different object</span>
<span class="sd">        :return:</span>
<span class="sd">        :rtype:</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">tree</span><span class="p">:</span> <span class="n">cdp</span><span class="o">.</span><span class="n">page</span><span class="o">.</span><span class="n">FrameResourceTree</span> <span class="o">=</span> <span class="k">await</span> <span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="n">send</span><span class="p">(</span>
            <span class="n">cdp</span><span class="o">.</span><span class="n">page</span><span class="o">.</span><span class="n">get_resource_tree</span><span class="p">()</span>
        <span class="p">)</span>
        <span class="k">return</span> <span class="n">tree</span></div>


<div class="viewcode-block" id="Tab.get_frame_resource_urls">
<a class="viewcode-back" href="../../../nodriver/classes/tab.html#nodriver.Tab.get_frame_resource_urls">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">get_frame_resource_urls</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        gets the urls of resources</span>
<span class="sd">        :return:</span>
<span class="sd">        :rtype:</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">_tree</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_frame_resource_tree</span><span class="p">()</span>
        <span class="k">return</span> <span class="p">[</span>
            <span class="n">x</span>
            <span class="k">for</span> <span class="n">x</span> <span class="ow">in</span> <span class="n">functools</span><span class="o">.</span><span class="n">reduce</span><span class="p">(</span>
                <span class="k">lambda</span> <span class="n">a</span><span class="p">,</span> <span class="n">b</span><span class="p">:</span> <span class="n">a</span> <span class="o">+</span> <span class="p">[</span><span class="n">b</span><span class="p">[</span><span class="mi">1</span><span class="p">]</span><span class="o">.</span><span class="n">url</span> <span class="k">if</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">b</span><span class="p">,</span> <span class="nb">tuple</span><span class="p">)</span> <span class="k">else</span> <span class="s2">&quot;&quot;</span><span class="p">],</span>
                <span class="n">util</span><span class="o">.</span><span class="n">flatten_frame_tree_resources</span><span class="p">(</span><span class="n">_tree</span><span class="p">),</span>
                <span class="p">[],</span>
            <span class="p">)</span>
            <span class="k">if</span> <span class="n">x</span>
        <span class="p">]</span></div>


<div class="viewcode-block" id="Tab.search_frame_resources">
<a class="viewcode-back" href="../../../nodriver/classes/tab.html#nodriver.Tab.search_frame_resources">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">search_frame_resources</span><span class="p">(</span>
        <span class="bp">self</span><span class="p">,</span> <span class="n">query</span><span class="p">:</span> <span class="nb">str</span>
    <span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Dict</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="n">List</span><span class="p">[</span><span class="n">cdp</span><span class="o">.</span><span class="n">debugger</span><span class="o">.</span><span class="n">SearchMatch</span><span class="p">]]:</span>
        <span class="k">try</span><span class="p">:</span>
            <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">_send_oneshot</span><span class="p">(</span><span class="n">cdp</span><span class="o">.</span><span class="n">page</span><span class="o">.</span><span class="n">enable</span><span class="p">())</span>
            <span class="n">list_of_tuples</span> <span class="o">=</span> <span class="nb">list</span><span class="p">(</span>
                <span class="n">util</span><span class="o">.</span><span class="n">flatten_frame_tree_resources</span><span class="p">(</span><span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_frame_resource_tree</span><span class="p">())</span>
            <span class="p">)</span>
            <span class="n">results</span> <span class="o">=</span> <span class="p">{}</span>
            <span class="k">for</span> <span class="n">item</span> <span class="ow">in</span> <span class="n">list_of_tuples</span><span class="p">:</span>
                <span class="k">if</span> <span class="ow">not</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">item</span><span class="p">,</span> <span class="nb">tuple</span><span class="p">):</span>
                    <span class="k">continue</span>
                <span class="n">frame</span><span class="p">,</span> <span class="n">resource</span> <span class="o">=</span> <span class="n">item</span>
                <span class="n">res</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">send</span><span class="p">(</span>
                    <span class="n">cdp</span><span class="o">.</span><span class="n">page</span><span class="o">.</span><span class="n">search_in_resource</span><span class="p">(</span>
                        <span class="n">frame_id</span><span class="o">=</span><span class="n">frame</span><span class="o">.</span><span class="n">id_</span><span class="p">,</span> <span class="n">url</span><span class="o">=</span><span class="n">resource</span><span class="o">.</span><span class="n">url</span><span class="p">,</span> <span class="n">query</span><span class="o">=</span><span class="n">query</span>
                    <span class="p">)</span>
                <span class="p">)</span>
                <span class="k">if</span> <span class="ow">not</span> <span class="n">res</span><span class="p">:</span>
                    <span class="k">continue</span>
                <span class="n">results</span><span class="p">[</span><span class="n">resource</span><span class="o">.</span><span class="n">url</span><span class="p">]</span> <span class="o">=</span> <span class="n">res</span>
        <span class="k">finally</span><span class="p">:</span>
            <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">_send_oneshot</span><span class="p">(</span><span class="n">cdp</span><span class="o">.</span><span class="n">page</span><span class="o">.</span><span class="n">disable</span><span class="p">())</span>

        <span class="k">return</span> <span class="n">results</span></div>


<div class="viewcode-block" id="Tab.verify_cf">
<a class="viewcode-back" href="../../../nodriver/classes/tab.html#nodriver.Tab.verify_cf">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">verify_cf</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">template_image</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span> <span class="n">flash</span><span class="o">=</span><span class="kc">False</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        convenience function to verify cf checkbox</span>

<span class="sd">        template_image can be custom (for example your language, included is english only),</span>
<span class="sd">        but you need to create the template image yourself, which is just a cropped</span>
<span class="sd">        image of the area, see example image, where the target is exactly in the center.</span>

<span class="sd">        example (111x71)</span>
<span class="sd">        ---------</span>
<span class="sd">        this includes the white space on the left, to make the box center</span>

<span class="sd">        .. image:: template_example.png</span>
<span class="sd">            :width: 111</span>
<span class="sd">            :alt: example template image</span>

<span class="sd">        :param template_image:</span>
<span class="sd">            template_image can be custom (for example your language, included is english only),</span>
<span class="sd">            but you need to create the template image yourself, which is just a cropped</span>
<span class="sd">            image of the area, where the target is exactly in the center. see example on</span>
<span class="sd">            (https://ultrafunkamsterdam.github.io/nodriver/nodriver/classes/tab.html#example-111x71),</span>

<span class="sd">        :type template_image:</span>
<span class="sd">        :param flash: whether to show an indicator where the mouse is clicking.</span>
<span class="sd">        :type flash:</span>
<span class="sd">        :return:</span>
<span class="sd">        :rtype:</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">browser</span> <span class="ow">and</span> <span class="bp">self</span><span class="o">.</span><span class="n">browser</span><span class="o">.</span><span class="n">config</span> <span class="ow">and</span> <span class="bp">self</span><span class="o">.</span><span class="n">browser</span><span class="o">.</span><span class="n">config</span><span class="o">.</span><span class="n">expert</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">Exception</span><span class="p">(</span>
<span class="w">                </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">                            this function is useless in expert mode, since it disables site-isolation-trials.</span>
<span class="sd">                            while this is a useful future to have access to all elements (also in iframes),</span>
<span class="sd">                            it is also being detected</span>
<span class="sd">                            &quot;&quot;&quot;</span>
            <span class="p">)</span>
        <span class="n">x</span><span class="p">,</span> <span class="n">y</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">template_location</span><span class="p">(</span><span class="n">template_image</span><span class="o">=</span><span class="n">template_image</span><span class="p">)</span>
        <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">mouse_click</span><span class="p">(</span><span class="n">x</span><span class="p">,</span> <span class="n">y</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">flash</span><span class="p">:</span>
            <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">flash_point</span><span class="p">(</span><span class="n">x</span><span class="p">,</span> <span class="n">y</span><span class="p">)</span></div>


<div class="viewcode-block" id="Tab.template_location">
<a class="viewcode-back" href="../../../nodriver/classes/tab.html#nodriver.Tab.template_location">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">template_location</span><span class="p">(</span>
        <span class="bp">self</span><span class="p">,</span> <span class="n">template_image</span><span class="p">:</span> <span class="n">PathLike</span> <span class="o">=</span> <span class="kc">None</span>
    <span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Union</span><span class="p">[</span><span class="n">Tuple</span><span class="p">[</span><span class="nb">int</span><span class="p">,</span> <span class="nb">int</span><span class="p">],</span> <span class="kc">None</span><span class="p">]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        attempts to find the location of given template image in the current viewport</span>
<span class="sd">        the only real use case for this is bot-detection systems.</span>
<span class="sd">        you can find for example the location of a &#39;verify&#39;-checkbox,</span>
<span class="sd">        which are hidden from dom using shadow-root&#39;s or workers.</span>

<span class="sd">        template_image can be custom (for example your language, included is english only),</span>
<span class="sd">        but you need to create the template image yourself, which is just a cropped</span>
<span class="sd">        image of the area, see example image, where the target is exactly in the center.</span>
<span class="sd">        template_image can be custom (for example your language), but you need to</span>
<span class="sd">        create the template image yourself, where the target is exactly in the center.</span>

<span class="sd">        example (111x71)</span>
<span class="sd">        ---------</span>
<span class="sd">        this includes the white space on the left, to make the box center</span>

<span class="sd">        .. image:: template_example.png</span>
<span class="sd">            :width: 111</span>
<span class="sd">            :alt: example template image</span>


<span class="sd">        :param template_image:</span>
<span class="sd">        :type template_image:</span>
<span class="sd">        :return:</span>
<span class="sd">        :rtype:</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">try</span><span class="p">:</span>
            <span class="kn">import</span> <span class="nn">cv2</span>
        <span class="k">except</span> <span class="ne">ImportError</span><span class="p">:</span>
            <span class="n">logger</span><span class="o">.</span><span class="n">warning</span><span class="p">(</span>
<span class="w">                </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">                missing package</span>
<span class="sd">                ----------------</span>
<span class="sd">                template_location function needs the computer vision library &quot;opencv-python&quot; installed</span>
<span class="sd">                to install:</span>
<span class="sd">                pip install opencv-python</span>
<span class="sd">            </span>
<span class="sd">            &quot;&quot;&quot;</span>
            <span class="p">)</span>
            <span class="k">return</span>
        <span class="k">try</span><span class="p">:</span>

            <span class="k">if</span> <span class="n">template_image</span><span class="p">:</span>
                <span class="n">template_image</span> <span class="o">=</span> <span class="n">Path</span><span class="p">(</span><span class="n">template_image</span><span class="p">)</span>
                <span class="k">if</span> <span class="ow">not</span> <span class="n">template_image</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
                    <span class="k">raise</span> <span class="ne">FileNotFoundError</span><span class="p">(</span>
                        <span class="s2">&quot;</span><span class="si">%s</span><span class="s2"> was not found in the current location : </span><span class="si">%s</span><span class="s2">&quot;</span>
                        <span class="o">%</span> <span class="p">(</span><span class="n">template_image</span><span class="p">,</span> <span class="n">os</span><span class="o">.</span><span class="n">getcwd</span><span class="p">())</span>
                    <span class="p">)</span>
            <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">save_screenshot</span><span class="p">(</span><span class="s2">&quot;screen.jpg&quot;</span><span class="p">)</span>
            <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.05</span><span class="p">)</span>
            <span class="n">im</span> <span class="o">=</span> <span class="n">cv2</span><span class="o">.</span><span class="n">imread</span><span class="p">(</span><span class="s2">&quot;screen.jpg&quot;</span><span class="p">)</span>
            <span class="n">im_gray</span> <span class="o">=</span> <span class="n">cv2</span><span class="o">.</span><span class="n">cvtColor</span><span class="p">(</span><span class="n">im</span><span class="p">,</span> <span class="n">cv2</span><span class="o">.</span><span class="n">COLOR_BGR2GRAY</span><span class="p">)</span>
            <span class="k">if</span> <span class="n">template_image</span><span class="p">:</span>
                <span class="n">template</span> <span class="o">=</span> <span class="n">cv2</span><span class="o">.</span><span class="n">imread</span><span class="p">(</span><span class="nb">str</span><span class="p">(</span><span class="n">template_image</span><span class="p">))</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="s2">&quot;cf_template.png&quot;</span><span class="p">,</span> <span class="s2">&quot;w+b&quot;</span><span class="p">)</span> <span class="k">as</span> <span class="n">fh</span><span class="p">:</span>
                    <span class="n">fh</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="n">util</span><span class="o">.</span><span class="n">get_cf_template</span><span class="p">())</span>
                <span class="n">template</span> <span class="o">=</span> <span class="n">cv2</span><span class="o">.</span><span class="n">imread</span><span class="p">(</span><span class="s2">&quot;cf_template.png&quot;</span><span class="p">)</span>
            <span class="n">template_gray</span> <span class="o">=</span> <span class="n">cv2</span><span class="o">.</span><span class="n">cvtColor</span><span class="p">(</span><span class="n">template</span><span class="p">,</span> <span class="n">cv2</span><span class="o">.</span><span class="n">COLOR_BGR2GRAY</span><span class="p">)</span>
            <span class="n">match</span> <span class="o">=</span> <span class="n">cv2</span><span class="o">.</span><span class="n">matchTemplate</span><span class="p">(</span><span class="n">im_gray</span><span class="p">,</span> <span class="n">template_gray</span><span class="p">,</span> <span class="n">cv2</span><span class="o">.</span><span class="n">TM_CCOEFF_NORMED</span><span class="p">)</span>
            <span class="p">(</span><span class="n">min_v</span><span class="p">,</span> <span class="n">max_v</span><span class="p">,</span> <span class="n">min_l</span><span class="p">,</span> <span class="n">max_l</span><span class="p">)</span> <span class="o">=</span> <span class="n">cv2</span><span class="o">.</span><span class="n">minMaxLoc</span><span class="p">(</span><span class="n">match</span><span class="p">)</span>
            <span class="p">(</span><span class="n">xs</span><span class="p">,</span> <span class="n">ys</span><span class="p">)</span> <span class="o">=</span> <span class="n">max_l</span>
            <span class="n">tmp_h</span><span class="p">,</span> <span class="n">tmp_w</span> <span class="o">=</span> <span class="n">template_gray</span><span class="o">.</span><span class="n">shape</span><span class="p">[:</span><span class="mi">2</span><span class="p">]</span>
            <span class="n">xe</span> <span class="o">=</span> <span class="n">xs</span> <span class="o">+</span> <span class="n">tmp_w</span>
            <span class="n">ye</span> <span class="o">=</span> <span class="n">ys</span> <span class="o">+</span> <span class="n">tmp_h</span>
            <span class="n">cx</span> <span class="o">=</span> <span class="p">(</span><span class="n">xs</span> <span class="o">+</span> <span class="n">xe</span><span class="p">)</span> <span class="o">//</span> <span class="mi">2</span>
            <span class="n">cy</span> <span class="o">=</span> <span class="p">(</span><span class="n">ys</span> <span class="o">+</span> <span class="n">ye</span><span class="p">)</span> <span class="o">//</span> <span class="mi">2</span>
            <span class="k">return</span> <span class="n">cx</span><span class="p">,</span> <span class="n">cy</span>
        <span class="k">except</span> <span class="p">(</span><span class="ne">TypeError</span><span class="p">,</span> <span class="ne">OSError</span><span class="p">,</span> <span class="ne">PermissionError</span><span class="p">):</span>
            <span class="k">pass</span>  <span class="c1"># ignore these exceptions</span>
        <span class="k">except</span><span class="p">:</span>  <span class="c1"># noqa - don&#39;t ignore other exceptions</span>
            <span class="k">raise</span>
        <span class="k">finally</span><span class="p">:</span>
            <span class="k">try</span><span class="p">:</span>
                <span class="n">os</span><span class="o">.</span><span class="n">unlink</span><span class="p">(</span><span class="s2">&quot;screen.jpg&quot;</span><span class="p">)</span>
            <span class="k">except</span><span class="p">:</span>
                <span class="n">logger</span><span class="o">.</span><span class="n">warning</span><span class="p">(</span><span class="s2">&quot;could not unlink temporary screenshot&quot;</span><span class="p">)</span>
            <span class="k">if</span> <span class="n">template_image</span><span class="p">:</span>
                <span class="k">pass</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="k">try</span><span class="p">:</span>
                    <span class="n">os</span><span class="o">.</span><span class="n">unlink</span><span class="p">(</span><span class="s2">&quot;cf_template.png&quot;</span><span class="p">)</span>
                <span class="k">except</span><span class="p">:</span>  <span class="c1"># noqa</span>
                    <span class="n">logger</span><span class="o">.</span><span class="n">warning</span><span class="p">(</span><span class="s2">&quot;could not unlink template file cf_template.png&quot;</span><span class="p">)</span></div>


<div class="viewcode-block" id="Tab.bypass_insecure_connection_warning">
<a class="viewcode-back" href="../../../nodriver/classes/tab.html#nodriver.Tab.bypass_insecure_connection_warning">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">bypass_insecure_connection_warning</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        when you enter a site where the certificate is invalid</span>
<span class="sd">        you get a warning. call this function to &quot;proceed&quot;</span>
<span class="sd">        :return:</span>
<span class="sd">        :rtype:</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">body</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">select</span><span class="p">(</span><span class="s2">&quot;body&quot;</span><span class="p">)</span>
        <span class="k">await</span> <span class="n">body</span><span class="o">.</span><span class="n">send_keys</span><span class="p">(</span><span class="s2">&quot;thisisunsafe&quot;</span><span class="p">)</span></div>


<div class="viewcode-block" id="Tab.mouse_move">
<a class="viewcode-back" href="../../../nodriver/classes/tab.html#nodriver.Tab.mouse_move">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">mouse_move</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">x</span><span class="p">:</span> <span class="nb">float</span><span class="p">,</span> <span class="n">y</span><span class="p">:</span> <span class="nb">float</span><span class="p">,</span> <span class="n">steps</span><span class="o">=</span><span class="mi">10</span><span class="p">,</span> <span class="n">flash</span><span class="o">=</span><span class="kc">False</span><span class="p">):</span>
        <span class="n">steps</span> <span class="o">=</span> <span class="mi">1</span> <span class="k">if</span> <span class="p">(</span><span class="ow">not</span> <span class="n">steps</span> <span class="ow">or</span> <span class="n">steps</span> <span class="o">&lt;</span> <span class="mi">1</span><span class="p">)</span> <span class="k">else</span> <span class="n">steps</span>
        <span class="c1"># probably the worst waay of calculating this. but couldn&#39;t think of a better solution today.</span>
        <span class="k">if</span> <span class="n">steps</span> <span class="o">&gt;</span> <span class="mi">1</span><span class="p">:</span>
            <span class="n">step_size_x</span> <span class="o">=</span> <span class="n">x</span> <span class="o">//</span> <span class="n">steps</span>
            <span class="n">step_size_y</span> <span class="o">=</span> <span class="n">y</span> <span class="o">//</span> <span class="n">steps</span>
            <span class="n">pathway</span> <span class="o">=</span> <span class="p">[(</span><span class="n">step_size_x</span> <span class="o">*</span> <span class="n">i</span><span class="p">,</span> <span class="n">step_size_y</span> <span class="o">*</span> <span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">steps</span> <span class="o">+</span> <span class="mi">1</span><span class="p">)]</span>
            <span class="k">for</span> <span class="n">point</span> <span class="ow">in</span> <span class="n">pathway</span><span class="p">:</span>
                <span class="k">if</span> <span class="n">flash</span><span class="p">:</span>
                    <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">flash_point</span><span class="p">(</span><span class="n">point</span><span class="p">[</span><span class="mi">0</span><span class="p">],</span> <span class="n">point</span><span class="p">[</span><span class="mi">1</span><span class="p">])</span>
                <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">send</span><span class="p">(</span>
                    <span class="n">cdp</span><span class="o">.</span><span class="n">input_</span><span class="o">.</span><span class="n">dispatch_mouse_event</span><span class="p">(</span>
                        <span class="s2">&quot;mouseMoved&quot;</span><span class="p">,</span> <span class="n">x</span><span class="o">=</span><span class="n">point</span><span class="p">[</span><span class="mi">0</span><span class="p">],</span> <span class="n">y</span><span class="o">=</span><span class="n">point</span><span class="p">[</span><span class="mi">1</span><span class="p">]</span>
                    <span class="p">)</span>
                <span class="p">)</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">send</span><span class="p">(</span><span class="n">cdp</span><span class="o">.</span><span class="n">input_</span><span class="o">.</span><span class="n">dispatch_mouse_event</span><span class="p">(</span><span class="s2">&quot;mouseMoved&quot;</span><span class="p">,</span> <span class="n">x</span><span class="o">=</span><span class="n">x</span><span class="p">,</span> <span class="n">y</span><span class="o">=</span><span class="n">y</span><span class="p">))</span>
        <span class="k">if</span> <span class="n">flash</span><span class="p">:</span>
            <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">flash_point</span><span class="p">(</span><span class="n">x</span><span class="p">,</span> <span class="n">y</span><span class="p">)</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.05</span><span class="p">)</span>
        <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">send</span><span class="p">(</span><span class="n">cdp</span><span class="o">.</span><span class="n">input_</span><span class="o">.</span><span class="n">dispatch_mouse_event</span><span class="p">(</span><span class="s2">&quot;mouseReleased&quot;</span><span class="p">,</span> <span class="n">x</span><span class="o">=</span><span class="n">x</span><span class="p">,</span> <span class="n">y</span><span class="o">=</span><span class="n">y</span><span class="p">))</span>
        <span class="k">if</span> <span class="n">flash</span><span class="p">:</span>
            <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">flash_point</span><span class="p">(</span><span class="n">x</span><span class="p">,</span> <span class="n">y</span><span class="p">)</span></div>


<div class="viewcode-block" id="Tab.scroll_bottom_reached">
<a class="viewcode-back" href="../../../nodriver/classes/tab.html#nodriver.Tab.scroll_bottom_reached">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">scroll_bottom_reached</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        returns True if scroll is at the bottom of the page</span>
<span class="sd">        handy when you need to scroll over paginated pages of different lengths</span>
<span class="sd">        :return:</span>
<span class="sd">        :rtype:</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="n">res</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">evaluate</span><span class="p">(</span>
            <span class="s2">&quot;document.body.offsetHeight - window.innerHeight == window.scrollY&quot;</span>
        <span class="p">)</span>
        <span class="k">if</span> <span class="n">res</span><span class="p">:</span>
            <span class="k">return</span> <span class="n">res</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span><span class="o">.</span><span class="n">value</span></div>


<div class="viewcode-block" id="Tab.mouse_click">
<a class="viewcode-back" href="../../../nodriver/classes/tab.html#nodriver.Tab.mouse_click">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">mouse_click</span><span class="p">(</span>
        <span class="bp">self</span><span class="p">,</span>
        <span class="n">x</span><span class="p">:</span> <span class="nb">float</span><span class="p">,</span>
        <span class="n">y</span><span class="p">:</span> <span class="nb">float</span><span class="p">,</span>
        <span class="n">button</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="s2">&quot;left&quot;</span><span class="p">,</span>
        <span class="n">buttons</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">int</span><span class="p">]</span> <span class="o">=</span> <span class="mi">1</span><span class="p">,</span>
        <span class="n">modifiers</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">int</span><span class="p">]</span> <span class="o">=</span> <span class="mi">0</span><span class="p">,</span>
        <span class="n">_until_event</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">type</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
    <span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;native click on position x,y</span>
<span class="sd">        :param y:</span>
<span class="sd">        :type y:</span>
<span class="sd">        :param x:</span>
<span class="sd">        :type x:</span>
<span class="sd">        :param button: str (default = &quot;left&quot;)</span>
<span class="sd">        :param buttons: which button (default 1 = left)</span>
<span class="sd">        :param modifiers: *(Optional)* Bit field representing pressed modifier keys.</span>
<span class="sd">                Alt=1, Ctrl=2, Meta/Command=4, Shift=8 (default: 0).</span>
<span class="sd">        :param _until_event: internal. event to wait for before returning</span>
<span class="sd">        :return:</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">send</span><span class="p">(</span>
            <span class="n">cdp</span><span class="o">.</span><span class="n">input_</span><span class="o">.</span><span class="n">dispatch_mouse_event</span><span class="p">(</span>
                <span class="s2">&quot;mousePressed&quot;</span><span class="p">,</span>
                <span class="n">x</span><span class="o">=</span><span class="n">x</span><span class="p">,</span>
                <span class="n">y</span><span class="o">=</span><span class="n">y</span><span class="p">,</span>
                <span class="n">modifiers</span><span class="o">=</span><span class="n">modifiers</span><span class="p">,</span>
                <span class="n">button</span><span class="o">=</span><span class="n">cdp</span><span class="o">.</span><span class="n">input_</span><span class="o">.</span><span class="n">MouseButton</span><span class="p">(</span><span class="n">button</span><span class="p">),</span>
                <span class="n">buttons</span><span class="o">=</span><span class="n">buttons</span><span class="p">,</span>
                <span class="n">click_count</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span>
            <span class="p">)</span>
        <span class="p">)</span>

        <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">send</span><span class="p">(</span>
            <span class="n">cdp</span><span class="o">.</span><span class="n">input_</span><span class="o">.</span><span class="n">dispatch_mouse_event</span><span class="p">(</span>
                <span class="s2">&quot;mouseReleased&quot;</span><span class="p">,</span>
                <span class="n">x</span><span class="o">=</span><span class="n">x</span><span class="p">,</span>
                <span class="n">y</span><span class="o">=</span><span class="n">y</span><span class="p">,</span>
                <span class="n">modifiers</span><span class="o">=</span><span class="n">modifiers</span><span class="p">,</span>
                <span class="n">button</span><span class="o">=</span><span class="n">cdp</span><span class="o">.</span><span class="n">input_</span><span class="o">.</span><span class="n">MouseButton</span><span class="p">(</span><span class="n">button</span><span class="p">),</span>
                <span class="n">buttons</span><span class="o">=</span><span class="n">buttons</span><span class="p">,</span>
                <span class="n">click_count</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span>
            <span class="p">)</span>
        <span class="p">)</span></div>


<div class="viewcode-block" id="Tab.mouse_drag">
<a class="viewcode-back" href="../../../nodriver/classes/tab.html#nodriver.Tab.mouse_drag">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">mouse_drag</span><span class="p">(</span>
        <span class="bp">self</span><span class="p">,</span>
        <span class="n">source_point</span><span class="p">:</span> <span class="nb">tuple</span><span class="p">[</span><span class="nb">float</span><span class="p">,</span> <span class="nb">float</span><span class="p">],</span>
        <span class="n">dest_point</span><span class="p">:</span> <span class="nb">tuple</span><span class="p">[</span><span class="nb">float</span><span class="p">,</span> <span class="nb">float</span><span class="p">],</span>
        <span class="n">relative</span><span class="p">:</span> <span class="nb">bool</span> <span class="o">=</span> <span class="kc">False</span><span class="p">,</span>
        <span class="n">steps</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="mi">1</span><span class="p">,</span>
    <span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        drag mouse from one point to another. holding button pressed</span>
<span class="sd">        you are probably looking for :py:meth:`element.Element.mouse_drag` method. where you</span>
<span class="sd">        can drag on the element</span>

<span class="sd">        :param dest_point:</span>
<span class="sd">        :type dest_point:</span>
<span class="sd">        :param source_point:</span>
<span class="sd">        :type source_point:</span>
<span class="sd">        :param relative: when True, treats point as relative. for example (-100, 200) will move left 100px and down 200px</span>
<span class="sd">        :type relative:</span>

<span class="sd">        :param steps: move in &lt;steps&gt; points, this could make it look more &quot;natural&quot; (default 1),</span>
<span class="sd">               but also a lot slower.</span>
<span class="sd">               for very smooth action use 50-100</span>
<span class="sd">        :type steps: int</span>
<span class="sd">        :return:</span>
<span class="sd">        :rtype:</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="n">relative</span><span class="p">:</span>
            <span class="n">dest_point</span> <span class="o">=</span> <span class="p">(</span>
                <span class="n">source_point</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span> <span class="o">+</span> <span class="n">dest_point</span><span class="p">[</span><span class="mi">0</span><span class="p">],</span>
                <span class="n">source_point</span><span class="p">[</span><span class="mi">1</span><span class="p">]</span> <span class="o">+</span> <span class="n">dest_point</span><span class="p">[</span><span class="mi">1</span><span class="p">],</span>
            <span class="p">)</span>
        <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">send</span><span class="p">(</span>
            <span class="n">cdp</span><span class="o">.</span><span class="n">input_</span><span class="o">.</span><span class="n">dispatch_mouse_event</span><span class="p">(</span>
                <span class="s2">&quot;mousePressed&quot;</span><span class="p">,</span>
                <span class="n">x</span><span class="o">=</span><span class="n">source_point</span><span class="p">[</span><span class="mi">0</span><span class="p">],</span>
                <span class="n">y</span><span class="o">=</span><span class="n">source_point</span><span class="p">[</span><span class="mi">1</span><span class="p">],</span>
                <span class="n">button</span><span class="o">=</span><span class="n">cdp</span><span class="o">.</span><span class="n">input_</span><span class="o">.</span><span class="n">MouseButton</span><span class="p">(</span><span class="s2">&quot;left&quot;</span><span class="p">),</span>
            <span class="p">)</span>
        <span class="p">)</span>
        <span class="n">steps</span> <span class="o">=</span> <span class="mi">1</span> <span class="k">if</span> <span class="p">(</span><span class="ow">not</span> <span class="n">steps</span> <span class="ow">or</span> <span class="n">steps</span> <span class="o">&lt;</span> <span class="mi">1</span><span class="p">)</span> <span class="k">else</span> <span class="n">steps</span>

        <span class="k">if</span> <span class="n">steps</span> <span class="o">==</span> <span class="mi">1</span><span class="p">:</span>
            <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">send</span><span class="p">(</span>
                <span class="n">cdp</span><span class="o">.</span><span class="n">input_</span><span class="o">.</span><span class="n">dispatch_mouse_event</span><span class="p">(</span>
                    <span class="s2">&quot;mouseMoved&quot;</span><span class="p">,</span> <span class="n">x</span><span class="o">=</span><span class="n">dest_point</span><span class="p">[</span><span class="mi">0</span><span class="p">],</span> <span class="n">y</span><span class="o">=</span><span class="n">dest_point</span><span class="p">[</span><span class="mi">1</span><span class="p">]</span>
                <span class="p">)</span>
            <span class="p">)</span>
        <span class="k">elif</span> <span class="n">steps</span> <span class="o">&gt;</span> <span class="mi">1</span><span class="p">:</span>
            <span class="c1"># probably the worst waay of calculating this. but couldn&#39;t think of a better solution today.</span>
            <span class="n">step_size_x</span> <span class="o">=</span> <span class="p">(</span><span class="n">dest_point</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span> <span class="o">-</span> <span class="n">source_point</span><span class="p">[</span><span class="mi">0</span><span class="p">])</span> <span class="o">/</span> <span class="n">steps</span>
            <span class="n">step_size_y</span> <span class="o">=</span> <span class="p">(</span><span class="n">dest_point</span><span class="p">[</span><span class="mi">1</span><span class="p">]</span> <span class="o">-</span> <span class="n">source_point</span><span class="p">[</span><span class="mi">1</span><span class="p">])</span> <span class="o">/</span> <span class="n">steps</span>
            <span class="n">pathway</span> <span class="o">=</span> <span class="p">[</span>
                <span class="p">(</span><span class="n">source_point</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span> <span class="o">+</span> <span class="n">step_size_x</span> <span class="o">*</span> <span class="n">i</span><span class="p">,</span> <span class="n">source_point</span><span class="p">[</span><span class="mi">1</span><span class="p">]</span> <span class="o">+</span> <span class="n">step_size_y</span> <span class="o">*</span> <span class="n">i</span><span class="p">)</span>
                <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">steps</span> <span class="o">+</span> <span class="mi">1</span><span class="p">)</span>
            <span class="p">]</span>
            <span class="k">for</span> <span class="n">point</span> <span class="ow">in</span> <span class="n">pathway</span><span class="p">:</span>
                <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">send</span><span class="p">(</span>
                    <span class="n">cdp</span><span class="o">.</span><span class="n">input_</span><span class="o">.</span><span class="n">dispatch_mouse_event</span><span class="p">(</span>
                        <span class="s2">&quot;mouseMoved&quot;</span><span class="p">,</span>
                        <span class="n">x</span><span class="o">=</span><span class="n">point</span><span class="p">[</span><span class="mi">0</span><span class="p">],</span>
                        <span class="n">y</span><span class="o">=</span><span class="n">point</span><span class="p">[</span><span class="mi">1</span><span class="p">],</span>
                    <span class="p">)</span>
                <span class="p">)</span>
                <span class="k">await</span> <span class="n">asyncio</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mi">0</span><span class="p">)</span>

        <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">send</span><span class="p">(</span>
            <span class="n">cdp</span><span class="o">.</span><span class="n">input_</span><span class="o">.</span><span class="n">dispatch_mouse_event</span><span class="p">(</span>
                <span class="n">type_</span><span class="o">=</span><span class="s2">&quot;mouseReleased&quot;</span><span class="p">,</span>
                <span class="n">x</span><span class="o">=</span><span class="n">dest_point</span><span class="p">[</span><span class="mi">0</span><span class="p">],</span>
                <span class="n">y</span><span class="o">=</span><span class="n">dest_point</span><span class="p">[</span><span class="mi">1</span><span class="p">],</span>
                <span class="n">button</span><span class="o">=</span><span class="n">cdp</span><span class="o">.</span><span class="n">input_</span><span class="o">.</span><span class="n">MouseButton</span><span class="p">(</span><span class="s2">&quot;left&quot;</span><span class="p">),</span>
            <span class="p">)</span>
        <span class="p">)</span></div>


<div class="viewcode-block" id="Tab.flash_point">
<a class="viewcode-back" href="../../../nodriver/classes/tab.html#nodriver.Tab.flash_point">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">flash_point</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">x</span><span class="p">,</span> <span class="n">y</span><span class="p">,</span> <span class="n">duration</span><span class="o">=</span><span class="mf">0.5</span><span class="p">,</span> <span class="n">size</span><span class="o">=</span><span class="mi">10</span><span class="p">):</span>
        <span class="n">style</span> <span class="o">=</span> <span class="p">(</span>
            <span class="s2">&quot;position:absolute;z-index:99999999;padding:0;margin:0;&quot;</span>
            <span class="s2">&quot;left:</span><span class="si">{:.1f}</span><span class="s2">px; top: </span><span class="si">{:.1f}</span><span class="s2">px;&quot;</span>
            <span class="s2">&quot;opacity:1;&quot;</span>
            <span class="s2">&quot;width:</span><span class="si">{:d}</span><span class="s2">px;height:</span><span class="si">{:d}</span><span class="s2">px;border-radius:50%;background:red;&quot;</span>
            <span class="s2">&quot;animation:show-pointer-ani </span><span class="si">{:.2f}</span><span class="s2">s ease 1;&quot;</span>
        <span class="p">)</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="n">x</span> <span class="o">-</span> <span class="mi">8</span><span class="p">,</span> <span class="n">y</span> <span class="o">-</span> <span class="mi">8</span><span class="p">,</span> <span class="n">size</span><span class="p">,</span> <span class="n">size</span><span class="p">,</span> <span class="n">duration</span><span class="p">)</span>
        <span class="n">script</span> <span class="o">=</span> <span class="p">(</span>
<span class="w">            </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">                var css = document.styleSheets[0];</span>
<span class="sd">                for( let css of [...document.styleSheets]) {{</span>
<span class="sd">                    try {{</span>
<span class="sd">                        css.insertRule(`</span>
<span class="sd">                        @keyframes show-pointer-ani {{</span>
<span class="sd">                              0% {{ opacity: 1; transform: scale(1, 1);}}</span>
<span class="sd">                              50% {{ transform: scale(3, 3);}}</span>
<span class="sd">                              100% {{ transform: scale(1, 1); opacity: 0;}}</span>
<span class="sd">                        }}`,css.cssRules.length);</span>
<span class="sd">                        break;</span>
<span class="sd">                    }} catch (e) {{</span>
<span class="sd">                        console.log(e)</span>
<span class="sd">                    }}</span>
<span class="sd">                }};</span>
<span class="sd">                var _d = document.createElement(&#39;div&#39;);</span>
<span class="sd">                _d.style = `{0:s}`;</span>
<span class="sd">                _d.id = `{1:s}`;</span>
<span class="sd">                document.body.insertAdjacentElement(&#39;afterBegin&#39;, _d);</span>
<span class="sd">    </span>
<span class="sd">                setTimeout( () =&gt; document.getElementById(&#39;{1:s}&#39;).remove(), {2:d});</span>
<span class="sd">    </span>
<span class="sd">            &quot;&quot;&quot;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span>
                <span class="n">style</span><span class="p">,</span> <span class="n">secrets</span><span class="o">.</span><span class="n">token_hex</span><span class="p">(</span><span class="mi">8</span><span class="p">),</span> <span class="nb">int</span><span class="p">(</span><span class="n">duration</span> <span class="o">*</span> <span class="mi">1000</span><span class="p">)</span>
            <span class="p">)</span>
            <span class="o">.</span><span class="n">replace</span><span class="p">(</span><span class="s2">&quot;  &quot;</span><span class="p">,</span> <span class="s2">&quot;&quot;</span><span class="p">)</span>
            <span class="o">.</span><span class="n">replace</span><span class="p">(</span><span class="s2">&quot;</span><span class="se">\n</span><span class="s2">&quot;</span><span class="p">,</span> <span class="s2">&quot;&quot;</span><span class="p">)</span>
        <span class="p">)</span>
        <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">send</span><span class="p">(</span>
            <span class="n">cdp</span><span class="o">.</span><span class="n">runtime</span><span class="o">.</span><span class="n">evaluate</span><span class="p">(</span>
                <span class="n">script</span><span class="p">,</span>
                <span class="n">await_promise</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
                <span class="n">user_gesture</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
            <span class="p">)</span>
        <span class="p">)</span></div>


    <span class="k">def</span> <span class="fm">__eq__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">other</span><span class="p">:</span> <span class="n">Tab</span><span class="p">):</span>
        <span class="k">try</span><span class="p">:</span>
            <span class="k">return</span> <span class="n">other</span><span class="o">.</span><span class="n">target</span> <span class="o">==</span> <span class="bp">self</span><span class="o">.</span><span class="n">target</span>
        <span class="k">except</span> <span class="p">(</span><span class="ne">AttributeError</span><span class="p">,</span> <span class="ne">TypeError</span><span class="p">):</span>
            <span class="k">return</span> <span class="kc">False</span>

    <span class="k">def</span> <span class="fm">__getattr__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">item</span><span class="p">):</span>
        <span class="k">try</span><span class="p">:</span>
            <span class="k">return</span> <span class="nb">getattr</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_target</span><span class="p">,</span> <span class="n">item</span><span class="p">)</span>
        <span class="k">except</span> <span class="ne">AttributeError</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">AttributeError</span><span class="p">(</span>
                <span class="sa">f</span><span class="s1">&#39;&quot;</span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="vm">__class__</span><span class="o">.</span><span class="vm">__name__</span><span class="si">}</span><span class="s1">&quot; has no attribute &quot;%s&quot;&#39;</span> <span class="o">%</span> <span class="n">item</span>
            <span class="p">)</span>

    <span class="k">def</span> <span class="fm">__repr__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="n">extra</span> <span class="o">=</span> <span class="s2">&quot;&quot;</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">target</span><span class="o">.</span><span class="n">url</span><span class="p">:</span>
            <span class="n">extra</span> <span class="o">=</span> <span class="sa">f</span><span class="s2">&quot;[url: </span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">target</span><span class="o">.</span><span class="n">url</span><span class="si">}</span><span class="s2">]&quot;</span>
        <span class="n">s</span> <span class="o">=</span> <span class="sa">f</span><span class="s2">&quot;&lt;</span><span class="si">{</span><span class="nb">type</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span><span class="o">.</span><span class="vm">__name__</span><span class="si">}</span><span class="s2"> [</span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">target_id</span><span class="si">}</span><span class="s2">] [</span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">type_</span><span class="si">}</span><span class="s2">] </span><span class="si">{</span><span class="n">extra</span><span class="si">}</span><span class="s2">&gt;&quot;</span>
        <span class="k">return</span> <span class="n">s</span></div>



<span class="c1">#</span>
<span class="c1"># from .connection import Transaction</span>
<span class="c1">#</span>
<span class="c1">#</span>
<span class="c1"># class TargetTransaction(Transaction):</span>
<span class="c1">#     session_id: cdp.target.SessionID</span>
<span class="c1">#</span>
<span class="c1">#     def __init__(self, cdp_obj: Generator, session_id: cdp.target.SessionID):</span>
<span class="c1">#         &quot;&quot;&quot;</span>
<span class="c1">#         :param cdp_obj:</span>
<span class="c1">#         &quot;&quot;&quot;</span>
<span class="c1">#         self.session_id = session_id</span>
<span class="c1">#         super().__init__(cdp_obj=cdp_obj)</span>
<span class="c1">#</span>
<span class="c1">#     @property</span>
<span class="c1">#     def message(self):</span>
<span class="c1">#         return json.dumps(</span>
<span class="c1">#             {</span>
<span class="c1">#                 &quot;method&quot;: self.method,</span>
<span class="c1">#                 &quot;params&quot;: self.params,</span>
<span class="c1">#                 &quot;id&quot;: self.id,</span>
<span class="c1">#                 &quot;sessionId&quot;: self.session_id,</span>
<span class="c1">#             }</span>
<span class="c1">#         )</span>
<span class="c1">#</span>
<span class="c1">#</span>
<span class="c1"># class TargetSession:</span>
<span class="c1">#</span>
<span class="c1">#     def __init__(self, tab: Tab):</span>
<span class="c1">#         self._tab = tab</span>
<span class="c1">#         self._browser = tab.browser</span>
<span class="c1">#         self._session_id = None</span>
<span class="c1">#         self._target_id = None</span>
<span class="c1">#</span>
<span class="c1">#     async def create_session(</span>
<span class="c1">#             self, target: Union[cdp.target.TargetID, cdp.target.TargetInfo]</span>
<span class="c1">#     ):</span>
<span class="c1">#         if isinstance(target, cdp.target.TargetID):</span>
<span class="c1">#             target = await self._tab.send(cdp.target.get_target_info(target))</span>
<span class="c1">#</span>
<span class="c1">#         self._target_id: cdp.target.TargetID = await self._tab.send(</span>
<span class="c1">#             cdp.target.create_target(url=&quot;&quot;)</span>
<span class="c1">#         )</span>
<span class="c1">#         self._session_id: cdp.target.SessionID = await self._tab.send(</span>
<span class="c1">#             cdp.target.attach_to_target(self._target_id, flatten=True)</span>
<span class="c1">#         )</span>
<span class="c1">#</span>
<span class="c1">#     async def send(self, cdp_obj: Generator[dict[str, Any], dict[str, Any], Any]):</span>
<span class="c1">#         tx = TargetTransaction(cdp_obj, self._session_id)</span>
<span class="c1">#         tx.id = next(self._tab.__count__)</span>
<span class="c1">#         self._tab.mapper.update({tx.id: tx})</span>
<span class="c1">#         return await self._tab.send(</span>
<span class="c1">#             cdp.target.send_message_to_target(</span>
<span class="c1">#                 json.dumps(tx.message), self._session_id, target_id=self._target_id</span>
<span class="c1">#             )</span>
<span class="c1">#         )</span>
<span class="c1">#</span>
<span class="c1"># #</span>
<span class="c1"># # class Frame(cdp.page.Frame):</span>
<span class="c1"># #     execution_contexts: typing.Dict[str, ExecutionContext] = {}</span>
<span class="c1"># #</span>
<span class="c1"># #     def __init__(self, id_: cdp.page.FrameId, **kw):</span>
<span class="c1"># #         none_gen = itertools.repeat(None)</span>
<span class="c1"># #         param_names = util.get_all_param_names(self.__class__)</span>
<span class="c1"># #         param_names.remove(&quot;execution_contexts&quot;)</span>
<span class="c1"># #         for k in kw:</span>
<span class="c1"># #             param_names.remove(k)</span>
<span class="c1"># #         params = dict(zip(param_names, none_gen))</span>
<span class="c1"># #         params.update({&quot;id_&quot;: id_, **kw})</span>
<span class="c1"># #         super().__init__(**params)</span>
<span class="c1">#</span>
<span class="c1"># #</span>
<span class="c1"># # class ExecutionContext(dict):</span>
<span class="c1"># #     id: cdp.runtime.ExecutionContextId</span>
<span class="c1"># #     frame_id: str</span>
<span class="c1"># #     unique_id: str</span>
<span class="c1"># #     _tab: Tab</span>
<span class="c1"># #</span>
<span class="c1"># #     def __init__(self, *a, **kw):</span>
<span class="c1"># #         super().__init__()</span>
<span class="c1"># #         super().__setattr__(&quot;__dict__&quot;, self)</span>
<span class="c1"># #         d: typing.Dict[str, Union[Tab, str]] = dict(*a, **kw)</span>
<span class="c1"># #         self._tab: Tab = d.pop(&quot;tab&quot;, None)</span>
<span class="c1"># #         self.__dict__.update(d)</span>
<span class="c1"># #</span>
<span class="c1"># #     def __repr__(self):</span>
<span class="c1"># #         return &quot;&lt;ExecutionContext (\n{}\n)&quot;.format(</span>
<span class="c1"># #             &quot;&quot;.join(f&quot;\t{k} = {v}\n&quot; for k, v in super().items() if k not in (&quot;_tab&quot;))</span>
<span class="c1"># #         )</span>
<span class="c1"># #</span>
<span class="c1"># #     async def evaluate(</span>
<span class="c1"># #             self,</span>
<span class="c1"># #             expression,</span>
<span class="c1"># #             allow_unsafe_eval_blocked_by_csp: bool = True,</span>
<span class="c1"># #             await_promises: bool = False,</span>
<span class="c1"># #             generate_preview: bool = False,</span>
<span class="c1"># #     ):</span>
<span class="c1"># #         try:</span>
<span class="c1"># #             raw = await self._tab.send(</span>
<span class="c1"># #                 cdp.runtime.evaluate(</span>
<span class="c1"># #                     expression=expression,</span>
<span class="c1"># #                     context_id=self.get(&quot;id_&quot;),</span>
<span class="c1"># #                     generate_preview=generate_preview,</span>
<span class="c1"># #                     return_by_value=False,</span>
<span class="c1"># #                     allow_unsafe_eval_blocked_by_csp=allow_unsafe_eval_blocked_by_csp,</span>
<span class="c1"># #                     await_promise=await_promises,</span>
<span class="c1"># #                 )</span>
<span class="c1"># #             )</span>
<span class="c1"># #             if raw:</span>
<span class="c1"># #                 remote_object, errors = raw</span>
<span class="c1"># #                 if errors:</span>
<span class="c1"># #                     raise ProtocolException(errors)</span>
<span class="c1"># #</span>
<span class="c1"># #                 if remote_object:</span>
<span class="c1"># #                     return remote_object</span>
<span class="c1"># #</span>
<span class="c1"># #                 # else:</span>
<span class="c1"># #                 #     return remote_object, errors</span>
<span class="c1"># #</span>
<span class="c1"># #         except:  # noqa</span>
<span class="c1"># #             raise</span>
</pre></div>
        </article>
      </div>
      <footer>
        
        <div class="related-pages">
          
          
        </div>
        <div class="bottom-of-page">
          <div class="left-details">
            <div class="copyright">
                Copyright &#169; 2023, Author
            </div>
            Made with <a href="https://www.sphinx-doc.org/">Sphinx</a> and <a class="muted-link" href="https://pradyunsg.me">@pradyunsg</a>'s
            
            <a href="https://github.com/pradyunsg/furo">Furo</a>
            
          </div>
          <div class="right-details">
            
          </div>
        </div>
        
      </footer>
    </div>
    <aside class="toc-drawer no-toc">
      
      
      
    </aside>
  </div>
</div><script src="../../../_static/documentation_options.js?v=5929fcd5"></script>
    <script src="../../../_static/doctools.js?v=888ff710"></script>
    <script src="../../../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../../../_static/scripts/furo.js?v=32e29ea5"></script>
    </body>
</html>