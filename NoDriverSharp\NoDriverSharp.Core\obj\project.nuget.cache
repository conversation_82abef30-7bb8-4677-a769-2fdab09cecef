{"version": 2, "dgSpecHash": "htS4rE0R+AQ=", "success": true, "projectFilePath": "c:\\Users\\<USER>\\source\\repos\\nodriver\\NoDriverSharp\\NoDriverSharp.Core\\NoDriverSharp.Core.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection.abstractions\\8.0.0\\microsoft.extensions.dependencyinjection.abstractions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.abstractions\\8.0.0\\microsoft.extensions.logging.abstractions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.json\\8.0.5\\system.text.json.8.0.5.nupkg.sha512"], "logs": [{"code": "NU1900", "level": "Warning", "message": "Error occurred while getting package vulnerability data: Unable to load the service index for source https://nuget.telerik.com/v3/index.json.", "projectPath": "c:\\Users\\<USER>\\source\\repos\\nodriver\\NoDriverSharp\\NoDriverSharp.Core\\NoDriverSharp.Core.csproj", "warningLevel": 1, "filePath": "c:\\Users\\<USER>\\source\\repos\\nodriver\\NoDriverSharp\\NoDriverSharp.Core\\NoDriverSharp.Core.csproj", "targetGraphs": []}]}