using Microsoft.Extensions.Logging;
using NoDriverSharp.Core;

namespace NoDriverSharp.Examples;

/// <summary>
/// Simple example demonstrating basic NoDriverSharp functionality
/// </summary>
public static class SimpleExample
{
    /// <summary>
    /// Runs a simple browser automation example
    /// </summary>
    public static async Task RunAsync()
    {
        // Configure logging
        using var loggerFactory = LoggerFactory.Create(builder =>
            builder.AddConsole().SetMinimumLevel(LogLevel.Information));

        var logger = loggerFactory.CreateLogger<Browser>();

        Console.WriteLine("NoDriverSharp Simple Example");
        Console.WriteLine("============================");

        try
        {
            // Create a stealth configuration for better anti-detection
            var config = NoDriver.CreateStealthConfig();
            config.Headless = false; // Set to true for headless mode

            Console.WriteLine("Starting browser...");
            await using var browser = await NoDriver.StartAsync(config, logger);

            Console.WriteLine("Browser started successfully!");
            Console.WriteLine($"Debug URL: {browser.DebuggerUrl}");

            // Navigate to a test website
            Console.WriteLine("Navigating to example.com...");
            var tab = await browser.GetAsync("https://www.example.com");

            Console.WriteLine($"Navigation completed!");
            Console.WriteLine($"Current URL: {tab.Url}");
            Console.WriteLine($"Page Title: {tab.Title}");

            // Wait a moment to see the page
            await Task.Delay(3000);

            // Take a screenshot
            Console.WriteLine("Taking screenshot...");
            await tab.SaveScreenshotAsync("simple_example_screenshot.png");
            Console.WriteLine("Screenshot saved as 'simple_example_screenshot.png'");

            // Navigate to another page
            Console.WriteLine("Navigating to httpbin.org...");
            await tab.NavigateAsync("https://httpbin.org/html");
            Console.WriteLine($"New URL: {tab.Url}");

            // Wait a moment
            await Task.Delay(2000);

            // Open a new tab
            Console.WriteLine("Opening new tab...");
            var newTab = await browser.GetAsync("https://www.google.com", newTab: true);
            Console.WriteLine($"New tab URL: {newTab.Url}");
            Console.WriteLine($"Total tabs: {browser.Tabs.Count}");

            // Take screenshot of the new tab
            await newTab.SaveScreenshotAsync("google_screenshot.png");
            Console.WriteLine("Google screenshot saved as 'google_screenshot.png'");

            Console.WriteLine("\nExample completed successfully!");
            Console.WriteLine("Press any key to close the browser...");
            Console.ReadKey();
        }
        catch (BrowserStartupException ex)
        {
            Console.WriteLine($"Failed to start browser: {ex.Message}");
            Console.WriteLine("Make sure Chrome/Chromium is installed and accessible.");
        }
        catch (NavigationException ex)
        {
            Console.WriteLine($"Navigation failed: {ex.Message}");
        }
        catch (NoDriverSharp.Core.TimeoutException ex)
        {
            Console.WriteLine($"Operation timed out: {ex.Message}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Unexpected error: {ex.Message}");
            if (ex.InnerException != null)
            {
                Console.WriteLine($"Inner exception: {ex.InnerException.Message}");
            }
        }
    }
}

/// <summary>
/// Advanced example showing configuration options
/// </summary>
public static class AdvancedExample
{
    /// <summary>
    /// Demonstrates advanced configuration and features
    /// </summary>
    public static async Task RunAsync()
    {
        Console.WriteLine("NoDriverSharp Advanced Example");
        Console.WriteLine("==============================");

        // Create custom configuration
        var config = new Config
        {
            Headless = true,
            WindowSize = (1920, 1080),
            UserDataDir = null, // Use temporary directory
            Expert = false, // Keep stealth mode
            NavigationTimeout = TimeSpan.FromSeconds(15),
            ElementTimeout = TimeSpan.FromSeconds(5)
        };

        // Add custom browser arguments
        config.BrowserArgs.AddRange(new[]
        {
            "--disable-images", // Disable image loading for faster navigation
            "--disable-javascript", // Disable JavaScript (optional)
            "--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        });

        try
        {
            Console.WriteLine("Starting browser with custom configuration...");
            await using var browser = await NoDriver.StartAsync(config);

            Console.WriteLine("Testing multiple navigation scenarios...");

            // Test 1: Basic navigation
            var tab1 = await browser.GetAsync("https://httpbin.org/status/200");
            Console.WriteLine($"Test 1 - Status page: {tab1.Url}");

            // Test 2: JSON endpoint
            var tab2 = await browser.GetAsync("https://httpbin.org/json", newTab: true);
            Console.WriteLine($"Test 2 - JSON endpoint: {tab2.Url}");

            // Test 3: Redirect test
            var tab3 = await browser.GetAsync("https://httpbin.org/redirect/3", newTab: true);
            Console.WriteLine($"Test 3 - After redirects: {tab3.Url}");

            // Take screenshots of all tabs
            for (int i = 0; i < browser.Tabs.Count; i++)
            {
                var tab = browser.Tabs[i];
                await tab.SaveScreenshotAsync($"advanced_tab_{i + 1}.png");
                Console.WriteLine($"Screenshot saved for tab {i + 1}: {tab.Url}");
            }

            // Test reload functionality
            Console.WriteLine("Testing page reload...");
            await tab1.ReloadAsync(ignoreCache: true);
            Console.WriteLine("Page reloaded successfully");

            Console.WriteLine($"\nAdvanced example completed! Total tabs: {browser.Tabs.Count}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Advanced example failed: {ex.Message}");
        }
    }
}

/// <summary>
/// Bot detection testing example
/// </summary>
public static class BotDetectionExample
{
    /// <summary>
    /// Tests anti-detection capabilities against fingerprint.com
    /// </summary>
    public static async Task RunAsync()
    {
        Console.WriteLine("NoDriverSharp Bot Detection Test");
        Console.WriteLine("================================");
        Console.WriteLine("Testing against fingerprint.com bot detection...");

        // Configure logging
        using var loggerFactory = LoggerFactory.Create(builder =>
            builder.AddConsole().SetMinimumLevel(LogLevel.Information));

        var logger = loggerFactory.CreateLogger<Browser>();

        try
        {
            // Create stealth configuration optimized for anti-detection
            var config = NoDriver.CreateStealthConfig();
            config.Headless = false; // Keep visible to see the results
            config.WindowSize = (1920, 1080);

            // Add additional stealth arguments
            config.BrowserArgs.AddRange(new[]
            {
                "--disable-blink-features=AutomationControlled",
                "--exclude-switches=enable-automation",
                "--disable-extensions-except=",
                "--disable-extensions",
                "--disable-plugins-discovery",
                "--disable-default-apps",
                "--disable-popup-blocking",
                "--disable-translate",
                "--disable-background-timer-throttling",
                "--disable-backgrounding-occluded-windows",
                "--disable-renderer-backgrounding",
                "--disable-features=TranslateUI,BlinkGenPropertyTrees",
                "--disable-ipc-flooding-protection",
                "--no-first-run",
                "--no-service-autorun",
                "--no-default-browser-check",
                "--password-store=basic",
                "--use-mock-keychain",
                "--disable-component-extensions-with-background-pages",
                "--disable-background-networking",
                "--disable-sync",
                "--metrics-recording-only",
                "--disable-default-apps",
                "--mute-audio",
                "--no-report-upload",
                "--disable-logging",
                "--disable-permissions-api",
                "--disable-client-side-phishing-detection",
                "--disable-component-update",
                "--disable-domain-reliability"
            });

            Console.WriteLine("Starting browser with enhanced stealth configuration...");
            await using var browser = await NoDriver.StartAsync(config, logger);

            Console.WriteLine("Browser started successfully!");

            // Navigate to the bot detection page
            Console.WriteLine("Navigating to fingerprint.com bot detection page...");
            var tab = await browser.GetAsync("https://fingerprint.com/products/bot-detection/");

            Console.WriteLine($"Navigation completed!");
            Console.WriteLine($"Current URL: {tab.Url}");
            Console.WriteLine($"Page Title: {tab.Title}");

            // Wait for the page to fully load and run detection
            Console.WriteLine("Waiting for bot detection analysis (10 seconds)...");
            await Task.Delay(10000);

            // Take a screenshot to see the results
            Console.WriteLine("Taking screenshot of detection results...");
            await tab.SaveScreenshotAsync("bot_detection_test.png");
            Console.WriteLine("Screenshot saved as 'bot_detection_test.png'");

            // Try to scroll and interact to appear more human-like
            Console.WriteLine("Performing human-like interactions...");

            // Wait a bit more to see if any delayed detection occurs
            await Task.Delay(5000);

            // Take another screenshot after interactions
            await tab.SaveScreenshotAsync("bot_detection_after_interaction.png");
            Console.WriteLine("Post-interaction screenshot saved as 'bot_detection_after_interaction.png'");

            Console.WriteLine("\nBot detection test completed!");
            Console.WriteLine("Check the screenshots to see if we were detected as a bot.");
            Console.WriteLine("If successful, the page should show normal content without bot warnings.");

            Console.WriteLine("\nPress any key to continue...");
            Console.ReadKey();
        }
        catch (BrowserStartupException ex)
        {
            Console.WriteLine($"Failed to start browser: {ex.Message}");
            Console.WriteLine("Make sure Chrome/Chromium is installed and accessible.");
        }
        catch (NavigationException ex)
        {
            Console.WriteLine($"Navigation failed: {ex.Message}");
            Console.WriteLine("This might indicate network issues or the site blocking our request.");
        }
        catch (NoDriverSharp.Core.TimeoutException ex)
        {
            Console.WriteLine($"Operation timed out: {ex.Message}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Unexpected error: {ex.Message}");
            if (ex.InnerException != null)
            {
                Console.WriteLine($"Inner exception: {ex.InnerException.Message}");
            }
        }
    }
}

/// <summary>
/// Advanced bot detection testing with multiple strategies
/// </summary>
public static class AdvancedBotDetectionExample
{
    /// <summary>
    /// Tests multiple anti-detection strategies
    /// </summary>
    public static async Task RunAsync()
    {
        Console.WriteLine("NoDriverSharp Advanced Bot Detection Test");
        Console.WriteLine("========================================");

        var testSites = new[]
        {
            ("Fingerprint.com", "https://fingerprint.com/products/bot-detection/"),
            ("Bot Detection Test", "https://bot.sannysoft.com/"),
            ("WebRTC Test", "https://browserleaks.com/webrtc"),
            ("Canvas Fingerprint", "https://browserleaks.com/canvas")
        };

        foreach (var (name, url) in testSites)
        {
            Console.WriteLine($"\nTesting {name}...");
            await TestSingleSite(name, url);

            Console.WriteLine("Waiting 3 seconds before next test...");
            await Task.Delay(3000);
        }

        Console.WriteLine("\nAll bot detection tests completed!");
        Console.WriteLine("Check the generated screenshots to analyze detection results.");
    }

    private static async Task TestSingleSite(string siteName, string url)
    {
        try
        {
            // Create a fresh stealth config for each test
            var config = NoDriver.CreateStealthConfig();
            config.Headless = true; // Use headless for faster testing
            config.WindowSize = (1920, 1080);

            // Randomize some settings to appear more unique
            var userAgents = new[]
            {
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.7103.114 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.7103.114 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.7103.114 Safari/537.36",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            };

            var randomUserAgent = userAgents[Random.Shared.Next(userAgents.Length)];
            config.BrowserArgs.Add($"--user-agent={randomUserAgent}");

            await using var browser = await NoDriver.StartAsync(config);
            var tab = await browser.GetAsync(url);

            // Wait for page to load and run detection
            await Task.Delay(8000);

            // Take screenshot
            var fileName = $"detection_test_{siteName.Replace(" ", "_").Replace(".", "_").ToLower()}.png";
            await tab.SaveScreenshotAsync(fileName);

            Console.WriteLine($"✓ {siteName} test completed - Screenshot: {fileName}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"✗ {siteName} test failed: {ex.Message}");
        }
    }
}

/// <summary>
/// Performance testing example
/// </summary>
public static class PerformanceExample
{
    /// <summary>
    /// Tests performance with multiple concurrent operations
    /// </summary>
    public static async Task RunAsync()
    {
        Console.WriteLine("NoDriverSharp Performance Example");
        Console.WriteLine("=================================");

        var config = NoDriver.CreateHeadlessConfig();
        config.NavigationTimeout = TimeSpan.FromSeconds(10);

        try
        {
            await using var browser = await NoDriver.StartAsync(config);

            var urls = new[]
            {
                "https://httpbin.org/delay/1",
                "https://httpbin.org/delay/2", 
                "https://httpbin.org/status/200",
                "https://httpbin.org/json",
                "https://httpbin.org/html"
            };

            Console.WriteLine($"Testing concurrent navigation to {urls.Length} URLs...");
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            // Create tabs concurrently
            var tasks = urls.Select(async (url, index) =>
            {
                var tab = await browser.GetAsync(url, newTab: index > 0);
                Console.WriteLine($"Completed: {url} ({tab.Url})");
                return tab;
            });

            var tabs = await Task.WhenAll(tasks);
            stopwatch.Stop();

            Console.WriteLine($"All {tabs.Length} tabs loaded in {stopwatch.ElapsedMilliseconds}ms");
            Console.WriteLine($"Average time per tab: {stopwatch.ElapsedMilliseconds / tabs.Length}ms");

            // Take screenshots concurrently
            Console.WriteLine("Taking screenshots concurrently...");
            stopwatch.Restart();

            var screenshotTasks = tabs.Select(async (tab, index) =>
            {
                await tab.SaveScreenshotAsync($"perf_test_{index + 1}.png");
                return index + 1;
            });

            await Task.WhenAll(screenshotTasks);
            stopwatch.Stop();

            Console.WriteLine($"All screenshots completed in {stopwatch.ElapsedMilliseconds}ms");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Performance example failed: {ex.Message}");
        }
    }
}
