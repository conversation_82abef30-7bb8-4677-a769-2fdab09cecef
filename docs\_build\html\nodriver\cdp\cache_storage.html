<!doctype html>
<html class="no-js" lang="en" data-content_root="../../">
  <head><meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width,initial-scale=1"/>
    <meta name="color-scheme" content="light dark"><meta name="viewport" content="width=device-width, initial-scale=1" />
<link rel="index" title="Index" href="../../genindex.html" /><link rel="search" title="Search" href="../../search.html" /><link rel="next" title="Cast" href="cast.html" /><link rel="prev" title="Browser" href="browser.html" />

    <!-- Generated with Sphinx 7.2.6 and Furo 2023.09.10 -->
        <title>CacheStorage - nodriver documentation</title>
      <link rel="stylesheet" type="text/css" href="../../_static/pygments.css?v=fa44fd50" />
    <link rel="stylesheet" type="text/css" href="../../_static/styles/furo.css?v=135e06be" />
    <link rel="stylesheet" type="text/css" href="../../_static/styles/furo-extensions.css?v=36a5483c" />
    <link rel="stylesheet" type="text/css" href="../../_static/./custom.css?v=3934642d" />
    
    


<style>
  body {
    --color-code-background: #eeffcc;
  --color-code-foreground: black;
  
  }
  @media not print {
    body[data-theme="dark"] {
      --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8eefba;
  --color-background-primary: #111112;
  --color-problematic: #e7aeae;
  
    }
    @media (prefers-color-scheme: dark) {
      body:not([data-theme="light"]) {
        --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8eefba;
  --color-background-primary: #111112;
  --color-problematic: #e7aeae;
  
      }
    }
  }
</style></head>
  <body>
    
    <script>
      document.body.dataset.theme = localStorage.getItem("theme") || "auto";
    </script>
    

<svg xmlns="http://www.w3.org/2000/svg" style="display: none;">
  <symbol id="svg-toc" viewBox="0 0 24 24">
    <title>Contents</title>
    <svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 1024 1024">
      <path d="M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM115.4 518.9L271.7 642c5.8 4.6 14.4.5 14.4-6.9V388.9c0-7.4-8.5-11.5-14.4-6.9L115.4 505.1a8.74 8.74 0 0 0 0 13.8z"/>
    </svg>
  </symbol>
  <symbol id="svg-menu" viewBox="0 0 24 24">
    <title>Menu</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-menu">
      <line x1="3" y1="12" x2="21" y2="12"></line>
      <line x1="3" y1="6" x2="21" y2="6"></line>
      <line x1="3" y1="18" x2="21" y2="18"></line>
    </svg>
  </symbol>
  <symbol id="svg-arrow-right" viewBox="0 0 24 24">
    <title>Expand</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-chevron-right">
      <polyline points="9 18 15 12 9 6"></polyline>
    </svg>
  </symbol>
  <symbol id="svg-sun" viewBox="0 0 24 24">
    <title>Light mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="feather-sun">
      <circle cx="12" cy="12" r="5"></circle>
      <line x1="12" y1="1" x2="12" y2="3"></line>
      <line x1="12" y1="21" x2="12" y2="23"></line>
      <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
      <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
      <line x1="1" y1="12" x2="3" y2="12"></line>
      <line x1="21" y1="12" x2="23" y2="12"></line>
      <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
      <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
    </svg>
  </symbol>
  <symbol id="svg-moon" viewBox="0 0 24 24">
    <title>Dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-moon">
      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
      <path d="M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z" />
    </svg>
  </symbol>
  <symbol id="svg-sun-half" viewBox="0 0 24 24">
    <title>Auto light/dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-shadow">
      <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
      <circle cx="12" cy="12" r="9" />
      <path d="M13 12h5" />
      <path d="M13 15h4" />
      <path d="M13 18h1" />
      <path d="M13 9h4" />
      <path d="M13 6h1" />
    </svg>
  </symbol>
</svg>

<input type="checkbox" class="sidebar-toggle" name="__navigation" id="__navigation">
<input type="checkbox" class="sidebar-toggle" name="__toc" id="__toc">
<label class="overlay sidebar-overlay" for="__navigation">
  <div class="visually-hidden">Hide navigation sidebar</div>
</label>
<label class="overlay toc-overlay" for="__toc">
  <div class="visually-hidden">Hide table of contents sidebar</div>
</label>



<div class="page">
  <header class="mobile-header">
    <div class="header-left">
      <label class="nav-overlay-icon" for="__navigation">
        <div class="visually-hidden">Toggle site navigation sidebar</div>
        <i class="icon"><svg><use href="#svg-menu"></use></svg></i>
      </label>
    </div>
    <div class="header-center">
      <a href="../../index.html"><div class="brand">nodriver  documentation</div></a>
    </div>
    <div class="header-right">
      <div class="theme-toggle-container theme-toggle-header">
        <button class="theme-toggle">
          <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
          <svg class="theme-icon-when-auto"><use href="#svg-sun-half"></use></svg>
          <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
          <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
        </button>
      </div>
      <label class="toc-overlay-icon toc-header-icon" for="__toc">
        <div class="visually-hidden">Toggle table of contents sidebar</div>
        <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
      </label>
    </div>
  </header>
  <aside class="sidebar-drawer">
    <div class="sidebar-container">
      
      <div class="sidebar-sticky"><div class="sidebar-scroll"><a class="sidebar-brand" href="../../index.html">
  
  
  <span class="sidebar-brand-text">nodriver  documentation</span>
  
</a><form class="sidebar-search-container" method="get" action="../../search.html" role="search">
  <input class="sidebar-search" placeholder="Search" name="q" aria-label="Search">
  <input type="hidden" name="check_keywords" value="yes">
  <input type="hidden" name="area" value="default">
</form>
<div id="searchbox"></div><div class="sidebar-tree">
  <ul>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html">Quickstart guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#usage-example">usage example</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#more-complete-example">More complete example</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#custom-starting-options">Custom starting options</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#alternative-custom-options">Alternative custom options</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#proxies-socks5-authenticated-too">Proxies (socks5, authenticated too)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#concrete-example">Concrete example</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../classes/browser.html">Browser class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../classes/tab.html">Tab class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../classes/element.html">Element class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../classes/others_and_helpers.html">Other classes and Helper classes</a></li>
</ul>
<ul class="current">
<li class="toctree-l1 current has-children"><a class="reference internal" href="../cdp.html">CDP object</a><input checked="" class="toctree-checkbox" id="toctree-checkbox-1" name="toctree-checkbox-1" role="switch" type="checkbox"/><label for="toctree-checkbox-1"><div class="visually-hidden">Toggle navigation of CDP object</div><i class="icon"><svg><use href="#svg-arrow-right"></use></svg></i></label><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="accessibility.html">Accessibility</a></li>
<li class="toctree-l2"><a class="reference internal" href="animation.html">Animation</a></li>
<li class="toctree-l2"><a class="reference internal" href="audits.html">Audits</a></li>
<li class="toctree-l2"><a class="reference internal" href="autofill.html">Autofill</a></li>
<li class="toctree-l2"><a class="reference internal" href="background_service.html">BackgroundService</a></li>
<li class="toctree-l2"><a class="reference internal" href="bluetooth_emulation.html">BluetoothEmulation</a></li>
<li class="toctree-l2"><a class="reference internal" href="browser.html">Browser</a></li>
<li class="toctree-l2 current current-page"><a class="current reference internal" href="#">CacheStorage</a></li>
<li class="toctree-l2"><a class="reference internal" href="cast.html">Cast</a></li>
<li class="toctree-l2"><a class="reference internal" href="console.html">Console</a></li>
<li class="toctree-l2"><a class="reference internal" href="css.html">CSS</a></li>
<li class="toctree-l2"><a class="reference internal" href="debugger.html">Debugger</a></li>
<li class="toctree-l2"><a class="reference internal" href="device_access.html">DeviceAccess</a></li>
<li class="toctree-l2"><a class="reference internal" href="device_orientation.html">DeviceOrientation</a></li>
<li class="toctree-l2"><a class="reference internal" href="dom.html">DOM</a></li>
<li class="toctree-l2"><a class="reference internal" href="dom_debugger.html">DOMDebugger</a></li>
<li class="toctree-l2"><a class="reference internal" href="dom_snapshot.html">DOMSnapshot</a></li>
<li class="toctree-l2"><a class="reference internal" href="dom_storage.html">DOMStorage</a></li>
<li class="toctree-l2"><a class="reference internal" href="emulation.html">Emulation</a></li>
<li class="toctree-l2"><a class="reference internal" href="event_breakpoints.html">EventBreakpoints</a></li>
<li class="toctree-l2"><a class="reference internal" href="extensions.html">Extensions</a></li>
<li class="toctree-l2"><a class="reference internal" href="fed_cm.html">FedCm</a></li>
<li class="toctree-l2"><a class="reference internal" href="fetch.html">Fetch</a></li>
<li class="toctree-l2"><a class="reference internal" href="file_system.html">FileSystem</a></li>
<li class="toctree-l2"><a class="reference internal" href="headless_experimental.html">HeadlessExperimental</a></li>
<li class="toctree-l2"><a class="reference internal" href="heap_profiler.html">HeapProfiler</a></li>
<li class="toctree-l2"><a class="reference internal" href="indexed_db.html">IndexedDB</a></li>
<li class="toctree-l2"><a class="reference internal" href="input_.html">Input</a></li>
<li class="toctree-l2"><a class="reference internal" href="inspector.html">Inspector</a></li>
<li class="toctree-l2"><a class="reference internal" href="io.html">IO</a></li>
<li class="toctree-l2"><a class="reference internal" href="layer_tree.html">LayerTree</a></li>
<li class="toctree-l2"><a class="reference internal" href="log.html">Log</a></li>
<li class="toctree-l2"><a class="reference internal" href="media.html">Media</a></li>
<li class="toctree-l2"><a class="reference internal" href="memory.html">Memory</a></li>
<li class="toctree-l2"><a class="reference internal" href="network.html">Network</a></li>
<li class="toctree-l2"><a class="reference internal" href="overlay.html">Overlay</a></li>
<li class="toctree-l2"><a class="reference internal" href="page.html">Page</a></li>
<li class="toctree-l2"><a class="reference internal" href="performance.html">Performance</a></li>
<li class="toctree-l2"><a class="reference internal" href="performance_timeline.html">PerformanceTimeline</a></li>
<li class="toctree-l2"><a class="reference internal" href="preload.html">Preload</a></li>
<li class="toctree-l2"><a class="reference internal" href="profiler.html">Profiler</a></li>
<li class="toctree-l2"><a class="reference internal" href="pwa.html">PWA</a></li>
<li class="toctree-l2"><a class="reference internal" href="runtime.html">Runtime</a></li>
<li class="toctree-l2"><a class="reference internal" href="schema.html">Schema</a></li>
<li class="toctree-l2"><a class="reference internal" href="security.html">Security</a></li>
<li class="toctree-l2"><a class="reference internal" href="service_worker.html">ServiceWorker</a></li>
<li class="toctree-l2"><a class="reference internal" href="storage.html">Storage</a></li>
<li class="toctree-l2"><a class="reference internal" href="system_info.html">SystemInfo</a></li>
<li class="toctree-l2"><a class="reference internal" href="target.html">Target</a></li>
<li class="toctree-l2"><a class="reference internal" href="tethering.html">Tethering</a></li>
<li class="toctree-l2"><a class="reference internal" href="tracing.html">Tracing</a></li>
<li class="toctree-l2"><a class="reference internal" href="web_audio.html">WebAudio</a></li>
<li class="toctree-l2"><a class="reference internal" href="web_authn.html">WebAuthn</a></li>
</ul>
</li>
</ul>

</div>
</div>
      </div>
      
    </div>
  </aside>
  <div class="main">
    <div class="content">
      <div class="article-container">
        <a href="#" class="back-to-top muted-link">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
            <path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8v12z"></path>
          </svg>
          <span>Back to top</span>
        </a>
        <div class="content-icon-container">
          
<div class="theme-toggle-container theme-toggle-content">
            <button class="theme-toggle">
              <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
              <svg class="theme-icon-when-auto"><use href="#svg-sun-half"></use></svg>
              <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
              <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
            </button>
          </div>
          <label class="toc-overlay-icon toc-content-icon" for="__toc">
            <div class="visually-hidden">Toggle table of contents sidebar</div>
            <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
          </label>
        </div>
        <article role="main">
          <section id="cachestorage">
<h1>CacheStorage<a class="headerlink" href="#cachestorage" title="Link to this heading">#</a></h1>
<p><em>This CDP domain is experimental.</em></p>
<ul class="simple" id="module-nodriver.cdp.cache_storage">
<li><p><a class="reference internal" href="#types">Types</a></p></li>
<li><p><a class="reference internal" href="#commands">Commands</a></p></li>
<li><p><a class="reference internal" href="#events">Events</a></p></li>
</ul>
<section id="types">
<h2>Types<a class="headerlink" href="#types" title="Link to this heading">#</a></h2>
<p>Generally, you do not need to instantiate CDP types
yourself. Instead, the API creates objects for you as return
values from commands, and then you can use those objects as
arguments to other commands.</p>
<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.cache_storage.CacheId">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">CacheId</span></span><a class="reference internal" href="../../_modules/nodriver/cdp/cache_storage.html#CacheId"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.cache_storage.CacheId" title="Link to this definition">#</a></dt>
<dd><p>Unique identifier of the Cache object.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.cache_storage.CachedResponseType">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">CachedResponseType</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">names</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">module</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">qualname</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">boundary</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/cache_storage.html#CachedResponseType"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.cache_storage.CachedResponseType" title="Link to this definition">#</a></dt>
<dd><p>type of HTTP response cached</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.cache_storage.CachedResponseType.BASIC">
<span class="sig-name descname"><span class="pre">BASIC</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'basic'</span></em><a class="headerlink" href="#nodriver.cdp.cache_storage.CachedResponseType.BASIC" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.cache_storage.CachedResponseType.CORS">
<span class="sig-name descname"><span class="pre">CORS</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'cors'</span></em><a class="headerlink" href="#nodriver.cdp.cache_storage.CachedResponseType.CORS" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.cache_storage.CachedResponseType.DEFAULT">
<span class="sig-name descname"><span class="pre">DEFAULT</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'default'</span></em><a class="headerlink" href="#nodriver.cdp.cache_storage.CachedResponseType.DEFAULT" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.cache_storage.CachedResponseType.ERROR">
<span class="sig-name descname"><span class="pre">ERROR</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'error'</span></em><a class="headerlink" href="#nodriver.cdp.cache_storage.CachedResponseType.ERROR" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.cache_storage.CachedResponseType.OPAQUE_RESPONSE">
<span class="sig-name descname"><span class="pre">OPAQUE_RESPONSE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'opaqueResponse'</span></em><a class="headerlink" href="#nodriver.cdp.cache_storage.CachedResponseType.OPAQUE_RESPONSE" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.cache_storage.CachedResponseType.OPAQUE_REDIRECT">
<span class="sig-name descname"><span class="pre">OPAQUE_REDIRECT</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'opaqueRedirect'</span></em><a class="headerlink" href="#nodriver.cdp.cache_storage.CachedResponseType.OPAQUE_REDIRECT" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.cache_storage.DataEntry">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">DataEntry</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">request_url</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">request_method</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">request_headers</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">response_time</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">response_status</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">response_status_text</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">response_type</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">response_headers</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/cache_storage.html#DataEntry"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.cache_storage.DataEntry" title="Link to this definition">#</a></dt>
<dd><p>Data entry.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.cache_storage.DataEntry.request_url">
<span class="sig-name descname"><span class="pre">request_url</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.cache_storage.DataEntry.request_url" title="Link to this definition">#</a></dt>
<dd><p>Request URL.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.cache_storage.DataEntry.request_method">
<span class="sig-name descname"><span class="pre">request_method</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.cache_storage.DataEntry.request_method" title="Link to this definition">#</a></dt>
<dd><p>Request method.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.cache_storage.DataEntry.request_headers">
<span class="sig-name descname"><span class="pre">request_headers</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.cache_storage.Header" title="nodriver.cdp.cache_storage.Header"><code class="xref py py-class docutils literal notranslate"><span class="pre">Header</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.cache_storage.DataEntry.request_headers" title="Link to this definition">#</a></dt>
<dd><p>Request headers</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.cache_storage.DataEntry.response_time">
<span class="sig-name descname"><span class="pre">response_time</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a></em><a class="headerlink" href="#nodriver.cdp.cache_storage.DataEntry.response_time" title="Link to this definition">#</a></dt>
<dd><p>Number of seconds since epoch.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.cache_storage.DataEntry.response_status">
<span class="sig-name descname"><span class="pre">response_status</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></em><a class="headerlink" href="#nodriver.cdp.cache_storage.DataEntry.response_status" title="Link to this definition">#</a></dt>
<dd><p>HTTP response status code.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.cache_storage.DataEntry.response_status_text">
<span class="sig-name descname"><span class="pre">response_status_text</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.cache_storage.DataEntry.response_status_text" title="Link to this definition">#</a></dt>
<dd><p>HTTP response status text.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.cache_storage.DataEntry.response_type">
<span class="sig-name descname"><span class="pre">response_type</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.cache_storage.CachedResponseType" title="nodriver.cdp.cache_storage.CachedResponseType"><code class="xref py py-class docutils literal notranslate"><span class="pre">CachedResponseType</span></code></a></em><a class="headerlink" href="#nodriver.cdp.cache_storage.DataEntry.response_type" title="Link to this definition">#</a></dt>
<dd><p>HTTP response type</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.cache_storage.DataEntry.response_headers">
<span class="sig-name descname"><span class="pre">response_headers</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.cache_storage.Header" title="nodriver.cdp.cache_storage.Header"><code class="xref py py-class docutils literal notranslate"><span class="pre">Header</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.cache_storage.DataEntry.response_headers" title="Link to this definition">#</a></dt>
<dd><p>Response headers</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.cache_storage.Cache">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">Cache</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cache_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">security_origin</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">storage_key</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">cache_name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">storage_bucket</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/cache_storage.html#Cache"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.cache_storage.Cache" title="Link to this definition">#</a></dt>
<dd><p>Cache identifier.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.cache_storage.Cache.cache_id">
<span class="sig-name descname"><span class="pre">cache_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.cache_storage.CacheId" title="nodriver.cdp.cache_storage.CacheId"><code class="xref py py-class docutils literal notranslate"><span class="pre">CacheId</span></code></a></em><a class="headerlink" href="#nodriver.cdp.cache_storage.Cache.cache_id" title="Link to this definition">#</a></dt>
<dd><p>An opaque unique id of the cache.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.cache_storage.Cache.security_origin">
<span class="sig-name descname"><span class="pre">security_origin</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.cache_storage.Cache.security_origin" title="Link to this definition">#</a></dt>
<dd><p>Security origin of the cache.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.cache_storage.Cache.storage_key">
<span class="sig-name descname"><span class="pre">storage_key</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.cache_storage.Cache.storage_key" title="Link to this definition">#</a></dt>
<dd><p>Storage key of the cache.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.cache_storage.Cache.cache_name">
<span class="sig-name descname"><span class="pre">cache_name</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.cache_storage.Cache.cache_name" title="Link to this definition">#</a></dt>
<dd><p>The name of the cache.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.cache_storage.Cache.storage_bucket">
<span class="sig-name descname"><span class="pre">storage_bucket</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="storage.html#nodriver.cdp.storage.StorageBucket" title="nodriver.cdp.storage.StorageBucket"><code class="xref py py-class docutils literal notranslate"><span class="pre">StorageBucket</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.cache_storage.Cache.storage_bucket" title="Link to this definition">#</a></dt>
<dd><p>Storage bucket of the cache.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.cache_storage.Header">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">Header</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/cache_storage.html#Header"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.cache_storage.Header" title="Link to this definition">#</a></dt>
<dd><dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.cache_storage.Header.name">
<span class="sig-name descname"><span class="pre">name</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.cache_storage.Header.name" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.cache_storage.Header.value">
<span class="sig-name descname"><span class="pre">value</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.cache_storage.Header.value" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.cache_storage.CachedResponse">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">CachedResponse</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">body</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/cache_storage.html#CachedResponse"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.cache_storage.CachedResponse" title="Link to this definition">#</a></dt>
<dd><p>Cached response</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.cache_storage.CachedResponse.body">
<span class="sig-name descname"><span class="pre">body</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.cache_storage.CachedResponse.body" title="Link to this definition">#</a></dt>
<dd><p>Entry content, base64-encoded. (Encoded as a base64 string when passed over JSON)</p>
</dd></dl>

</dd></dl>

</section>
<section id="commands">
<h2>Commands<a class="headerlink" href="#commands" title="Link to this heading">#</a></h2>
<p>Each command is a generator function. The return
type <code class="docutils literal notranslate"><span class="pre">Generator[x,</span> <span class="pre">y,</span> <span class="pre">z]</span></code> indicates that the generator
<em>yields</em> arguments of type <code class="docutils literal notranslate"><span class="pre">x</span></code>, it must be resumed with
an argument of type <code class="docutils literal notranslate"><span class="pre">y</span></code>, and it returns type <code class="docutils literal notranslate"><span class="pre">z</span></code>. In
this library, types <code class="docutils literal notranslate"><span class="pre">x</span></code> and <code class="docutils literal notranslate"><span class="pre">y</span></code> are the same for all
commands, and <code class="docutils literal notranslate"><span class="pre">z</span></code> is the return type you should pay attention
to. For more information, see
<a class="reference internal" href="../../readme.html#getting-started-commands"><span class="std std-ref">Getting Started: Commands</span></a>.</p>
<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.cache_storage.delete_cache">
<span class="sig-name descname"><span class="pre">delete_cache</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cache_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/cache_storage.html#delete_cache"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.cache_storage.delete_cache" title="Link to this definition">#</a></dt>
<dd><p>Deletes a cache.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>cache_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.cache_storage.CacheId" title="nodriver.cdp.cache_storage.CacheId"><code class="xref py py-class docutils literal notranslate"><span class="pre">CacheId</span></code></a></span>) – Id of cache for deletion.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.cache_storage.delete_entry">
<span class="sig-name descname"><span class="pre">delete_entry</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cache_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">request</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/cache_storage.html#delete_entry"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.cache_storage.delete_entry" title="Link to this definition">#</a></dt>
<dd><p>Deletes a cache entry.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>cache_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.cache_storage.CacheId" title="nodriver.cdp.cache_storage.CacheId"><code class="xref py py-class docutils literal notranslate"><span class="pre">CacheId</span></code></a></span>) – Id of cache where the entry will be deleted.</p></li>
<li><p><strong>request</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – URL spec of the request.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.cache_storage.request_cache_names">
<span class="sig-name descname"><span class="pre">request_cache_names</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">security_origin</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">storage_key</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">storage_bucket</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/cache_storage.html#request_cache_names"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.cache_storage.request_cache_names" title="Link to this definition">#</a></dt>
<dd><p>Requests cache names.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>security_origin</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]</span>) – <em>(Optional)</em> At least and at most one of securityOrigin, storageKey, storageBucket must be specified. Security origin.</p></li>
<li><p><strong>storage_key</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]</span>) – <em>(Optional)</em> Storage key.</p></li>
<li><p><strong>storage_bucket</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="storage.html#nodriver.cdp.storage.StorageBucket" title="nodriver.cdp.storage.StorageBucket"><code class="xref py py-class docutils literal notranslate"><span class="pre">StorageBucket</span></code></a>]</span>) – <em>(Optional)</em> Storage bucket. If not specified, it uses the default bucket.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.cache_storage.Cache" title="nodriver.cdp.cache_storage.Cache"><code class="xref py py-class docutils literal notranslate"><span class="pre">Cache</span></code></a>]]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>Caches for the security origin.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.cache_storage.request_cached_response">
<span class="sig-name descname"><span class="pre">request_cached_response</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cache_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">request_url</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">request_headers</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/cache_storage.html#request_cached_response"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.cache_storage.request_cached_response" title="Link to this definition">#</a></dt>
<dd><p>Fetches cache entry.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>cache_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.cache_storage.CacheId" title="nodriver.cdp.cache_storage.CacheId"><code class="xref py py-class docutils literal notranslate"><span class="pre">CacheId</span></code></a></span>) – Id of cache that contains the entry.</p></li>
<li><p><strong>request_url</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – URL spec of the request.</p></li>
<li><p><strong>request_headers</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.cache_storage.Header" title="nodriver.cdp.cache_storage.Header"><code class="xref py py-class docutils literal notranslate"><span class="pre">Header</span></code></a>]</span>) – headers of the request.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference internal" href="#nodriver.cdp.cache_storage.CachedResponse" title="nodriver.cdp.cache_storage.CachedResponse"><code class="xref py py-class docutils literal notranslate"><span class="pre">CachedResponse</span></code></a>]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>Response read from the cache.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.cache_storage.request_entries">
<span class="sig-name descname"><span class="pre">request_entries</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cache_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">skip_count</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">page_size</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">path_filter</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/cache_storage.html#request_entries"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.cache_storage.request_entries" title="Link to this definition">#</a></dt>
<dd><p>Requests data from cache.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>cache_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.cache_storage.CacheId" title="nodriver.cdp.cache_storage.CacheId"><code class="xref py py-class docutils literal notranslate"><span class="pre">CacheId</span></code></a></span>) – ID of cache to get entries from.</p></li>
<li><p><strong>skip_count</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a>]</span>) – <em>(Optional)</em> Number of records to skip.</p></li>
<li><p><strong>page_size</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a>]</span>) – <em>(Optional)</em> Number of records to fetch.</p></li>
<li><p><strong>path_filter</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]</span>) – <em>(Optional)</em> If present, only return the entries containing this substring in the path</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Tuple" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Tuple</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.cache_storage.DataEntry" title="nodriver.cdp.cache_storage.DataEntry"><code class="xref py py-class docutils literal notranslate"><span class="pre">DataEntry</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a>]]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p><p>A tuple with the following items:</p>
<ol class="arabic simple" start="0">
<li><p><strong>cacheDataEntries</strong> - Array of object store data entries.</p></li>
<li><p><strong>returnCount</strong> - Count of returned entries from this storage. If pathFilter is empty, it is the count of all entries from this storage.</p></li>
</ol>
</p>
</dd>
</dl>
</dd></dl>

</section>
<section id="events">
<h2>Events<a class="headerlink" href="#events" title="Link to this heading">#</a></h2>
<p><em>There are no events in this module.</em></p>
</section>
</section>

        </article>
      </div>
      <footer>
        
        <div class="related-pages">
          <a class="next-page" href="cast.html">
              <div class="page-info">
                <div class="context">
                  <span>Next</span>
                </div>
                <div class="title">Cast</div>
              </div>
              <svg class="furo-related-icon"><use href="#svg-arrow-right"></use></svg>
            </a>
          <a class="prev-page" href="browser.html">
              <svg class="furo-related-icon"><use href="#svg-arrow-right"></use></svg>
              <div class="page-info">
                <div class="context">
                  <span>Previous</span>
                </div>
                
                <div class="title">Browser</div>
                
              </div>
            </a>
        </div>
        <div class="bottom-of-page">
          <div class="left-details">
            <div class="copyright">
                Copyright &#169; 2023, Author
            </div>
            Made with <a href="https://www.sphinx-doc.org/">Sphinx</a> and <a class="muted-link" href="https://pradyunsg.me">@pradyunsg</a>'s
            
            <a href="https://github.com/pradyunsg/furo">Furo</a>
            
          </div>
          <div class="right-details">
            
          </div>
        </div>
        
      </footer>
    </div>
    <aside class="toc-drawer">
      
      
      <div class="toc-sticky toc-scroll">
        <div class="toc-title-container">
          <span class="toc-title">
            On this page
          </span>
        </div>
        <div class="toc-tree-container">
          <div class="toc-tree">
            <ul>
<li><a class="reference internal" href="#">CacheStorage</a><ul>
<li><a class="reference internal" href="#types">Types</a><ul>
<li><a class="reference internal" href="#nodriver.cdp.cache_storage.CacheId"><code class="docutils literal notranslate"><span class="pre">CacheId</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.cache_storage.CachedResponseType"><code class="docutils literal notranslate"><span class="pre">CachedResponseType</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.cache_storage.CachedResponseType.BASIC"><code class="docutils literal notranslate"><span class="pre">CachedResponseType.BASIC</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.cache_storage.CachedResponseType.CORS"><code class="docutils literal notranslate"><span class="pre">CachedResponseType.CORS</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.cache_storage.CachedResponseType.DEFAULT"><code class="docutils literal notranslate"><span class="pre">CachedResponseType.DEFAULT</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.cache_storage.CachedResponseType.ERROR"><code class="docutils literal notranslate"><span class="pre">CachedResponseType.ERROR</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.cache_storage.CachedResponseType.OPAQUE_RESPONSE"><code class="docutils literal notranslate"><span class="pre">CachedResponseType.OPAQUE_RESPONSE</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.cache_storage.CachedResponseType.OPAQUE_REDIRECT"><code class="docutils literal notranslate"><span class="pre">CachedResponseType.OPAQUE_REDIRECT</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.cache_storage.DataEntry"><code class="docutils literal notranslate"><span class="pre">DataEntry</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.cache_storage.DataEntry.request_url"><code class="docutils literal notranslate"><span class="pre">DataEntry.request_url</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.cache_storage.DataEntry.request_method"><code class="docutils literal notranslate"><span class="pre">DataEntry.request_method</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.cache_storage.DataEntry.request_headers"><code class="docutils literal notranslate"><span class="pre">DataEntry.request_headers</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.cache_storage.DataEntry.response_time"><code class="docutils literal notranslate"><span class="pre">DataEntry.response_time</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.cache_storage.DataEntry.response_status"><code class="docutils literal notranslate"><span class="pre">DataEntry.response_status</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.cache_storage.DataEntry.response_status_text"><code class="docutils literal notranslate"><span class="pre">DataEntry.response_status_text</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.cache_storage.DataEntry.response_type"><code class="docutils literal notranslate"><span class="pre">DataEntry.response_type</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.cache_storage.DataEntry.response_headers"><code class="docutils literal notranslate"><span class="pre">DataEntry.response_headers</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.cache_storage.Cache"><code class="docutils literal notranslate"><span class="pre">Cache</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.cache_storage.Cache.cache_id"><code class="docutils literal notranslate"><span class="pre">Cache.cache_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.cache_storage.Cache.security_origin"><code class="docutils literal notranslate"><span class="pre">Cache.security_origin</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.cache_storage.Cache.storage_key"><code class="docutils literal notranslate"><span class="pre">Cache.storage_key</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.cache_storage.Cache.cache_name"><code class="docutils literal notranslate"><span class="pre">Cache.cache_name</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.cache_storage.Cache.storage_bucket"><code class="docutils literal notranslate"><span class="pre">Cache.storage_bucket</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.cache_storage.Header"><code class="docutils literal notranslate"><span class="pre">Header</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.cache_storage.Header.name"><code class="docutils literal notranslate"><span class="pre">Header.name</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.cache_storage.Header.value"><code class="docutils literal notranslate"><span class="pre">Header.value</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.cache_storage.CachedResponse"><code class="docutils literal notranslate"><span class="pre">CachedResponse</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.cache_storage.CachedResponse.body"><code class="docutils literal notranslate"><span class="pre">CachedResponse.body</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#commands">Commands</a><ul>
<li><a class="reference internal" href="#nodriver.cdp.cache_storage.delete_cache"><code class="docutils literal notranslate"><span class="pre">delete_cache()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.cache_storage.delete_entry"><code class="docutils literal notranslate"><span class="pre">delete_entry()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.cache_storage.request_cache_names"><code class="docutils literal notranslate"><span class="pre">request_cache_names()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.cache_storage.request_cached_response"><code class="docutils literal notranslate"><span class="pre">request_cached_response()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.cache_storage.request_entries"><code class="docutils literal notranslate"><span class="pre">request_entries()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#events">Events</a></li>
</ul>
</li>
</ul>

          </div>
        </div>
      </div>
      
      
    </aside>
  </div>
</div><script src="../../_static/documentation_options.js?v=5929fcd5"></script>
    <script src="../../_static/doctools.js?v=888ff710"></script>
    <script src="../../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../../_static/scripts/furo.js?v=32e29ea5"></script>
    </body>
</html>