<!doctype html>
<html class="no-js" lang="en" data-content_root="../../">
  <head><meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width,initial-scale=1"/>
    <meta name="color-scheme" content="light dark"><meta name="viewport" content="width=device-width, initial-scale=1" />
<link rel="index" title="Index" href="../../genindex.html" /><link rel="search" title="Search" href="../../search.html" /><link rel="next" title="DOMStorage" href="dom_storage.html" /><link rel="prev" title="DOMDebugger" href="dom_debugger.html" />

    <!-- Generated with Sphinx 7.2.6 and Furo 2023.09.10 -->
        <title>DOMSnapshot - nodriver documentation</title>
      <link rel="stylesheet" type="text/css" href="../../_static/pygments.css?v=fa44fd50" />
    <link rel="stylesheet" type="text/css" href="../../_static/styles/furo.css?v=135e06be" />
    <link rel="stylesheet" type="text/css" href="../../_static/styles/furo-extensions.css?v=36a5483c" />
    <link rel="stylesheet" type="text/css" href="../../_static/./custom.css?v=3934642d" />
    
    


<style>
  body {
    --color-code-background: #eeffcc;
  --color-code-foreground: black;
  
  }
  @media not print {
    body[data-theme="dark"] {
      --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8eefba;
  --color-background-primary: #111112;
  --color-problematic: #e7aeae;
  
    }
    @media (prefers-color-scheme: dark) {
      body:not([data-theme="light"]) {
        --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8eefba;
  --color-background-primary: #111112;
  --color-problematic: #e7aeae;
  
      }
    }
  }
</style></head>
  <body>
    
    <script>
      document.body.dataset.theme = localStorage.getItem("theme") || "auto";
    </script>
    

<svg xmlns="http://www.w3.org/2000/svg" style="display: none;">
  <symbol id="svg-toc" viewBox="0 0 24 24">
    <title>Contents</title>
    <svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 1024 1024">
      <path d="M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM115.4 518.9L271.7 642c5.8 4.6 14.4.5 14.4-6.9V388.9c0-7.4-8.5-11.5-14.4-6.9L115.4 505.1a8.74 8.74 0 0 0 0 13.8z"/>
    </svg>
  </symbol>
  <symbol id="svg-menu" viewBox="0 0 24 24">
    <title>Menu</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-menu">
      <line x1="3" y1="12" x2="21" y2="12"></line>
      <line x1="3" y1="6" x2="21" y2="6"></line>
      <line x1="3" y1="18" x2="21" y2="18"></line>
    </svg>
  </symbol>
  <symbol id="svg-arrow-right" viewBox="0 0 24 24">
    <title>Expand</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-chevron-right">
      <polyline points="9 18 15 12 9 6"></polyline>
    </svg>
  </symbol>
  <symbol id="svg-sun" viewBox="0 0 24 24">
    <title>Light mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="feather-sun">
      <circle cx="12" cy="12" r="5"></circle>
      <line x1="12" y1="1" x2="12" y2="3"></line>
      <line x1="12" y1="21" x2="12" y2="23"></line>
      <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
      <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
      <line x1="1" y1="12" x2="3" y2="12"></line>
      <line x1="21" y1="12" x2="23" y2="12"></line>
      <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
      <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
    </svg>
  </symbol>
  <symbol id="svg-moon" viewBox="0 0 24 24">
    <title>Dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-moon">
      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
      <path d="M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z" />
    </svg>
  </symbol>
  <symbol id="svg-sun-half" viewBox="0 0 24 24">
    <title>Auto light/dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-shadow">
      <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
      <circle cx="12" cy="12" r="9" />
      <path d="M13 12h5" />
      <path d="M13 15h4" />
      <path d="M13 18h1" />
      <path d="M13 9h4" />
      <path d="M13 6h1" />
    </svg>
  </symbol>
</svg>

<input type="checkbox" class="sidebar-toggle" name="__navigation" id="__navigation">
<input type="checkbox" class="sidebar-toggle" name="__toc" id="__toc">
<label class="overlay sidebar-overlay" for="__navigation">
  <div class="visually-hidden">Hide navigation sidebar</div>
</label>
<label class="overlay toc-overlay" for="__toc">
  <div class="visually-hidden">Hide table of contents sidebar</div>
</label>



<div class="page">
  <header class="mobile-header">
    <div class="header-left">
      <label class="nav-overlay-icon" for="__navigation">
        <div class="visually-hidden">Toggle site navigation sidebar</div>
        <i class="icon"><svg><use href="#svg-menu"></use></svg></i>
      </label>
    </div>
    <div class="header-center">
      <a href="../../index.html"><div class="brand">nodriver  documentation</div></a>
    </div>
    <div class="header-right">
      <div class="theme-toggle-container theme-toggle-header">
        <button class="theme-toggle">
          <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
          <svg class="theme-icon-when-auto"><use href="#svg-sun-half"></use></svg>
          <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
          <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
        </button>
      </div>
      <label class="toc-overlay-icon toc-header-icon" for="__toc">
        <div class="visually-hidden">Toggle table of contents sidebar</div>
        <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
      </label>
    </div>
  </header>
  <aside class="sidebar-drawer">
    <div class="sidebar-container">
      
      <div class="sidebar-sticky"><div class="sidebar-scroll"><a class="sidebar-brand" href="../../index.html">
  
  
  <span class="sidebar-brand-text">nodriver  documentation</span>
  
</a><form class="sidebar-search-container" method="get" action="../../search.html" role="search">
  <input class="sidebar-search" placeholder="Search" name="q" aria-label="Search">
  <input type="hidden" name="check_keywords" value="yes">
  <input type="hidden" name="area" value="default">
</form>
<div id="searchbox"></div><div class="sidebar-tree">
  <ul>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html">Quickstart guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#usage-example">usage example</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#more-complete-example">More complete example</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#custom-starting-options">Custom starting options</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#alternative-custom-options">Alternative custom options</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#proxies-socks5-authenticated-too">Proxies (socks5, authenticated too)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#concrete-example">Concrete example</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../classes/browser.html">Browser class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../classes/tab.html">Tab class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../classes/element.html">Element class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../classes/others_and_helpers.html">Other classes and Helper classes</a></li>
</ul>
<ul class="current">
<li class="toctree-l1 current has-children"><a class="reference internal" href="../cdp.html">CDP object</a><input checked="" class="toctree-checkbox" id="toctree-checkbox-1" name="toctree-checkbox-1" role="switch" type="checkbox"/><label for="toctree-checkbox-1"><div class="visually-hidden">Toggle navigation of CDP object</div><i class="icon"><svg><use href="#svg-arrow-right"></use></svg></i></label><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="accessibility.html">Accessibility</a></li>
<li class="toctree-l2"><a class="reference internal" href="animation.html">Animation</a></li>
<li class="toctree-l2"><a class="reference internal" href="audits.html">Audits</a></li>
<li class="toctree-l2"><a class="reference internal" href="autofill.html">Autofill</a></li>
<li class="toctree-l2"><a class="reference internal" href="background_service.html">BackgroundService</a></li>
<li class="toctree-l2"><a class="reference internal" href="bluetooth_emulation.html">BluetoothEmulation</a></li>
<li class="toctree-l2"><a class="reference internal" href="browser.html">Browser</a></li>
<li class="toctree-l2"><a class="reference internal" href="cache_storage.html">CacheStorage</a></li>
<li class="toctree-l2"><a class="reference internal" href="cast.html">Cast</a></li>
<li class="toctree-l2"><a class="reference internal" href="console.html">Console</a></li>
<li class="toctree-l2"><a class="reference internal" href="css.html">CSS</a></li>
<li class="toctree-l2"><a class="reference internal" href="debugger.html">Debugger</a></li>
<li class="toctree-l2"><a class="reference internal" href="device_access.html">DeviceAccess</a></li>
<li class="toctree-l2"><a class="reference internal" href="device_orientation.html">DeviceOrientation</a></li>
<li class="toctree-l2"><a class="reference internal" href="dom.html">DOM</a></li>
<li class="toctree-l2"><a class="reference internal" href="dom_debugger.html">DOMDebugger</a></li>
<li class="toctree-l2 current current-page"><a class="current reference internal" href="#">DOMSnapshot</a></li>
<li class="toctree-l2"><a class="reference internal" href="dom_storage.html">DOMStorage</a></li>
<li class="toctree-l2"><a class="reference internal" href="emulation.html">Emulation</a></li>
<li class="toctree-l2"><a class="reference internal" href="event_breakpoints.html">EventBreakpoints</a></li>
<li class="toctree-l2"><a class="reference internal" href="extensions.html">Extensions</a></li>
<li class="toctree-l2"><a class="reference internal" href="fed_cm.html">FedCm</a></li>
<li class="toctree-l2"><a class="reference internal" href="fetch.html">Fetch</a></li>
<li class="toctree-l2"><a class="reference internal" href="file_system.html">FileSystem</a></li>
<li class="toctree-l2"><a class="reference internal" href="headless_experimental.html">HeadlessExperimental</a></li>
<li class="toctree-l2"><a class="reference internal" href="heap_profiler.html">HeapProfiler</a></li>
<li class="toctree-l2"><a class="reference internal" href="indexed_db.html">IndexedDB</a></li>
<li class="toctree-l2"><a class="reference internal" href="input_.html">Input</a></li>
<li class="toctree-l2"><a class="reference internal" href="inspector.html">Inspector</a></li>
<li class="toctree-l2"><a class="reference internal" href="io.html">IO</a></li>
<li class="toctree-l2"><a class="reference internal" href="layer_tree.html">LayerTree</a></li>
<li class="toctree-l2"><a class="reference internal" href="log.html">Log</a></li>
<li class="toctree-l2"><a class="reference internal" href="media.html">Media</a></li>
<li class="toctree-l2"><a class="reference internal" href="memory.html">Memory</a></li>
<li class="toctree-l2"><a class="reference internal" href="network.html">Network</a></li>
<li class="toctree-l2"><a class="reference internal" href="overlay.html">Overlay</a></li>
<li class="toctree-l2"><a class="reference internal" href="page.html">Page</a></li>
<li class="toctree-l2"><a class="reference internal" href="performance.html">Performance</a></li>
<li class="toctree-l2"><a class="reference internal" href="performance_timeline.html">PerformanceTimeline</a></li>
<li class="toctree-l2"><a class="reference internal" href="preload.html">Preload</a></li>
<li class="toctree-l2"><a class="reference internal" href="profiler.html">Profiler</a></li>
<li class="toctree-l2"><a class="reference internal" href="pwa.html">PWA</a></li>
<li class="toctree-l2"><a class="reference internal" href="runtime.html">Runtime</a></li>
<li class="toctree-l2"><a class="reference internal" href="schema.html">Schema</a></li>
<li class="toctree-l2"><a class="reference internal" href="security.html">Security</a></li>
<li class="toctree-l2"><a class="reference internal" href="service_worker.html">ServiceWorker</a></li>
<li class="toctree-l2"><a class="reference internal" href="storage.html">Storage</a></li>
<li class="toctree-l2"><a class="reference internal" href="system_info.html">SystemInfo</a></li>
<li class="toctree-l2"><a class="reference internal" href="target.html">Target</a></li>
<li class="toctree-l2"><a class="reference internal" href="tethering.html">Tethering</a></li>
<li class="toctree-l2"><a class="reference internal" href="tracing.html">Tracing</a></li>
<li class="toctree-l2"><a class="reference internal" href="web_audio.html">WebAudio</a></li>
<li class="toctree-l2"><a class="reference internal" href="web_authn.html">WebAuthn</a></li>
</ul>
</li>
</ul>

</div>
</div>
      </div>
      
    </div>
  </aside>
  <div class="main">
    <div class="content">
      <div class="article-container">
        <a href="#" class="back-to-top muted-link">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
            <path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8v12z"></path>
          </svg>
          <span>Back to top</span>
        </a>
        <div class="content-icon-container">
          
<div class="theme-toggle-container theme-toggle-content">
            <button class="theme-toggle">
              <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
              <svg class="theme-icon-when-auto"><use href="#svg-sun-half"></use></svg>
              <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
              <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
            </button>
          </div>
          <label class="toc-overlay-icon toc-content-icon" for="__toc">
            <div class="visually-hidden">Toggle table of contents sidebar</div>
            <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
          </label>
        </div>
        <article role="main">
          <section id="domsnapshot">
<h1>DOMSnapshot<a class="headerlink" href="#domsnapshot" title="Link to this heading">#</a></h1>
<p>This domain facilitates obtaining document snapshots with DOM, layout, and style information.</p>
<p><em>This CDP domain is experimental.</em></p>
<ul class="simple" id="module-nodriver.cdp.dom_snapshot">
<li><p><a class="reference internal" href="#types">Types</a></p></li>
<li><p><a class="reference internal" href="#commands">Commands</a></p></li>
<li><p><a class="reference internal" href="#events">Events</a></p></li>
</ul>
<section id="types">
<h2>Types<a class="headerlink" href="#types" title="Link to this heading">#</a></h2>
<p>Generally, you do not need to instantiate CDP types
yourself. Instead, the API creates objects for you as return
values from commands, and then you can use those objects as
arguments to other commands.</p>
<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.DOMNode">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">DOMNode</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">node_type</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">node_name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">node_value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">backend_node_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">text_value</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">input_value</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">input_checked</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">option_selected</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">child_node_indexes</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">attributes</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pseudo_element_indexes</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">layout_node_index</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">document_url</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">base_url</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">content_language</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">document_encoding</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">public_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">system_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">frame_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">content_document_index</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pseudo_type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">shadow_root_type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">is_clickable</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">event_listeners</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">current_source_url</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">origin_url</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">scroll_offset_x</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">scroll_offset_y</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom_snapshot.html#DOMNode"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom_snapshot.DOMNode" title="Link to this definition">#</a></dt>
<dd><p>A Node in the DOM tree.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.DOMNode.node_type">
<span class="sig-name descname"><span class="pre">node_type</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.DOMNode.node_type" title="Link to this definition">#</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">Node</span></code>’s nodeType.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.DOMNode.node_name">
<span class="sig-name descname"><span class="pre">node_name</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.DOMNode.node_name" title="Link to this definition">#</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">Node</span></code>’s nodeName.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.DOMNode.node_value">
<span class="sig-name descname"><span class="pre">node_value</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.DOMNode.node_value" title="Link to this definition">#</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">Node</span></code>’s nodeValue.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.DOMNode.backend_node_id">
<span class="sig-name descname"><span class="pre">backend_node_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="dom.html#nodriver.cdp.dom.BackendNodeId" title="nodriver.cdp.dom.BackendNodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">BackendNodeId</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.DOMNode.backend_node_id" title="Link to this definition">#</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">Node</span></code>’s id, corresponds to DOM.Node.backendNodeId.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.DOMNode.text_value">
<span class="sig-name descname"><span class="pre">text_value</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.DOMNode.text_value" title="Link to this definition">#</a></dt>
<dd><p>Only set for textarea elements, contains the text value.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.DOMNode.input_value">
<span class="sig-name descname"><span class="pre">input_value</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.DOMNode.input_value" title="Link to this definition">#</a></dt>
<dd><p>Only set for input elements, contains the input’s associated text value.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.DOMNode.input_checked">
<span class="sig-name descname"><span class="pre">input_checked</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.DOMNode.input_checked" title="Link to this definition">#</a></dt>
<dd><p>Only set for radio and checkbox input elements, indicates if the element has been checked</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.DOMNode.option_selected">
<span class="sig-name descname"><span class="pre">option_selected</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.DOMNode.option_selected" title="Link to this definition">#</a></dt>
<dd><p>Only set for option elements, indicates if the element has been selected</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.DOMNode.child_node_indexes">
<span class="sig-name descname"><span class="pre">child_node_indexes</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a><span class="pre">]]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.DOMNode.child_node_indexes" title="Link to this definition">#</a></dt>
<dd><p>The indexes of the node’s child nodes in the <code class="docutils literal notranslate"><span class="pre">domNodes</span></code> array returned by <code class="docutils literal notranslate"><span class="pre">getSnapshot</span></code>, if
any.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.DOMNode.attributes">
<span class="sig-name descname"><span class="pre">attributes</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.dom_snapshot.NameValue" title="nodriver.cdp.dom_snapshot.NameValue"><code class="xref py py-class docutils literal notranslate"><span class="pre">NameValue</span></code></a><span class="pre">]]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.DOMNode.attributes" title="Link to this definition">#</a></dt>
<dd><p>Attributes of an <code class="docutils literal notranslate"><span class="pre">Element</span></code> node.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.DOMNode.pseudo_element_indexes">
<span class="sig-name descname"><span class="pre">pseudo_element_indexes</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a><span class="pre">]]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.DOMNode.pseudo_element_indexes" title="Link to this definition">#</a></dt>
<dd><p>Indexes of pseudo elements associated with this node in the <code class="docutils literal notranslate"><span class="pre">domNodes</span></code> array returned by
<code class="docutils literal notranslate"><span class="pre">getSnapshot</span></code>, if any.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.DOMNode.layout_node_index">
<span class="sig-name descname"><span class="pre">layout_node_index</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.DOMNode.layout_node_index" title="Link to this definition">#</a></dt>
<dd><p>The index of the node’s related layout tree node in the <code class="docutils literal notranslate"><span class="pre">layoutTreeNodes</span></code> array returned by
<code class="docutils literal notranslate"><span class="pre">getSnapshot</span></code>, if any.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.DOMNode.document_url">
<span class="sig-name descname"><span class="pre">document_url</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.DOMNode.document_url" title="Link to this definition">#</a></dt>
<dd><p>Document URL that <code class="docutils literal notranslate"><span class="pre">Document</span></code> or <code class="docutils literal notranslate"><span class="pre">FrameOwner</span></code> node points to.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.DOMNode.base_url">
<span class="sig-name descname"><span class="pre">base_url</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.DOMNode.base_url" title="Link to this definition">#</a></dt>
<dd><p>Base URL that <code class="docutils literal notranslate"><span class="pre">Document</span></code> or <code class="docutils literal notranslate"><span class="pre">FrameOwner</span></code> node uses for URL completion.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.DOMNode.content_language">
<span class="sig-name descname"><span class="pre">content_language</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.DOMNode.content_language" title="Link to this definition">#</a></dt>
<dd><p>Only set for documents, contains the document’s content language.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.DOMNode.document_encoding">
<span class="sig-name descname"><span class="pre">document_encoding</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.DOMNode.document_encoding" title="Link to this definition">#</a></dt>
<dd><p>Only set for documents, contains the document’s character set encoding.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.DOMNode.public_id">
<span class="sig-name descname"><span class="pre">public_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.DOMNode.public_id" title="Link to this definition">#</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">DocumentType</span></code> node’s publicId.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.DOMNode.system_id">
<span class="sig-name descname"><span class="pre">system_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.DOMNode.system_id" title="Link to this definition">#</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">DocumentType</span></code> node’s systemId.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.DOMNode.frame_id">
<span class="sig-name descname"><span class="pre">frame_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="page.html#nodriver.cdp.page.FrameId" title="nodriver.cdp.page.FrameId"><code class="xref py py-class docutils literal notranslate"><span class="pre">FrameId</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.DOMNode.frame_id" title="Link to this definition">#</a></dt>
<dd><p>Frame ID for frame owner elements and also for the document node.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.DOMNode.content_document_index">
<span class="sig-name descname"><span class="pre">content_document_index</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.DOMNode.content_document_index" title="Link to this definition">#</a></dt>
<dd><p>The index of a frame owner element’s content document in the <code class="docutils literal notranslate"><span class="pre">domNodes</span></code> array returned by
<code class="docutils literal notranslate"><span class="pre">getSnapshot</span></code>, if any.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.DOMNode.pseudo_type">
<span class="sig-name descname"><span class="pre">pseudo_type</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="dom.html#nodriver.cdp.dom.PseudoType" title="nodriver.cdp.dom.PseudoType"><code class="xref py py-class docutils literal notranslate"><span class="pre">PseudoType</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.DOMNode.pseudo_type" title="Link to this definition">#</a></dt>
<dd><p>Type of a pseudo element node.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.DOMNode.shadow_root_type">
<span class="sig-name descname"><span class="pre">shadow_root_type</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="dom.html#nodriver.cdp.dom.ShadowRootType" title="nodriver.cdp.dom.ShadowRootType"><code class="xref py py-class docutils literal notranslate"><span class="pre">ShadowRootType</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.DOMNode.shadow_root_type" title="Link to this definition">#</a></dt>
<dd><p>Shadow root type.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.DOMNode.is_clickable">
<span class="sig-name descname"><span class="pre">is_clickable</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.DOMNode.is_clickable" title="Link to this definition">#</a></dt>
<dd><p>Whether this DOM node responds to mouse clicks. This includes nodes that have had click
event listeners attached via JavaScript as well as anchor tags that naturally navigate when
clicked.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.DOMNode.event_listeners">
<span class="sig-name descname"><span class="pre">event_listeners</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="dom_debugger.html#nodriver.cdp.dom_debugger.EventListener" title="nodriver.cdp.dom_debugger.EventListener"><code class="xref py py-class docutils literal notranslate"><span class="pre">EventListener</span></code></a><span class="pre">]]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.DOMNode.event_listeners" title="Link to this definition">#</a></dt>
<dd><p>Details of the node’s event listeners, if any.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.DOMNode.current_source_url">
<span class="sig-name descname"><span class="pre">current_source_url</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.DOMNode.current_source_url" title="Link to this definition">#</a></dt>
<dd><p>The selected url for nodes with a srcset attribute.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.DOMNode.origin_url">
<span class="sig-name descname"><span class="pre">origin_url</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.DOMNode.origin_url" title="Link to this definition">#</a></dt>
<dd><p>The url of the script (if any) that generates this node.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.DOMNode.scroll_offset_x">
<span class="sig-name descname"><span class="pre">scroll_offset_x</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.DOMNode.scroll_offset_x" title="Link to this definition">#</a></dt>
<dd><p>Scroll offsets, set when this node is a Document.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.DOMNode.scroll_offset_y">
<span class="sig-name descname"><span class="pre">scroll_offset_y</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.DOMNode.scroll_offset_y" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.InlineTextBox">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">InlineTextBox</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">bounding_box</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start_character_index</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">num_characters</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom_snapshot.html#InlineTextBox"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom_snapshot.InlineTextBox" title="Link to this definition">#</a></dt>
<dd><p>Details of post layout rendered text positions. The exact layout should not be regarded as
stable and may change between versions.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.InlineTextBox.bounding_box">
<span class="sig-name descname"><span class="pre">bounding_box</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="dom.html#nodriver.cdp.dom.Rect" title="nodriver.cdp.dom.Rect"><code class="xref py py-class docutils literal notranslate"><span class="pre">Rect</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.InlineTextBox.bounding_box" title="Link to this definition">#</a></dt>
<dd><p>The bounding box in document coordinates. Note that scroll offset of the document is ignored.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.InlineTextBox.start_character_index">
<span class="sig-name descname"><span class="pre">start_character_index</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.InlineTextBox.start_character_index" title="Link to this definition">#</a></dt>
<dd><p>The starting index in characters, for this post layout textbox substring. Characters that
would be represented as a surrogate pair in UTF-16 have length 2.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.InlineTextBox.num_characters">
<span class="sig-name descname"><span class="pre">num_characters</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.InlineTextBox.num_characters" title="Link to this definition">#</a></dt>
<dd><p>The number of characters in this post layout textbox substring. Characters that would be
represented as a surrogate pair in UTF-16 have length 2.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.LayoutTreeNode">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">LayoutTreeNode</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">dom_node_index</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">bounding_box</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">layout_text</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">inline_text_nodes</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">style_index</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">paint_order</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">is_stacking_context</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom_snapshot.html#LayoutTreeNode"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom_snapshot.LayoutTreeNode" title="Link to this definition">#</a></dt>
<dd><p>Details of an element in the DOM tree with a LayoutObject.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.LayoutTreeNode.dom_node_index">
<span class="sig-name descname"><span class="pre">dom_node_index</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.LayoutTreeNode.dom_node_index" title="Link to this definition">#</a></dt>
<dd><p>The index of the related DOM node in the <code class="docutils literal notranslate"><span class="pre">domNodes</span></code> array returned by <code class="docutils literal notranslate"><span class="pre">getSnapshot</span></code>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.LayoutTreeNode.bounding_box">
<span class="sig-name descname"><span class="pre">bounding_box</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="dom.html#nodriver.cdp.dom.Rect" title="nodriver.cdp.dom.Rect"><code class="xref py py-class docutils literal notranslate"><span class="pre">Rect</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.LayoutTreeNode.bounding_box" title="Link to this definition">#</a></dt>
<dd><p>The bounding box in document coordinates. Note that scroll offset of the document is ignored.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.LayoutTreeNode.layout_text">
<span class="sig-name descname"><span class="pre">layout_text</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.LayoutTreeNode.layout_text" title="Link to this definition">#</a></dt>
<dd><p>Contents of the LayoutText, if any.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.LayoutTreeNode.inline_text_nodes">
<span class="sig-name descname"><span class="pre">inline_text_nodes</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.dom_snapshot.InlineTextBox" title="nodriver.cdp.dom_snapshot.InlineTextBox"><code class="xref py py-class docutils literal notranslate"><span class="pre">InlineTextBox</span></code></a><span class="pre">]]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.LayoutTreeNode.inline_text_nodes" title="Link to this definition">#</a></dt>
<dd><p>The post-layout inline text nodes, if any.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.LayoutTreeNode.style_index">
<span class="sig-name descname"><span class="pre">style_index</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.LayoutTreeNode.style_index" title="Link to this definition">#</a></dt>
<dd><p>Index into the <code class="docutils literal notranslate"><span class="pre">computedStyles</span></code> array returned by <code class="docutils literal notranslate"><span class="pre">getSnapshot</span></code>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.LayoutTreeNode.paint_order">
<span class="sig-name descname"><span class="pre">paint_order</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.LayoutTreeNode.paint_order" title="Link to this definition">#</a></dt>
<dd><p>Global paint order index, which is determined by the stacking order of the nodes. Nodes
that are painted together will have the same index. Only provided if includePaintOrder in
getSnapshot was true.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.LayoutTreeNode.is_stacking_context">
<span class="sig-name descname"><span class="pre">is_stacking_context</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.LayoutTreeNode.is_stacking_context" title="Link to this definition">#</a></dt>
<dd><p>Set to true to indicate the element begins a new stacking context.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.ComputedStyle">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">ComputedStyle</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">properties</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom_snapshot.html#ComputedStyle"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom_snapshot.ComputedStyle" title="Link to this definition">#</a></dt>
<dd><p>A subset of the full ComputedStyle as defined by the request whitelist.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.ComputedStyle.properties">
<span class="sig-name descname"><span class="pre">properties</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.dom_snapshot.NameValue" title="nodriver.cdp.dom_snapshot.NameValue"><code class="xref py py-class docutils literal notranslate"><span class="pre">NameValue</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.ComputedStyle.properties" title="Link to this definition">#</a></dt>
<dd><p>Name/value pairs of computed style properties.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.NameValue">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">NameValue</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom_snapshot.html#NameValue"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom_snapshot.NameValue" title="Link to this definition">#</a></dt>
<dd><p>A name/value pair.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.NameValue.name">
<span class="sig-name descname"><span class="pre">name</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.NameValue.name" title="Link to this definition">#</a></dt>
<dd><p>Attribute/property name.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.NameValue.value">
<span class="sig-name descname"><span class="pre">value</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.NameValue.value" title="Link to this definition">#</a></dt>
<dd><p>Attribute/property value.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.StringIndex">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">StringIndex</span></span><a class="reference internal" href="../../_modules/nodriver/cdp/dom_snapshot.html#StringIndex"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom_snapshot.StringIndex" title="Link to this definition">#</a></dt>
<dd><p>Index of the string in the strings table.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.ArrayOfStrings">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">ArrayOfStrings</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">iterable</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">()</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom_snapshot.html#ArrayOfStrings"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom_snapshot.ArrayOfStrings" title="Link to this definition">#</a></dt>
<dd><p>Index of the string in the strings table.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.RareStringData">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">RareStringData</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">index</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom_snapshot.html#RareStringData"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom_snapshot.RareStringData" title="Link to this definition">#</a></dt>
<dd><p>Data that is only present on rare nodes.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.RareStringData.index">
<span class="sig-name descname"><span class="pre">index</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.RareStringData.index" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.RareStringData.value">
<span class="sig-name descname"><span class="pre">value</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.dom_snapshot.StringIndex" title="nodriver.cdp.dom_snapshot.StringIndex"><code class="xref py py-class docutils literal notranslate"><span class="pre">StringIndex</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.RareStringData.value" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.RareBooleanData">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">RareBooleanData</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">index</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom_snapshot.html#RareBooleanData"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom_snapshot.RareBooleanData" title="Link to this definition">#</a></dt>
<dd><dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.RareBooleanData.index">
<span class="sig-name descname"><span class="pre">index</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.RareBooleanData.index" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.RareIntegerData">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">RareIntegerData</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">index</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom_snapshot.html#RareIntegerData"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom_snapshot.RareIntegerData" title="Link to this definition">#</a></dt>
<dd><dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.RareIntegerData.index">
<span class="sig-name descname"><span class="pre">index</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.RareIntegerData.index" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.RareIntegerData.value">
<span class="sig-name descname"><span class="pre">value</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.RareIntegerData.value" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.Rectangle">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">Rectangle</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">iterable</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">()</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom_snapshot.html#Rectangle"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom_snapshot.Rectangle" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.DocumentSnapshot">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">DocumentSnapshot</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">document_url</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">title</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">base_url</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">content_language</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">encoding_name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">public_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">system_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">frame_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">nodes</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">layout</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">text_boxes</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">scroll_offset_x</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">scroll_offset_y</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">content_width</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">content_height</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom_snapshot.html#DocumentSnapshot"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom_snapshot.DocumentSnapshot" title="Link to this definition">#</a></dt>
<dd><p>Document snapshot.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.DocumentSnapshot.document_url">
<span class="sig-name descname"><span class="pre">document_url</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.dom_snapshot.StringIndex" title="nodriver.cdp.dom_snapshot.StringIndex"><code class="xref py py-class docutils literal notranslate"><span class="pre">StringIndex</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.DocumentSnapshot.document_url" title="Link to this definition">#</a></dt>
<dd><p>Document URL that <code class="docutils literal notranslate"><span class="pre">Document</span></code> or <code class="docutils literal notranslate"><span class="pre">FrameOwner</span></code> node points to.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.DocumentSnapshot.title">
<span class="sig-name descname"><span class="pre">title</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.dom_snapshot.StringIndex" title="nodriver.cdp.dom_snapshot.StringIndex"><code class="xref py py-class docutils literal notranslate"><span class="pre">StringIndex</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.DocumentSnapshot.title" title="Link to this definition">#</a></dt>
<dd><p>Document title.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.DocumentSnapshot.base_url">
<span class="sig-name descname"><span class="pre">base_url</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.dom_snapshot.StringIndex" title="nodriver.cdp.dom_snapshot.StringIndex"><code class="xref py py-class docutils literal notranslate"><span class="pre">StringIndex</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.DocumentSnapshot.base_url" title="Link to this definition">#</a></dt>
<dd><p>Base URL that <code class="docutils literal notranslate"><span class="pre">Document</span></code> or <code class="docutils literal notranslate"><span class="pre">FrameOwner</span></code> node uses for URL completion.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.DocumentSnapshot.content_language">
<span class="sig-name descname"><span class="pre">content_language</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.dom_snapshot.StringIndex" title="nodriver.cdp.dom_snapshot.StringIndex"><code class="xref py py-class docutils literal notranslate"><span class="pre">StringIndex</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.DocumentSnapshot.content_language" title="Link to this definition">#</a></dt>
<dd><p>Contains the document’s content language.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.DocumentSnapshot.encoding_name">
<span class="sig-name descname"><span class="pre">encoding_name</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.dom_snapshot.StringIndex" title="nodriver.cdp.dom_snapshot.StringIndex"><code class="xref py py-class docutils literal notranslate"><span class="pre">StringIndex</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.DocumentSnapshot.encoding_name" title="Link to this definition">#</a></dt>
<dd><p>Contains the document’s character set encoding.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.DocumentSnapshot.public_id">
<span class="sig-name descname"><span class="pre">public_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.dom_snapshot.StringIndex" title="nodriver.cdp.dom_snapshot.StringIndex"><code class="xref py py-class docutils literal notranslate"><span class="pre">StringIndex</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.DocumentSnapshot.public_id" title="Link to this definition">#</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">DocumentType</span></code> node’s publicId.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.DocumentSnapshot.system_id">
<span class="sig-name descname"><span class="pre">system_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.dom_snapshot.StringIndex" title="nodriver.cdp.dom_snapshot.StringIndex"><code class="xref py py-class docutils literal notranslate"><span class="pre">StringIndex</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.DocumentSnapshot.system_id" title="Link to this definition">#</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">DocumentType</span></code> node’s systemId.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.DocumentSnapshot.frame_id">
<span class="sig-name descname"><span class="pre">frame_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.dom_snapshot.StringIndex" title="nodriver.cdp.dom_snapshot.StringIndex"><code class="xref py py-class docutils literal notranslate"><span class="pre">StringIndex</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.DocumentSnapshot.frame_id" title="Link to this definition">#</a></dt>
<dd><p>Frame ID for frame owner elements and also for the document node.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.DocumentSnapshot.nodes">
<span class="sig-name descname"><span class="pre">nodes</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.dom_snapshot.NodeTreeSnapshot" title="nodriver.cdp.dom_snapshot.NodeTreeSnapshot"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeTreeSnapshot</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.DocumentSnapshot.nodes" title="Link to this definition">#</a></dt>
<dd><p>A table with dom nodes.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.DocumentSnapshot.layout">
<span class="sig-name descname"><span class="pre">layout</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.dom_snapshot.LayoutTreeSnapshot" title="nodriver.cdp.dom_snapshot.LayoutTreeSnapshot"><code class="xref py py-class docutils literal notranslate"><span class="pre">LayoutTreeSnapshot</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.DocumentSnapshot.layout" title="Link to this definition">#</a></dt>
<dd><p>The nodes in the layout tree.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.DocumentSnapshot.text_boxes">
<span class="sig-name descname"><span class="pre">text_boxes</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.dom_snapshot.TextBoxSnapshot" title="nodriver.cdp.dom_snapshot.TextBoxSnapshot"><code class="xref py py-class docutils literal notranslate"><span class="pre">TextBoxSnapshot</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.DocumentSnapshot.text_boxes" title="Link to this definition">#</a></dt>
<dd><p>The post-layout inline text nodes.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.DocumentSnapshot.scroll_offset_x">
<span class="sig-name descname"><span class="pre">scroll_offset_x</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.DocumentSnapshot.scroll_offset_x" title="Link to this definition">#</a></dt>
<dd><p>Horizontal scroll offset.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.DocumentSnapshot.scroll_offset_y">
<span class="sig-name descname"><span class="pre">scroll_offset_y</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.DocumentSnapshot.scroll_offset_y" title="Link to this definition">#</a></dt>
<dd><p>Vertical scroll offset.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.DocumentSnapshot.content_width">
<span class="sig-name descname"><span class="pre">content_width</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.DocumentSnapshot.content_width" title="Link to this definition">#</a></dt>
<dd><p>Document content width.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.DocumentSnapshot.content_height">
<span class="sig-name descname"><span class="pre">content_height</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.DocumentSnapshot.content_height" title="Link to this definition">#</a></dt>
<dd><p>Document content height.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.NodeTreeSnapshot">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">NodeTreeSnapshot</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">parent_index</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">node_type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">shadow_root_type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">node_name</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">node_value</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">backend_node_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">attributes</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">text_value</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">input_value</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">input_checked</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">option_selected</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">content_document_index</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pseudo_type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pseudo_identifier</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">is_clickable</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">current_source_url</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">origin_url</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom_snapshot.html#NodeTreeSnapshot"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom_snapshot.NodeTreeSnapshot" title="Link to this definition">#</a></dt>
<dd><p>Table containing nodes.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.NodeTreeSnapshot.parent_index">
<span class="sig-name descname"><span class="pre">parent_index</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a><span class="pre">]]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.NodeTreeSnapshot.parent_index" title="Link to this definition">#</a></dt>
<dd><p>Parent node index.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.NodeTreeSnapshot.node_type">
<span class="sig-name descname"><span class="pre">node_type</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a><span class="pre">]]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.NodeTreeSnapshot.node_type" title="Link to this definition">#</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">Node</span></code>’s nodeType.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.NodeTreeSnapshot.shadow_root_type">
<span class="sig-name descname"><span class="pre">shadow_root_type</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.dom_snapshot.RareStringData" title="nodriver.cdp.dom_snapshot.RareStringData"><code class="xref py py-class docutils literal notranslate"><span class="pre">RareStringData</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.NodeTreeSnapshot.shadow_root_type" title="Link to this definition">#</a></dt>
<dd><p>Type of the shadow root the <code class="docutils literal notranslate"><span class="pre">Node</span></code> is in. String values are equal to the <code class="docutils literal notranslate"><span class="pre">ShadowRootType</span></code> enum.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.NodeTreeSnapshot.node_name">
<span class="sig-name descname"><span class="pre">node_name</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.dom_snapshot.StringIndex" title="nodriver.cdp.dom_snapshot.StringIndex"><code class="xref py py-class docutils literal notranslate"><span class="pre">StringIndex</span></code></a><span class="pre">]]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.NodeTreeSnapshot.node_name" title="Link to this definition">#</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">Node</span></code>’s nodeName.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.NodeTreeSnapshot.node_value">
<span class="sig-name descname"><span class="pre">node_value</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.dom_snapshot.StringIndex" title="nodriver.cdp.dom_snapshot.StringIndex"><code class="xref py py-class docutils literal notranslate"><span class="pre">StringIndex</span></code></a><span class="pre">]]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.NodeTreeSnapshot.node_value" title="Link to this definition">#</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">Node</span></code>’s nodeValue.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.NodeTreeSnapshot.backend_node_id">
<span class="sig-name descname"><span class="pre">backend_node_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="dom.html#nodriver.cdp.dom.BackendNodeId" title="nodriver.cdp.dom.BackendNodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">BackendNodeId</span></code></a><span class="pre">]]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.NodeTreeSnapshot.backend_node_id" title="Link to this definition">#</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">Node</span></code>’s id, corresponds to DOM.Node.backendNodeId.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.NodeTreeSnapshot.attributes">
<span class="sig-name descname"><span class="pre">attributes</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.dom_snapshot.ArrayOfStrings" title="nodriver.cdp.dom_snapshot.ArrayOfStrings"><code class="xref py py-class docutils literal notranslate"><span class="pre">ArrayOfStrings</span></code></a><span class="pre">]]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.NodeTreeSnapshot.attributes" title="Link to this definition">#</a></dt>
<dd><p>Attributes of an <code class="docutils literal notranslate"><span class="pre">Element</span></code> node. Flatten name, value pairs.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.NodeTreeSnapshot.text_value">
<span class="sig-name descname"><span class="pre">text_value</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.dom_snapshot.RareStringData" title="nodriver.cdp.dom_snapshot.RareStringData"><code class="xref py py-class docutils literal notranslate"><span class="pre">RareStringData</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.NodeTreeSnapshot.text_value" title="Link to this definition">#</a></dt>
<dd><p>Only set for textarea elements, contains the text value.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.NodeTreeSnapshot.input_value">
<span class="sig-name descname"><span class="pre">input_value</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.dom_snapshot.RareStringData" title="nodriver.cdp.dom_snapshot.RareStringData"><code class="xref py py-class docutils literal notranslate"><span class="pre">RareStringData</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.NodeTreeSnapshot.input_value" title="Link to this definition">#</a></dt>
<dd><p>Only set for input elements, contains the input’s associated text value.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.NodeTreeSnapshot.input_checked">
<span class="sig-name descname"><span class="pre">input_checked</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.dom_snapshot.RareBooleanData" title="nodriver.cdp.dom_snapshot.RareBooleanData"><code class="xref py py-class docutils literal notranslate"><span class="pre">RareBooleanData</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.NodeTreeSnapshot.input_checked" title="Link to this definition">#</a></dt>
<dd><p>Only set for radio and checkbox input elements, indicates if the element has been checked</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.NodeTreeSnapshot.option_selected">
<span class="sig-name descname"><span class="pre">option_selected</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.dom_snapshot.RareBooleanData" title="nodriver.cdp.dom_snapshot.RareBooleanData"><code class="xref py py-class docutils literal notranslate"><span class="pre">RareBooleanData</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.NodeTreeSnapshot.option_selected" title="Link to this definition">#</a></dt>
<dd><p>Only set for option elements, indicates if the element has been selected</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.NodeTreeSnapshot.content_document_index">
<span class="sig-name descname"><span class="pre">content_document_index</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.dom_snapshot.RareIntegerData" title="nodriver.cdp.dom_snapshot.RareIntegerData"><code class="xref py py-class docutils literal notranslate"><span class="pre">RareIntegerData</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.NodeTreeSnapshot.content_document_index" title="Link to this definition">#</a></dt>
<dd><p>The index of the document in the list of the snapshot documents.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.NodeTreeSnapshot.pseudo_type">
<span class="sig-name descname"><span class="pre">pseudo_type</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.dom_snapshot.RareStringData" title="nodriver.cdp.dom_snapshot.RareStringData"><code class="xref py py-class docutils literal notranslate"><span class="pre">RareStringData</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.NodeTreeSnapshot.pseudo_type" title="Link to this definition">#</a></dt>
<dd><p>Type of a pseudo element node.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.NodeTreeSnapshot.pseudo_identifier">
<span class="sig-name descname"><span class="pre">pseudo_identifier</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.dom_snapshot.RareStringData" title="nodriver.cdp.dom_snapshot.RareStringData"><code class="xref py py-class docutils literal notranslate"><span class="pre">RareStringData</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.NodeTreeSnapshot.pseudo_identifier" title="Link to this definition">#</a></dt>
<dd><p>Pseudo element identifier for this node. Only present if there is a
valid pseudoType.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.NodeTreeSnapshot.is_clickable">
<span class="sig-name descname"><span class="pre">is_clickable</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.dom_snapshot.RareBooleanData" title="nodriver.cdp.dom_snapshot.RareBooleanData"><code class="xref py py-class docutils literal notranslate"><span class="pre">RareBooleanData</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.NodeTreeSnapshot.is_clickable" title="Link to this definition">#</a></dt>
<dd><p>Whether this DOM node responds to mouse clicks. This includes nodes that have had click
event listeners attached via JavaScript as well as anchor tags that naturally navigate when
clicked.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.NodeTreeSnapshot.current_source_url">
<span class="sig-name descname"><span class="pre">current_source_url</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.dom_snapshot.RareStringData" title="nodriver.cdp.dom_snapshot.RareStringData"><code class="xref py py-class docutils literal notranslate"><span class="pre">RareStringData</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.NodeTreeSnapshot.current_source_url" title="Link to this definition">#</a></dt>
<dd><p>The selected url for nodes with a srcset attribute.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.NodeTreeSnapshot.origin_url">
<span class="sig-name descname"><span class="pre">origin_url</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.dom_snapshot.RareStringData" title="nodriver.cdp.dom_snapshot.RareStringData"><code class="xref py py-class docutils literal notranslate"><span class="pre">RareStringData</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.NodeTreeSnapshot.origin_url" title="Link to this definition">#</a></dt>
<dd><p>The url of the script (if any) that generates this node.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.LayoutTreeSnapshot">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">LayoutTreeSnapshot</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">node_index</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">styles</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">bounds</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">text</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">stacking_contexts</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">paint_orders</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">offset_rects</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">scroll_rects</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">client_rects</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">blended_background_colors</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">text_color_opacities</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom_snapshot.html#LayoutTreeSnapshot"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom_snapshot.LayoutTreeSnapshot" title="Link to this definition">#</a></dt>
<dd><p>Table of details of an element in the DOM tree with a LayoutObject.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.LayoutTreeSnapshot.node_index">
<span class="sig-name descname"><span class="pre">node_index</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.LayoutTreeSnapshot.node_index" title="Link to this definition">#</a></dt>
<dd><p>Index of the corresponding node in the <code class="docutils literal notranslate"><span class="pre">NodeTreeSnapshot</span></code> array returned by <code class="docutils literal notranslate"><span class="pre">captureSnapshot</span></code>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.LayoutTreeSnapshot.styles">
<span class="sig-name descname"><span class="pre">styles</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.dom_snapshot.ArrayOfStrings" title="nodriver.cdp.dom_snapshot.ArrayOfStrings"><code class="xref py py-class docutils literal notranslate"><span class="pre">ArrayOfStrings</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.LayoutTreeSnapshot.styles" title="Link to this definition">#</a></dt>
<dd><p>Array of indexes specifying computed style strings, filtered according to the <code class="docutils literal notranslate"><span class="pre">computedStyles</span></code> parameter passed to <code class="docutils literal notranslate"><span class="pre">captureSnapshot</span></code>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.LayoutTreeSnapshot.bounds">
<span class="sig-name descname"><span class="pre">bounds</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.dom_snapshot.Rectangle" title="nodriver.cdp.dom_snapshot.Rectangle"><code class="xref py py-class docutils literal notranslate"><span class="pre">Rectangle</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.LayoutTreeSnapshot.bounds" title="Link to this definition">#</a></dt>
<dd><p>The absolute position bounding box.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.LayoutTreeSnapshot.text">
<span class="sig-name descname"><span class="pre">text</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.dom_snapshot.StringIndex" title="nodriver.cdp.dom_snapshot.StringIndex"><code class="xref py py-class docutils literal notranslate"><span class="pre">StringIndex</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.LayoutTreeSnapshot.text" title="Link to this definition">#</a></dt>
<dd><p>Contents of the LayoutText, if any.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.LayoutTreeSnapshot.stacking_contexts">
<span class="sig-name descname"><span class="pre">stacking_contexts</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.dom_snapshot.RareBooleanData" title="nodriver.cdp.dom_snapshot.RareBooleanData"><code class="xref py py-class docutils literal notranslate"><span class="pre">RareBooleanData</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.LayoutTreeSnapshot.stacking_contexts" title="Link to this definition">#</a></dt>
<dd><p>Stacking context information.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.LayoutTreeSnapshot.paint_orders">
<span class="sig-name descname"><span class="pre">paint_orders</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a><span class="pre">]]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.LayoutTreeSnapshot.paint_orders" title="Link to this definition">#</a></dt>
<dd><p>Global paint order index, which is determined by the stacking order of the nodes. Nodes
that are painted together will have the same index. Only provided if includePaintOrder in
captureSnapshot was true.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.LayoutTreeSnapshot.offset_rects">
<span class="sig-name descname"><span class="pre">offset_rects</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.dom_snapshot.Rectangle" title="nodriver.cdp.dom_snapshot.Rectangle"><code class="xref py py-class docutils literal notranslate"><span class="pre">Rectangle</span></code></a><span class="pre">]]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.LayoutTreeSnapshot.offset_rects" title="Link to this definition">#</a></dt>
<dd><p>The offset rect of nodes. Only available when includeDOMRects is set to true</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.LayoutTreeSnapshot.scroll_rects">
<span class="sig-name descname"><span class="pre">scroll_rects</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.dom_snapshot.Rectangle" title="nodriver.cdp.dom_snapshot.Rectangle"><code class="xref py py-class docutils literal notranslate"><span class="pre">Rectangle</span></code></a><span class="pre">]]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.LayoutTreeSnapshot.scroll_rects" title="Link to this definition">#</a></dt>
<dd><p>The scroll rect of nodes. Only available when includeDOMRects is set to true</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.LayoutTreeSnapshot.client_rects">
<span class="sig-name descname"><span class="pre">client_rects</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.dom_snapshot.Rectangle" title="nodriver.cdp.dom_snapshot.Rectangle"><code class="xref py py-class docutils literal notranslate"><span class="pre">Rectangle</span></code></a><span class="pre">]]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.LayoutTreeSnapshot.client_rects" title="Link to this definition">#</a></dt>
<dd><p>The client rect of nodes. Only available when includeDOMRects is set to true</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.LayoutTreeSnapshot.blended_background_colors">
<span class="sig-name descname"><span class="pre">blended_background_colors</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.dom_snapshot.StringIndex" title="nodriver.cdp.dom_snapshot.StringIndex"><code class="xref py py-class docutils literal notranslate"><span class="pre">StringIndex</span></code></a><span class="pre">]]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.LayoutTreeSnapshot.blended_background_colors" title="Link to this definition">#</a></dt>
<dd><p>The list of background colors that are blended with colors of overlapping elements.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.LayoutTreeSnapshot.text_color_opacities">
<span class="sig-name descname"><span class="pre">text_color_opacities</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a><span class="pre">]]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.LayoutTreeSnapshot.text_color_opacities" title="Link to this definition">#</a></dt>
<dd><p>The list of computed text opacities.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.TextBoxSnapshot">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">TextBoxSnapshot</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">layout_index</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">bounds</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">length</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom_snapshot.html#TextBoxSnapshot"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom_snapshot.TextBoxSnapshot" title="Link to this definition">#</a></dt>
<dd><p>Table of details of the post layout rendered text positions. The exact layout should not be regarded as
stable and may change between versions.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.TextBoxSnapshot.layout_index">
<span class="sig-name descname"><span class="pre">layout_index</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.TextBoxSnapshot.layout_index" title="Link to this definition">#</a></dt>
<dd><p>Index of the layout tree node that owns this box collection.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.TextBoxSnapshot.bounds">
<span class="sig-name descname"><span class="pre">bounds</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.dom_snapshot.Rectangle" title="nodriver.cdp.dom_snapshot.Rectangle"><code class="xref py py-class docutils literal notranslate"><span class="pre">Rectangle</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.TextBoxSnapshot.bounds" title="Link to this definition">#</a></dt>
<dd><p>The absolute position bounding box.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.TextBoxSnapshot.start">
<span class="sig-name descname"><span class="pre">start</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.TextBoxSnapshot.start" title="Link to this definition">#</a></dt>
<dd><p>The starting index in characters, for this post layout textbox substring. Characters that
would be represented as a surrogate pair in UTF-16 have length 2.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.TextBoxSnapshot.length">
<span class="sig-name descname"><span class="pre">length</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.dom_snapshot.TextBoxSnapshot.length" title="Link to this definition">#</a></dt>
<dd><p>The number of characters in this post layout textbox substring. Characters that would be
represented as a surrogate pair in UTF-16 have length 2.</p>
</dd></dl>

</dd></dl>

</section>
<section id="commands">
<h2>Commands<a class="headerlink" href="#commands" title="Link to this heading">#</a></h2>
<p>Each command is a generator function. The return
type <code class="docutils literal notranslate"><span class="pre">Generator[x,</span> <span class="pre">y,</span> <span class="pre">z]</span></code> indicates that the generator
<em>yields</em> arguments of type <code class="docutils literal notranslate"><span class="pre">x</span></code>, it must be resumed with
an argument of type <code class="docutils literal notranslate"><span class="pre">y</span></code>, and it returns type <code class="docutils literal notranslate"><span class="pre">z</span></code>. In
this library, types <code class="docutils literal notranslate"><span class="pre">x</span></code> and <code class="docutils literal notranslate"><span class="pre">y</span></code> are the same for all
commands, and <code class="docutils literal notranslate"><span class="pre">z</span></code> is the return type you should pay attention
to. For more information, see
<a class="reference internal" href="../../readme.html#getting-started-commands"><span class="std std-ref">Getting Started: Commands</span></a>.</p>
<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.capture_snapshot">
<span class="sig-name descname"><span class="pre">capture_snapshot</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">computed_styles</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">include_paint_order</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">include_dom_rects</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">include_blended_background_colors</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">include_text_color_opacities</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom_snapshot.html#capture_snapshot"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom_snapshot.capture_snapshot" title="Link to this definition">#</a></dt>
<dd><p>Returns a document snapshot, including the full DOM tree of the root node (including iframes,
template contents, and imported documents) in a flattened array, as well as layout and
white-listed computed style information for the nodes. Shadow DOM in the returned DOM tree is
flattened.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>computed_styles</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]</span>) – Whitelist of computed styles to return.</p></li>
<li><p><strong>include_paint_order</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <em>(Optional)</em> Whether to include layout object paint orders into the snapshot.</p></li>
<li><p><strong>include_dom_rects</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <em>(Optional)</em> Whether to include DOM rectangles (offsetRects, clientRects, scrollRects) into the snapshot</p></li>
<li><p><strong>include_blended_background_colors</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <strong>(EXPERIMENTAL)</strong> <em>(Optional)</em> Whether to include blended background colors in the snapshot (default: false). Blended background color is achieved by blending background colors of all elements that overlap with the current element.</p></li>
<li><p><strong>include_text_color_opacities</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <strong>(EXPERIMENTAL)</strong> <em>(Optional)</em> Whether to include text color opacity in the snapshot (default: false). An element might have the opacity property set that affects the text color of the element. The final text color opacity is computed based on the opacity of all overlapping elements.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Tuple" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Tuple</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.dom_snapshot.DocumentSnapshot" title="nodriver.cdp.dom_snapshot.DocumentSnapshot"><code class="xref py py-class docutils literal notranslate"><span class="pre">DocumentSnapshot</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]]]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p><p>A tuple with the following items:</p>
<ol class="arabic simple" start="0">
<li><p><strong>documents</strong> - The nodes in the DOM tree. The DOMNode at index 0 corresponds to the root document.</p></li>
<li><p><strong>strings</strong> - Shared string table that all string properties refer to with indexes.</p></li>
</ol>
</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.disable">
<span class="sig-name descname"><span class="pre">disable</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom_snapshot.html#disable"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom_snapshot.disable" title="Link to this definition">#</a></dt>
<dd><p>Disables DOM snapshot agent for the given page.</p>
<dl class="field-list simple">
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.enable">
<span class="sig-name descname"><span class="pre">enable</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom_snapshot.html#enable"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom_snapshot.enable" title="Link to this definition">#</a></dt>
<dd><p>Enables DOM snapshot agent for the given page.</p>
<dl class="field-list simple">
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.dom_snapshot.get_snapshot">
<span class="sig-name descname"><span class="pre">get_snapshot</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">computed_style_whitelist</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">include_event_listeners</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">include_paint_order</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">include_user_agent_shadow_tree</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom_snapshot.html#get_snapshot"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom_snapshot.get_snapshot" title="Link to this definition">#</a></dt>
<dd><p>Returns a document snapshot, including the full DOM tree of the root node (including iframes,
template contents, and imported documents) in a flattened array, as well as layout and
white-listed computed style information for the nodes. Shadow DOM in the returned DOM tree is
flattened.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 1.3.</span></p>
</div>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>computed_style_whitelist</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]</span>) – Whitelist of computed styles to return.</p></li>
<li><p><strong>include_event_listeners</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <em>(Optional)</em> Whether or not to retrieve details of DOM listeners (default false).</p></li>
<li><p><strong>include_paint_order</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <em>(Optional)</em> Whether to determine and include the paint order index of LayoutTreeNodes (default false).</p></li>
<li><p><strong>include_user_agent_shadow_tree</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <em>(Optional)</em> Whether to include UA shadow tree in the snapshot (default false).</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Tuple" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Tuple</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.dom_snapshot.DOMNode" title="nodriver.cdp.dom_snapshot.DOMNode"><code class="xref py py-class docutils literal notranslate"><span class="pre">DOMNode</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.dom_snapshot.LayoutTreeNode" title="nodriver.cdp.dom_snapshot.LayoutTreeNode"><code class="xref py py-class docutils literal notranslate"><span class="pre">LayoutTreeNode</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.dom_snapshot.ComputedStyle" title="nodriver.cdp.dom_snapshot.ComputedStyle"><code class="xref py py-class docutils literal notranslate"><span class="pre">ComputedStyle</span></code></a>]]]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p><p>A tuple with the following items:</p>
<ol class="arabic simple" start="0">
<li><p><strong>domNodes</strong> - The nodes in the DOM tree. The DOMNode at index 0 corresponds to the root document.</p></li>
<li><p><strong>layoutTreeNodes</strong> - The nodes in the layout tree.</p></li>
<li><p><strong>computedStyles</strong> - Whitelisted ComputedStyle properties for each node in the layout tree.</p></li>
</ol>
</p>
</dd>
</dl>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 1.3.</span></p>
</div>
</dd></dl>

</section>
<section id="events">
<h2>Events<a class="headerlink" href="#events" title="Link to this heading">#</a></h2>
<p><em>There are no events in this module.</em></p>
</section>
</section>

        </article>
      </div>
      <footer>
        
        <div class="related-pages">
          <a class="next-page" href="dom_storage.html">
              <div class="page-info">
                <div class="context">
                  <span>Next</span>
                </div>
                <div class="title">DOMStorage</div>
              </div>
              <svg class="furo-related-icon"><use href="#svg-arrow-right"></use></svg>
            </a>
          <a class="prev-page" href="dom_debugger.html">
              <svg class="furo-related-icon"><use href="#svg-arrow-right"></use></svg>
              <div class="page-info">
                <div class="context">
                  <span>Previous</span>
                </div>
                
                <div class="title">DOMDebugger</div>
                
              </div>
            </a>
        </div>
        <div class="bottom-of-page">
          <div class="left-details">
            <div class="copyright">
                Copyright &#169; 2023, Author
            </div>
            Made with <a href="https://www.sphinx-doc.org/">Sphinx</a> and <a class="muted-link" href="https://pradyunsg.me">@pradyunsg</a>'s
            
            <a href="https://github.com/pradyunsg/furo">Furo</a>
            
          </div>
          <div class="right-details">
            
          </div>
        </div>
        
      </footer>
    </div>
    <aside class="toc-drawer">
      
      
      <div class="toc-sticky toc-scroll">
        <div class="toc-title-container">
          <span class="toc-title">
            On this page
          </span>
        </div>
        <div class="toc-tree-container">
          <div class="toc-tree">
            <ul>
<li><a class="reference internal" href="#">DOMSnapshot</a><ul>
<li><a class="reference internal" href="#types">Types</a><ul>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.DOMNode"><code class="docutils literal notranslate"><span class="pre">DOMNode</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.DOMNode.node_type"><code class="docutils literal notranslate"><span class="pre">DOMNode.node_type</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.DOMNode.node_name"><code class="docutils literal notranslate"><span class="pre">DOMNode.node_name</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.DOMNode.node_value"><code class="docutils literal notranslate"><span class="pre">DOMNode.node_value</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.DOMNode.backend_node_id"><code class="docutils literal notranslate"><span class="pre">DOMNode.backend_node_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.DOMNode.text_value"><code class="docutils literal notranslate"><span class="pre">DOMNode.text_value</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.DOMNode.input_value"><code class="docutils literal notranslate"><span class="pre">DOMNode.input_value</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.DOMNode.input_checked"><code class="docutils literal notranslate"><span class="pre">DOMNode.input_checked</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.DOMNode.option_selected"><code class="docutils literal notranslate"><span class="pre">DOMNode.option_selected</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.DOMNode.child_node_indexes"><code class="docutils literal notranslate"><span class="pre">DOMNode.child_node_indexes</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.DOMNode.attributes"><code class="docutils literal notranslate"><span class="pre">DOMNode.attributes</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.DOMNode.pseudo_element_indexes"><code class="docutils literal notranslate"><span class="pre">DOMNode.pseudo_element_indexes</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.DOMNode.layout_node_index"><code class="docutils literal notranslate"><span class="pre">DOMNode.layout_node_index</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.DOMNode.document_url"><code class="docutils literal notranslate"><span class="pre">DOMNode.document_url</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.DOMNode.base_url"><code class="docutils literal notranslate"><span class="pre">DOMNode.base_url</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.DOMNode.content_language"><code class="docutils literal notranslate"><span class="pre">DOMNode.content_language</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.DOMNode.document_encoding"><code class="docutils literal notranslate"><span class="pre">DOMNode.document_encoding</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.DOMNode.public_id"><code class="docutils literal notranslate"><span class="pre">DOMNode.public_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.DOMNode.system_id"><code class="docutils literal notranslate"><span class="pre">DOMNode.system_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.DOMNode.frame_id"><code class="docutils literal notranslate"><span class="pre">DOMNode.frame_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.DOMNode.content_document_index"><code class="docutils literal notranslate"><span class="pre">DOMNode.content_document_index</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.DOMNode.pseudo_type"><code class="docutils literal notranslate"><span class="pre">DOMNode.pseudo_type</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.DOMNode.shadow_root_type"><code class="docutils literal notranslate"><span class="pre">DOMNode.shadow_root_type</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.DOMNode.is_clickable"><code class="docutils literal notranslate"><span class="pre">DOMNode.is_clickable</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.DOMNode.event_listeners"><code class="docutils literal notranslate"><span class="pre">DOMNode.event_listeners</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.DOMNode.current_source_url"><code class="docutils literal notranslate"><span class="pre">DOMNode.current_source_url</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.DOMNode.origin_url"><code class="docutils literal notranslate"><span class="pre">DOMNode.origin_url</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.DOMNode.scroll_offset_x"><code class="docutils literal notranslate"><span class="pre">DOMNode.scroll_offset_x</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.DOMNode.scroll_offset_y"><code class="docutils literal notranslate"><span class="pre">DOMNode.scroll_offset_y</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.InlineTextBox"><code class="docutils literal notranslate"><span class="pre">InlineTextBox</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.InlineTextBox.bounding_box"><code class="docutils literal notranslate"><span class="pre">InlineTextBox.bounding_box</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.InlineTextBox.start_character_index"><code class="docutils literal notranslate"><span class="pre">InlineTextBox.start_character_index</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.InlineTextBox.num_characters"><code class="docutils literal notranslate"><span class="pre">InlineTextBox.num_characters</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.LayoutTreeNode"><code class="docutils literal notranslate"><span class="pre">LayoutTreeNode</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.LayoutTreeNode.dom_node_index"><code class="docutils literal notranslate"><span class="pre">LayoutTreeNode.dom_node_index</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.LayoutTreeNode.bounding_box"><code class="docutils literal notranslate"><span class="pre">LayoutTreeNode.bounding_box</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.LayoutTreeNode.layout_text"><code class="docutils literal notranslate"><span class="pre">LayoutTreeNode.layout_text</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.LayoutTreeNode.inline_text_nodes"><code class="docutils literal notranslate"><span class="pre">LayoutTreeNode.inline_text_nodes</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.LayoutTreeNode.style_index"><code class="docutils literal notranslate"><span class="pre">LayoutTreeNode.style_index</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.LayoutTreeNode.paint_order"><code class="docutils literal notranslate"><span class="pre">LayoutTreeNode.paint_order</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.LayoutTreeNode.is_stacking_context"><code class="docutils literal notranslate"><span class="pre">LayoutTreeNode.is_stacking_context</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.ComputedStyle"><code class="docutils literal notranslate"><span class="pre">ComputedStyle</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.ComputedStyle.properties"><code class="docutils literal notranslate"><span class="pre">ComputedStyle.properties</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.NameValue"><code class="docutils literal notranslate"><span class="pre">NameValue</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.NameValue.name"><code class="docutils literal notranslate"><span class="pre">NameValue.name</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.NameValue.value"><code class="docutils literal notranslate"><span class="pre">NameValue.value</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.StringIndex"><code class="docutils literal notranslate"><span class="pre">StringIndex</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.ArrayOfStrings"><code class="docutils literal notranslate"><span class="pre">ArrayOfStrings</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.RareStringData"><code class="docutils literal notranslate"><span class="pre">RareStringData</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.RareStringData.index"><code class="docutils literal notranslate"><span class="pre">RareStringData.index</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.RareStringData.value"><code class="docutils literal notranslate"><span class="pre">RareStringData.value</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.RareBooleanData"><code class="docutils literal notranslate"><span class="pre">RareBooleanData</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.RareBooleanData.index"><code class="docutils literal notranslate"><span class="pre">RareBooleanData.index</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.RareIntegerData"><code class="docutils literal notranslate"><span class="pre">RareIntegerData</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.RareIntegerData.index"><code class="docutils literal notranslate"><span class="pre">RareIntegerData.index</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.RareIntegerData.value"><code class="docutils literal notranslate"><span class="pre">RareIntegerData.value</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.Rectangle"><code class="docutils literal notranslate"><span class="pre">Rectangle</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.DocumentSnapshot"><code class="docutils literal notranslate"><span class="pre">DocumentSnapshot</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.DocumentSnapshot.document_url"><code class="docutils literal notranslate"><span class="pre">DocumentSnapshot.document_url</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.DocumentSnapshot.title"><code class="docutils literal notranslate"><span class="pre">DocumentSnapshot.title</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.DocumentSnapshot.base_url"><code class="docutils literal notranslate"><span class="pre">DocumentSnapshot.base_url</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.DocumentSnapshot.content_language"><code class="docutils literal notranslate"><span class="pre">DocumentSnapshot.content_language</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.DocumentSnapshot.encoding_name"><code class="docutils literal notranslate"><span class="pre">DocumentSnapshot.encoding_name</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.DocumentSnapshot.public_id"><code class="docutils literal notranslate"><span class="pre">DocumentSnapshot.public_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.DocumentSnapshot.system_id"><code class="docutils literal notranslate"><span class="pre">DocumentSnapshot.system_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.DocumentSnapshot.frame_id"><code class="docutils literal notranslate"><span class="pre">DocumentSnapshot.frame_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.DocumentSnapshot.nodes"><code class="docutils literal notranslate"><span class="pre">DocumentSnapshot.nodes</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.DocumentSnapshot.layout"><code class="docutils literal notranslate"><span class="pre">DocumentSnapshot.layout</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.DocumentSnapshot.text_boxes"><code class="docutils literal notranslate"><span class="pre">DocumentSnapshot.text_boxes</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.DocumentSnapshot.scroll_offset_x"><code class="docutils literal notranslate"><span class="pre">DocumentSnapshot.scroll_offset_x</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.DocumentSnapshot.scroll_offset_y"><code class="docutils literal notranslate"><span class="pre">DocumentSnapshot.scroll_offset_y</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.DocumentSnapshot.content_width"><code class="docutils literal notranslate"><span class="pre">DocumentSnapshot.content_width</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.DocumentSnapshot.content_height"><code class="docutils literal notranslate"><span class="pre">DocumentSnapshot.content_height</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.NodeTreeSnapshot"><code class="docutils literal notranslate"><span class="pre">NodeTreeSnapshot</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.NodeTreeSnapshot.parent_index"><code class="docutils literal notranslate"><span class="pre">NodeTreeSnapshot.parent_index</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.NodeTreeSnapshot.node_type"><code class="docutils literal notranslate"><span class="pre">NodeTreeSnapshot.node_type</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.NodeTreeSnapshot.shadow_root_type"><code class="docutils literal notranslate"><span class="pre">NodeTreeSnapshot.shadow_root_type</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.NodeTreeSnapshot.node_name"><code class="docutils literal notranslate"><span class="pre">NodeTreeSnapshot.node_name</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.NodeTreeSnapshot.node_value"><code class="docutils literal notranslate"><span class="pre">NodeTreeSnapshot.node_value</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.NodeTreeSnapshot.backend_node_id"><code class="docutils literal notranslate"><span class="pre">NodeTreeSnapshot.backend_node_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.NodeTreeSnapshot.attributes"><code class="docutils literal notranslate"><span class="pre">NodeTreeSnapshot.attributes</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.NodeTreeSnapshot.text_value"><code class="docutils literal notranslate"><span class="pre">NodeTreeSnapshot.text_value</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.NodeTreeSnapshot.input_value"><code class="docutils literal notranslate"><span class="pre">NodeTreeSnapshot.input_value</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.NodeTreeSnapshot.input_checked"><code class="docutils literal notranslate"><span class="pre">NodeTreeSnapshot.input_checked</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.NodeTreeSnapshot.option_selected"><code class="docutils literal notranslate"><span class="pre">NodeTreeSnapshot.option_selected</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.NodeTreeSnapshot.content_document_index"><code class="docutils literal notranslate"><span class="pre">NodeTreeSnapshot.content_document_index</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.NodeTreeSnapshot.pseudo_type"><code class="docutils literal notranslate"><span class="pre">NodeTreeSnapshot.pseudo_type</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.NodeTreeSnapshot.pseudo_identifier"><code class="docutils literal notranslate"><span class="pre">NodeTreeSnapshot.pseudo_identifier</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.NodeTreeSnapshot.is_clickable"><code class="docutils literal notranslate"><span class="pre">NodeTreeSnapshot.is_clickable</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.NodeTreeSnapshot.current_source_url"><code class="docutils literal notranslate"><span class="pre">NodeTreeSnapshot.current_source_url</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.NodeTreeSnapshot.origin_url"><code class="docutils literal notranslate"><span class="pre">NodeTreeSnapshot.origin_url</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.LayoutTreeSnapshot"><code class="docutils literal notranslate"><span class="pre">LayoutTreeSnapshot</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.LayoutTreeSnapshot.node_index"><code class="docutils literal notranslate"><span class="pre">LayoutTreeSnapshot.node_index</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.LayoutTreeSnapshot.styles"><code class="docutils literal notranslate"><span class="pre">LayoutTreeSnapshot.styles</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.LayoutTreeSnapshot.bounds"><code class="docutils literal notranslate"><span class="pre">LayoutTreeSnapshot.bounds</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.LayoutTreeSnapshot.text"><code class="docutils literal notranslate"><span class="pre">LayoutTreeSnapshot.text</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.LayoutTreeSnapshot.stacking_contexts"><code class="docutils literal notranslate"><span class="pre">LayoutTreeSnapshot.stacking_contexts</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.LayoutTreeSnapshot.paint_orders"><code class="docutils literal notranslate"><span class="pre">LayoutTreeSnapshot.paint_orders</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.LayoutTreeSnapshot.offset_rects"><code class="docutils literal notranslate"><span class="pre">LayoutTreeSnapshot.offset_rects</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.LayoutTreeSnapshot.scroll_rects"><code class="docutils literal notranslate"><span class="pre">LayoutTreeSnapshot.scroll_rects</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.LayoutTreeSnapshot.client_rects"><code class="docutils literal notranslate"><span class="pre">LayoutTreeSnapshot.client_rects</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.LayoutTreeSnapshot.blended_background_colors"><code class="docutils literal notranslate"><span class="pre">LayoutTreeSnapshot.blended_background_colors</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.LayoutTreeSnapshot.text_color_opacities"><code class="docutils literal notranslate"><span class="pre">LayoutTreeSnapshot.text_color_opacities</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.TextBoxSnapshot"><code class="docutils literal notranslate"><span class="pre">TextBoxSnapshot</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.TextBoxSnapshot.layout_index"><code class="docutils literal notranslate"><span class="pre">TextBoxSnapshot.layout_index</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.TextBoxSnapshot.bounds"><code class="docutils literal notranslate"><span class="pre">TextBoxSnapshot.bounds</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.TextBoxSnapshot.start"><code class="docutils literal notranslate"><span class="pre">TextBoxSnapshot.start</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.TextBoxSnapshot.length"><code class="docutils literal notranslate"><span class="pre">TextBoxSnapshot.length</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#commands">Commands</a><ul>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.capture_snapshot"><code class="docutils literal notranslate"><span class="pre">capture_snapshot()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.disable"><code class="docutils literal notranslate"><span class="pre">disable()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.enable"><code class="docutils literal notranslate"><span class="pre">enable()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom_snapshot.get_snapshot"><code class="docutils literal notranslate"><span class="pre">get_snapshot()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#events">Events</a></li>
</ul>
</li>
</ul>

          </div>
        </div>
      </div>
      
      
    </aside>
  </div>
</div><script src="../../_static/documentation_options.js?v=5929fcd5"></script>
    <script src="../../_static/doctools.js?v=888ff710"></script>
    <script src="../../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../../_static/scripts/furo.js?v=32e29ea5"></script>
    </body>
</html>