<!doctype html>
<html class="no-js" lang="en" data-content_root="../../../">
  <head><meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width,initial-scale=1"/>
    <meta name="color-scheme" content="light dark"><link rel="index" title="Index" href="../../../genindex.html" /><link rel="search" title="Search" href="../../../search.html" />

    <!-- Generated with Sphinx 7.2.6 and Furo 2023.09.10 -->
        <title>nodriver.core.element - nodriver documentation</title>
      <link rel="stylesheet" type="text/css" href="../../../_static/pygments.css?v=fa44fd50" />
    <link rel="stylesheet" type="text/css" href="../../../_static/styles/furo.css?v=135e06be" />
    <link rel="stylesheet" type="text/css" href="../../../_static/styles/furo-extensions.css?v=36a5483c" />
    <link rel="stylesheet" type="text/css" href="../../../_static/./custom.css?v=3934642d" />
    
    


<style>
  body {
    --color-code-background: #eeffcc;
  --color-code-foreground: black;
  
  }
  @media not print {
    body[data-theme="dark"] {
      --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8eefba;
  --color-background-primary: #111112;
  --color-problematic: #e7aeae;
  
    }
    @media (prefers-color-scheme: dark) {
      body:not([data-theme="light"]) {
        --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8eefba;
  --color-background-primary: #111112;
  --color-problematic: #e7aeae;
  
      }
    }
  }
</style></head>
  <body>
    
    <script>
      document.body.dataset.theme = localStorage.getItem("theme") || "auto";
    </script>
    

<svg xmlns="http://www.w3.org/2000/svg" style="display: none;">
  <symbol id="svg-toc" viewBox="0 0 24 24">
    <title>Contents</title>
    <svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 1024 1024">
      <path d="M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM115.4 518.9L271.7 642c5.8 4.6 14.4.5 14.4-6.9V388.9c0-7.4-8.5-11.5-14.4-6.9L115.4 505.1a8.74 8.74 0 0 0 0 13.8z"/>
    </svg>
  </symbol>
  <symbol id="svg-menu" viewBox="0 0 24 24">
    <title>Menu</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-menu">
      <line x1="3" y1="12" x2="21" y2="12"></line>
      <line x1="3" y1="6" x2="21" y2="6"></line>
      <line x1="3" y1="18" x2="21" y2="18"></line>
    </svg>
  </symbol>
  <symbol id="svg-arrow-right" viewBox="0 0 24 24">
    <title>Expand</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-chevron-right">
      <polyline points="9 18 15 12 9 6"></polyline>
    </svg>
  </symbol>
  <symbol id="svg-sun" viewBox="0 0 24 24">
    <title>Light mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="feather-sun">
      <circle cx="12" cy="12" r="5"></circle>
      <line x1="12" y1="1" x2="12" y2="3"></line>
      <line x1="12" y1="21" x2="12" y2="23"></line>
      <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
      <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
      <line x1="1" y1="12" x2="3" y2="12"></line>
      <line x1="21" y1="12" x2="23" y2="12"></line>
      <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
      <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
    </svg>
  </symbol>
  <symbol id="svg-moon" viewBox="0 0 24 24">
    <title>Dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-moon">
      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
      <path d="M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z" />
    </svg>
  </symbol>
  <symbol id="svg-sun-half" viewBox="0 0 24 24">
    <title>Auto light/dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-shadow">
      <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
      <circle cx="12" cy="12" r="9" />
      <path d="M13 12h5" />
      <path d="M13 15h4" />
      <path d="M13 18h1" />
      <path d="M13 9h4" />
      <path d="M13 6h1" />
    </svg>
  </symbol>
</svg>

<input type="checkbox" class="sidebar-toggle" name="__navigation" id="__navigation">
<input type="checkbox" class="sidebar-toggle" name="__toc" id="__toc">
<label class="overlay sidebar-overlay" for="__navigation">
  <div class="visually-hidden">Hide navigation sidebar</div>
</label>
<label class="overlay toc-overlay" for="__toc">
  <div class="visually-hidden">Hide table of contents sidebar</div>
</label>



<div class="page">
  <header class="mobile-header">
    <div class="header-left">
      <label class="nav-overlay-icon" for="__navigation">
        <div class="visually-hidden">Toggle site navigation sidebar</div>
        <i class="icon"><svg><use href="#svg-menu"></use></svg></i>
      </label>
    </div>
    <div class="header-center">
      <a href="../../../index.html"><div class="brand">nodriver  documentation</div></a>
    </div>
    <div class="header-right">
      <div class="theme-toggle-container theme-toggle-header">
        <button class="theme-toggle">
          <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
          <svg class="theme-icon-when-auto"><use href="#svg-sun-half"></use></svg>
          <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
          <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
        </button>
      </div>
      <label class="toc-overlay-icon toc-header-icon no-toc" for="__toc">
        <div class="visually-hidden">Toggle table of contents sidebar</div>
        <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
      </label>
    </div>
  </header>
  <aside class="sidebar-drawer">
    <div class="sidebar-container">
      
      <div class="sidebar-sticky"><div class="sidebar-scroll"><a class="sidebar-brand" href="../../../index.html">
  
  
  <span class="sidebar-brand-text">nodriver  documentation</span>
  
</a><form class="sidebar-search-container" method="get" action="../../../search.html" role="search">
  <input class="sidebar-search" placeholder="Search" name="q" aria-label="Search">
  <input type="hidden" name="check_keywords" value="yes">
  <input type="hidden" name="area" value="default">
</form>
<div id="searchbox"></div><div class="sidebar-tree">
  <ul>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html">Quickstart guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#usage-example">usage example</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#more-complete-example">More complete example</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#custom-starting-options">Custom starting options</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#alternative-custom-options">Alternative custom options</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#proxies-socks5-authenticated-too">Proxies (socks5, authenticated too)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#concrete-example">Concrete example</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/classes/browser.html">Browser class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/classes/tab.html">Tab class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/classes/element.html">Element class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/classes/others_and_helpers.html">Other classes and Helper classes</a></li>
</ul>
<ul>
<li class="toctree-l1 has-children"><a class="reference internal" href="../../../nodriver/cdp.html">CDP object</a><input class="toctree-checkbox" id="toctree-checkbox-1" name="toctree-checkbox-1" role="switch" type="checkbox"/><label for="toctree-checkbox-1"><div class="visually-hidden">Toggle navigation of CDP object</div><i class="icon"><svg><use href="#svg-arrow-right"></use></svg></i></label><ul>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/accessibility.html">Accessibility</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/animation.html">Animation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/audits.html">Audits</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/autofill.html">Autofill</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/background_service.html">BackgroundService</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/bluetooth_emulation.html">BluetoothEmulation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/browser.html">Browser</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/cache_storage.html">CacheStorage</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/cast.html">Cast</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/console.html">Console</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/css.html">CSS</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/debugger.html">Debugger</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/device_access.html">DeviceAccess</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/device_orientation.html">DeviceOrientation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/dom.html">DOM</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/dom_debugger.html">DOMDebugger</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/dom_snapshot.html">DOMSnapshot</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/dom_storage.html">DOMStorage</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/emulation.html">Emulation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/event_breakpoints.html">EventBreakpoints</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/extensions.html">Extensions</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/fed_cm.html">FedCm</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/fetch.html">Fetch</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/file_system.html">FileSystem</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/headless_experimental.html">HeadlessExperimental</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/heap_profiler.html">HeapProfiler</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/indexed_db.html">IndexedDB</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/input_.html">Input</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/inspector.html">Inspector</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/io.html">IO</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/layer_tree.html">LayerTree</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/log.html">Log</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/media.html">Media</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/memory.html">Memory</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/network.html">Network</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/overlay.html">Overlay</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/page.html">Page</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/performance.html">Performance</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/performance_timeline.html">PerformanceTimeline</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/preload.html">Preload</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/profiler.html">Profiler</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/pwa.html">PWA</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/runtime.html">Runtime</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/schema.html">Schema</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/security.html">Security</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/service_worker.html">ServiceWorker</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/storage.html">Storage</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/system_info.html">SystemInfo</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/target.html">Target</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/tethering.html">Tethering</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/tracing.html">Tracing</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/web_audio.html">WebAudio</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/web_authn.html">WebAuthn</a></li>
</ul>
</li>
</ul>

</div>
</div>
      </div>
      
    </div>
  </aside>
  <div class="main">
    <div class="content">
      <div class="article-container">
        <a href="#" class="back-to-top muted-link">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
            <path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8v12z"></path>
          </svg>
          <span>Back to top</span>
        </a>
        <div class="content-icon-container">
          <div class="theme-toggle-container theme-toggle-content">
            <button class="theme-toggle">
              <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
              <svg class="theme-icon-when-auto"><use href="#svg-sun-half"></use></svg>
              <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
              <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
            </button>
          </div>
          <label class="toc-overlay-icon toc-content-icon no-toc" for="__toc">
            <div class="visually-hidden">Toggle table of contents sidebar</div>
            <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
          </label>
        </div>
        <article role="main">
          <h1>Source code for nodriver.core.element</h1><div class="highlight"><pre>
<span></span><span class="c1"># Copyright 2024 by UltrafunkAmsterdam (https://github.com/UltrafunkAmsterdam)</span>
<span class="c1"># All rights reserved.</span>
<span class="c1"># This file is part of the nodriver package.</span>
<span class="c1"># and is released under the &quot;GNU AFFERO GENERAL PUBLIC LICENSE&quot;.</span>
<span class="c1"># Please see the LICENSE.txt file that should have been included as part of this package.</span>

<span class="kn">from</span> <span class="nn">__future__</span> <span class="kn">import</span> <span class="n">annotations</span>

<span class="kn">import</span> <span class="nn">json</span>
<span class="kn">import</span> <span class="nn">logging</span>
<span class="kn">import</span> <span class="nn">pathlib</span>
<span class="kn">import</span> <span class="nn">secrets</span>
<span class="kn">import</span> <span class="nn">typing</span>

<span class="kn">from</span> <span class="nn">..</span> <span class="kn">import</span> <span class="n">cdp</span>
<span class="kn">from</span> <span class="nn">.</span> <span class="kn">import</span> <span class="n">util</span>
<span class="kn">from</span> <span class="nn">._contradict</span> <span class="kn">import</span> <span class="n">ContraDict</span>
<span class="kn">from</span> <span class="nn">.config</span> <span class="kn">import</span> <span class="n">PathLike</span>

<span class="n">logger</span> <span class="o">=</span> <span class="n">logging</span><span class="o">.</span><span class="n">getLogger</span><span class="p">(</span><span class="vm">__name__</span><span class="p">)</span>

<span class="k">if</span> <span class="n">typing</span><span class="o">.</span><span class="n">TYPE_CHECKING</span><span class="p">:</span>
    <span class="kn">from</span> <span class="nn">.tab</span> <span class="kn">import</span> <span class="n">Tab</span>


<span class="k">def</span> <span class="nf">create</span><span class="p">(</span><span class="n">node</span><span class="p">:</span> <span class="n">cdp</span><span class="o">.</span><span class="n">dom</span><span class="o">.</span><span class="n">Node</span><span class="p">,</span> <span class="n">tab</span><span class="p">:</span> <span class="n">Tab</span><span class="p">,</span> <span class="n">tree</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">cdp</span><span class="o">.</span><span class="n">dom</span><span class="o">.</span><span class="n">Node</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    factory for Elements</span>
<span class="sd">    this is used with Tab.query_selector(_all), since we already have the tree,</span>
<span class="sd">    we don&#39;t need to fetch it for every single element.</span>

<span class="sd">    :param node: cdp dom node representation</span>
<span class="sd">    :type node: cdp.dom.Node</span>
<span class="sd">    :param tab: the target object to which this element belongs</span>
<span class="sd">    :type tab: Tab</span>
<span class="sd">    :param tree: [Optional] the full node tree to which &lt;node&gt; belongs, enhances performance.</span>
<span class="sd">                when not provided, you need to call `await elem.update()` before using .children / .parent</span>
<span class="sd">    :type tree:</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">elem</span> <span class="o">=</span> <span class="n">Element</span><span class="p">(</span><span class="n">node</span><span class="p">,</span> <span class="n">tab</span><span class="p">,</span> <span class="n">tree</span><span class="p">)</span>

    <span class="k">return</span> <span class="n">elem</span>


<div class="viewcode-block" id="Element">
<a class="viewcode-back" href="../../../nodriver/classes/element.html#nodriver.Element">[docs]</a>
<span class="k">class</span> <span class="nc">Element</span><span class="p">:</span>
    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">node</span><span class="p">:</span> <span class="n">cdp</span><span class="o">.</span><span class="n">dom</span><span class="o">.</span><span class="n">Node</span><span class="p">,</span> <span class="n">tab</span><span class="p">:</span> <span class="n">Tab</span><span class="p">,</span> <span class="n">tree</span><span class="p">:</span> <span class="n">cdp</span><span class="o">.</span><span class="n">dom</span><span class="o">.</span><span class="n">Node</span> <span class="o">=</span> <span class="kc">None</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Represents an (HTML) DOM Element</span>

<span class="sd">        :param node: cdp dom node representation</span>
<span class="sd">        :type node: cdp.dom.Node</span>
<span class="sd">        :param tab: the target object to which this element belongs</span>
<span class="sd">        :type tab: Tab</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="n">node</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">Exception</span><span class="p">(</span><span class="s2">&quot;node cannot be None&quot;</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_tab</span> <span class="o">=</span> <span class="n">tab</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">_node</span> <span class="o">=</span> <span class="n">node</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_tree</span> <span class="o">=</span> <span class="n">tree</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_parent</span> <span class="o">=</span> <span class="kc">None</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_remote_object</span> <span class="o">=</span> <span class="kc">None</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_attrs</span> <span class="o">=</span> <span class="n">ContraDict</span><span class="p">(</span><span class="n">silent</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_make_attrs</span><span class="p">()</span>

    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">tag</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">node_name</span><span class="p">:</span>
            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">node_name</span><span class="o">.</span><span class="n">lower</span><span class="p">()</span>

    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">tag_name</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">tag</span>

    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">node_id</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">node</span><span class="o">.</span><span class="n">node_id</span>

    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">backend_node_id</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">node</span><span class="o">.</span><span class="n">backend_node_id</span>

    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">node_type</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">node</span><span class="o">.</span><span class="n">node_type</span>

    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">node_name</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">node</span><span class="o">.</span><span class="n">node_name</span>

    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">local_name</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">node</span><span class="o">.</span><span class="n">local_name</span>

    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">node_value</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">node</span><span class="o">.</span><span class="n">node_value</span>

    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">parent_id</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">node</span><span class="o">.</span><span class="n">parent_id</span>

    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">child_node_count</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">node</span><span class="o">.</span><span class="n">child_node_count</span>

    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">attributes</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">node</span><span class="o">.</span><span class="n">attributes</span>

    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">document_url</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">node</span><span class="o">.</span><span class="n">document_url</span>

    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">base_url</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">node</span><span class="o">.</span><span class="n">base_url</span>

    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">public_id</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">node</span><span class="o">.</span><span class="n">public_id</span>

    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">system_id</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">node</span><span class="o">.</span><span class="n">system_id</span>

    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">internal_subset</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">node</span><span class="o">.</span><span class="n">internal_subset</span>

    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">xml_version</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">node</span><span class="o">.</span><span class="n">xml_version</span>

    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">value</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">node</span><span class="o">.</span><span class="n">value</span>

    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">pseudo_type</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">node</span><span class="o">.</span><span class="n">pseudo_type</span>

    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">pseudo_identifier</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">node</span><span class="o">.</span><span class="n">pseudo_identifier</span>

    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">shadow_root_type</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">node</span><span class="o">.</span><span class="n">shadow_root_type</span>

    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">frame_id</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">node</span><span class="o">.</span><span class="n">frame_id</span>

    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">content_document</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">node</span><span class="o">.</span><span class="n">content_document</span>

    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">shadow_roots</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">node</span><span class="o">.</span><span class="n">shadow_roots</span>

    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">template_content</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">node</span><span class="o">.</span><span class="n">template_content</span>

    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">pseudo_elements</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">node</span><span class="o">.</span><span class="n">pseudo_elements</span>

    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">imported_document</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">node</span><span class="o">.</span><span class="n">imported_document</span>

    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">distributed_nodes</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">node</span><span class="o">.</span><span class="n">distributed_nodes</span>

    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">is_svg</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">node</span><span class="o">.</span><span class="n">is_svg</span>

    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">compatibility_mode</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">node</span><span class="o">.</span><span class="n">compatibility_mode</span>

    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">assigned_slot</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">node</span><span class="o">.</span><span class="n">assigned_slot</span>

    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">tab</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_tab</span>

    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">shadow_children</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">shadow_roots</span><span class="p">:</span>
            <span class="n">root</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">shadow_roots</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span>
            <span class="k">if</span> <span class="n">root</span><span class="o">.</span><span class="n">shadow_root_type</span> <span class="o">==</span> <span class="n">cdp</span><span class="o">.</span><span class="n">dom</span><span class="o">.</span><span class="n">ShadowRootType</span><span class="o">.</span><span class="n">OPEN_</span><span class="p">:</span>
                <span class="k">return</span> <span class="p">[</span><span class="n">create</span><span class="p">(</span><span class="n">child</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">tab</span><span class="p">)</span> <span class="k">for</span> <span class="n">child</span> <span class="ow">in</span> <span class="n">root</span><span class="o">.</span><span class="n">children</span><span class="p">]</span>

    <span class="k">def</span> <span class="fm">__getattr__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">item</span><span class="p">):</span>
        <span class="c1"># if attribute is not found on the element python object</span>
        <span class="c1"># check if it may be present in the element attributes (eg, href=, src=, alt=)</span>
        <span class="c1"># returns None when attribute is not found</span>
        <span class="c1"># instead of raising AttributeError</span>
        <span class="n">x</span> <span class="o">=</span> <span class="nb">getattr</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">attrs</span><span class="p">,</span> <span class="n">item</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">x</span><span class="p">:</span>
            <span class="k">return</span> <span class="n">x</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span><span class="s2">&quot;could not find attribute &#39;</span><span class="si">%s</span><span class="s2">&#39; in </span><span class="si">%s</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="p">(</span><span class="n">item</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">attrs</span><span class="p">))</span>

    <span class="k">def</span> <span class="fm">__setattr__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">key</span><span class="p">,</span> <span class="n">value</span><span class="p">):</span>
        <span class="k">if</span> <span class="n">key</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span> <span class="o">!=</span> <span class="s2">&quot;_&quot;</span><span class="p">:</span>
            <span class="k">if</span> <span class="n">key</span><span class="p">[</span><span class="mi">1</span><span class="p">:]</span> <span class="ow">not</span> <span class="ow">in</span> <span class="nb">vars</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span><span class="o">.</span><span class="n">keys</span><span class="p">():</span>
                <span class="c1"># we probably deal with an attribute of</span>
                <span class="c1"># the html element, so forward it</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">attrs</span><span class="o">.</span><span class="fm">__setattr__</span><span class="p">(</span><span class="n">key</span><span class="p">,</span> <span class="n">value</span><span class="p">)</span>
                <span class="k">return</span>
        <span class="c1"># we probably deal with an attribute of</span>
        <span class="c1"># the python object</span>
        <span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="fm">__setattr__</span><span class="p">(</span><span class="n">key</span><span class="p">,</span> <span class="n">value</span><span class="p">)</span>

    <span class="k">def</span> <span class="fm">__setitem__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">key</span><span class="p">,</span> <span class="n">value</span><span class="p">):</span>
        <span class="k">if</span> <span class="n">key</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span> <span class="o">!=</span> <span class="s2">&quot;_&quot;</span><span class="p">:</span>
            <span class="k">if</span> <span class="n">key</span><span class="p">[</span><span class="mi">1</span><span class="p">:]</span> <span class="ow">not</span> <span class="ow">in</span> <span class="nb">vars</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span><span class="o">.</span><span class="n">keys</span><span class="p">():</span>
                <span class="c1"># we probably deal with an attribute of</span>
                <span class="c1"># the html element, so forward it</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">attrs</span><span class="p">[</span><span class="n">key</span><span class="p">]</span> <span class="o">=</span> <span class="n">value</span>

    <span class="k">def</span> <span class="fm">__getitem__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">item</span><span class="p">):</span>
        <span class="c1"># we probably deal with an attribute of</span>
        <span class="c1"># the html element, so forward it</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">attrs</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="n">item</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span>

<div class="viewcode-block" id="Element.save_to_dom">
<a class="viewcode-back" href="../../../nodriver/classes/element.html#nodriver.Element.save_to_dom">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">save_to_dom</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        saves element to dom</span>
<span class="sd">        :return:</span>
<span class="sd">        :rtype:</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_remote_object</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">_tab</span><span class="o">.</span><span class="n">send</span><span class="p">(</span>
            <span class="n">cdp</span><span class="o">.</span><span class="n">dom</span><span class="o">.</span><span class="n">resolve_node</span><span class="p">(</span><span class="n">backend_node_id</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">backend_node_id</span><span class="p">)</span>
        <span class="p">)</span>
        <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">_tab</span><span class="o">.</span><span class="n">send</span><span class="p">(</span><span class="n">cdp</span><span class="o">.</span><span class="n">dom</span><span class="o">.</span><span class="n">set_outer_html</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">node_id</span><span class="p">,</span> <span class="n">outer_html</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="bp">self</span><span class="p">)))</span>
        <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">update</span><span class="p">()</span></div>


<div class="viewcode-block" id="Element.remove_from_dom">
<a class="viewcode-back" href="../../../nodriver/classes/element.html#nodriver.Element.remove_from_dom">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">remove_from_dom</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;removes the element from dom&quot;&quot;&quot;</span>
        <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">update</span><span class="p">()</span>  <span class="c1"># ensure we have latest node_id</span>
        <span class="n">node</span> <span class="o">=</span> <span class="n">util</span><span class="o">.</span><span class="n">filter_recurse</span><span class="p">(</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_tree</span><span class="p">,</span> <span class="k">lambda</span> <span class="n">node</span><span class="p">:</span> <span class="n">node</span><span class="o">.</span><span class="n">backend_node_id</span> <span class="o">==</span> <span class="bp">self</span><span class="o">.</span><span class="n">backend_node_id</span>
        <span class="p">)</span>
        <span class="k">if</span> <span class="n">node</span><span class="p">:</span>
            <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">tab</span><span class="o">.</span><span class="n">send</span><span class="p">(</span><span class="n">cdp</span><span class="o">.</span><span class="n">dom</span><span class="o">.</span><span class="n">remove_node</span><span class="p">(</span><span class="n">node</span><span class="o">.</span><span class="n">node_id</span><span class="p">))</span></div>

        <span class="c1"># self._tree = util.remove_from_tree(self.tree, self.node)</span>

<div class="viewcode-block" id="Element.update">
<a class="viewcode-back" href="../../../nodriver/classes/element.html#nodriver.Element.update">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">update</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">_node</span><span class="o">=</span><span class="kc">None</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        updates element to retrieve more properties. for example this enables</span>
<span class="sd">        :py:obj:`~children` and :py:obj:`~parent` attributes.</span>

<span class="sd">        also resolves js opbject which is stored object in :py:obj:`~remote_object`</span>

<span class="sd">        usually you will get element nodes by the usage of</span>

<span class="sd">        :py:meth:`Tab.query_selector_all()`</span>

<span class="sd">        :py:meth:`Tab.find_elements_by_text()`</span>

<span class="sd">        those elements are already updated and you can browse through children directly.</span>

<span class="sd">        The reason for a seperate call instead of doing it at initialization,</span>
<span class="sd">        is because when you are retrieving 100+ elements this becomes quite expensive.</span>

<span class="sd">        therefore, it is not advised to call this method on a bunch of blocks (100+) at the same time.</span>

<span class="sd">        :return:</span>
<span class="sd">        :rtype:</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="n">_node</span><span class="p">:</span>
            <span class="n">doc</span> <span class="o">=</span> <span class="n">_node</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_parent</span> <span class="o">=</span> <span class="kc">None</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">doc</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">_tab</span><span class="o">.</span><span class="n">send</span><span class="p">(</span><span class="n">cdp</span><span class="o">.</span><span class="n">dom</span><span class="o">.</span><span class="n">get_document</span><span class="p">(</span><span class="o">-</span><span class="mi">1</span><span class="p">,</span> <span class="kc">True</span><span class="p">))</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_parent</span> <span class="o">=</span> <span class="kc">None</span>
        <span class="n">updated_node</span> <span class="o">=</span> <span class="n">util</span><span class="o">.</span><span class="n">filter_recurse</span><span class="p">(</span>
            <span class="n">doc</span><span class="p">,</span> <span class="k">lambda</span> <span class="n">n</span><span class="p">:</span> <span class="n">n</span><span class="o">.</span><span class="n">backend_node_id</span> <span class="o">==</span> <span class="bp">self</span><span class="o">.</span><span class="n">_node</span><span class="o">.</span><span class="n">backend_node_id</span>
        <span class="p">)</span>
        <span class="k">if</span> <span class="n">updated_node</span><span class="p">:</span>
            <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span><span class="s2">&quot;node seems changed, and has now been updated.&quot;</span><span class="p">)</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_node</span> <span class="o">=</span> <span class="n">updated_node</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_tree</span> <span class="o">=</span> <span class="n">doc</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">_remote_object</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">_tab</span><span class="o">.</span><span class="n">send</span><span class="p">(</span>
            <span class="n">cdp</span><span class="o">.</span><span class="n">dom</span><span class="o">.</span><span class="n">resolve_node</span><span class="p">(</span><span class="n">backend_node_id</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">_node</span><span class="o">.</span><span class="n">backend_node_id</span><span class="p">)</span>
        <span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">attrs</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_make_attrs</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">node_name</span> <span class="o">!=</span> <span class="s2">&quot;IFRAME&quot;</span><span class="p">:</span>
            <span class="n">parent_node</span> <span class="o">=</span> <span class="n">util</span><span class="o">.</span><span class="n">filter_recurse</span><span class="p">(</span>
                <span class="n">doc</span><span class="p">,</span> <span class="k">lambda</span> <span class="n">n</span><span class="p">:</span> <span class="n">n</span><span class="o">.</span><span class="n">node_id</span> <span class="o">==</span> <span class="bp">self</span><span class="o">.</span><span class="n">node</span><span class="o">.</span><span class="n">parent_id</span>
            <span class="p">)</span>
            <span class="k">if</span> <span class="ow">not</span> <span class="n">parent_node</span><span class="p">:</span>
                <span class="c1"># could happen if node is for example &lt;html&gt;</span>
                <span class="k">return</span> <span class="bp">self</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_parent</span> <span class="o">=</span> <span class="n">create</span><span class="p">(</span><span class="n">parent_node</span><span class="p">,</span> <span class="n">tab</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">_tab</span><span class="p">,</span> <span class="n">tree</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">_tree</span><span class="p">)</span>
        <span class="k">return</span> <span class="bp">self</span></div>


    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">node</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_node</span>

    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">tree</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">cdp</span><span class="o">.</span><span class="n">dom</span><span class="o">.</span><span class="n">Node</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_tree</span>
        <span class="c1"># raise RuntimeError(&quot;you should first call  `await update()` on this object to populate it&#39;s tree&quot;)</span>

    <span class="nd">@tree</span><span class="o">.</span><span class="n">setter</span>
    <span class="k">def</span> <span class="nf">tree</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">tree</span><span class="p">:</span> <span class="n">cdp</span><span class="o">.</span><span class="n">dom</span><span class="o">.</span><span class="n">Node</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_tree</span> <span class="o">=</span> <span class="n">tree</span>

    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">attrs</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        attributes are stored here, however, you can set them directly on the element object as well.</span>
<span class="sd">        :return:</span>
<span class="sd">        :rtype:</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_attrs</span>

    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">parent</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Union</span><span class="p">[</span><span class="n">Element</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        get the parent element (node) of current element(node)</span>
<span class="sd">        :return:</span>
<span class="sd">        :rtype:</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">tree</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">RuntimeError</span><span class="p">(</span><span class="s2">&quot;could not get parent since the element has no tree set&quot;</span><span class="p">)</span>
        <span class="n">parent_node</span> <span class="o">=</span> <span class="n">util</span><span class="o">.</span><span class="n">filter_recurse</span><span class="p">(</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">tree</span><span class="p">,</span> <span class="k">lambda</span> <span class="n">n</span><span class="p">:</span> <span class="n">n</span><span class="o">.</span><span class="n">node_id</span> <span class="o">==</span> <span class="bp">self</span><span class="o">.</span><span class="n">parent_id</span>
        <span class="p">)</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="n">parent_node</span><span class="p">:</span>
            <span class="k">return</span> <span class="kc">None</span>
        <span class="n">parent_element</span> <span class="o">=</span> <span class="n">create</span><span class="p">(</span><span class="n">parent_node</span><span class="p">,</span> <span class="n">tab</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">_tab</span><span class="p">,</span> <span class="n">tree</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">tree</span><span class="p">)</span>
        <span class="k">return</span> <span class="n">parent_element</span>

    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">children</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Union</span><span class="p">[</span><span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">Element</span><span class="p">],</span> <span class="nb">str</span><span class="p">]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        returns the elements&#39; children. those children also have a children property</span>
<span class="sd">        so you can browse through the entire tree as well.</span>
<span class="sd">        :return:</span>
<span class="sd">        :rtype:</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">_children</span> <span class="o">=</span> <span class="p">[]</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">_node</span><span class="o">.</span><span class="n">node_name</span> <span class="o">==</span> <span class="s2">&quot;IFRAME&quot;</span><span class="p">:</span>
            <span class="c1"># iframes are not exact the same as other nodes</span>
            <span class="c1"># the children of iframes are found under</span>
            <span class="c1"># the .content_document property, which is of more</span>
            <span class="c1"># use than the node itself</span>
            <span class="n">frame</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_node</span><span class="o">.</span><span class="n">content_document</span>
            <span class="k">if</span> <span class="ow">not</span> <span class="n">frame</span><span class="o">.</span><span class="n">child_node_count</span><span class="p">:</span>
                <span class="k">return</span> <span class="p">[]</span>
            <span class="k">for</span> <span class="n">child</span> <span class="ow">in</span> <span class="n">frame</span><span class="o">.</span><span class="n">children</span><span class="p">:</span>
                <span class="n">child_elem</span> <span class="o">=</span> <span class="n">create</span><span class="p">(</span><span class="n">child</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">_tab</span><span class="p">,</span> <span class="n">frame</span><span class="p">)</span>
                <span class="k">if</span> <span class="n">child_elem</span><span class="p">:</span>
                    <span class="n">_children</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">child_elem</span><span class="p">)</span>
            <span class="c1"># self._node = frame</span>
            <span class="k">return</span> <span class="n">_children</span>
        <span class="k">elif</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">node</span><span class="o">.</span><span class="n">child_node_count</span><span class="p">:</span>
            <span class="k">return</span> <span class="p">[]</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">node</span><span class="o">.</span><span class="n">children</span><span class="p">:</span>
            <span class="k">for</span> <span class="n">child</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">node</span><span class="o">.</span><span class="n">children</span><span class="p">:</span>
                <span class="n">child_elem</span> <span class="o">=</span> <span class="n">create</span><span class="p">(</span><span class="n">child</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">_tab</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">tree</span><span class="p">)</span>
                <span class="k">if</span> <span class="n">child_elem</span><span class="p">:</span>
                    <span class="n">_children</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">child_elem</span><span class="p">)</span>
        <span class="k">return</span> <span class="n">_children</span>

    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">remote_object</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">cdp</span><span class="o">.</span><span class="n">runtime</span><span class="o">.</span><span class="n">RemoteObject</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_remote_object</span>

    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">object_id</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">cdp</span><span class="o">.</span><span class="n">runtime</span><span class="o">.</span><span class="n">RemoteObjectId</span><span class="p">:</span>
        <span class="k">try</span><span class="p">:</span>
            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">remote_object</span><span class="o">.</span><span class="n">object_id</span>
        <span class="k">except</span> <span class="ne">AttributeError</span><span class="p">:</span>
            <span class="k">pass</span>

<div class="viewcode-block" id="Element.click">
<a class="viewcode-back" href="../../../nodriver/classes/element.html#nodriver.Element.click">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">click</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Click the element.</span>

<span class="sd">        :return:</span>
<span class="sd">        :rtype:</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_remote_object</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">_tab</span><span class="o">.</span><span class="n">send</span><span class="p">(</span>
            <span class="n">cdp</span><span class="o">.</span><span class="n">dom</span><span class="o">.</span><span class="n">resolve_node</span><span class="p">(</span><span class="n">backend_node_id</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">backend_node_id</span><span class="p">)</span>
        <span class="p">)</span>
        <span class="n">arguments</span> <span class="o">=</span> <span class="p">[</span><span class="n">cdp</span><span class="o">.</span><span class="n">runtime</span><span class="o">.</span><span class="n">CallArgument</span><span class="p">(</span><span class="n">object_id</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">_remote_object</span><span class="o">.</span><span class="n">object_id</span><span class="p">)]</span>
        <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">flash</span><span class="p">(</span><span class="mf">0.25</span><span class="p">)</span>
        <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">_tab</span><span class="o">.</span><span class="n">send</span><span class="p">(</span>
            <span class="n">cdp</span><span class="o">.</span><span class="n">runtime</span><span class="o">.</span><span class="n">call_function_on</span><span class="p">(</span>
                <span class="s2">&quot;(el) =&gt; el.click()&quot;</span><span class="p">,</span>
                <span class="n">object_id</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">_remote_object</span><span class="o">.</span><span class="n">object_id</span><span class="p">,</span>
                <span class="n">arguments</span><span class="o">=</span><span class="n">arguments</span><span class="p">,</span>
                <span class="n">await_promise</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
                <span class="n">user_gesture</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
                <span class="n">return_by_value</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
            <span class="p">)</span>
        <span class="p">)</span></div>


<div class="viewcode-block" id="Element.get_js_attributes">
<a class="viewcode-back" href="../../../nodriver/classes/element.html#nodriver.Element.get_js_attributes">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">get_js_attributes</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="n">ContraDict</span><span class="p">(</span>
            <span class="n">json</span><span class="o">.</span><span class="n">loads</span><span class="p">(</span>
                <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">apply</span><span class="p">(</span>
<span class="w">                    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">            function (e) {</span>
<span class="sd">                let o = {}</span>
<span class="sd">                for(let k in e){</span>
<span class="sd">                    o[k] = e[k]</span>
<span class="sd">                }</span>
<span class="sd">                return JSON.stringify(o)</span>
<span class="sd">            }</span>
<span class="sd">            &quot;&quot;&quot;</span>
                <span class="p">)</span>
            <span class="p">)</span>
        <span class="p">)</span></div>


    <span class="k">def</span> <span class="fm">__await__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">update</span><span class="p">()</span><span class="o">.</span><span class="fm">__await__</span><span class="p">()</span>

    <span class="k">def</span> <span class="fm">__call__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">js_method</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        calling the element object will call a js method on the object</span>
<span class="sd">        eg, element.play() in case of a video element, it will call .play()</span>
<span class="sd">        :param js_method:</span>
<span class="sd">        :type js_method:</span>
<span class="sd">        :return:</span>
<span class="sd">        :rtype:</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">apply</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;(e) =&gt; e[&#39;</span><span class="si">{</span><span class="n">js_method</span><span class="si">}</span><span class="s2">&#39;]()&quot;</span><span class="p">)</span>

<div class="viewcode-block" id="Element.apply">
<a class="viewcode-back" href="../../../nodriver/classes/element.html#nodriver.Element.apply">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">apply</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">js_function</span><span class="p">,</span> <span class="n">return_by_value</span><span class="o">=</span><span class="kc">True</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        apply javascript to this element. the given js_function string should accept the js element as parameter,</span>
<span class="sd">        and can be a arrow function, or function declaration.</span>
<span class="sd">        eg:</span>
<span class="sd">            - &#39;(elem) =&gt; { elem.value = &quot;blabla&quot;; consolelog(elem); alert(JSON.stringify(elem); } &#39;</span>
<span class="sd">            - &#39;elem =&gt; elem.play()&#39;</span>
<span class="sd">            - function myFunction(elem) { alert(elem) }</span>

<span class="sd">        :param js_function: the js function definition which received this element.</span>
<span class="sd">        :type js_function: str</span>
<span class="sd">        :param return_by_value:</span>
<span class="sd">        :type return_by_value:</span>
<span class="sd">        :return:</span>
<span class="sd">        :rtype:</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_remote_object</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">_tab</span><span class="o">.</span><span class="n">send</span><span class="p">(</span>
            <span class="n">cdp</span><span class="o">.</span><span class="n">dom</span><span class="o">.</span><span class="n">resolve_node</span><span class="p">(</span><span class="n">backend_node_id</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">backend_node_id</span><span class="p">)</span>
        <span class="p">)</span>
        <span class="n">result</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Tuple</span><span class="p">[</span><span class="n">cdp</span><span class="o">.</span><span class="n">runtime</span><span class="o">.</span><span class="n">RemoteObject</span><span class="p">,</span> <span class="n">typing</span><span class="o">.</span><span class="n">Any</span><span class="p">]</span> <span class="o">=</span> <span class="p">(</span>
            <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">_tab</span><span class="o">.</span><span class="n">send</span><span class="p">(</span>
                <span class="n">cdp</span><span class="o">.</span><span class="n">runtime</span><span class="o">.</span><span class="n">call_function_on</span><span class="p">(</span>
                    <span class="n">js_function</span><span class="p">,</span>
                    <span class="n">object_id</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">_remote_object</span><span class="o">.</span><span class="n">object_id</span><span class="p">,</span>
                    <span class="n">arguments</span><span class="o">=</span><span class="p">[</span>
                        <span class="n">cdp</span><span class="o">.</span><span class="n">runtime</span><span class="o">.</span><span class="n">CallArgument</span><span class="p">(</span>
                            <span class="n">object_id</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">_remote_object</span><span class="o">.</span><span class="n">object_id</span>
                        <span class="p">)</span>
                    <span class="p">],</span>
                    <span class="n">return_by_value</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
                    <span class="n">user_gesture</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
                <span class="p">)</span>
            <span class="p">)</span>
        <span class="p">)</span>
        <span class="k">if</span> <span class="n">result</span> <span class="ow">and</span> <span class="n">result</span><span class="p">[</span><span class="mi">0</span><span class="p">]:</span>
            <span class="k">if</span> <span class="n">return_by_value</span><span class="p">:</span>
                <span class="k">return</span> <span class="n">result</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span><span class="o">.</span><span class="n">value</span>
            <span class="k">return</span> <span class="n">result</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span>
        <span class="k">elif</span> <span class="n">result</span><span class="p">[</span><span class="mi">1</span><span class="p">]:</span>
            <span class="k">return</span> <span class="n">result</span><span class="p">[</span><span class="mi">1</span><span class="p">]</span></div>


<div class="viewcode-block" id="Element.get_position">
<a class="viewcode-back" href="../../../nodriver/classes/element.html#nodriver.Element.get_position">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">get_position</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="nb">abs</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Position</span><span class="p">:</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">parent</span> <span class="ow">or</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">object_id</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_remote_object</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">_tab</span><span class="o">.</span><span class="n">send</span><span class="p">(</span>
                <span class="n">cdp</span><span class="o">.</span><span class="n">dom</span><span class="o">.</span><span class="n">resolve_node</span><span class="p">(</span><span class="n">backend_node_id</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">backend_node_id</span><span class="p">)</span>
            <span class="p">)</span>
            <span class="c1"># await self.update()</span>
        <span class="k">try</span><span class="p">:</span>
            <span class="n">quads</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">tab</span><span class="o">.</span><span class="n">send</span><span class="p">(</span>
                <span class="n">cdp</span><span class="o">.</span><span class="n">dom</span><span class="o">.</span><span class="n">get_content_quads</span><span class="p">(</span><span class="n">object_id</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">remote_object</span><span class="o">.</span><span class="n">object_id</span><span class="p">)</span>
            <span class="p">)</span>
            <span class="k">if</span> <span class="ow">not</span> <span class="n">quads</span><span class="p">:</span>
                <span class="k">raise</span> <span class="ne">Exception</span><span class="p">(</span><span class="s2">&quot;could not find position for </span><span class="si">%s</span><span class="s2"> &quot;</span> <span class="o">%</span> <span class="bp">self</span><span class="p">)</span>
            <span class="n">pos</span> <span class="o">=</span> <span class="n">Position</span><span class="p">(</span><span class="n">quads</span><span class="p">[</span><span class="mi">0</span><span class="p">])</span>
            <span class="k">if</span> <span class="nb">abs</span><span class="p">:</span>
                <span class="n">scroll_y</span> <span class="o">=</span> <span class="p">(</span><span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">tab</span><span class="o">.</span><span class="n">evaluate</span><span class="p">(</span><span class="s2">&quot;window.scrollY&quot;</span><span class="p">))</span><span class="o">.</span><span class="n">value</span>
                <span class="n">scroll_x</span> <span class="o">=</span> <span class="p">(</span><span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">tab</span><span class="o">.</span><span class="n">evaluate</span><span class="p">(</span><span class="s2">&quot;window.scrollX&quot;</span><span class="p">))</span><span class="o">.</span><span class="n">value</span>
                <span class="n">abs_x</span> <span class="o">=</span> <span class="n">pos</span><span class="o">.</span><span class="n">left</span> <span class="o">+</span> <span class="n">scroll_x</span> <span class="o">+</span> <span class="p">(</span><span class="n">pos</span><span class="o">.</span><span class="n">width</span> <span class="o">/</span> <span class="mi">2</span><span class="p">)</span>
                <span class="n">abs_y</span> <span class="o">=</span> <span class="n">pos</span><span class="o">.</span><span class="n">top</span> <span class="o">+</span> <span class="n">scroll_y</span> <span class="o">+</span> <span class="p">(</span><span class="n">pos</span><span class="o">.</span><span class="n">height</span> <span class="o">/</span> <span class="mi">2</span><span class="p">)</span>
                <span class="n">pos</span><span class="o">.</span><span class="n">abs_x</span> <span class="o">=</span> <span class="n">abs_x</span>
                <span class="n">pos</span><span class="o">.</span><span class="n">abs_y</span> <span class="o">=</span> <span class="n">abs_y</span>
            <span class="k">return</span> <span class="n">pos</span>
        <span class="k">except</span> <span class="ne">IndexError</span><span class="p">:</span>
            <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span>
                <span class="s2">&quot;no content quads for </span><span class="si">%s</span><span class="s2">. mostly caused by element which is not &#39;in plain sight&#39;&quot;</span>
                <span class="o">%</span> <span class="bp">self</span>
            <span class="p">)</span></div>


<div class="viewcode-block" id="Element.mouse_click">
<a class="viewcode-back" href="../../../nodriver/classes/element.html#nodriver.Element.mouse_click">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">mouse_click</span><span class="p">(</span>
        <span class="bp">self</span><span class="p">,</span>
        <span class="n">button</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="s2">&quot;left&quot;</span><span class="p">,</span>
        <span class="n">buttons</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">int</span><span class="p">]</span> <span class="o">=</span> <span class="mi">1</span><span class="p">,</span>
        <span class="n">modifiers</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">int</span><span class="p">]</span> <span class="o">=</span> <span class="mi">0</span><span class="p">,</span>
        <span class="n">_until_event</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">type</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
    <span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;native click (on element) . note: this likely does not work atm, use click() instead</span>

<span class="sd">        :param button: str (default = &quot;left&quot;)</span>
<span class="sd">        :param buttons: which button (default 1 = left)</span>
<span class="sd">        :param modifiers: *(Optional)* Bit field representing pressed modifier keys.</span>
<span class="sd">                Alt=1, Ctrl=2, Meta/Command=4, Shift=8 (default: 0).</span>
<span class="sd">        :param _until_event: internal. event to wait for before returning</span>
<span class="sd">        :return:</span>

<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">try</span><span class="p">:</span>
            <span class="n">center</span> <span class="o">=</span> <span class="p">(</span><span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_position</span><span class="p">())</span><span class="o">.</span><span class="n">center</span>
        <span class="k">except</span> <span class="ne">AttributeError</span><span class="p">:</span>
            <span class="k">return</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="n">center</span><span class="p">:</span>
            <span class="n">logger</span><span class="o">.</span><span class="n">warning</span><span class="p">(</span><span class="s2">&quot;could not calculate box model for </span><span class="si">%s</span><span class="s2">&quot;</span><span class="p">,</span> <span class="bp">self</span><span class="p">)</span>
            <span class="k">return</span>

        <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span><span class="s2">&quot;clicking on location </span><span class="si">%.2f</span><span class="s2">, </span><span class="si">%.2f</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="n">center</span><span class="p">)</span>

        <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">_tab</span><span class="o">.</span><span class="n">mouse_click</span><span class="p">(</span><span class="n">center</span><span class="p">[</span><span class="mi">0</span><span class="p">],</span> <span class="n">center</span><span class="p">[</span><span class="mi">1</span><span class="p">])</span>
        <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">_tab</span><span class="o">.</span><span class="n">flash_point</span><span class="p">(</span><span class="n">center</span><span class="p">[</span><span class="mi">0</span><span class="p">],</span> <span class="n">center</span><span class="p">[</span><span class="mi">1</span><span class="p">])</span></div>


        <span class="c1"># await asyncio.gather(</span>
        <span class="c1">#     self._tab.send(</span>
        <span class="c1">#         cdp.input_.dispatch_mouse_event(</span>
        <span class="c1">#             &quot;mousePressed&quot;,</span>
        <span class="c1">#             x=center[0],</span>
        <span class="c1">#             y=center[1],</span>
        <span class="c1">#             modifiers=modifiers,</span>
        <span class="c1">#             button=cdp.input_.MouseButton(button),</span>
        <span class="c1">#             buttons=buttons,</span>
        <span class="c1">#             click_count=1,</span>
        <span class="c1">#         )</span>
        <span class="c1">#     ),</span>
        <span class="c1">#     self._tab.send(</span>
        <span class="c1">#         cdp.input_.dispatch_mouse_event(</span>
        <span class="c1">#             &quot;mouseReleased&quot;,</span>
        <span class="c1">#             x=center[0],</span>
        <span class="c1">#             y=center[1],</span>
        <span class="c1">#             modifiers=modifiers,</span>
        <span class="c1">#             button=cdp.input_.MouseButton(button),</span>
        <span class="c1">#             buttons=buttons,</span>
        <span class="c1">#             click_count=1,</span>
        <span class="c1">#         )</span>
        <span class="c1">#     ),</span>
        <span class="c1"># )</span>
        <span class="c1"># try:</span>
        <span class="c1">#     await self.flash()</span>
        <span class="c1"># except:  # noqa</span>
        <span class="c1">#     pass</span>

    <span class="n">click_mouse</span> <span class="o">=</span> <span class="n">mouse_click</span>

<div class="viewcode-block" id="Element.mouse_move">
<a class="viewcode-back" href="../../../nodriver/classes/element.html#nodriver.Element.mouse_move">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">mouse_move</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;moves mouse (not click), to element position. when an element has an</span>
<span class="sd">        hover/mouseover effect, this would trigger it&quot;&quot;&quot;</span>
        <span class="k">try</span><span class="p">:</span>
            <span class="n">center</span> <span class="o">=</span> <span class="p">(</span><span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_position</span><span class="p">())</span><span class="o">.</span><span class="n">center</span>
        <span class="k">except</span> <span class="ne">AttributeError</span><span class="p">:</span>
            <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span><span class="s2">&quot;did not find location for </span><span class="si">%s</span><span class="s2">&quot;</span><span class="p">,</span> <span class="bp">self</span><span class="p">)</span>
            <span class="k">return</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span>
            <span class="s2">&quot;mouse move to location </span><span class="si">%.2f</span><span class="s2">, </span><span class="si">%.2f</span><span class="s2"> where </span><span class="si">%s</span><span class="s2"> is located&quot;</span><span class="p">,</span> <span class="o">*</span><span class="n">center</span><span class="p">,</span> <span class="bp">self</span>
        <span class="p">)</span>
        <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">_tab</span><span class="o">.</span><span class="n">mouse_move</span><span class="p">(</span><span class="n">center</span><span class="p">[</span><span class="mi">0</span><span class="p">],</span> <span class="n">center</span><span class="p">[</span><span class="mi">1</span><span class="p">])</span></div>


        <span class="c1">#     cdp.input_.dispatch_mouse_event(&quot;mouseMoved&quot;, x=center[0], y=center[1])</span>
        <span class="c1"># )</span>
        <span class="c1"># await self._tab.sleep(0.05)</span>
        <span class="c1"># await self._tab.send(</span>
        <span class="c1">#     cdp.input_.dispatch_mouse_event(&quot;mouseReleased&quot;, x=center[0], y=center[1])</span>
        <span class="c1"># )</span>

<div class="viewcode-block" id="Element.mouse_drag">
<a class="viewcode-back" href="../../../nodriver/classes/element.html#nodriver.Element.mouse_drag">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">mouse_drag</span><span class="p">(</span>
        <span class="bp">self</span><span class="p">,</span>
        <span class="n">destination</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Union</span><span class="p">[</span><span class="n">Element</span><span class="p">,</span> <span class="n">typing</span><span class="o">.</span><span class="n">Tuple</span><span class="p">[</span><span class="nb">int</span><span class="p">,</span> <span class="nb">int</span><span class="p">]],</span>
        <span class="n">relative</span><span class="p">:</span> <span class="nb">bool</span> <span class="o">=</span> <span class="kc">False</span><span class="p">,</span>
        <span class="n">steps</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="mi">1</span><span class="p">,</span>
    <span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        drag an element to another element or target coordinates. dragging of elements should be supported  by the site of course</span>


<span class="sd">        :param destination: another element where to drag to, or a tuple (x,y) of ints representing coordinate</span>
<span class="sd">        :type destination: Element or coordinate as x,y tuple</span>

<span class="sd">        :param relative: when True, treats coordinate as relative. for example (-100, 200) will move left 100px and down 200px</span>
<span class="sd">        :type relative:</span>

<span class="sd">        :param steps: move in &lt;steps&gt; points, this could make it look more &quot;natural&quot; (default 1),</span>
<span class="sd">               but also a lot slower.</span>
<span class="sd">               for very smooth action use 50-100</span>
<span class="sd">        :type steps: int</span>
<span class="sd">        :return:</span>
<span class="sd">        :rtype:</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">try</span><span class="p">:</span>
            <span class="n">start_point</span> <span class="o">=</span> <span class="p">(</span><span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_position</span><span class="p">())</span><span class="o">.</span><span class="n">center</span>
        <span class="k">except</span> <span class="ne">AttributeError</span><span class="p">:</span>
            <span class="k">return</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="n">start_point</span><span class="p">:</span>
            <span class="n">logger</span><span class="o">.</span><span class="n">warning</span><span class="p">(</span><span class="s2">&quot;could not calculate box model for </span><span class="si">%s</span><span class="s2">&quot;</span><span class="p">,</span> <span class="bp">self</span><span class="p">)</span>
            <span class="k">return</span>
        <span class="n">end_point</span> <span class="o">=</span> <span class="kc">None</span>
        <span class="k">if</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">destination</span><span class="p">,</span> <span class="n">Element</span><span class="p">):</span>
            <span class="k">try</span><span class="p">:</span>
                <span class="n">end_point</span> <span class="o">=</span> <span class="p">(</span><span class="k">await</span> <span class="n">destination</span><span class="o">.</span><span class="n">get_position</span><span class="p">())</span><span class="o">.</span><span class="n">center</span>
            <span class="k">except</span> <span class="ne">AttributeError</span><span class="p">:</span>
                <span class="k">return</span>
            <span class="k">if</span> <span class="ow">not</span> <span class="n">end_point</span><span class="p">:</span>
                <span class="n">logger</span><span class="o">.</span><span class="n">warning</span><span class="p">(</span><span class="s2">&quot;could not calculate box model for </span><span class="si">%s</span><span class="s2">&quot;</span><span class="p">,</span> <span class="n">destination</span><span class="p">)</span>
                <span class="k">return</span>
        <span class="k">elif</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">destination</span><span class="p">,</span> <span class="p">(</span><span class="nb">tuple</span><span class="p">,</span> <span class="nb">list</span><span class="p">)):</span>
            <span class="k">if</span> <span class="n">relative</span><span class="p">:</span>
                <span class="n">end_point</span> <span class="o">=</span> <span class="p">(</span>
                    <span class="n">start_point</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span> <span class="o">+</span> <span class="n">destination</span><span class="p">[</span><span class="mi">0</span><span class="p">],</span>
                    <span class="n">start_point</span><span class="p">[</span><span class="mi">1</span><span class="p">]</span> <span class="o">+</span> <span class="n">destination</span><span class="p">[</span><span class="mi">1</span><span class="p">],</span>
                <span class="p">)</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="n">end_point</span> <span class="o">=</span> <span class="n">destination</span>
        <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">_tab</span><span class="o">.</span><span class="n">mouse_drag</span><span class="p">(</span>
            <span class="n">start_point</span><span class="p">,</span> <span class="n">end_point</span><span class="p">,</span> <span class="n">relative</span><span class="o">=</span><span class="n">relative</span><span class="p">,</span> <span class="n">steps</span><span class="o">=</span><span class="n">steps</span>
        <span class="p">)</span></div>

        <span class="c1"># await self._tab.send(</span>
        <span class="c1">#     cdp.input_.dispatch_mouse_event(</span>
        <span class="c1">#         &quot;mousePressed&quot;,</span>
        <span class="c1">#         x=start_point[0],</span>
        <span class="c1">#         y=start_point[1],</span>
        <span class="c1">#         button=cdp.input_.MouseButton(&quot;left&quot;),</span>
        <span class="c1">#     )</span>
        <span class="c1"># )</span>
        <span class="c1">#</span>
        <span class="c1"># steps = 1 if (not steps or steps &lt; 1) else steps</span>
        <span class="c1"># if steps == 1:</span>
        <span class="c1">#     await self._tab.send(</span>
        <span class="c1">#         cdp.input_.dispatch_mouse_event(</span>
        <span class="c1">#             &quot;mouseMoved&quot;,</span>
        <span class="c1">#             x=end_point[0],</span>
        <span class="c1">#             y=end_point[1],</span>
        <span class="c1">#         )</span>
        <span class="c1">#     )</span>
        <span class="c1"># elif steps &gt; 1:</span>
        <span class="c1">#     # probably the worst waay of calculating this. but couldn&#39;t think of a better solution today.</span>
        <span class="c1">#     step_size_x = (end_point[0] - start_point[0]) / steps</span>
        <span class="c1">#     step_size_y = (end_point[1] - start_point[1]) / steps</span>
        <span class="c1">#     pathway = [</span>
        <span class="c1">#         (start_point[0] + step_size_x * i, start_point[1] + step_size_y * i)</span>
        <span class="c1">#         for i in range(steps + 1)</span>
        <span class="c1">#     ]</span>
        <span class="c1">#</span>
        <span class="c1">#     for point in pathway:</span>
        <span class="c1">#         await self._tab.send(</span>
        <span class="c1">#             cdp.input_.dispatch_mouse_event(</span>
        <span class="c1">#                 &quot;mouseMoved&quot;,</span>
        <span class="c1">#                 x=point[0],</span>
        <span class="c1">#                 y=point[1],</span>
        <span class="c1">#             )</span>
        <span class="c1">#         )</span>
        <span class="c1">#         await asyncio.sleep(0)</span>
        <span class="c1">#</span>
        <span class="c1"># await self._tab.send(</span>
        <span class="c1">#     cdp.input_.dispatch_mouse_event(</span>
        <span class="c1">#         type_=&quot;mouseReleased&quot;,</span>
        <span class="c1">#         x=end_point[0],</span>
        <span class="c1">#         y=end_point[1],</span>
        <span class="c1">#         button=cdp.input_.MouseButton(&quot;left&quot;),</span>
        <span class="c1">#     )</span>
        <span class="c1"># )</span>

<div class="viewcode-block" id="Element.scroll_into_view">
<a class="viewcode-back" href="../../../nodriver/classes/element.html#nodriver.Element.scroll_into_view">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">scroll_into_view</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;scrolls element into view&quot;&quot;&quot;</span>
        <span class="k">try</span><span class="p">:</span>
            <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">tab</span><span class="o">.</span><span class="n">send</span><span class="p">(</span>
                <span class="n">cdp</span><span class="o">.</span><span class="n">dom</span><span class="o">.</span><span class="n">scroll_into_view_if_needed</span><span class="p">(</span><span class="n">backend_node_id</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">backend_node_id</span><span class="p">)</span>
            <span class="p">)</span>
        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
            <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span><span class="s2">&quot;could not scroll into view: </span><span class="si">%s</span><span class="s2">&quot;</span><span class="p">,</span> <span class="n">e</span><span class="p">)</span>
            <span class="k">return</span></div>


        <span class="c1"># await self.apply(&quot;&quot;&quot;(el) =&gt; el.scrollIntoView(false)&quot;&quot;&quot;)</span>

<div class="viewcode-block" id="Element.clear_input">
<a class="viewcode-back" href="../../../nodriver/classes/element.html#nodriver.Element.clear_input">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">clear_input</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">_until_event</span><span class="p">:</span> <span class="nb">type</span> <span class="o">=</span> <span class="kc">None</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;clears an input field&quot;&quot;&quot;</span>
        <span class="k">return</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">apply</span><span class="p">(</span><span class="s1">&#39;function (element) { element.value = &quot;&quot; } &#39;</span><span class="p">)</span></div>


<div class="viewcode-block" id="Element.send_keys">
<a class="viewcode-back" href="../../../nodriver/classes/element.html#nodriver.Element.send_keys">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">send_keys</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">text</span><span class="p">:</span> <span class="nb">str</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        send text to an input field, or any other html element.</span>

<span class="sd">        hint, if you ever get stuck where using py:meth:`~click`</span>
<span class="sd">        does not work, sending the keystroke \\n or \\r\\n or a spacebar work wonders!</span>

<span class="sd">        :param text: text to send</span>
<span class="sd">        :return: None</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">apply</span><span class="p">(</span><span class="s2">&quot;(elem) =&gt; elem.focus()&quot;</span><span class="p">)</span>
        <span class="p">[</span>
            <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">_tab</span><span class="o">.</span><span class="n">send</span><span class="p">(</span><span class="n">cdp</span><span class="o">.</span><span class="n">input_</span><span class="o">.</span><span class="n">dispatch_key_event</span><span class="p">(</span><span class="s2">&quot;char&quot;</span><span class="p">,</span> <span class="n">text</span><span class="o">=</span><span class="n">char</span><span class="p">))</span>
            <span class="k">for</span> <span class="n">char</span> <span class="ow">in</span> <span class="nb">list</span><span class="p">(</span><span class="n">text</span><span class="p">)</span>
        <span class="p">]</span></div>


<div class="viewcode-block" id="Element.send_file">
<a class="viewcode-back" href="../../../nodriver/classes/element.html#nodriver.Element.send_file">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">send_file</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="o">*</span><span class="n">file_paths</span><span class="p">:</span> <span class="n">PathLike</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        some form input require a file (upload), a full path needs to be provided.</span>
<span class="sd">        this method sends 1 or more file(s) to the input field.</span>

<span class="sd">        needles to say, but make sure the field accepts multiple files if you want to send more files.</span>
<span class="sd">        otherwise the browser might crash.</span>

<span class="sd">        example :</span>
<span class="sd">        `await fileinputElement.send_file(&#39;c:/temp/image.png&#39;, &#39;c:/users/<USER>/lol.gif&#39;)`</span>

<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">file_paths</span> <span class="o">=</span> <span class="p">[</span><span class="nb">str</span><span class="p">(</span><span class="n">p</span><span class="p">)</span> <span class="k">for</span> <span class="n">p</span> <span class="ow">in</span> <span class="n">file_paths</span><span class="p">]</span>
        <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">_tab</span><span class="o">.</span><span class="n">send</span><span class="p">(</span>
            <span class="n">cdp</span><span class="o">.</span><span class="n">dom</span><span class="o">.</span><span class="n">set_file_input_files</span><span class="p">(</span>
                <span class="n">files</span><span class="o">=</span><span class="p">[</span><span class="o">*</span><span class="n">file_paths</span><span class="p">],</span>
                <span class="n">backend_node_id</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">backend_node_id</span><span class="p">,</span>
                <span class="n">object_id</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">object_id</span><span class="p">,</span>
            <span class="p">)</span>
        <span class="p">)</span></div>


<div class="viewcode-block" id="Element.focus">
<a class="viewcode-back" href="../../../nodriver/classes/element.html#nodriver.Element.focus">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">focus</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;focus the current element. often useful in form (select) fields&quot;&quot;&quot;</span>
        <span class="k">return</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">apply</span><span class="p">(</span><span class="s2">&quot;(element) =&gt; element.focus()&quot;</span><span class="p">)</span></div>


<div class="viewcode-block" id="Element.select_option">
<a class="viewcode-back" href="../../../nodriver/classes/element.html#nodriver.Element.select_option">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">select_option</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        for form (select) fields. when you have queried the options you can call this method on the option object.</span>
<span class="sd">        02/08/2024: fixed the problem where events are not fired when programattically selecting an option.</span>

<span class="sd">        calling :func:`option.select_option()` will use that option as selected value.</span>
<span class="sd">        does not work in all cases.</span>

<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">node_name</span> <span class="o">==</span> <span class="s2">&quot;OPTION&quot;</span><span class="p">:</span>
            <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">apply</span><span class="p">(</span>
<span class="w">                </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">                (o) =&gt; {  </span>
<span class="sd">                    o.selected = true ; </span>
<span class="sd">                    o.dispatchEvent(new Event(&#39;change&#39;, {view: window,bubbles: true}))</span>
<span class="sd">                }</span>
<span class="sd">                &quot;&quot;&quot;</span>
            <span class="p">)</span></div>


<div class="viewcode-block" id="Element.set_value">
<a class="viewcode-back" href="../../../nodriver/classes/element.html#nodriver.Element.set_value">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">set_value</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">value</span><span class="p">):</span>
        <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">_tab</span><span class="o">.</span><span class="n">send</span><span class="p">(</span><span class="n">cdp</span><span class="o">.</span><span class="n">dom</span><span class="o">.</span><span class="n">set_node_value</span><span class="p">(</span><span class="n">node_id</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">node_id</span><span class="p">,</span> <span class="n">value</span><span class="o">=</span><span class="n">value</span><span class="p">))</span></div>


<div class="viewcode-block" id="Element.set_text">
<a class="viewcode-back" href="../../../nodriver/classes/element.html#nodriver.Element.set_text">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">set_text</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">value</span><span class="p">):</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">node_type</span> <span class="o">==</span> <span class="mi">3</span><span class="p">:</span>
            <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">child_node_count</span> <span class="o">==</span> <span class="mi">1</span><span class="p">:</span>
                <span class="n">child_node</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">children</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span>
                <span class="k">await</span> <span class="n">child_node</span><span class="o">.</span><span class="n">set_text</span><span class="p">(</span><span class="n">value</span><span class="p">)</span>
                <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">update</span><span class="p">()</span>
                <span class="k">return</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="k">raise</span> <span class="ne">RuntimeError</span><span class="p">(</span><span class="s2">&quot;could only set value of text nodes&quot;</span><span class="p">)</span>
        <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">update</span><span class="p">()</span>
        <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">_tab</span><span class="o">.</span><span class="n">send</span><span class="p">(</span><span class="n">cdp</span><span class="o">.</span><span class="n">dom</span><span class="o">.</span><span class="n">set_node_value</span><span class="p">(</span><span class="n">node_id</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">node_id</span><span class="p">,</span> <span class="n">value</span><span class="o">=</span><span class="n">value</span><span class="p">))</span></div>


<div class="viewcode-block" id="Element.get_html">
<a class="viewcode-back" href="../../../nodriver/classes/element.html#nodriver.Element.get_html">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">get_html</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">_tab</span><span class="o">.</span><span class="n">send</span><span class="p">(</span>
            <span class="n">cdp</span><span class="o">.</span><span class="n">dom</span><span class="o">.</span><span class="n">get_outer_html</span><span class="p">(</span><span class="n">backend_node_id</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">backend_node_id</span><span class="p">)</span>
        <span class="p">)</span></div>


    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">text</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        gets the text contents of this element</span>
<span class="sd">        note: this includes text in the form of script content, as those are also just &#39;text nodes&#39;</span>

<span class="sd">        :return:</span>
<span class="sd">        :rtype:</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">text_node</span> <span class="o">=</span> <span class="n">util</span><span class="o">.</span><span class="n">filter_recurse</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">node</span><span class="p">,</span> <span class="k">lambda</span> <span class="n">n</span><span class="p">:</span> <span class="n">n</span><span class="o">.</span><span class="n">node_type</span> <span class="o">==</span> <span class="mi">3</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">text_node</span><span class="p">:</span>
            <span class="k">return</span> <span class="n">text_node</span><span class="o">.</span><span class="n">node_value</span>
        <span class="k">return</span> <span class="s2">&quot;&quot;</span>

    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">text_all</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        gets the text contents of this element, and it&#39;s children in a concatenated string</span>
<span class="sd">        note: this includes text in the form of script content, as those are also just &#39;text nodes&#39;</span>
<span class="sd">        :return:</span>
<span class="sd">        :rtype:</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">text_nodes</span> <span class="o">=</span> <span class="n">util</span><span class="o">.</span><span class="n">filter_recurse_all</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">node</span><span class="p">,</span> <span class="k">lambda</span> <span class="n">n</span><span class="p">:</span> <span class="n">n</span><span class="o">.</span><span class="n">node_type</span> <span class="o">==</span> <span class="mi">3</span><span class="p">)</span>
        <span class="k">return</span> <span class="s2">&quot; &quot;</span><span class="o">.</span><span class="n">join</span><span class="p">([</span><span class="n">n</span><span class="o">.</span><span class="n">node_value</span> <span class="k">for</span> <span class="n">n</span> <span class="ow">in</span> <span class="n">text_nodes</span><span class="p">])</span>

<div class="viewcode-block" id="Element.query_selector_all">
<a class="viewcode-back" href="../../../nodriver/classes/element.html#nodriver.Element.query_selector_all">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">query_selector_all</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">selector</span><span class="p">:</span> <span class="nb">str</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        like js querySelectorAll()</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">update</span><span class="p">()</span>
        <span class="k">return</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">tab</span><span class="o">.</span><span class="n">query_selector_all</span><span class="p">(</span><span class="n">selector</span><span class="p">,</span> <span class="n">_node</span><span class="o">=</span><span class="bp">self</span><span class="p">)</span></div>


<div class="viewcode-block" id="Element.query_selector">
<a class="viewcode-back" href="../../../nodriver/classes/element.html#nodriver.Element.query_selector">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">query_selector</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">selector</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        like js querySelector()</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">update</span><span class="p">()</span>
        <span class="k">return</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">tab</span><span class="o">.</span><span class="n">query_selector</span><span class="p">(</span><span class="n">selector</span><span class="p">,</span> <span class="bp">self</span><span class="p">)</span></div>


    <span class="c1"># async def find_all(self, string: str):</span>
    <span class="c1">#     base_node = self.node</span>
    <span class="c1">#     if self.node.node_name == &quot;IFRAME&quot;:</span>
    <span class="c1">#         if self.node.content_document:</span>
    <span class="c1">#             base_node = self.node.content_document</span>
    <span class="c1">#     cdp.target.attach_to_target()</span>
    <span class="c1">#     cdp.target.create_target()</span>
    <span class="c1">#     cdp.target.create_browser_context()</span>
    <span class="c1">#     search_id, nresult = await self.tab.send(cdp.dom.perform_search(string, True))</span>
    <span class="c1">#     if nresult:</span>
    <span class="c1">#         node_ids = await self.send(</span>
    <span class="c1">#             cdp.dom.get_search_results(search_id, 0, nresult)</span>
    <span class="c1">#         )</span>
    <span class="c1">#         # doc = await self.send(cdp.dom.get_document(-1, True))</span>

<div class="viewcode-block" id="Element.save_screenshot">
<a class="viewcode-back" href="../../../nodriver/classes/element.html#nodriver.Element.save_screenshot">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">save_screenshot</span><span class="p">(</span>
        <span class="bp">self</span><span class="p">,</span>
        <span class="n">filename</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">PathLike</span><span class="p">]</span> <span class="o">=</span> <span class="s2">&quot;auto&quot;</span><span class="p">,</span>
        <span class="nb">format</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="s2">&quot;jpeg&quot;</span><span class="p">,</span>
        <span class="n">scale</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">typing</span><span class="o">.</span><span class="n">Union</span><span class="p">[</span><span class="nb">int</span><span class="p">,</span> <span class="nb">float</span><span class="p">]]</span> <span class="o">=</span> <span class="mi">1</span><span class="p">,</span>
    <span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Saves a screenshot of this element (only)</span>
<span class="sd">        This is not the same as :py:obj:`Tab.save_screenshot`, which saves a &quot;regular&quot; screenshot</span>

<span class="sd">        When the element is hidden, or has no size, or is otherwise not capturable, a RuntimeError is raised</span>

<span class="sd">        :param filename: uses this as the save path</span>
<span class="sd">        :type filename: PathLike</span>
<span class="sd">        :param format: jpeg or png (defaults to jpeg)</span>
<span class="sd">        :type format: str</span>
<span class="sd">        :param scale: the scale of the screenshot, eg: 1 = size as is, 2 = double, 0.5 is half</span>
<span class="sd">        :return: the path/filename of saved screenshot</span>
<span class="sd">        :rtype: str</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="kn">import</span> <span class="nn">base64</span>
        <span class="kn">import</span> <span class="nn">datetime</span>
        <span class="kn">import</span> <span class="nn">urllib.parse</span>

        <span class="n">pos</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_position</span><span class="p">()</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="n">pos</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">RuntimeError</span><span class="p">(</span>
                <span class="s2">&quot;could not determine position of element. probably because it&#39;s not in view, or hidden&quot;</span>
            <span class="p">)</span>
        <span class="n">viewport</span> <span class="o">=</span> <span class="n">pos</span><span class="o">.</span><span class="n">to_viewport</span><span class="p">(</span><span class="n">scale</span><span class="p">)</span>
        <span class="n">path</span> <span class="o">=</span> <span class="kc">None</span>
        <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">tab</span><span class="o">.</span><span class="n">sleep</span><span class="p">()</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="n">filename</span> <span class="ow">or</span> <span class="n">filename</span> <span class="o">==</span> <span class="s2">&quot;auto&quot;</span><span class="p">:</span>
            <span class="n">parsed</span> <span class="o">=</span> <span class="n">urllib</span><span class="o">.</span><span class="n">parse</span><span class="o">.</span><span class="n">urlparse</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">tab</span><span class="o">.</span><span class="n">target</span><span class="o">.</span><span class="n">url</span><span class="p">)</span>
            <span class="n">parts</span> <span class="o">=</span> <span class="n">parsed</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">split</span><span class="p">(</span><span class="s2">&quot;/&quot;</span><span class="p">)</span>
            <span class="n">last_part</span> <span class="o">=</span> <span class="n">parts</span><span class="p">[</span><span class="o">-</span><span class="mi">1</span><span class="p">]</span>
            <span class="n">last_part</span> <span class="o">=</span> <span class="n">last_part</span><span class="o">.</span><span class="n">rsplit</span><span class="p">(</span><span class="s2">&quot;?&quot;</span><span class="p">,</span> <span class="mi">1</span><span class="p">)[</span><span class="mi">0</span><span class="p">]</span>
            <span class="n">dt_str</span> <span class="o">=</span> <span class="n">datetime</span><span class="o">.</span><span class="n">datetime</span><span class="o">.</span><span class="n">now</span><span class="p">()</span><span class="o">.</span><span class="n">strftime</span><span class="p">(</span><span class="s2">&quot;%Y-%m-</span><span class="si">%d</span><span class="s2">_%H-%M-%S&quot;</span><span class="p">)</span>
            <span class="n">candidate</span> <span class="o">=</span> <span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">parsed</span><span class="o">.</span><span class="n">hostname</span><span class="si">}</span><span class="s2">__</span><span class="si">{</span><span class="n">last_part</span><span class="si">}</span><span class="s2">_</span><span class="si">{</span><span class="n">dt_str</span><span class="si">}</span><span class="s2">&quot;</span>
            <span class="n">ext</span> <span class="o">=</span> <span class="s2">&quot;&quot;</span>
            <span class="k">if</span> <span class="nb">format</span><span class="o">.</span><span class="n">lower</span><span class="p">()</span> <span class="ow">in</span> <span class="p">[</span><span class="s2">&quot;jpg&quot;</span><span class="p">,</span> <span class="s2">&quot;jpeg&quot;</span><span class="p">]:</span>
                <span class="n">ext</span> <span class="o">=</span> <span class="s2">&quot;.jpg&quot;</span>
                <span class="nb">format</span> <span class="o">=</span> <span class="s2">&quot;jpeg&quot;</span>
            <span class="k">elif</span> <span class="nb">format</span><span class="o">.</span><span class="n">lower</span><span class="p">()</span> <span class="ow">in</span> <span class="p">[</span><span class="s2">&quot;png&quot;</span><span class="p">]:</span>
                <span class="n">ext</span> <span class="o">=</span> <span class="s2">&quot;.png&quot;</span>
                <span class="nb">format</span> <span class="o">=</span> <span class="s2">&quot;png&quot;</span>
            <span class="n">path</span> <span class="o">=</span> <span class="n">pathlib</span><span class="o">.</span><span class="n">Path</span><span class="p">(</span><span class="n">candidate</span> <span class="o">+</span> <span class="n">ext</span><span class="p">)</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">path</span> <span class="o">=</span> <span class="n">pathlib</span><span class="o">.</span><span class="n">Path</span><span class="p">(</span><span class="n">filename</span><span class="p">)</span>

        <span class="n">path</span><span class="o">.</span><span class="n">parent</span><span class="o">.</span><span class="n">mkdir</span><span class="p">(</span><span class="n">parents</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span> <span class="n">exist_ok</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
        <span class="n">data</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">_tab</span><span class="o">.</span><span class="n">send</span><span class="p">(</span>
            <span class="n">cdp</span><span class="o">.</span><span class="n">page</span><span class="o">.</span><span class="n">capture_screenshot</span><span class="p">(</span>
                <span class="nb">format</span><span class="p">,</span> <span class="n">clip</span><span class="o">=</span><span class="n">viewport</span><span class="p">,</span> <span class="n">capture_beyond_viewport</span><span class="o">=</span><span class="kc">True</span>
            <span class="p">)</span>
        <span class="p">)</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="n">data</span><span class="p">:</span>
            <span class="kn">from</span> <span class="nn">.connection</span> <span class="kn">import</span> <span class="n">ProtocolException</span>

            <span class="k">raise</span> <span class="n">ProtocolException</span><span class="p">(</span>
                <span class="s2">&quot;could not take screenshot. most possible cause is the page has not finished loading yet.&quot;</span>
            <span class="p">)</span>

        <span class="n">data_bytes</span> <span class="o">=</span> <span class="n">base64</span><span class="o">.</span><span class="n">b64decode</span><span class="p">(</span><span class="n">data</span><span class="p">)</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="n">path</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">RuntimeError</span><span class="p">(</span><span class="s2">&quot;invalid filename or path: &#39;</span><span class="si">%s</span><span class="s2">&#39;&quot;</span> <span class="o">%</span> <span class="n">filename</span><span class="p">)</span>
        <span class="n">path</span><span class="o">.</span><span class="n">write_bytes</span><span class="p">(</span><span class="n">data_bytes</span><span class="p">)</span>
        <span class="k">return</span> <span class="nb">str</span><span class="p">(</span><span class="n">path</span><span class="p">)</span></div>


<div class="viewcode-block" id="Element.flash">
<a class="viewcode-back" href="../../../nodriver/classes/element.html#nodriver.Element.flash">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">flash</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">duration</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Union</span><span class="p">[</span><span class="nb">float</span><span class="p">,</span> <span class="nb">int</span><span class="p">]</span> <span class="o">=</span> <span class="mf">0.5</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        displays for a short time a red dot on the element (only if the element itself is visible)</span>

<span class="sd">        :param coords: x,y</span>
<span class="sd">        :type coords: x,y</span>
<span class="sd">        :param duration: seconds (default 0.5)</span>
<span class="sd">        :type duration:</span>
<span class="sd">        :return:</span>
<span class="sd">        :rtype:</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="kn">from</span> <span class="nn">.connection</span> <span class="kn">import</span> <span class="n">ProtocolException</span>

        <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">remote_object</span><span class="p">:</span>
            <span class="k">try</span><span class="p">:</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">_remote_object</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">tab</span><span class="o">.</span><span class="n">send</span><span class="p">(</span>
                    <span class="n">cdp</span><span class="o">.</span><span class="n">dom</span><span class="o">.</span><span class="n">resolve_node</span><span class="p">(</span><span class="n">backend_node_id</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">backend_node_id</span><span class="p">)</span>
                <span class="p">)</span>
            <span class="k">except</span> <span class="n">ProtocolException</span><span class="p">:</span>
                <span class="k">return</span>
        <span class="k">try</span><span class="p">:</span>
            <span class="n">pos</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_position</span><span class="p">()</span>

        <span class="k">except</span> <span class="p">(</span><span class="ne">Exception</span><span class="p">,):</span>
            <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span><span class="s2">&quot;flash() : could not determine position&quot;</span><span class="p">)</span>
            <span class="k">return</span>

        <span class="n">style</span> <span class="o">=</span> <span class="p">(</span>
            <span class="s2">&quot;position:absolute;z-index:99999999;padding:0;margin:0;&quot;</span>
            <span class="s2">&quot;left:</span><span class="si">{:.1f}</span><span class="s2">px; top: </span><span class="si">{:.1f}</span><span class="s2">px;&quot;</span>
            <span class="s2">&quot;opacity:1;&quot;</span>
            <span class="s2">&quot;width:16px;height:16px;border-radius:50%;background:red;&quot;</span>
            <span class="s2">&quot;animation:show-pointer-ani </span><span class="si">{:.2f}</span><span class="s2">s ease 1;&quot;</span>
        <span class="p">)</span><span class="o">.</span><span class="n">format</span><span class="p">(</span>
            <span class="n">pos</span><span class="o">.</span><span class="n">center</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span> <span class="o">-</span> <span class="mi">8</span><span class="p">,</span>  <span class="c1"># -8 to account for drawn circle itself (w,h)</span>
            <span class="n">pos</span><span class="o">.</span><span class="n">center</span><span class="p">[</span><span class="mi">1</span><span class="p">]</span> <span class="o">-</span> <span class="mi">8</span><span class="p">,</span>
            <span class="n">duration</span><span class="p">,</span>
        <span class="p">)</span>
        <span class="n">script</span> <span class="o">=</span> <span class="p">(</span>
<span class="w">            </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">            (targetElement) =&gt; {{</span>
<span class="sd">                var css = document.styleSheets[0];</span>
<span class="sd">                for( let css of [...document.styleSheets]) {{</span>
<span class="sd">                    try {{</span>
<span class="sd">                        css.insertRule(`</span>
<span class="sd">                        @keyframes show-pointer-ani {{</span>
<span class="sd">                              0% {{ opacity: 1; transform: scale(2, 2);}}</span>
<span class="sd">                              25% {{ transform: scale(5,5) }}</span>
<span class="sd">                              50% {{ transform: scale(3, 3);}}</span>
<span class="sd">                              75%: {{ transform: scale(2,2) }}</span>
<span class="sd">                              100% {{ transform: scale(1, 1); opacity: 0;}}</span>
<span class="sd">                        }}`,css.cssRules.length);</span>
<span class="sd">                        break;</span>
<span class="sd">                    }} catch (e) {{</span>
<span class="sd">                        console.log(e)</span>
<span class="sd">                    }}</span>
<span class="sd">                }};</span>
<span class="sd">                var _d = document.createElement(&#39;div&#39;);</span>
<span class="sd">                _d.style = `{0:s}`;</span>
<span class="sd">                _d.id = `{1:s}`;</span>
<span class="sd">                document.body.insertAdjacentElement(&#39;afterBegin&#39;, _d);</span>
<span class="sd">                                </span>
<span class="sd">                setTimeout( () =&gt; document.getElementById(&#39;{1:s}&#39;).remove(), {2:d});</span>
<span class="sd">            }}</span>
<span class="sd">            &quot;&quot;&quot;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span>
                <span class="n">style</span><span class="p">,</span>
                <span class="n">secrets</span><span class="o">.</span><span class="n">token_hex</span><span class="p">(</span><span class="mi">8</span><span class="p">),</span>
                <span class="nb">int</span><span class="p">(</span><span class="n">duration</span> <span class="o">*</span> <span class="mi">1000</span><span class="p">),</span>
            <span class="p">)</span>
            <span class="o">.</span><span class="n">replace</span><span class="p">(</span><span class="s2">&quot;  &quot;</span><span class="p">,</span> <span class="s2">&quot;&quot;</span><span class="p">)</span>
            <span class="o">.</span><span class="n">replace</span><span class="p">(</span><span class="s2">&quot;</span><span class="se">\n</span><span class="s2">&quot;</span><span class="p">,</span> <span class="s2">&quot;&quot;</span><span class="p">)</span>
        <span class="p">)</span>

        <span class="n">arguments</span> <span class="o">=</span> <span class="p">[</span><span class="n">cdp</span><span class="o">.</span><span class="n">runtime</span><span class="o">.</span><span class="n">CallArgument</span><span class="p">(</span><span class="n">object_id</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">_remote_object</span><span class="o">.</span><span class="n">object_id</span><span class="p">)]</span>
        <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">_tab</span><span class="o">.</span><span class="n">send</span><span class="p">(</span>
            <span class="n">cdp</span><span class="o">.</span><span class="n">runtime</span><span class="o">.</span><span class="n">call_function_on</span><span class="p">(</span>
                <span class="n">script</span><span class="p">,</span>
                <span class="n">object_id</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">_remote_object</span><span class="o">.</span><span class="n">object_id</span><span class="p">,</span>
                <span class="n">arguments</span><span class="o">=</span><span class="n">arguments</span><span class="p">,</span>
                <span class="n">await_promise</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
                <span class="n">user_gesture</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
            <span class="p">)</span>
        <span class="p">)</span></div>


<div class="viewcode-block" id="Element.highlight_overlay">
<a class="viewcode-back" href="../../../nodriver/classes/element.html#nodriver.Element.highlight_overlay">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">highlight_overlay</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        highlights the element devtools-style. To remove the highlight,</span>
<span class="sd">        call the method again.</span>
<span class="sd">        :return:</span>
<span class="sd">        :rtype:</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">if</span> <span class="nb">getattr</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="s2">&quot;_is_highlighted&quot;</span><span class="p">,</span> <span class="kc">False</span><span class="p">):</span>
            <span class="k">del</span> <span class="bp">self</span><span class="o">.</span><span class="n">_is_highlighted</span>
            <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">tab</span><span class="o">.</span><span class="n">send</span><span class="p">(</span><span class="n">cdp</span><span class="o">.</span><span class="n">overlay</span><span class="o">.</span><span class="n">hide_highlight</span><span class="p">())</span>
            <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">tab</span><span class="o">.</span><span class="n">send</span><span class="p">(</span><span class="n">cdp</span><span class="o">.</span><span class="n">dom</span><span class="o">.</span><span class="n">disable</span><span class="p">())</span>
            <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">tab</span><span class="o">.</span><span class="n">send</span><span class="p">(</span><span class="n">cdp</span><span class="o">.</span><span class="n">overlay</span><span class="o">.</span><span class="n">disable</span><span class="p">())</span>
            <span class="k">return</span>
        <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">tab</span><span class="o">.</span><span class="n">send</span><span class="p">(</span><span class="n">cdp</span><span class="o">.</span><span class="n">dom</span><span class="o">.</span><span class="n">enable</span><span class="p">())</span>
        <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">tab</span><span class="o">.</span><span class="n">send</span><span class="p">(</span><span class="n">cdp</span><span class="o">.</span><span class="n">overlay</span><span class="o">.</span><span class="n">enable</span><span class="p">())</span>
        <span class="n">conf</span> <span class="o">=</span> <span class="n">cdp</span><span class="o">.</span><span class="n">overlay</span><span class="o">.</span><span class="n">HighlightConfig</span><span class="p">(</span>
            <span class="n">show_info</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span> <span class="n">show_extension_lines</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span> <span class="n">show_styles</span><span class="o">=</span><span class="kc">True</span>
        <span class="p">)</span>
        <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">tab</span><span class="o">.</span><span class="n">send</span><span class="p">(</span>
            <span class="n">cdp</span><span class="o">.</span><span class="n">overlay</span><span class="o">.</span><span class="n">highlight_node</span><span class="p">(</span>
                <span class="n">highlight_config</span><span class="o">=</span><span class="n">conf</span><span class="p">,</span> <span class="n">backend_node_id</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">backend_node_id</span>
            <span class="p">)</span>
        <span class="p">)</span>
        <span class="nb">setattr</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="s2">&quot;_is_highlighted&quot;</span><span class="p">,</span> <span class="mi">1</span><span class="p">)</span></div>


<div class="viewcode-block" id="Element.record_video">
<a class="viewcode-back" href="../../../nodriver/classes/element.html#nodriver.Element.record_video">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">record_video</span><span class="p">(</span>
        <span class="bp">self</span><span class="p">,</span>
        <span class="n">filename</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
        <span class="n">folder</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
        <span class="n">duration</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">typing</span><span class="o">.</span><span class="n">Union</span><span class="p">[</span><span class="nb">int</span><span class="p">,</span> <span class="nb">float</span><span class="p">]]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
    <span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        experimental option.</span>

<span class="sd">        :param filename: the desired filename</span>
<span class="sd">        :param folder: the download folder path</span>
<span class="sd">        :param duration: record for this many seconds and then download</span>

<span class="sd">        on html5 video nodes, you can call this method to start recording of the video.</span>

<span class="sd">        when any of the follow happens:</span>

<span class="sd">        - video ends</span>
<span class="sd">        - calling videoelement(&#39;pause&#39;)</span>
<span class="sd">        - video stops</span>

<span class="sd">        the video recorded will be downloaded.</span>

<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">node_name</span> <span class="o">!=</span> <span class="s2">&quot;VIDEO&quot;</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">RuntimeError</span><span class="p">(</span>
                <span class="s2">&quot;record_video can only be called on html5 video elements&quot;</span>
            <span class="p">)</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="n">folder</span><span class="p">:</span>
            <span class="n">directory_path</span> <span class="o">=</span> <span class="n">pathlib</span><span class="o">.</span><span class="n">Path</span><span class="o">.</span><span class="n">cwd</span><span class="p">()</span> <span class="o">/</span> <span class="s2">&quot;downloads&quot;</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">directory_path</span> <span class="o">=</span> <span class="n">pathlib</span><span class="o">.</span><span class="n">Path</span><span class="p">(</span><span class="n">folder</span><span class="p">)</span>

        <span class="n">directory_path</span><span class="o">.</span><span class="n">mkdir</span><span class="p">(</span><span class="n">exist_ok</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
        <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">_tab</span><span class="o">.</span><span class="n">send</span><span class="p">(</span>
            <span class="n">cdp</span><span class="o">.</span><span class="n">browser</span><span class="o">.</span><span class="n">set_download_behavior</span><span class="p">(</span>
                <span class="s2">&quot;allow&quot;</span><span class="p">,</span> <span class="n">download_path</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">directory_path</span><span class="p">)</span>
            <span class="p">)</span>
        <span class="p">)</span>
        <span class="k">await</span> <span class="bp">self</span><span class="p">(</span><span class="s2">&quot;pause&quot;</span><span class="p">)</span>
        <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">apply</span><span class="p">(</span>
<span class="w">            </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">            function extractVid(vid) {{</span>
<span class="sd">                    </span>
<span class="sd">                      var duration = {duration:.1f}; </span>
<span class="sd">                      var stream = vid.captureStream();</span>
<span class="sd">                      var mr = new MediaRecorder(stream, {{audio:true, video:true}})</span>
<span class="sd">                      mr.ondataavailable  = function(e) {{</span>
<span class="sd">                          vid[&#39;_recording&#39;] = false</span>
<span class="sd">                          var blob = e.data;</span>
<span class="sd">                          f = new File([blob], {{name: {filename}, type:&#39;octet/stream&#39;}});</span>
<span class="sd">                          var objectUrl = URL.createObjectURL(f);</span>
<span class="sd">                          var link = document.createElement(&#39;a&#39;);</span>
<span class="sd">                          link.setAttribute(&#39;href&#39;, objectUrl)</span>
<span class="sd">                          link.setAttribute(&#39;download&#39;, {filename})</span>
<span class="sd">                          link.style.display = &#39;none&#39;</span>

<span class="sd">                          document.body.appendChild(link)</span>

<span class="sd">                          link.click()</span>

<span class="sd">                          document.body.removeChild(link)</span>
<span class="sd">                       }}</span>
<span class="sd">                       </span>
<span class="sd">                       mr.start()</span>
<span class="sd">                       vid.addEventListener(&#39;ended&#39; , (e) =&gt; mr.stop())</span>
<span class="sd">                       vid.addEventListener(&#39;pause&#39; , (e) =&gt; mr.stop())</span>
<span class="sd">                       vid.addEventListener(&#39;abort&#39;, (e) =&gt; mr.stop())</span>
<span class="sd">                       </span>
<span class="sd">                       </span>
<span class="sd">                       if ( duration ) {{ </span>
<span class="sd">                            setTimeout(() =&gt; {{ vid.pause(); vid.play() }}, duration);</span>
<span class="sd">                       }}</span>
<span class="sd">                       vid[&#39;_recording&#39;] = true</span>
<span class="sd">                  ;}}</span>
<span class="sd">                </span>
<span class="sd">            &quot;&quot;&quot;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span>
                <span class="n">filename</span><span class="o">=</span><span class="sa">f</span><span class="s1">&#39;&quot;</span><span class="si">{</span><span class="n">filename</span><span class="si">}</span><span class="s1">&quot;&#39;</span> <span class="k">if</span> <span class="n">filename</span> <span class="k">else</span> <span class="s1">&#39;document.title + &quot;.mp4&quot;&#39;</span><span class="p">,</span>
                <span class="n">duration</span><span class="o">=</span><span class="nb">int</span><span class="p">(</span><span class="n">duration</span> <span class="o">*</span> <span class="mi">1000</span><span class="p">)</span> <span class="k">if</span> <span class="n">duration</span> <span class="k">else</span> <span class="mi">0</span><span class="p">,</span>
            <span class="p">)</span>
        <span class="p">)</span>
        <span class="k">await</span> <span class="bp">self</span><span class="p">(</span><span class="s2">&quot;play&quot;</span><span class="p">)</span>
        <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">_tab</span></div>


<div class="viewcode-block" id="Element.is_recording">
<a class="viewcode-back" href="../../../nodriver/classes/element.html#nodriver.Element.is_recording">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">is_recording</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">apply</span><span class="p">(</span><span class="s1">&#39;(vid) =&gt; vid[&quot;_recording&quot;]&#39;</span><span class="p">)</span></div>


    <span class="k">def</span> <span class="nf">_make_attrs</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="n">sav</span> <span class="o">=</span> <span class="kc">None</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">node</span><span class="o">.</span><span class="n">attributes</span><span class="p">:</span>
            <span class="k">for</span> <span class="n">i</span><span class="p">,</span> <span class="n">a</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">node</span><span class="o">.</span><span class="n">attributes</span><span class="p">):</span>
                <span class="k">if</span> <span class="n">i</span> <span class="o">==</span> <span class="mi">0</span> <span class="ow">or</span> <span class="n">i</span> <span class="o">%</span> <span class="mi">2</span> <span class="o">==</span> <span class="mi">0</span><span class="p">:</span>
                    <span class="k">if</span> <span class="n">a</span> <span class="o">==</span> <span class="s2">&quot;class&quot;</span><span class="p">:</span>
                        <span class="n">a</span> <span class="o">=</span> <span class="s2">&quot;class_&quot;</span>
                    <span class="n">sav</span> <span class="o">=</span> <span class="n">a</span>
                <span class="k">else</span><span class="p">:</span>
                    <span class="k">if</span> <span class="n">sav</span><span class="p">:</span>
                        <span class="bp">self</span><span class="o">.</span><span class="n">attrs</span><span class="p">[</span><span class="n">sav</span><span class="p">]</span> <span class="o">=</span> <span class="n">a</span>

    <span class="k">def</span> <span class="fm">__eq__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">other</span><span class="p">:</span> <span class="n">Element</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">bool</span><span class="p">:</span>
        <span class="c1"># if other.__dict__.values() == self.__dict__.values():</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="n">other</span><span class="p">:</span>
            <span class="k">return</span> <span class="kc">False</span>
        <span class="k">if</span> <span class="n">other</span><span class="o">.</span><span class="n">backend_node_id</span> <span class="ow">and</span> <span class="bp">self</span><span class="o">.</span><span class="n">backend_node_id</span><span class="p">:</span>
            <span class="k">return</span> <span class="n">other</span><span class="o">.</span><span class="n">backend_node_id</span> <span class="o">==</span> <span class="bp">self</span><span class="o">.</span><span class="n">backend_node_id</span>

        <span class="k">return</span> <span class="kc">False</span>

    <span class="k">def</span> <span class="fm">__repr__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="n">tag_name</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">node</span><span class="o">.</span><span class="n">node_name</span><span class="o">.</span><span class="n">lower</span><span class="p">()</span>
        <span class="n">content</span> <span class="o">=</span> <span class="s2">&quot;&quot;</span>

        <span class="c1"># collect all text from this leaf</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">child_node_count</span><span class="p">:</span>
            <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">child_node_count</span> <span class="o">==</span> <span class="mi">1</span><span class="p">:</span>
                <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">children</span><span class="p">:</span>
                    <span class="n">content</span> <span class="o">+=</span> <span class="nb">str</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">children</span><span class="p">[</span><span class="mi">0</span><span class="p">])</span>

            <span class="k">elif</span> <span class="bp">self</span><span class="o">.</span><span class="n">child_node_count</span> <span class="o">&gt;</span> <span class="mi">1</span><span class="p">:</span>
                <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">children</span><span class="p">:</span>
                    <span class="k">for</span> <span class="n">child</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">children</span><span class="p">:</span>
                        <span class="n">content</span> <span class="o">+=</span> <span class="nb">str</span><span class="p">(</span><span class="n">child</span><span class="p">)</span>

        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">node</span><span class="o">.</span><span class="n">node_type</span> <span class="o">==</span> <span class="mi">3</span><span class="p">:</span>  <span class="c1"># we could be a text node ourselves</span>
            <span class="n">content</span> <span class="o">+=</span> <span class="bp">self</span><span class="o">.</span><span class="n">node_value</span>

            <span class="c1"># return text only, no tag names</span>
            <span class="c1"># this makes it look most natural, and compatible with other hml libs</span>

            <span class="k">return</span> <span class="n">content</span>

        <span class="n">attrs</span> <span class="o">=</span> <span class="s2">&quot; &quot;</span><span class="o">.</span><span class="n">join</span><span class="p">(</span>
            <span class="p">[</span><span class="sa">f</span><span class="s1">&#39;</span><span class="si">{</span><span class="n">k</span><span class="w"> </span><span class="k">if</span><span class="w"> </span><span class="n">k</span><span class="w"> </span><span class="o">!=</span><span class="w"> </span><span class="s2">&quot;class_&quot;</span><span class="w"> </span><span class="k">else</span><span class="w"> </span><span class="s2">&quot;class&quot;</span><span class="si">}</span><span class="s1">=&quot;</span><span class="si">{</span><span class="n">v</span><span class="si">}</span><span class="s1">&quot;&#39;</span> <span class="k">for</span> <span class="n">k</span><span class="p">,</span> <span class="n">v</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">attrs</span><span class="o">.</span><span class="n">items</span><span class="p">()]</span>
        <span class="p">)</span>
        <span class="n">s</span> <span class="o">=</span> <span class="sa">f</span><span class="s2">&quot;&lt;</span><span class="si">{</span><span class="n">tag_name</span><span class="si">}</span><span class="s2"> </span><span class="si">{</span><span class="n">attrs</span><span class="si">}</span><span class="s2">&gt;</span><span class="si">{</span><span class="n">content</span><span class="si">}</span><span class="s2">&lt;/</span><span class="si">{</span><span class="n">tag_name</span><span class="si">}</span><span class="s2">&gt;&quot;</span>
        <span class="k">return</span> <span class="n">s</span></div>



<span class="k">class</span> <span class="nc">Position</span><span class="p">(</span><span class="n">cdp</span><span class="o">.</span><span class="n">dom</span><span class="o">.</span><span class="n">Quad</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;helper class for element positioning&quot;&quot;&quot;</span>

    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">points</span><span class="p">):</span>
        <span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="fm">__init__</span><span class="p">(</span><span class="n">points</span><span class="p">)</span>
        <span class="p">(</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">left</span><span class="p">,</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">top</span><span class="p">,</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">right</span><span class="p">,</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">top</span><span class="p">,</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">right</span><span class="p">,</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">bottom</span><span class="p">,</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">left</span><span class="p">,</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">bottom</span><span class="p">,</span>
        <span class="p">)</span> <span class="o">=</span> <span class="n">points</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">abs_x</span><span class="p">:</span> <span class="nb">float</span> <span class="o">=</span> <span class="mi">0</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">abs_y</span><span class="p">:</span> <span class="nb">float</span> <span class="o">=</span> <span class="mi">0</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">x</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">left</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">y</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">top</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">height</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">width</span> <span class="o">=</span> <span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">bottom</span> <span class="o">-</span> <span class="bp">self</span><span class="o">.</span><span class="n">top</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">right</span> <span class="o">-</span> <span class="bp">self</span><span class="o">.</span><span class="n">left</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">center</span> <span class="o">=</span> <span class="p">(</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">left</span> <span class="o">+</span> <span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">width</span> <span class="o">/</span> <span class="mi">2</span><span class="p">),</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">top</span> <span class="o">+</span> <span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">height</span> <span class="o">/</span> <span class="mi">2</span><span class="p">),</span>
        <span class="p">)</span>

    <span class="k">def</span> <span class="nf">to_viewport</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">scale</span><span class="o">=</span><span class="mi">1</span><span class="p">):</span>
        <span class="k">return</span> <span class="n">cdp</span><span class="o">.</span><span class="n">page</span><span class="o">.</span><span class="n">Viewport</span><span class="p">(</span>
            <span class="n">x</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">x</span><span class="p">,</span> <span class="n">y</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">y</span><span class="p">,</span> <span class="n">width</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">width</span><span class="p">,</span> <span class="n">height</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">height</span><span class="p">,</span> <span class="n">scale</span><span class="o">=</span><span class="n">scale</span>
        <span class="p">)</span>

    <span class="k">def</span> <span class="fm">__repr__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="sa">f</span><span class="s2">&quot;&lt;Position(x=</span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">left</span><span class="si">}</span><span class="s2">, y=</span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">top</span><span class="si">}</span><span class="s2">, width=</span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">width</span><span class="si">}</span><span class="s2">, height=</span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">height</span><span class="si">}</span><span class="s2">)&gt;&quot;</span>


<span class="k">async</span> <span class="k">def</span> <span class="nf">resolve_node</span><span class="p">(</span><span class="n">tab</span><span class="p">:</span> <span class="n">Tab</span><span class="p">,</span> <span class="n">node_id</span><span class="p">:</span> <span class="n">cdp</span><span class="o">.</span><span class="n">dom</span><span class="o">.</span><span class="n">NodeId</span><span class="p">):</span>
    <span class="n">remote_obj</span><span class="p">:</span> <span class="n">cdp</span><span class="o">.</span><span class="n">runtime</span><span class="o">.</span><span class="n">RemoteObject</span> <span class="o">=</span> <span class="k">await</span> <span class="n">tab</span><span class="o">.</span><span class="n">send</span><span class="p">(</span>
        <span class="n">cdp</span><span class="o">.</span><span class="n">dom</span><span class="o">.</span><span class="n">resolve_node</span><span class="p">(</span><span class="n">node_id</span><span class="o">=</span><span class="n">node_id</span><span class="p">)</span>
    <span class="p">)</span>
    <span class="n">node_id</span><span class="p">:</span> <span class="n">cdp</span><span class="o">.</span><span class="n">dom</span><span class="o">.</span><span class="n">NodeId</span> <span class="o">=</span> <span class="k">await</span> <span class="n">tab</span><span class="o">.</span><span class="n">send</span><span class="p">(</span><span class="n">cdp</span><span class="o">.</span><span class="n">dom</span><span class="o">.</span><span class="n">request_node</span><span class="p">(</span><span class="n">remote_obj</span><span class="o">.</span><span class="n">object_id</span><span class="p">))</span>
    <span class="n">node</span><span class="p">:</span> <span class="n">cdp</span><span class="o">.</span><span class="n">dom</span><span class="o">.</span><span class="n">Node</span> <span class="o">=</span> <span class="k">await</span> <span class="n">tab</span><span class="o">.</span><span class="n">send</span><span class="p">(</span><span class="n">cdp</span><span class="o">.</span><span class="n">dom</span><span class="o">.</span><span class="n">describe_node</span><span class="p">(</span><span class="n">node_id</span><span class="p">))</span>
    <span class="k">return</span> <span class="n">node</span>
</pre></div>
        </article>
      </div>
      <footer>
        
        <div class="related-pages">
          
          
        </div>
        <div class="bottom-of-page">
          <div class="left-details">
            <div class="copyright">
                Copyright &#169; 2023, Author
            </div>
            Made with <a href="https://www.sphinx-doc.org/">Sphinx</a> and <a class="muted-link" href="https://pradyunsg.me">@pradyunsg</a>'s
            
            <a href="https://github.com/pradyunsg/furo">Furo</a>
            
          </div>
          <div class="right-details">
            
          </div>
        </div>
        
      </footer>
    </div>
    <aside class="toc-drawer no-toc">
      
      
      
    </aside>
  </div>
</div><script src="../../../_static/documentation_options.js?v=5929fcd5"></script>
    <script src="../../../_static/doctools.js?v=888ff710"></script>
    <script src="../../../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../../../_static/scripts/furo.js?v=32e29ea5"></script>
    </body>
</html>