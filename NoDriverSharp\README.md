# NoDriverSharp

**NoDriverSharp** is a C# port of the Python [nodriver](https://github.com/ultrafunkamsterdam/nodriver) library, providing next-level async browser automation using Chrome DevTools Protocol (CDP) without Selenium or WebDriver dependencies.

## Features

- 🚀 **Direct CDP Communication** - No WebDriver overhead, direct communication with Chrome DevTools Protocol
- ⚡ **Fully Async** - Built with async/await patterns for maximum performance
- 🛡️ **Anti-Detection** - Optimized to avoid detection by anti-bot systems
- 🎯 **Easy to Use** - Simple, intuitive API that "just works"
- 🔧 **Highly Configurable** - Full access to Chrome launch arguments and CDP domains
- 📸 **Screenshots** - Built-in screenshot capabilities
- 🌐 **Multi-Tab Support** - Manage multiple browser tabs simultaneously

## Quick Start

### Installation

```bash
# Clone the repository
git clone https://github.com/NoDriverSharp/NoDriverSharp.git
cd NoDriverSharp

# Build the solution
dotnet build
```

### Basic Usage

```csharp
using NoDriverSharp.Core;

// Start a browser and navigate to a website
await using var browser = await NoDriver.StartAsync();
var tab = await browser.GetAsync("https://www.example.com");

// Take a screenshot
await tab.SaveScreenshotAsync("screenshot.png");

// Navigate to another page
await tab.NavigateAsync("https://www.google.com");

// Open a new tab
var newTab = await browser.GetAsync("https://www.github.com", newTab: true);
```

### Configuration Options

```csharp
using NoDriverSharp.Core;

// Create custom configuration
var config = new Config
{
    Headless = true,
    UserDataDir = "/path/to/profile",
    BrowserExecutablePath = "/path/to/chrome",
    Port = 9222,
    WindowSize = (1920, 1080)
};

// Start browser with custom config
await using var browser = await NoDriver.StartAsync(config);
```

### Predefined Configurations

```csharp
// Headless mode optimized for automation
var headlessConfig = NoDriver.CreateHeadlessConfig();
await using var browser1 = await NoDriver.StartAsync(headlessConfig);

// Stealth mode for anti-detection
var stealthConfig = NoDriver.CreateStealthConfig();
await using var browser2 = await NoDriver.StartAsync(stealthConfig);

// Debug mode with additional logging
var debugConfig = NoDriver.CreateDebugConfig();
await using var browser3 = await NoDriver.StartAsync(debugConfig);
```

## Architecture

NoDriverSharp consists of several key components:

### Core Classes

- **`Browser`** - Manages the Chrome process and multiple tabs
- **`Tab`** - Represents a browser tab with navigation and interaction capabilities  
- **`Connection`** - Handles WebSocket communication with Chrome DevTools Protocol
- **`Config`** - Configuration management for browser startup options
- **`Element`** - Represents DOM elements (coming soon)

### CDP Integration

- **`NoDriverSharp.CDP`** - Chrome DevTools Protocol domain classes
- Auto-generated from CDP specifications
- Strongly-typed command and event classes
- Full coverage of CDP domains (Page, DOM, Runtime, etc.)

## Examples

### Basic Navigation

```csharp
await using var browser = await NoDriver.StartAsync();
var tab = await browser.GetAsync("https://www.example.com");

Console.WriteLine($"Current URL: {tab.Url}");
Console.WriteLine($"Page Title: {tab.Title}");
```

### Multiple Tabs

```csharp
await using var browser = await NoDriver.StartAsync();

// Open multiple tabs
var tab1 = await browser.GetAsync("https://www.google.com");
var tab2 = await browser.GetAsync("https://www.github.com", newTab: true);
var tab3 = await browser.GetAsync("https://www.stackoverflow.com", newTab: true);

Console.WriteLine($"Total tabs: {browser.Tabs.Count}");

// Close a specific tab
await browser.CloseTabAsync(tab2);
```

### Screenshots

```csharp
await using var browser = await NoDriver.StartAsync();
var tab = await browser.GetAsync("https://www.example.com");

// Take screenshot as byte array
var screenshotData = await tab.CaptureScreenshotAsync();

// Save screenshot to file
await tab.SaveScreenshotAsync("example.png");

// Custom screenshot options
await tab.SaveScreenshotAsync("example.jpg", format: "jpeg", quality: 90);
```

### Error Handling

```csharp
try
{
    await using var browser = await NoDriver.StartAsync();
    var tab = await browser.GetAsync("https://invalid-url");
}
catch (BrowserStartupException ex)
{
    Console.WriteLine($"Failed to start browser: {ex.Message}");
}
catch (NavigationException ex)
{
    Console.WriteLine($"Navigation failed: {ex.Message}");
}
catch (TimeoutException ex)
{
    Console.WriteLine($"Operation timed out: {ex.Message}");
}
```

## Requirements

- **.NET 8.0** or later
- **Chrome/Chromium** browser installed
- **Windows, Linux, or macOS**

## Supported Browsers

- Google Chrome
- Chromium
- Microsoft Edge
- Brave Browser

## Roadmap

- [ ] **Element Interaction** - Click, type, select elements
- [ ] **DOM Querying** - CSS selectors, XPath support
- [ ] **Form Automation** - Fill forms, submit data
- [ ] **Network Monitoring** - Intercept and modify requests
- [ ] **Cookie Management** - Save/load browser sessions
- [ ] **Mobile Emulation** - Device and viewport emulation
- [ ] **Proxy Support** - HTTP/SOCKS proxy configuration
- [ ] **Extensions** - Load Chrome extensions
- [ ] **Performance Metrics** - Page load timing and metrics

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- Original Python [nodriver](https://github.com/ultrafunkamsterdam/nodriver) library by UltrafunkAmsterdam
- Chrome DevTools Protocol documentation
- .NET community for excellent async/await patterns

## Related Projects

- [nodriver (Python)](https://github.com/ultrafunkamsterdam/nodriver) - Original Python implementation
- [undetected-chromedriver](https://github.com/ultrafunkamsterdam/undetected-chromedriver) - Predecessor to nodriver
- [Playwright](https://playwright.dev/) - Microsoft's browser automation framework
- [Selenium](https://selenium.dev/) - Traditional WebDriver-based automation
