using Microsoft.Extensions.Logging;

namespace NoDriverSharp.Core;

/// <summary>
/// Main entry point for NoDriverSharp - provides convenient static methods for browser automation
/// </summary>
public static class NoDriver
{
    /// <summary>
    /// Creates and starts a new browser instance with default configuration
    /// </summary>
    /// <param name="headless">Whether to run in headless mode</param>
    /// <param name="userDataDir">Custom user data directory (optional)</param>
    /// <param name="browserExecutablePath">Custom browser executable path (optional)</param>
    /// <param name="port">Custom debugging port (optional, 0 for auto-assign)</param>
    /// <param name="logger">Custom logger instance (optional)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>A new Browser instance</returns>
    public static async Task<Browser> StartAsync(
        bool headless = false,
        string? userDataDir = null,
        string? browserExecutablePath = null,
        int port = 0,
        ILogger<Browser>? logger = null,
        CancellationToken cancellationToken = default)
    {
        var config = new Config
        {
            Headless = headless,
            UserDataDir = userDataDir,
            BrowserExecutablePath = browserExecutablePath,
            Port = port
        };

        return await Browser.CreateAsync(config, logger, cancellationToken);
    }

    /// <summary>
    /// Creates and starts a new browser instance with custom configuration
    /// </summary>
    /// <param name="config">Browser configuration</param>
    /// <param name="logger">Custom logger instance (optional)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>A new Browser instance</returns>
    public static async Task<Browser> StartAsync(
        Config config,
        ILogger<Browser>? logger = null,
        CancellationToken cancellationToken = default)
    {
        return await Browser.CreateAsync(config, logger, cancellationToken);
    }

    /// <summary>
    /// Creates and starts a new browser instance, navigates to the specified URL, and returns the tab
    /// </summary>
    /// <param name="url">URL to navigate to</param>
    /// <param name="headless">Whether to run in headless mode</param>
    /// <param name="userDataDir">Custom user data directory (optional)</param>
    /// <param name="browserExecutablePath">Custom browser executable path (optional)</param>
    /// <param name="logger">Custom logger instance (optional)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>A Tab instance navigated to the specified URL</returns>
    public static async Task<Tab> GetAsync(
        string url,
        bool headless = false,
        string? userDataDir = null,
        string? browserExecutablePath = null,
        ILogger<Browser>? logger = null,
        CancellationToken cancellationToken = default)
    {
        var browser = await StartAsync(headless, userDataDir, browserExecutablePath, 0, logger, cancellationToken);
        return await browser.GetAsync(url, cancellationToken: cancellationToken);
    }

    /// <summary>
    /// Creates a new configuration object with sensible defaults
    /// </summary>
    /// <returns>A new Config instance</returns>
    public static Config CreateConfig()
    {
        return new Config();
    }

    /// <summary>
    /// Creates a configuration object optimized for headless automation
    /// </summary>
    /// <returns>A Config instance configured for headless mode</returns>
    public static Config CreateHeadlessConfig()
    {
        var config = new Config
        {
            Headless = true,
            Sandbox = true
        };

        // Add additional headless-optimized arguments
        config.BrowserArgs.AddRange(new[]
        {
            "--disable-gpu",
            "--disable-software-rasterizer",
            "--disable-background-timer-throttling",
            "--disable-backgrounding-occluded-windows",
            "--disable-renderer-backgrounding"
        });

        return config;
    }

    /// <summary>
    /// Creates a configuration object optimized for stealth/anti-detection
    /// </summary>
    /// <returns>A Config instance configured for stealth mode</returns>
    public static Config CreateStealthConfig()
    {
        var config = new Config
        {
            Expert = false, // Keep expert mode off for better stealth
            Sandbox = true
        };

        // Add stealth-focused arguments
        config.BrowserArgs.AddRange(new[]
        {
            "--disable-blink-features=AutomationControlled",
            "--exclude-switches=enable-automation",
            "--disable-extensions-except=",
            "--disable-extensions",
            "--no-first-run",
            "--disable-default-apps",
            "--disable-popup-blocking",
            "--disable-translate",
            "--disable-background-timer-throttling",
            "--disable-backgrounding-occluded-windows",
            "--disable-renderer-backgrounding",
            "--disable-features=TranslateUI,BlinkGenPropertyTrees"
        });

        return config;
    }

    /// <summary>
    /// Creates a configuration object optimized for debugging
    /// </summary>
    /// <returns>A Config instance configured for debugging</returns>
    public static Config CreateDebugConfig()
    {
        var config = new Config
        {
            Expert = true,
            Headless = false,
            Sandbox = false
        };

        // Add debugging-friendly arguments
        config.BrowserArgs.AddRange(new[]
        {
            "--disable-web-security",
            "--disable-features=VizDisplayCompositor",
            "--disable-ipc-flooding-protection",
            "--enable-logging",
            "--log-level=0"
        });

        return config;
    }

    /// <summary>
    /// Finds available Chrome/Chromium executable on the system
    /// </summary>
    /// <returns>Path to browser executable or null if not found</returns>
    public static string? FindBrowserExecutable()
    {
        return Config.FindBrowserExecutable();
    }

    /// <summary>
    /// Creates a temporary user data directory
    /// </summary>
    /// <returns>Path to the created temporary directory</returns>
    public static string CreateTempUserDataDir()
    {
        return Config.CreateTempUserDataDir();
    }
}
