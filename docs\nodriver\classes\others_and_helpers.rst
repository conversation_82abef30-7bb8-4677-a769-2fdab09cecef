================================
Other classes and Helper classes
================================


Config class
-----------------

.. autoclass:: nodriver.Config
    :members:
    :undoc-members:
    :inherited-members:



ContraDict class
-----------------

Many components in this package are built using a
base class of :any:`nodriver.core._contradict.ContraDict`.

It's nothing more than a dictionary which has attribute access AND
is JSON serializable.


.. autoclass:: nodriver.core._contradict.ContraDict
   :members:
   :inherited-members:


Helper functions
---------------------

.. automodule:: nodriver.core._contradict
    :members:
    :inherited-members:


