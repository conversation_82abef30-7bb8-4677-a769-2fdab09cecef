<!doctype html>
<html class="no-js" lang="en" data-content_root="../../">
  <head><meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width,initial-scale=1"/>
    <meta name="color-scheme" content="light dark"><meta name="viewport" content="width=device-width, initial-scale=1" />
<link rel="index" title="Index" href="../../genindex.html" /><link rel="search" title="Search" href="../../search.html" /><link rel="next" title="Debugger" href="debugger.html" /><link rel="prev" title="Console" href="console.html" />

    <!-- Generated with Sphinx 7.2.6 and Furo 2023.09.10 -->
        <title>CSS - nodriver documentation</title>
      <link rel="stylesheet" type="text/css" href="../../_static/pygments.css?v=fa44fd50" />
    <link rel="stylesheet" type="text/css" href="../../_static/styles/furo.css?v=135e06be" />
    <link rel="stylesheet" type="text/css" href="../../_static/styles/furo-extensions.css?v=36a5483c" />
    <link rel="stylesheet" type="text/css" href="../../_static/./custom.css?v=3934642d" />
    
    


<style>
  body {
    --color-code-background: #eeffcc;
  --color-code-foreground: black;
  
  }
  @media not print {
    body[data-theme="dark"] {
      --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8eefba;
  --color-background-primary: #111112;
  --color-problematic: #e7aeae;
  
    }
    @media (prefers-color-scheme: dark) {
      body:not([data-theme="light"]) {
        --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8eefba;
  --color-background-primary: #111112;
  --color-problematic: #e7aeae;
  
      }
    }
  }
</style></head>
  <body>
    
    <script>
      document.body.dataset.theme = localStorage.getItem("theme") || "auto";
    </script>
    

<svg xmlns="http://www.w3.org/2000/svg" style="display: none;">
  <symbol id="svg-toc" viewBox="0 0 24 24">
    <title>Contents</title>
    <svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 1024 1024">
      <path d="M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM115.4 518.9L271.7 642c5.8 4.6 14.4.5 14.4-6.9V388.9c0-7.4-8.5-11.5-14.4-6.9L115.4 505.1a8.74 8.74 0 0 0 0 13.8z"/>
    </svg>
  </symbol>
  <symbol id="svg-menu" viewBox="0 0 24 24">
    <title>Menu</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-menu">
      <line x1="3" y1="12" x2="21" y2="12"></line>
      <line x1="3" y1="6" x2="21" y2="6"></line>
      <line x1="3" y1="18" x2="21" y2="18"></line>
    </svg>
  </symbol>
  <symbol id="svg-arrow-right" viewBox="0 0 24 24">
    <title>Expand</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-chevron-right">
      <polyline points="9 18 15 12 9 6"></polyline>
    </svg>
  </symbol>
  <symbol id="svg-sun" viewBox="0 0 24 24">
    <title>Light mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="feather-sun">
      <circle cx="12" cy="12" r="5"></circle>
      <line x1="12" y1="1" x2="12" y2="3"></line>
      <line x1="12" y1="21" x2="12" y2="23"></line>
      <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
      <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
      <line x1="1" y1="12" x2="3" y2="12"></line>
      <line x1="21" y1="12" x2="23" y2="12"></line>
      <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
      <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
    </svg>
  </symbol>
  <symbol id="svg-moon" viewBox="0 0 24 24">
    <title>Dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-moon">
      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
      <path d="M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z" />
    </svg>
  </symbol>
  <symbol id="svg-sun-half" viewBox="0 0 24 24">
    <title>Auto light/dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-shadow">
      <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
      <circle cx="12" cy="12" r="9" />
      <path d="M13 12h5" />
      <path d="M13 15h4" />
      <path d="M13 18h1" />
      <path d="M13 9h4" />
      <path d="M13 6h1" />
    </svg>
  </symbol>
</svg>

<input type="checkbox" class="sidebar-toggle" name="__navigation" id="__navigation">
<input type="checkbox" class="sidebar-toggle" name="__toc" id="__toc">
<label class="overlay sidebar-overlay" for="__navigation">
  <div class="visually-hidden">Hide navigation sidebar</div>
</label>
<label class="overlay toc-overlay" for="__toc">
  <div class="visually-hidden">Hide table of contents sidebar</div>
</label>



<div class="page">
  <header class="mobile-header">
    <div class="header-left">
      <label class="nav-overlay-icon" for="__navigation">
        <div class="visually-hidden">Toggle site navigation sidebar</div>
        <i class="icon"><svg><use href="#svg-menu"></use></svg></i>
      </label>
    </div>
    <div class="header-center">
      <a href="../../index.html"><div class="brand">nodriver  documentation</div></a>
    </div>
    <div class="header-right">
      <div class="theme-toggle-container theme-toggle-header">
        <button class="theme-toggle">
          <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
          <svg class="theme-icon-when-auto"><use href="#svg-sun-half"></use></svg>
          <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
          <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
        </button>
      </div>
      <label class="toc-overlay-icon toc-header-icon" for="__toc">
        <div class="visually-hidden">Toggle table of contents sidebar</div>
        <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
      </label>
    </div>
  </header>
  <aside class="sidebar-drawer">
    <div class="sidebar-container">
      
      <div class="sidebar-sticky"><div class="sidebar-scroll"><a class="sidebar-brand" href="../../index.html">
  
  
  <span class="sidebar-brand-text">nodriver  documentation</span>
  
</a><form class="sidebar-search-container" method="get" action="../../search.html" role="search">
  <input class="sidebar-search" placeholder="Search" name="q" aria-label="Search">
  <input type="hidden" name="check_keywords" value="yes">
  <input type="hidden" name="area" value="default">
</form>
<div id="searchbox"></div><div class="sidebar-tree">
  <ul>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html">Quickstart guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#usage-example">usage example</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#more-complete-example">More complete example</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#custom-starting-options">Custom starting options</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#alternative-custom-options">Alternative custom options</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#proxies-socks5-authenticated-too">Proxies (socks5, authenticated too)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#concrete-example">Concrete example</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../classes/browser.html">Browser class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../classes/tab.html">Tab class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../classes/element.html">Element class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../classes/others_and_helpers.html">Other classes and Helper classes</a></li>
</ul>
<ul class="current">
<li class="toctree-l1 current has-children"><a class="reference internal" href="../cdp.html">CDP object</a><input checked="" class="toctree-checkbox" id="toctree-checkbox-1" name="toctree-checkbox-1" role="switch" type="checkbox"/><label for="toctree-checkbox-1"><div class="visually-hidden">Toggle navigation of CDP object</div><i class="icon"><svg><use href="#svg-arrow-right"></use></svg></i></label><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="accessibility.html">Accessibility</a></li>
<li class="toctree-l2"><a class="reference internal" href="animation.html">Animation</a></li>
<li class="toctree-l2"><a class="reference internal" href="audits.html">Audits</a></li>
<li class="toctree-l2"><a class="reference internal" href="autofill.html">Autofill</a></li>
<li class="toctree-l2"><a class="reference internal" href="background_service.html">BackgroundService</a></li>
<li class="toctree-l2"><a class="reference internal" href="bluetooth_emulation.html">BluetoothEmulation</a></li>
<li class="toctree-l2"><a class="reference internal" href="browser.html">Browser</a></li>
<li class="toctree-l2"><a class="reference internal" href="cache_storage.html">CacheStorage</a></li>
<li class="toctree-l2"><a class="reference internal" href="cast.html">Cast</a></li>
<li class="toctree-l2"><a class="reference internal" href="console.html">Console</a></li>
<li class="toctree-l2 current current-page"><a class="current reference internal" href="#">CSS</a></li>
<li class="toctree-l2"><a class="reference internal" href="debugger.html">Debugger</a></li>
<li class="toctree-l2"><a class="reference internal" href="device_access.html">DeviceAccess</a></li>
<li class="toctree-l2"><a class="reference internal" href="device_orientation.html">DeviceOrientation</a></li>
<li class="toctree-l2"><a class="reference internal" href="dom.html">DOM</a></li>
<li class="toctree-l2"><a class="reference internal" href="dom_debugger.html">DOMDebugger</a></li>
<li class="toctree-l2"><a class="reference internal" href="dom_snapshot.html">DOMSnapshot</a></li>
<li class="toctree-l2"><a class="reference internal" href="dom_storage.html">DOMStorage</a></li>
<li class="toctree-l2"><a class="reference internal" href="emulation.html">Emulation</a></li>
<li class="toctree-l2"><a class="reference internal" href="event_breakpoints.html">EventBreakpoints</a></li>
<li class="toctree-l2"><a class="reference internal" href="extensions.html">Extensions</a></li>
<li class="toctree-l2"><a class="reference internal" href="fed_cm.html">FedCm</a></li>
<li class="toctree-l2"><a class="reference internal" href="fetch.html">Fetch</a></li>
<li class="toctree-l2"><a class="reference internal" href="file_system.html">FileSystem</a></li>
<li class="toctree-l2"><a class="reference internal" href="headless_experimental.html">HeadlessExperimental</a></li>
<li class="toctree-l2"><a class="reference internal" href="heap_profiler.html">HeapProfiler</a></li>
<li class="toctree-l2"><a class="reference internal" href="indexed_db.html">IndexedDB</a></li>
<li class="toctree-l2"><a class="reference internal" href="input_.html">Input</a></li>
<li class="toctree-l2"><a class="reference internal" href="inspector.html">Inspector</a></li>
<li class="toctree-l2"><a class="reference internal" href="io.html">IO</a></li>
<li class="toctree-l2"><a class="reference internal" href="layer_tree.html">LayerTree</a></li>
<li class="toctree-l2"><a class="reference internal" href="log.html">Log</a></li>
<li class="toctree-l2"><a class="reference internal" href="media.html">Media</a></li>
<li class="toctree-l2"><a class="reference internal" href="memory.html">Memory</a></li>
<li class="toctree-l2"><a class="reference internal" href="network.html">Network</a></li>
<li class="toctree-l2"><a class="reference internal" href="overlay.html">Overlay</a></li>
<li class="toctree-l2"><a class="reference internal" href="page.html">Page</a></li>
<li class="toctree-l2"><a class="reference internal" href="performance.html">Performance</a></li>
<li class="toctree-l2"><a class="reference internal" href="performance_timeline.html">PerformanceTimeline</a></li>
<li class="toctree-l2"><a class="reference internal" href="preload.html">Preload</a></li>
<li class="toctree-l2"><a class="reference internal" href="profiler.html">Profiler</a></li>
<li class="toctree-l2"><a class="reference internal" href="pwa.html">PWA</a></li>
<li class="toctree-l2"><a class="reference internal" href="runtime.html">Runtime</a></li>
<li class="toctree-l2"><a class="reference internal" href="schema.html">Schema</a></li>
<li class="toctree-l2"><a class="reference internal" href="security.html">Security</a></li>
<li class="toctree-l2"><a class="reference internal" href="service_worker.html">ServiceWorker</a></li>
<li class="toctree-l2"><a class="reference internal" href="storage.html">Storage</a></li>
<li class="toctree-l2"><a class="reference internal" href="system_info.html">SystemInfo</a></li>
<li class="toctree-l2"><a class="reference internal" href="target.html">Target</a></li>
<li class="toctree-l2"><a class="reference internal" href="tethering.html">Tethering</a></li>
<li class="toctree-l2"><a class="reference internal" href="tracing.html">Tracing</a></li>
<li class="toctree-l2"><a class="reference internal" href="web_audio.html">WebAudio</a></li>
<li class="toctree-l2"><a class="reference internal" href="web_authn.html">WebAuthn</a></li>
</ul>
</li>
</ul>

</div>
</div>
      </div>
      
    </div>
  </aside>
  <div class="main">
    <div class="content">
      <div class="article-container">
        <a href="#" class="back-to-top muted-link">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
            <path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8v12z"></path>
          </svg>
          <span>Back to top</span>
        </a>
        <div class="content-icon-container">
          
<div class="theme-toggle-container theme-toggle-content">
            <button class="theme-toggle">
              <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
              <svg class="theme-icon-when-auto"><use href="#svg-sun-half"></use></svg>
              <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
              <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
            </button>
          </div>
          <label class="toc-overlay-icon toc-content-icon" for="__toc">
            <div class="visually-hidden">Toggle table of contents sidebar</div>
            <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
          </label>
        </div>
        <article role="main">
          <section id="css">
<h1>CSS<a class="headerlink" href="#css" title="Link to this heading">#</a></h1>
<p>This domain exposes CSS read/write operations. All CSS objects (stylesheets, rules, and styles)
have an associated <a class="reference external" href="https://docs.python.org/3/library/functions.html#id" title="(in Python v3.13)"><code class="xref any docutils literal notranslate"><span class="pre">id</span></code></a> used in subsequent operations on the related object. Each object type has
a specific <a class="reference external" href="https://docs.python.org/3/library/functions.html#id" title="(in Python v3.13)"><code class="xref any docutils literal notranslate"><span class="pre">id</span></code></a> structure, and those are not interchangeable between objects of different kinds.
CSS objects can be loaded using the <code class="xref any docutils literal notranslate"><span class="pre">get*ForNode()</span></code> calls (which accept a DOM node id). A client
can also keep track of stylesheets via the <code class="xref any docutils literal notranslate"><span class="pre">styleSheetAdded</span></code>/<code class="xref any docutils literal notranslate"><span class="pre">styleSheetRemoved</span></code> events and
subsequently load the required stylesheet contents using the <code class="xref any docutils literal notranslate"><span class="pre">getStyleSheet[Text]()</span></code> methods.</p>
<p><em>This CDP domain is experimental.</em></p>
<ul class="simple" id="module-nodriver.cdp.css">
<li><p><a class="reference internal" href="#types">Types</a></p></li>
<li><p><a class="reference internal" href="#commands">Commands</a></p></li>
<li><p><a class="reference internal" href="#events">Events</a></p></li>
</ul>
<section id="types">
<h2>Types<a class="headerlink" href="#types" title="Link to this heading">#</a></h2>
<p>Generally, you do not need to instantiate CDP types
yourself. Instead, the API creates objects for you as return
values from commands, and then you can use those objects as
arguments to other commands.</p>
<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.css.StyleSheetId">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">StyleSheetId</span></span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#StyleSheetId"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.StyleSheetId" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.css.StyleSheetOrigin">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">StyleSheetOrigin</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">names</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">module</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">qualname</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">boundary</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#StyleSheetOrigin"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.StyleSheetOrigin" title="Link to this definition">#</a></dt>
<dd><p>Stylesheet type: “injected” for stylesheets injected via extension, “user-agent” for user-agent
stylesheets, “inspector” for stylesheets created by the inspector (i.e. those holding the “via
inspector” rules), “regular” for regular stylesheets.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.StyleSheetOrigin.INJECTED">
<span class="sig-name descname"><span class="pre">INJECTED</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'injected'</span></em><a class="headerlink" href="#nodriver.cdp.css.StyleSheetOrigin.INJECTED" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.StyleSheetOrigin.USER_AGENT">
<span class="sig-name descname"><span class="pre">USER_AGENT</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'user-agent'</span></em><a class="headerlink" href="#nodriver.cdp.css.StyleSheetOrigin.USER_AGENT" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.StyleSheetOrigin.INSPECTOR">
<span class="sig-name descname"><span class="pre">INSPECTOR</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'inspector'</span></em><a class="headerlink" href="#nodriver.cdp.css.StyleSheetOrigin.INSPECTOR" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.StyleSheetOrigin.REGULAR">
<span class="sig-name descname"><span class="pre">REGULAR</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'regular'</span></em><a class="headerlink" href="#nodriver.cdp.css.StyleSheetOrigin.REGULAR" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.css.PseudoElementMatches">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">PseudoElementMatches</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">pseudo_type</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">matches</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pseudo_identifier</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#PseudoElementMatches"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.PseudoElementMatches" title="Link to this definition">#</a></dt>
<dd><p>CSS rule collection for a single pseudo style.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.PseudoElementMatches.pseudo_type">
<span class="sig-name descname"><span class="pre">pseudo_type</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="dom.html#nodriver.cdp.dom.PseudoType" title="nodriver.cdp.dom.PseudoType"><code class="xref py py-class docutils literal notranslate"><span class="pre">PseudoType</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.PseudoElementMatches.pseudo_type" title="Link to this definition">#</a></dt>
<dd><p>Pseudo element type.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.PseudoElementMatches.matches">
<span class="sig-name descname"><span class="pre">matches</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.css.RuleMatch" title="nodriver.cdp.css.RuleMatch"><code class="xref py py-class docutils literal notranslate"><span class="pre">RuleMatch</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.css.PseudoElementMatches.matches" title="Link to this definition">#</a></dt>
<dd><p>Matches of CSS rules applicable to the pseudo style.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.PseudoElementMatches.pseudo_identifier">
<span class="sig-name descname"><span class="pre">pseudo_identifier</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.PseudoElementMatches.pseudo_identifier" title="Link to this definition">#</a></dt>
<dd><p>Pseudo element custom ident.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSAnimationStyle">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">CSSAnimationStyle</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">style</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">name</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#CSSAnimationStyle"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.CSSAnimationStyle" title="Link to this definition">#</a></dt>
<dd><p>CSS style coming from animations with the name of the animation.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSAnimationStyle.style">
<span class="sig-name descname"><span class="pre">style</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.css.CSSStyle" title="nodriver.cdp.css.CSSStyle"><code class="xref py py-class docutils literal notranslate"><span class="pre">CSSStyle</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.CSSAnimationStyle.style" title="Link to this definition">#</a></dt>
<dd><p>The style coming from the animation.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSAnimationStyle.name">
<span class="sig-name descname"><span class="pre">name</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSAnimationStyle.name" title="Link to this definition">#</a></dt>
<dd><p>The name of the animation.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.css.InheritedStyleEntry">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">InheritedStyleEntry</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">matched_css_rules</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">inline_style</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#InheritedStyleEntry"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.InheritedStyleEntry" title="Link to this definition">#</a></dt>
<dd><p>Inherited CSS rule collection from ancestor node.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.InheritedStyleEntry.matched_css_rules">
<span class="sig-name descname"><span class="pre">matched_css_rules</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.css.RuleMatch" title="nodriver.cdp.css.RuleMatch"><code class="xref py py-class docutils literal notranslate"><span class="pre">RuleMatch</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.css.InheritedStyleEntry.matched_css_rules" title="Link to this definition">#</a></dt>
<dd><p>Matches of CSS rules matching the ancestor node in the style inheritance chain.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.InheritedStyleEntry.inline_style">
<span class="sig-name descname"><span class="pre">inline_style</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.css.CSSStyle" title="nodriver.cdp.css.CSSStyle"><code class="xref py py-class docutils literal notranslate"><span class="pre">CSSStyle</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.InheritedStyleEntry.inline_style" title="Link to this definition">#</a></dt>
<dd><p>The ancestor node’s inline style, if any, in the style inheritance chain.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.css.InheritedAnimatedStyleEntry">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">InheritedAnimatedStyleEntry</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">animation_styles</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">transitions_style</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#InheritedAnimatedStyleEntry"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.InheritedAnimatedStyleEntry" title="Link to this definition">#</a></dt>
<dd><p>Inherited CSS style collection for animated styles from ancestor node.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.InheritedAnimatedStyleEntry.animation_styles">
<span class="sig-name descname"><span class="pre">animation_styles</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.css.CSSAnimationStyle" title="nodriver.cdp.css.CSSAnimationStyle"><code class="xref py py-class docutils literal notranslate"><span class="pre">CSSAnimationStyle</span></code></a><span class="pre">]]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.InheritedAnimatedStyleEntry.animation_styles" title="Link to this definition">#</a></dt>
<dd><p>Styles coming from the animations of the ancestor, if any, in the style inheritance chain.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.InheritedAnimatedStyleEntry.transitions_style">
<span class="sig-name descname"><span class="pre">transitions_style</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.css.CSSStyle" title="nodriver.cdp.css.CSSStyle"><code class="xref py py-class docutils literal notranslate"><span class="pre">CSSStyle</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.InheritedAnimatedStyleEntry.transitions_style" title="Link to this definition">#</a></dt>
<dd><p>The style coming from the transitions of the ancestor, if any, in the style inheritance chain.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.css.InheritedPseudoElementMatches">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">InheritedPseudoElementMatches</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">pseudo_elements</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#InheritedPseudoElementMatches"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.InheritedPseudoElementMatches" title="Link to this definition">#</a></dt>
<dd><p>Inherited pseudo element matches from pseudos of an ancestor node.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.InheritedPseudoElementMatches.pseudo_elements">
<span class="sig-name descname"><span class="pre">pseudo_elements</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.css.PseudoElementMatches" title="nodriver.cdp.css.PseudoElementMatches"><code class="xref py py-class docutils literal notranslate"><span class="pre">PseudoElementMatches</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.css.InheritedPseudoElementMatches.pseudo_elements" title="Link to this definition">#</a></dt>
<dd><p>Matches of pseudo styles from the pseudos of an ancestor node.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.css.RuleMatch">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">RuleMatch</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">rule</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">matching_selectors</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#RuleMatch"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.RuleMatch" title="Link to this definition">#</a></dt>
<dd><p>Match data for a CSS rule.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.RuleMatch.rule">
<span class="sig-name descname"><span class="pre">rule</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.css.CSSRule" title="nodriver.cdp.css.CSSRule"><code class="xref py py-class docutils literal notranslate"><span class="pre">CSSRule</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.RuleMatch.rule" title="Link to this definition">#</a></dt>
<dd><p>CSS rule in the match.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.RuleMatch.matching_selectors">
<span class="sig-name descname"><span class="pre">matching_selectors</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.css.RuleMatch.matching_selectors" title="Link to this definition">#</a></dt>
<dd><p>Matching selector indices in the rule’s selectorList selectors (0-based).</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.css.Value">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">Value</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">text</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">range_</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">specificity</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#Value"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.Value" title="Link to this definition">#</a></dt>
<dd><p>Data for a simple selector (these are delimited by commas in a selector list).</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.Value.text">
<span class="sig-name descname"><span class="pre">text</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.Value.text" title="Link to this definition">#</a></dt>
<dd><p>Value text.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.Value.range_">
<span class="sig-name descname"><span class="pre">range_</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.css.SourceRange" title="nodriver.cdp.css.SourceRange"><code class="xref py py-class docutils literal notranslate"><span class="pre">SourceRange</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.Value.range_" title="Link to this definition">#</a></dt>
<dd><p>Value range in the underlying resource (if available).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.Value.specificity">
<span class="sig-name descname"><span class="pre">specificity</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.css.Specificity" title="nodriver.cdp.css.Specificity"><code class="xref py py-class docutils literal notranslate"><span class="pre">Specificity</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.Value.specificity" title="Link to this definition">#</a></dt>
<dd><p>Specificity of the selector.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.css.Specificity">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">Specificity</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">a</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">b</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">c</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#Specificity"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.Specificity" title="Link to this definition">#</a></dt>
<dd><p>Specificity:
<a class="reference external" href="https://drafts.csswg.org/selectors/#specificity-rules">https://drafts.csswg.org/selectors/#specificity-rules</a></p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.Specificity.a">
<span class="sig-name descname"><span class="pre">a</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.Specificity.a" title="Link to this definition">#</a></dt>
<dd><p>The a component, which represents the number of ID selectors.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.Specificity.b">
<span class="sig-name descname"><span class="pre">b</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.Specificity.b" title="Link to this definition">#</a></dt>
<dd><p>The b component, which represents the number of class selectors, attributes selectors, and
pseudo-classes.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.Specificity.c">
<span class="sig-name descname"><span class="pre">c</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.Specificity.c" title="Link to this definition">#</a></dt>
<dd><p>The c component, which represents the number of type selectors and pseudo-elements.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.css.SelectorList">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">SelectorList</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">selectors</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">text</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#SelectorList"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.SelectorList" title="Link to this definition">#</a></dt>
<dd><p>Selector list data.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.SelectorList.selectors">
<span class="sig-name descname"><span class="pre">selectors</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.css.Value" title="nodriver.cdp.css.Value"><code class="xref py py-class docutils literal notranslate"><span class="pre">Value</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.css.SelectorList.selectors" title="Link to this definition">#</a></dt>
<dd><p>Selectors in the list.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.SelectorList.text">
<span class="sig-name descname"><span class="pre">text</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.SelectorList.text" title="Link to this definition">#</a></dt>
<dd><p>Rule selector text.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSStyleSheetHeader">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">CSSStyleSheetHeader</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">style_sheet_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">frame_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">source_url</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">origin</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">title</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">disabled</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">is_inline</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">is_mutable</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">is_constructed</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start_line</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start_column</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">length</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">end_line</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">end_column</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">source_map_url</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">owner_node</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">has_source_url</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">loading_failed</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#CSSStyleSheetHeader"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.CSSStyleSheetHeader" title="Link to this definition">#</a></dt>
<dd><p>CSS stylesheet metainformation.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSStyleSheetHeader.style_sheet_id">
<span class="sig-name descname"><span class="pre">style_sheet_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.css.StyleSheetId" title="nodriver.cdp.css.StyleSheetId"><code class="xref py py-class docutils literal notranslate"><span class="pre">StyleSheetId</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.CSSStyleSheetHeader.style_sheet_id" title="Link to this definition">#</a></dt>
<dd><p>The stylesheet identifier.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSStyleSheetHeader.frame_id">
<span class="sig-name descname"><span class="pre">frame_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="page.html#nodriver.cdp.page.FrameId" title="nodriver.cdp.page.FrameId"><code class="xref py py-class docutils literal notranslate"><span class="pre">FrameId</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.CSSStyleSheetHeader.frame_id" title="Link to this definition">#</a></dt>
<dd><p>Owner frame identifier.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSStyleSheetHeader.source_url">
<span class="sig-name descname"><span class="pre">source_url</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.CSSStyleSheetHeader.source_url" title="Link to this definition">#</a></dt>
<dd><p>Stylesheet resource URL. Empty if this is a constructed stylesheet created using
new CSSStyleSheet() (but non-empty if this is a constructed stylesheet imported
as a CSS module script).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSStyleSheetHeader.origin">
<span class="sig-name descname"><span class="pre">origin</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.css.StyleSheetOrigin" title="nodriver.cdp.css.StyleSheetOrigin"><code class="xref py py-class docutils literal notranslate"><span class="pre">StyleSheetOrigin</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.CSSStyleSheetHeader.origin" title="Link to this definition">#</a></dt>
<dd><p>Stylesheet origin.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSStyleSheetHeader.title">
<span class="sig-name descname"><span class="pre">title</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.CSSStyleSheetHeader.title" title="Link to this definition">#</a></dt>
<dd><p>Stylesheet title.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSStyleSheetHeader.disabled">
<span class="sig-name descname"><span class="pre">disabled</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.CSSStyleSheetHeader.disabled" title="Link to this definition">#</a></dt>
<dd><p>Denotes whether the stylesheet is disabled.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSStyleSheetHeader.is_inline">
<span class="sig-name descname"><span class="pre">is_inline</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.CSSStyleSheetHeader.is_inline" title="Link to this definition">#</a></dt>
<dd><p>Whether this stylesheet is created for STYLE tag by parser. This flag is not set for
document.written STYLE tags.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSStyleSheetHeader.is_mutable">
<span class="sig-name descname"><span class="pre">is_mutable</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.CSSStyleSheetHeader.is_mutable" title="Link to this definition">#</a></dt>
<dd><p>Whether this stylesheet is mutable. Inline stylesheets become mutable
after they have been modified via CSSOM API.
<code class="docutils literal notranslate"><span class="pre">&lt;link&gt;</span></code> element’s stylesheets become mutable only if DevTools modifies them.
Constructed stylesheets (new CSSStyleSheet()) are mutable immediately after creation.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSStyleSheetHeader.is_constructed">
<span class="sig-name descname"><span class="pre">is_constructed</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.CSSStyleSheetHeader.is_constructed" title="Link to this definition">#</a></dt>
<dd><p>True if this stylesheet is created through new CSSStyleSheet() or imported as a
CSS module script.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSStyleSheetHeader.start_line">
<span class="sig-name descname"><span class="pre">start_line</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.CSSStyleSheetHeader.start_line" title="Link to this definition">#</a></dt>
<dd><p>Line offset of the stylesheet within the resource (zero based).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSStyleSheetHeader.start_column">
<span class="sig-name descname"><span class="pre">start_column</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.CSSStyleSheetHeader.start_column" title="Link to this definition">#</a></dt>
<dd><p>Column offset of the stylesheet within the resource (zero based).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSStyleSheetHeader.length">
<span class="sig-name descname"><span class="pre">length</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.CSSStyleSheetHeader.length" title="Link to this definition">#</a></dt>
<dd><p>Size of the content (in characters).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSStyleSheetHeader.end_line">
<span class="sig-name descname"><span class="pre">end_line</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.CSSStyleSheetHeader.end_line" title="Link to this definition">#</a></dt>
<dd><p>Line offset of the end of the stylesheet within the resource (zero based).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSStyleSheetHeader.end_column">
<span class="sig-name descname"><span class="pre">end_column</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.CSSStyleSheetHeader.end_column" title="Link to this definition">#</a></dt>
<dd><p>Column offset of the end of the stylesheet within the resource (zero based).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSStyleSheetHeader.source_map_url">
<span class="sig-name descname"><span class="pre">source_map_url</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSStyleSheetHeader.source_map_url" title="Link to this definition">#</a></dt>
<dd><p>URL of source map associated with the stylesheet (if any).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSStyleSheetHeader.owner_node">
<span class="sig-name descname"><span class="pre">owner_node</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="dom.html#nodriver.cdp.dom.BackendNodeId" title="nodriver.cdp.dom.BackendNodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">BackendNodeId</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSStyleSheetHeader.owner_node" title="Link to this definition">#</a></dt>
<dd><p>The backend id for the owner node of the stylesheet.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSStyleSheetHeader.has_source_url">
<span class="sig-name descname"><span class="pre">has_source_url</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSStyleSheetHeader.has_source_url" title="Link to this definition">#</a></dt>
<dd><p>Whether the sourceURL field value comes from the sourceURL comment.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSStyleSheetHeader.loading_failed">
<span class="sig-name descname"><span class="pre">loading_failed</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSStyleSheetHeader.loading_failed" title="Link to this definition">#</a></dt>
<dd><p>If the style sheet was loaded from a network resource, this indicates when the resource failed to load</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSRule">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">CSSRule</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">selector_list</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">origin</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">style</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">style_sheet_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">nesting_selectors</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">media</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">container_queries</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">supports</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">layers</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">scopes</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">rule_types</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">starting_styles</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#CSSRule"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.CSSRule" title="Link to this definition">#</a></dt>
<dd><p>CSS rule representation.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSRule.selector_list">
<span class="sig-name descname"><span class="pre">selector_list</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.css.SelectorList" title="nodriver.cdp.css.SelectorList"><code class="xref py py-class docutils literal notranslate"><span class="pre">SelectorList</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.CSSRule.selector_list" title="Link to this definition">#</a></dt>
<dd><p>Rule selector data.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSRule.origin">
<span class="sig-name descname"><span class="pre">origin</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.css.StyleSheetOrigin" title="nodriver.cdp.css.StyleSheetOrigin"><code class="xref py py-class docutils literal notranslate"><span class="pre">StyleSheetOrigin</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.CSSRule.origin" title="Link to this definition">#</a></dt>
<dd><p>Parent stylesheet’s origin.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSRule.style">
<span class="sig-name descname"><span class="pre">style</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.css.CSSStyle" title="nodriver.cdp.css.CSSStyle"><code class="xref py py-class docutils literal notranslate"><span class="pre">CSSStyle</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.CSSRule.style" title="Link to this definition">#</a></dt>
<dd><p>Associated style declaration.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSRule.style_sheet_id">
<span class="sig-name descname"><span class="pre">style_sheet_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.css.StyleSheetId" title="nodriver.cdp.css.StyleSheetId"><code class="xref py py-class docutils literal notranslate"><span class="pre">StyleSheetId</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSRule.style_sheet_id" title="Link to this definition">#</a></dt>
<dd><p>The css style sheet identifier (absent for user agent stylesheet and user-specified
stylesheet rules) this rule came from.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSRule.nesting_selectors">
<span class="sig-name descname"><span class="pre">nesting_selectors</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSRule.nesting_selectors" title="Link to this definition">#</a></dt>
<dd><p>Array of selectors from ancestor style rules, sorted by distance from the current rule.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSRule.media">
<span class="sig-name descname"><span class="pre">media</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.css.CSSMedia" title="nodriver.cdp.css.CSSMedia"><code class="xref py py-class docutils literal notranslate"><span class="pre">CSSMedia</span></code></a><span class="pre">]]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSRule.media" title="Link to this definition">#</a></dt>
<dd><p>Media list array (for rules involving media queries). The array enumerates media queries
starting with the innermost one, going outwards.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSRule.container_queries">
<span class="sig-name descname"><span class="pre">container_queries</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.css.CSSContainerQuery" title="nodriver.cdp.css.CSSContainerQuery"><code class="xref py py-class docutils literal notranslate"><span class="pre">CSSContainerQuery</span></code></a><span class="pre">]]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSRule.container_queries" title="Link to this definition">#</a></dt>
<dd><p>Container query list array (for rules involving container queries).
The array enumerates container queries starting with the innermost one, going outwards.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSRule.supports">
<span class="sig-name descname"><span class="pre">supports</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.css.CSSSupports" title="nodriver.cdp.css.CSSSupports"><code class="xref py py-class docutils literal notranslate"><span class="pre">CSSSupports</span></code></a><span class="pre">]]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSRule.supports" title="Link to this definition">#</a></dt>
<dd><p>&#64;supports CSS at-rule array.
The array enumerates &#64;supports at-rules starting with the innermost one, going outwards.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSRule.layers">
<span class="sig-name descname"><span class="pre">layers</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.css.CSSLayer" title="nodriver.cdp.css.CSSLayer"><code class="xref py py-class docutils literal notranslate"><span class="pre">CSSLayer</span></code></a><span class="pre">]]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSRule.layers" title="Link to this definition">#</a></dt>
<dd><p>Cascade layer array. Contains the layer hierarchy that this rule belongs to starting
with the innermost layer and going outwards.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSRule.scopes">
<span class="sig-name descname"><span class="pre">scopes</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.css.CSSScope" title="nodriver.cdp.css.CSSScope"><code class="xref py py-class docutils literal notranslate"><span class="pre">CSSScope</span></code></a><span class="pre">]]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSRule.scopes" title="Link to this definition">#</a></dt>
<dd><p>&#64;scope CSS at-rule array.
The array enumerates &#64;scope at-rules starting with the innermost one, going outwards.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSRule.rule_types">
<span class="sig-name descname"><span class="pre">rule_types</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.css.CSSRuleType" title="nodriver.cdp.css.CSSRuleType"><code class="xref py py-class docutils literal notranslate"><span class="pre">CSSRuleType</span></code></a><span class="pre">]]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSRule.rule_types" title="Link to this definition">#</a></dt>
<dd><p>The array keeps the types of ancestor CSSRules from the innermost going outwards.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSRule.starting_styles">
<span class="sig-name descname"><span class="pre">starting_styles</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.css.CSSStartingStyle" title="nodriver.cdp.css.CSSStartingStyle"><code class="xref py py-class docutils literal notranslate"><span class="pre">CSSStartingStyle</span></code></a><span class="pre">]]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSRule.starting_styles" title="Link to this definition">#</a></dt>
<dd><p>&#64;starting-style CSS at-rule array.
The array enumerates &#64;starting-style at-rules starting with the innermost one, going outwards.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSRuleType">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">CSSRuleType</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">names</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">module</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">qualname</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">boundary</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#CSSRuleType"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.CSSRuleType" title="Link to this definition">#</a></dt>
<dd><p>Enum indicating the type of a CSS rule, used to represent the order of a style rule’s ancestors.
This list only contains rule types that are collected during the ancestor rule collection.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSRuleType.MEDIA_RULE">
<span class="sig-name descname"><span class="pre">MEDIA_RULE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'MediaRule'</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSRuleType.MEDIA_RULE" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSRuleType.SUPPORTS_RULE">
<span class="sig-name descname"><span class="pre">SUPPORTS_RULE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'SupportsRule'</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSRuleType.SUPPORTS_RULE" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSRuleType.CONTAINER_RULE">
<span class="sig-name descname"><span class="pre">CONTAINER_RULE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'ContainerRule'</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSRuleType.CONTAINER_RULE" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSRuleType.LAYER_RULE">
<span class="sig-name descname"><span class="pre">LAYER_RULE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'LayerRule'</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSRuleType.LAYER_RULE" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSRuleType.SCOPE_RULE">
<span class="sig-name descname"><span class="pre">SCOPE_RULE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'ScopeRule'</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSRuleType.SCOPE_RULE" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSRuleType.STYLE_RULE">
<span class="sig-name descname"><span class="pre">STYLE_RULE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'StyleRule'</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSRuleType.STYLE_RULE" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSRuleType.STARTING_STYLE_RULE">
<span class="sig-name descname"><span class="pre">STARTING_STYLE_RULE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'StartingStyleRule'</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSRuleType.STARTING_STYLE_RULE" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.css.RuleUsage">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">RuleUsage</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">style_sheet_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start_offset</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">end_offset</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">used</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#RuleUsage"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.RuleUsage" title="Link to this definition">#</a></dt>
<dd><p>CSS coverage information.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.RuleUsage.style_sheet_id">
<span class="sig-name descname"><span class="pre">style_sheet_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.css.StyleSheetId" title="nodriver.cdp.css.StyleSheetId"><code class="xref py py-class docutils literal notranslate"><span class="pre">StyleSheetId</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.RuleUsage.style_sheet_id" title="Link to this definition">#</a></dt>
<dd><p>The css style sheet identifier (absent for user agent stylesheet and user-specified
stylesheet rules) this rule came from.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.RuleUsage.start_offset">
<span class="sig-name descname"><span class="pre">start_offset</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.RuleUsage.start_offset" title="Link to this definition">#</a></dt>
<dd><p>Offset of the start of the rule (including selector) from the beginning of the stylesheet.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.RuleUsage.end_offset">
<span class="sig-name descname"><span class="pre">end_offset</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.RuleUsage.end_offset" title="Link to this definition">#</a></dt>
<dd><p>Offset of the end of the rule body from the beginning of the stylesheet.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.RuleUsage.used">
<span class="sig-name descname"><span class="pre">used</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.RuleUsage.used" title="Link to this definition">#</a></dt>
<dd><p>Indicates whether the rule was actually used by some element in the page.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.css.SourceRange">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">SourceRange</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">start_line</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start_column</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">end_line</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">end_column</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#SourceRange"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.SourceRange" title="Link to this definition">#</a></dt>
<dd><p>Text range within a resource. All numbers are zero-based.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.SourceRange.start_line">
<span class="sig-name descname"><span class="pre">start_line</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.SourceRange.start_line" title="Link to this definition">#</a></dt>
<dd><p>Start line of range.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.SourceRange.start_column">
<span class="sig-name descname"><span class="pre">start_column</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.SourceRange.start_column" title="Link to this definition">#</a></dt>
<dd><p>Start column of range (inclusive).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.SourceRange.end_line">
<span class="sig-name descname"><span class="pre">end_line</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.SourceRange.end_line" title="Link to this definition">#</a></dt>
<dd><p>End line of range</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.SourceRange.end_column">
<span class="sig-name descname"><span class="pre">end_column</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.SourceRange.end_column" title="Link to this definition">#</a></dt>
<dd><p>End column of range (exclusive).</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.css.ShorthandEntry">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">ShorthandEntry</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">important</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#ShorthandEntry"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.ShorthandEntry" title="Link to this definition">#</a></dt>
<dd><dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.ShorthandEntry.name">
<span class="sig-name descname"><span class="pre">name</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.ShorthandEntry.name" title="Link to this definition">#</a></dt>
<dd><p>Shorthand name.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.ShorthandEntry.value">
<span class="sig-name descname"><span class="pre">value</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.ShorthandEntry.value" title="Link to this definition">#</a></dt>
<dd><p>Shorthand value.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.ShorthandEntry.important">
<span class="sig-name descname"><span class="pre">important</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.ShorthandEntry.important" title="Link to this definition">#</a></dt>
<dd><p>Whether the property has “!important” annotation (implies <code class="docutils literal notranslate"><span class="pre">false</span></code> if absent).</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSComputedStyleProperty">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">CSSComputedStyleProperty</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#CSSComputedStyleProperty"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.CSSComputedStyleProperty" title="Link to this definition">#</a></dt>
<dd><dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSComputedStyleProperty.name">
<span class="sig-name descname"><span class="pre">name</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.CSSComputedStyleProperty.name" title="Link to this definition">#</a></dt>
<dd><p>Computed style property name.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSComputedStyleProperty.value">
<span class="sig-name descname"><span class="pre">value</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.CSSComputedStyleProperty.value" title="Link to this definition">#</a></dt>
<dd><p>Computed style property value.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSStyle">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">CSSStyle</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">css_properties</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">shorthand_entries</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">style_sheet_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">css_text</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">range_</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#CSSStyle"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.CSSStyle" title="Link to this definition">#</a></dt>
<dd><p>CSS style representation.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSStyle.css_properties">
<span class="sig-name descname"><span class="pre">css_properties</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.css.CSSProperty" title="nodriver.cdp.css.CSSProperty"><code class="xref py py-class docutils literal notranslate"><span class="pre">CSSProperty</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSStyle.css_properties" title="Link to this definition">#</a></dt>
<dd><p>CSS properties in the style.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSStyle.shorthand_entries">
<span class="sig-name descname"><span class="pre">shorthand_entries</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.css.ShorthandEntry" title="nodriver.cdp.css.ShorthandEntry"><code class="xref py py-class docutils literal notranslate"><span class="pre">ShorthandEntry</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSStyle.shorthand_entries" title="Link to this definition">#</a></dt>
<dd><p>Computed values for all shorthands found in the style.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSStyle.style_sheet_id">
<span class="sig-name descname"><span class="pre">style_sheet_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.css.StyleSheetId" title="nodriver.cdp.css.StyleSheetId"><code class="xref py py-class docutils literal notranslate"><span class="pre">StyleSheetId</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSStyle.style_sheet_id" title="Link to this definition">#</a></dt>
<dd><p>The css style sheet identifier (absent for user agent stylesheet and user-specified
stylesheet rules) this rule came from.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSStyle.css_text">
<span class="sig-name descname"><span class="pre">css_text</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSStyle.css_text" title="Link to this definition">#</a></dt>
<dd><p>Style declaration text (if available).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSStyle.range_">
<span class="sig-name descname"><span class="pre">range_</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.css.SourceRange" title="nodriver.cdp.css.SourceRange"><code class="xref py py-class docutils literal notranslate"><span class="pre">SourceRange</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSStyle.range_" title="Link to this definition">#</a></dt>
<dd><p>Style declaration range in the enclosing stylesheet (if available).</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSProperty">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">CSSProperty</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">important</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">implicit</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">text</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">parsed_ok</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">disabled</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">range_</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">longhand_properties</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#CSSProperty"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.CSSProperty" title="Link to this definition">#</a></dt>
<dd><p>CSS property declaration data.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSProperty.name">
<span class="sig-name descname"><span class="pre">name</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.CSSProperty.name" title="Link to this definition">#</a></dt>
<dd><p>The property name.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSProperty.value">
<span class="sig-name descname"><span class="pre">value</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.CSSProperty.value" title="Link to this definition">#</a></dt>
<dd><p>The property value.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSProperty.important">
<span class="sig-name descname"><span class="pre">important</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSProperty.important" title="Link to this definition">#</a></dt>
<dd><p>Whether the property has “!important” annotation (implies <code class="docutils literal notranslate"><span class="pre">false</span></code> if absent).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSProperty.implicit">
<span class="sig-name descname"><span class="pre">implicit</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSProperty.implicit" title="Link to this definition">#</a></dt>
<dd><p>Whether the property is implicit (implies <code class="docutils literal notranslate"><span class="pre">false</span></code> if absent).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSProperty.text">
<span class="sig-name descname"><span class="pre">text</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSProperty.text" title="Link to this definition">#</a></dt>
<dd><p>The full property text as specified in the style.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSProperty.parsed_ok">
<span class="sig-name descname"><span class="pre">parsed_ok</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSProperty.parsed_ok" title="Link to this definition">#</a></dt>
<dd><p>Whether the property is understood by the browser (implies <code class="docutils literal notranslate"><span class="pre">true</span></code> if absent).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSProperty.disabled">
<span class="sig-name descname"><span class="pre">disabled</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSProperty.disabled" title="Link to this definition">#</a></dt>
<dd><p>Whether the property is disabled by the user (present for source-based properties only).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSProperty.range_">
<span class="sig-name descname"><span class="pre">range_</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.css.SourceRange" title="nodriver.cdp.css.SourceRange"><code class="xref py py-class docutils literal notranslate"><span class="pre">SourceRange</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSProperty.range_" title="Link to this definition">#</a></dt>
<dd><p>The entire property range in the enclosing style declaration (if available).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSProperty.longhand_properties">
<span class="sig-name descname"><span class="pre">longhand_properties</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.css.CSSProperty" title="nodriver.cdp.css.CSSProperty"><code class="xref py py-class docutils literal notranslate"><span class="pre">CSSProperty</span></code></a><span class="pre">]]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSProperty.longhand_properties" title="Link to this definition">#</a></dt>
<dd><p>Parsed longhand components of this property if it is a shorthand.
This field will be empty if the given property is not a shorthand.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSMedia">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">CSSMedia</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">text</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">source</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">source_url</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">range_</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">style_sheet_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">media_list</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#CSSMedia"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.CSSMedia" title="Link to this definition">#</a></dt>
<dd><p>CSS media rule descriptor.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSMedia.text">
<span class="sig-name descname"><span class="pre">text</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.CSSMedia.text" title="Link to this definition">#</a></dt>
<dd><p>Media query text.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSMedia.source">
<span class="sig-name descname"><span class="pre">source</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.CSSMedia.source" title="Link to this definition">#</a></dt>
<dd><p>“mediaRule” if specified by a &#64;media rule, “importRule” if
specified by an &#64;import rule, “linkedSheet” if specified by a “media” attribute in a linked
stylesheet’s LINK tag, “inlineSheet” if specified by a “media” attribute in an inline
stylesheet’s STYLE tag.</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>Source of the media query</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSMedia.source_url">
<span class="sig-name descname"><span class="pre">source_url</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSMedia.source_url" title="Link to this definition">#</a></dt>
<dd><p>URL of the document containing the media query description.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSMedia.range_">
<span class="sig-name descname"><span class="pre">range_</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.css.SourceRange" title="nodriver.cdp.css.SourceRange"><code class="xref py py-class docutils literal notranslate"><span class="pre">SourceRange</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSMedia.range_" title="Link to this definition">#</a></dt>
<dd><p>The associated rule (&#64;media or &#64;import) header range in the enclosing stylesheet (if
available).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSMedia.style_sheet_id">
<span class="sig-name descname"><span class="pre">style_sheet_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.css.StyleSheetId" title="nodriver.cdp.css.StyleSheetId"><code class="xref py py-class docutils literal notranslate"><span class="pre">StyleSheetId</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSMedia.style_sheet_id" title="Link to this definition">#</a></dt>
<dd><p>Identifier of the stylesheet containing this object (if exists).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSMedia.media_list">
<span class="sig-name descname"><span class="pre">media_list</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.css.MediaQuery" title="nodriver.cdp.css.MediaQuery"><code class="xref py py-class docutils literal notranslate"><span class="pre">MediaQuery</span></code></a><span class="pre">]]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSMedia.media_list" title="Link to this definition">#</a></dt>
<dd><p>Array of media queries.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.css.MediaQuery">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">MediaQuery</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">expressions</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">active</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#MediaQuery"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.MediaQuery" title="Link to this definition">#</a></dt>
<dd><p>Media query descriptor.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.MediaQuery.expressions">
<span class="sig-name descname"><span class="pre">expressions</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.css.MediaQueryExpression" title="nodriver.cdp.css.MediaQueryExpression"><code class="xref py py-class docutils literal notranslate"><span class="pre">MediaQueryExpression</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.css.MediaQuery.expressions" title="Link to this definition">#</a></dt>
<dd><p>Array of media query expressions.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.MediaQuery.active">
<span class="sig-name descname"><span class="pre">active</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.MediaQuery.active" title="Link to this definition">#</a></dt>
<dd><p>Whether the media query condition is satisfied.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.css.MediaQueryExpression">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">MediaQueryExpression</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">unit</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">feature</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value_range</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">computed_length</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#MediaQueryExpression"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.MediaQueryExpression" title="Link to this definition">#</a></dt>
<dd><p>Media query expression descriptor.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.MediaQueryExpression.value">
<span class="sig-name descname"><span class="pre">value</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.MediaQueryExpression.value" title="Link to this definition">#</a></dt>
<dd><p>Media query expression value.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.MediaQueryExpression.unit">
<span class="sig-name descname"><span class="pre">unit</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.MediaQueryExpression.unit" title="Link to this definition">#</a></dt>
<dd><p>Media query expression units.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.MediaQueryExpression.feature">
<span class="sig-name descname"><span class="pre">feature</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.MediaQueryExpression.feature" title="Link to this definition">#</a></dt>
<dd><p>Media query expression feature.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.MediaQueryExpression.value_range">
<span class="sig-name descname"><span class="pre">value_range</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.css.SourceRange" title="nodriver.cdp.css.SourceRange"><code class="xref py py-class docutils literal notranslate"><span class="pre">SourceRange</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.MediaQueryExpression.value_range" title="Link to this definition">#</a></dt>
<dd><p>The associated range of the value text in the enclosing stylesheet (if available).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.MediaQueryExpression.computed_length">
<span class="sig-name descname"><span class="pre">computed_length</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.MediaQueryExpression.computed_length" title="Link to this definition">#</a></dt>
<dd><p>Computed length of media query expression (if applicable).</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSContainerQuery">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">CSSContainerQuery</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">text</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">range_</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">style_sheet_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">name</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">physical_axes</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">logical_axes</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">queries_scroll_state</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#CSSContainerQuery"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.CSSContainerQuery" title="Link to this definition">#</a></dt>
<dd><p>CSS container query rule descriptor.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSContainerQuery.text">
<span class="sig-name descname"><span class="pre">text</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.CSSContainerQuery.text" title="Link to this definition">#</a></dt>
<dd><p>Container query text.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSContainerQuery.range_">
<span class="sig-name descname"><span class="pre">range_</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.css.SourceRange" title="nodriver.cdp.css.SourceRange"><code class="xref py py-class docutils literal notranslate"><span class="pre">SourceRange</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSContainerQuery.range_" title="Link to this definition">#</a></dt>
<dd><p>The associated rule header range in the enclosing stylesheet (if
available).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSContainerQuery.style_sheet_id">
<span class="sig-name descname"><span class="pre">style_sheet_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.css.StyleSheetId" title="nodriver.cdp.css.StyleSheetId"><code class="xref py py-class docutils literal notranslate"><span class="pre">StyleSheetId</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSContainerQuery.style_sheet_id" title="Link to this definition">#</a></dt>
<dd><p>Identifier of the stylesheet containing this object (if exists).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSContainerQuery.name">
<span class="sig-name descname"><span class="pre">name</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSContainerQuery.name" title="Link to this definition">#</a></dt>
<dd><p>Optional name for the container.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSContainerQuery.physical_axes">
<span class="sig-name descname"><span class="pre">physical_axes</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="dom.html#nodriver.cdp.dom.PhysicalAxes" title="nodriver.cdp.dom.PhysicalAxes"><code class="xref py py-class docutils literal notranslate"><span class="pre">PhysicalAxes</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSContainerQuery.physical_axes" title="Link to this definition">#</a></dt>
<dd><p>Optional physical axes queried for the container.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSContainerQuery.logical_axes">
<span class="sig-name descname"><span class="pre">logical_axes</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="dom.html#nodriver.cdp.dom.LogicalAxes" title="nodriver.cdp.dom.LogicalAxes"><code class="xref py py-class docutils literal notranslate"><span class="pre">LogicalAxes</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSContainerQuery.logical_axes" title="Link to this definition">#</a></dt>
<dd><p>Optional logical axes queried for the container.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSContainerQuery.queries_scroll_state">
<span class="sig-name descname"><span class="pre">queries_scroll_state</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSContainerQuery.queries_scroll_state" title="Link to this definition">#</a></dt>
<dd><p>true if the query contains scroll-state() queries.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSSupports">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">CSSSupports</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">text</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">active</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">range_</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">style_sheet_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#CSSSupports"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.CSSSupports" title="Link to this definition">#</a></dt>
<dd><p>CSS Supports at-rule descriptor.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSSupports.text">
<span class="sig-name descname"><span class="pre">text</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.CSSSupports.text" title="Link to this definition">#</a></dt>
<dd><p>Supports rule text.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSSupports.active">
<span class="sig-name descname"><span class="pre">active</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.CSSSupports.active" title="Link to this definition">#</a></dt>
<dd><p>Whether the supports condition is satisfied.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSSupports.range_">
<span class="sig-name descname"><span class="pre">range_</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.css.SourceRange" title="nodriver.cdp.css.SourceRange"><code class="xref py py-class docutils literal notranslate"><span class="pre">SourceRange</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSSupports.range_" title="Link to this definition">#</a></dt>
<dd><p>The associated rule header range in the enclosing stylesheet (if
available).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSSupports.style_sheet_id">
<span class="sig-name descname"><span class="pre">style_sheet_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.css.StyleSheetId" title="nodriver.cdp.css.StyleSheetId"><code class="xref py py-class docutils literal notranslate"><span class="pre">StyleSheetId</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSSupports.style_sheet_id" title="Link to this definition">#</a></dt>
<dd><p>Identifier of the stylesheet containing this object (if exists).</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSScope">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">CSSScope</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">text</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">range_</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">style_sheet_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#CSSScope"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.CSSScope" title="Link to this definition">#</a></dt>
<dd><p>CSS Scope at-rule descriptor.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSScope.text">
<span class="sig-name descname"><span class="pre">text</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.CSSScope.text" title="Link to this definition">#</a></dt>
<dd><p>Scope rule text.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSScope.range_">
<span class="sig-name descname"><span class="pre">range_</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.css.SourceRange" title="nodriver.cdp.css.SourceRange"><code class="xref py py-class docutils literal notranslate"><span class="pre">SourceRange</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSScope.range_" title="Link to this definition">#</a></dt>
<dd><p>The associated rule header range in the enclosing stylesheet (if
available).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSScope.style_sheet_id">
<span class="sig-name descname"><span class="pre">style_sheet_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.css.StyleSheetId" title="nodriver.cdp.css.StyleSheetId"><code class="xref py py-class docutils literal notranslate"><span class="pre">StyleSheetId</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSScope.style_sheet_id" title="Link to this definition">#</a></dt>
<dd><p>Identifier of the stylesheet containing this object (if exists).</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSLayer">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">CSSLayer</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">text</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">range_</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">style_sheet_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#CSSLayer"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.CSSLayer" title="Link to this definition">#</a></dt>
<dd><p>CSS Layer at-rule descriptor.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSLayer.text">
<span class="sig-name descname"><span class="pre">text</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.CSSLayer.text" title="Link to this definition">#</a></dt>
<dd><p>Layer name.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSLayer.range_">
<span class="sig-name descname"><span class="pre">range_</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.css.SourceRange" title="nodriver.cdp.css.SourceRange"><code class="xref py py-class docutils literal notranslate"><span class="pre">SourceRange</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSLayer.range_" title="Link to this definition">#</a></dt>
<dd><p>The associated rule header range in the enclosing stylesheet (if
available).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSLayer.style_sheet_id">
<span class="sig-name descname"><span class="pre">style_sheet_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.css.StyleSheetId" title="nodriver.cdp.css.StyleSheetId"><code class="xref py py-class docutils literal notranslate"><span class="pre">StyleSheetId</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSLayer.style_sheet_id" title="Link to this definition">#</a></dt>
<dd><p>Identifier of the stylesheet containing this object (if exists).</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSStartingStyle">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">CSSStartingStyle</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">range_</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">style_sheet_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#CSSStartingStyle"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.CSSStartingStyle" title="Link to this definition">#</a></dt>
<dd><p>CSS Starting Style at-rule descriptor.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSStartingStyle.range_">
<span class="sig-name descname"><span class="pre">range_</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.css.SourceRange" title="nodriver.cdp.css.SourceRange"><code class="xref py py-class docutils literal notranslate"><span class="pre">SourceRange</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSStartingStyle.range_" title="Link to this definition">#</a></dt>
<dd><p>The associated rule header range in the enclosing stylesheet (if
available).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSStartingStyle.style_sheet_id">
<span class="sig-name descname"><span class="pre">style_sheet_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.css.StyleSheetId" title="nodriver.cdp.css.StyleSheetId"><code class="xref py py-class docutils literal notranslate"><span class="pre">StyleSheetId</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSStartingStyle.style_sheet_id" title="Link to this definition">#</a></dt>
<dd><p>Identifier of the stylesheet containing this object (if exists).</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSLayerData">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">CSSLayerData</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">order</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">sub_layers</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#CSSLayerData"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.CSSLayerData" title="Link to this definition">#</a></dt>
<dd><p>CSS Layer data.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSLayerData.name">
<span class="sig-name descname"><span class="pre">name</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.CSSLayerData.name" title="Link to this definition">#</a></dt>
<dd><p>Layer name.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSLayerData.order">
<span class="sig-name descname"><span class="pre">order</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.CSSLayerData.order" title="Link to this definition">#</a></dt>
<dd><p>Layer order. The order determines the order of the layer in the cascade order.
A higher number has higher priority in the cascade order.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSLayerData.sub_layers">
<span class="sig-name descname"><span class="pre">sub_layers</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.css.CSSLayerData" title="nodriver.cdp.css.CSSLayerData"><code class="xref py py-class docutils literal notranslate"><span class="pre">CSSLayerData</span></code></a><span class="pre">]]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSLayerData.sub_layers" title="Link to this definition">#</a></dt>
<dd><p>Direct sub-layers</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.css.PlatformFontUsage">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">PlatformFontUsage</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">family_name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">post_script_name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">is_custom_font</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">glyph_count</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#PlatformFontUsage"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.PlatformFontUsage" title="Link to this definition">#</a></dt>
<dd><p>Information about amount of glyphs that were rendered with given font.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.PlatformFontUsage.family_name">
<span class="sig-name descname"><span class="pre">family_name</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.PlatformFontUsage.family_name" title="Link to this definition">#</a></dt>
<dd><p>Font’s family name reported by platform.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.PlatformFontUsage.post_script_name">
<span class="sig-name descname"><span class="pre">post_script_name</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.PlatformFontUsage.post_script_name" title="Link to this definition">#</a></dt>
<dd><p>Font’s PostScript name reported by platform.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.PlatformFontUsage.is_custom_font">
<span class="sig-name descname"><span class="pre">is_custom_font</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.PlatformFontUsage.is_custom_font" title="Link to this definition">#</a></dt>
<dd><p>Indicates if the font was downloaded or resolved locally.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.PlatformFontUsage.glyph_count">
<span class="sig-name descname"><span class="pre">glyph_count</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.PlatformFontUsage.glyph_count" title="Link to this definition">#</a></dt>
<dd><p>Amount of glyphs that were rendered with this font.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.css.FontVariationAxis">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">FontVariationAxis</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">tag</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">min_value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">max_value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">default_value</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#FontVariationAxis"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.FontVariationAxis" title="Link to this definition">#</a></dt>
<dd><p>Information about font variation axes for variable fonts</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.FontVariationAxis.tag">
<span class="sig-name descname"><span class="pre">tag</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.FontVariationAxis.tag" title="Link to this definition">#</a></dt>
<dd><p>The font-variation-setting tag (a.k.a. “axis tag”).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.FontVariationAxis.name">
<span class="sig-name descname"><span class="pre">name</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.FontVariationAxis.name" title="Link to this definition">#</a></dt>
<dd><p>Human-readable variation name in the default language (normally, “en”).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.FontVariationAxis.min_value">
<span class="sig-name descname"><span class="pre">min_value</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.FontVariationAxis.min_value" title="Link to this definition">#</a></dt>
<dd><p>The minimum value (inclusive) the font supports for this tag.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.FontVariationAxis.max_value">
<span class="sig-name descname"><span class="pre">max_value</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.FontVariationAxis.max_value" title="Link to this definition">#</a></dt>
<dd><p>The maximum value (inclusive) the font supports for this tag.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.FontVariationAxis.default_value">
<span class="sig-name descname"><span class="pre">default_value</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.FontVariationAxis.default_value" title="Link to this definition">#</a></dt>
<dd><p>The default value.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.css.FontFace">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">FontFace</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">font_family</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">font_style</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">font_variant</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">font_weight</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">font_stretch</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">font_display</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">unicode_range</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">src</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">platform_font_family</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">font_variation_axes</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#FontFace"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.FontFace" title="Link to this definition">#</a></dt>
<dd><p>Properties of a web font: <a class="reference external" href="https://www.w3.org/TR/2008/REC-CSS2-20080411/fonts.html#font-descriptions">https://www.w3.org/TR/2008/REC-CSS2-20080411/fonts.html#font-descriptions</a>
and additional information such as platformFontFamily and fontVariationAxes.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.FontFace.font_family">
<span class="sig-name descname"><span class="pre">font_family</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.FontFace.font_family" title="Link to this definition">#</a></dt>
<dd><p>The font-family.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.FontFace.font_style">
<span class="sig-name descname"><span class="pre">font_style</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.FontFace.font_style" title="Link to this definition">#</a></dt>
<dd><p>The font-style.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.FontFace.font_variant">
<span class="sig-name descname"><span class="pre">font_variant</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.FontFace.font_variant" title="Link to this definition">#</a></dt>
<dd><p>The font-variant.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.FontFace.font_weight">
<span class="sig-name descname"><span class="pre">font_weight</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.FontFace.font_weight" title="Link to this definition">#</a></dt>
<dd><p>The font-weight.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.FontFace.font_stretch">
<span class="sig-name descname"><span class="pre">font_stretch</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.FontFace.font_stretch" title="Link to this definition">#</a></dt>
<dd><p>The font-stretch.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.FontFace.font_display">
<span class="sig-name descname"><span class="pre">font_display</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.FontFace.font_display" title="Link to this definition">#</a></dt>
<dd><p>The font-display.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.FontFace.unicode_range">
<span class="sig-name descname"><span class="pre">unicode_range</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.FontFace.unicode_range" title="Link to this definition">#</a></dt>
<dd><p>The unicode-range.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.FontFace.src">
<span class="sig-name descname"><span class="pre">src</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.FontFace.src" title="Link to this definition">#</a></dt>
<dd><p>The src.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.FontFace.platform_font_family">
<span class="sig-name descname"><span class="pre">platform_font_family</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.FontFace.platform_font_family" title="Link to this definition">#</a></dt>
<dd><p>The resolved platform font family</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.FontFace.font_variation_axes">
<span class="sig-name descname"><span class="pre">font_variation_axes</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.css.FontVariationAxis" title="nodriver.cdp.css.FontVariationAxis"><code class="xref py py-class docutils literal notranslate"><span class="pre">FontVariationAxis</span></code></a><span class="pre">]]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.FontFace.font_variation_axes" title="Link to this definition">#</a></dt>
<dd><p>Available variation settings (a.k.a. “axes”).</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSTryRule">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">CSSTryRule</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">origin</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">style</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">style_sheet_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#CSSTryRule"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.CSSTryRule" title="Link to this definition">#</a></dt>
<dd><p>CSS try rule representation.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSTryRule.origin">
<span class="sig-name descname"><span class="pre">origin</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.css.StyleSheetOrigin" title="nodriver.cdp.css.StyleSheetOrigin"><code class="xref py py-class docutils literal notranslate"><span class="pre">StyleSheetOrigin</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.CSSTryRule.origin" title="Link to this definition">#</a></dt>
<dd><p>Parent stylesheet’s origin.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSTryRule.style">
<span class="sig-name descname"><span class="pre">style</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.css.CSSStyle" title="nodriver.cdp.css.CSSStyle"><code class="xref py py-class docutils literal notranslate"><span class="pre">CSSStyle</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.CSSTryRule.style" title="Link to this definition">#</a></dt>
<dd><p>Associated style declaration.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSTryRule.style_sheet_id">
<span class="sig-name descname"><span class="pre">style_sheet_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.css.StyleSheetId" title="nodriver.cdp.css.StyleSheetId"><code class="xref py py-class docutils literal notranslate"><span class="pre">StyleSheetId</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSTryRule.style_sheet_id" title="Link to this definition">#</a></dt>
<dd><p>The css style sheet identifier (absent for user agent stylesheet and user-specified
stylesheet rules) this rule came from.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSPositionTryRule">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">CSSPositionTryRule</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">origin</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">style</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">active</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">style_sheet_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#CSSPositionTryRule"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.CSSPositionTryRule" title="Link to this definition">#</a></dt>
<dd><p>CSS &#64;position-try rule representation.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSPositionTryRule.name">
<span class="sig-name descname"><span class="pre">name</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.css.Value" title="nodriver.cdp.css.Value"><code class="xref py py-class docutils literal notranslate"><span class="pre">Value</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.CSSPositionTryRule.name" title="Link to this definition">#</a></dt>
<dd><p>The prelude dashed-ident name</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSPositionTryRule.origin">
<span class="sig-name descname"><span class="pre">origin</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.css.StyleSheetOrigin" title="nodriver.cdp.css.StyleSheetOrigin"><code class="xref py py-class docutils literal notranslate"><span class="pre">StyleSheetOrigin</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.CSSPositionTryRule.origin" title="Link to this definition">#</a></dt>
<dd><p>Parent stylesheet’s origin.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSPositionTryRule.style">
<span class="sig-name descname"><span class="pre">style</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.css.CSSStyle" title="nodriver.cdp.css.CSSStyle"><code class="xref py py-class docutils literal notranslate"><span class="pre">CSSStyle</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.CSSPositionTryRule.style" title="Link to this definition">#</a></dt>
<dd><p>Associated style declaration.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSPositionTryRule.active">
<span class="sig-name descname"><span class="pre">active</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.CSSPositionTryRule.active" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSPositionTryRule.style_sheet_id">
<span class="sig-name descname"><span class="pre">style_sheet_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.css.StyleSheetId" title="nodriver.cdp.css.StyleSheetId"><code class="xref py py-class docutils literal notranslate"><span class="pre">StyleSheetId</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSPositionTryRule.style_sheet_id" title="Link to this definition">#</a></dt>
<dd><p>The css style sheet identifier (absent for user agent stylesheet and user-specified
stylesheet rules) this rule came from.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSKeyframesRule">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">CSSKeyframesRule</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">animation_name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">keyframes</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#CSSKeyframesRule"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.CSSKeyframesRule" title="Link to this definition">#</a></dt>
<dd><p>CSS keyframes rule representation.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSKeyframesRule.animation_name">
<span class="sig-name descname"><span class="pre">animation_name</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.css.Value" title="nodriver.cdp.css.Value"><code class="xref py py-class docutils literal notranslate"><span class="pre">Value</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.CSSKeyframesRule.animation_name" title="Link to this definition">#</a></dt>
<dd><p>Animation name.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSKeyframesRule.keyframes">
<span class="sig-name descname"><span class="pre">keyframes</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.css.CSSKeyframeRule" title="nodriver.cdp.css.CSSKeyframeRule"><code class="xref py py-class docutils literal notranslate"><span class="pre">CSSKeyframeRule</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSKeyframesRule.keyframes" title="Link to this definition">#</a></dt>
<dd><p>List of keyframes.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSPropertyRegistration">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">CSSPropertyRegistration</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">property_name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">inherits</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">syntax</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">initial_value</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#CSSPropertyRegistration"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.CSSPropertyRegistration" title="Link to this definition">#</a></dt>
<dd><p>Representation of a custom property registration through CSS.registerProperty</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSPropertyRegistration.property_name">
<span class="sig-name descname"><span class="pre">property_name</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.CSSPropertyRegistration.property_name" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSPropertyRegistration.inherits">
<span class="sig-name descname"><span class="pre">inherits</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.CSSPropertyRegistration.inherits" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSPropertyRegistration.syntax">
<span class="sig-name descname"><span class="pre">syntax</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.CSSPropertyRegistration.syntax" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSPropertyRegistration.initial_value">
<span class="sig-name descname"><span class="pre">initial_value</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.css.Value" title="nodriver.cdp.css.Value"><code class="xref py py-class docutils literal notranslate"><span class="pre">Value</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSPropertyRegistration.initial_value" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSFontPaletteValuesRule">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">CSSFontPaletteValuesRule</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">origin</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">font_palette_name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">style</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">style_sheet_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#CSSFontPaletteValuesRule"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.CSSFontPaletteValuesRule" title="Link to this definition">#</a></dt>
<dd><p>CSS font-palette-values rule representation.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSFontPaletteValuesRule.origin">
<span class="sig-name descname"><span class="pre">origin</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.css.StyleSheetOrigin" title="nodriver.cdp.css.StyleSheetOrigin"><code class="xref py py-class docutils literal notranslate"><span class="pre">StyleSheetOrigin</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.CSSFontPaletteValuesRule.origin" title="Link to this definition">#</a></dt>
<dd><p>Parent stylesheet’s origin.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSFontPaletteValuesRule.font_palette_name">
<span class="sig-name descname"><span class="pre">font_palette_name</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.css.Value" title="nodriver.cdp.css.Value"><code class="xref py py-class docutils literal notranslate"><span class="pre">Value</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.CSSFontPaletteValuesRule.font_palette_name" title="Link to this definition">#</a></dt>
<dd><p>Associated font palette name.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSFontPaletteValuesRule.style">
<span class="sig-name descname"><span class="pre">style</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.css.CSSStyle" title="nodriver.cdp.css.CSSStyle"><code class="xref py py-class docutils literal notranslate"><span class="pre">CSSStyle</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.CSSFontPaletteValuesRule.style" title="Link to this definition">#</a></dt>
<dd><p>Associated style declaration.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSFontPaletteValuesRule.style_sheet_id">
<span class="sig-name descname"><span class="pre">style_sheet_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.css.StyleSheetId" title="nodriver.cdp.css.StyleSheetId"><code class="xref py py-class docutils literal notranslate"><span class="pre">StyleSheetId</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSFontPaletteValuesRule.style_sheet_id" title="Link to this definition">#</a></dt>
<dd><p>The css style sheet identifier (absent for user agent stylesheet and user-specified
stylesheet rules) this rule came from.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSPropertyRule">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">CSSPropertyRule</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">origin</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">property_name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">style</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">style_sheet_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#CSSPropertyRule"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.CSSPropertyRule" title="Link to this definition">#</a></dt>
<dd><p>CSS property at-rule representation.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSPropertyRule.origin">
<span class="sig-name descname"><span class="pre">origin</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.css.StyleSheetOrigin" title="nodriver.cdp.css.StyleSheetOrigin"><code class="xref py py-class docutils literal notranslate"><span class="pre">StyleSheetOrigin</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.CSSPropertyRule.origin" title="Link to this definition">#</a></dt>
<dd><p>Parent stylesheet’s origin.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSPropertyRule.property_name">
<span class="sig-name descname"><span class="pre">property_name</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.css.Value" title="nodriver.cdp.css.Value"><code class="xref py py-class docutils literal notranslate"><span class="pre">Value</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.CSSPropertyRule.property_name" title="Link to this definition">#</a></dt>
<dd><p>Associated property name.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSPropertyRule.style">
<span class="sig-name descname"><span class="pre">style</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.css.CSSStyle" title="nodriver.cdp.css.CSSStyle"><code class="xref py py-class docutils literal notranslate"><span class="pre">CSSStyle</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.CSSPropertyRule.style" title="Link to this definition">#</a></dt>
<dd><p>Associated style declaration.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSPropertyRule.style_sheet_id">
<span class="sig-name descname"><span class="pre">style_sheet_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.css.StyleSheetId" title="nodriver.cdp.css.StyleSheetId"><code class="xref py py-class docutils literal notranslate"><span class="pre">StyleSheetId</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSPropertyRule.style_sheet_id" title="Link to this definition">#</a></dt>
<dd><p>The css style sheet identifier (absent for user agent stylesheet and user-specified
stylesheet rules) this rule came from.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSFunctionParameter">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">CSSFunctionParameter</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type_</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#CSSFunctionParameter"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.CSSFunctionParameter" title="Link to this definition">#</a></dt>
<dd><p>CSS function argument representation.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSFunctionParameter.name">
<span class="sig-name descname"><span class="pre">name</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.CSSFunctionParameter.name" title="Link to this definition">#</a></dt>
<dd><p>The parameter name.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSFunctionParameter.type_">
<span class="sig-name descname"><span class="pre">type_</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.CSSFunctionParameter.type_" title="Link to this definition">#</a></dt>
<dd><p>The parameter type.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSFunctionConditionNode">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">CSSFunctionConditionNode</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">children</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">condition_text</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">media</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">container_queries</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">supports</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#CSSFunctionConditionNode"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.CSSFunctionConditionNode" title="Link to this definition">#</a></dt>
<dd><p>CSS function conditional block representation.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSFunctionConditionNode.children">
<span class="sig-name descname"><span class="pre">children</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.css.CSSFunctionNode" title="nodriver.cdp.css.CSSFunctionNode"><code class="xref py py-class docutils literal notranslate"><span class="pre">CSSFunctionNode</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSFunctionConditionNode.children" title="Link to this definition">#</a></dt>
<dd><p>Block body.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSFunctionConditionNode.condition_text">
<span class="sig-name descname"><span class="pre">condition_text</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.CSSFunctionConditionNode.condition_text" title="Link to this definition">#</a></dt>
<dd><p>The condition text.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSFunctionConditionNode.media">
<span class="sig-name descname"><span class="pre">media</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.css.CSSMedia" title="nodriver.cdp.css.CSSMedia"><code class="xref py py-class docutils literal notranslate"><span class="pre">CSSMedia</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSFunctionConditionNode.media" title="Link to this definition">#</a></dt>
<dd><p>Media query for this conditional block. Only one type of condition should be set.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSFunctionConditionNode.container_queries">
<span class="sig-name descname"><span class="pre">container_queries</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.css.CSSContainerQuery" title="nodriver.cdp.css.CSSContainerQuery"><code class="xref py py-class docutils literal notranslate"><span class="pre">CSSContainerQuery</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSFunctionConditionNode.container_queries" title="Link to this definition">#</a></dt>
<dd><p>Container query for this conditional block. Only one type of condition should be set.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSFunctionConditionNode.supports">
<span class="sig-name descname"><span class="pre">supports</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.css.CSSSupports" title="nodriver.cdp.css.CSSSupports"><code class="xref py py-class docutils literal notranslate"><span class="pre">CSSSupports</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSFunctionConditionNode.supports" title="Link to this definition">#</a></dt>
<dd><p>&#64;supports CSS at-rule condition. Only one type of condition should be set.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSFunctionNode">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">CSSFunctionNode</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">condition</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">style</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#CSSFunctionNode"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.CSSFunctionNode" title="Link to this definition">#</a></dt>
<dd><p>Section of the body of a CSS function rule.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSFunctionNode.condition">
<span class="sig-name descname"><span class="pre">condition</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.css.CSSFunctionConditionNode" title="nodriver.cdp.css.CSSFunctionConditionNode"><code class="xref py py-class docutils literal notranslate"><span class="pre">CSSFunctionConditionNode</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSFunctionNode.condition" title="Link to this definition">#</a></dt>
<dd><p>A conditional block. If set, style should not be set.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSFunctionNode.style">
<span class="sig-name descname"><span class="pre">style</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.css.CSSStyle" title="nodriver.cdp.css.CSSStyle"><code class="xref py py-class docutils literal notranslate"><span class="pre">CSSStyle</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSFunctionNode.style" title="Link to this definition">#</a></dt>
<dd><p>Values set by this node. If set, condition should not be set.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSFunctionRule">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">CSSFunctionRule</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">origin</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">parameters</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">children</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">style_sheet_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#CSSFunctionRule"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.CSSFunctionRule" title="Link to this definition">#</a></dt>
<dd><p>CSS function at-rule representation.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSFunctionRule.name">
<span class="sig-name descname"><span class="pre">name</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.css.Value" title="nodriver.cdp.css.Value"><code class="xref py py-class docutils literal notranslate"><span class="pre">Value</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.CSSFunctionRule.name" title="Link to this definition">#</a></dt>
<dd><p>Name of the function.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSFunctionRule.origin">
<span class="sig-name descname"><span class="pre">origin</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.css.StyleSheetOrigin" title="nodriver.cdp.css.StyleSheetOrigin"><code class="xref py py-class docutils literal notranslate"><span class="pre">StyleSheetOrigin</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.CSSFunctionRule.origin" title="Link to this definition">#</a></dt>
<dd><p>Parent stylesheet’s origin.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSFunctionRule.parameters">
<span class="sig-name descname"><span class="pre">parameters</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.css.CSSFunctionParameter" title="nodriver.cdp.css.CSSFunctionParameter"><code class="xref py py-class docutils literal notranslate"><span class="pre">CSSFunctionParameter</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSFunctionRule.parameters" title="Link to this definition">#</a></dt>
<dd><p>List of parameters.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSFunctionRule.children">
<span class="sig-name descname"><span class="pre">children</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.css.CSSFunctionNode" title="nodriver.cdp.css.CSSFunctionNode"><code class="xref py py-class docutils literal notranslate"><span class="pre">CSSFunctionNode</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSFunctionRule.children" title="Link to this definition">#</a></dt>
<dd><p>Function body.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSFunctionRule.style_sheet_id">
<span class="sig-name descname"><span class="pre">style_sheet_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.css.StyleSheetId" title="nodriver.cdp.css.StyleSheetId"><code class="xref py py-class docutils literal notranslate"><span class="pre">StyleSheetId</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSFunctionRule.style_sheet_id" title="Link to this definition">#</a></dt>
<dd><p>The css style sheet identifier (absent for user agent stylesheet and user-specified
stylesheet rules) this rule came from.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSKeyframeRule">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">CSSKeyframeRule</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">origin</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">key_text</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">style</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">style_sheet_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#CSSKeyframeRule"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.CSSKeyframeRule" title="Link to this definition">#</a></dt>
<dd><p>CSS keyframe rule representation.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSKeyframeRule.origin">
<span class="sig-name descname"><span class="pre">origin</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.css.StyleSheetOrigin" title="nodriver.cdp.css.StyleSheetOrigin"><code class="xref py py-class docutils literal notranslate"><span class="pre">StyleSheetOrigin</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.CSSKeyframeRule.origin" title="Link to this definition">#</a></dt>
<dd><p>Parent stylesheet’s origin.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSKeyframeRule.key_text">
<span class="sig-name descname"><span class="pre">key_text</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.css.Value" title="nodriver.cdp.css.Value"><code class="xref py py-class docutils literal notranslate"><span class="pre">Value</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.CSSKeyframeRule.key_text" title="Link to this definition">#</a></dt>
<dd><p>Associated key text.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSKeyframeRule.style">
<span class="sig-name descname"><span class="pre">style</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.css.CSSStyle" title="nodriver.cdp.css.CSSStyle"><code class="xref py py-class docutils literal notranslate"><span class="pre">CSSStyle</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.CSSKeyframeRule.style" title="Link to this definition">#</a></dt>
<dd><p>Associated style declaration.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.CSSKeyframeRule.style_sheet_id">
<span class="sig-name descname"><span class="pre">style_sheet_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.css.StyleSheetId" title="nodriver.cdp.css.StyleSheetId"><code class="xref py py-class docutils literal notranslate"><span class="pre">StyleSheetId</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.css.CSSKeyframeRule.style_sheet_id" title="Link to this definition">#</a></dt>
<dd><p>The css style sheet identifier (absent for user agent stylesheet and user-specified
stylesheet rules) this rule came from.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.css.StyleDeclarationEdit">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">StyleDeclarationEdit</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">style_sheet_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">range_</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">text</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#StyleDeclarationEdit"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.StyleDeclarationEdit" title="Link to this definition">#</a></dt>
<dd><p>A descriptor of operation to mutate style declaration text.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.StyleDeclarationEdit.style_sheet_id">
<span class="sig-name descname"><span class="pre">style_sheet_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.css.StyleSheetId" title="nodriver.cdp.css.StyleSheetId"><code class="xref py py-class docutils literal notranslate"><span class="pre">StyleSheetId</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.StyleDeclarationEdit.style_sheet_id" title="Link to this definition">#</a></dt>
<dd><p>The css style sheet identifier.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.StyleDeclarationEdit.range_">
<span class="sig-name descname"><span class="pre">range_</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.css.SourceRange" title="nodriver.cdp.css.SourceRange"><code class="xref py py-class docutils literal notranslate"><span class="pre">SourceRange</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.StyleDeclarationEdit.range_" title="Link to this definition">#</a></dt>
<dd><p>The range of the style text in the enclosing stylesheet.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.StyleDeclarationEdit.text">
<span class="sig-name descname"><span class="pre">text</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.StyleDeclarationEdit.text" title="Link to this definition">#</a></dt>
<dd><p>New style text.</p>
</dd></dl>

</dd></dl>

</section>
<section id="commands">
<h2>Commands<a class="headerlink" href="#commands" title="Link to this heading">#</a></h2>
<p>Each command is a generator function. The return
type <code class="docutils literal notranslate"><span class="pre">Generator[x,</span> <span class="pre">y,</span> <span class="pre">z]</span></code> indicates that the generator
<em>yields</em> arguments of type <code class="docutils literal notranslate"><span class="pre">x</span></code>, it must be resumed with
an argument of type <code class="docutils literal notranslate"><span class="pre">y</span></code>, and it returns type <code class="docutils literal notranslate"><span class="pre">z</span></code>. In
this library, types <code class="docutils literal notranslate"><span class="pre">x</span></code> and <code class="docutils literal notranslate"><span class="pre">y</span></code> are the same for all
commands, and <code class="docutils literal notranslate"><span class="pre">z</span></code> is the return type you should pay attention
to. For more information, see
<a class="reference internal" href="../../readme.html#getting-started-commands"><span class="std std-ref">Getting Started: Commands</span></a>.</p>
<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.css.add_rule">
<span class="sig-name descname"><span class="pre">add_rule</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">style_sheet_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">rule_text</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">location</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">node_for_property_syntax_validation</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#add_rule"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.add_rule" title="Link to this definition">#</a></dt>
<dd><p>Inserts a new rule with the given <code class="docutils literal notranslate"><span class="pre">ruleText</span></code> in a stylesheet with given <code class="docutils literal notranslate"><span class="pre">styleSheetId</span></code>, at the
position specified by <code class="docutils literal notranslate"><span class="pre">location</span></code>.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>style_sheet_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.css.StyleSheetId" title="nodriver.cdp.css.StyleSheetId"><code class="xref py py-class docutils literal notranslate"><span class="pre">StyleSheetId</span></code></a></span>) – The css style sheet identifier where a new rule should be inserted.</p></li>
<li><p><strong>rule_text</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – The text of a new rule.</p></li>
<li><p><strong>location</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.css.SourceRange" title="nodriver.cdp.css.SourceRange"><code class="xref py py-class docutils literal notranslate"><span class="pre">SourceRange</span></code></a></span>) – Text position of a new rule in the target style sheet.</p></li>
<li><p><strong>node_for_property_syntax_validation</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="dom.html#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a>]</span>) – <strong>(EXPERIMENTAL)</strong> <em>(Optional)</em> NodeId for the DOM node in whose context custom property declarations for registered properties should be validated. If omitted, declarations in the new rule text can only be validated statically, which may produce incorrect results if the declaration contains a var() for example.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference internal" href="#nodriver.cdp.css.CSSRule" title="nodriver.cdp.css.CSSRule"><code class="xref py py-class docutils literal notranslate"><span class="pre">CSSRule</span></code></a>]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>The newly created rule.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.css.collect_class_names">
<span class="sig-name descname"><span class="pre">collect_class_names</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">style_sheet_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#collect_class_names"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.collect_class_names" title="Link to this definition">#</a></dt>
<dd><p>Returns all class names from specified stylesheet.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>style_sheet_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.css.StyleSheetId" title="nodriver.cdp.css.StyleSheetId"><code class="xref py py-class docutils literal notranslate"><span class="pre">StyleSheetId</span></code></a></span>) – </p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>Class name list.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.css.create_style_sheet">
<span class="sig-name descname"><span class="pre">create_style_sheet</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">frame_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">force</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#create_style_sheet"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.create_style_sheet" title="Link to this definition">#</a></dt>
<dd><p>Creates a new special “via-inspector” stylesheet in the frame with given <code class="docutils literal notranslate"><span class="pre">frameId</span></code>.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>frame_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="page.html#nodriver.cdp.page.FrameId" title="nodriver.cdp.page.FrameId"><code class="xref py py-class docutils literal notranslate"><span class="pre">FrameId</span></code></a></span>) – Identifier of the frame where “via-inspector” stylesheet should be created.</p></li>
<li><p><strong>force</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <em>(Optional)</em> If true, creates a new stylesheet for every call. If false, returns a stylesheet previously created by a call with force=false for the frame’s document if it exists or creates a new stylesheet (default: false).</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference internal" href="#nodriver.cdp.css.StyleSheetId" title="nodriver.cdp.css.StyleSheetId"><code class="xref py py-class docutils literal notranslate"><span class="pre">StyleSheetId</span></code></a>]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>Identifier of the created “via-inspector” stylesheet.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.css.disable">
<span class="sig-name descname"><span class="pre">disable</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#disable"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.disable" title="Link to this definition">#</a></dt>
<dd><p>Disables the CSS agent for the given page.</p>
<dl class="field-list simple">
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.css.enable">
<span class="sig-name descname"><span class="pre">enable</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#enable"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.enable" title="Link to this definition">#</a></dt>
<dd><p>Enables the CSS agent for the given page. Clients should not assume that the CSS agent has been
enabled until the result of this command is received.</p>
<dl class="field-list simple">
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.css.force_pseudo_state">
<span class="sig-name descname"><span class="pre">force_pseudo_state</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">node_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">forced_pseudo_classes</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#force_pseudo_state"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.force_pseudo_state" title="Link to this definition">#</a></dt>
<dd><p>Ensures that the given node will have specified pseudo-classes whenever its style is computed by
the browser.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="dom.html#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a></span>) – The element id for which to force the pseudo state.</p></li>
<li><p><strong>forced_pseudo_classes</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]</span>) – Element pseudo classes to force when computing the element’s style.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.css.force_starting_style">
<span class="sig-name descname"><span class="pre">force_starting_style</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">node_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">forced</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#force_starting_style"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.force_starting_style" title="Link to this definition">#</a></dt>
<dd><p>Ensures that the given node is in its starting-style state.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="dom.html#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a></span>) – The element id for which to force the starting-style state.</p></li>
<li><p><strong>forced</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a></span>) – Boolean indicating if this is on or off.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.css.get_animated_styles_for_node">
<span class="sig-name descname"><span class="pre">get_animated_styles_for_node</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">node_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#get_animated_styles_for_node"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.get_animated_styles_for_node" title="Link to this definition">#</a></dt>
<dd><p>Returns the styles coming from animations &amp; transitions
including the animation &amp; transition styles coming from inheritance chain.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="dom.html#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a></span>) – </p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Tuple" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Tuple</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.css.CSSAnimationStyle" title="nodriver.cdp.css.CSSAnimationStyle"><code class="xref py py-class docutils literal notranslate"><span class="pre">CSSAnimationStyle</span></code></a>]], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="#nodriver.cdp.css.CSSStyle" title="nodriver.cdp.css.CSSStyle"><code class="xref py py-class docutils literal notranslate"><span class="pre">CSSStyle</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.css.InheritedAnimatedStyleEntry" title="nodriver.cdp.css.InheritedAnimatedStyleEntry"><code class="xref py py-class docutils literal notranslate"><span class="pre">InheritedAnimatedStyleEntry</span></code></a>]]]]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p><p>A tuple with the following items:</p>
<ol class="arabic simple" start="0">
<li><p><strong>animationStyles</strong> - <em>(Optional)</em> Styles coming from animations.</p></li>
<li><p><strong>transitionsStyle</strong> - <em>(Optional)</em> Style coming from transitions.</p></li>
<li><p><strong>inherited</strong> - <em>(Optional)</em> Inherited style entries for animationsStyle and transitionsStyle from the inheritance chain of the element.</p></li>
</ol>
</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.css.get_background_colors">
<span class="sig-name descname"><span class="pre">get_background_colors</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">node_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#get_background_colors"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.get_background_colors" title="Link to this definition">#</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="dom.html#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a></span>) – Id of the node to get background colors for.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Tuple" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Tuple</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]]]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p><p>A tuple with the following items:</p>
<ol class="arabic simple" start="0">
<li><p><strong>backgroundColors</strong> - <em>(Optional)</em> The range of background colors behind this element, if it contains any visible text. If no visible text is present, this will be undefined. In the case of a flat background color, this will consist of simply that color. In the case of a gradient, this will consist of each of the color stops. For anything more complicated, this will be an empty array. Images will be ignored (as if the image had failed to load).</p></li>
<li><p><strong>computedFontSize</strong> - <em>(Optional)</em> The computed font size for this node, as a CSS computed value string (e.g. ‘12px’).</p></li>
<li><p><strong>computedFontWeight</strong> - <em>(Optional)</em> The computed font weight for this node, as a CSS computed value string (e.g. ‘normal’ or ‘100’).</p></li>
</ol>
</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.css.get_computed_style_for_node">
<span class="sig-name descname"><span class="pre">get_computed_style_for_node</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">node_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#get_computed_style_for_node"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.get_computed_style_for_node" title="Link to this definition">#</a></dt>
<dd><p>Returns the computed style for a DOM node identified by <code class="docutils literal notranslate"><span class="pre">nodeId</span></code>.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="dom.html#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a></span>) – </p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.css.CSSComputedStyleProperty" title="nodriver.cdp.css.CSSComputedStyleProperty"><code class="xref py py-class docutils literal notranslate"><span class="pre">CSSComputedStyleProperty</span></code></a>]]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>Computed style for the specified DOM node.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.css.get_inline_styles_for_node">
<span class="sig-name descname"><span class="pre">get_inline_styles_for_node</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">node_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#get_inline_styles_for_node"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.get_inline_styles_for_node" title="Link to this definition">#</a></dt>
<dd><p>Returns the styles defined inline (explicitly in the “style” attribute and implicitly, using DOM
attributes) for a DOM node identified by <code class="docutils literal notranslate"><span class="pre">nodeId</span></code>.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="dom.html#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a></span>) – </p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Tuple" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Tuple</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="#nodriver.cdp.css.CSSStyle" title="nodriver.cdp.css.CSSStyle"><code class="xref py py-class docutils literal notranslate"><span class="pre">CSSStyle</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="#nodriver.cdp.css.CSSStyle" title="nodriver.cdp.css.CSSStyle"><code class="xref py py-class docutils literal notranslate"><span class="pre">CSSStyle</span></code></a>]]]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p><p>A tuple with the following items:</p>
<ol class="arabic simple" start="0">
<li><p><strong>inlineStyle</strong> - <em>(Optional)</em> Inline style for the specified DOM node.</p></li>
<li><p><strong>attributesStyle</strong> - <em>(Optional)</em> Attribute-defined element style (e.g. resulting from “width=20 height=100%”).</p></li>
</ol>
</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.css.get_layers_for_node">
<span class="sig-name descname"><span class="pre">get_layers_for_node</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">node_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#get_layers_for_node"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.get_layers_for_node" title="Link to this definition">#</a></dt>
<dd><p>Returns all layers parsed by the rendering engine for the tree scope of a node.
Given a DOM element identified by nodeId, getLayersForNode returns the root
layer for the nearest ancestor document or shadow root. The layer root contains
the full layer tree for the tree scope and their ordering.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="dom.html#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a></span>) – </p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference internal" href="#nodriver.cdp.css.CSSLayerData" title="nodriver.cdp.css.CSSLayerData"><code class="xref py py-class docutils literal notranslate"><span class="pre">CSSLayerData</span></code></a>]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.css.get_location_for_selector">
<span class="sig-name descname"><span class="pre">get_location_for_selector</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">style_sheet_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">selector_text</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#get_location_for_selector"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.get_location_for_selector" title="Link to this definition">#</a></dt>
<dd><p>Given a CSS selector text and a style sheet ID, getLocationForSelector
returns an array of locations of the CSS selector in the style sheet.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>style_sheet_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.css.StyleSheetId" title="nodriver.cdp.css.StyleSheetId"><code class="xref py py-class docutils literal notranslate"><span class="pre">StyleSheetId</span></code></a></span>) – </p></li>
<li><p><strong>selector_text</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – </p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.css.SourceRange" title="nodriver.cdp.css.SourceRange"><code class="xref py py-class docutils literal notranslate"><span class="pre">SourceRange</span></code></a>]]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.css.get_longhand_properties">
<span class="sig-name descname"><span class="pre">get_longhand_properties</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">shorthand_name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#get_longhand_properties"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.get_longhand_properties" title="Link to this definition">#</a></dt>
<dd><p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>shorthand_name</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – </p></li>
<li><p><strong>value</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – </p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.css.CSSProperty" title="nodriver.cdp.css.CSSProperty"><code class="xref py py-class docutils literal notranslate"><span class="pre">CSSProperty</span></code></a>]]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.css.get_matched_styles_for_node">
<span class="sig-name descname"><span class="pre">get_matched_styles_for_node</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">node_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#get_matched_styles_for_node"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.get_matched_styles_for_node" title="Link to this definition">#</a></dt>
<dd><p>Returns requested styles for a DOM node identified by <code class="docutils literal notranslate"><span class="pre">nodeId</span></code>.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="dom.html#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a></span>) – </p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Tuple" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Tuple</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="#nodriver.cdp.css.CSSStyle" title="nodriver.cdp.css.CSSStyle"><code class="xref py py-class docutils literal notranslate"><span class="pre">CSSStyle</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="#nodriver.cdp.css.CSSStyle" title="nodriver.cdp.css.CSSStyle"><code class="xref py py-class docutils literal notranslate"><span class="pre">CSSStyle</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.css.RuleMatch" title="nodriver.cdp.css.RuleMatch"><code class="xref py py-class docutils literal notranslate"><span class="pre">RuleMatch</span></code></a>]], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.css.PseudoElementMatches" title="nodriver.cdp.css.PseudoElementMatches"><code class="xref py py-class docutils literal notranslate"><span class="pre">PseudoElementMatches</span></code></a>]], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.css.InheritedStyleEntry" title="nodriver.cdp.css.InheritedStyleEntry"><code class="xref py py-class docutils literal notranslate"><span class="pre">InheritedStyleEntry</span></code></a>]], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.css.InheritedPseudoElementMatches" title="nodriver.cdp.css.InheritedPseudoElementMatches"><code class="xref py py-class docutils literal notranslate"><span class="pre">InheritedPseudoElementMatches</span></code></a>]], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.css.CSSKeyframesRule" title="nodriver.cdp.css.CSSKeyframesRule"><code class="xref py py-class docutils literal notranslate"><span class="pre">CSSKeyframesRule</span></code></a>]], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.css.CSSPositionTryRule" title="nodriver.cdp.css.CSSPositionTryRule"><code class="xref py py-class docutils literal notranslate"><span class="pre">CSSPositionTryRule</span></code></a>]], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.css.CSSPropertyRule" title="nodriver.cdp.css.CSSPropertyRule"><code class="xref py py-class docutils literal notranslate"><span class="pre">CSSPropertyRule</span></code></a>]], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.css.CSSPropertyRegistration" title="nodriver.cdp.css.CSSPropertyRegistration"><code class="xref py py-class docutils literal notranslate"><span class="pre">CSSPropertyRegistration</span></code></a>]], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="#nodriver.cdp.css.CSSFontPaletteValuesRule" title="nodriver.cdp.css.CSSFontPaletteValuesRule"><code class="xref py py-class docutils literal notranslate"><span class="pre">CSSFontPaletteValuesRule</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="dom.html#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.css.CSSFunctionRule" title="nodriver.cdp.css.CSSFunctionRule"><code class="xref py py-class docutils literal notranslate"><span class="pre">CSSFunctionRule</span></code></a>]]]]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p><p>A tuple with the following items:</p>
<ol class="arabic simple" start="0">
<li><p><strong>inlineStyle</strong> - <em>(Optional)</em> Inline style for the specified DOM node.</p></li>
<li><p><strong>attributesStyle</strong> - <em>(Optional)</em> Attribute-defined element style (e.g. resulting from “width=20 height=100%”).</p></li>
<li><p><strong>matchedCSSRules</strong> - <em>(Optional)</em> CSS rules matching this node, from all applicable stylesheets.</p></li>
<li><p><strong>pseudoElements</strong> - <em>(Optional)</em> Pseudo style matches for this node.</p></li>
<li><p><strong>inherited</strong> - <em>(Optional)</em> A chain of inherited styles (from the immediate node parent up to the DOM tree root).</p></li>
<li><p><strong>inheritedPseudoElements</strong> - <em>(Optional)</em> A chain of inherited pseudo element styles (from the immediate node parent up to the DOM tree root).</p></li>
<li><p><strong>cssKeyframesRules</strong> - <em>(Optional)</em> A list of CSS keyframed animations matching this node.</p></li>
<li><p><strong>cssPositionTryRules</strong> - <em>(Optional)</em> A list of CSS &#64;position-try rules matching this node, based on the position-try-fallbacks property.</p></li>
<li><p><strong>activePositionFallbackIndex</strong> - <em>(Optional)</em> Index of the active fallback in the applied position-try-fallback property, will not be set if there is no active position-try fallback.</p></li>
<li><p><strong>cssPropertyRules</strong> - <em>(Optional)</em> A list of CSS at-property rules matching this node.</p></li>
<li><p><strong>cssPropertyRegistrations</strong> - <em>(Optional)</em> A list of CSS property registrations matching this node.</p></li>
<li><p><strong>cssFontPaletteValuesRule</strong> - <em>(Optional)</em> A font-palette-values rule matching this node.</p></li>
<li><p><strong>parentLayoutNodeId</strong> - <em>(Optional)</em> Id of the first parent element that does not have display: contents.</p></li>
<li><p><strong>cssFunctionRules</strong> - <em>(Optional)</em> A list of CSS at-function rules referenced by styles of this node.</p></li>
</ol>
</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.css.get_media_queries">
<span class="sig-name descname"><span class="pre">get_media_queries</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#get_media_queries"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.get_media_queries" title="Link to this definition">#</a></dt>
<dd><p>Returns all media queries parsed by the rendering engine.</p>
<dl class="field-list simple">
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.css.CSSMedia" title="nodriver.cdp.css.CSSMedia"><code class="xref py py-class docutils literal notranslate"><span class="pre">CSSMedia</span></code></a>]]</span></p>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.css.get_platform_fonts_for_node">
<span class="sig-name descname"><span class="pre">get_platform_fonts_for_node</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">node_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#get_platform_fonts_for_node"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.get_platform_fonts_for_node" title="Link to this definition">#</a></dt>
<dd><p>Requests information about platform fonts which we used to render child TextNodes in the given
node.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="dom.html#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a></span>) – </p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.css.PlatformFontUsage" title="nodriver.cdp.css.PlatformFontUsage"><code class="xref py py-class docutils literal notranslate"><span class="pre">PlatformFontUsage</span></code></a>]]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>Usage statistics for every employed platform font.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.css.get_style_sheet_text">
<span class="sig-name descname"><span class="pre">get_style_sheet_text</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">style_sheet_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#get_style_sheet_text"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.get_style_sheet_text" title="Link to this definition">#</a></dt>
<dd><p>Returns the current textual content for a stylesheet.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>style_sheet_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.css.StyleSheetId" title="nodriver.cdp.css.StyleSheetId"><code class="xref py py-class docutils literal notranslate"><span class="pre">StyleSheetId</span></code></a></span>) – </p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>The stylesheet text.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.css.resolve_values">
<span class="sig-name descname"><span class="pre">resolve_values</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">values</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">node_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">property_name</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pseudo_type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pseudo_identifier</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#resolve_values"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.resolve_values" title="Link to this definition">#</a></dt>
<dd><p>Resolve the specified values in the context of the provided element.
For example, a value of ‘1em’ is evaluated according to the computed
‘font-size’ of the element and a value ‘calc(1px + 2px)’ will be
resolved to ‘3px’.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>values</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]</span>) – Substitution functions (var()/env()/attr()) and cascade-dependent keywords (revert/revert-layer) do not work.</p></li>
<li><p><strong>node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="dom.html#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a></span>) – Id of the node in whose context the expression is evaluated</p></li>
<li><p><strong>property_name</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]</span>) – <em>(Optional)</em> Only longhands and custom property names are accepted.</p></li>
<li><p><strong>pseudo_type</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="dom.html#nodriver.cdp.dom.PseudoType" title="nodriver.cdp.dom.PseudoType"><code class="xref py py-class docutils literal notranslate"><span class="pre">PseudoType</span></code></a>]</span>) – <strong>(EXPERIMENTAL)</strong> <em>(Optional)</em> Pseudo element type, only works for pseudo elements that generate elements in the tree, such as ::before and ::after.</p></li>
<li><p><strong>pseudo_identifier</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]</span>) – <strong>(EXPERIMENTAL)</strong> <em>(Optional)</em> Pseudo element custom ident.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.css.set_container_query_text">
<span class="sig-name descname"><span class="pre">set_container_query_text</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">style_sheet_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">range_</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">text</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#set_container_query_text"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.set_container_query_text" title="Link to this definition">#</a></dt>
<dd><p>Modifies the expression of a container query.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>style_sheet_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.css.StyleSheetId" title="nodriver.cdp.css.StyleSheetId"><code class="xref py py-class docutils literal notranslate"><span class="pre">StyleSheetId</span></code></a></span>) – </p></li>
<li><p><strong>range</strong> – </p></li>
<li><p><strong>text</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – </p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference internal" href="#nodriver.cdp.css.CSSContainerQuery" title="nodriver.cdp.css.CSSContainerQuery"><code class="xref py py-class docutils literal notranslate"><span class="pre">CSSContainerQuery</span></code></a>]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>The resulting CSS container query rule after modification.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.css.set_effective_property_value_for_node">
<span class="sig-name descname"><span class="pre">set_effective_property_value_for_node</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">node_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">property_name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#set_effective_property_value_for_node"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.set_effective_property_value_for_node" title="Link to this definition">#</a></dt>
<dd><p>Find a rule with the given active property for the given node and set the new value for this
property</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="dom.html#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a></span>) – The element id for which to set property.</p></li>
<li><p><strong>property_name</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – </p></li>
<li><p><strong>value</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – </p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.css.set_keyframe_key">
<span class="sig-name descname"><span class="pre">set_keyframe_key</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">style_sheet_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">range_</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">key_text</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#set_keyframe_key"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.set_keyframe_key" title="Link to this definition">#</a></dt>
<dd><p>Modifies the keyframe rule key text.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>style_sheet_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.css.StyleSheetId" title="nodriver.cdp.css.StyleSheetId"><code class="xref py py-class docutils literal notranslate"><span class="pre">StyleSheetId</span></code></a></span>) – </p></li>
<li><p><strong>range</strong> – </p></li>
<li><p><strong>key_text</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – </p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference internal" href="#nodriver.cdp.css.Value" title="nodriver.cdp.css.Value"><code class="xref py py-class docutils literal notranslate"><span class="pre">Value</span></code></a>]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>The resulting key text after modification.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.css.set_local_fonts_enabled">
<span class="sig-name descname"><span class="pre">set_local_fonts_enabled</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">enabled</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#set_local_fonts_enabled"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.set_local_fonts_enabled" title="Link to this definition">#</a></dt>
<dd><p>Enables/disables rendering of local CSS fonts (enabled by default).</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>enabled</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a></span>) – Whether rendering of local fonts is enabled.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.css.set_media_text">
<span class="sig-name descname"><span class="pre">set_media_text</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">style_sheet_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">range_</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">text</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#set_media_text"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.set_media_text" title="Link to this definition">#</a></dt>
<dd><p>Modifies the rule selector.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>style_sheet_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.css.StyleSheetId" title="nodriver.cdp.css.StyleSheetId"><code class="xref py py-class docutils literal notranslate"><span class="pre">StyleSheetId</span></code></a></span>) – </p></li>
<li><p><strong>range</strong> – </p></li>
<li><p><strong>text</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – </p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference internal" href="#nodriver.cdp.css.CSSMedia" title="nodriver.cdp.css.CSSMedia"><code class="xref py py-class docutils literal notranslate"><span class="pre">CSSMedia</span></code></a>]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>The resulting CSS media rule after modification.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.css.set_property_rule_property_name">
<span class="sig-name descname"><span class="pre">set_property_rule_property_name</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">style_sheet_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">range_</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">property_name</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#set_property_rule_property_name"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.set_property_rule_property_name" title="Link to this definition">#</a></dt>
<dd><p>Modifies the property rule property name.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>style_sheet_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.css.StyleSheetId" title="nodriver.cdp.css.StyleSheetId"><code class="xref py py-class docutils literal notranslate"><span class="pre">StyleSheetId</span></code></a></span>) – </p></li>
<li><p><strong>range</strong> – </p></li>
<li><p><strong>property_name</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – </p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference internal" href="#nodriver.cdp.css.Value" title="nodriver.cdp.css.Value"><code class="xref py py-class docutils literal notranslate"><span class="pre">Value</span></code></a>]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>The resulting key text after modification.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.css.set_rule_selector">
<span class="sig-name descname"><span class="pre">set_rule_selector</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">style_sheet_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">range_</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">selector</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#set_rule_selector"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.set_rule_selector" title="Link to this definition">#</a></dt>
<dd><p>Modifies the rule selector.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>style_sheet_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.css.StyleSheetId" title="nodriver.cdp.css.StyleSheetId"><code class="xref py py-class docutils literal notranslate"><span class="pre">StyleSheetId</span></code></a></span>) – </p></li>
<li><p><strong>range</strong> – </p></li>
<li><p><strong>selector</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – </p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference internal" href="#nodriver.cdp.css.SelectorList" title="nodriver.cdp.css.SelectorList"><code class="xref py py-class docutils literal notranslate"><span class="pre">SelectorList</span></code></a>]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>The resulting selector list after modification.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.css.set_scope_text">
<span class="sig-name descname"><span class="pre">set_scope_text</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">style_sheet_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">range_</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">text</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#set_scope_text"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.set_scope_text" title="Link to this definition">#</a></dt>
<dd><p>Modifies the expression of a scope at-rule.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>style_sheet_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.css.StyleSheetId" title="nodriver.cdp.css.StyleSheetId"><code class="xref py py-class docutils literal notranslate"><span class="pre">StyleSheetId</span></code></a></span>) – </p></li>
<li><p><strong>range</strong> – </p></li>
<li><p><strong>text</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – </p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference internal" href="#nodriver.cdp.css.CSSScope" title="nodriver.cdp.css.CSSScope"><code class="xref py py-class docutils literal notranslate"><span class="pre">CSSScope</span></code></a>]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>The resulting CSS Scope rule after modification.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.css.set_style_sheet_text">
<span class="sig-name descname"><span class="pre">set_style_sheet_text</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">style_sheet_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">text</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#set_style_sheet_text"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.set_style_sheet_text" title="Link to this definition">#</a></dt>
<dd><p>Sets the new stylesheet text.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>style_sheet_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.css.StyleSheetId" title="nodriver.cdp.css.StyleSheetId"><code class="xref py py-class docutils literal notranslate"><span class="pre">StyleSheetId</span></code></a></span>) – </p></li>
<li><p><strong>text</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – </p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p><em>(Optional)</em> URL of source map associated with script (if any).</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.css.set_style_texts">
<span class="sig-name descname"><span class="pre">set_style_texts</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">edits</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">node_for_property_syntax_validation</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#set_style_texts"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.set_style_texts" title="Link to this definition">#</a></dt>
<dd><p>Applies specified style edits one after another in the given order.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>edits</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.css.StyleDeclarationEdit" title="nodriver.cdp.css.StyleDeclarationEdit"><code class="xref py py-class docutils literal notranslate"><span class="pre">StyleDeclarationEdit</span></code></a>]</span>) – </p></li>
<li><p><strong>node_for_property_syntax_validation</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="dom.html#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a>]</span>) – <strong>(EXPERIMENTAL)</strong> <em>(Optional)</em> NodeId for the DOM node in whose context custom property declarations for registered properties should be validated. If omitted, declarations in the new rule text can only be validated statically, which may produce incorrect results if the declaration contains a var() for example.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.css.CSSStyle" title="nodriver.cdp.css.CSSStyle"><code class="xref py py-class docutils literal notranslate"><span class="pre">CSSStyle</span></code></a>]]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>The resulting styles after modification.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.css.set_supports_text">
<span class="sig-name descname"><span class="pre">set_supports_text</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">style_sheet_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">range_</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">text</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#set_supports_text"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.set_supports_text" title="Link to this definition">#</a></dt>
<dd><p>Modifies the expression of a supports at-rule.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>style_sheet_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.css.StyleSheetId" title="nodriver.cdp.css.StyleSheetId"><code class="xref py py-class docutils literal notranslate"><span class="pre">StyleSheetId</span></code></a></span>) – </p></li>
<li><p><strong>range</strong> – </p></li>
<li><p><strong>text</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – </p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference internal" href="#nodriver.cdp.css.CSSSupports" title="nodriver.cdp.css.CSSSupports"><code class="xref py py-class docutils literal notranslate"><span class="pre">CSSSupports</span></code></a>]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>The resulting CSS Supports rule after modification.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.css.start_rule_usage_tracking">
<span class="sig-name descname"><span class="pre">start_rule_usage_tracking</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#start_rule_usage_tracking"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.start_rule_usage_tracking" title="Link to this definition">#</a></dt>
<dd><p>Enables the selector recording.</p>
<dl class="field-list simple">
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.css.stop_rule_usage_tracking">
<span class="sig-name descname"><span class="pre">stop_rule_usage_tracking</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#stop_rule_usage_tracking"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.stop_rule_usage_tracking" title="Link to this definition">#</a></dt>
<dd><p>Stop tracking rule usage and return the list of rules that were used since last call to
<code class="docutils literal notranslate"><span class="pre">takeCoverageDelta</span></code> (or since start of coverage instrumentation).</p>
<dl class="field-list simple">
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.css.RuleUsage" title="nodriver.cdp.css.RuleUsage"><code class="xref py py-class docutils literal notranslate"><span class="pre">RuleUsage</span></code></a>]]</span></p>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.css.take_computed_style_updates">
<span class="sig-name descname"><span class="pre">take_computed_style_updates</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#take_computed_style_updates"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.take_computed_style_updates" title="Link to this definition">#</a></dt>
<dd><p>Polls the next batch of computed style updates.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="dom.html#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a>]]</span></p>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>The list of node Ids that have their tracked computed styles updated.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.css.take_coverage_delta">
<span class="sig-name descname"><span class="pre">take_coverage_delta</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#take_coverage_delta"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.take_coverage_delta" title="Link to this definition">#</a></dt>
<dd><p>Obtain list of rules that became used since last call to this method (or since start of coverage
instrumentation).</p>
<dl class="field-list simple">
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Tuple" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Tuple</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.css.RuleUsage" title="nodriver.cdp.css.RuleUsage"><code class="xref py py-class docutils literal notranslate"><span class="pre">RuleUsage</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a>]]</span></p>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p><p>A tuple with the following items:</p>
<ol class="arabic simple" start="0">
<li><p><strong>coverage</strong> -</p></li>
<li><p><strong>timestamp</strong> - Monotonically increasing time, in seconds.</p></li>
</ol>
</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.css.track_computed_style_updates">
<span class="sig-name descname"><span class="pre">track_computed_style_updates</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">properties_to_track</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#track_computed_style_updates"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.track_computed_style_updates" title="Link to this definition">#</a></dt>
<dd><p>Starts tracking the given computed styles for updates. The specified array of properties
replaces the one previously specified. Pass empty array to disable tracking.
Use takeComputedStyleUpdates to retrieve the list of nodes that had properties modified.
The changes to computed style properties are only tracked for nodes pushed to the front-end
by the DOM agent. If no changes to the tracked properties occur after the node has been pushed
to the front-end, no updates will be issued for the node.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>properties_to_track</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.css.CSSComputedStyleProperty" title="nodriver.cdp.css.CSSComputedStyleProperty"><code class="xref py py-class docutils literal notranslate"><span class="pre">CSSComputedStyleProperty</span></code></a>]</span>) – </p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.css.track_computed_style_updates_for_node">
<span class="sig-name descname"><span class="pre">track_computed_style_updates_for_node</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">node_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#track_computed_style_updates_for_node"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.track_computed_style_updates_for_node" title="Link to this definition">#</a></dt>
<dd><p>Starts tracking the given node for the computed style updates
and whenever the computed style is updated for node, it queues
a <code class="docutils literal notranslate"><span class="pre">computedStyleUpdated</span></code> event with throttling.
There can only be 1 node tracked for computed style updates
so passing a new node id removes tracking from the previous node.
Pass <code class="docutils literal notranslate"><span class="pre">undefined</span></code> to disable tracking.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="dom.html#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a>]</span>) – <em>(Optional)</em></p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

</section>
<section id="events">
<h2>Events<a class="headerlink" href="#events" title="Link to this heading">#</a></h2>
<p>Generally, you do not need to instantiate CDP events
yourself. Instead, the API creates events for you and then
you use the event’s attributes.</p>
<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.css.FontsUpdated">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">FontsUpdated</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">font</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#FontsUpdated"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.FontsUpdated" title="Link to this definition">#</a></dt>
<dd><p>Fires whenever a web font is updated.  A non-empty font parameter indicates a successfully loaded
web font.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.FontsUpdated.font">
<span class="sig-name descname"><span class="pre">font</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.css.FontFace" title="nodriver.cdp.css.FontFace"><code class="xref py py-class docutils literal notranslate"><span class="pre">FontFace</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.css.FontsUpdated.font" title="Link to this definition">#</a></dt>
<dd><p>The web font that has loaded.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.css.MediaQueryResultChanged">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">MediaQueryResultChanged</span></span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#MediaQueryResultChanged"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.MediaQueryResultChanged" title="Link to this definition">#</a></dt>
<dd><p>Fires whenever a MediaQuery result changes (for example, after a browser window has been
resized.) The current implementation considers only viewport-dependent media features.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.css.StyleSheetAdded">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">StyleSheetAdded</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">header</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#StyleSheetAdded"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.StyleSheetAdded" title="Link to this definition">#</a></dt>
<dd><p>Fired whenever an active document stylesheet is added.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.StyleSheetAdded.header">
<span class="sig-name descname"><span class="pre">header</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.css.CSSStyleSheetHeader" title="nodriver.cdp.css.CSSStyleSheetHeader"><code class="xref py py-class docutils literal notranslate"><span class="pre">CSSStyleSheetHeader</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.StyleSheetAdded.header" title="Link to this definition">#</a></dt>
<dd><p>Added stylesheet metainfo.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.css.StyleSheetChanged">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">StyleSheetChanged</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">style_sheet_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#StyleSheetChanged"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.StyleSheetChanged" title="Link to this definition">#</a></dt>
<dd><p>Fired whenever a stylesheet is changed as a result of the client operation.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.StyleSheetChanged.style_sheet_id">
<span class="sig-name descname"><span class="pre">style_sheet_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.css.StyleSheetId" title="nodriver.cdp.css.StyleSheetId"><code class="xref py py-class docutils literal notranslate"><span class="pre">StyleSheetId</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.StyleSheetChanged.style_sheet_id" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.css.StyleSheetRemoved">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">StyleSheetRemoved</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">style_sheet_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#StyleSheetRemoved"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.StyleSheetRemoved" title="Link to this definition">#</a></dt>
<dd><p>Fired whenever an active document stylesheet is removed.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.StyleSheetRemoved.style_sheet_id">
<span class="sig-name descname"><span class="pre">style_sheet_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.css.StyleSheetId" title="nodriver.cdp.css.StyleSheetId"><code class="xref py py-class docutils literal notranslate"><span class="pre">StyleSheetId</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.StyleSheetRemoved.style_sheet_id" title="Link to this definition">#</a></dt>
<dd><p>Identifier of the removed stylesheet.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.css.ComputedStyleUpdated">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">ComputedStyleUpdated</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">node_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/css.html#ComputedStyleUpdated"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.css.ComputedStyleUpdated" title="Link to this definition">#</a></dt>
<dd><p><strong>EXPERIMENTAL</strong></p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.css.ComputedStyleUpdated.node_id">
<span class="sig-name descname"><span class="pre">node_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="dom.html#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a></em><a class="headerlink" href="#nodriver.cdp.css.ComputedStyleUpdated.node_id" title="Link to this definition">#</a></dt>
<dd><p>The node id that has updated computed styles.</p>
</dd></dl>

</dd></dl>

</section>
</section>

        </article>
      </div>
      <footer>
        
        <div class="related-pages">
          <a class="next-page" href="debugger.html">
              <div class="page-info">
                <div class="context">
                  <span>Next</span>
                </div>
                <div class="title">Debugger</div>
              </div>
              <svg class="furo-related-icon"><use href="#svg-arrow-right"></use></svg>
            </a>
          <a class="prev-page" href="console.html">
              <svg class="furo-related-icon"><use href="#svg-arrow-right"></use></svg>
              <div class="page-info">
                <div class="context">
                  <span>Previous</span>
                </div>
                
                <div class="title">Console</div>
                
              </div>
            </a>
        </div>
        <div class="bottom-of-page">
          <div class="left-details">
            <div class="copyright">
                Copyright &#169; 2023, Author
            </div>
            Made with <a href="https://www.sphinx-doc.org/">Sphinx</a> and <a class="muted-link" href="https://pradyunsg.me">@pradyunsg</a>'s
            
            <a href="https://github.com/pradyunsg/furo">Furo</a>
            
          </div>
          <div class="right-details">
            
          </div>
        </div>
        
      </footer>
    </div>
    <aside class="toc-drawer">
      
      
      <div class="toc-sticky toc-scroll">
        <div class="toc-title-container">
          <span class="toc-title">
            On this page
          </span>
        </div>
        <div class="toc-tree-container">
          <div class="toc-tree">
            <ul>
<li><a class="reference internal" href="#">CSS</a><ul>
<li><a class="reference internal" href="#types">Types</a><ul>
<li><a class="reference internal" href="#nodriver.cdp.css.StyleSheetId"><code class="docutils literal notranslate"><span class="pre">StyleSheetId</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.StyleSheetOrigin"><code class="docutils literal notranslate"><span class="pre">StyleSheetOrigin</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.css.StyleSheetOrigin.INJECTED"><code class="docutils literal notranslate"><span class="pre">StyleSheetOrigin.INJECTED</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.StyleSheetOrigin.USER_AGENT"><code class="docutils literal notranslate"><span class="pre">StyleSheetOrigin.USER_AGENT</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.StyleSheetOrigin.INSPECTOR"><code class="docutils literal notranslate"><span class="pre">StyleSheetOrigin.INSPECTOR</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.StyleSheetOrigin.REGULAR"><code class="docutils literal notranslate"><span class="pre">StyleSheetOrigin.REGULAR</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.css.PseudoElementMatches"><code class="docutils literal notranslate"><span class="pre">PseudoElementMatches</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.css.PseudoElementMatches.pseudo_type"><code class="docutils literal notranslate"><span class="pre">PseudoElementMatches.pseudo_type</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.PseudoElementMatches.matches"><code class="docutils literal notranslate"><span class="pre">PseudoElementMatches.matches</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.PseudoElementMatches.pseudo_identifier"><code class="docutils literal notranslate"><span class="pre">PseudoElementMatches.pseudo_identifier</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSAnimationStyle"><code class="docutils literal notranslate"><span class="pre">CSSAnimationStyle</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSAnimationStyle.style"><code class="docutils literal notranslate"><span class="pre">CSSAnimationStyle.style</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSAnimationStyle.name"><code class="docutils literal notranslate"><span class="pre">CSSAnimationStyle.name</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.css.InheritedStyleEntry"><code class="docutils literal notranslate"><span class="pre">InheritedStyleEntry</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.css.InheritedStyleEntry.matched_css_rules"><code class="docutils literal notranslate"><span class="pre">InheritedStyleEntry.matched_css_rules</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.InheritedStyleEntry.inline_style"><code class="docutils literal notranslate"><span class="pre">InheritedStyleEntry.inline_style</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.css.InheritedAnimatedStyleEntry"><code class="docutils literal notranslate"><span class="pre">InheritedAnimatedStyleEntry</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.css.InheritedAnimatedStyleEntry.animation_styles"><code class="docutils literal notranslate"><span class="pre">InheritedAnimatedStyleEntry.animation_styles</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.InheritedAnimatedStyleEntry.transitions_style"><code class="docutils literal notranslate"><span class="pre">InheritedAnimatedStyleEntry.transitions_style</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.css.InheritedPseudoElementMatches"><code class="docutils literal notranslate"><span class="pre">InheritedPseudoElementMatches</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.css.InheritedPseudoElementMatches.pseudo_elements"><code class="docutils literal notranslate"><span class="pre">InheritedPseudoElementMatches.pseudo_elements</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.css.RuleMatch"><code class="docutils literal notranslate"><span class="pre">RuleMatch</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.css.RuleMatch.rule"><code class="docutils literal notranslate"><span class="pre">RuleMatch.rule</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.RuleMatch.matching_selectors"><code class="docutils literal notranslate"><span class="pre">RuleMatch.matching_selectors</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.css.Value"><code class="docutils literal notranslate"><span class="pre">Value</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.css.Value.text"><code class="docutils literal notranslate"><span class="pre">Value.text</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.Value.range_"><code class="docutils literal notranslate"><span class="pre">Value.range_</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.Value.specificity"><code class="docutils literal notranslate"><span class="pre">Value.specificity</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.css.Specificity"><code class="docutils literal notranslate"><span class="pre">Specificity</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.css.Specificity.a"><code class="docutils literal notranslate"><span class="pre">Specificity.a</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.Specificity.b"><code class="docutils literal notranslate"><span class="pre">Specificity.b</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.Specificity.c"><code class="docutils literal notranslate"><span class="pre">Specificity.c</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.css.SelectorList"><code class="docutils literal notranslate"><span class="pre">SelectorList</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.css.SelectorList.selectors"><code class="docutils literal notranslate"><span class="pre">SelectorList.selectors</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.SelectorList.text"><code class="docutils literal notranslate"><span class="pre">SelectorList.text</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSStyleSheetHeader"><code class="docutils literal notranslate"><span class="pre">CSSStyleSheetHeader</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSStyleSheetHeader.style_sheet_id"><code class="docutils literal notranslate"><span class="pre">CSSStyleSheetHeader.style_sheet_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSStyleSheetHeader.frame_id"><code class="docutils literal notranslate"><span class="pre">CSSStyleSheetHeader.frame_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSStyleSheetHeader.source_url"><code class="docutils literal notranslate"><span class="pre">CSSStyleSheetHeader.source_url</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSStyleSheetHeader.origin"><code class="docutils literal notranslate"><span class="pre">CSSStyleSheetHeader.origin</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSStyleSheetHeader.title"><code class="docutils literal notranslate"><span class="pre">CSSStyleSheetHeader.title</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSStyleSheetHeader.disabled"><code class="docutils literal notranslate"><span class="pre">CSSStyleSheetHeader.disabled</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSStyleSheetHeader.is_inline"><code class="docutils literal notranslate"><span class="pre">CSSStyleSheetHeader.is_inline</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSStyleSheetHeader.is_mutable"><code class="docutils literal notranslate"><span class="pre">CSSStyleSheetHeader.is_mutable</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSStyleSheetHeader.is_constructed"><code class="docutils literal notranslate"><span class="pre">CSSStyleSheetHeader.is_constructed</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSStyleSheetHeader.start_line"><code class="docutils literal notranslate"><span class="pre">CSSStyleSheetHeader.start_line</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSStyleSheetHeader.start_column"><code class="docutils literal notranslate"><span class="pre">CSSStyleSheetHeader.start_column</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSStyleSheetHeader.length"><code class="docutils literal notranslate"><span class="pre">CSSStyleSheetHeader.length</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSStyleSheetHeader.end_line"><code class="docutils literal notranslate"><span class="pre">CSSStyleSheetHeader.end_line</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSStyleSheetHeader.end_column"><code class="docutils literal notranslate"><span class="pre">CSSStyleSheetHeader.end_column</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSStyleSheetHeader.source_map_url"><code class="docutils literal notranslate"><span class="pre">CSSStyleSheetHeader.source_map_url</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSStyleSheetHeader.owner_node"><code class="docutils literal notranslate"><span class="pre">CSSStyleSheetHeader.owner_node</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSStyleSheetHeader.has_source_url"><code class="docutils literal notranslate"><span class="pre">CSSStyleSheetHeader.has_source_url</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSStyleSheetHeader.loading_failed"><code class="docutils literal notranslate"><span class="pre">CSSStyleSheetHeader.loading_failed</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSRule"><code class="docutils literal notranslate"><span class="pre">CSSRule</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSRule.selector_list"><code class="docutils literal notranslate"><span class="pre">CSSRule.selector_list</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSRule.origin"><code class="docutils literal notranslate"><span class="pre">CSSRule.origin</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSRule.style"><code class="docutils literal notranslate"><span class="pre">CSSRule.style</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSRule.style_sheet_id"><code class="docutils literal notranslate"><span class="pre">CSSRule.style_sheet_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSRule.nesting_selectors"><code class="docutils literal notranslate"><span class="pre">CSSRule.nesting_selectors</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSRule.media"><code class="docutils literal notranslate"><span class="pre">CSSRule.media</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSRule.container_queries"><code class="docutils literal notranslate"><span class="pre">CSSRule.container_queries</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSRule.supports"><code class="docutils literal notranslate"><span class="pre">CSSRule.supports</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSRule.layers"><code class="docutils literal notranslate"><span class="pre">CSSRule.layers</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSRule.scopes"><code class="docutils literal notranslate"><span class="pre">CSSRule.scopes</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSRule.rule_types"><code class="docutils literal notranslate"><span class="pre">CSSRule.rule_types</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSRule.starting_styles"><code class="docutils literal notranslate"><span class="pre">CSSRule.starting_styles</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSRuleType"><code class="docutils literal notranslate"><span class="pre">CSSRuleType</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSRuleType.MEDIA_RULE"><code class="docutils literal notranslate"><span class="pre">CSSRuleType.MEDIA_RULE</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSRuleType.SUPPORTS_RULE"><code class="docutils literal notranslate"><span class="pre">CSSRuleType.SUPPORTS_RULE</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSRuleType.CONTAINER_RULE"><code class="docutils literal notranslate"><span class="pre">CSSRuleType.CONTAINER_RULE</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSRuleType.LAYER_RULE"><code class="docutils literal notranslate"><span class="pre">CSSRuleType.LAYER_RULE</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSRuleType.SCOPE_RULE"><code class="docutils literal notranslate"><span class="pre">CSSRuleType.SCOPE_RULE</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSRuleType.STYLE_RULE"><code class="docutils literal notranslate"><span class="pre">CSSRuleType.STYLE_RULE</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSRuleType.STARTING_STYLE_RULE"><code class="docutils literal notranslate"><span class="pre">CSSRuleType.STARTING_STYLE_RULE</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.css.RuleUsage"><code class="docutils literal notranslate"><span class="pre">RuleUsage</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.css.RuleUsage.style_sheet_id"><code class="docutils literal notranslate"><span class="pre">RuleUsage.style_sheet_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.RuleUsage.start_offset"><code class="docutils literal notranslate"><span class="pre">RuleUsage.start_offset</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.RuleUsage.end_offset"><code class="docutils literal notranslate"><span class="pre">RuleUsage.end_offset</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.RuleUsage.used"><code class="docutils literal notranslate"><span class="pre">RuleUsage.used</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.css.SourceRange"><code class="docutils literal notranslate"><span class="pre">SourceRange</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.css.SourceRange.start_line"><code class="docutils literal notranslate"><span class="pre">SourceRange.start_line</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.SourceRange.start_column"><code class="docutils literal notranslate"><span class="pre">SourceRange.start_column</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.SourceRange.end_line"><code class="docutils literal notranslate"><span class="pre">SourceRange.end_line</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.SourceRange.end_column"><code class="docutils literal notranslate"><span class="pre">SourceRange.end_column</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.css.ShorthandEntry"><code class="docutils literal notranslate"><span class="pre">ShorthandEntry</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.css.ShorthandEntry.name"><code class="docutils literal notranslate"><span class="pre">ShorthandEntry.name</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.ShorthandEntry.value"><code class="docutils literal notranslate"><span class="pre">ShorthandEntry.value</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.ShorthandEntry.important"><code class="docutils literal notranslate"><span class="pre">ShorthandEntry.important</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSComputedStyleProperty"><code class="docutils literal notranslate"><span class="pre">CSSComputedStyleProperty</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSComputedStyleProperty.name"><code class="docutils literal notranslate"><span class="pre">CSSComputedStyleProperty.name</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSComputedStyleProperty.value"><code class="docutils literal notranslate"><span class="pre">CSSComputedStyleProperty.value</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSStyle"><code class="docutils literal notranslate"><span class="pre">CSSStyle</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSStyle.css_properties"><code class="docutils literal notranslate"><span class="pre">CSSStyle.css_properties</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSStyle.shorthand_entries"><code class="docutils literal notranslate"><span class="pre">CSSStyle.shorthand_entries</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSStyle.style_sheet_id"><code class="docutils literal notranslate"><span class="pre">CSSStyle.style_sheet_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSStyle.css_text"><code class="docutils literal notranslate"><span class="pre">CSSStyle.css_text</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSStyle.range_"><code class="docutils literal notranslate"><span class="pre">CSSStyle.range_</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSProperty"><code class="docutils literal notranslate"><span class="pre">CSSProperty</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSProperty.name"><code class="docutils literal notranslate"><span class="pre">CSSProperty.name</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSProperty.value"><code class="docutils literal notranslate"><span class="pre">CSSProperty.value</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSProperty.important"><code class="docutils literal notranslate"><span class="pre">CSSProperty.important</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSProperty.implicit"><code class="docutils literal notranslate"><span class="pre">CSSProperty.implicit</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSProperty.text"><code class="docutils literal notranslate"><span class="pre">CSSProperty.text</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSProperty.parsed_ok"><code class="docutils literal notranslate"><span class="pre">CSSProperty.parsed_ok</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSProperty.disabled"><code class="docutils literal notranslate"><span class="pre">CSSProperty.disabled</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSProperty.range_"><code class="docutils literal notranslate"><span class="pre">CSSProperty.range_</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSProperty.longhand_properties"><code class="docutils literal notranslate"><span class="pre">CSSProperty.longhand_properties</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSMedia"><code class="docutils literal notranslate"><span class="pre">CSSMedia</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSMedia.text"><code class="docutils literal notranslate"><span class="pre">CSSMedia.text</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSMedia.source"><code class="docutils literal notranslate"><span class="pre">CSSMedia.source</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSMedia.source_url"><code class="docutils literal notranslate"><span class="pre">CSSMedia.source_url</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSMedia.range_"><code class="docutils literal notranslate"><span class="pre">CSSMedia.range_</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSMedia.style_sheet_id"><code class="docutils literal notranslate"><span class="pre">CSSMedia.style_sheet_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSMedia.media_list"><code class="docutils literal notranslate"><span class="pre">CSSMedia.media_list</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.css.MediaQuery"><code class="docutils literal notranslate"><span class="pre">MediaQuery</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.css.MediaQuery.expressions"><code class="docutils literal notranslate"><span class="pre">MediaQuery.expressions</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.MediaQuery.active"><code class="docutils literal notranslate"><span class="pre">MediaQuery.active</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.css.MediaQueryExpression"><code class="docutils literal notranslate"><span class="pre">MediaQueryExpression</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.css.MediaQueryExpression.value"><code class="docutils literal notranslate"><span class="pre">MediaQueryExpression.value</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.MediaQueryExpression.unit"><code class="docutils literal notranslate"><span class="pre">MediaQueryExpression.unit</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.MediaQueryExpression.feature"><code class="docutils literal notranslate"><span class="pre">MediaQueryExpression.feature</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.MediaQueryExpression.value_range"><code class="docutils literal notranslate"><span class="pre">MediaQueryExpression.value_range</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.MediaQueryExpression.computed_length"><code class="docutils literal notranslate"><span class="pre">MediaQueryExpression.computed_length</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSContainerQuery"><code class="docutils literal notranslate"><span class="pre">CSSContainerQuery</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSContainerQuery.text"><code class="docutils literal notranslate"><span class="pre">CSSContainerQuery.text</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSContainerQuery.range_"><code class="docutils literal notranslate"><span class="pre">CSSContainerQuery.range_</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSContainerQuery.style_sheet_id"><code class="docutils literal notranslate"><span class="pre">CSSContainerQuery.style_sheet_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSContainerQuery.name"><code class="docutils literal notranslate"><span class="pre">CSSContainerQuery.name</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSContainerQuery.physical_axes"><code class="docutils literal notranslate"><span class="pre">CSSContainerQuery.physical_axes</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSContainerQuery.logical_axes"><code class="docutils literal notranslate"><span class="pre">CSSContainerQuery.logical_axes</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSContainerQuery.queries_scroll_state"><code class="docutils literal notranslate"><span class="pre">CSSContainerQuery.queries_scroll_state</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSSupports"><code class="docutils literal notranslate"><span class="pre">CSSSupports</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSSupports.text"><code class="docutils literal notranslate"><span class="pre">CSSSupports.text</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSSupports.active"><code class="docutils literal notranslate"><span class="pre">CSSSupports.active</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSSupports.range_"><code class="docutils literal notranslate"><span class="pre">CSSSupports.range_</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSSupports.style_sheet_id"><code class="docutils literal notranslate"><span class="pre">CSSSupports.style_sheet_id</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSScope"><code class="docutils literal notranslate"><span class="pre">CSSScope</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSScope.text"><code class="docutils literal notranslate"><span class="pre">CSSScope.text</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSScope.range_"><code class="docutils literal notranslate"><span class="pre">CSSScope.range_</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSScope.style_sheet_id"><code class="docutils literal notranslate"><span class="pre">CSSScope.style_sheet_id</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSLayer"><code class="docutils literal notranslate"><span class="pre">CSSLayer</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSLayer.text"><code class="docutils literal notranslate"><span class="pre">CSSLayer.text</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSLayer.range_"><code class="docutils literal notranslate"><span class="pre">CSSLayer.range_</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSLayer.style_sheet_id"><code class="docutils literal notranslate"><span class="pre">CSSLayer.style_sheet_id</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSStartingStyle"><code class="docutils literal notranslate"><span class="pre">CSSStartingStyle</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSStartingStyle.range_"><code class="docutils literal notranslate"><span class="pre">CSSStartingStyle.range_</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSStartingStyle.style_sheet_id"><code class="docutils literal notranslate"><span class="pre">CSSStartingStyle.style_sheet_id</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSLayerData"><code class="docutils literal notranslate"><span class="pre">CSSLayerData</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSLayerData.name"><code class="docutils literal notranslate"><span class="pre">CSSLayerData.name</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSLayerData.order"><code class="docutils literal notranslate"><span class="pre">CSSLayerData.order</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSLayerData.sub_layers"><code class="docutils literal notranslate"><span class="pre">CSSLayerData.sub_layers</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.css.PlatformFontUsage"><code class="docutils literal notranslate"><span class="pre">PlatformFontUsage</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.css.PlatformFontUsage.family_name"><code class="docutils literal notranslate"><span class="pre">PlatformFontUsage.family_name</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.PlatformFontUsage.post_script_name"><code class="docutils literal notranslate"><span class="pre">PlatformFontUsage.post_script_name</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.PlatformFontUsage.is_custom_font"><code class="docutils literal notranslate"><span class="pre">PlatformFontUsage.is_custom_font</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.PlatformFontUsage.glyph_count"><code class="docutils literal notranslate"><span class="pre">PlatformFontUsage.glyph_count</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.css.FontVariationAxis"><code class="docutils literal notranslate"><span class="pre">FontVariationAxis</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.css.FontVariationAxis.tag"><code class="docutils literal notranslate"><span class="pre">FontVariationAxis.tag</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.FontVariationAxis.name"><code class="docutils literal notranslate"><span class="pre">FontVariationAxis.name</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.FontVariationAxis.min_value"><code class="docutils literal notranslate"><span class="pre">FontVariationAxis.min_value</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.FontVariationAxis.max_value"><code class="docutils literal notranslate"><span class="pre">FontVariationAxis.max_value</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.FontVariationAxis.default_value"><code class="docutils literal notranslate"><span class="pre">FontVariationAxis.default_value</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.css.FontFace"><code class="docutils literal notranslate"><span class="pre">FontFace</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.css.FontFace.font_family"><code class="docutils literal notranslate"><span class="pre">FontFace.font_family</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.FontFace.font_style"><code class="docutils literal notranslate"><span class="pre">FontFace.font_style</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.FontFace.font_variant"><code class="docutils literal notranslate"><span class="pre">FontFace.font_variant</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.FontFace.font_weight"><code class="docutils literal notranslate"><span class="pre">FontFace.font_weight</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.FontFace.font_stretch"><code class="docutils literal notranslate"><span class="pre">FontFace.font_stretch</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.FontFace.font_display"><code class="docutils literal notranslate"><span class="pre">FontFace.font_display</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.FontFace.unicode_range"><code class="docutils literal notranslate"><span class="pre">FontFace.unicode_range</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.FontFace.src"><code class="docutils literal notranslate"><span class="pre">FontFace.src</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.FontFace.platform_font_family"><code class="docutils literal notranslate"><span class="pre">FontFace.platform_font_family</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.FontFace.font_variation_axes"><code class="docutils literal notranslate"><span class="pre">FontFace.font_variation_axes</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSTryRule"><code class="docutils literal notranslate"><span class="pre">CSSTryRule</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSTryRule.origin"><code class="docutils literal notranslate"><span class="pre">CSSTryRule.origin</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSTryRule.style"><code class="docutils literal notranslate"><span class="pre">CSSTryRule.style</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSTryRule.style_sheet_id"><code class="docutils literal notranslate"><span class="pre">CSSTryRule.style_sheet_id</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSPositionTryRule"><code class="docutils literal notranslate"><span class="pre">CSSPositionTryRule</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSPositionTryRule.name"><code class="docutils literal notranslate"><span class="pre">CSSPositionTryRule.name</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSPositionTryRule.origin"><code class="docutils literal notranslate"><span class="pre">CSSPositionTryRule.origin</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSPositionTryRule.style"><code class="docutils literal notranslate"><span class="pre">CSSPositionTryRule.style</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSPositionTryRule.active"><code class="docutils literal notranslate"><span class="pre">CSSPositionTryRule.active</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSPositionTryRule.style_sheet_id"><code class="docutils literal notranslate"><span class="pre">CSSPositionTryRule.style_sheet_id</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSKeyframesRule"><code class="docutils literal notranslate"><span class="pre">CSSKeyframesRule</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSKeyframesRule.animation_name"><code class="docutils literal notranslate"><span class="pre">CSSKeyframesRule.animation_name</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSKeyframesRule.keyframes"><code class="docutils literal notranslate"><span class="pre">CSSKeyframesRule.keyframes</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSPropertyRegistration"><code class="docutils literal notranslate"><span class="pre">CSSPropertyRegistration</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSPropertyRegistration.property_name"><code class="docutils literal notranslate"><span class="pre">CSSPropertyRegistration.property_name</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSPropertyRegistration.inherits"><code class="docutils literal notranslate"><span class="pre">CSSPropertyRegistration.inherits</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSPropertyRegistration.syntax"><code class="docutils literal notranslate"><span class="pre">CSSPropertyRegistration.syntax</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSPropertyRegistration.initial_value"><code class="docutils literal notranslate"><span class="pre">CSSPropertyRegistration.initial_value</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSFontPaletteValuesRule"><code class="docutils literal notranslate"><span class="pre">CSSFontPaletteValuesRule</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSFontPaletteValuesRule.origin"><code class="docutils literal notranslate"><span class="pre">CSSFontPaletteValuesRule.origin</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSFontPaletteValuesRule.font_palette_name"><code class="docutils literal notranslate"><span class="pre">CSSFontPaletteValuesRule.font_palette_name</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSFontPaletteValuesRule.style"><code class="docutils literal notranslate"><span class="pre">CSSFontPaletteValuesRule.style</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSFontPaletteValuesRule.style_sheet_id"><code class="docutils literal notranslate"><span class="pre">CSSFontPaletteValuesRule.style_sheet_id</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSPropertyRule"><code class="docutils literal notranslate"><span class="pre">CSSPropertyRule</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSPropertyRule.origin"><code class="docutils literal notranslate"><span class="pre">CSSPropertyRule.origin</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSPropertyRule.property_name"><code class="docutils literal notranslate"><span class="pre">CSSPropertyRule.property_name</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSPropertyRule.style"><code class="docutils literal notranslate"><span class="pre">CSSPropertyRule.style</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSPropertyRule.style_sheet_id"><code class="docutils literal notranslate"><span class="pre">CSSPropertyRule.style_sheet_id</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSFunctionParameter"><code class="docutils literal notranslate"><span class="pre">CSSFunctionParameter</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSFunctionParameter.name"><code class="docutils literal notranslate"><span class="pre">CSSFunctionParameter.name</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSFunctionParameter.type_"><code class="docutils literal notranslate"><span class="pre">CSSFunctionParameter.type_</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSFunctionConditionNode"><code class="docutils literal notranslate"><span class="pre">CSSFunctionConditionNode</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSFunctionConditionNode.children"><code class="docutils literal notranslate"><span class="pre">CSSFunctionConditionNode.children</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSFunctionConditionNode.condition_text"><code class="docutils literal notranslate"><span class="pre">CSSFunctionConditionNode.condition_text</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSFunctionConditionNode.media"><code class="docutils literal notranslate"><span class="pre">CSSFunctionConditionNode.media</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSFunctionConditionNode.container_queries"><code class="docutils literal notranslate"><span class="pre">CSSFunctionConditionNode.container_queries</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSFunctionConditionNode.supports"><code class="docutils literal notranslate"><span class="pre">CSSFunctionConditionNode.supports</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSFunctionNode"><code class="docutils literal notranslate"><span class="pre">CSSFunctionNode</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSFunctionNode.condition"><code class="docutils literal notranslate"><span class="pre">CSSFunctionNode.condition</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSFunctionNode.style"><code class="docutils literal notranslate"><span class="pre">CSSFunctionNode.style</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSFunctionRule"><code class="docutils literal notranslate"><span class="pre">CSSFunctionRule</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSFunctionRule.name"><code class="docutils literal notranslate"><span class="pre">CSSFunctionRule.name</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSFunctionRule.origin"><code class="docutils literal notranslate"><span class="pre">CSSFunctionRule.origin</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSFunctionRule.parameters"><code class="docutils literal notranslate"><span class="pre">CSSFunctionRule.parameters</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSFunctionRule.children"><code class="docutils literal notranslate"><span class="pre">CSSFunctionRule.children</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSFunctionRule.style_sheet_id"><code class="docutils literal notranslate"><span class="pre">CSSFunctionRule.style_sheet_id</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSKeyframeRule"><code class="docutils literal notranslate"><span class="pre">CSSKeyframeRule</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSKeyframeRule.origin"><code class="docutils literal notranslate"><span class="pre">CSSKeyframeRule.origin</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSKeyframeRule.key_text"><code class="docutils literal notranslate"><span class="pre">CSSKeyframeRule.key_text</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSKeyframeRule.style"><code class="docutils literal notranslate"><span class="pre">CSSKeyframeRule.style</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.CSSKeyframeRule.style_sheet_id"><code class="docutils literal notranslate"><span class="pre">CSSKeyframeRule.style_sheet_id</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.css.StyleDeclarationEdit"><code class="docutils literal notranslate"><span class="pre">StyleDeclarationEdit</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.css.StyleDeclarationEdit.style_sheet_id"><code class="docutils literal notranslate"><span class="pre">StyleDeclarationEdit.style_sheet_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.StyleDeclarationEdit.range_"><code class="docutils literal notranslate"><span class="pre">StyleDeclarationEdit.range_</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.StyleDeclarationEdit.text"><code class="docutils literal notranslate"><span class="pre">StyleDeclarationEdit.text</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#commands">Commands</a><ul>
<li><a class="reference internal" href="#nodriver.cdp.css.add_rule"><code class="docutils literal notranslate"><span class="pre">add_rule()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.collect_class_names"><code class="docutils literal notranslate"><span class="pre">collect_class_names()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.create_style_sheet"><code class="docutils literal notranslate"><span class="pre">create_style_sheet()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.disable"><code class="docutils literal notranslate"><span class="pre">disable()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.enable"><code class="docutils literal notranslate"><span class="pre">enable()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.force_pseudo_state"><code class="docutils literal notranslate"><span class="pre">force_pseudo_state()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.force_starting_style"><code class="docutils literal notranslate"><span class="pre">force_starting_style()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.get_animated_styles_for_node"><code class="docutils literal notranslate"><span class="pre">get_animated_styles_for_node()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.get_background_colors"><code class="docutils literal notranslate"><span class="pre">get_background_colors()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.get_computed_style_for_node"><code class="docutils literal notranslate"><span class="pre">get_computed_style_for_node()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.get_inline_styles_for_node"><code class="docutils literal notranslate"><span class="pre">get_inline_styles_for_node()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.get_layers_for_node"><code class="docutils literal notranslate"><span class="pre">get_layers_for_node()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.get_location_for_selector"><code class="docutils literal notranslate"><span class="pre">get_location_for_selector()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.get_longhand_properties"><code class="docutils literal notranslate"><span class="pre">get_longhand_properties()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.get_matched_styles_for_node"><code class="docutils literal notranslate"><span class="pre">get_matched_styles_for_node()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.get_media_queries"><code class="docutils literal notranslate"><span class="pre">get_media_queries()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.get_platform_fonts_for_node"><code class="docutils literal notranslate"><span class="pre">get_platform_fonts_for_node()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.get_style_sheet_text"><code class="docutils literal notranslate"><span class="pre">get_style_sheet_text()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.resolve_values"><code class="docutils literal notranslate"><span class="pre">resolve_values()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.set_container_query_text"><code class="docutils literal notranslate"><span class="pre">set_container_query_text()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.set_effective_property_value_for_node"><code class="docutils literal notranslate"><span class="pre">set_effective_property_value_for_node()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.set_keyframe_key"><code class="docutils literal notranslate"><span class="pre">set_keyframe_key()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.set_local_fonts_enabled"><code class="docutils literal notranslate"><span class="pre">set_local_fonts_enabled()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.set_media_text"><code class="docutils literal notranslate"><span class="pre">set_media_text()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.set_property_rule_property_name"><code class="docutils literal notranslate"><span class="pre">set_property_rule_property_name()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.set_rule_selector"><code class="docutils literal notranslate"><span class="pre">set_rule_selector()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.set_scope_text"><code class="docutils literal notranslate"><span class="pre">set_scope_text()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.set_style_sheet_text"><code class="docutils literal notranslate"><span class="pre">set_style_sheet_text()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.set_style_texts"><code class="docutils literal notranslate"><span class="pre">set_style_texts()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.set_supports_text"><code class="docutils literal notranslate"><span class="pre">set_supports_text()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.start_rule_usage_tracking"><code class="docutils literal notranslate"><span class="pre">start_rule_usage_tracking()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.stop_rule_usage_tracking"><code class="docutils literal notranslate"><span class="pre">stop_rule_usage_tracking()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.take_computed_style_updates"><code class="docutils literal notranslate"><span class="pre">take_computed_style_updates()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.take_coverage_delta"><code class="docutils literal notranslate"><span class="pre">take_coverage_delta()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.track_computed_style_updates"><code class="docutils literal notranslate"><span class="pre">track_computed_style_updates()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.track_computed_style_updates_for_node"><code class="docutils literal notranslate"><span class="pre">track_computed_style_updates_for_node()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#events">Events</a><ul>
<li><a class="reference internal" href="#nodriver.cdp.css.FontsUpdated"><code class="docutils literal notranslate"><span class="pre">FontsUpdated</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.css.FontsUpdated.font"><code class="docutils literal notranslate"><span class="pre">FontsUpdated.font</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.css.MediaQueryResultChanged"><code class="docutils literal notranslate"><span class="pre">MediaQueryResultChanged</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.css.StyleSheetAdded"><code class="docutils literal notranslate"><span class="pre">StyleSheetAdded</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.css.StyleSheetAdded.header"><code class="docutils literal notranslate"><span class="pre">StyleSheetAdded.header</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.css.StyleSheetChanged"><code class="docutils literal notranslate"><span class="pre">StyleSheetChanged</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.css.StyleSheetChanged.style_sheet_id"><code class="docutils literal notranslate"><span class="pre">StyleSheetChanged.style_sheet_id</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.css.StyleSheetRemoved"><code class="docutils literal notranslate"><span class="pre">StyleSheetRemoved</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.css.StyleSheetRemoved.style_sheet_id"><code class="docutils literal notranslate"><span class="pre">StyleSheetRemoved.style_sheet_id</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.css.ComputedStyleUpdated"><code class="docutils literal notranslate"><span class="pre">ComputedStyleUpdated</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.css.ComputedStyleUpdated.node_id"><code class="docutils literal notranslate"><span class="pre">ComputedStyleUpdated.node_id</span></code></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>

          </div>
        </div>
      </div>
      
      
    </aside>
  </div>
</div><script src="../../_static/documentation_options.js?v=5929fcd5"></script>
    <script src="../../_static/doctools.js?v=888ff710"></script>
    <script src="../../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../../_static/scripts/furo.js?v=32e29ea5"></script>
    </body>
</html>