<!doctype html>
<html class="no-js" lang="en" data-content_root="../../../">
  <head><meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width,initial-scale=1"/>
    <meta name="color-scheme" content="light dark"><link rel="index" title="Index" href="../../../genindex.html" /><link rel="search" title="Search" href="../../../search.html" />

    <!-- Generated with Sphinx 7.2.6 and Furo 2023.09.10 -->
        <title>nodriver.cdp.runtime - nodriver documentation</title>
      <link rel="stylesheet" type="text/css" href="../../../_static/pygments.css?v=fa44fd50" />
    <link rel="stylesheet" type="text/css" href="../../../_static/styles/furo.css?v=135e06be" />
    <link rel="stylesheet" type="text/css" href="../../../_static/styles/furo-extensions.css?v=36a5483c" />
    <link rel="stylesheet" type="text/css" href="../../../_static/./custom.css?v=3934642d" />
    
    


<style>
  body {
    --color-code-background: #eeffcc;
  --color-code-foreground: black;
  
  }
  @media not print {
    body[data-theme="dark"] {
      --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8eefba;
  --color-background-primary: #111112;
  --color-problematic: #e7aeae;
  
    }
    @media (prefers-color-scheme: dark) {
      body:not([data-theme="light"]) {
        --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8eefba;
  --color-background-primary: #111112;
  --color-problematic: #e7aeae;
  
      }
    }
  }
</style></head>
  <body>
    
    <script>
      document.body.dataset.theme = localStorage.getItem("theme") || "auto";
    </script>
    

<svg xmlns="http://www.w3.org/2000/svg" style="display: none;">
  <symbol id="svg-toc" viewBox="0 0 24 24">
    <title>Contents</title>
    <svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 1024 1024">
      <path d="M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM115.4 518.9L271.7 642c5.8 4.6 14.4.5 14.4-6.9V388.9c0-7.4-8.5-11.5-14.4-6.9L115.4 505.1a8.74 8.74 0 0 0 0 13.8z"/>
    </svg>
  </symbol>
  <symbol id="svg-menu" viewBox="0 0 24 24">
    <title>Menu</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-menu">
      <line x1="3" y1="12" x2="21" y2="12"></line>
      <line x1="3" y1="6" x2="21" y2="6"></line>
      <line x1="3" y1="18" x2="21" y2="18"></line>
    </svg>
  </symbol>
  <symbol id="svg-arrow-right" viewBox="0 0 24 24">
    <title>Expand</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-chevron-right">
      <polyline points="9 18 15 12 9 6"></polyline>
    </svg>
  </symbol>
  <symbol id="svg-sun" viewBox="0 0 24 24">
    <title>Light mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="feather-sun">
      <circle cx="12" cy="12" r="5"></circle>
      <line x1="12" y1="1" x2="12" y2="3"></line>
      <line x1="12" y1="21" x2="12" y2="23"></line>
      <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
      <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
      <line x1="1" y1="12" x2="3" y2="12"></line>
      <line x1="21" y1="12" x2="23" y2="12"></line>
      <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
      <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
    </svg>
  </symbol>
  <symbol id="svg-moon" viewBox="0 0 24 24">
    <title>Dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-moon">
      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
      <path d="M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z" />
    </svg>
  </symbol>
  <symbol id="svg-sun-half" viewBox="0 0 24 24">
    <title>Auto light/dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-shadow">
      <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
      <circle cx="12" cy="12" r="9" />
      <path d="M13 12h5" />
      <path d="M13 15h4" />
      <path d="M13 18h1" />
      <path d="M13 9h4" />
      <path d="M13 6h1" />
    </svg>
  </symbol>
</svg>

<input type="checkbox" class="sidebar-toggle" name="__navigation" id="__navigation">
<input type="checkbox" class="sidebar-toggle" name="__toc" id="__toc">
<label class="overlay sidebar-overlay" for="__navigation">
  <div class="visually-hidden">Hide navigation sidebar</div>
</label>
<label class="overlay toc-overlay" for="__toc">
  <div class="visually-hidden">Hide table of contents sidebar</div>
</label>



<div class="page">
  <header class="mobile-header">
    <div class="header-left">
      <label class="nav-overlay-icon" for="__navigation">
        <div class="visually-hidden">Toggle site navigation sidebar</div>
        <i class="icon"><svg><use href="#svg-menu"></use></svg></i>
      </label>
    </div>
    <div class="header-center">
      <a href="../../../index.html"><div class="brand">nodriver  documentation</div></a>
    </div>
    <div class="header-right">
      <div class="theme-toggle-container theme-toggle-header">
        <button class="theme-toggle">
          <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
          <svg class="theme-icon-when-auto"><use href="#svg-sun-half"></use></svg>
          <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
          <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
        </button>
      </div>
      <label class="toc-overlay-icon toc-header-icon no-toc" for="__toc">
        <div class="visually-hidden">Toggle table of contents sidebar</div>
        <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
      </label>
    </div>
  </header>
  <aside class="sidebar-drawer">
    <div class="sidebar-container">
      
      <div class="sidebar-sticky"><div class="sidebar-scroll"><a class="sidebar-brand" href="../../../index.html">
  
  
  <span class="sidebar-brand-text">nodriver  documentation</span>
  
</a><form class="sidebar-search-container" method="get" action="../../../search.html" role="search">
  <input class="sidebar-search" placeholder="Search" name="q" aria-label="Search">
  <input type="hidden" name="check_keywords" value="yes">
  <input type="hidden" name="area" value="default">
</form>
<div id="searchbox"></div><div class="sidebar-tree">
  <ul>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html">Quickstart guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#usage-example">usage example</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#more-complete-example">More complete example</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#custom-starting-options">Custom starting options</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#alternative-custom-options">Alternative custom options</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#proxies-socks5-authenticated-too">Proxies (socks5, authenticated too)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#concrete-example">Concrete example</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/classes/browser.html">Browser class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/classes/tab.html">Tab class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/classes/element.html">Element class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/classes/others_and_helpers.html">Other classes and Helper classes</a></li>
</ul>
<ul>
<li class="toctree-l1 has-children"><a class="reference internal" href="../../../nodriver/cdp.html">CDP object</a><input class="toctree-checkbox" id="toctree-checkbox-1" name="toctree-checkbox-1" role="switch" type="checkbox"/><label for="toctree-checkbox-1"><div class="visually-hidden">Toggle navigation of CDP object</div><i class="icon"><svg><use href="#svg-arrow-right"></use></svg></i></label><ul>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/accessibility.html">Accessibility</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/animation.html">Animation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/audits.html">Audits</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/autofill.html">Autofill</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/background_service.html">BackgroundService</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/bluetooth_emulation.html">BluetoothEmulation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/browser.html">Browser</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/cache_storage.html">CacheStorage</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/cast.html">Cast</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/console.html">Console</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/css.html">CSS</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/debugger.html">Debugger</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/device_access.html">DeviceAccess</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/device_orientation.html">DeviceOrientation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/dom.html">DOM</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/dom_debugger.html">DOMDebugger</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/dom_snapshot.html">DOMSnapshot</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/dom_storage.html">DOMStorage</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/emulation.html">Emulation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/event_breakpoints.html">EventBreakpoints</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/extensions.html">Extensions</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/fed_cm.html">FedCm</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/fetch.html">Fetch</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/file_system.html">FileSystem</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/headless_experimental.html">HeadlessExperimental</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/heap_profiler.html">HeapProfiler</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/indexed_db.html">IndexedDB</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/input_.html">Input</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/inspector.html">Inspector</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/io.html">IO</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/layer_tree.html">LayerTree</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/log.html">Log</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/media.html">Media</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/memory.html">Memory</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/network.html">Network</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/overlay.html">Overlay</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/page.html">Page</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/performance.html">Performance</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/performance_timeline.html">PerformanceTimeline</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/preload.html">Preload</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/profiler.html">Profiler</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/pwa.html">PWA</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/runtime.html">Runtime</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/schema.html">Schema</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/security.html">Security</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/service_worker.html">ServiceWorker</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/storage.html">Storage</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/system_info.html">SystemInfo</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/target.html">Target</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/tethering.html">Tethering</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/tracing.html">Tracing</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/web_audio.html">WebAudio</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/web_authn.html">WebAuthn</a></li>
</ul>
</li>
</ul>

</div>
</div>
      </div>
      
    </div>
  </aside>
  <div class="main">
    <div class="content">
      <div class="article-container">
        <a href="#" class="back-to-top muted-link">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
            <path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8v12z"></path>
          </svg>
          <span>Back to top</span>
        </a>
        <div class="content-icon-container">
          <div class="theme-toggle-container theme-toggle-content">
            <button class="theme-toggle">
              <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
              <svg class="theme-icon-when-auto"><use href="#svg-sun-half"></use></svg>
              <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
              <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
            </button>
          </div>
          <label class="toc-overlay-icon toc-content-icon no-toc" for="__toc">
            <div class="visually-hidden">Toggle table of contents sidebar</div>
            <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
          </label>
        </div>
        <article role="main">
          <h1>Source code for nodriver.cdp.runtime</h1><div class="highlight"><pre>
<span></span><span class="c1"># DO NOT EDIT THIS FILE!</span>
<span class="c1">#</span>
<span class="c1"># This file is generated from the CDP specification. If you need to make</span>
<span class="c1"># changes, edit the generator and regenerate all of the modules.</span>
<span class="c1">#</span>
<span class="c1"># CDP domain: Runtime</span>

<span class="kn">from</span> <span class="nn">__future__</span> <span class="kn">import</span> <span class="n">annotations</span>

<span class="kn">import</span> <span class="nn">typing</span>
<span class="kn">from</span> <span class="nn">dataclasses</span> <span class="kn">import</span> <span class="n">dataclass</span>

<span class="kn">from</span> <span class="nn">.util</span> <span class="kn">import</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">event_class</span>


<div class="viewcode-block" id="ScriptId">
<a class="viewcode-back" href="../../../nodriver/cdp/runtime.html#nodriver.cdp.runtime.ScriptId">[docs]</a>
<span class="k">class</span> <span class="nc">ScriptId</span><span class="p">(</span><span class="nb">str</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Unique script identifier.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">ScriptId</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span>

    <span class="k">def</span> <span class="fm">__repr__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="s2">&quot;ScriptId(</span><span class="si">{}</span><span class="s2">)&quot;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="fm">__repr__</span><span class="p">())</span></div>



<div class="viewcode-block" id="SerializationOptions">
<a class="viewcode-back" href="../../../nodriver/cdp/runtime.html#nodriver.cdp.runtime.SerializationOptions">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">SerializationOptions</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Represents options for serialization. Overrides ``generatePreview`` and ``returnByValue``.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">serialization</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: Deep serialization depth. Default is full depth. Respected only in ``deep`` serialization mode.</span>
    <span class="n">max_depth</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">int</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: Embedder-specific parameters. For example if connected to V8 in Chrome these control DOM</span>
    <span class="c1">#: serialization via ``maxNodeDepth: integer`` and ``includeShadowTree: &quot;none&quot; `` &quot;open&quot; `` &quot;all&quot;``.</span>
    <span class="c1">#: Values can be only of type string or integer.</span>
    <span class="n">additional_parameters</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">dict</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;serialization&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">serialization</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">max_depth</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;maxDepth&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">max_depth</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">additional_parameters</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;additionalParameters&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">additional_parameters</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">SerializationOptions</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">serialization</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;serialization&quot;</span><span class="p">]),</span>
            <span class="n">max_depth</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">int</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;maxDepth&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;maxDepth&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">additional_parameters</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">dict</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;additionalParameters&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;additionalParameters&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="DeepSerializedValue">
<a class="viewcode-back" href="../../../nodriver/cdp/runtime.html#nodriver.cdp.runtime.DeepSerializedValue">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">DeepSerializedValue</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Represents deep serialized value.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">type_</span><span class="p">:</span> <span class="nb">str</span>

    <span class="n">value</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">typing</span><span class="o">.</span><span class="n">Any</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="n">object_id</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: Set if value reference met more then once during serialization. In such</span>
    <span class="c1">#: case, value is provided only to one of the serialized values. Unique</span>
    <span class="c1">#: per value in the scope of one CDP call.</span>
    <span class="n">weak_local_object_reference</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">int</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;type&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">type_</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;value&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">object_id</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;objectId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">object_id</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">weak_local_object_reference</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;weakLocalObjectReference&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">weak_local_object_reference</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">DeepSerializedValue</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">type_</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;type&quot;</span><span class="p">]),</span>
            <span class="n">value</span><span class="o">=</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;value&quot;</span><span class="p">]</span> <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;value&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span> <span class="k">else</span> <span class="kc">None</span><span class="p">,</span>
            <span class="n">object_id</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;objectId&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;objectId&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">weak_local_object_reference</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">int</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;weakLocalObjectReference&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;weakLocalObjectReference&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="RemoteObjectId">
<a class="viewcode-back" href="../../../nodriver/cdp/runtime.html#nodriver.cdp.runtime.RemoteObjectId">[docs]</a>
<span class="k">class</span> <span class="nc">RemoteObjectId</span><span class="p">(</span><span class="nb">str</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Unique object identifier.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">RemoteObjectId</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span>

    <span class="k">def</span> <span class="fm">__repr__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="s2">&quot;RemoteObjectId(</span><span class="si">{}</span><span class="s2">)&quot;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="fm">__repr__</span><span class="p">())</span></div>



<div class="viewcode-block" id="UnserializableValue">
<a class="viewcode-back" href="../../../nodriver/cdp/runtime.html#nodriver.cdp.runtime.UnserializableValue">[docs]</a>
<span class="k">class</span> <span class="nc">UnserializableValue</span><span class="p">(</span><span class="nb">str</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Primitive value which cannot be JSON-stringified. Includes values ``-0``, ``NaN``, ``Infinity``,</span>
<span class="sd">    ``-Infinity``, and bigint literals.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">UnserializableValue</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span>

    <span class="k">def</span> <span class="fm">__repr__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="s2">&quot;UnserializableValue(</span><span class="si">{}</span><span class="s2">)&quot;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="fm">__repr__</span><span class="p">())</span></div>



<div class="viewcode-block" id="RemoteObject">
<a class="viewcode-back" href="../../../nodriver/cdp/runtime.html#nodriver.cdp.runtime.RemoteObject">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">RemoteObject</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Mirror object referencing original JavaScript object.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: Object type.</span>
    <span class="n">type_</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: Object subtype hint. Specified for ``object`` type values only.</span>
    <span class="c1">#: NOTE: If you change anything here, make sure to also update</span>
    <span class="c1">#: ``subtype`` in ``ObjectPreview`` and ``PropertyPreview`` below.</span>
    <span class="n">subtype</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: Object class (constructor) name. Specified for ``object`` type values only.</span>
    <span class="n">class_name</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: Remote object value in case of primitive values or JSON values (if it was requested).</span>
    <span class="n">value</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">typing</span><span class="o">.</span><span class="n">Any</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: Primitive value which can not be JSON-stringified does not have ``value``, but gets this</span>
    <span class="c1">#: property.</span>
    <span class="n">unserializable_value</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">UnserializableValue</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: String representation of the object.</span>
    <span class="n">description</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: Deep serialized value.</span>
    <span class="n">deep_serialized_value</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">DeepSerializedValue</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: Unique object identifier (for non-primitive values).</span>
    <span class="n">object_id</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">RemoteObjectId</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: Preview containing abbreviated property values. Specified for ``object`` type values only.</span>
    <span class="n">preview</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">ObjectPreview</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="n">custom_preview</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">CustomPreview</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;type&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">type_</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">subtype</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;subtype&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">subtype</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">class_name</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;className&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">class_name</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;value&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">unserializable_value</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;unserializableValue&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">unserializable_value</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">description</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;description&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">description</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">deep_serialized_value</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;deepSerializedValue&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">deep_serialized_value</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">object_id</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;objectId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">object_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">preview</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;preview&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">preview</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">custom_preview</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;customPreview&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">custom_preview</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">RemoteObject</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">type_</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;type&quot;</span><span class="p">]),</span>
            <span class="n">subtype</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;subtype&quot;</span><span class="p">])</span> <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;subtype&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span> <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">class_name</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;className&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;className&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">value</span><span class="o">=</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;value&quot;</span><span class="p">]</span> <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;value&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span> <span class="k">else</span> <span class="kc">None</span><span class="p">,</span>
            <span class="n">unserializable_value</span><span class="o">=</span><span class="p">(</span>
                <span class="n">UnserializableValue</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;unserializableValue&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;unserializableValue&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">description</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;description&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;description&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">deep_serialized_value</span><span class="o">=</span><span class="p">(</span>
                <span class="n">DeepSerializedValue</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;deepSerializedValue&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;deepSerializedValue&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">object_id</span><span class="o">=</span><span class="p">(</span>
                <span class="n">RemoteObjectId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;objectId&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;objectId&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">preview</span><span class="o">=</span><span class="p">(</span>
                <span class="n">ObjectPreview</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;preview&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;preview&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">custom_preview</span><span class="o">=</span><span class="p">(</span>
                <span class="n">CustomPreview</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;customPreview&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;customPreview&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="CustomPreview">
<a class="viewcode-back" href="../../../nodriver/cdp/runtime.html#nodriver.cdp.runtime.CustomPreview">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">CustomPreview</span><span class="p">:</span>
    <span class="c1">#: The JSON-stringified result of formatter.header(object, config) call.</span>
    <span class="c1">#: It contains json ML array that represents RemoteObject.</span>
    <span class="n">header</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: If formatter returns true as a result of formatter.hasBody call then bodyGetterId will</span>
    <span class="c1">#: contain RemoteObjectId for the function that returns result of formatter.body(object, config) call.</span>
    <span class="c1">#: The result value is json ML array.</span>
    <span class="n">body_getter_id</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">RemoteObjectId</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;header&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">header</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">body_getter_id</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;bodyGetterId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">body_getter_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">CustomPreview</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">header</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;header&quot;</span><span class="p">]),</span>
            <span class="n">body_getter_id</span><span class="o">=</span><span class="p">(</span>
                <span class="n">RemoteObjectId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;bodyGetterId&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;bodyGetterId&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="ObjectPreview">
<a class="viewcode-back" href="../../../nodriver/cdp/runtime.html#nodriver.cdp.runtime.ObjectPreview">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">ObjectPreview</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Object containing abbreviated remote object value.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: Object type.</span>
    <span class="n">type_</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: True iff some of the properties or entries of the original object did not fit.</span>
    <span class="n">overflow</span><span class="p">:</span> <span class="nb">bool</span>

    <span class="c1">#: List of the properties.</span>
    <span class="n">properties</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">PropertyPreview</span><span class="p">]</span>

    <span class="c1">#: Object subtype hint. Specified for ``object`` type values only.</span>
    <span class="n">subtype</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: String representation of the object.</span>
    <span class="n">description</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: List of the entries. Specified for ``map`` and ``set`` subtype values only.</span>
    <span class="n">entries</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">EntryPreview</span><span class="p">]]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;type&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">type_</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;overflow&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">overflow</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;properties&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">properties</span><span class="p">]</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">subtype</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;subtype&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">subtype</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">description</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;description&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">description</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">entries</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;entries&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">entries</span><span class="p">]</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">ObjectPreview</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">type_</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;type&quot;</span><span class="p">]),</span>
            <span class="n">overflow</span><span class="o">=</span><span class="nb">bool</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;overflow&quot;</span><span class="p">]),</span>
            <span class="n">properties</span><span class="o">=</span><span class="p">[</span><span class="n">PropertyPreview</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;properties&quot;</span><span class="p">]],</span>
            <span class="n">subtype</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;subtype&quot;</span><span class="p">])</span> <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;subtype&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span> <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">description</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;description&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;description&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">entries</span><span class="o">=</span><span class="p">(</span>
                <span class="p">[</span><span class="n">EntryPreview</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;entries&quot;</span><span class="p">]]</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;entries&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="PropertyPreview">
<a class="viewcode-back" href="../../../nodriver/cdp/runtime.html#nodriver.cdp.runtime.PropertyPreview">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">PropertyPreview</span><span class="p">:</span>
    <span class="c1">#: Property name.</span>
    <span class="n">name</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: Object type. Accessor means that the property itself is an accessor property.</span>
    <span class="n">type_</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: User-friendly property value string.</span>
    <span class="n">value</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: Nested value preview.</span>
    <span class="n">value_preview</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">ObjectPreview</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: Object subtype hint. Specified for ``object`` type values only.</span>
    <span class="n">subtype</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;name&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">name</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;type&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">type_</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;value&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">value_preview</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;valuePreview&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">value_preview</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">subtype</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;subtype&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">subtype</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">PropertyPreview</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">name</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;name&quot;</span><span class="p">]),</span>
            <span class="n">type_</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;type&quot;</span><span class="p">]),</span>
            <span class="n">value</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;value&quot;</span><span class="p">])</span> <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;value&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span> <span class="k">else</span> <span class="kc">None</span><span class="p">,</span>
            <span class="n">value_preview</span><span class="o">=</span><span class="p">(</span>
                <span class="n">ObjectPreview</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;valuePreview&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;valuePreview&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">subtype</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;subtype&quot;</span><span class="p">])</span> <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;subtype&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span> <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="EntryPreview">
<a class="viewcode-back" href="../../../nodriver/cdp/runtime.html#nodriver.cdp.runtime.EntryPreview">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">EntryPreview</span><span class="p">:</span>
    <span class="c1">#: Preview of the value.</span>
    <span class="n">value</span><span class="p">:</span> <span class="n">ObjectPreview</span>

    <span class="c1">#: Preview of the key. Specified for map-like collection entries.</span>
    <span class="n">key</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">ObjectPreview</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;value&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">key</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;key&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">key</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">EntryPreview</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">value</span><span class="o">=</span><span class="n">ObjectPreview</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;value&quot;</span><span class="p">]),</span>
            <span class="n">key</span><span class="o">=</span><span class="p">(</span>
                <span class="n">ObjectPreview</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;key&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;key&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="PropertyDescriptor">
<a class="viewcode-back" href="../../../nodriver/cdp/runtime.html#nodriver.cdp.runtime.PropertyDescriptor">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">PropertyDescriptor</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Object property descriptor.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: Property name or symbol description.</span>
    <span class="n">name</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: True if the type of this property descriptor may be changed and if the property may be</span>
    <span class="c1">#: deleted from the corresponding object.</span>
    <span class="n">configurable</span><span class="p">:</span> <span class="nb">bool</span>

    <span class="c1">#: True if this property shows up during enumeration of the properties on the corresponding</span>
    <span class="c1">#: object.</span>
    <span class="n">enumerable</span><span class="p">:</span> <span class="nb">bool</span>

    <span class="c1">#: The value associated with the property.</span>
    <span class="n">value</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">RemoteObject</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: True if the value associated with the property may be changed (data descriptors only).</span>
    <span class="n">writable</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">bool</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: A function which serves as a getter for the property, or ``undefined`` if there is no getter</span>
    <span class="c1">#: (accessor descriptors only).</span>
    <span class="n">get</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">RemoteObject</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: A function which serves as a setter for the property, or ``undefined`` if there is no setter</span>
    <span class="c1">#: (accessor descriptors only).</span>
    <span class="n">set_</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">RemoteObject</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: True if the result was thrown during the evaluation.</span>
    <span class="n">was_thrown</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">bool</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: True if the property is owned for the object.</span>
    <span class="n">is_own</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">bool</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: Property symbol object, if the property is of the ``symbol`` type.</span>
    <span class="n">symbol</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">RemoteObject</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;name&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">name</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;configurable&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">configurable</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;enumerable&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">enumerable</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;value&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">writable</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;writable&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">writable</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">get</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;get&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">get</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">set_</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;set&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">set_</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">was_thrown</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;wasThrown&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">was_thrown</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">is_own</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;isOwn&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">is_own</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">symbol</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;symbol&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">symbol</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">PropertyDescriptor</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">name</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;name&quot;</span><span class="p">]),</span>
            <span class="n">configurable</span><span class="o">=</span><span class="nb">bool</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;configurable&quot;</span><span class="p">]),</span>
            <span class="n">enumerable</span><span class="o">=</span><span class="nb">bool</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;enumerable&quot;</span><span class="p">]),</span>
            <span class="n">value</span><span class="o">=</span><span class="p">(</span>
                <span class="n">RemoteObject</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;value&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;value&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">writable</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">bool</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;writable&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;writable&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">get</span><span class="o">=</span><span class="p">(</span>
                <span class="n">RemoteObject</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;get&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;get&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">set_</span><span class="o">=</span><span class="p">(</span>
                <span class="n">RemoteObject</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;set&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;set&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">was_thrown</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">bool</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;wasThrown&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;wasThrown&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">is_own</span><span class="o">=</span><span class="nb">bool</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;isOwn&quot;</span><span class="p">])</span> <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;isOwn&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span> <span class="k">else</span> <span class="kc">None</span><span class="p">,</span>
            <span class="n">symbol</span><span class="o">=</span><span class="p">(</span>
                <span class="n">RemoteObject</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;symbol&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;symbol&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="InternalPropertyDescriptor">
<a class="viewcode-back" href="../../../nodriver/cdp/runtime.html#nodriver.cdp.runtime.InternalPropertyDescriptor">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">InternalPropertyDescriptor</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Object internal property descriptor. This property isn&#39;t normally visible in JavaScript code.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: Conventional property name.</span>
    <span class="n">name</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: The value associated with the property.</span>
    <span class="n">value</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">RemoteObject</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;name&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">name</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;value&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">InternalPropertyDescriptor</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">name</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;name&quot;</span><span class="p">]),</span>
            <span class="n">value</span><span class="o">=</span><span class="p">(</span>
                <span class="n">RemoteObject</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;value&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;value&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="PrivatePropertyDescriptor">
<a class="viewcode-back" href="../../../nodriver/cdp/runtime.html#nodriver.cdp.runtime.PrivatePropertyDescriptor">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">PrivatePropertyDescriptor</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Object private field descriptor.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: Private property name.</span>
    <span class="n">name</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: The value associated with the private property.</span>
    <span class="n">value</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">RemoteObject</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: A function which serves as a getter for the private property,</span>
    <span class="c1">#: or ``undefined`` if there is no getter (accessor descriptors only).</span>
    <span class="n">get</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">RemoteObject</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: A function which serves as a setter for the private property,</span>
    <span class="c1">#: or ``undefined`` if there is no setter (accessor descriptors only).</span>
    <span class="n">set_</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">RemoteObject</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;name&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">name</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;value&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">get</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;get&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">get</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">set_</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;set&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">set_</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">PrivatePropertyDescriptor</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">name</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;name&quot;</span><span class="p">]),</span>
            <span class="n">value</span><span class="o">=</span><span class="p">(</span>
                <span class="n">RemoteObject</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;value&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;value&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">get</span><span class="o">=</span><span class="p">(</span>
                <span class="n">RemoteObject</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;get&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;get&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">set_</span><span class="o">=</span><span class="p">(</span>
                <span class="n">RemoteObject</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;set&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;set&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="CallArgument">
<a class="viewcode-back" href="../../../nodriver/cdp/runtime.html#nodriver.cdp.runtime.CallArgument">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">CallArgument</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Represents function call argument. Either remote object id ``objectId``, primitive ``value``,</span>
<span class="sd">    unserializable primitive value or neither of (for undefined) them should be specified.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: Primitive value or serializable javascript object.</span>
    <span class="n">value</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">typing</span><span class="o">.</span><span class="n">Any</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: Primitive value which can not be JSON-stringified.</span>
    <span class="n">unserializable_value</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">UnserializableValue</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: Remote object handle.</span>
    <span class="n">object_id</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">RemoteObjectId</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;value&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">unserializable_value</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;unserializableValue&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">unserializable_value</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">object_id</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;objectId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">object_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">CallArgument</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">value</span><span class="o">=</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;value&quot;</span><span class="p">]</span> <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;value&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span> <span class="k">else</span> <span class="kc">None</span><span class="p">,</span>
            <span class="n">unserializable_value</span><span class="o">=</span><span class="p">(</span>
                <span class="n">UnserializableValue</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;unserializableValue&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;unserializableValue&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">object_id</span><span class="o">=</span><span class="p">(</span>
                <span class="n">RemoteObjectId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;objectId&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;objectId&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="ExecutionContextId">
<a class="viewcode-back" href="../../../nodriver/cdp/runtime.html#nodriver.cdp.runtime.ExecutionContextId">[docs]</a>
<span class="k">class</span> <span class="nc">ExecutionContextId</span><span class="p">(</span><span class="nb">int</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Id of an execution context.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">int</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">int</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">ExecutionContextId</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span>

    <span class="k">def</span> <span class="fm">__repr__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="s2">&quot;ExecutionContextId(</span><span class="si">{}</span><span class="s2">)&quot;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="fm">__repr__</span><span class="p">())</span></div>



<div class="viewcode-block" id="ExecutionContextDescription">
<a class="viewcode-back" href="../../../nodriver/cdp/runtime.html#nodriver.cdp.runtime.ExecutionContextDescription">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">ExecutionContextDescription</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Description of an isolated world.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: Unique id of the execution context. It can be used to specify in which execution context</span>
    <span class="c1">#: script evaluation should be performed.</span>
    <span class="n">id_</span><span class="p">:</span> <span class="n">ExecutionContextId</span>

    <span class="c1">#: Execution context origin.</span>
    <span class="n">origin</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: Human readable name describing given context.</span>
    <span class="n">name</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: A system-unique execution context identifier. Unlike the id, this is unique across</span>
    <span class="c1">#: multiple processes, so can be reliably used to identify specific context while backend</span>
    <span class="c1">#: performs a cross-process navigation.</span>
    <span class="n">unique_id</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: Embedder-specific auxiliary data likely matching {isDefault: boolean, type: &#39;default&#39;``&#39;isolated&#39;``&#39;worker&#39;, frameId: string}</span>
    <span class="n">aux_data</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">dict</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;id&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">id_</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;origin&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">origin</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;name&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">name</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;uniqueId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">unique_id</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">aux_data</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;auxData&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">aux_data</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">ExecutionContextDescription</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">id_</span><span class="o">=</span><span class="n">ExecutionContextId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;id&quot;</span><span class="p">]),</span>
            <span class="n">origin</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;origin&quot;</span><span class="p">]),</span>
            <span class="n">name</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;name&quot;</span><span class="p">]),</span>
            <span class="n">unique_id</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;uniqueId&quot;</span><span class="p">]),</span>
            <span class="n">aux_data</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">dict</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;auxData&quot;</span><span class="p">])</span> <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;auxData&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span> <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="ExceptionDetails">
<a class="viewcode-back" href="../../../nodriver/cdp/runtime.html#nodriver.cdp.runtime.ExceptionDetails">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">ExceptionDetails</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Detailed information about exception (or error) that was thrown during script compilation or</span>
<span class="sd">    execution.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: Exception id.</span>
    <span class="n">exception_id</span><span class="p">:</span> <span class="nb">int</span>

    <span class="c1">#: Exception text, which should be used together with exception object when available.</span>
    <span class="n">text</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: Line number of the exception location (0-based).</span>
    <span class="n">line_number</span><span class="p">:</span> <span class="nb">int</span>

    <span class="c1">#: Column number of the exception location (0-based).</span>
    <span class="n">column_number</span><span class="p">:</span> <span class="nb">int</span>

    <span class="c1">#: Script ID of the exception location.</span>
    <span class="n">script_id</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">ScriptId</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: URL of the exception location, to be used when the script was not reported.</span>
    <span class="n">url</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: JavaScript stack trace if available.</span>
    <span class="n">stack_trace</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">StackTrace</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: Exception object if available.</span>
    <span class="n">exception</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">RemoteObject</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: Identifier of the context where exception happened.</span>
    <span class="n">execution_context_id</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">ExecutionContextId</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: Dictionary with entries of meta data that the client associated</span>
    <span class="c1">#: with this exception, such as information about associated network</span>
    <span class="c1">#: requests, etc.</span>
    <span class="n">exception_meta_data</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">dict</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;exceptionId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">exception_id</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;text&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">text</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;lineNumber&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">line_number</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;columnNumber&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">column_number</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">script_id</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;scriptId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">script_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">url</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;url&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">url</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">stack_trace</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;stackTrace&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">stack_trace</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">exception</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;exception&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">exception</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">execution_context_id</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;executionContextId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">execution_context_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">exception_meta_data</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;exceptionMetaData&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">exception_meta_data</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">ExceptionDetails</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">exception_id</span><span class="o">=</span><span class="nb">int</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;exceptionId&quot;</span><span class="p">]),</span>
            <span class="n">text</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;text&quot;</span><span class="p">]),</span>
            <span class="n">line_number</span><span class="o">=</span><span class="nb">int</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;lineNumber&quot;</span><span class="p">]),</span>
            <span class="n">column_number</span><span class="o">=</span><span class="nb">int</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;columnNumber&quot;</span><span class="p">]),</span>
            <span class="n">script_id</span><span class="o">=</span><span class="p">(</span>
                <span class="n">ScriptId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;scriptId&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;scriptId&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">url</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;url&quot;</span><span class="p">])</span> <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;url&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span> <span class="k">else</span> <span class="kc">None</span><span class="p">,</span>
            <span class="n">stack_trace</span><span class="o">=</span><span class="p">(</span>
                <span class="n">StackTrace</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;stackTrace&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;stackTrace&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">exception</span><span class="o">=</span><span class="p">(</span>
                <span class="n">RemoteObject</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;exception&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;exception&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">execution_context_id</span><span class="o">=</span><span class="p">(</span>
                <span class="n">ExecutionContextId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;executionContextId&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;executionContextId&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">exception_meta_data</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">dict</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;exceptionMetaData&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;exceptionMetaData&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="Timestamp">
<a class="viewcode-back" href="../../../nodriver/cdp/runtime.html#nodriver.cdp.runtime.Timestamp">[docs]</a>
<span class="k">class</span> <span class="nc">Timestamp</span><span class="p">(</span><span class="nb">float</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Number of milliseconds since epoch.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">float</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">float</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Timestamp</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span>

    <span class="k">def</span> <span class="fm">__repr__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="s2">&quot;Timestamp(</span><span class="si">{}</span><span class="s2">)&quot;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="fm">__repr__</span><span class="p">())</span></div>



<div class="viewcode-block" id="TimeDelta">
<a class="viewcode-back" href="../../../nodriver/cdp/runtime.html#nodriver.cdp.runtime.TimeDelta">[docs]</a>
<span class="k">class</span> <span class="nc">TimeDelta</span><span class="p">(</span><span class="nb">float</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Number of milliseconds.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">float</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">float</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">TimeDelta</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span>

    <span class="k">def</span> <span class="fm">__repr__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="s2">&quot;TimeDelta(</span><span class="si">{}</span><span class="s2">)&quot;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="fm">__repr__</span><span class="p">())</span></div>



<div class="viewcode-block" id="CallFrame">
<a class="viewcode-back" href="../../../nodriver/cdp/runtime.html#nodriver.cdp.runtime.CallFrame">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">CallFrame</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Stack entry for runtime errors and assertions.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: JavaScript function name.</span>
    <span class="n">function_name</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: JavaScript script id.</span>
    <span class="n">script_id</span><span class="p">:</span> <span class="n">ScriptId</span>

    <span class="c1">#: JavaScript script name or url.</span>
    <span class="n">url</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: JavaScript script line number (0-based).</span>
    <span class="n">line_number</span><span class="p">:</span> <span class="nb">int</span>

    <span class="c1">#: JavaScript script column number (0-based).</span>
    <span class="n">column_number</span><span class="p">:</span> <span class="nb">int</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;functionName&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">function_name</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;scriptId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">script_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;url&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">url</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;lineNumber&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">line_number</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;columnNumber&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">column_number</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">CallFrame</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">function_name</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;functionName&quot;</span><span class="p">]),</span>
            <span class="n">script_id</span><span class="o">=</span><span class="n">ScriptId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;scriptId&quot;</span><span class="p">]),</span>
            <span class="n">url</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;url&quot;</span><span class="p">]),</span>
            <span class="n">line_number</span><span class="o">=</span><span class="nb">int</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;lineNumber&quot;</span><span class="p">]),</span>
            <span class="n">column_number</span><span class="o">=</span><span class="nb">int</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;columnNumber&quot;</span><span class="p">]),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="StackTrace">
<a class="viewcode-back" href="../../../nodriver/cdp/runtime.html#nodriver.cdp.runtime.StackTrace">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">StackTrace</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Call frames for assertions or error messages.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: JavaScript function name.</span>
    <span class="n">call_frames</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">CallFrame</span><span class="p">]</span>

    <span class="c1">#: String label of this stack trace. For async traces this may be a name of the function that</span>
    <span class="c1">#: initiated the async call.</span>
    <span class="n">description</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: Asynchronous JavaScript stack trace that preceded this stack, if available.</span>
    <span class="n">parent</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">StackTrace</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: Asynchronous JavaScript stack trace that preceded this stack, if available.</span>
    <span class="n">parent_id</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">StackTraceId</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;callFrames&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">call_frames</span><span class="p">]</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">description</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;description&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">description</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">parent</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;parent&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">parent</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">parent_id</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;parentId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">parent_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">StackTrace</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">call_frames</span><span class="o">=</span><span class="p">[</span><span class="n">CallFrame</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;callFrames&quot;</span><span class="p">]],</span>
            <span class="n">description</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;description&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;description&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">parent</span><span class="o">=</span><span class="p">(</span>
                <span class="n">StackTrace</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;parent&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;parent&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">parent_id</span><span class="o">=</span><span class="p">(</span>
                <span class="n">StackTraceId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;parentId&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;parentId&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="UniqueDebuggerId">
<a class="viewcode-back" href="../../../nodriver/cdp/runtime.html#nodriver.cdp.runtime.UniqueDebuggerId">[docs]</a>
<span class="k">class</span> <span class="nc">UniqueDebuggerId</span><span class="p">(</span><span class="nb">str</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Unique identifier of current debugger.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">UniqueDebuggerId</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span>

    <span class="k">def</span> <span class="fm">__repr__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="s2">&quot;UniqueDebuggerId(</span><span class="si">{}</span><span class="s2">)&quot;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="fm">__repr__</span><span class="p">())</span></div>



<div class="viewcode-block" id="StackTraceId">
<a class="viewcode-back" href="../../../nodriver/cdp/runtime.html#nodriver.cdp.runtime.StackTraceId">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">StackTraceId</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    If ``debuggerId`` is set stack trace comes from another debugger and can be resolved there. This</span>
<span class="sd">    allows to track cross-debugger calls. See ``Runtime.StackTrace`` and ``Debugger.paused`` for usages.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">id_</span><span class="p">:</span> <span class="nb">str</span>

    <span class="n">debugger_id</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">UniqueDebuggerId</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;id&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">id_</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">debugger_id</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;debuggerId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">debugger_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">StackTraceId</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">id_</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;id&quot;</span><span class="p">]),</span>
            <span class="n">debugger_id</span><span class="o">=</span><span class="p">(</span>
                <span class="n">UniqueDebuggerId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;debuggerId&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;debuggerId&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="await_promise">
<a class="viewcode-back" href="../../../nodriver/cdp/runtime.html#nodriver.cdp.runtime.await_promise">[docs]</a>
<span class="k">def</span> <span class="nf">await_promise</span><span class="p">(</span>
        <span class="n">promise_object_id</span><span class="p">:</span> <span class="n">RemoteObjectId</span><span class="p">,</span>
        <span class="n">return_by_value</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">bool</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
        <span class="n">generate_preview</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">bool</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span>
    <span class="n">T_JSON_DICT</span><span class="p">,</span>
    <span class="n">T_JSON_DICT</span><span class="p">,</span>
    <span class="n">typing</span><span class="o">.</span><span class="n">Tuple</span><span class="p">[</span><span class="n">RemoteObject</span><span class="p">,</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">ExceptionDetails</span><span class="p">]],</span>
<span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Add handler to promise with given promise object id.</span>

<span class="sd">    :param promise_object_id: Identifier of the promise.</span>
<span class="sd">    :param return_by_value: *(Optional)* Whether the result is expected to be a JSON object that should be sent by value.</span>
<span class="sd">    :param generate_preview: *(Optional)* Whether preview should be generated for the result.</span>
<span class="sd">    :returns: A tuple with the following items:</span>

<span class="sd">        0. **result** - Promise result. Will contain rejected value if promise was rejected.</span>
<span class="sd">        1. **exceptionDetails** - *(Optional)* Exception details if stack strace is available.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;promiseObjectId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">promise_object_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="k">if</span> <span class="n">return_by_value</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;returnByValue&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">return_by_value</span>
    <span class="k">if</span> <span class="n">generate_preview</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;generatePreview&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">generate_preview</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Runtime.awaitPromise&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span>
    <span class="k">return</span> <span class="p">(</span>
        <span class="n">RemoteObject</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;result&quot;</span><span class="p">]),</span>
        <span class="p">(</span>
            <span class="n">ExceptionDetails</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;exceptionDetails&quot;</span><span class="p">])</span>
            <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;exceptionDetails&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
            <span class="k">else</span> <span class="kc">None</span>
        <span class="p">),</span>
    <span class="p">)</span></div>



<div class="viewcode-block" id="call_function_on">
<a class="viewcode-back" href="../../../nodriver/cdp/runtime.html#nodriver.cdp.runtime.call_function_on">[docs]</a>
<span class="k">def</span> <span class="nf">call_function_on</span><span class="p">(</span>
        <span class="n">function_declaration</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span>
        <span class="n">object_id</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">RemoteObjectId</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
        <span class="n">arguments</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">CallArgument</span><span class="p">]]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
        <span class="n">silent</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">bool</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
        <span class="n">return_by_value</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">bool</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
        <span class="n">generate_preview</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">bool</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
        <span class="n">user_gesture</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">bool</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
        <span class="n">await_promise</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">bool</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
        <span class="n">execution_context_id</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">ExecutionContextId</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
        <span class="n">object_group</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
        <span class="n">throw_on_side_effect</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">bool</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
        <span class="n">unique_context_id</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
        <span class="n">serialization_options</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">SerializationOptions</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span>
    <span class="n">T_JSON_DICT</span><span class="p">,</span>
    <span class="n">T_JSON_DICT</span><span class="p">,</span>
    <span class="n">typing</span><span class="o">.</span><span class="n">Tuple</span><span class="p">[</span><span class="n">RemoteObject</span><span class="p">,</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">ExceptionDetails</span><span class="p">]],</span>
<span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Calls function with given declaration on the given object. Object group of the result is</span>
<span class="sd">    inherited from the target object.</span>

<span class="sd">    :param function_declaration: Declaration of the function to call.</span>
<span class="sd">    :param object_id: *(Optional)* Identifier of the object to call function on. Either objectId or executionContextId should be specified.</span>
<span class="sd">    :param arguments: *(Optional)* Call arguments. All call arguments must belong to the same JavaScript world as the target object.</span>
<span class="sd">    :param silent: *(Optional)* In silent mode exceptions thrown during evaluation are not reported and do not pause execution. Overrides ```setPauseOnException```` state.</span>
<span class="sd">    :param return_by_value: *(Optional)* Whether the result is expected to be a JSON object which should be sent by value. Can be overriden by ````serializationOptions````.</span>
<span class="sd">    :param generate_preview: **(EXPERIMENTAL)** *(Optional)* Whether preview should be generated for the result.</span>
<span class="sd">    :param user_gesture: *(Optional)* Whether execution should be treated as initiated by user in the UI.</span>
<span class="sd">    :param await_promise: *(Optional)* Whether execution should ````await```` for resulting value and return once awaited promise is resolved.</span>
<span class="sd">    :param execution_context_id: *(Optional)* Specifies execution context which global object will be used to call function on. Either executionContextId or objectId should be specified.</span>
<span class="sd">    :param object_group: *(Optional)* Symbolic group name that can be used to release multiple objects. If objectGroup is not specified and objectId is, objectGroup will be inherited from object.</span>
<span class="sd">    :param throw_on_side_effect: **(EXPERIMENTAL)** *(Optional)* Whether to throw an exception if side effect cannot be ruled out during evaluation.</span>
<span class="sd">    :param unique_context_id: **(EXPERIMENTAL)** *(Optional)* An alternative way to specify the execution context to call function on. Compared to contextId that may be reused across processes, this is guaranteed to be system-unique, so it can be used to prevent accidental function call in context different than intended (e.g. as a result of navigation across process boundaries). This is mutually exclusive with ````executionContextId````.</span>
<span class="sd">    :param serialization_options: **(EXPERIMENTAL)** *(Optional)* Specifies the result serialization. If provided, overrides ````generatePreview```` and ````returnByValue```.</span>
<span class="sd">    :returns: A tuple with the following items:</span>

<span class="sd">        0. **result** - Call result.</span>
<span class="sd">        1. **exceptionDetails** - *(Optional)* Exception details.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;functionDeclaration&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">function_declaration</span>
    <span class="k">if</span> <span class="n">object_id</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;objectId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">object_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="k">if</span> <span class="n">arguments</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;arguments&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">arguments</span><span class="p">]</span>
    <span class="k">if</span> <span class="n">silent</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;silent&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">silent</span>
    <span class="k">if</span> <span class="n">return_by_value</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;returnByValue&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">return_by_value</span>
    <span class="k">if</span> <span class="n">generate_preview</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;generatePreview&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">generate_preview</span>
    <span class="k">if</span> <span class="n">user_gesture</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;userGesture&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">user_gesture</span>
    <span class="k">if</span> <span class="n">await_promise</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;awaitPromise&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">await_promise</span>
    <span class="k">if</span> <span class="n">execution_context_id</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;executionContextId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">execution_context_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="k">if</span> <span class="n">object_group</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;objectGroup&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">object_group</span>
    <span class="k">if</span> <span class="n">throw_on_side_effect</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;throwOnSideEffect&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">throw_on_side_effect</span>
    <span class="k">if</span> <span class="n">unique_context_id</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;uniqueContextId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">unique_context_id</span>
    <span class="k">if</span> <span class="n">serialization_options</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;serializationOptions&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">serialization_options</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Runtime.callFunctionOn&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span>
    <span class="k">return</span> <span class="p">(</span>
        <span class="n">RemoteObject</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;result&quot;</span><span class="p">]),</span>
        <span class="p">(</span>
            <span class="n">ExceptionDetails</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;exceptionDetails&quot;</span><span class="p">])</span>
            <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;exceptionDetails&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
            <span class="k">else</span> <span class="kc">None</span>
        <span class="p">),</span>
    <span class="p">)</span></div>



<div class="viewcode-block" id="compile_script">
<a class="viewcode-back" href="../../../nodriver/cdp/runtime.html#nodriver.cdp.runtime.compile_script">[docs]</a>
<span class="k">def</span> <span class="nf">compile_script</span><span class="p">(</span>
        <span class="n">expression</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span>
        <span class="n">source_url</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span>
        <span class="n">persist_script</span><span class="p">:</span> <span class="nb">bool</span><span class="p">,</span>
        <span class="n">execution_context_id</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">ExecutionContextId</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span>
    <span class="n">T_JSON_DICT</span><span class="p">,</span>
    <span class="n">T_JSON_DICT</span><span class="p">,</span>
    <span class="n">typing</span><span class="o">.</span><span class="n">Tuple</span><span class="p">[</span><span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">ScriptId</span><span class="p">],</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">ExceptionDetails</span><span class="p">]],</span>
<span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Compiles expression.</span>

<span class="sd">    :param expression: Expression to compile.</span>
<span class="sd">    :param source_url: Source url to be set for the script.</span>
<span class="sd">    :param persist_script: Specifies whether the compiled script should be persisted.</span>
<span class="sd">    :param execution_context_id: *(Optional)* Specifies in which execution context to perform script run. If the parameter is omitted the evaluation will be performed in the context of the inspected page.</span>
<span class="sd">    :returns: A tuple with the following items:</span>

<span class="sd">        0. **scriptId** - *(Optional)* Id of the script.</span>
<span class="sd">        1. **exceptionDetails** - *(Optional)* Exception details.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;expression&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">expression</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;sourceURL&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">source_url</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;persistScript&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">persist_script</span>
    <span class="k">if</span> <span class="n">execution_context_id</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;executionContextId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">execution_context_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Runtime.compileScript&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span>
    <span class="k">return</span> <span class="p">(</span>
        <span class="p">(</span>
            <span class="n">ScriptId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;scriptId&quot;</span><span class="p">])</span>
            <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;scriptId&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
            <span class="k">else</span> <span class="kc">None</span>
        <span class="p">),</span>
        <span class="p">(</span>
            <span class="n">ExceptionDetails</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;exceptionDetails&quot;</span><span class="p">])</span>
            <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;exceptionDetails&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
            <span class="k">else</span> <span class="kc">None</span>
        <span class="p">),</span>
    <span class="p">)</span></div>



<div class="viewcode-block" id="disable">
<a class="viewcode-back" href="../../../nodriver/cdp/runtime.html#nodriver.cdp.runtime.disable">[docs]</a>
<span class="k">def</span> <span class="nf">disable</span><span class="p">()</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Disables reporting of execution contexts creation.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Runtime.disable&quot;</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span></div>



<div class="viewcode-block" id="discard_console_entries">
<a class="viewcode-back" href="../../../nodriver/cdp/runtime.html#nodriver.cdp.runtime.discard_console_entries">[docs]</a>
<span class="k">def</span> <span class="nf">discard_console_entries</span><span class="p">()</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Discards collected exceptions and console API calls.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Runtime.discardConsoleEntries&quot;</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span></div>



<div class="viewcode-block" id="enable">
<a class="viewcode-back" href="../../../nodriver/cdp/runtime.html#nodriver.cdp.runtime.enable">[docs]</a>
<span class="k">def</span> <span class="nf">enable</span><span class="p">()</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Enables reporting of execution contexts creation by means of ``executionContextCreated`` event.</span>
<span class="sd">    When the reporting gets enabled the event will be sent immediately for each existing execution</span>
<span class="sd">    context.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Runtime.enable&quot;</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span></div>



<div class="viewcode-block" id="evaluate">
<a class="viewcode-back" href="../../../nodriver/cdp/runtime.html#nodriver.cdp.runtime.evaluate">[docs]</a>
<span class="k">def</span> <span class="nf">evaluate</span><span class="p">(</span>
        <span class="n">expression</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span>
        <span class="n">object_group</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
        <span class="n">include_command_line_api</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">bool</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
        <span class="n">silent</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">bool</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
        <span class="n">context_id</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">ExecutionContextId</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
        <span class="n">return_by_value</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">bool</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
        <span class="n">generate_preview</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">bool</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
        <span class="n">user_gesture</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">bool</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
        <span class="n">await_promise</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">bool</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
        <span class="n">throw_on_side_effect</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">bool</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
        <span class="n">timeout</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">TimeDelta</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
        <span class="n">disable_breaks</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">bool</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
        <span class="n">repl_mode</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">bool</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
        <span class="n">allow_unsafe_eval_blocked_by_csp</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">bool</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
        <span class="n">unique_context_id</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
        <span class="n">serialization_options</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">SerializationOptions</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span>
    <span class="n">T_JSON_DICT</span><span class="p">,</span>
    <span class="n">T_JSON_DICT</span><span class="p">,</span>
    <span class="n">typing</span><span class="o">.</span><span class="n">Tuple</span><span class="p">[</span><span class="n">RemoteObject</span><span class="p">,</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">ExceptionDetails</span><span class="p">]],</span>
<span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Evaluates expression on global object.</span>

<span class="sd">    :param expression: Expression to evaluate.</span>
<span class="sd">    :param object_group: *(Optional)* Symbolic group name that can be used to release multiple objects.</span>
<span class="sd">    :param include_command_line_api: *(Optional)* Determines whether Command Line API should be available during the evaluation.</span>
<span class="sd">    :param silent: *(Optional)* In silent mode exceptions thrown during evaluation are not reported and do not pause execution. Overrides ```setPauseOnException```` state.</span>
<span class="sd">    :param context_id: *(Optional)* Specifies in which execution context to perform evaluation. If the parameter is omitted the evaluation will be performed in the context of the inspected page. This is mutually exclusive with ````uniqueContextId````, which offers an alternative way to identify the execution context that is more reliable in a multi-process environment.</span>
<span class="sd">    :param return_by_value: *(Optional)* Whether the result is expected to be a JSON object that should be sent by value.</span>
<span class="sd">    :param generate_preview: **(EXPERIMENTAL)** *(Optional)* Whether preview should be generated for the result.</span>
<span class="sd">    :param user_gesture: *(Optional)* Whether execution should be treated as initiated by user in the UI.</span>
<span class="sd">    :param await_promise: *(Optional)* Whether execution should ````await```` for resulting value and return once awaited promise is resolved.</span>
<span class="sd">    :param throw_on_side_effect: **(EXPERIMENTAL)** *(Optional)* Whether to throw an exception if side effect cannot be ruled out during evaluation. This implies ````disableBreaks```` below.</span>
<span class="sd">    :param timeout: **(EXPERIMENTAL)** *(Optional)* Terminate execution after timing out (number of milliseconds).</span>
<span class="sd">    :param disable_breaks: **(EXPERIMENTAL)** *(Optional)* Disable breakpoints during execution.</span>
<span class="sd">    :param repl_mode: **(EXPERIMENTAL)** *(Optional)* Setting this flag to true enables ````let```` re-declaration and top-level ````await````. Note that ````let```` variables can only be re-declared if they originate from ````replMode```` themselves.</span>
<span class="sd">    :param allow_unsafe_eval_blocked_by_csp: **(EXPERIMENTAL)** *(Optional)* The Content Security Policy (CSP) for the target might block &#39;unsafe-eval&#39; which includes eval(), Function(), setTimeout() and setInterval() when called with non-callable arguments. This flag bypasses CSP for this evaluation and allows unsafe-eval. Defaults to true.</span>
<span class="sd">    :param unique_context_id: **(EXPERIMENTAL)** *(Optional)* An alternative way to specify the execution context to evaluate in. Compared to contextId that may be reused across processes, this is guaranteed to be system-unique, so it can be used to prevent accidental evaluation of the expression in context different than intended (e.g. as a result of navigation across process boundaries). This is mutually exclusive with ````contextId````.</span>
<span class="sd">    :param serialization_options: **(EXPERIMENTAL)** *(Optional)* Specifies the result serialization. If provided, overrides ````generatePreview```` and ````returnByValue```.</span>
<span class="sd">    :returns: A tuple with the following items:</span>

<span class="sd">        0. **result** - Evaluation result.</span>
<span class="sd">        1. **exceptionDetails** - *(Optional)* Exception details.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;expression&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">expression</span>
    <span class="k">if</span> <span class="n">object_group</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;objectGroup&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">object_group</span>
    <span class="k">if</span> <span class="n">include_command_line_api</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;includeCommandLineAPI&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">include_command_line_api</span>
    <span class="k">if</span> <span class="n">silent</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;silent&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">silent</span>
    <span class="k">if</span> <span class="n">context_id</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;contextId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">context_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="k">if</span> <span class="n">return_by_value</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;returnByValue&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">return_by_value</span>
    <span class="k">if</span> <span class="n">generate_preview</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;generatePreview&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">generate_preview</span>
    <span class="k">if</span> <span class="n">user_gesture</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;userGesture&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">user_gesture</span>
    <span class="k">if</span> <span class="n">await_promise</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;awaitPromise&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">await_promise</span>
    <span class="k">if</span> <span class="n">throw_on_side_effect</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;throwOnSideEffect&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">throw_on_side_effect</span>
    <span class="k">if</span> <span class="n">timeout</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;timeout&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">timeout</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="k">if</span> <span class="n">disable_breaks</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;disableBreaks&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">disable_breaks</span>
    <span class="k">if</span> <span class="n">repl_mode</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;replMode&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">repl_mode</span>
    <span class="k">if</span> <span class="n">allow_unsafe_eval_blocked_by_csp</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;allowUnsafeEvalBlockedByCSP&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">allow_unsafe_eval_blocked_by_csp</span>
    <span class="k">if</span> <span class="n">unique_context_id</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;uniqueContextId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">unique_context_id</span>
    <span class="k">if</span> <span class="n">serialization_options</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;serializationOptions&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">serialization_options</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Runtime.evaluate&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span>
    <span class="k">return</span> <span class="p">(</span>
        <span class="n">RemoteObject</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;result&quot;</span><span class="p">]),</span>
        <span class="p">(</span>
            <span class="n">ExceptionDetails</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;exceptionDetails&quot;</span><span class="p">])</span>
            <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;exceptionDetails&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
            <span class="k">else</span> <span class="kc">None</span>
        <span class="p">),</span>
    <span class="p">)</span></div>



<div class="viewcode-block" id="get_isolate_id">
<a class="viewcode-back" href="../../../nodriver/cdp/runtime.html#nodriver.cdp.runtime.get_isolate_id">[docs]</a>
<span class="k">def</span> <span class="nf">get_isolate_id</span><span class="p">()</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="nb">str</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Returns the isolate id.</span>

<span class="sd">    **EXPERIMENTAL**</span>

<span class="sd">    :returns: The isolate id.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Runtime.getIsolateId&quot;</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span>
    <span class="k">return</span> <span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;id&quot;</span><span class="p">])</span></div>



<div class="viewcode-block" id="get_heap_usage">
<a class="viewcode-back" href="../../../nodriver/cdp/runtime.html#nodriver.cdp.runtime.get_heap_usage">[docs]</a>
<span class="k">def</span> <span class="nf">get_heap_usage</span><span class="p">()</span> <span class="o">-&gt;</span> <span class="p">(</span>
        <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">typing</span><span class="o">.</span><span class="n">Tuple</span><span class="p">[</span><span class="nb">float</span><span class="p">,</span> <span class="nb">float</span><span class="p">,</span> <span class="nb">float</span><span class="p">,</span> <span class="nb">float</span><span class="p">]]</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Returns the JavaScript heap usage.</span>
<span class="sd">    It is the total usage of the corresponding isolate not scoped to a particular Runtime.</span>

<span class="sd">    **EXPERIMENTAL**</span>

<span class="sd">    :returns: A tuple with the following items:</span>

<span class="sd">        0. **usedSize** - Used JavaScript heap size in bytes.</span>
<span class="sd">        1. **totalSize** - Allocated JavaScript heap size in bytes.</span>
<span class="sd">        2. **embedderHeapUsedSize** - Used size in bytes in the embedder&#39;s garbage-collected heap.</span>
<span class="sd">        3. **backingStorageSize** - Size in bytes of backing storage for array buffers and external strings.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Runtime.getHeapUsage&quot;</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span>
    <span class="k">return</span> <span class="p">(</span>
        <span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;usedSize&quot;</span><span class="p">]),</span>
        <span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;totalSize&quot;</span><span class="p">]),</span>
        <span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;embedderHeapUsedSize&quot;</span><span class="p">]),</span>
        <span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;backingStorageSize&quot;</span><span class="p">]),</span>
    <span class="p">)</span></div>



<div class="viewcode-block" id="get_properties">
<a class="viewcode-back" href="../../../nodriver/cdp/runtime.html#nodriver.cdp.runtime.get_properties">[docs]</a>
<span class="k">def</span> <span class="nf">get_properties</span><span class="p">(</span>
        <span class="n">object_id</span><span class="p">:</span> <span class="n">RemoteObjectId</span><span class="p">,</span>
        <span class="n">own_properties</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">bool</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
        <span class="n">accessor_properties_only</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">bool</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
        <span class="n">generate_preview</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">bool</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
        <span class="n">non_indexed_properties_only</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">bool</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span>
    <span class="n">T_JSON_DICT</span><span class="p">,</span>
    <span class="n">T_JSON_DICT</span><span class="p">,</span>
    <span class="n">typing</span><span class="o">.</span><span class="n">Tuple</span><span class="p">[</span>
        <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">PropertyDescriptor</span><span class="p">],</span>
        <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">InternalPropertyDescriptor</span><span class="p">]],</span>
        <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">PrivatePropertyDescriptor</span><span class="p">]],</span>
        <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">ExceptionDetails</span><span class="p">],</span>
    <span class="p">],</span>
<span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Returns properties of a given object. Object group of the result is inherited from the target</span>
<span class="sd">    object.</span>

<span class="sd">    :param object_id: Identifier of the object to return properties for.</span>
<span class="sd">    :param own_properties: *(Optional)* If true, returns properties belonging only to the element itself, not to its prototype chain.</span>
<span class="sd">    :param accessor_properties_only: **(EXPERIMENTAL)** *(Optional)* If true, returns accessor properties (with getter/setter) only; internal properties are not returned either.</span>
<span class="sd">    :param generate_preview: **(EXPERIMENTAL)** *(Optional)* Whether preview should be generated for the results.</span>
<span class="sd">    :param non_indexed_properties_only: **(EXPERIMENTAL)** *(Optional)* If true, returns non-indexed properties only.</span>
<span class="sd">    :returns: A tuple with the following items:</span>

<span class="sd">        0. **result** - Object properties.</span>
<span class="sd">        1. **internalProperties** - *(Optional)* Internal object properties (only of the element itself).</span>
<span class="sd">        2. **privateProperties** - *(Optional)* Object private properties.</span>
<span class="sd">        3. **exceptionDetails** - *(Optional)* Exception details.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;objectId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">object_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="k">if</span> <span class="n">own_properties</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;ownProperties&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">own_properties</span>
    <span class="k">if</span> <span class="n">accessor_properties_only</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;accessorPropertiesOnly&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">accessor_properties_only</span>
    <span class="k">if</span> <span class="n">generate_preview</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;generatePreview&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">generate_preview</span>
    <span class="k">if</span> <span class="n">non_indexed_properties_only</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;nonIndexedPropertiesOnly&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">non_indexed_properties_only</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Runtime.getProperties&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span>
    <span class="k">return</span> <span class="p">(</span>
        <span class="p">[</span><span class="n">PropertyDescriptor</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;result&quot;</span><span class="p">]],</span>
        <span class="p">(</span>
            <span class="p">[</span>
                <span class="n">InternalPropertyDescriptor</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span>
                <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;internalProperties&quot;</span><span class="p">]</span>
            <span class="p">]</span>
            <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;internalProperties&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
            <span class="k">else</span> <span class="kc">None</span>
        <span class="p">),</span>
        <span class="p">(</span>
            <span class="p">[</span><span class="n">PrivatePropertyDescriptor</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;privateProperties&quot;</span><span class="p">]]</span>
            <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;privateProperties&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
            <span class="k">else</span> <span class="kc">None</span>
        <span class="p">),</span>
        <span class="p">(</span>
            <span class="n">ExceptionDetails</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;exceptionDetails&quot;</span><span class="p">])</span>
            <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;exceptionDetails&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
            <span class="k">else</span> <span class="kc">None</span>
        <span class="p">),</span>
    <span class="p">)</span></div>



<div class="viewcode-block" id="global_lexical_scope_names">
<a class="viewcode-back" href="../../../nodriver/cdp/runtime.html#nodriver.cdp.runtime.global_lexical_scope_names">[docs]</a>
<span class="k">def</span> <span class="nf">global_lexical_scope_names</span><span class="p">(</span>
        <span class="n">execution_context_id</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">ExecutionContextId</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">]]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Returns all let, const and class variables from global scope.</span>

<span class="sd">    :param execution_context_id: *(Optional)* Specifies in which execution context to lookup global scope variables.</span>
<span class="sd">    :returns:</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="k">if</span> <span class="n">execution_context_id</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;executionContextId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">execution_context_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Runtime.globalLexicalScopeNames&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span>
    <span class="k">return</span> <span class="p">[</span><span class="nb">str</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;names&quot;</span><span class="p">]]</span></div>



<div class="viewcode-block" id="query_objects">
<a class="viewcode-back" href="../../../nodriver/cdp/runtime.html#nodriver.cdp.runtime.query_objects">[docs]</a>
<span class="k">def</span> <span class="nf">query_objects</span><span class="p">(</span>
        <span class="n">prototype_object_id</span><span class="p">:</span> <span class="n">RemoteObjectId</span><span class="p">,</span> <span class="n">object_group</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">RemoteObject</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    :param prototype_object_id: Identifier of the prototype to return objects for.</span>
<span class="sd">    :param object_group: *(Optional)* Symbolic group name that can be used to release the results.</span>
<span class="sd">    :returns: Array with objects.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;prototypeObjectId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">prototype_object_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="k">if</span> <span class="n">object_group</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;objectGroup&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">object_group</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Runtime.queryObjects&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span>
    <span class="k">return</span> <span class="n">RemoteObject</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;objects&quot;</span><span class="p">])</span></div>



<div class="viewcode-block" id="release_object">
<a class="viewcode-back" href="../../../nodriver/cdp/runtime.html#nodriver.cdp.runtime.release_object">[docs]</a>
<span class="k">def</span> <span class="nf">release_object</span><span class="p">(</span>
        <span class="n">object_id</span><span class="p">:</span> <span class="n">RemoteObjectId</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Releases remote object with given id.</span>

<span class="sd">    :param object_id: Identifier of the object to release.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;objectId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">object_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Runtime.releaseObject&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span></div>



<div class="viewcode-block" id="release_object_group">
<a class="viewcode-back" href="../../../nodriver/cdp/runtime.html#nodriver.cdp.runtime.release_object_group">[docs]</a>
<span class="k">def</span> <span class="nf">release_object_group</span><span class="p">(</span>
        <span class="n">object_group</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Releases all remote objects that belong to a given group.</span>

<span class="sd">    :param object_group: Symbolic object group name.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;objectGroup&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">object_group</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Runtime.releaseObjectGroup&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span></div>



<div class="viewcode-block" id="run_if_waiting_for_debugger">
<a class="viewcode-back" href="../../../nodriver/cdp/runtime.html#nodriver.cdp.runtime.run_if_waiting_for_debugger">[docs]</a>
<span class="k">def</span> <span class="nf">run_if_waiting_for_debugger</span><span class="p">()</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Tells inspected instance to run if it was waiting for debugger to attach.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Runtime.runIfWaitingForDebugger&quot;</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span></div>



<div class="viewcode-block" id="run_script">
<a class="viewcode-back" href="../../../nodriver/cdp/runtime.html#nodriver.cdp.runtime.run_script">[docs]</a>
<span class="k">def</span> <span class="nf">run_script</span><span class="p">(</span>
        <span class="n">script_id</span><span class="p">:</span> <span class="n">ScriptId</span><span class="p">,</span>
        <span class="n">execution_context_id</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">ExecutionContextId</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
        <span class="n">object_group</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
        <span class="n">silent</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">bool</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
        <span class="n">include_command_line_api</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">bool</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
        <span class="n">return_by_value</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">bool</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
        <span class="n">generate_preview</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">bool</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
        <span class="n">await_promise</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">bool</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span>
    <span class="n">T_JSON_DICT</span><span class="p">,</span>
    <span class="n">T_JSON_DICT</span><span class="p">,</span>
    <span class="n">typing</span><span class="o">.</span><span class="n">Tuple</span><span class="p">[</span><span class="n">RemoteObject</span><span class="p">,</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">ExceptionDetails</span><span class="p">]],</span>
<span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Runs script with given id in a given context.</span>

<span class="sd">    :param script_id: Id of the script to run.</span>
<span class="sd">    :param execution_context_id: *(Optional)* Specifies in which execution context to perform script run. If the parameter is omitted the evaluation will be performed in the context of the inspected page.</span>
<span class="sd">    :param object_group: *(Optional)* Symbolic group name that can be used to release multiple objects.</span>
<span class="sd">    :param silent: *(Optional)* In silent mode exceptions thrown during evaluation are not reported and do not pause execution. Overrides ```setPauseOnException```` state.</span>
<span class="sd">    :param include_command_line_api: *(Optional)* Determines whether Command Line API should be available during the evaluation.</span>
<span class="sd">    :param return_by_value: *(Optional)* Whether the result is expected to be a JSON object which should be sent by value.</span>
<span class="sd">    :param generate_preview: *(Optional)* Whether preview should be generated for the result.</span>
<span class="sd">    :param await_promise: *(Optional)* Whether execution should ````await``` for resulting value and return once awaited promise is resolved.</span>
<span class="sd">    :returns: A tuple with the following items:</span>

<span class="sd">        0. **result** - Run result.</span>
<span class="sd">        1. **exceptionDetails** - *(Optional)* Exception details.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;scriptId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">script_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="k">if</span> <span class="n">execution_context_id</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;executionContextId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">execution_context_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="k">if</span> <span class="n">object_group</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;objectGroup&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">object_group</span>
    <span class="k">if</span> <span class="n">silent</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;silent&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">silent</span>
    <span class="k">if</span> <span class="n">include_command_line_api</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;includeCommandLineAPI&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">include_command_line_api</span>
    <span class="k">if</span> <span class="n">return_by_value</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;returnByValue&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">return_by_value</span>
    <span class="k">if</span> <span class="n">generate_preview</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;generatePreview&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">generate_preview</span>
    <span class="k">if</span> <span class="n">await_promise</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;awaitPromise&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">await_promise</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Runtime.runScript&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span>
    <span class="k">return</span> <span class="p">(</span>
        <span class="n">RemoteObject</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;result&quot;</span><span class="p">]),</span>
        <span class="p">(</span>
            <span class="n">ExceptionDetails</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;exceptionDetails&quot;</span><span class="p">])</span>
            <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;exceptionDetails&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
            <span class="k">else</span> <span class="kc">None</span>
        <span class="p">),</span>
    <span class="p">)</span></div>



<div class="viewcode-block" id="set_async_call_stack_depth">
<a class="viewcode-back" href="../../../nodriver/cdp/runtime.html#nodriver.cdp.runtime.set_async_call_stack_depth">[docs]</a>
<span class="k">def</span> <span class="nf">set_async_call_stack_depth</span><span class="p">(</span>
        <span class="n">max_depth</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Enables or disables async call stacks tracking.</span>

<span class="sd">    :param max_depth: Maximum depth of async call stacks. Setting to ```0``` will effectively disable collecting async call stacks (default).</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;maxDepth&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">max_depth</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Runtime.setAsyncCallStackDepth&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span></div>



<div class="viewcode-block" id="set_custom_object_formatter_enabled">
<a class="viewcode-back" href="../../../nodriver/cdp/runtime.html#nodriver.cdp.runtime.set_custom_object_formatter_enabled">[docs]</a>
<span class="k">def</span> <span class="nf">set_custom_object_formatter_enabled</span><span class="p">(</span>
        <span class="n">enabled</span><span class="p">:</span> <span class="nb">bool</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>


<span class="sd">    **EXPERIMENTAL**</span>

<span class="sd">    :param enabled:</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;enabled&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">enabled</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Runtime.setCustomObjectFormatterEnabled&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span></div>



<div class="viewcode-block" id="set_max_call_stack_size_to_capture">
<a class="viewcode-back" href="../../../nodriver/cdp/runtime.html#nodriver.cdp.runtime.set_max_call_stack_size_to_capture">[docs]</a>
<span class="k">def</span> <span class="nf">set_max_call_stack_size_to_capture</span><span class="p">(</span>
        <span class="n">size</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>


<span class="sd">    **EXPERIMENTAL**</span>

<span class="sd">    :param size:</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;size&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">size</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Runtime.setMaxCallStackSizeToCapture&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span></div>



<div class="viewcode-block" id="terminate_execution">
<a class="viewcode-back" href="../../../nodriver/cdp/runtime.html#nodriver.cdp.runtime.terminate_execution">[docs]</a>
<span class="k">def</span> <span class="nf">terminate_execution</span><span class="p">()</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Terminate current or next JavaScript execution.</span>
<span class="sd">    Will cancel the termination when the outer-most script execution ends.</span>

<span class="sd">    **EXPERIMENTAL**</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Runtime.terminateExecution&quot;</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span></div>



<div class="viewcode-block" id="add_binding">
<a class="viewcode-back" href="../../../nodriver/cdp/runtime.html#nodriver.cdp.runtime.add_binding">[docs]</a>
<span class="k">def</span> <span class="nf">add_binding</span><span class="p">(</span>
        <span class="n">name</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span>
        <span class="n">execution_context_id</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">ExecutionContextId</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
        <span class="n">execution_context_name</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    If executionContextId is empty, adds binding with the given name on the</span>
<span class="sd">    global objects of all inspected contexts, including those created later,</span>
<span class="sd">    bindings survive reloads.</span>
<span class="sd">    Binding function takes exactly one argument, this argument should be string,</span>
<span class="sd">    in case of any other input, function throws an exception.</span>
<span class="sd">    Each binding function call produces Runtime.bindingCalled notification.</span>

<span class="sd">    :param name:</span>
<span class="sd">    :param execution_context_id: **(DEPRECATED)** **(EXPERIMENTAL)** *(Optional)* If specified, the binding would only be exposed to the specified execution context. If omitted and ```executionContextName```` is not set, the binding is exposed to all execution contexts of the target. This parameter is mutually exclusive with ````executionContextName````. Deprecated in favor of ````executionContextName```` due to an unclear use case and bugs in implementation (crbug.com/1169639). ````executionContextId```` will be removed in the future.</span>
<span class="sd">    :param execution_context_name: *(Optional)* If specified, the binding is exposed to the executionContext with matching name, even for contexts created after the binding is added. See also ````ExecutionContext.name```` and ````worldName```` parameter to ````Page.addScriptToEvaluateOnNewDocument````. This parameter is mutually exclusive with ````executionContextId```.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;name&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">name</span>
    <span class="k">if</span> <span class="n">execution_context_id</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;executionContextId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">execution_context_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="k">if</span> <span class="n">execution_context_name</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;executionContextName&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">execution_context_name</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Runtime.addBinding&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span></div>



<div class="viewcode-block" id="remove_binding">
<a class="viewcode-back" href="../../../nodriver/cdp/runtime.html#nodriver.cdp.runtime.remove_binding">[docs]</a>
<span class="k">def</span> <span class="nf">remove_binding</span><span class="p">(</span><span class="n">name</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    This method does not remove binding function from global object but</span>
<span class="sd">    unsubscribes current runtime agent from Runtime.bindingCalled notifications.</span>

<span class="sd">    :param name:</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;name&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">name</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Runtime.removeBinding&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span></div>



<div class="viewcode-block" id="get_exception_details">
<a class="viewcode-back" href="../../../nodriver/cdp/runtime.html#nodriver.cdp.runtime.get_exception_details">[docs]</a>
<span class="k">def</span> <span class="nf">get_exception_details</span><span class="p">(</span>
        <span class="n">error_object_id</span><span class="p">:</span> <span class="n">RemoteObjectId</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">ExceptionDetails</span><span class="p">]]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    This method tries to lookup and populate exception details for a</span>
<span class="sd">    JavaScript Error object.</span>
<span class="sd">    Note that the stackTrace portion of the resulting exceptionDetails will</span>
<span class="sd">    only be populated if the Runtime domain was enabled at the time when the</span>
<span class="sd">    Error was thrown.</span>

<span class="sd">    **EXPERIMENTAL**</span>

<span class="sd">    :param error_object_id: The error object for which to resolve the exception details.</span>
<span class="sd">    :returns:</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;errorObjectId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">error_object_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Runtime.getExceptionDetails&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span>
    <span class="k">return</span> <span class="p">(</span>
        <span class="n">ExceptionDetails</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;exceptionDetails&quot;</span><span class="p">])</span>
        <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;exceptionDetails&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
        <span class="k">else</span> <span class="kc">None</span>
    <span class="p">)</span></div>



<div class="viewcode-block" id="BindingCalled">
<a class="viewcode-back" href="../../../nodriver/cdp/runtime.html#nodriver.cdp.runtime.BindingCalled">[docs]</a>
<span class="nd">@event_class</span><span class="p">(</span><span class="s2">&quot;Runtime.bindingCalled&quot;</span><span class="p">)</span>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">BindingCalled</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    **EXPERIMENTAL**</span>

<span class="sd">    Notification is issued every time when binding is called.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">name</span><span class="p">:</span> <span class="nb">str</span>
    <span class="n">payload</span><span class="p">:</span> <span class="nb">str</span>
    <span class="c1">#: Identifier of the context where the call was made.</span>
    <span class="n">execution_context_id</span><span class="p">:</span> <span class="n">ExecutionContextId</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">BindingCalled</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">name</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;name&quot;</span><span class="p">]),</span>
            <span class="n">payload</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;payload&quot;</span><span class="p">]),</span>
            <span class="n">execution_context_id</span><span class="o">=</span><span class="n">ExecutionContextId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span>
                <span class="n">json</span><span class="p">[</span><span class="s2">&quot;executionContextId&quot;</span><span class="p">]</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="ConsoleAPICalled">
<a class="viewcode-back" href="../../../nodriver/cdp/runtime.html#nodriver.cdp.runtime.ConsoleAPICalled">[docs]</a>
<span class="nd">@event_class</span><span class="p">(</span><span class="s2">&quot;Runtime.consoleAPICalled&quot;</span><span class="p">)</span>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">ConsoleAPICalled</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Issued when console API was called.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: Type of the call.</span>
    <span class="n">type_</span><span class="p">:</span> <span class="nb">str</span>
    <span class="c1">#: Call arguments.</span>
    <span class="n">args</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">RemoteObject</span><span class="p">]</span>
    <span class="c1">#: Identifier of the context where the call was made.</span>
    <span class="n">execution_context_id</span><span class="p">:</span> <span class="n">ExecutionContextId</span>
    <span class="c1">#: Call timestamp.</span>
    <span class="n">timestamp</span><span class="p">:</span> <span class="n">Timestamp</span>
    <span class="c1">#: Stack trace captured when the call was made. The async stack chain is automatically reported for</span>
    <span class="c1">#: the following call types: ``assert``, ``error``, ``trace``, ``warning``. For other types the async call</span>
    <span class="c1">#: chain can be retrieved using ``Debugger.getStackTrace`` and ``stackTrace.parentId`` field.</span>
    <span class="n">stack_trace</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">StackTrace</span><span class="p">]</span>
    <span class="c1">#: Console context descriptor for calls on non-default console context (not console.*):</span>
    <span class="c1">#: &#39;anonymous#unique-logger-id&#39; for call on unnamed context, &#39;name#unique-logger-id&#39; for call</span>
    <span class="c1">#: on named context.</span>
    <span class="n">context</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">ConsoleAPICalled</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">type_</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;type&quot;</span><span class="p">]),</span>
            <span class="n">args</span><span class="o">=</span><span class="p">[</span><span class="n">RemoteObject</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;args&quot;</span><span class="p">]],</span>
            <span class="n">execution_context_id</span><span class="o">=</span><span class="n">ExecutionContextId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span>
                <span class="n">json</span><span class="p">[</span><span class="s2">&quot;executionContextId&quot;</span><span class="p">]</span>
            <span class="p">),</span>
            <span class="n">timestamp</span><span class="o">=</span><span class="n">Timestamp</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;timestamp&quot;</span><span class="p">]),</span>
            <span class="n">stack_trace</span><span class="o">=</span><span class="p">(</span>
                <span class="n">StackTrace</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;stackTrace&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;stackTrace&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">context</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;context&quot;</span><span class="p">])</span> <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;context&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span> <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="ExceptionRevoked">
<a class="viewcode-back" href="../../../nodriver/cdp/runtime.html#nodriver.cdp.runtime.ExceptionRevoked">[docs]</a>
<span class="nd">@event_class</span><span class="p">(</span><span class="s2">&quot;Runtime.exceptionRevoked&quot;</span><span class="p">)</span>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">ExceptionRevoked</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Issued when unhandled exception was revoked.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: Reason describing why exception was revoked.</span>
    <span class="n">reason</span><span class="p">:</span> <span class="nb">str</span>
    <span class="c1">#: The id of revoked exception, as reported in ``exceptionThrown``.</span>
    <span class="n">exception_id</span><span class="p">:</span> <span class="nb">int</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">ExceptionRevoked</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">reason</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;reason&quot;</span><span class="p">]),</span> <span class="n">exception_id</span><span class="o">=</span><span class="nb">int</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;exceptionId&quot;</span><span class="p">]))</span></div>



<div class="viewcode-block" id="ExceptionThrown">
<a class="viewcode-back" href="../../../nodriver/cdp/runtime.html#nodriver.cdp.runtime.ExceptionThrown">[docs]</a>
<span class="nd">@event_class</span><span class="p">(</span><span class="s2">&quot;Runtime.exceptionThrown&quot;</span><span class="p">)</span>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">ExceptionThrown</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Issued when exception was thrown and unhandled.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: Timestamp of the exception.</span>
    <span class="n">timestamp</span><span class="p">:</span> <span class="n">Timestamp</span>
    <span class="n">exception_details</span><span class="p">:</span> <span class="n">ExceptionDetails</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">ExceptionThrown</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">timestamp</span><span class="o">=</span><span class="n">Timestamp</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;timestamp&quot;</span><span class="p">]),</span>
            <span class="n">exception_details</span><span class="o">=</span><span class="n">ExceptionDetails</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;exceptionDetails&quot;</span><span class="p">]),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="ExecutionContextCreated">
<a class="viewcode-back" href="../../../nodriver/cdp/runtime.html#nodriver.cdp.runtime.ExecutionContextCreated">[docs]</a>
<span class="nd">@event_class</span><span class="p">(</span><span class="s2">&quot;Runtime.executionContextCreated&quot;</span><span class="p">)</span>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">ExecutionContextCreated</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Issued when new execution context is created.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: A newly created execution context.</span>
    <span class="n">context</span><span class="p">:</span> <span class="n">ExecutionContextDescription</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">ExecutionContextCreated</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">context</span><span class="o">=</span><span class="n">ExecutionContextDescription</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;context&quot;</span><span class="p">]))</span></div>



<div class="viewcode-block" id="ExecutionContextDestroyed">
<a class="viewcode-back" href="../../../nodriver/cdp/runtime.html#nodriver.cdp.runtime.ExecutionContextDestroyed">[docs]</a>
<span class="nd">@event_class</span><span class="p">(</span><span class="s2">&quot;Runtime.executionContextDestroyed&quot;</span><span class="p">)</span>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">ExecutionContextDestroyed</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Issued when execution context is destroyed.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: Id of the destroyed context</span>
    <span class="n">execution_context_id</span><span class="p">:</span> <span class="n">ExecutionContextId</span>
    <span class="c1">#: Unique Id of the destroyed context</span>
    <span class="n">execution_context_unique_id</span><span class="p">:</span> <span class="nb">str</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">ExecutionContextDestroyed</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">execution_context_id</span><span class="o">=</span><span class="n">ExecutionContextId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span>
                <span class="n">json</span><span class="p">[</span><span class="s2">&quot;executionContextId&quot;</span><span class="p">]</span>
            <span class="p">),</span>
            <span class="n">execution_context_unique_id</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;executionContextUniqueId&quot;</span><span class="p">]),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="ExecutionContextsCleared">
<a class="viewcode-back" href="../../../nodriver/cdp/runtime.html#nodriver.cdp.runtime.ExecutionContextsCleared">[docs]</a>
<span class="nd">@event_class</span><span class="p">(</span><span class="s2">&quot;Runtime.executionContextsCleared&quot;</span><span class="p">)</span>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">ExecutionContextsCleared</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Issued when all executionContexts were cleared in browser</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">ExecutionContextsCleared</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">()</span></div>



<div class="viewcode-block" id="InspectRequested">
<a class="viewcode-back" href="../../../nodriver/cdp/runtime.html#nodriver.cdp.runtime.InspectRequested">[docs]</a>
<span class="nd">@event_class</span><span class="p">(</span><span class="s2">&quot;Runtime.inspectRequested&quot;</span><span class="p">)</span>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">InspectRequested</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Issued when object should be inspected (for example, as a result of inspect() command line API</span>
<span class="sd">    call).</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">object_</span><span class="p">:</span> <span class="n">RemoteObject</span>
    <span class="n">hints</span><span class="p">:</span> <span class="nb">dict</span>
    <span class="c1">#: Identifier of the context where the call was made.</span>
    <span class="n">execution_context_id</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">ExecutionContextId</span><span class="p">]</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">InspectRequested</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">object_</span><span class="o">=</span><span class="n">RemoteObject</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;object&quot;</span><span class="p">]),</span>
            <span class="n">hints</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;hints&quot;</span><span class="p">]),</span>
            <span class="n">execution_context_id</span><span class="o">=</span><span class="p">(</span>
                <span class="n">ExecutionContextId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;executionContextId&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;executionContextId&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>

</pre></div>
        </article>
      </div>
      <footer>
        
        <div class="related-pages">
          
          
        </div>
        <div class="bottom-of-page">
          <div class="left-details">
            <div class="copyright">
                Copyright &#169; 2023, Author
            </div>
            Made with <a href="https://www.sphinx-doc.org/">Sphinx</a> and <a class="muted-link" href="https://pradyunsg.me">@pradyunsg</a>'s
            
            <a href="https://github.com/pradyunsg/furo">Furo</a>
            
          </div>
          <div class="right-details">
            
          </div>
        </div>
        
      </footer>
    </div>
    <aside class="toc-drawer no-toc">
      
      
      
    </aside>
  </div>
</div><script src="../../../_static/documentation_options.js?v=5929fcd5"></script>
    <script src="../../../_static/doctools.js?v=888ff710"></script>
    <script src="../../../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../../../_static/scripts/furo.js?v=32e29ea5"></script>
    </body>
</html>