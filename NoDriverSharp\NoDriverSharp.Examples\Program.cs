using NoDriverSharp.Examples;

Console.WriteLine("NoDriverSharp Examples");
Console.WriteLine("======================");
Console.WriteLine();
Console.WriteLine("Choose an example to run:");
Console.WriteLine("1. Simple Example - Basic browser automation");
Console.WriteLine("2. Advanced Example - Custom configuration and features");
Console.WriteLine("3. Performance Example - Concurrent operations testing");
Console.WriteLine("4. Bot Detection Test - Test against fingerprint.com");
Console.WriteLine("5. Advanced Bot Detection - Test multiple detection sites");
Console.WriteLine("6. Debug Navigation - Test basic navigation with detailed logging");
Console.WriteLine("7. Exit");
Console.WriteLine();

while (true)
{
    Console.Write("Enter your choice (1-7): ");
    var input = Console.ReadLine();

    try
    {
        switch (input)
        {
            case "1":
                Console.WriteLine();
                await SimpleExample.RunAsync();
                break;

            case "2":
                Console.WriteLine();
                await AdvancedExample.RunAsync();
                break;

            case "3":
                Console.WriteLine();
                await PerformanceExample.RunAsync();
                break;

            case "4":
                Console.WriteLine();
                await BotDetectionExample.RunAsync();
                break;

            case "5":
                Console.WriteLine();
                await AdvancedBotDetectionExample.RunAsync();
                break;

            case "6":
                Console.WriteLine();
                await DebugNavigationExample.RunAsync();
                break;

            case "7":
                Console.WriteLine("Goodbye!");
                return;

            default:
                Console.WriteLine("Invalid choice. Please enter 1, 2, 3, 4, 5, 6, or 7.");
                continue;
        }
    }
    catch (Exception ex)
    {
        Console.WriteLine($"Example failed with error: {ex.Message}");
        if (ex.InnerException != null)
        {
            Console.WriteLine($"Inner exception: {ex.InnerException.Message}");
        }
    }

    Console.WriteLine();
    Console.WriteLine("Press any key to return to the menu...");
    Console.ReadKey();
    Console.WriteLine();
}
