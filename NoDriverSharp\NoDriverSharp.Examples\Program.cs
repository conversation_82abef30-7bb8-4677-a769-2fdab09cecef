﻿using Microsoft.Extensions.Logging;
using NoDriverSharp.Core;

// Configure logging
using var loggerFactory = LoggerFactory.Create(builder =>
    builder.AddConsole().SetMinimumLevel(LogLevel.Information));

var logger = loggerFactory.CreateLogger<Browser>();

Console.WriteLine("NoDriverSharp Example - Basic Browser Automation");
Console.WriteLine("================================================");

try
{
    // Example 1: Basic navigation
    Console.WriteLine("\n1. Starting browser and navigating to a website...");

    await using var browser = await NoDriver.StartAsync(
        headless: false,
        logger: logger);

    var tab = await browser.GetAsync("https://www.example.com");
    Console.WriteLine($"Navigated to: {tab.Url}");
    Console.WriteLine($"Page title: {tab.Title}");

    // Wait a moment to see the page
    await Task.Delay(2000);

    // Example 2: Take a screenshot
    Console.WriteLine("\n2. Taking a screenshot...");
    await tab.SaveScreenshotAsync("example_screenshot.png");
    Console.WriteLine("Screenshot saved as 'example_screenshot.png'");

    // Example 3: Navigate to another page
    Console.WriteLine("\n3. Navigating to another page...");
    await tab.NavigateAsync("https://httpbin.org/html");
    Console.WriteLine($"Navigated to: {tab.Url}");

    // Wait a moment
    await Task.Delay(2000);

    // Example 4: Reload the page
    Console.WriteLine("\n4. Reloading the page...");
    await tab.ReloadAsync();
    Console.WriteLine("Page reloaded successfully");

    // Example 5: Open a new tab
    Console.WriteLine("\n5. Opening a new tab...");
    var newTab = await browser.GetAsync("https://www.google.com", newTab: true);
    Console.WriteLine($"New tab opened: {newTab.Url}");
    Console.WriteLine($"Total tabs: {browser.Tabs.Count}");

    // Wait before closing
    Console.WriteLine("\nPress any key to close the browser...");
    Console.ReadKey();

    Console.WriteLine("\nClosing browser...");
}
catch (Exception ex)
{
    Console.WriteLine($"Error: {ex.Message}");
    if (ex.InnerException != null)
    {
        Console.WriteLine($"Inner exception: {ex.InnerException.Message}");
    }
}

Console.WriteLine("Example completed.");
