<!doctype html>
<html class="no-js" lang="en" data-content_root="../../">
  <head><meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width,initial-scale=1"/>
    <meta name="color-scheme" content="light dark"><meta name="viewport" content="width=device-width, initial-scale=1" />
<link rel="index" title="Index" href="../../genindex.html" /><link rel="search" title="Search" href="../../search.html" /><link rel="next" title="Target" href="target.html" /><link rel="prev" title="Storage" href="storage.html" />

    <!-- Generated with Sphinx 7.2.6 and Furo 2023.09.10 -->
        <title>SystemInfo - nodriver documentation</title>
      <link rel="stylesheet" type="text/css" href="../../_static/pygments.css?v=fa44fd50" />
    <link rel="stylesheet" type="text/css" href="../../_static/styles/furo.css?v=135e06be" />
    <link rel="stylesheet" type="text/css" href="../../_static/styles/furo-extensions.css?v=36a5483c" />
    <link rel="stylesheet" type="text/css" href="../../_static/./custom.css?v=3934642d" />
    
    


<style>
  body {
    --color-code-background: #eeffcc;
  --color-code-foreground: black;
  
  }
  @media not print {
    body[data-theme="dark"] {
      --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8eefba;
  --color-background-primary: #111112;
  --color-problematic: #e7aeae;
  
    }
    @media (prefers-color-scheme: dark) {
      body:not([data-theme="light"]) {
        --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8eefba;
  --color-background-primary: #111112;
  --color-problematic: #e7aeae;
  
      }
    }
  }
</style></head>
  <body>
    
    <script>
      document.body.dataset.theme = localStorage.getItem("theme") || "auto";
    </script>
    

<svg xmlns="http://www.w3.org/2000/svg" style="display: none;">
  <symbol id="svg-toc" viewBox="0 0 24 24">
    <title>Contents</title>
    <svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 1024 1024">
      <path d="M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM115.4 518.9L271.7 642c5.8 4.6 14.4.5 14.4-6.9V388.9c0-7.4-8.5-11.5-14.4-6.9L115.4 505.1a8.74 8.74 0 0 0 0 13.8z"/>
    </svg>
  </symbol>
  <symbol id="svg-menu" viewBox="0 0 24 24">
    <title>Menu</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-menu">
      <line x1="3" y1="12" x2="21" y2="12"></line>
      <line x1="3" y1="6" x2="21" y2="6"></line>
      <line x1="3" y1="18" x2="21" y2="18"></line>
    </svg>
  </symbol>
  <symbol id="svg-arrow-right" viewBox="0 0 24 24">
    <title>Expand</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-chevron-right">
      <polyline points="9 18 15 12 9 6"></polyline>
    </svg>
  </symbol>
  <symbol id="svg-sun" viewBox="0 0 24 24">
    <title>Light mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="feather-sun">
      <circle cx="12" cy="12" r="5"></circle>
      <line x1="12" y1="1" x2="12" y2="3"></line>
      <line x1="12" y1="21" x2="12" y2="23"></line>
      <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
      <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
      <line x1="1" y1="12" x2="3" y2="12"></line>
      <line x1="21" y1="12" x2="23" y2="12"></line>
      <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
      <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
    </svg>
  </symbol>
  <symbol id="svg-moon" viewBox="0 0 24 24">
    <title>Dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-moon">
      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
      <path d="M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z" />
    </svg>
  </symbol>
  <symbol id="svg-sun-half" viewBox="0 0 24 24">
    <title>Auto light/dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-shadow">
      <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
      <circle cx="12" cy="12" r="9" />
      <path d="M13 12h5" />
      <path d="M13 15h4" />
      <path d="M13 18h1" />
      <path d="M13 9h4" />
      <path d="M13 6h1" />
    </svg>
  </symbol>
</svg>

<input type="checkbox" class="sidebar-toggle" name="__navigation" id="__navigation">
<input type="checkbox" class="sidebar-toggle" name="__toc" id="__toc">
<label class="overlay sidebar-overlay" for="__navigation">
  <div class="visually-hidden">Hide navigation sidebar</div>
</label>
<label class="overlay toc-overlay" for="__toc">
  <div class="visually-hidden">Hide table of contents sidebar</div>
</label>



<div class="page">
  <header class="mobile-header">
    <div class="header-left">
      <label class="nav-overlay-icon" for="__navigation">
        <div class="visually-hidden">Toggle site navigation sidebar</div>
        <i class="icon"><svg><use href="#svg-menu"></use></svg></i>
      </label>
    </div>
    <div class="header-center">
      <a href="../../index.html"><div class="brand">nodriver  documentation</div></a>
    </div>
    <div class="header-right">
      <div class="theme-toggle-container theme-toggle-header">
        <button class="theme-toggle">
          <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
          <svg class="theme-icon-when-auto"><use href="#svg-sun-half"></use></svg>
          <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
          <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
        </button>
      </div>
      <label class="toc-overlay-icon toc-header-icon" for="__toc">
        <div class="visually-hidden">Toggle table of contents sidebar</div>
        <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
      </label>
    </div>
  </header>
  <aside class="sidebar-drawer">
    <div class="sidebar-container">
      
      <div class="sidebar-sticky"><div class="sidebar-scroll"><a class="sidebar-brand" href="../../index.html">
  
  
  <span class="sidebar-brand-text">nodriver  documentation</span>
  
</a><form class="sidebar-search-container" method="get" action="../../search.html" role="search">
  <input class="sidebar-search" placeholder="Search" name="q" aria-label="Search">
  <input type="hidden" name="check_keywords" value="yes">
  <input type="hidden" name="area" value="default">
</form>
<div id="searchbox"></div><div class="sidebar-tree">
  <ul>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html">Quickstart guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#usage-example">usage example</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#more-complete-example">More complete example</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#custom-starting-options">Custom starting options</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#alternative-custom-options">Alternative custom options</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#proxies-socks5-authenticated-too">Proxies (socks5, authenticated too)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#concrete-example">Concrete example</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../classes/browser.html">Browser class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../classes/tab.html">Tab class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../classes/element.html">Element class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../classes/others_and_helpers.html">Other classes and Helper classes</a></li>
</ul>
<ul class="current">
<li class="toctree-l1 current has-children"><a class="reference internal" href="../cdp.html">CDP object</a><input checked="" class="toctree-checkbox" id="toctree-checkbox-1" name="toctree-checkbox-1" role="switch" type="checkbox"/><label for="toctree-checkbox-1"><div class="visually-hidden">Toggle navigation of CDP object</div><i class="icon"><svg><use href="#svg-arrow-right"></use></svg></i></label><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="accessibility.html">Accessibility</a></li>
<li class="toctree-l2"><a class="reference internal" href="animation.html">Animation</a></li>
<li class="toctree-l2"><a class="reference internal" href="audits.html">Audits</a></li>
<li class="toctree-l2"><a class="reference internal" href="autofill.html">Autofill</a></li>
<li class="toctree-l2"><a class="reference internal" href="background_service.html">BackgroundService</a></li>
<li class="toctree-l2"><a class="reference internal" href="bluetooth_emulation.html">BluetoothEmulation</a></li>
<li class="toctree-l2"><a class="reference internal" href="browser.html">Browser</a></li>
<li class="toctree-l2"><a class="reference internal" href="cache_storage.html">CacheStorage</a></li>
<li class="toctree-l2"><a class="reference internal" href="cast.html">Cast</a></li>
<li class="toctree-l2"><a class="reference internal" href="console.html">Console</a></li>
<li class="toctree-l2"><a class="reference internal" href="css.html">CSS</a></li>
<li class="toctree-l2"><a class="reference internal" href="debugger.html">Debugger</a></li>
<li class="toctree-l2"><a class="reference internal" href="device_access.html">DeviceAccess</a></li>
<li class="toctree-l2"><a class="reference internal" href="device_orientation.html">DeviceOrientation</a></li>
<li class="toctree-l2"><a class="reference internal" href="dom.html">DOM</a></li>
<li class="toctree-l2"><a class="reference internal" href="dom_debugger.html">DOMDebugger</a></li>
<li class="toctree-l2"><a class="reference internal" href="dom_snapshot.html">DOMSnapshot</a></li>
<li class="toctree-l2"><a class="reference internal" href="dom_storage.html">DOMStorage</a></li>
<li class="toctree-l2"><a class="reference internal" href="emulation.html">Emulation</a></li>
<li class="toctree-l2"><a class="reference internal" href="event_breakpoints.html">EventBreakpoints</a></li>
<li class="toctree-l2"><a class="reference internal" href="extensions.html">Extensions</a></li>
<li class="toctree-l2"><a class="reference internal" href="fed_cm.html">FedCm</a></li>
<li class="toctree-l2"><a class="reference internal" href="fetch.html">Fetch</a></li>
<li class="toctree-l2"><a class="reference internal" href="file_system.html">FileSystem</a></li>
<li class="toctree-l2"><a class="reference internal" href="headless_experimental.html">HeadlessExperimental</a></li>
<li class="toctree-l2"><a class="reference internal" href="heap_profiler.html">HeapProfiler</a></li>
<li class="toctree-l2"><a class="reference internal" href="indexed_db.html">IndexedDB</a></li>
<li class="toctree-l2"><a class="reference internal" href="input_.html">Input</a></li>
<li class="toctree-l2"><a class="reference internal" href="inspector.html">Inspector</a></li>
<li class="toctree-l2"><a class="reference internal" href="io.html">IO</a></li>
<li class="toctree-l2"><a class="reference internal" href="layer_tree.html">LayerTree</a></li>
<li class="toctree-l2"><a class="reference internal" href="log.html">Log</a></li>
<li class="toctree-l2"><a class="reference internal" href="media.html">Media</a></li>
<li class="toctree-l2"><a class="reference internal" href="memory.html">Memory</a></li>
<li class="toctree-l2"><a class="reference internal" href="network.html">Network</a></li>
<li class="toctree-l2"><a class="reference internal" href="overlay.html">Overlay</a></li>
<li class="toctree-l2"><a class="reference internal" href="page.html">Page</a></li>
<li class="toctree-l2"><a class="reference internal" href="performance.html">Performance</a></li>
<li class="toctree-l2"><a class="reference internal" href="performance_timeline.html">PerformanceTimeline</a></li>
<li class="toctree-l2"><a class="reference internal" href="preload.html">Preload</a></li>
<li class="toctree-l2"><a class="reference internal" href="profiler.html">Profiler</a></li>
<li class="toctree-l2"><a class="reference internal" href="pwa.html">PWA</a></li>
<li class="toctree-l2"><a class="reference internal" href="runtime.html">Runtime</a></li>
<li class="toctree-l2"><a class="reference internal" href="schema.html">Schema</a></li>
<li class="toctree-l2"><a class="reference internal" href="security.html">Security</a></li>
<li class="toctree-l2"><a class="reference internal" href="service_worker.html">ServiceWorker</a></li>
<li class="toctree-l2"><a class="reference internal" href="storage.html">Storage</a></li>
<li class="toctree-l2 current current-page"><a class="current reference internal" href="#">SystemInfo</a></li>
<li class="toctree-l2"><a class="reference internal" href="target.html">Target</a></li>
<li class="toctree-l2"><a class="reference internal" href="tethering.html">Tethering</a></li>
<li class="toctree-l2"><a class="reference internal" href="tracing.html">Tracing</a></li>
<li class="toctree-l2"><a class="reference internal" href="web_audio.html">WebAudio</a></li>
<li class="toctree-l2"><a class="reference internal" href="web_authn.html">WebAuthn</a></li>
</ul>
</li>
</ul>

</div>
</div>
      </div>
      
    </div>
  </aside>
  <div class="main">
    <div class="content">
      <div class="article-container">
        <a href="#" class="back-to-top muted-link">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
            <path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8v12z"></path>
          </svg>
          <span>Back to top</span>
        </a>
        <div class="content-icon-container">
          
<div class="theme-toggle-container theme-toggle-content">
            <button class="theme-toggle">
              <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
              <svg class="theme-icon-when-auto"><use href="#svg-sun-half"></use></svg>
              <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
              <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
            </button>
          </div>
          <label class="toc-overlay-icon toc-content-icon" for="__toc">
            <div class="visually-hidden">Toggle table of contents sidebar</div>
            <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
          </label>
        </div>
        <article role="main">
          <section id="systeminfo">
<h1>SystemInfo<a class="headerlink" href="#systeminfo" title="Link to this heading">#</a></h1>
<p>The SystemInfo domain defines methods and events for querying low-level system information.</p>
<p><em>This CDP domain is experimental.</em></p>
<ul class="simple" id="module-nodriver.cdp.system_info">
<li><p><a class="reference internal" href="#types">Types</a></p></li>
<li><p><a class="reference internal" href="#commands">Commands</a></p></li>
<li><p><a class="reference internal" href="#events">Events</a></p></li>
</ul>
<section id="types">
<h2>Types<a class="headerlink" href="#types" title="Link to this heading">#</a></h2>
<p>Generally, you do not need to instantiate CDP types
yourself. Instead, the API creates objects for you as return
values from commands, and then you can use those objects as
arguments to other commands.</p>
<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.system_info.GPUDevice">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">GPUDevice</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">vendor_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">device_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">vendor_string</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">device_string</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">driver_vendor</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">driver_version</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">sub_sys_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">revision</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/system_info.html#GPUDevice"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.system_info.GPUDevice" title="Link to this definition">#</a></dt>
<dd><p>Describes a single graphics processor (GPU).</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.system_info.GPUDevice.vendor_id">
<span class="sig-name descname"><span class="pre">vendor_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a></em><a class="headerlink" href="#nodriver.cdp.system_info.GPUDevice.vendor_id" title="Link to this definition">#</a></dt>
<dd><p>PCI ID of the GPU vendor, if available; 0 otherwise.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.system_info.GPUDevice.device_id">
<span class="sig-name descname"><span class="pre">device_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a></em><a class="headerlink" href="#nodriver.cdp.system_info.GPUDevice.device_id" title="Link to this definition">#</a></dt>
<dd><p>PCI ID of the GPU device, if available; 0 otherwise.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.system_info.GPUDevice.vendor_string">
<span class="sig-name descname"><span class="pre">vendor_string</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.system_info.GPUDevice.vendor_string" title="Link to this definition">#</a></dt>
<dd><p>String description of the GPU vendor, if the PCI ID is not available.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.system_info.GPUDevice.device_string">
<span class="sig-name descname"><span class="pre">device_string</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.system_info.GPUDevice.device_string" title="Link to this definition">#</a></dt>
<dd><p>String description of the GPU device, if the PCI ID is not available.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.system_info.GPUDevice.driver_vendor">
<span class="sig-name descname"><span class="pre">driver_vendor</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.system_info.GPUDevice.driver_vendor" title="Link to this definition">#</a></dt>
<dd><p>String description of the GPU driver vendor.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.system_info.GPUDevice.driver_version">
<span class="sig-name descname"><span class="pre">driver_version</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.system_info.GPUDevice.driver_version" title="Link to this definition">#</a></dt>
<dd><p>String description of the GPU driver version.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.system_info.GPUDevice.sub_sys_id">
<span class="sig-name descname"><span class="pre">sub_sys_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.system_info.GPUDevice.sub_sys_id" title="Link to this definition">#</a></dt>
<dd><p>Sub sys ID of the GPU, only available on Windows.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.system_info.GPUDevice.revision">
<span class="sig-name descname"><span class="pre">revision</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.system_info.GPUDevice.revision" title="Link to this definition">#</a></dt>
<dd><p>Revision of the GPU, only available on Windows.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.system_info.Size">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">Size</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">width</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">height</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/system_info.html#Size"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.system_info.Size" title="Link to this definition">#</a></dt>
<dd><p>Describes the width and height dimensions of an entity.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.system_info.Size.width">
<span class="sig-name descname"><span class="pre">width</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></em><a class="headerlink" href="#nodriver.cdp.system_info.Size.width" title="Link to this definition">#</a></dt>
<dd><p>Width in pixels.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.system_info.Size.height">
<span class="sig-name descname"><span class="pre">height</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></em><a class="headerlink" href="#nodriver.cdp.system_info.Size.height" title="Link to this definition">#</a></dt>
<dd><p>Height in pixels.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.system_info.VideoDecodeAcceleratorCapability">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">VideoDecodeAcceleratorCapability</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">profile</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">max_resolution</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">min_resolution</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/system_info.html#VideoDecodeAcceleratorCapability"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.system_info.VideoDecodeAcceleratorCapability" title="Link to this definition">#</a></dt>
<dd><p>Describes a supported video decoding profile with its associated minimum and
maximum resolutions.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.system_info.VideoDecodeAcceleratorCapability.profile">
<span class="sig-name descname"><span class="pre">profile</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.system_info.VideoDecodeAcceleratorCapability.profile" title="Link to this definition">#</a></dt>
<dd><p>Video codec profile that is supported, e.g. VP9 Profile 2.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.system_info.VideoDecodeAcceleratorCapability.max_resolution">
<span class="sig-name descname"><span class="pre">max_resolution</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.system_info.Size" title="nodriver.cdp.system_info.Size"><code class="xref py py-class docutils literal notranslate"><span class="pre">Size</span></code></a></em><a class="headerlink" href="#nodriver.cdp.system_info.VideoDecodeAcceleratorCapability.max_resolution" title="Link to this definition">#</a></dt>
<dd><p>Maximum video dimensions in pixels supported for this <code class="docutils literal notranslate"><span class="pre">profile</span></code>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.system_info.VideoDecodeAcceleratorCapability.min_resolution">
<span class="sig-name descname"><span class="pre">min_resolution</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.system_info.Size" title="nodriver.cdp.system_info.Size"><code class="xref py py-class docutils literal notranslate"><span class="pre">Size</span></code></a></em><a class="headerlink" href="#nodriver.cdp.system_info.VideoDecodeAcceleratorCapability.min_resolution" title="Link to this definition">#</a></dt>
<dd><p>Minimum video dimensions in pixels supported for this <code class="docutils literal notranslate"><span class="pre">profile</span></code>.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.system_info.VideoEncodeAcceleratorCapability">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">VideoEncodeAcceleratorCapability</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">profile</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">max_resolution</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">max_framerate_numerator</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">max_framerate_denominator</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/system_info.html#VideoEncodeAcceleratorCapability"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.system_info.VideoEncodeAcceleratorCapability" title="Link to this definition">#</a></dt>
<dd><p>Describes a supported video encoding profile with its associated maximum
resolution and maximum framerate.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.system_info.VideoEncodeAcceleratorCapability.profile">
<span class="sig-name descname"><span class="pre">profile</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.system_info.VideoEncodeAcceleratorCapability.profile" title="Link to this definition">#</a></dt>
<dd><p>Video codec profile that is supported, e.g H264 Main.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.system_info.VideoEncodeAcceleratorCapability.max_resolution">
<span class="sig-name descname"><span class="pre">max_resolution</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.system_info.Size" title="nodriver.cdp.system_info.Size"><code class="xref py py-class docutils literal notranslate"><span class="pre">Size</span></code></a></em><a class="headerlink" href="#nodriver.cdp.system_info.VideoEncodeAcceleratorCapability.max_resolution" title="Link to this definition">#</a></dt>
<dd><p>Maximum video dimensions in pixels supported for this <code class="docutils literal notranslate"><span class="pre">profile</span></code>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.system_info.VideoEncodeAcceleratorCapability.max_framerate_numerator">
<span class="sig-name descname"><span class="pre">max_framerate_numerator</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></em><a class="headerlink" href="#nodriver.cdp.system_info.VideoEncodeAcceleratorCapability.max_framerate_numerator" title="Link to this definition">#</a></dt>
<dd><p>Maximum encoding framerate in frames per second supported for this
<code class="docutils literal notranslate"><span class="pre">profile</span></code>, as fraction’s numerator and denominator, e.g. 24/1 fps,
24000/1001 fps, etc.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.system_info.VideoEncodeAcceleratorCapability.max_framerate_denominator">
<span class="sig-name descname"><span class="pre">max_framerate_denominator</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></em><a class="headerlink" href="#nodriver.cdp.system_info.VideoEncodeAcceleratorCapability.max_framerate_denominator" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.system_info.SubsamplingFormat">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">SubsamplingFormat</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">names</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">module</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">qualname</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">boundary</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/system_info.html#SubsamplingFormat"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.system_info.SubsamplingFormat" title="Link to this definition">#</a></dt>
<dd><p>YUV subsampling type of the pixels of a given image.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.system_info.SubsamplingFormat.YUV420">
<span class="sig-name descname"><span class="pre">YUV420</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'yuv420'</span></em><a class="headerlink" href="#nodriver.cdp.system_info.SubsamplingFormat.YUV420" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.system_info.SubsamplingFormat.YUV422">
<span class="sig-name descname"><span class="pre">YUV422</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'yuv422'</span></em><a class="headerlink" href="#nodriver.cdp.system_info.SubsamplingFormat.YUV422" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.system_info.SubsamplingFormat.YUV444">
<span class="sig-name descname"><span class="pre">YUV444</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'yuv444'</span></em><a class="headerlink" href="#nodriver.cdp.system_info.SubsamplingFormat.YUV444" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.system_info.ImageType">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">ImageType</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">names</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">module</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">qualname</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">boundary</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/system_info.html#ImageType"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.system_info.ImageType" title="Link to this definition">#</a></dt>
<dd><p>Image format of a given image.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.system_info.ImageType.JPEG">
<span class="sig-name descname"><span class="pre">JPEG</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'jpeg'</span></em><a class="headerlink" href="#nodriver.cdp.system_info.ImageType.JPEG" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.system_info.ImageType.WEBP">
<span class="sig-name descname"><span class="pre">WEBP</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'webp'</span></em><a class="headerlink" href="#nodriver.cdp.system_info.ImageType.WEBP" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.system_info.ImageType.UNKNOWN">
<span class="sig-name descname"><span class="pre">UNKNOWN</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'unknown'</span></em><a class="headerlink" href="#nodriver.cdp.system_info.ImageType.UNKNOWN" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.system_info.ImageDecodeAcceleratorCapability">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">ImageDecodeAcceleratorCapability</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">image_type</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">max_dimensions</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">min_dimensions</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">subsamplings</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/system_info.html#ImageDecodeAcceleratorCapability"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.system_info.ImageDecodeAcceleratorCapability" title="Link to this definition">#</a></dt>
<dd><p>Describes a supported image decoding profile with its associated minimum and
maximum resolutions and subsampling.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.system_info.ImageDecodeAcceleratorCapability.image_type">
<span class="sig-name descname"><span class="pre">image_type</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.system_info.ImageType" title="nodriver.cdp.system_info.ImageType"><code class="xref py py-class docutils literal notranslate"><span class="pre">ImageType</span></code></a></em><a class="headerlink" href="#nodriver.cdp.system_info.ImageDecodeAcceleratorCapability.image_type" title="Link to this definition">#</a></dt>
<dd><p>Image coded, e.g. Jpeg.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.system_info.ImageDecodeAcceleratorCapability.max_dimensions">
<span class="sig-name descname"><span class="pre">max_dimensions</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.system_info.Size" title="nodriver.cdp.system_info.Size"><code class="xref py py-class docutils literal notranslate"><span class="pre">Size</span></code></a></em><a class="headerlink" href="#nodriver.cdp.system_info.ImageDecodeAcceleratorCapability.max_dimensions" title="Link to this definition">#</a></dt>
<dd><p>Maximum supported dimensions of the image in pixels.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.system_info.ImageDecodeAcceleratorCapability.min_dimensions">
<span class="sig-name descname"><span class="pre">min_dimensions</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.system_info.Size" title="nodriver.cdp.system_info.Size"><code class="xref py py-class docutils literal notranslate"><span class="pre">Size</span></code></a></em><a class="headerlink" href="#nodriver.cdp.system_info.ImageDecodeAcceleratorCapability.min_dimensions" title="Link to this definition">#</a></dt>
<dd><p>Minimum supported dimensions of the image in pixels.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.system_info.ImageDecodeAcceleratorCapability.subsamplings">
<span class="sig-name descname"><span class="pre">subsamplings</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.system_info.SubsamplingFormat" title="nodriver.cdp.system_info.SubsamplingFormat"><code class="xref py py-class docutils literal notranslate"><span class="pre">SubsamplingFormat</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.system_info.ImageDecodeAcceleratorCapability.subsamplings" title="Link to this definition">#</a></dt>
<dd><p>0, if known.</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>Optional array of supported subsampling formats, e.g. 4</p>
</dd>
<dt class="field-even">Type<span class="colon">:</span></dt>
<dd class="field-even"><p>2</p>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.system_info.GPUInfo">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">GPUInfo</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">devices</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">driver_bug_workarounds</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">video_decoding</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">video_encoding</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">image_decoding</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">aux_attributes</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">feature_status</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/system_info.html#GPUInfo"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.system_info.GPUInfo" title="Link to this definition">#</a></dt>
<dd><p>Provides information about the GPU(s) on the system.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.system_info.GPUInfo.devices">
<span class="sig-name descname"><span class="pre">devices</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.system_info.GPUDevice" title="nodriver.cdp.system_info.GPUDevice"><code class="xref py py-class docutils literal notranslate"><span class="pre">GPUDevice</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.system_info.GPUInfo.devices" title="Link to this definition">#</a></dt>
<dd><p>The graphics devices on the system. Element 0 is the primary GPU.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.system_info.GPUInfo.driver_bug_workarounds">
<span class="sig-name descname"><span class="pre">driver_bug_workarounds</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.system_info.GPUInfo.driver_bug_workarounds" title="Link to this definition">#</a></dt>
<dd><p>An optional array of GPU driver bug workarounds.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.system_info.GPUInfo.video_decoding">
<span class="sig-name descname"><span class="pre">video_decoding</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.system_info.VideoDecodeAcceleratorCapability" title="nodriver.cdp.system_info.VideoDecodeAcceleratorCapability"><code class="xref py py-class docutils literal notranslate"><span class="pre">VideoDecodeAcceleratorCapability</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.system_info.GPUInfo.video_decoding" title="Link to this definition">#</a></dt>
<dd><p>Supported accelerated video decoding capabilities.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.system_info.GPUInfo.video_encoding">
<span class="sig-name descname"><span class="pre">video_encoding</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.system_info.VideoEncodeAcceleratorCapability" title="nodriver.cdp.system_info.VideoEncodeAcceleratorCapability"><code class="xref py py-class docutils literal notranslate"><span class="pre">VideoEncodeAcceleratorCapability</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.system_info.GPUInfo.video_encoding" title="Link to this definition">#</a></dt>
<dd><p>Supported accelerated video encoding capabilities.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.system_info.GPUInfo.image_decoding">
<span class="sig-name descname"><span class="pre">image_decoding</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.system_info.ImageDecodeAcceleratorCapability" title="nodriver.cdp.system_info.ImageDecodeAcceleratorCapability"><code class="xref py py-class docutils literal notranslate"><span class="pre">ImageDecodeAcceleratorCapability</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.system_info.GPUInfo.image_decoding" title="Link to this definition">#</a></dt>
<dd><p>Supported accelerated image decoding capabilities.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.system_info.GPUInfo.aux_attributes">
<span class="sig-name descname"><span class="pre">aux_attributes</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">dict</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.system_info.GPUInfo.aux_attributes" title="Link to this definition">#</a></dt>
<dd><p>An optional dictionary of additional GPU related attributes.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.system_info.GPUInfo.feature_status">
<span class="sig-name descname"><span class="pre">feature_status</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">dict</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.system_info.GPUInfo.feature_status" title="Link to this definition">#</a></dt>
<dd><p>An optional dictionary of graphics features and their status.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.system_info.ProcessInfo">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">ProcessInfo</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">type_</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">id_</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">cpu_time</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/system_info.html#ProcessInfo"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.system_info.ProcessInfo" title="Link to this definition">#</a></dt>
<dd><p>Represents process info.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.system_info.ProcessInfo.type_">
<span class="sig-name descname"><span class="pre">type_</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.system_info.ProcessInfo.type_" title="Link to this definition">#</a></dt>
<dd><p>Specifies process type.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.system_info.ProcessInfo.id_">
<span class="sig-name descname"><span class="pre">id_</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></em><a class="headerlink" href="#nodriver.cdp.system_info.ProcessInfo.id_" title="Link to this definition">#</a></dt>
<dd><p>Specifies process id.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.system_info.ProcessInfo.cpu_time">
<span class="sig-name descname"><span class="pre">cpu_time</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a></em><a class="headerlink" href="#nodriver.cdp.system_info.ProcessInfo.cpu_time" title="Link to this definition">#</a></dt>
<dd><p>Specifies cumulative CPU usage in seconds across all threads of the
process since the process start.</p>
</dd></dl>

</dd></dl>

</section>
<section id="commands">
<h2>Commands<a class="headerlink" href="#commands" title="Link to this heading">#</a></h2>
<p>Each command is a generator function. The return
type <code class="docutils literal notranslate"><span class="pre">Generator[x,</span> <span class="pre">y,</span> <span class="pre">z]</span></code> indicates that the generator
<em>yields</em> arguments of type <code class="docutils literal notranslate"><span class="pre">x</span></code>, it must be resumed with
an argument of type <code class="docutils literal notranslate"><span class="pre">y</span></code>, and it returns type <code class="docutils literal notranslate"><span class="pre">z</span></code>. In
this library, types <code class="docutils literal notranslate"><span class="pre">x</span></code> and <code class="docutils literal notranslate"><span class="pre">y</span></code> are the same for all
commands, and <code class="docutils literal notranslate"><span class="pre">z</span></code> is the return type you should pay attention
to. For more information, see
<a class="reference internal" href="../../readme.html#getting-started-commands"><span class="std std-ref">Getting Started: Commands</span></a>.</p>
<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.system_info.get_feature_state">
<span class="sig-name descname"><span class="pre">get_feature_state</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">feature_state</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/system_info.html#get_feature_state"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.system_info.get_feature_state" title="Link to this definition">#</a></dt>
<dd><p>Returns information about the feature state.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>feature_state</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – </p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.system_info.get_info">
<span class="sig-name descname"><span class="pre">get_info</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/system_info.html#get_info"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.system_info.get_info" title="Link to this definition">#</a></dt>
<dd><p>Returns information about the system.</p>
<dl class="field-list simple">
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Tuple" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Tuple</span></code></a>[<a class="reference internal" href="#nodriver.cdp.system_info.GPUInfo" title="nodriver.cdp.system_info.GPUInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">GPUInfo</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]]</span></p>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p><p>A tuple with the following items:</p>
<ol class="arabic simple" start="0">
<li><p><strong>gpu</strong> - Information about the GPUs on the system.</p></li>
<li><p><strong>modelName</strong> - A platform-dependent description of the model of the machine. On Mac OS, this is, for example, ‘MacBookPro’. Will be the empty string if not supported.</p></li>
<li><p><strong>modelVersion</strong> - A platform-dependent description of the version of the machine. On Mac OS, this is, for example, ‘10.1’. Will be the empty string if not supported.</p></li>
<li><p><strong>commandLine</strong> - The command line string used to launch the browser. Will be the empty string if not supported.</p></li>
</ol>
</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.system_info.get_process_info">
<span class="sig-name descname"><span class="pre">get_process_info</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/system_info.html#get_process_info"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.system_info.get_process_info" title="Link to this definition">#</a></dt>
<dd><p>Returns information about all running processes.</p>
<dl class="field-list simple">
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.system_info.ProcessInfo" title="nodriver.cdp.system_info.ProcessInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">ProcessInfo</span></code></a>]]</span></p>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>An array of process info blocks.</p>
</dd>
</dl>
</dd></dl>

</section>
<section id="events">
<h2>Events<a class="headerlink" href="#events" title="Link to this heading">#</a></h2>
<p><em>There are no events in this module.</em></p>
</section>
</section>

        </article>
      </div>
      <footer>
        
        <div class="related-pages">
          <a class="next-page" href="target.html">
              <div class="page-info">
                <div class="context">
                  <span>Next</span>
                </div>
                <div class="title">Target</div>
              </div>
              <svg class="furo-related-icon"><use href="#svg-arrow-right"></use></svg>
            </a>
          <a class="prev-page" href="storage.html">
              <svg class="furo-related-icon"><use href="#svg-arrow-right"></use></svg>
              <div class="page-info">
                <div class="context">
                  <span>Previous</span>
                </div>
                
                <div class="title">Storage</div>
                
              </div>
            </a>
        </div>
        <div class="bottom-of-page">
          <div class="left-details">
            <div class="copyright">
                Copyright &#169; 2023, Author
            </div>
            Made with <a href="https://www.sphinx-doc.org/">Sphinx</a> and <a class="muted-link" href="https://pradyunsg.me">@pradyunsg</a>'s
            
            <a href="https://github.com/pradyunsg/furo">Furo</a>
            
          </div>
          <div class="right-details">
            
          </div>
        </div>
        
      </footer>
    </div>
    <aside class="toc-drawer">
      
      
      <div class="toc-sticky toc-scroll">
        <div class="toc-title-container">
          <span class="toc-title">
            On this page
          </span>
        </div>
        <div class="toc-tree-container">
          <div class="toc-tree">
            <ul>
<li><a class="reference internal" href="#">SystemInfo</a><ul>
<li><a class="reference internal" href="#types">Types</a><ul>
<li><a class="reference internal" href="#nodriver.cdp.system_info.GPUDevice"><code class="docutils literal notranslate"><span class="pre">GPUDevice</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.system_info.GPUDevice.vendor_id"><code class="docutils literal notranslate"><span class="pre">GPUDevice.vendor_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.system_info.GPUDevice.device_id"><code class="docutils literal notranslate"><span class="pre">GPUDevice.device_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.system_info.GPUDevice.vendor_string"><code class="docutils literal notranslate"><span class="pre">GPUDevice.vendor_string</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.system_info.GPUDevice.device_string"><code class="docutils literal notranslate"><span class="pre">GPUDevice.device_string</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.system_info.GPUDevice.driver_vendor"><code class="docutils literal notranslate"><span class="pre">GPUDevice.driver_vendor</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.system_info.GPUDevice.driver_version"><code class="docutils literal notranslate"><span class="pre">GPUDevice.driver_version</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.system_info.GPUDevice.sub_sys_id"><code class="docutils literal notranslate"><span class="pre">GPUDevice.sub_sys_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.system_info.GPUDevice.revision"><code class="docutils literal notranslate"><span class="pre">GPUDevice.revision</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.system_info.Size"><code class="docutils literal notranslate"><span class="pre">Size</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.system_info.Size.width"><code class="docutils literal notranslate"><span class="pre">Size.width</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.system_info.Size.height"><code class="docutils literal notranslate"><span class="pre">Size.height</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.system_info.VideoDecodeAcceleratorCapability"><code class="docutils literal notranslate"><span class="pre">VideoDecodeAcceleratorCapability</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.system_info.VideoDecodeAcceleratorCapability.profile"><code class="docutils literal notranslate"><span class="pre">VideoDecodeAcceleratorCapability.profile</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.system_info.VideoDecodeAcceleratorCapability.max_resolution"><code class="docutils literal notranslate"><span class="pre">VideoDecodeAcceleratorCapability.max_resolution</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.system_info.VideoDecodeAcceleratorCapability.min_resolution"><code class="docutils literal notranslate"><span class="pre">VideoDecodeAcceleratorCapability.min_resolution</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.system_info.VideoEncodeAcceleratorCapability"><code class="docutils literal notranslate"><span class="pre">VideoEncodeAcceleratorCapability</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.system_info.VideoEncodeAcceleratorCapability.profile"><code class="docutils literal notranslate"><span class="pre">VideoEncodeAcceleratorCapability.profile</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.system_info.VideoEncodeAcceleratorCapability.max_resolution"><code class="docutils literal notranslate"><span class="pre">VideoEncodeAcceleratorCapability.max_resolution</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.system_info.VideoEncodeAcceleratorCapability.max_framerate_numerator"><code class="docutils literal notranslate"><span class="pre">VideoEncodeAcceleratorCapability.max_framerate_numerator</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.system_info.VideoEncodeAcceleratorCapability.max_framerate_denominator"><code class="docutils literal notranslate"><span class="pre">VideoEncodeAcceleratorCapability.max_framerate_denominator</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.system_info.SubsamplingFormat"><code class="docutils literal notranslate"><span class="pre">SubsamplingFormat</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.system_info.SubsamplingFormat.YUV420"><code class="docutils literal notranslate"><span class="pre">SubsamplingFormat.YUV420</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.system_info.SubsamplingFormat.YUV422"><code class="docutils literal notranslate"><span class="pre">SubsamplingFormat.YUV422</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.system_info.SubsamplingFormat.YUV444"><code class="docutils literal notranslate"><span class="pre">SubsamplingFormat.YUV444</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.system_info.ImageType"><code class="docutils literal notranslate"><span class="pre">ImageType</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.system_info.ImageType.JPEG"><code class="docutils literal notranslate"><span class="pre">ImageType.JPEG</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.system_info.ImageType.WEBP"><code class="docutils literal notranslate"><span class="pre">ImageType.WEBP</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.system_info.ImageType.UNKNOWN"><code class="docutils literal notranslate"><span class="pre">ImageType.UNKNOWN</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.system_info.ImageDecodeAcceleratorCapability"><code class="docutils literal notranslate"><span class="pre">ImageDecodeAcceleratorCapability</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.system_info.ImageDecodeAcceleratorCapability.image_type"><code class="docutils literal notranslate"><span class="pre">ImageDecodeAcceleratorCapability.image_type</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.system_info.ImageDecodeAcceleratorCapability.max_dimensions"><code class="docutils literal notranslate"><span class="pre">ImageDecodeAcceleratorCapability.max_dimensions</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.system_info.ImageDecodeAcceleratorCapability.min_dimensions"><code class="docutils literal notranslate"><span class="pre">ImageDecodeAcceleratorCapability.min_dimensions</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.system_info.ImageDecodeAcceleratorCapability.subsamplings"><code class="docutils literal notranslate"><span class="pre">ImageDecodeAcceleratorCapability.subsamplings</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.system_info.GPUInfo"><code class="docutils literal notranslate"><span class="pre">GPUInfo</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.system_info.GPUInfo.devices"><code class="docutils literal notranslate"><span class="pre">GPUInfo.devices</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.system_info.GPUInfo.driver_bug_workarounds"><code class="docutils literal notranslate"><span class="pre">GPUInfo.driver_bug_workarounds</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.system_info.GPUInfo.video_decoding"><code class="docutils literal notranslate"><span class="pre">GPUInfo.video_decoding</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.system_info.GPUInfo.video_encoding"><code class="docutils literal notranslate"><span class="pre">GPUInfo.video_encoding</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.system_info.GPUInfo.image_decoding"><code class="docutils literal notranslate"><span class="pre">GPUInfo.image_decoding</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.system_info.GPUInfo.aux_attributes"><code class="docutils literal notranslate"><span class="pre">GPUInfo.aux_attributes</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.system_info.GPUInfo.feature_status"><code class="docutils literal notranslate"><span class="pre">GPUInfo.feature_status</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.system_info.ProcessInfo"><code class="docutils literal notranslate"><span class="pre">ProcessInfo</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.system_info.ProcessInfo.type_"><code class="docutils literal notranslate"><span class="pre">ProcessInfo.type_</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.system_info.ProcessInfo.id_"><code class="docutils literal notranslate"><span class="pre">ProcessInfo.id_</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.system_info.ProcessInfo.cpu_time"><code class="docutils literal notranslate"><span class="pre">ProcessInfo.cpu_time</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#commands">Commands</a><ul>
<li><a class="reference internal" href="#nodriver.cdp.system_info.get_feature_state"><code class="docutils literal notranslate"><span class="pre">get_feature_state()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.system_info.get_info"><code class="docutils literal notranslate"><span class="pre">get_info()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.system_info.get_process_info"><code class="docutils literal notranslate"><span class="pre">get_process_info()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#events">Events</a></li>
</ul>
</li>
</ul>

          </div>
        </div>
      </div>
      
      
    </aside>
  </div>
</div><script src="../../_static/documentation_options.js?v=5929fcd5"></script>
    <script src="../../_static/doctools.js?v=888ff710"></script>
    <script src="../../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../../_static/scripts/furo.js?v=32e29ea5"></script>
    </body>
</html>