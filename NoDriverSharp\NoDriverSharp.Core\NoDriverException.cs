namespace NoDriverSharp.Core;

/// <summary>
/// Base exception for all NoDriverSharp exceptions
/// </summary>
public class NoDriverException : Exception
{
    public NoDriverException() : base() { }
    
    public NoDriverException(string message) : base(message) { }
    
    public NoDriverException(string message, Exception innerException) : base(message, innerException) { }
}

/// <summary>
/// Exception thrown when a CDP protocol error occurs
/// </summary>
public class ProtocolException : NoDriverException
{
    /// <summary>
    /// CDP error code
    /// </summary>
    public int ErrorCode { get; }

    /// <summary>
    /// CDP error data
    /// </summary>
    public object? ErrorData { get; }

    public ProtocolException(int errorCode, string message, object? errorData = null) 
        : base($"CDP Protocol Error {errorCode}: {message}")
    {
        ErrorCode = errorCode;
        ErrorData = errorData;
    }

    public ProtocolException(int errorCode, string message, Exception innerException, object? errorData = null) 
        : base($"CDP Protocol Error {errorCode}: {message}", innerException)
    {
        ErrorCode = errorCode;
        ErrorData = errorData;
    }
}

/// <summary>
/// Exception thrown when browser connection fails
/// </summary>
public class ConnectionException : NoDriverException
{
    public ConnectionException() : base() { }
    
    public ConnectionException(string message) : base(message) { }
    
    public ConnectionException(string message, Exception innerException) : base(message, innerException) { }
}

/// <summary>
/// Exception thrown when browser startup fails
/// </summary>
public class BrowserStartupException : NoDriverException
{
    public BrowserStartupException() : base() { }
    
    public BrowserStartupException(string message) : base(message) { }
    
    public BrowserStartupException(string message, Exception innerException) : base(message, innerException) { }
}

/// <summary>
/// Exception thrown when element operations fail
/// </summary>
public class ElementException : NoDriverException
{
    public ElementException() : base() { }
    
    public ElementException(string message) : base(message) { }
    
    public ElementException(string message, Exception innerException) : base(message, innerException) { }
}

/// <summary>
/// Exception thrown when navigation operations fail
/// </summary>
public class NavigationException : NoDriverException
{
    public NavigationException() : base() { }
    
    public NavigationException(string message) : base(message) { }
    
    public NavigationException(string message, Exception innerException) : base(message, innerException) { }
}

/// <summary>
/// Exception thrown when timeout occurs
/// </summary>
public class TimeoutException : NoDriverException
{
    /// <summary>
    /// The timeout duration that was exceeded
    /// </summary>
    public TimeSpan Timeout { get; }

    public TimeoutException(TimeSpan timeout) 
        : base($"Operation timed out after {timeout.TotalSeconds:F1} seconds")
    {
        Timeout = timeout;
    }

    public TimeoutException(TimeSpan timeout, string message) 
        : base($"{message} (timeout: {timeout.TotalSeconds:F1} seconds)")
    {
        Timeout = timeout;
    }

    public TimeoutException(TimeSpan timeout, string message, Exception innerException) 
        : base($"{message} (timeout: {timeout.TotalSeconds:F1} seconds)", innerException)
    {
        Timeout = timeout;
    }
}
