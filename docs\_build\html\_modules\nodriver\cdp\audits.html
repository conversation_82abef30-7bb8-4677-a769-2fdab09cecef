<!doctype html>
<html class="no-js" lang="en" data-content_root="../../../">
  <head><meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width,initial-scale=1"/>
    <meta name="color-scheme" content="light dark"><link rel="index" title="Index" href="../../../genindex.html" /><link rel="search" title="Search" href="../../../search.html" />

    <!-- Generated with Sphinx 7.2.6 and Furo 2023.09.10 -->
        <title>nodriver.cdp.audits - nodriver documentation</title>
      <link rel="stylesheet" type="text/css" href="../../../_static/pygments.css?v=fa44fd50" />
    <link rel="stylesheet" type="text/css" href="../../../_static/styles/furo.css?v=135e06be" />
    <link rel="stylesheet" type="text/css" href="../../../_static/styles/furo-extensions.css?v=36a5483c" />
    <link rel="stylesheet" type="text/css" href="../../../_static/./custom.css?v=3934642d" />
    
    


<style>
  body {
    --color-code-background: #eeffcc;
  --color-code-foreground: black;
  
  }
  @media not print {
    body[data-theme="dark"] {
      --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8eefba;
  --color-background-primary: #111112;
  --color-problematic: #e7aeae;
  
    }
    @media (prefers-color-scheme: dark) {
      body:not([data-theme="light"]) {
        --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8eefba;
  --color-background-primary: #111112;
  --color-problematic: #e7aeae;
  
      }
    }
  }
</style></head>
  <body>
    
    <script>
      document.body.dataset.theme = localStorage.getItem("theme") || "auto";
    </script>
    

<svg xmlns="http://www.w3.org/2000/svg" style="display: none;">
  <symbol id="svg-toc" viewBox="0 0 24 24">
    <title>Contents</title>
    <svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 1024 1024">
      <path d="M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM115.4 518.9L271.7 642c5.8 4.6 14.4.5 14.4-6.9V388.9c0-7.4-8.5-11.5-14.4-6.9L115.4 505.1a8.74 8.74 0 0 0 0 13.8z"/>
    </svg>
  </symbol>
  <symbol id="svg-menu" viewBox="0 0 24 24">
    <title>Menu</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-menu">
      <line x1="3" y1="12" x2="21" y2="12"></line>
      <line x1="3" y1="6" x2="21" y2="6"></line>
      <line x1="3" y1="18" x2="21" y2="18"></line>
    </svg>
  </symbol>
  <symbol id="svg-arrow-right" viewBox="0 0 24 24">
    <title>Expand</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-chevron-right">
      <polyline points="9 18 15 12 9 6"></polyline>
    </svg>
  </symbol>
  <symbol id="svg-sun" viewBox="0 0 24 24">
    <title>Light mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="feather-sun">
      <circle cx="12" cy="12" r="5"></circle>
      <line x1="12" y1="1" x2="12" y2="3"></line>
      <line x1="12" y1="21" x2="12" y2="23"></line>
      <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
      <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
      <line x1="1" y1="12" x2="3" y2="12"></line>
      <line x1="21" y1="12" x2="23" y2="12"></line>
      <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
      <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
    </svg>
  </symbol>
  <symbol id="svg-moon" viewBox="0 0 24 24">
    <title>Dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-moon">
      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
      <path d="M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z" />
    </svg>
  </symbol>
  <symbol id="svg-sun-half" viewBox="0 0 24 24">
    <title>Auto light/dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-shadow">
      <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
      <circle cx="12" cy="12" r="9" />
      <path d="M13 12h5" />
      <path d="M13 15h4" />
      <path d="M13 18h1" />
      <path d="M13 9h4" />
      <path d="M13 6h1" />
    </svg>
  </symbol>
</svg>

<input type="checkbox" class="sidebar-toggle" name="__navigation" id="__navigation">
<input type="checkbox" class="sidebar-toggle" name="__toc" id="__toc">
<label class="overlay sidebar-overlay" for="__navigation">
  <div class="visually-hidden">Hide navigation sidebar</div>
</label>
<label class="overlay toc-overlay" for="__toc">
  <div class="visually-hidden">Hide table of contents sidebar</div>
</label>



<div class="page">
  <header class="mobile-header">
    <div class="header-left">
      <label class="nav-overlay-icon" for="__navigation">
        <div class="visually-hidden">Toggle site navigation sidebar</div>
        <i class="icon"><svg><use href="#svg-menu"></use></svg></i>
      </label>
    </div>
    <div class="header-center">
      <a href="../../../index.html"><div class="brand">nodriver  documentation</div></a>
    </div>
    <div class="header-right">
      <div class="theme-toggle-container theme-toggle-header">
        <button class="theme-toggle">
          <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
          <svg class="theme-icon-when-auto"><use href="#svg-sun-half"></use></svg>
          <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
          <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
        </button>
      </div>
      <label class="toc-overlay-icon toc-header-icon no-toc" for="__toc">
        <div class="visually-hidden">Toggle table of contents sidebar</div>
        <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
      </label>
    </div>
  </header>
  <aside class="sidebar-drawer">
    <div class="sidebar-container">
      
      <div class="sidebar-sticky"><div class="sidebar-scroll"><a class="sidebar-brand" href="../../../index.html">
  
  
  <span class="sidebar-brand-text">nodriver  documentation</span>
  
</a><form class="sidebar-search-container" method="get" action="../../../search.html" role="search">
  <input class="sidebar-search" placeholder="Search" name="q" aria-label="Search">
  <input type="hidden" name="check_keywords" value="yes">
  <input type="hidden" name="area" value="default">
</form>
<div id="searchbox"></div><div class="sidebar-tree">
  <ul>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html">Quickstart guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#usage-example">usage example</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#more-complete-example">More complete example</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#custom-starting-options">Custom starting options</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#alternative-custom-options">Alternative custom options</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#proxies-socks5-authenticated-too">Proxies (socks5, authenticated too)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#concrete-example">Concrete example</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/classes/browser.html">Browser class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/classes/tab.html">Tab class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/classes/element.html">Element class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/classes/others_and_helpers.html">Other classes and Helper classes</a></li>
</ul>
<ul>
<li class="toctree-l1 has-children"><a class="reference internal" href="../../../nodriver/cdp.html">CDP object</a><input class="toctree-checkbox" id="toctree-checkbox-1" name="toctree-checkbox-1" role="switch" type="checkbox"/><label for="toctree-checkbox-1"><div class="visually-hidden">Toggle navigation of CDP object</div><i class="icon"><svg><use href="#svg-arrow-right"></use></svg></i></label><ul>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/accessibility.html">Accessibility</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/animation.html">Animation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/audits.html">Audits</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/autofill.html">Autofill</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/background_service.html">BackgroundService</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/bluetooth_emulation.html">BluetoothEmulation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/browser.html">Browser</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/cache_storage.html">CacheStorage</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/cast.html">Cast</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/console.html">Console</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/css.html">CSS</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/debugger.html">Debugger</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/device_access.html">DeviceAccess</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/device_orientation.html">DeviceOrientation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/dom.html">DOM</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/dom_debugger.html">DOMDebugger</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/dom_snapshot.html">DOMSnapshot</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/dom_storage.html">DOMStorage</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/emulation.html">Emulation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/event_breakpoints.html">EventBreakpoints</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/extensions.html">Extensions</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/fed_cm.html">FedCm</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/fetch.html">Fetch</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/file_system.html">FileSystem</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/headless_experimental.html">HeadlessExperimental</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/heap_profiler.html">HeapProfiler</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/indexed_db.html">IndexedDB</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/input_.html">Input</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/inspector.html">Inspector</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/io.html">IO</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/layer_tree.html">LayerTree</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/log.html">Log</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/media.html">Media</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/memory.html">Memory</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/network.html">Network</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/overlay.html">Overlay</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/page.html">Page</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/performance.html">Performance</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/performance_timeline.html">PerformanceTimeline</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/preload.html">Preload</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/profiler.html">Profiler</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/pwa.html">PWA</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/runtime.html">Runtime</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/schema.html">Schema</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/security.html">Security</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/service_worker.html">ServiceWorker</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/storage.html">Storage</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/system_info.html">SystemInfo</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/target.html">Target</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/tethering.html">Tethering</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/tracing.html">Tracing</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/web_audio.html">WebAudio</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/web_authn.html">WebAuthn</a></li>
</ul>
</li>
</ul>

</div>
</div>
      </div>
      
    </div>
  </aside>
  <div class="main">
    <div class="content">
      <div class="article-container">
        <a href="#" class="back-to-top muted-link">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
            <path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8v12z"></path>
          </svg>
          <span>Back to top</span>
        </a>
        <div class="content-icon-container">
          <div class="theme-toggle-container theme-toggle-content">
            <button class="theme-toggle">
              <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
              <svg class="theme-icon-when-auto"><use href="#svg-sun-half"></use></svg>
              <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
              <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
            </button>
          </div>
          <label class="toc-overlay-icon toc-content-icon no-toc" for="__toc">
            <div class="visually-hidden">Toggle table of contents sidebar</div>
            <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
          </label>
        </div>
        <article role="main">
          <h1>Source code for nodriver.cdp.audits</h1><div class="highlight"><pre>
<span></span><span class="c1"># DO NOT EDIT THIS FILE!</span>
<span class="c1">#</span>
<span class="c1"># This file is generated from the CDP specification. If you need to make</span>
<span class="c1"># changes, edit the generator and regenerate all of the modules.</span>
<span class="c1">#</span>
<span class="c1"># CDP domain: Audits (experimental)</span>

<span class="kn">from</span> <span class="nn">__future__</span> <span class="kn">import</span> <span class="n">annotations</span>

<span class="kn">import</span> <span class="nn">enum</span>
<span class="kn">import</span> <span class="nn">typing</span>
<span class="kn">from</span> <span class="nn">dataclasses</span> <span class="kn">import</span> <span class="n">dataclass</span>

<span class="kn">from</span> <span class="nn">.</span> <span class="kn">import</span> <span class="n">dom</span><span class="p">,</span> <span class="n">network</span><span class="p">,</span> <span class="n">page</span><span class="p">,</span> <span class="n">runtime</span>
<span class="kn">from</span> <span class="nn">.util</span> <span class="kn">import</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">event_class</span>


<div class="viewcode-block" id="AffectedCookie">
<a class="viewcode-back" href="../../../nodriver/cdp/audits.html#nodriver.cdp.audits.AffectedCookie">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">AffectedCookie</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Information about a cookie that is affected by an inspector issue.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: The following three properties uniquely identify a cookie</span>
    <span class="n">name</span><span class="p">:</span> <span class="nb">str</span>

    <span class="n">path</span><span class="p">:</span> <span class="nb">str</span>

    <span class="n">domain</span><span class="p">:</span> <span class="nb">str</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;name&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">name</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;path&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">path</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;domain&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">domain</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">AffectedCookie</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">name</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;name&quot;</span><span class="p">]),</span>
            <span class="n">path</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;path&quot;</span><span class="p">]),</span>
            <span class="n">domain</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;domain&quot;</span><span class="p">]),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="AffectedRequest">
<a class="viewcode-back" href="../../../nodriver/cdp/audits.html#nodriver.cdp.audits.AffectedRequest">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">AffectedRequest</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Information about a request that is affected by an inspector issue.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">url</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: The unique request id.</span>
    <span class="n">request_id</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">network</span><span class="o">.</span><span class="n">RequestId</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;url&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">url</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">request_id</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;requestId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">request_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">AffectedRequest</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">url</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;url&quot;</span><span class="p">]),</span>
            <span class="n">request_id</span><span class="o">=</span><span class="p">(</span>
                <span class="n">network</span><span class="o">.</span><span class="n">RequestId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;requestId&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;requestId&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="AffectedFrame">
<a class="viewcode-back" href="../../../nodriver/cdp/audits.html#nodriver.cdp.audits.AffectedFrame">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">AffectedFrame</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Information about the frame affected by an inspector issue.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">frame_id</span><span class="p">:</span> <span class="n">page</span><span class="o">.</span><span class="n">FrameId</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;frameId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">frame_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">AffectedFrame</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">frame_id</span><span class="o">=</span><span class="n">page</span><span class="o">.</span><span class="n">FrameId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;frameId&quot;</span><span class="p">]),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="CookieExclusionReason">
<a class="viewcode-back" href="../../../nodriver/cdp/audits.html#nodriver.cdp.audits.CookieExclusionReason">[docs]</a>
<span class="k">class</span> <span class="nc">CookieExclusionReason</span><span class="p">(</span><span class="n">enum</span><span class="o">.</span><span class="n">Enum</span><span class="p">):</span>
    <span class="n">EXCLUDE_SAME_SITE_UNSPECIFIED_TREATED_AS_LAX</span> <span class="o">=</span> <span class="p">(</span>
        <span class="s2">&quot;ExcludeSameSiteUnspecifiedTreatedAsLax&quot;</span>
    <span class="p">)</span>
    <span class="n">EXCLUDE_SAME_SITE_NONE_INSECURE</span> <span class="o">=</span> <span class="s2">&quot;ExcludeSameSiteNoneInsecure&quot;</span>
    <span class="n">EXCLUDE_SAME_SITE_LAX</span> <span class="o">=</span> <span class="s2">&quot;ExcludeSameSiteLax&quot;</span>
    <span class="n">EXCLUDE_SAME_SITE_STRICT</span> <span class="o">=</span> <span class="s2">&quot;ExcludeSameSiteStrict&quot;</span>
    <span class="n">EXCLUDE_INVALID_SAME_PARTY</span> <span class="o">=</span> <span class="s2">&quot;ExcludeInvalidSameParty&quot;</span>
    <span class="n">EXCLUDE_SAME_PARTY_CROSS_PARTY_CONTEXT</span> <span class="o">=</span> <span class="s2">&quot;ExcludeSamePartyCrossPartyContext&quot;</span>
    <span class="n">EXCLUDE_DOMAIN_NON_ASCII</span> <span class="o">=</span> <span class="s2">&quot;ExcludeDomainNonASCII&quot;</span>
    <span class="n">EXCLUDE_THIRD_PARTY_COOKIE_BLOCKED_IN_FIRST_PARTY_SET</span> <span class="o">=</span> <span class="p">(</span>
        <span class="s2">&quot;ExcludeThirdPartyCookieBlockedInFirstPartySet&quot;</span>
    <span class="p">)</span>
    <span class="n">EXCLUDE_THIRD_PARTY_PHASEOUT</span> <span class="o">=</span> <span class="s2">&quot;ExcludeThirdPartyPhaseout&quot;</span>
    <span class="n">EXCLUDE_PORT_MISMATCH</span> <span class="o">=</span> <span class="s2">&quot;ExcludePortMismatch&quot;</span>
    <span class="n">EXCLUDE_SCHEME_MISMATCH</span> <span class="o">=</span> <span class="s2">&quot;ExcludeSchemeMismatch&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">CookieExclusionReason</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span></div>



<div class="viewcode-block" id="CookieWarningReason">
<a class="viewcode-back" href="../../../nodriver/cdp/audits.html#nodriver.cdp.audits.CookieWarningReason">[docs]</a>
<span class="k">class</span> <span class="nc">CookieWarningReason</span><span class="p">(</span><span class="n">enum</span><span class="o">.</span><span class="n">Enum</span><span class="p">):</span>
    <span class="n">WARN_SAME_SITE_UNSPECIFIED_CROSS_SITE_CONTEXT</span> <span class="o">=</span> <span class="p">(</span>
        <span class="s2">&quot;WarnSameSiteUnspecifiedCrossSiteContext&quot;</span>
    <span class="p">)</span>
    <span class="n">WARN_SAME_SITE_NONE_INSECURE</span> <span class="o">=</span> <span class="s2">&quot;WarnSameSiteNoneInsecure&quot;</span>
    <span class="n">WARN_SAME_SITE_UNSPECIFIED_LAX_ALLOW_UNSAFE</span> <span class="o">=</span> <span class="p">(</span>
        <span class="s2">&quot;WarnSameSiteUnspecifiedLaxAllowUnsafe&quot;</span>
    <span class="p">)</span>
    <span class="n">WARN_SAME_SITE_STRICT_LAX_DOWNGRADE_STRICT</span> <span class="o">=</span> <span class="s2">&quot;WarnSameSiteStrictLaxDowngradeStrict&quot;</span>
    <span class="n">WARN_SAME_SITE_STRICT_CROSS_DOWNGRADE_STRICT</span> <span class="o">=</span> <span class="p">(</span>
        <span class="s2">&quot;WarnSameSiteStrictCrossDowngradeStrict&quot;</span>
    <span class="p">)</span>
    <span class="n">WARN_SAME_SITE_STRICT_CROSS_DOWNGRADE_LAX</span> <span class="o">=</span> <span class="s2">&quot;WarnSameSiteStrictCrossDowngradeLax&quot;</span>
    <span class="n">WARN_SAME_SITE_LAX_CROSS_DOWNGRADE_STRICT</span> <span class="o">=</span> <span class="s2">&quot;WarnSameSiteLaxCrossDowngradeStrict&quot;</span>
    <span class="n">WARN_SAME_SITE_LAX_CROSS_DOWNGRADE_LAX</span> <span class="o">=</span> <span class="s2">&quot;WarnSameSiteLaxCrossDowngradeLax&quot;</span>
    <span class="n">WARN_ATTRIBUTE_VALUE_EXCEEDS_MAX_SIZE</span> <span class="o">=</span> <span class="s2">&quot;WarnAttributeValueExceedsMaxSize&quot;</span>
    <span class="n">WARN_DOMAIN_NON_ASCII</span> <span class="o">=</span> <span class="s2">&quot;WarnDomainNonASCII&quot;</span>
    <span class="n">WARN_THIRD_PARTY_PHASEOUT</span> <span class="o">=</span> <span class="s2">&quot;WarnThirdPartyPhaseout&quot;</span>
    <span class="n">WARN_CROSS_SITE_REDIRECT_DOWNGRADE_CHANGES_INCLUSION</span> <span class="o">=</span> <span class="p">(</span>
        <span class="s2">&quot;WarnCrossSiteRedirectDowngradeChangesInclusion&quot;</span>
    <span class="p">)</span>
    <span class="n">WARN_DEPRECATION_TRIAL_METADATA</span> <span class="o">=</span> <span class="s2">&quot;WarnDeprecationTrialMetadata&quot;</span>
    <span class="n">WARN_THIRD_PARTY_COOKIE_HEURISTIC</span> <span class="o">=</span> <span class="s2">&quot;WarnThirdPartyCookieHeuristic&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">CookieWarningReason</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span></div>



<div class="viewcode-block" id="CookieOperation">
<a class="viewcode-back" href="../../../nodriver/cdp/audits.html#nodriver.cdp.audits.CookieOperation">[docs]</a>
<span class="k">class</span> <span class="nc">CookieOperation</span><span class="p">(</span><span class="n">enum</span><span class="o">.</span><span class="n">Enum</span><span class="p">):</span>
    <span class="n">SET_COOKIE</span> <span class="o">=</span> <span class="s2">&quot;SetCookie&quot;</span>
    <span class="n">READ_COOKIE</span> <span class="o">=</span> <span class="s2">&quot;ReadCookie&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">CookieOperation</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span></div>



<div class="viewcode-block" id="InsightType">
<a class="viewcode-back" href="../../../nodriver/cdp/audits.html#nodriver.cdp.audits.InsightType">[docs]</a>
<span class="k">class</span> <span class="nc">InsightType</span><span class="p">(</span><span class="n">enum</span><span class="o">.</span><span class="n">Enum</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Represents the category of insight that a cookie issue falls under.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">GIT_HUB_RESOURCE</span> <span class="o">=</span> <span class="s2">&quot;GitHubResource&quot;</span>
    <span class="n">GRACE_PERIOD</span> <span class="o">=</span> <span class="s2">&quot;GracePeriod&quot;</span>
    <span class="n">HEURISTICS</span> <span class="o">=</span> <span class="s2">&quot;Heuristics&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">InsightType</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span></div>



<div class="viewcode-block" id="CookieIssueInsight">
<a class="viewcode-back" href="../../../nodriver/cdp/audits.html#nodriver.cdp.audits.CookieIssueInsight">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">CookieIssueInsight</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Information about the suggested solution to a cookie issue.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">type_</span><span class="p">:</span> <span class="n">InsightType</span>

    <span class="c1">#: Link to table entry in third-party cookie migration readiness list.</span>
    <span class="n">table_entry_url</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;type&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">type_</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">table_entry_url</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;tableEntryUrl&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">table_entry_url</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">CookieIssueInsight</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">type_</span><span class="o">=</span><span class="n">InsightType</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;type&quot;</span><span class="p">]),</span>
            <span class="n">table_entry_url</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;tableEntryUrl&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;tableEntryUrl&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="CookieIssueDetails">
<a class="viewcode-back" href="../../../nodriver/cdp/audits.html#nodriver.cdp.audits.CookieIssueDetails">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">CookieIssueDetails</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    This information is currently necessary, as the front-end has a difficult</span>
<span class="sd">    time finding a specific cookie. With this, we can convey specific error</span>
<span class="sd">    information without the cookie.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">cookie_warning_reasons</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">CookieWarningReason</span><span class="p">]</span>

    <span class="n">cookie_exclusion_reasons</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">CookieExclusionReason</span><span class="p">]</span>

    <span class="c1">#: Optionally identifies the site-for-cookies and the cookie url, which</span>
    <span class="c1">#: may be used by the front-end as additional context.</span>
    <span class="n">operation</span><span class="p">:</span> <span class="n">CookieOperation</span>

    <span class="c1">#: If AffectedCookie is not set then rawCookieLine contains the raw</span>
    <span class="c1">#: Set-Cookie header string. This hints at a problem where the</span>
    <span class="c1">#: cookie line is syntactically or semantically malformed in a way</span>
    <span class="c1">#: that no valid cookie could be created.</span>
    <span class="n">cookie</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">AffectedCookie</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="n">raw_cookie_line</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="n">site_for_cookies</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="n">cookie_url</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="n">request</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">AffectedRequest</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: The recommended solution to the issue.</span>
    <span class="n">insight</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">CookieIssueInsight</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;cookieWarningReasons&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span>
            <span class="n">i</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">cookie_warning_reasons</span>
        <span class="p">]</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;cookieExclusionReasons&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span>
            <span class="n">i</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">cookie_exclusion_reasons</span>
        <span class="p">]</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;operation&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">operation</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">cookie</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;cookie&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">cookie</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">raw_cookie_line</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;rawCookieLine&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">raw_cookie_line</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">site_for_cookies</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;siteForCookies&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">site_for_cookies</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">cookie_url</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;cookieUrl&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">cookie_url</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">request</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;request&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">request</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">insight</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;insight&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">insight</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">CookieIssueDetails</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">cookie_warning_reasons</span><span class="o">=</span><span class="p">[</span>
                <span class="n">CookieWarningReason</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;cookieWarningReasons&quot;</span><span class="p">]</span>
            <span class="p">],</span>
            <span class="n">cookie_exclusion_reasons</span><span class="o">=</span><span class="p">[</span>
                <span class="n">CookieExclusionReason</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span>
                <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;cookieExclusionReasons&quot;</span><span class="p">]</span>
            <span class="p">],</span>
            <span class="n">operation</span><span class="o">=</span><span class="n">CookieOperation</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;operation&quot;</span><span class="p">]),</span>
            <span class="n">cookie</span><span class="o">=</span><span class="p">(</span>
                <span class="n">AffectedCookie</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;cookie&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;cookie&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">raw_cookie_line</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;rawCookieLine&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;rawCookieLine&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">site_for_cookies</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;siteForCookies&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;siteForCookies&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">cookie_url</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;cookieUrl&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;cookieUrl&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">request</span><span class="o">=</span><span class="p">(</span>
                <span class="n">AffectedRequest</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;request&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;request&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">insight</span><span class="o">=</span><span class="p">(</span>
                <span class="n">CookieIssueInsight</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;insight&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;insight&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="MixedContentResolutionStatus">
<a class="viewcode-back" href="../../../nodriver/cdp/audits.html#nodriver.cdp.audits.MixedContentResolutionStatus">[docs]</a>
<span class="k">class</span> <span class="nc">MixedContentResolutionStatus</span><span class="p">(</span><span class="n">enum</span><span class="o">.</span><span class="n">Enum</span><span class="p">):</span>
    <span class="n">MIXED_CONTENT_BLOCKED</span> <span class="o">=</span> <span class="s2">&quot;MixedContentBlocked&quot;</span>
    <span class="n">MIXED_CONTENT_AUTOMATICALLY_UPGRADED</span> <span class="o">=</span> <span class="s2">&quot;MixedContentAutomaticallyUpgraded&quot;</span>
    <span class="n">MIXED_CONTENT_WARNING</span> <span class="o">=</span> <span class="s2">&quot;MixedContentWarning&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">MixedContentResolutionStatus</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span></div>



<div class="viewcode-block" id="MixedContentResourceType">
<a class="viewcode-back" href="../../../nodriver/cdp/audits.html#nodriver.cdp.audits.MixedContentResourceType">[docs]</a>
<span class="k">class</span> <span class="nc">MixedContentResourceType</span><span class="p">(</span><span class="n">enum</span><span class="o">.</span><span class="n">Enum</span><span class="p">):</span>
    <span class="n">ATTRIBUTION_SRC</span> <span class="o">=</span> <span class="s2">&quot;AttributionSrc&quot;</span>
    <span class="n">AUDIO</span> <span class="o">=</span> <span class="s2">&quot;Audio&quot;</span>
    <span class="n">BEACON</span> <span class="o">=</span> <span class="s2">&quot;Beacon&quot;</span>
    <span class="n">CSP_REPORT</span> <span class="o">=</span> <span class="s2">&quot;CSPReport&quot;</span>
    <span class="n">DOWNLOAD</span> <span class="o">=</span> <span class="s2">&quot;Download&quot;</span>
    <span class="n">EVENT_SOURCE</span> <span class="o">=</span> <span class="s2">&quot;EventSource&quot;</span>
    <span class="n">FAVICON</span> <span class="o">=</span> <span class="s2">&quot;Favicon&quot;</span>
    <span class="n">FONT</span> <span class="o">=</span> <span class="s2">&quot;Font&quot;</span>
    <span class="n">FORM</span> <span class="o">=</span> <span class="s2">&quot;Form&quot;</span>
    <span class="n">FRAME</span> <span class="o">=</span> <span class="s2">&quot;Frame&quot;</span>
    <span class="n">IMAGE</span> <span class="o">=</span> <span class="s2">&quot;Image&quot;</span>
    <span class="n">IMPORT</span> <span class="o">=</span> <span class="s2">&quot;Import&quot;</span>
    <span class="n">JSON</span> <span class="o">=</span> <span class="s2">&quot;JSON&quot;</span>
    <span class="n">MANIFEST</span> <span class="o">=</span> <span class="s2">&quot;Manifest&quot;</span>
    <span class="n">PING</span> <span class="o">=</span> <span class="s2">&quot;Ping&quot;</span>
    <span class="n">PLUGIN_DATA</span> <span class="o">=</span> <span class="s2">&quot;PluginData&quot;</span>
    <span class="n">PLUGIN_RESOURCE</span> <span class="o">=</span> <span class="s2">&quot;PluginResource&quot;</span>
    <span class="n">PREFETCH</span> <span class="o">=</span> <span class="s2">&quot;Prefetch&quot;</span>
    <span class="n">RESOURCE</span> <span class="o">=</span> <span class="s2">&quot;Resource&quot;</span>
    <span class="n">SCRIPT</span> <span class="o">=</span> <span class="s2">&quot;Script&quot;</span>
    <span class="n">SERVICE_WORKER</span> <span class="o">=</span> <span class="s2">&quot;ServiceWorker&quot;</span>
    <span class="n">SHARED_WORKER</span> <span class="o">=</span> <span class="s2">&quot;SharedWorker&quot;</span>
    <span class="n">SPECULATION_RULES</span> <span class="o">=</span> <span class="s2">&quot;SpeculationRules&quot;</span>
    <span class="n">STYLESHEET</span> <span class="o">=</span> <span class="s2">&quot;Stylesheet&quot;</span>
    <span class="n">TRACK</span> <span class="o">=</span> <span class="s2">&quot;Track&quot;</span>
    <span class="n">VIDEO</span> <span class="o">=</span> <span class="s2">&quot;Video&quot;</span>
    <span class="n">WORKER</span> <span class="o">=</span> <span class="s2">&quot;Worker&quot;</span>
    <span class="n">XML_HTTP_REQUEST</span> <span class="o">=</span> <span class="s2">&quot;XMLHttpRequest&quot;</span>
    <span class="n">XSLT</span> <span class="o">=</span> <span class="s2">&quot;XSLT&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">MixedContentResourceType</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span></div>



<div class="viewcode-block" id="MixedContentIssueDetails">
<a class="viewcode-back" href="../../../nodriver/cdp/audits.html#nodriver.cdp.audits.MixedContentIssueDetails">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">MixedContentIssueDetails</span><span class="p">:</span>
    <span class="c1">#: The way the mixed content issue is being resolved.</span>
    <span class="n">resolution_status</span><span class="p">:</span> <span class="n">MixedContentResolutionStatus</span>

    <span class="c1">#: The unsafe http url causing the mixed content issue.</span>
    <span class="n">insecure_url</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: The url responsible for the call to an unsafe url.</span>
    <span class="n">main_resource_url</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: The type of resource causing the mixed content issue (css, js, iframe,</span>
    <span class="c1">#: form,...). Marked as optional because it is mapped to from</span>
    <span class="c1">#: blink::mojom::RequestContextType, which will be replaced</span>
    <span class="c1">#: by network::mojom::RequestDestination</span>
    <span class="n">resource_type</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">MixedContentResourceType</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: The mixed content request.</span>
    <span class="c1">#: Does not always exist (e.g. for unsafe form submission urls).</span>
    <span class="n">request</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">AffectedRequest</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: Optional because not every mixed content issue is necessarily linked to a frame.</span>
    <span class="n">frame</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">AffectedFrame</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;resolutionStatus&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">resolution_status</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;insecureURL&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">insecure_url</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;mainResourceURL&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">main_resource_url</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">resource_type</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;resourceType&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">resource_type</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">request</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;request&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">request</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">frame</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;frame&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">frame</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">MixedContentIssueDetails</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">resolution_status</span><span class="o">=</span><span class="n">MixedContentResolutionStatus</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span>
                <span class="n">json</span><span class="p">[</span><span class="s2">&quot;resolutionStatus&quot;</span><span class="p">]</span>
            <span class="p">),</span>
            <span class="n">insecure_url</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;insecureURL&quot;</span><span class="p">]),</span>
            <span class="n">main_resource_url</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;mainResourceURL&quot;</span><span class="p">]),</span>
            <span class="n">resource_type</span><span class="o">=</span><span class="p">(</span>
                <span class="n">MixedContentResourceType</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;resourceType&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;resourceType&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">request</span><span class="o">=</span><span class="p">(</span>
                <span class="n">AffectedRequest</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;request&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;request&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">frame</span><span class="o">=</span><span class="p">(</span>
                <span class="n">AffectedFrame</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;frame&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;frame&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="BlockedByResponseReason">
<a class="viewcode-back" href="../../../nodriver/cdp/audits.html#nodriver.cdp.audits.BlockedByResponseReason">[docs]</a>
<span class="k">class</span> <span class="nc">BlockedByResponseReason</span><span class="p">(</span><span class="n">enum</span><span class="o">.</span><span class="n">Enum</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Enum indicating the reason a response has been blocked. These reasons are</span>
<span class="sd">    refinements of the net error BLOCKED_BY_RESPONSE.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">COEP_FRAME_RESOURCE_NEEDS_COEP_HEADER</span> <span class="o">=</span> <span class="s2">&quot;CoepFrameResourceNeedsCoepHeader&quot;</span>
    <span class="n">COOP_SANDBOXED_I_FRAME_CANNOT_NAVIGATE_TO_COOP_PAGE</span> <span class="o">=</span> <span class="p">(</span>
        <span class="s2">&quot;CoopSandboxedIFrameCannotNavigateToCoopPage&quot;</span>
    <span class="p">)</span>
    <span class="n">CORP_NOT_SAME_ORIGIN</span> <span class="o">=</span> <span class="s2">&quot;CorpNotSameOrigin&quot;</span>
    <span class="n">CORP_NOT_SAME_ORIGIN_AFTER_DEFAULTED_TO_SAME_ORIGIN_BY_COEP</span> <span class="o">=</span> <span class="p">(</span>
        <span class="s2">&quot;CorpNotSameOriginAfterDefaultedToSameOriginByCoep&quot;</span>
    <span class="p">)</span>
    <span class="n">CORP_NOT_SAME_ORIGIN_AFTER_DEFAULTED_TO_SAME_ORIGIN_BY_DIP</span> <span class="o">=</span> <span class="p">(</span>
        <span class="s2">&quot;CorpNotSameOriginAfterDefaultedToSameOriginByDip&quot;</span>
    <span class="p">)</span>
    <span class="n">CORP_NOT_SAME_ORIGIN_AFTER_DEFAULTED_TO_SAME_ORIGIN_BY_COEP_AND_DIP</span> <span class="o">=</span> <span class="p">(</span>
        <span class="s2">&quot;CorpNotSameOriginAfterDefaultedToSameOriginByCoepAndDip&quot;</span>
    <span class="p">)</span>
    <span class="n">CORP_NOT_SAME_SITE</span> <span class="o">=</span> <span class="s2">&quot;CorpNotSameSite&quot;</span>
    <span class="n">SRI_MESSAGE_SIGNATURE_MISMATCH</span> <span class="o">=</span> <span class="s2">&quot;SRIMessageSignatureMismatch&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">BlockedByResponseReason</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span></div>



<div class="viewcode-block" id="BlockedByResponseIssueDetails">
<a class="viewcode-back" href="../../../nodriver/cdp/audits.html#nodriver.cdp.audits.BlockedByResponseIssueDetails">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">BlockedByResponseIssueDetails</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Details for a request that has been blocked with the BLOCKED_BY_RESPONSE</span>
<span class="sd">    code. Currently only used for COEP/COOP, but may be extended to include</span>
<span class="sd">    some CSP errors in the future.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">request</span><span class="p">:</span> <span class="n">AffectedRequest</span>

    <span class="n">reason</span><span class="p">:</span> <span class="n">BlockedByResponseReason</span>

    <span class="n">parent_frame</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">AffectedFrame</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="n">blocked_frame</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">AffectedFrame</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;request&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">request</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;reason&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">reason</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">parent_frame</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;parentFrame&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">parent_frame</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">blocked_frame</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;blockedFrame&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">blocked_frame</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">BlockedByResponseIssueDetails</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">request</span><span class="o">=</span><span class="n">AffectedRequest</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;request&quot;</span><span class="p">]),</span>
            <span class="n">reason</span><span class="o">=</span><span class="n">BlockedByResponseReason</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;reason&quot;</span><span class="p">]),</span>
            <span class="n">parent_frame</span><span class="o">=</span><span class="p">(</span>
                <span class="n">AffectedFrame</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;parentFrame&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;parentFrame&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">blocked_frame</span><span class="o">=</span><span class="p">(</span>
                <span class="n">AffectedFrame</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;blockedFrame&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;blockedFrame&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="HeavyAdResolutionStatus">
<a class="viewcode-back" href="../../../nodriver/cdp/audits.html#nodriver.cdp.audits.HeavyAdResolutionStatus">[docs]</a>
<span class="k">class</span> <span class="nc">HeavyAdResolutionStatus</span><span class="p">(</span><span class="n">enum</span><span class="o">.</span><span class="n">Enum</span><span class="p">):</span>
    <span class="n">HEAVY_AD_BLOCKED</span> <span class="o">=</span> <span class="s2">&quot;HeavyAdBlocked&quot;</span>
    <span class="n">HEAVY_AD_WARNING</span> <span class="o">=</span> <span class="s2">&quot;HeavyAdWarning&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">HeavyAdResolutionStatus</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span></div>



<div class="viewcode-block" id="HeavyAdReason">
<a class="viewcode-back" href="../../../nodriver/cdp/audits.html#nodriver.cdp.audits.HeavyAdReason">[docs]</a>
<span class="k">class</span> <span class="nc">HeavyAdReason</span><span class="p">(</span><span class="n">enum</span><span class="o">.</span><span class="n">Enum</span><span class="p">):</span>
    <span class="n">NETWORK_TOTAL_LIMIT</span> <span class="o">=</span> <span class="s2">&quot;NetworkTotalLimit&quot;</span>
    <span class="n">CPU_TOTAL_LIMIT</span> <span class="o">=</span> <span class="s2">&quot;CpuTotalLimit&quot;</span>
    <span class="n">CPU_PEAK_LIMIT</span> <span class="o">=</span> <span class="s2">&quot;CpuPeakLimit&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">HeavyAdReason</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span></div>



<div class="viewcode-block" id="HeavyAdIssueDetails">
<a class="viewcode-back" href="../../../nodriver/cdp/audits.html#nodriver.cdp.audits.HeavyAdIssueDetails">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">HeavyAdIssueDetails</span><span class="p">:</span>
    <span class="c1">#: The resolution status, either blocking the content or warning.</span>
    <span class="n">resolution</span><span class="p">:</span> <span class="n">HeavyAdResolutionStatus</span>

    <span class="c1">#: The reason the ad was blocked, total network or cpu or peak cpu.</span>
    <span class="n">reason</span><span class="p">:</span> <span class="n">HeavyAdReason</span>

    <span class="c1">#: The frame that was blocked.</span>
    <span class="n">frame</span><span class="p">:</span> <span class="n">AffectedFrame</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;resolution&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">resolution</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;reason&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">reason</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;frame&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">frame</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">HeavyAdIssueDetails</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">resolution</span><span class="o">=</span><span class="n">HeavyAdResolutionStatus</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;resolution&quot;</span><span class="p">]),</span>
            <span class="n">reason</span><span class="o">=</span><span class="n">HeavyAdReason</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;reason&quot;</span><span class="p">]),</span>
            <span class="n">frame</span><span class="o">=</span><span class="n">AffectedFrame</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;frame&quot;</span><span class="p">]),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="ContentSecurityPolicyViolationType">
<a class="viewcode-back" href="../../../nodriver/cdp/audits.html#nodriver.cdp.audits.ContentSecurityPolicyViolationType">[docs]</a>
<span class="k">class</span> <span class="nc">ContentSecurityPolicyViolationType</span><span class="p">(</span><span class="n">enum</span><span class="o">.</span><span class="n">Enum</span><span class="p">):</span>
    <span class="n">K_INLINE_VIOLATION</span> <span class="o">=</span> <span class="s2">&quot;kInlineViolation&quot;</span>
    <span class="n">K_EVAL_VIOLATION</span> <span class="o">=</span> <span class="s2">&quot;kEvalViolation&quot;</span>
    <span class="n">K_URL_VIOLATION</span> <span class="o">=</span> <span class="s2">&quot;kURLViolation&quot;</span>
    <span class="n">K_SRI_VIOLATION</span> <span class="o">=</span> <span class="s2">&quot;kSRIViolation&quot;</span>
    <span class="n">K_TRUSTED_TYPES_SINK_VIOLATION</span> <span class="o">=</span> <span class="s2">&quot;kTrustedTypesSinkViolation&quot;</span>
    <span class="n">K_TRUSTED_TYPES_POLICY_VIOLATION</span> <span class="o">=</span> <span class="s2">&quot;kTrustedTypesPolicyViolation&quot;</span>
    <span class="n">K_WASM_EVAL_VIOLATION</span> <span class="o">=</span> <span class="s2">&quot;kWasmEvalViolation&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">ContentSecurityPolicyViolationType</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span></div>



<div class="viewcode-block" id="SourceCodeLocation">
<a class="viewcode-back" href="../../../nodriver/cdp/audits.html#nodriver.cdp.audits.SourceCodeLocation">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">SourceCodeLocation</span><span class="p">:</span>
    <span class="n">url</span><span class="p">:</span> <span class="nb">str</span>

    <span class="n">line_number</span><span class="p">:</span> <span class="nb">int</span>

    <span class="n">column_number</span><span class="p">:</span> <span class="nb">int</span>

    <span class="n">script_id</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">runtime</span><span class="o">.</span><span class="n">ScriptId</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;url&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">url</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;lineNumber&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">line_number</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;columnNumber&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">column_number</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">script_id</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;scriptId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">script_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">SourceCodeLocation</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">url</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;url&quot;</span><span class="p">]),</span>
            <span class="n">line_number</span><span class="o">=</span><span class="nb">int</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;lineNumber&quot;</span><span class="p">]),</span>
            <span class="n">column_number</span><span class="o">=</span><span class="nb">int</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;columnNumber&quot;</span><span class="p">]),</span>
            <span class="n">script_id</span><span class="o">=</span><span class="p">(</span>
                <span class="n">runtime</span><span class="o">.</span><span class="n">ScriptId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;scriptId&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;scriptId&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="ContentSecurityPolicyIssueDetails">
<a class="viewcode-back" href="../../../nodriver/cdp/audits.html#nodriver.cdp.audits.ContentSecurityPolicyIssueDetails">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">ContentSecurityPolicyIssueDetails</span><span class="p">:</span>
    <span class="c1">#: Specific directive that is violated, causing the CSP issue.</span>
    <span class="n">violated_directive</span><span class="p">:</span> <span class="nb">str</span>

    <span class="n">is_report_only</span><span class="p">:</span> <span class="nb">bool</span>

    <span class="n">content_security_policy_violation_type</span><span class="p">:</span> <span class="n">ContentSecurityPolicyViolationType</span>

    <span class="c1">#: The url not included in allowed sources.</span>
    <span class="n">blocked_url</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="n">frame_ancestor</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">AffectedFrame</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="n">source_code_location</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">SourceCodeLocation</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="n">violating_node_id</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">dom</span><span class="o">.</span><span class="n">BackendNodeId</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;violatedDirective&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">violated_directive</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;isReportOnly&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">is_report_only</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;contentSecurityPolicyViolationType&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">(</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">content_security_policy_violation_type</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="p">)</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">blocked_url</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;blockedURL&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">blocked_url</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">frame_ancestor</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;frameAncestor&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">frame_ancestor</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">source_code_location</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;sourceCodeLocation&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">source_code_location</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">violating_node_id</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;violatingNodeId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">violating_node_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">ContentSecurityPolicyIssueDetails</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">violated_directive</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;violatedDirective&quot;</span><span class="p">]),</span>
            <span class="n">is_report_only</span><span class="o">=</span><span class="nb">bool</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;isReportOnly&quot;</span><span class="p">]),</span>
            <span class="n">content_security_policy_violation_type</span><span class="o">=</span><span class="n">ContentSecurityPolicyViolationType</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span>
                <span class="n">json</span><span class="p">[</span><span class="s2">&quot;contentSecurityPolicyViolationType&quot;</span><span class="p">]</span>
            <span class="p">),</span>
            <span class="n">blocked_url</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;blockedURL&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;blockedURL&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">frame_ancestor</span><span class="o">=</span><span class="p">(</span>
                <span class="n">AffectedFrame</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;frameAncestor&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;frameAncestor&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">source_code_location</span><span class="o">=</span><span class="p">(</span>
                <span class="n">SourceCodeLocation</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;sourceCodeLocation&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;sourceCodeLocation&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">violating_node_id</span><span class="o">=</span><span class="p">(</span>
                <span class="n">dom</span><span class="o">.</span><span class="n">BackendNodeId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;violatingNodeId&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;violatingNodeId&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="SharedArrayBufferIssueType">
<a class="viewcode-back" href="../../../nodriver/cdp/audits.html#nodriver.cdp.audits.SharedArrayBufferIssueType">[docs]</a>
<span class="k">class</span> <span class="nc">SharedArrayBufferIssueType</span><span class="p">(</span><span class="n">enum</span><span class="o">.</span><span class="n">Enum</span><span class="p">):</span>
    <span class="n">TRANSFER_ISSUE</span> <span class="o">=</span> <span class="s2">&quot;TransferIssue&quot;</span>
    <span class="n">CREATION_ISSUE</span> <span class="o">=</span> <span class="s2">&quot;CreationIssue&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">SharedArrayBufferIssueType</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span></div>



<div class="viewcode-block" id="SharedArrayBufferIssueDetails">
<a class="viewcode-back" href="../../../nodriver/cdp/audits.html#nodriver.cdp.audits.SharedArrayBufferIssueDetails">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">SharedArrayBufferIssueDetails</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Details for a issue arising from an SAB being instantiated in, or</span>
<span class="sd">    transferred to a context that is not cross-origin isolated.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">source_code_location</span><span class="p">:</span> <span class="n">SourceCodeLocation</span>

    <span class="n">is_warning</span><span class="p">:</span> <span class="nb">bool</span>

    <span class="n">type_</span><span class="p">:</span> <span class="n">SharedArrayBufferIssueType</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;sourceCodeLocation&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">source_code_location</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;isWarning&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">is_warning</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;type&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">type_</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">SharedArrayBufferIssueDetails</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">source_code_location</span><span class="o">=</span><span class="n">SourceCodeLocation</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span>
                <span class="n">json</span><span class="p">[</span><span class="s2">&quot;sourceCodeLocation&quot;</span><span class="p">]</span>
            <span class="p">),</span>
            <span class="n">is_warning</span><span class="o">=</span><span class="nb">bool</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;isWarning&quot;</span><span class="p">]),</span>
            <span class="n">type_</span><span class="o">=</span><span class="n">SharedArrayBufferIssueType</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;type&quot;</span><span class="p">]),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="LowTextContrastIssueDetails">
<a class="viewcode-back" href="../../../nodriver/cdp/audits.html#nodriver.cdp.audits.LowTextContrastIssueDetails">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">LowTextContrastIssueDetails</span><span class="p">:</span>
    <span class="n">violating_node_id</span><span class="p">:</span> <span class="n">dom</span><span class="o">.</span><span class="n">BackendNodeId</span>

    <span class="n">violating_node_selector</span><span class="p">:</span> <span class="nb">str</span>

    <span class="n">contrast_ratio</span><span class="p">:</span> <span class="nb">float</span>

    <span class="n">threshold_aa</span><span class="p">:</span> <span class="nb">float</span>

    <span class="n">threshold_aaa</span><span class="p">:</span> <span class="nb">float</span>

    <span class="n">font_size</span><span class="p">:</span> <span class="nb">str</span>

    <span class="n">font_weight</span><span class="p">:</span> <span class="nb">str</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;violatingNodeId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">violating_node_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;violatingNodeSelector&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">violating_node_selector</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;contrastRatio&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">contrast_ratio</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;thresholdAA&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">threshold_aa</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;thresholdAAA&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">threshold_aaa</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;fontSize&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">font_size</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;fontWeight&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">font_weight</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">LowTextContrastIssueDetails</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">violating_node_id</span><span class="o">=</span><span class="n">dom</span><span class="o">.</span><span class="n">BackendNodeId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;violatingNodeId&quot;</span><span class="p">]),</span>
            <span class="n">violating_node_selector</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;violatingNodeSelector&quot;</span><span class="p">]),</span>
            <span class="n">contrast_ratio</span><span class="o">=</span><span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;contrastRatio&quot;</span><span class="p">]),</span>
            <span class="n">threshold_aa</span><span class="o">=</span><span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;thresholdAA&quot;</span><span class="p">]),</span>
            <span class="n">threshold_aaa</span><span class="o">=</span><span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;thresholdAAA&quot;</span><span class="p">]),</span>
            <span class="n">font_size</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;fontSize&quot;</span><span class="p">]),</span>
            <span class="n">font_weight</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;fontWeight&quot;</span><span class="p">]),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="CorsIssueDetails">
<a class="viewcode-back" href="../../../nodriver/cdp/audits.html#nodriver.cdp.audits.CorsIssueDetails">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">CorsIssueDetails</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Details for a CORS related issue, e.g. a warning or error related to</span>
<span class="sd">    CORS RFC1918 enforcement.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">cors_error_status</span><span class="p">:</span> <span class="n">network</span><span class="o">.</span><span class="n">CorsErrorStatus</span>

    <span class="n">is_warning</span><span class="p">:</span> <span class="nb">bool</span>

    <span class="n">request</span><span class="p">:</span> <span class="n">AffectedRequest</span>

    <span class="n">location</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">SourceCodeLocation</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="n">initiator_origin</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="n">resource_ip_address_space</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">network</span><span class="o">.</span><span class="n">IPAddressSpace</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="n">client_security_state</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">network</span><span class="o">.</span><span class="n">ClientSecurityState</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;corsErrorStatus&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">cors_error_status</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;isWarning&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">is_warning</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;request&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">request</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">location</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;location&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">location</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">initiator_origin</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;initiatorOrigin&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">initiator_origin</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">resource_ip_address_space</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;resourceIPAddressSpace&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">resource_ip_address_space</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">client_security_state</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;clientSecurityState&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">client_security_state</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">CorsIssueDetails</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">cors_error_status</span><span class="o">=</span><span class="n">network</span><span class="o">.</span><span class="n">CorsErrorStatus</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span>
                <span class="n">json</span><span class="p">[</span><span class="s2">&quot;corsErrorStatus&quot;</span><span class="p">]</span>
            <span class="p">),</span>
            <span class="n">is_warning</span><span class="o">=</span><span class="nb">bool</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;isWarning&quot;</span><span class="p">]),</span>
            <span class="n">request</span><span class="o">=</span><span class="n">AffectedRequest</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;request&quot;</span><span class="p">]),</span>
            <span class="n">location</span><span class="o">=</span><span class="p">(</span>
                <span class="n">SourceCodeLocation</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;location&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;location&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">initiator_origin</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;initiatorOrigin&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;initiatorOrigin&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">resource_ip_address_space</span><span class="o">=</span><span class="p">(</span>
                <span class="n">network</span><span class="o">.</span><span class="n">IPAddressSpace</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;resourceIPAddressSpace&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;resourceIPAddressSpace&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">client_security_state</span><span class="o">=</span><span class="p">(</span>
                <span class="n">network</span><span class="o">.</span><span class="n">ClientSecurityState</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;clientSecurityState&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;clientSecurityState&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="AttributionReportingIssueType">
<a class="viewcode-back" href="../../../nodriver/cdp/audits.html#nodriver.cdp.audits.AttributionReportingIssueType">[docs]</a>
<span class="k">class</span> <span class="nc">AttributionReportingIssueType</span><span class="p">(</span><span class="n">enum</span><span class="o">.</span><span class="n">Enum</span><span class="p">):</span>
    <span class="n">PERMISSION_POLICY_DISABLED</span> <span class="o">=</span> <span class="s2">&quot;PermissionPolicyDisabled&quot;</span>
    <span class="n">UNTRUSTWORTHY_REPORTING_ORIGIN</span> <span class="o">=</span> <span class="s2">&quot;UntrustworthyReportingOrigin&quot;</span>
    <span class="n">INSECURE_CONTEXT</span> <span class="o">=</span> <span class="s2">&quot;InsecureContext&quot;</span>
    <span class="n">INVALID_HEADER</span> <span class="o">=</span> <span class="s2">&quot;InvalidHeader&quot;</span>
    <span class="n">INVALID_REGISTER_TRIGGER_HEADER</span> <span class="o">=</span> <span class="s2">&quot;InvalidRegisterTriggerHeader&quot;</span>
    <span class="n">SOURCE_AND_TRIGGER_HEADERS</span> <span class="o">=</span> <span class="s2">&quot;SourceAndTriggerHeaders&quot;</span>
    <span class="n">SOURCE_IGNORED</span> <span class="o">=</span> <span class="s2">&quot;SourceIgnored&quot;</span>
    <span class="n">TRIGGER_IGNORED</span> <span class="o">=</span> <span class="s2">&quot;TriggerIgnored&quot;</span>
    <span class="n">OS_SOURCE_IGNORED</span> <span class="o">=</span> <span class="s2">&quot;OsSourceIgnored&quot;</span>
    <span class="n">OS_TRIGGER_IGNORED</span> <span class="o">=</span> <span class="s2">&quot;OsTriggerIgnored&quot;</span>
    <span class="n">INVALID_REGISTER_OS_SOURCE_HEADER</span> <span class="o">=</span> <span class="s2">&quot;InvalidRegisterOsSourceHeader&quot;</span>
    <span class="n">INVALID_REGISTER_OS_TRIGGER_HEADER</span> <span class="o">=</span> <span class="s2">&quot;InvalidRegisterOsTriggerHeader&quot;</span>
    <span class="n">WEB_AND_OS_HEADERS</span> <span class="o">=</span> <span class="s2">&quot;WebAndOsHeaders&quot;</span>
    <span class="n">NO_WEB_OR_OS_SUPPORT</span> <span class="o">=</span> <span class="s2">&quot;NoWebOrOsSupport&quot;</span>
    <span class="n">NAVIGATION_REGISTRATION_WITHOUT_TRANSIENT_USER_ACTIVATION</span> <span class="o">=</span> <span class="p">(</span>
        <span class="s2">&quot;NavigationRegistrationWithoutTransientUserActivation&quot;</span>
    <span class="p">)</span>
    <span class="n">INVALID_INFO_HEADER</span> <span class="o">=</span> <span class="s2">&quot;InvalidInfoHeader&quot;</span>
    <span class="n">NO_REGISTER_SOURCE_HEADER</span> <span class="o">=</span> <span class="s2">&quot;NoRegisterSourceHeader&quot;</span>
    <span class="n">NO_REGISTER_TRIGGER_HEADER</span> <span class="o">=</span> <span class="s2">&quot;NoRegisterTriggerHeader&quot;</span>
    <span class="n">NO_REGISTER_OS_SOURCE_HEADER</span> <span class="o">=</span> <span class="s2">&quot;NoRegisterOsSourceHeader&quot;</span>
    <span class="n">NO_REGISTER_OS_TRIGGER_HEADER</span> <span class="o">=</span> <span class="s2">&quot;NoRegisterOsTriggerHeader&quot;</span>
    <span class="n">NAVIGATION_REGISTRATION_UNIQUE_SCOPE_ALREADY_SET</span> <span class="o">=</span> <span class="p">(</span>
        <span class="s2">&quot;NavigationRegistrationUniqueScopeAlreadySet&quot;</span>
    <span class="p">)</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">AttributionReportingIssueType</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span></div>



<div class="viewcode-block" id="SharedDictionaryError">
<a class="viewcode-back" href="../../../nodriver/cdp/audits.html#nodriver.cdp.audits.SharedDictionaryError">[docs]</a>
<span class="k">class</span> <span class="nc">SharedDictionaryError</span><span class="p">(</span><span class="n">enum</span><span class="o">.</span><span class="n">Enum</span><span class="p">):</span>
    <span class="n">USE_ERROR_CROSS_ORIGIN_NO_CORS_REQUEST</span> <span class="o">=</span> <span class="s2">&quot;UseErrorCrossOriginNoCorsRequest&quot;</span>
    <span class="n">USE_ERROR_DICTIONARY_LOAD_FAILURE</span> <span class="o">=</span> <span class="s2">&quot;UseErrorDictionaryLoadFailure&quot;</span>
    <span class="n">USE_ERROR_MATCHING_DICTIONARY_NOT_USED</span> <span class="o">=</span> <span class="s2">&quot;UseErrorMatchingDictionaryNotUsed&quot;</span>
    <span class="n">USE_ERROR_UNEXPECTED_CONTENT_DICTIONARY_HEADER</span> <span class="o">=</span> <span class="p">(</span>
        <span class="s2">&quot;UseErrorUnexpectedContentDictionaryHeader&quot;</span>
    <span class="p">)</span>
    <span class="n">WRITE_ERROR_COSS_ORIGIN_NO_CORS_REQUEST</span> <span class="o">=</span> <span class="s2">&quot;WriteErrorCossOriginNoCorsRequest&quot;</span>
    <span class="n">WRITE_ERROR_DISALLOWED_BY_SETTINGS</span> <span class="o">=</span> <span class="s2">&quot;WriteErrorDisallowedBySettings&quot;</span>
    <span class="n">WRITE_ERROR_EXPIRED_RESPONSE</span> <span class="o">=</span> <span class="s2">&quot;WriteErrorExpiredResponse&quot;</span>
    <span class="n">WRITE_ERROR_FEATURE_DISABLED</span> <span class="o">=</span> <span class="s2">&quot;WriteErrorFeatureDisabled&quot;</span>
    <span class="n">WRITE_ERROR_INSUFFICIENT_RESOURCES</span> <span class="o">=</span> <span class="s2">&quot;WriteErrorInsufficientResources&quot;</span>
    <span class="n">WRITE_ERROR_INVALID_MATCH_FIELD</span> <span class="o">=</span> <span class="s2">&quot;WriteErrorInvalidMatchField&quot;</span>
    <span class="n">WRITE_ERROR_INVALID_STRUCTURED_HEADER</span> <span class="o">=</span> <span class="s2">&quot;WriteErrorInvalidStructuredHeader&quot;</span>
    <span class="n">WRITE_ERROR_NAVIGATION_REQUEST</span> <span class="o">=</span> <span class="s2">&quot;WriteErrorNavigationRequest&quot;</span>
    <span class="n">WRITE_ERROR_NO_MATCH_FIELD</span> <span class="o">=</span> <span class="s2">&quot;WriteErrorNoMatchField&quot;</span>
    <span class="n">WRITE_ERROR_NON_LIST_MATCH_DEST_FIELD</span> <span class="o">=</span> <span class="s2">&quot;WriteErrorNonListMatchDestField&quot;</span>
    <span class="n">WRITE_ERROR_NON_SECURE_CONTEXT</span> <span class="o">=</span> <span class="s2">&quot;WriteErrorNonSecureContext&quot;</span>
    <span class="n">WRITE_ERROR_NON_STRING_ID_FIELD</span> <span class="o">=</span> <span class="s2">&quot;WriteErrorNonStringIdField&quot;</span>
    <span class="n">WRITE_ERROR_NON_STRING_IN_MATCH_DEST_LIST</span> <span class="o">=</span> <span class="s2">&quot;WriteErrorNonStringInMatchDestList&quot;</span>
    <span class="n">WRITE_ERROR_NON_STRING_MATCH_FIELD</span> <span class="o">=</span> <span class="s2">&quot;WriteErrorNonStringMatchField&quot;</span>
    <span class="n">WRITE_ERROR_NON_TOKEN_TYPE_FIELD</span> <span class="o">=</span> <span class="s2">&quot;WriteErrorNonTokenTypeField&quot;</span>
    <span class="n">WRITE_ERROR_REQUEST_ABORTED</span> <span class="o">=</span> <span class="s2">&quot;WriteErrorRequestAborted&quot;</span>
    <span class="n">WRITE_ERROR_SHUTTING_DOWN</span> <span class="o">=</span> <span class="s2">&quot;WriteErrorShuttingDown&quot;</span>
    <span class="n">WRITE_ERROR_TOO_LONG_ID_FIELD</span> <span class="o">=</span> <span class="s2">&quot;WriteErrorTooLongIdField&quot;</span>
    <span class="n">WRITE_ERROR_UNSUPPORTED_TYPE</span> <span class="o">=</span> <span class="s2">&quot;WriteErrorUnsupportedType&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">SharedDictionaryError</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span></div>



<div class="viewcode-block" id="SRIMessageSignatureError">
<a class="viewcode-back" href="../../../nodriver/cdp/audits.html#nodriver.cdp.audits.SRIMessageSignatureError">[docs]</a>
<span class="k">class</span> <span class="nc">SRIMessageSignatureError</span><span class="p">(</span><span class="n">enum</span><span class="o">.</span><span class="n">Enum</span><span class="p">):</span>
    <span class="n">MISSING_SIGNATURE_HEADER</span> <span class="o">=</span> <span class="s2">&quot;MissingSignatureHeader&quot;</span>
    <span class="n">MISSING_SIGNATURE_INPUT_HEADER</span> <span class="o">=</span> <span class="s2">&quot;MissingSignatureInputHeader&quot;</span>
    <span class="n">INVALID_SIGNATURE_HEADER</span> <span class="o">=</span> <span class="s2">&quot;InvalidSignatureHeader&quot;</span>
    <span class="n">INVALID_SIGNATURE_INPUT_HEADER</span> <span class="o">=</span> <span class="s2">&quot;InvalidSignatureInputHeader&quot;</span>
    <span class="n">SIGNATURE_HEADER_VALUE_IS_NOT_BYTE_SEQUENCE</span> <span class="o">=</span> <span class="p">(</span>
        <span class="s2">&quot;SignatureHeaderValueIsNotByteSequence&quot;</span>
    <span class="p">)</span>
    <span class="n">SIGNATURE_HEADER_VALUE_IS_PARAMETERIZED</span> <span class="o">=</span> <span class="s2">&quot;SignatureHeaderValueIsParameterized&quot;</span>
    <span class="n">SIGNATURE_HEADER_VALUE_IS_INCORRECT_LENGTH</span> <span class="o">=</span> <span class="s2">&quot;SignatureHeaderValueIsIncorrectLength&quot;</span>
    <span class="n">SIGNATURE_INPUT_HEADER_MISSING_LABEL</span> <span class="o">=</span> <span class="s2">&quot;SignatureInputHeaderMissingLabel&quot;</span>
    <span class="n">SIGNATURE_INPUT_HEADER_VALUE_NOT_INNER_LIST</span> <span class="o">=</span> <span class="p">(</span>
        <span class="s2">&quot;SignatureInputHeaderValueNotInnerList&quot;</span>
    <span class="p">)</span>
    <span class="n">SIGNATURE_INPUT_HEADER_VALUE_MISSING_COMPONENTS</span> <span class="o">=</span> <span class="p">(</span>
        <span class="s2">&quot;SignatureInputHeaderValueMissingComponents&quot;</span>
    <span class="p">)</span>
    <span class="n">SIGNATURE_INPUT_HEADER_INVALID_COMPONENT_TYPE</span> <span class="o">=</span> <span class="p">(</span>
        <span class="s2">&quot;SignatureInputHeaderInvalidComponentType&quot;</span>
    <span class="p">)</span>
    <span class="n">SIGNATURE_INPUT_HEADER_INVALID_COMPONENT_NAME</span> <span class="o">=</span> <span class="p">(</span>
        <span class="s2">&quot;SignatureInputHeaderInvalidComponentName&quot;</span>
    <span class="p">)</span>
    <span class="n">SIGNATURE_INPUT_HEADER_INVALID_HEADER_COMPONENT_PARAMETER</span> <span class="o">=</span> <span class="p">(</span>
        <span class="s2">&quot;SignatureInputHeaderInvalidHeaderComponentParameter&quot;</span>
    <span class="p">)</span>
    <span class="n">SIGNATURE_INPUT_HEADER_INVALID_DERIVED_COMPONENT_PARAMETER</span> <span class="o">=</span> <span class="p">(</span>
        <span class="s2">&quot;SignatureInputHeaderInvalidDerivedComponentParameter&quot;</span>
    <span class="p">)</span>
    <span class="n">SIGNATURE_INPUT_HEADER_KEY_ID_LENGTH</span> <span class="o">=</span> <span class="s2">&quot;SignatureInputHeaderKeyIdLength&quot;</span>
    <span class="n">SIGNATURE_INPUT_HEADER_INVALID_PARAMETER</span> <span class="o">=</span> <span class="s2">&quot;SignatureInputHeaderInvalidParameter&quot;</span>
    <span class="n">SIGNATURE_INPUT_HEADER_MISSING_REQUIRED_PARAMETERS</span> <span class="o">=</span> <span class="p">(</span>
        <span class="s2">&quot;SignatureInputHeaderMissingRequiredParameters&quot;</span>
    <span class="p">)</span>
    <span class="n">VALIDATION_FAILED_SIGNATURE_EXPIRED</span> <span class="o">=</span> <span class="s2">&quot;ValidationFailedSignatureExpired&quot;</span>
    <span class="n">VALIDATION_FAILED_INVALID_LENGTH</span> <span class="o">=</span> <span class="s2">&quot;ValidationFailedInvalidLength&quot;</span>
    <span class="n">VALIDATION_FAILED_SIGNATURE_MISMATCH</span> <span class="o">=</span> <span class="s2">&quot;ValidationFailedSignatureMismatch&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">SRIMessageSignatureError</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span></div>



<div class="viewcode-block" id="AttributionReportingIssueDetails">
<a class="viewcode-back" href="../../../nodriver/cdp/audits.html#nodriver.cdp.audits.AttributionReportingIssueDetails">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">AttributionReportingIssueDetails</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Details for issues around &quot;Attribution Reporting API&quot; usage.</span>
<span class="sd">    Explainer: https://github.com/WICG/attribution-reporting-api</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">violation_type</span><span class="p">:</span> <span class="n">AttributionReportingIssueType</span>

    <span class="n">request</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">AffectedRequest</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="n">violating_node_id</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">dom</span><span class="o">.</span><span class="n">BackendNodeId</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="n">invalid_parameter</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;violationType&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">violation_type</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">request</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;request&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">request</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">violating_node_id</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;violatingNodeId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">violating_node_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">invalid_parameter</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;invalidParameter&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">invalid_parameter</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">AttributionReportingIssueDetails</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">violation_type</span><span class="o">=</span><span class="n">AttributionReportingIssueType</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span>
                <span class="n">json</span><span class="p">[</span><span class="s2">&quot;violationType&quot;</span><span class="p">]</span>
            <span class="p">),</span>
            <span class="n">request</span><span class="o">=</span><span class="p">(</span>
                <span class="n">AffectedRequest</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;request&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;request&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">violating_node_id</span><span class="o">=</span><span class="p">(</span>
                <span class="n">dom</span><span class="o">.</span><span class="n">BackendNodeId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;violatingNodeId&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;violatingNodeId&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">invalid_parameter</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;invalidParameter&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;invalidParameter&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="QuirksModeIssueDetails">
<a class="viewcode-back" href="../../../nodriver/cdp/audits.html#nodriver.cdp.audits.QuirksModeIssueDetails">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">QuirksModeIssueDetails</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Details for issues about documents in Quirks Mode</span>
<span class="sd">    or Limited Quirks Mode that affects page layouting.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: If false, it means the document&#39;s mode is &quot;quirks&quot;</span>
    <span class="c1">#: instead of &quot;limited-quirks&quot;.</span>
    <span class="n">is_limited_quirks_mode</span><span class="p">:</span> <span class="nb">bool</span>

    <span class="n">document_node_id</span><span class="p">:</span> <span class="n">dom</span><span class="o">.</span><span class="n">BackendNodeId</span>

    <span class="n">url</span><span class="p">:</span> <span class="nb">str</span>

    <span class="n">frame_id</span><span class="p">:</span> <span class="n">page</span><span class="o">.</span><span class="n">FrameId</span>

    <span class="n">loader_id</span><span class="p">:</span> <span class="n">network</span><span class="o">.</span><span class="n">LoaderId</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;isLimitedQuirksMode&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">is_limited_quirks_mode</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;documentNodeId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">document_node_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;url&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">url</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;frameId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">frame_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;loaderId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">loader_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">QuirksModeIssueDetails</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">is_limited_quirks_mode</span><span class="o">=</span><span class="nb">bool</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;isLimitedQuirksMode&quot;</span><span class="p">]),</span>
            <span class="n">document_node_id</span><span class="o">=</span><span class="n">dom</span><span class="o">.</span><span class="n">BackendNodeId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;documentNodeId&quot;</span><span class="p">]),</span>
            <span class="n">url</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;url&quot;</span><span class="p">]),</span>
            <span class="n">frame_id</span><span class="o">=</span><span class="n">page</span><span class="o">.</span><span class="n">FrameId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;frameId&quot;</span><span class="p">]),</span>
            <span class="n">loader_id</span><span class="o">=</span><span class="n">network</span><span class="o">.</span><span class="n">LoaderId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;loaderId&quot;</span><span class="p">]),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="NavigatorUserAgentIssueDetails">
<a class="viewcode-back" href="../../../nodriver/cdp/audits.html#nodriver.cdp.audits.NavigatorUserAgentIssueDetails">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">NavigatorUserAgentIssueDetails</span><span class="p">:</span>
    <span class="n">url</span><span class="p">:</span> <span class="nb">str</span>

    <span class="n">location</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">SourceCodeLocation</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;url&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">url</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">location</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;location&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">location</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">NavigatorUserAgentIssueDetails</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">url</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;url&quot;</span><span class="p">]),</span>
            <span class="n">location</span><span class="o">=</span><span class="p">(</span>
                <span class="n">SourceCodeLocation</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;location&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;location&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="SharedDictionaryIssueDetails">
<a class="viewcode-back" href="../../../nodriver/cdp/audits.html#nodriver.cdp.audits.SharedDictionaryIssueDetails">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">SharedDictionaryIssueDetails</span><span class="p">:</span>
    <span class="n">shared_dictionary_error</span><span class="p">:</span> <span class="n">SharedDictionaryError</span>

    <span class="n">request</span><span class="p">:</span> <span class="n">AffectedRequest</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;sharedDictionaryError&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">shared_dictionary_error</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;request&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">request</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">SharedDictionaryIssueDetails</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">shared_dictionary_error</span><span class="o">=</span><span class="n">SharedDictionaryError</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span>
                <span class="n">json</span><span class="p">[</span><span class="s2">&quot;sharedDictionaryError&quot;</span><span class="p">]</span>
            <span class="p">),</span>
            <span class="n">request</span><span class="o">=</span><span class="n">AffectedRequest</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;request&quot;</span><span class="p">]),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="SRIMessageSignatureIssueDetails">
<a class="viewcode-back" href="../../../nodriver/cdp/audits.html#nodriver.cdp.audits.SRIMessageSignatureIssueDetails">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">SRIMessageSignatureIssueDetails</span><span class="p">:</span>
    <span class="n">error</span><span class="p">:</span> <span class="n">SRIMessageSignatureError</span>

    <span class="n">signature_base</span><span class="p">:</span> <span class="nb">str</span>

    <span class="n">request</span><span class="p">:</span> <span class="n">AffectedRequest</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;error&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">error</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;signatureBase&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">signature_base</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;request&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">request</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">SRIMessageSignatureIssueDetails</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">error</span><span class="o">=</span><span class="n">SRIMessageSignatureError</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;error&quot;</span><span class="p">]),</span>
            <span class="n">signature_base</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;signatureBase&quot;</span><span class="p">]),</span>
            <span class="n">request</span><span class="o">=</span><span class="n">AffectedRequest</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;request&quot;</span><span class="p">]),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="GenericIssueErrorType">
<a class="viewcode-back" href="../../../nodriver/cdp/audits.html#nodriver.cdp.audits.GenericIssueErrorType">[docs]</a>
<span class="k">class</span> <span class="nc">GenericIssueErrorType</span><span class="p">(</span><span class="n">enum</span><span class="o">.</span><span class="n">Enum</span><span class="p">):</span>
    <span class="n">FORM_LABEL_FOR_NAME_ERROR</span> <span class="o">=</span> <span class="s2">&quot;FormLabelForNameError&quot;</span>
    <span class="n">FORM_DUPLICATE_ID_FOR_INPUT_ERROR</span> <span class="o">=</span> <span class="s2">&quot;FormDuplicateIdForInputError&quot;</span>
    <span class="n">FORM_INPUT_WITH_NO_LABEL_ERROR</span> <span class="o">=</span> <span class="s2">&quot;FormInputWithNoLabelError&quot;</span>
    <span class="n">FORM_AUTOCOMPLETE_ATTRIBUTE_EMPTY_ERROR</span> <span class="o">=</span> <span class="s2">&quot;FormAutocompleteAttributeEmptyError&quot;</span>
    <span class="n">FORM_EMPTY_ID_AND_NAME_ATTRIBUTES_FOR_INPUT_ERROR</span> <span class="o">=</span> <span class="p">(</span>
        <span class="s2">&quot;FormEmptyIdAndNameAttributesForInputError&quot;</span>
    <span class="p">)</span>
    <span class="n">FORM_ARIA_LABELLED_BY_TO_NON_EXISTING_ID</span> <span class="o">=</span> <span class="s2">&quot;FormAriaLabelledByToNonExistingId&quot;</span>
    <span class="n">FORM_INPUT_ASSIGNED_AUTOCOMPLETE_VALUE_TO_ID_OR_NAME_ATTRIBUTE_ERROR</span> <span class="o">=</span> <span class="p">(</span>
        <span class="s2">&quot;FormInputAssignedAutocompleteValueToIdOrNameAttributeError&quot;</span>
    <span class="p">)</span>
    <span class="n">FORM_LABEL_HAS_NEITHER_FOR_NOR_NESTED_INPUT</span> <span class="o">=</span> <span class="s2">&quot;FormLabelHasNeitherForNorNestedInput&quot;</span>
    <span class="n">FORM_LABEL_FOR_MATCHES_NON_EXISTING_ID_ERROR</span> <span class="o">=</span> <span class="p">(</span>
        <span class="s2">&quot;FormLabelForMatchesNonExistingIdError&quot;</span>
    <span class="p">)</span>
    <span class="n">FORM_INPUT_HAS_WRONG_BUT_WELL_INTENDED_AUTOCOMPLETE_VALUE_ERROR</span> <span class="o">=</span> <span class="p">(</span>
        <span class="s2">&quot;FormInputHasWrongButWellIntendedAutocompleteValueError&quot;</span>
    <span class="p">)</span>
    <span class="n">RESPONSE_WAS_BLOCKED_BY_ORB</span> <span class="o">=</span> <span class="s2">&quot;ResponseWasBlockedByORB&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">GenericIssueErrorType</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span></div>



<div class="viewcode-block" id="GenericIssueDetails">
<a class="viewcode-back" href="../../../nodriver/cdp/audits.html#nodriver.cdp.audits.GenericIssueDetails">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">GenericIssueDetails</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Depending on the concrete errorType, different properties are set.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: Issues with the same errorType are aggregated in the frontend.</span>
    <span class="n">error_type</span><span class="p">:</span> <span class="n">GenericIssueErrorType</span>

    <span class="n">frame_id</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">page</span><span class="o">.</span><span class="n">FrameId</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="n">violating_node_id</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">dom</span><span class="o">.</span><span class="n">BackendNodeId</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="n">violating_node_attribute</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="n">request</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">AffectedRequest</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;errorType&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">error_type</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">frame_id</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;frameId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">frame_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">violating_node_id</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;violatingNodeId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">violating_node_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">violating_node_attribute</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;violatingNodeAttribute&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">violating_node_attribute</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">request</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;request&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">request</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">GenericIssueDetails</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">error_type</span><span class="o">=</span><span class="n">GenericIssueErrorType</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;errorType&quot;</span><span class="p">]),</span>
            <span class="n">frame_id</span><span class="o">=</span><span class="p">(</span>
                <span class="n">page</span><span class="o">.</span><span class="n">FrameId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;frameId&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;frameId&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">violating_node_id</span><span class="o">=</span><span class="p">(</span>
                <span class="n">dom</span><span class="o">.</span><span class="n">BackendNodeId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;violatingNodeId&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;violatingNodeId&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">violating_node_attribute</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;violatingNodeAttribute&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;violatingNodeAttribute&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">request</span><span class="o">=</span><span class="p">(</span>
                <span class="n">AffectedRequest</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;request&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;request&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="DeprecationIssueDetails">
<a class="viewcode-back" href="../../../nodriver/cdp/audits.html#nodriver.cdp.audits.DeprecationIssueDetails">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">DeprecationIssueDetails</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    This issue tracks information needed to print a deprecation message.</span>
<span class="sd">    https://source.chromium.org/chromium/chromium/src/+/main:third_party/blink/renderer/core/frame/third_party/blink/renderer/core/frame/deprecation/README.md</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">source_code_location</span><span class="p">:</span> <span class="n">SourceCodeLocation</span>

    <span class="c1">#: One of the deprecation names from third_party/blink/renderer/core/frame/deprecation/deprecation.json5</span>
    <span class="n">type_</span><span class="p">:</span> <span class="nb">str</span>

    <span class="n">affected_frame</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">AffectedFrame</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;sourceCodeLocation&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">source_code_location</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;type&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">type_</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">affected_frame</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;affectedFrame&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">affected_frame</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">DeprecationIssueDetails</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">source_code_location</span><span class="o">=</span><span class="n">SourceCodeLocation</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span>
                <span class="n">json</span><span class="p">[</span><span class="s2">&quot;sourceCodeLocation&quot;</span><span class="p">]</span>
            <span class="p">),</span>
            <span class="n">type_</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;type&quot;</span><span class="p">]),</span>
            <span class="n">affected_frame</span><span class="o">=</span><span class="p">(</span>
                <span class="n">AffectedFrame</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;affectedFrame&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;affectedFrame&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="BounceTrackingIssueDetails">
<a class="viewcode-back" href="../../../nodriver/cdp/audits.html#nodriver.cdp.audits.BounceTrackingIssueDetails">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">BounceTrackingIssueDetails</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    This issue warns about sites in the redirect chain of a finished navigation</span>
<span class="sd">    that may be flagged as trackers and have their state cleared if they don&#39;t</span>
<span class="sd">    receive a user interaction. Note that in this context &#39;site&#39; means eTLD+1.</span>
<span class="sd">    For example, if the URL ``https://example.test:80/bounce`` was in the</span>
<span class="sd">    redirect chain, the site reported would be ``example.test``.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">tracking_sites</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;trackingSites&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">tracking_sites</span><span class="p">]</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">BounceTrackingIssueDetails</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">tracking_sites</span><span class="o">=</span><span class="p">[</span><span class="nb">str</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;trackingSites&quot;</span><span class="p">]],</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="CookieDeprecationMetadataIssueDetails">
<a class="viewcode-back" href="../../../nodriver/cdp/audits.html#nodriver.cdp.audits.CookieDeprecationMetadataIssueDetails">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">CookieDeprecationMetadataIssueDetails</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    This issue warns about third-party sites that are accessing cookies on the</span>
<span class="sd">    current page, and have been permitted due to having a global metadata grant.</span>
<span class="sd">    Note that in this context &#39;site&#39; means eTLD+1. For example, if the URL</span>
<span class="sd">    ``https://example.test:80/web_page`` was accessing cookies, the site reported</span>
<span class="sd">    would be ``example.test``.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">allowed_sites</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span>

    <span class="n">opt_out_percentage</span><span class="p">:</span> <span class="nb">float</span>

    <span class="n">is_opt_out_top_level</span><span class="p">:</span> <span class="nb">bool</span>

    <span class="n">operation</span><span class="p">:</span> <span class="n">CookieOperation</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;allowedSites&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">allowed_sites</span><span class="p">]</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;optOutPercentage&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">opt_out_percentage</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;isOptOutTopLevel&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">is_opt_out_top_level</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;operation&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">operation</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">CookieDeprecationMetadataIssueDetails</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">allowed_sites</span><span class="o">=</span><span class="p">[</span><span class="nb">str</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;allowedSites&quot;</span><span class="p">]],</span>
            <span class="n">opt_out_percentage</span><span class="o">=</span><span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;optOutPercentage&quot;</span><span class="p">]),</span>
            <span class="n">is_opt_out_top_level</span><span class="o">=</span><span class="nb">bool</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;isOptOutTopLevel&quot;</span><span class="p">]),</span>
            <span class="n">operation</span><span class="o">=</span><span class="n">CookieOperation</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;operation&quot;</span><span class="p">]),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="ClientHintIssueReason">
<a class="viewcode-back" href="../../../nodriver/cdp/audits.html#nodriver.cdp.audits.ClientHintIssueReason">[docs]</a>
<span class="k">class</span> <span class="nc">ClientHintIssueReason</span><span class="p">(</span><span class="n">enum</span><span class="o">.</span><span class="n">Enum</span><span class="p">):</span>
    <span class="n">META_TAG_ALLOW_LIST_INVALID_ORIGIN</span> <span class="o">=</span> <span class="s2">&quot;MetaTagAllowListInvalidOrigin&quot;</span>
    <span class="n">META_TAG_MODIFIED_HTML</span> <span class="o">=</span> <span class="s2">&quot;MetaTagModifiedHTML&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">ClientHintIssueReason</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span></div>



<div class="viewcode-block" id="FederatedAuthRequestIssueDetails">
<a class="viewcode-back" href="../../../nodriver/cdp/audits.html#nodriver.cdp.audits.FederatedAuthRequestIssueDetails">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">FederatedAuthRequestIssueDetails</span><span class="p">:</span>
    <span class="n">federated_auth_request_issue_reason</span><span class="p">:</span> <span class="n">FederatedAuthRequestIssueReason</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;federatedAuthRequestIssueReason&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">(</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">federated_auth_request_issue_reason</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="p">)</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">FederatedAuthRequestIssueDetails</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">federated_auth_request_issue_reason</span><span class="o">=</span><span class="n">FederatedAuthRequestIssueReason</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span>
                <span class="n">json</span><span class="p">[</span><span class="s2">&quot;federatedAuthRequestIssueReason&quot;</span><span class="p">]</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="FederatedAuthRequestIssueReason">
<a class="viewcode-back" href="../../../nodriver/cdp/audits.html#nodriver.cdp.audits.FederatedAuthRequestIssueReason">[docs]</a>
<span class="k">class</span> <span class="nc">FederatedAuthRequestIssueReason</span><span class="p">(</span><span class="n">enum</span><span class="o">.</span><span class="n">Enum</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Represents the failure reason when a federated authentication reason fails.</span>
<span class="sd">    Should be updated alongside RequestIdTokenStatus in</span>
<span class="sd">    third_party/blink/public/mojom/devtools/inspector_issue.mojom to include</span>
<span class="sd">    all cases except for success.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">SHOULD_EMBARGO</span> <span class="o">=</span> <span class="s2">&quot;ShouldEmbargo&quot;</span>
    <span class="n">TOO_MANY_REQUESTS</span> <span class="o">=</span> <span class="s2">&quot;TooManyRequests&quot;</span>
    <span class="n">WELL_KNOWN_HTTP_NOT_FOUND</span> <span class="o">=</span> <span class="s2">&quot;WellKnownHttpNotFound&quot;</span>
    <span class="n">WELL_KNOWN_NO_RESPONSE</span> <span class="o">=</span> <span class="s2">&quot;WellKnownNoResponse&quot;</span>
    <span class="n">WELL_KNOWN_INVALID_RESPONSE</span> <span class="o">=</span> <span class="s2">&quot;WellKnownInvalidResponse&quot;</span>
    <span class="n">WELL_KNOWN_LIST_EMPTY</span> <span class="o">=</span> <span class="s2">&quot;WellKnownListEmpty&quot;</span>
    <span class="n">WELL_KNOWN_INVALID_CONTENT_TYPE</span> <span class="o">=</span> <span class="s2">&quot;WellKnownInvalidContentType&quot;</span>
    <span class="n">CONFIG_NOT_IN_WELL_KNOWN</span> <span class="o">=</span> <span class="s2">&quot;ConfigNotInWellKnown&quot;</span>
    <span class="n">WELL_KNOWN_TOO_BIG</span> <span class="o">=</span> <span class="s2">&quot;WellKnownTooBig&quot;</span>
    <span class="n">CONFIG_HTTP_NOT_FOUND</span> <span class="o">=</span> <span class="s2">&quot;ConfigHttpNotFound&quot;</span>
    <span class="n">CONFIG_NO_RESPONSE</span> <span class="o">=</span> <span class="s2">&quot;ConfigNoResponse&quot;</span>
    <span class="n">CONFIG_INVALID_RESPONSE</span> <span class="o">=</span> <span class="s2">&quot;ConfigInvalidResponse&quot;</span>
    <span class="n">CONFIG_INVALID_CONTENT_TYPE</span> <span class="o">=</span> <span class="s2">&quot;ConfigInvalidContentType&quot;</span>
    <span class="n">CLIENT_METADATA_HTTP_NOT_FOUND</span> <span class="o">=</span> <span class="s2">&quot;ClientMetadataHttpNotFound&quot;</span>
    <span class="n">CLIENT_METADATA_NO_RESPONSE</span> <span class="o">=</span> <span class="s2">&quot;ClientMetadataNoResponse&quot;</span>
    <span class="n">CLIENT_METADATA_INVALID_RESPONSE</span> <span class="o">=</span> <span class="s2">&quot;ClientMetadataInvalidResponse&quot;</span>
    <span class="n">CLIENT_METADATA_INVALID_CONTENT_TYPE</span> <span class="o">=</span> <span class="s2">&quot;ClientMetadataInvalidContentType&quot;</span>
    <span class="n">IDP_NOT_POTENTIALLY_TRUSTWORTHY</span> <span class="o">=</span> <span class="s2">&quot;IdpNotPotentiallyTrustworthy&quot;</span>
    <span class="n">DISABLED_IN_SETTINGS</span> <span class="o">=</span> <span class="s2">&quot;DisabledInSettings&quot;</span>
    <span class="n">DISABLED_IN_FLAGS</span> <span class="o">=</span> <span class="s2">&quot;DisabledInFlags&quot;</span>
    <span class="n">ERROR_FETCHING_SIGNIN</span> <span class="o">=</span> <span class="s2">&quot;ErrorFetchingSignin&quot;</span>
    <span class="n">INVALID_SIGNIN_RESPONSE</span> <span class="o">=</span> <span class="s2">&quot;InvalidSigninResponse&quot;</span>
    <span class="n">ACCOUNTS_HTTP_NOT_FOUND</span> <span class="o">=</span> <span class="s2">&quot;AccountsHttpNotFound&quot;</span>
    <span class="n">ACCOUNTS_NO_RESPONSE</span> <span class="o">=</span> <span class="s2">&quot;AccountsNoResponse&quot;</span>
    <span class="n">ACCOUNTS_INVALID_RESPONSE</span> <span class="o">=</span> <span class="s2">&quot;AccountsInvalidResponse&quot;</span>
    <span class="n">ACCOUNTS_LIST_EMPTY</span> <span class="o">=</span> <span class="s2">&quot;AccountsListEmpty&quot;</span>
    <span class="n">ACCOUNTS_INVALID_CONTENT_TYPE</span> <span class="o">=</span> <span class="s2">&quot;AccountsInvalidContentType&quot;</span>
    <span class="n">ID_TOKEN_HTTP_NOT_FOUND</span> <span class="o">=</span> <span class="s2">&quot;IdTokenHttpNotFound&quot;</span>
    <span class="n">ID_TOKEN_NO_RESPONSE</span> <span class="o">=</span> <span class="s2">&quot;IdTokenNoResponse&quot;</span>
    <span class="n">ID_TOKEN_INVALID_RESPONSE</span> <span class="o">=</span> <span class="s2">&quot;IdTokenInvalidResponse&quot;</span>
    <span class="n">ID_TOKEN_IDP_ERROR_RESPONSE</span> <span class="o">=</span> <span class="s2">&quot;IdTokenIdpErrorResponse&quot;</span>
    <span class="n">ID_TOKEN_CROSS_SITE_IDP_ERROR_RESPONSE</span> <span class="o">=</span> <span class="s2">&quot;IdTokenCrossSiteIdpErrorResponse&quot;</span>
    <span class="n">ID_TOKEN_INVALID_REQUEST</span> <span class="o">=</span> <span class="s2">&quot;IdTokenInvalidRequest&quot;</span>
    <span class="n">ID_TOKEN_INVALID_CONTENT_TYPE</span> <span class="o">=</span> <span class="s2">&quot;IdTokenInvalidContentType&quot;</span>
    <span class="n">ERROR_ID_TOKEN</span> <span class="o">=</span> <span class="s2">&quot;ErrorIdToken&quot;</span>
    <span class="n">CANCELED</span> <span class="o">=</span> <span class="s2">&quot;Canceled&quot;</span>
    <span class="n">RP_PAGE_NOT_VISIBLE</span> <span class="o">=</span> <span class="s2">&quot;RpPageNotVisible&quot;</span>
    <span class="n">SILENT_MEDIATION_FAILURE</span> <span class="o">=</span> <span class="s2">&quot;SilentMediationFailure&quot;</span>
    <span class="n">THIRD_PARTY_COOKIES_BLOCKED</span> <span class="o">=</span> <span class="s2">&quot;ThirdPartyCookiesBlocked&quot;</span>
    <span class="n">NOT_SIGNED_IN_WITH_IDP</span> <span class="o">=</span> <span class="s2">&quot;NotSignedInWithIdp&quot;</span>
    <span class="n">MISSING_TRANSIENT_USER_ACTIVATION</span> <span class="o">=</span> <span class="s2">&quot;MissingTransientUserActivation&quot;</span>
    <span class="n">REPLACED_BY_ACTIVE_MODE</span> <span class="o">=</span> <span class="s2">&quot;ReplacedByActiveMode&quot;</span>
    <span class="n">INVALID_FIELDS_SPECIFIED</span> <span class="o">=</span> <span class="s2">&quot;InvalidFieldsSpecified&quot;</span>
    <span class="n">RELYING_PARTY_ORIGIN_IS_OPAQUE</span> <span class="o">=</span> <span class="s2">&quot;RelyingPartyOriginIsOpaque&quot;</span>
    <span class="n">TYPE_NOT_MATCHING</span> <span class="o">=</span> <span class="s2">&quot;TypeNotMatching&quot;</span>
    <span class="n">UI_DISMISSED_NO_EMBARGO</span> <span class="o">=</span> <span class="s2">&quot;UiDismissedNoEmbargo&quot;</span>
    <span class="n">CORS_ERROR</span> <span class="o">=</span> <span class="s2">&quot;CorsError&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">FederatedAuthRequestIssueReason</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span></div>



<div class="viewcode-block" id="FederatedAuthUserInfoRequestIssueDetails">
<a class="viewcode-back" href="../../../nodriver/cdp/audits.html#nodriver.cdp.audits.FederatedAuthUserInfoRequestIssueDetails">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">FederatedAuthUserInfoRequestIssueDetails</span><span class="p">:</span>
    <span class="n">federated_auth_user_info_request_issue_reason</span><span class="p">:</span> <span class="p">(</span>
        <span class="n">FederatedAuthUserInfoRequestIssueReason</span>
    <span class="p">)</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;federatedAuthUserInfoRequestIssueReason&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">(</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">federated_auth_user_info_request_issue_reason</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="p">)</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">FederatedAuthUserInfoRequestIssueDetails</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">federated_auth_user_info_request_issue_reason</span><span class="o">=</span><span class="n">FederatedAuthUserInfoRequestIssueReason</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span>
                <span class="n">json</span><span class="p">[</span><span class="s2">&quot;federatedAuthUserInfoRequestIssueReason&quot;</span><span class="p">]</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="FederatedAuthUserInfoRequestIssueReason">
<a class="viewcode-back" href="../../../nodriver/cdp/audits.html#nodriver.cdp.audits.FederatedAuthUserInfoRequestIssueReason">[docs]</a>
<span class="k">class</span> <span class="nc">FederatedAuthUserInfoRequestIssueReason</span><span class="p">(</span><span class="n">enum</span><span class="o">.</span><span class="n">Enum</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Represents the failure reason when a getUserInfo() call fails.</span>
<span class="sd">    Should be updated alongside FederatedAuthUserInfoRequestResult in</span>
<span class="sd">    third_party/blink/public/mojom/devtools/inspector_issue.mojom.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">NOT_SAME_ORIGIN</span> <span class="o">=</span> <span class="s2">&quot;NotSameOrigin&quot;</span>
    <span class="n">NOT_IFRAME</span> <span class="o">=</span> <span class="s2">&quot;NotIframe&quot;</span>
    <span class="n">NOT_POTENTIALLY_TRUSTWORTHY</span> <span class="o">=</span> <span class="s2">&quot;NotPotentiallyTrustworthy&quot;</span>
    <span class="n">NO_API_PERMISSION</span> <span class="o">=</span> <span class="s2">&quot;NoApiPermission&quot;</span>
    <span class="n">NOT_SIGNED_IN_WITH_IDP</span> <span class="o">=</span> <span class="s2">&quot;NotSignedInWithIdp&quot;</span>
    <span class="n">NO_ACCOUNT_SHARING_PERMISSION</span> <span class="o">=</span> <span class="s2">&quot;NoAccountSharingPermission&quot;</span>
    <span class="n">INVALID_CONFIG_OR_WELL_KNOWN</span> <span class="o">=</span> <span class="s2">&quot;InvalidConfigOrWellKnown&quot;</span>
    <span class="n">INVALID_ACCOUNTS_RESPONSE</span> <span class="o">=</span> <span class="s2">&quot;InvalidAccountsResponse&quot;</span>
    <span class="n">NO_RETURNING_USER_FROM_FETCHED_ACCOUNTS</span> <span class="o">=</span> <span class="s2">&quot;NoReturningUserFromFetchedAccounts&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">FederatedAuthUserInfoRequestIssueReason</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span></div>



<div class="viewcode-block" id="ClientHintIssueDetails">
<a class="viewcode-back" href="../../../nodriver/cdp/audits.html#nodriver.cdp.audits.ClientHintIssueDetails">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">ClientHintIssueDetails</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    This issue tracks client hints related issues. It&#39;s used to deprecate old</span>
<span class="sd">    features, encourage the use of new ones, and provide general guidance.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">source_code_location</span><span class="p">:</span> <span class="n">SourceCodeLocation</span>

    <span class="n">client_hint_issue_reason</span><span class="p">:</span> <span class="n">ClientHintIssueReason</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;sourceCodeLocation&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">source_code_location</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;clientHintIssueReason&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">client_hint_issue_reason</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">ClientHintIssueDetails</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">source_code_location</span><span class="o">=</span><span class="n">SourceCodeLocation</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span>
                <span class="n">json</span><span class="p">[</span><span class="s2">&quot;sourceCodeLocation&quot;</span><span class="p">]</span>
            <span class="p">),</span>
            <span class="n">client_hint_issue_reason</span><span class="o">=</span><span class="n">ClientHintIssueReason</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span>
                <span class="n">json</span><span class="p">[</span><span class="s2">&quot;clientHintIssueReason&quot;</span><span class="p">]</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="FailedRequestInfo">
<a class="viewcode-back" href="../../../nodriver/cdp/audits.html#nodriver.cdp.audits.FailedRequestInfo">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">FailedRequestInfo</span><span class="p">:</span>
    <span class="c1">#: The URL that failed to load.</span>
    <span class="n">url</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: The failure message for the failed request.</span>
    <span class="n">failure_message</span><span class="p">:</span> <span class="nb">str</span>

    <span class="n">request_id</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">network</span><span class="o">.</span><span class="n">RequestId</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;url&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">url</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;failureMessage&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">failure_message</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">request_id</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;requestId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">request_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">FailedRequestInfo</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">url</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;url&quot;</span><span class="p">]),</span>
            <span class="n">failure_message</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;failureMessage&quot;</span><span class="p">]),</span>
            <span class="n">request_id</span><span class="o">=</span><span class="p">(</span>
                <span class="n">network</span><span class="o">.</span><span class="n">RequestId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;requestId&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;requestId&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="PartitioningBlobURLInfo">
<a class="viewcode-back" href="../../../nodriver/cdp/audits.html#nodriver.cdp.audits.PartitioningBlobURLInfo">[docs]</a>
<span class="k">class</span> <span class="nc">PartitioningBlobURLInfo</span><span class="p">(</span><span class="n">enum</span><span class="o">.</span><span class="n">Enum</span><span class="p">):</span>
    <span class="n">BLOCKED_CROSS_PARTITION_FETCHING</span> <span class="o">=</span> <span class="s2">&quot;BlockedCrossPartitionFetching&quot;</span>
    <span class="n">ENFORCE_NOOPENER_FOR_NAVIGATION</span> <span class="o">=</span> <span class="s2">&quot;EnforceNoopenerForNavigation&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">PartitioningBlobURLInfo</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span></div>



<div class="viewcode-block" id="PartitioningBlobURLIssueDetails">
<a class="viewcode-back" href="../../../nodriver/cdp/audits.html#nodriver.cdp.audits.PartitioningBlobURLIssueDetails">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">PartitioningBlobURLIssueDetails</span><span class="p">:</span>
    <span class="c1">#: The BlobURL that failed to load.</span>
    <span class="n">url</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: Additional information about the Partitioning Blob URL issue.</span>
    <span class="n">partitioning_blob_url_info</span><span class="p">:</span> <span class="n">PartitioningBlobURLInfo</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;url&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">url</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;partitioningBlobURLInfo&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">partitioning_blob_url_info</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">PartitioningBlobURLIssueDetails</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">url</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;url&quot;</span><span class="p">]),</span>
            <span class="n">partitioning_blob_url_info</span><span class="o">=</span><span class="n">PartitioningBlobURLInfo</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span>
                <span class="n">json</span><span class="p">[</span><span class="s2">&quot;partitioningBlobURLInfo&quot;</span><span class="p">]</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="SelectElementAccessibilityIssueReason">
<a class="viewcode-back" href="../../../nodriver/cdp/audits.html#nodriver.cdp.audits.SelectElementAccessibilityIssueReason">[docs]</a>
<span class="k">class</span> <span class="nc">SelectElementAccessibilityIssueReason</span><span class="p">(</span><span class="n">enum</span><span class="o">.</span><span class="n">Enum</span><span class="p">):</span>
    <span class="n">DISALLOWED_SELECT_CHILD</span> <span class="o">=</span> <span class="s2">&quot;DisallowedSelectChild&quot;</span>
    <span class="n">DISALLOWED_OPT_GROUP_CHILD</span> <span class="o">=</span> <span class="s2">&quot;DisallowedOptGroupChild&quot;</span>
    <span class="n">NON_PHRASING_CONTENT_OPTION_CHILD</span> <span class="o">=</span> <span class="s2">&quot;NonPhrasingContentOptionChild&quot;</span>
    <span class="n">INTERACTIVE_CONTENT_OPTION_CHILD</span> <span class="o">=</span> <span class="s2">&quot;InteractiveContentOptionChild&quot;</span>
    <span class="n">INTERACTIVE_CONTENT_LEGEND_CHILD</span> <span class="o">=</span> <span class="s2">&quot;InteractiveContentLegendChild&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">SelectElementAccessibilityIssueReason</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span></div>



<div class="viewcode-block" id="SelectElementAccessibilityIssueDetails">
<a class="viewcode-back" href="../../../nodriver/cdp/audits.html#nodriver.cdp.audits.SelectElementAccessibilityIssueDetails">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">SelectElementAccessibilityIssueDetails</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    This issue warns about errors in the select element content model.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">node_id</span><span class="p">:</span> <span class="n">dom</span><span class="o">.</span><span class="n">BackendNodeId</span>

    <span class="n">select_element_accessibility_issue_reason</span><span class="p">:</span> <span class="n">SelectElementAccessibilityIssueReason</span>

    <span class="n">has_disallowed_attributes</span><span class="p">:</span> <span class="nb">bool</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;nodeId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">node_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;selectElementAccessibilityIssueReason&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">(</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">select_element_accessibility_issue_reason</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="p">)</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;hasDisallowedAttributes&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">has_disallowed_attributes</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">SelectElementAccessibilityIssueDetails</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">node_id</span><span class="o">=</span><span class="n">dom</span><span class="o">.</span><span class="n">BackendNodeId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;nodeId&quot;</span><span class="p">]),</span>
            <span class="n">select_element_accessibility_issue_reason</span><span class="o">=</span><span class="n">SelectElementAccessibilityIssueReason</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span>
                <span class="n">json</span><span class="p">[</span><span class="s2">&quot;selectElementAccessibilityIssueReason&quot;</span><span class="p">]</span>
            <span class="p">),</span>
            <span class="n">has_disallowed_attributes</span><span class="o">=</span><span class="nb">bool</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;hasDisallowedAttributes&quot;</span><span class="p">]),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="StyleSheetLoadingIssueReason">
<a class="viewcode-back" href="../../../nodriver/cdp/audits.html#nodriver.cdp.audits.StyleSheetLoadingIssueReason">[docs]</a>
<span class="k">class</span> <span class="nc">StyleSheetLoadingIssueReason</span><span class="p">(</span><span class="n">enum</span><span class="o">.</span><span class="n">Enum</span><span class="p">):</span>
    <span class="n">LATE_IMPORT_RULE</span> <span class="o">=</span> <span class="s2">&quot;LateImportRule&quot;</span>
    <span class="n">REQUEST_FAILED</span> <span class="o">=</span> <span class="s2">&quot;RequestFailed&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">StyleSheetLoadingIssueReason</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span></div>



<div class="viewcode-block" id="StylesheetLoadingIssueDetails">
<a class="viewcode-back" href="../../../nodriver/cdp/audits.html#nodriver.cdp.audits.StylesheetLoadingIssueDetails">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">StylesheetLoadingIssueDetails</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    This issue warns when a referenced stylesheet couldn&#39;t be loaded.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: Source code position that referenced the failing stylesheet.</span>
    <span class="n">source_code_location</span><span class="p">:</span> <span class="n">SourceCodeLocation</span>

    <span class="c1">#: Reason why the stylesheet couldn&#39;t be loaded.</span>
    <span class="n">style_sheet_loading_issue_reason</span><span class="p">:</span> <span class="n">StyleSheetLoadingIssueReason</span>

    <span class="c1">#: Contains additional info when the failure was due to a request.</span>
    <span class="n">failed_request_info</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">FailedRequestInfo</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;sourceCodeLocation&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">source_code_location</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;styleSheetLoadingIssueReason&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">(</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">style_sheet_loading_issue_reason</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="p">)</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">failed_request_info</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;failedRequestInfo&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">failed_request_info</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">StylesheetLoadingIssueDetails</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">source_code_location</span><span class="o">=</span><span class="n">SourceCodeLocation</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span>
                <span class="n">json</span><span class="p">[</span><span class="s2">&quot;sourceCodeLocation&quot;</span><span class="p">]</span>
            <span class="p">),</span>
            <span class="n">style_sheet_loading_issue_reason</span><span class="o">=</span><span class="n">StyleSheetLoadingIssueReason</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span>
                <span class="n">json</span><span class="p">[</span><span class="s2">&quot;styleSheetLoadingIssueReason&quot;</span><span class="p">]</span>
            <span class="p">),</span>
            <span class="n">failed_request_info</span><span class="o">=</span><span class="p">(</span>
                <span class="n">FailedRequestInfo</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;failedRequestInfo&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;failedRequestInfo&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="PropertyRuleIssueReason">
<a class="viewcode-back" href="../../../nodriver/cdp/audits.html#nodriver.cdp.audits.PropertyRuleIssueReason">[docs]</a>
<span class="k">class</span> <span class="nc">PropertyRuleIssueReason</span><span class="p">(</span><span class="n">enum</span><span class="o">.</span><span class="n">Enum</span><span class="p">):</span>
    <span class="n">INVALID_SYNTAX</span> <span class="o">=</span> <span class="s2">&quot;InvalidSyntax&quot;</span>
    <span class="n">INVALID_INITIAL_VALUE</span> <span class="o">=</span> <span class="s2">&quot;InvalidInitialValue&quot;</span>
    <span class="n">INVALID_INHERITS</span> <span class="o">=</span> <span class="s2">&quot;InvalidInherits&quot;</span>
    <span class="n">INVALID_NAME</span> <span class="o">=</span> <span class="s2">&quot;InvalidName&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">PropertyRuleIssueReason</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span></div>



<div class="viewcode-block" id="PropertyRuleIssueDetails">
<a class="viewcode-back" href="../../../nodriver/cdp/audits.html#nodriver.cdp.audits.PropertyRuleIssueDetails">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">PropertyRuleIssueDetails</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    This issue warns about errors in property rules that lead to property</span>
<span class="sd">    registrations being ignored.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: Source code position of the property rule.</span>
    <span class="n">source_code_location</span><span class="p">:</span> <span class="n">SourceCodeLocation</span>

    <span class="c1">#: Reason why the property rule was discarded.</span>
    <span class="n">property_rule_issue_reason</span><span class="p">:</span> <span class="n">PropertyRuleIssueReason</span>

    <span class="c1">#: The value of the property rule property that failed to parse</span>
    <span class="n">property_value</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;sourceCodeLocation&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">source_code_location</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;propertyRuleIssueReason&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">property_rule_issue_reason</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">property_value</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;propertyValue&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">property_value</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">PropertyRuleIssueDetails</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">source_code_location</span><span class="o">=</span><span class="n">SourceCodeLocation</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span>
                <span class="n">json</span><span class="p">[</span><span class="s2">&quot;sourceCodeLocation&quot;</span><span class="p">]</span>
            <span class="p">),</span>
            <span class="n">property_rule_issue_reason</span><span class="o">=</span><span class="n">PropertyRuleIssueReason</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span>
                <span class="n">json</span><span class="p">[</span><span class="s2">&quot;propertyRuleIssueReason&quot;</span><span class="p">]</span>
            <span class="p">),</span>
            <span class="n">property_value</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;propertyValue&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;propertyValue&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="InspectorIssueCode">
<a class="viewcode-back" href="../../../nodriver/cdp/audits.html#nodriver.cdp.audits.InspectorIssueCode">[docs]</a>
<span class="k">class</span> <span class="nc">InspectorIssueCode</span><span class="p">(</span><span class="n">enum</span><span class="o">.</span><span class="n">Enum</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    A unique identifier for the type of issue. Each type may use one of the</span>
<span class="sd">    optional fields in InspectorIssueDetails to convey more specific</span>
<span class="sd">    information about the kind of issue.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">COOKIE_ISSUE</span> <span class="o">=</span> <span class="s2">&quot;CookieIssue&quot;</span>
    <span class="n">MIXED_CONTENT_ISSUE</span> <span class="o">=</span> <span class="s2">&quot;MixedContentIssue&quot;</span>
    <span class="n">BLOCKED_BY_RESPONSE_ISSUE</span> <span class="o">=</span> <span class="s2">&quot;BlockedByResponseIssue&quot;</span>
    <span class="n">HEAVY_AD_ISSUE</span> <span class="o">=</span> <span class="s2">&quot;HeavyAdIssue&quot;</span>
    <span class="n">CONTENT_SECURITY_POLICY_ISSUE</span> <span class="o">=</span> <span class="s2">&quot;ContentSecurityPolicyIssue&quot;</span>
    <span class="n">SHARED_ARRAY_BUFFER_ISSUE</span> <span class="o">=</span> <span class="s2">&quot;SharedArrayBufferIssue&quot;</span>
    <span class="n">LOW_TEXT_CONTRAST_ISSUE</span> <span class="o">=</span> <span class="s2">&quot;LowTextContrastIssue&quot;</span>
    <span class="n">CORS_ISSUE</span> <span class="o">=</span> <span class="s2">&quot;CorsIssue&quot;</span>
    <span class="n">ATTRIBUTION_REPORTING_ISSUE</span> <span class="o">=</span> <span class="s2">&quot;AttributionReportingIssue&quot;</span>
    <span class="n">QUIRKS_MODE_ISSUE</span> <span class="o">=</span> <span class="s2">&quot;QuirksModeIssue&quot;</span>
    <span class="n">PARTITIONING_BLOB_URL_ISSUE</span> <span class="o">=</span> <span class="s2">&quot;PartitioningBlobURLIssue&quot;</span>
    <span class="n">NAVIGATOR_USER_AGENT_ISSUE</span> <span class="o">=</span> <span class="s2">&quot;NavigatorUserAgentIssue&quot;</span>
    <span class="n">GENERIC_ISSUE</span> <span class="o">=</span> <span class="s2">&quot;GenericIssue&quot;</span>
    <span class="n">DEPRECATION_ISSUE</span> <span class="o">=</span> <span class="s2">&quot;DeprecationIssue&quot;</span>
    <span class="n">CLIENT_HINT_ISSUE</span> <span class="o">=</span> <span class="s2">&quot;ClientHintIssue&quot;</span>
    <span class="n">FEDERATED_AUTH_REQUEST_ISSUE</span> <span class="o">=</span> <span class="s2">&quot;FederatedAuthRequestIssue&quot;</span>
    <span class="n">BOUNCE_TRACKING_ISSUE</span> <span class="o">=</span> <span class="s2">&quot;BounceTrackingIssue&quot;</span>
    <span class="n">COOKIE_DEPRECATION_METADATA_ISSUE</span> <span class="o">=</span> <span class="s2">&quot;CookieDeprecationMetadataIssue&quot;</span>
    <span class="n">STYLESHEET_LOADING_ISSUE</span> <span class="o">=</span> <span class="s2">&quot;StylesheetLoadingIssue&quot;</span>
    <span class="n">FEDERATED_AUTH_USER_INFO_REQUEST_ISSUE</span> <span class="o">=</span> <span class="s2">&quot;FederatedAuthUserInfoRequestIssue&quot;</span>
    <span class="n">PROPERTY_RULE_ISSUE</span> <span class="o">=</span> <span class="s2">&quot;PropertyRuleIssue&quot;</span>
    <span class="n">SHARED_DICTIONARY_ISSUE</span> <span class="o">=</span> <span class="s2">&quot;SharedDictionaryIssue&quot;</span>
    <span class="n">SELECT_ELEMENT_ACCESSIBILITY_ISSUE</span> <span class="o">=</span> <span class="s2">&quot;SelectElementAccessibilityIssue&quot;</span>
    <span class="n">SRI_MESSAGE_SIGNATURE_ISSUE</span> <span class="o">=</span> <span class="s2">&quot;SRIMessageSignatureIssue&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">InspectorIssueCode</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span></div>



<div class="viewcode-block" id="InspectorIssueDetails">
<a class="viewcode-back" href="../../../nodriver/cdp/audits.html#nodriver.cdp.audits.InspectorIssueDetails">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">InspectorIssueDetails</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    This struct holds a list of optional fields with additional information</span>
<span class="sd">    specific to the kind of issue. When adding a new issue code, please also</span>
<span class="sd">    add a new optional field to this type.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">cookie_issue_details</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">CookieIssueDetails</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="n">mixed_content_issue_details</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">MixedContentIssueDetails</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="n">blocked_by_response_issue_details</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span>
        <span class="n">BlockedByResponseIssueDetails</span>
    <span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="n">heavy_ad_issue_details</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">HeavyAdIssueDetails</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="n">content_security_policy_issue_details</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span>
        <span class="n">ContentSecurityPolicyIssueDetails</span>
    <span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="n">shared_array_buffer_issue_details</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span>
        <span class="n">SharedArrayBufferIssueDetails</span>
    <span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="n">low_text_contrast_issue_details</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">LowTextContrastIssueDetails</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="n">cors_issue_details</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">CorsIssueDetails</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="n">attribution_reporting_issue_details</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span>
        <span class="n">AttributionReportingIssueDetails</span>
    <span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="n">quirks_mode_issue_details</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">QuirksModeIssueDetails</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="n">partitioning_blob_url_issue_details</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span>
        <span class="n">PartitioningBlobURLIssueDetails</span>
    <span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="n">navigator_user_agent_issue_details</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span>
        <span class="n">NavigatorUserAgentIssueDetails</span>
    <span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="n">generic_issue_details</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">GenericIssueDetails</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="n">deprecation_issue_details</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">DeprecationIssueDetails</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="n">client_hint_issue_details</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">ClientHintIssueDetails</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="n">federated_auth_request_issue_details</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span>
        <span class="n">FederatedAuthRequestIssueDetails</span>
    <span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="n">bounce_tracking_issue_details</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">BounceTrackingIssueDetails</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="n">cookie_deprecation_metadata_issue_details</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span>
        <span class="n">CookieDeprecationMetadataIssueDetails</span>
    <span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="n">stylesheet_loading_issue_details</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">StylesheetLoadingIssueDetails</span><span class="p">]</span> <span class="o">=</span> <span class="p">(</span>
        <span class="kc">None</span>
    <span class="p">)</span>

    <span class="n">property_rule_issue_details</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">PropertyRuleIssueDetails</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="n">federated_auth_user_info_request_issue_details</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span>
        <span class="n">FederatedAuthUserInfoRequestIssueDetails</span>
    <span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="n">shared_dictionary_issue_details</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">SharedDictionaryIssueDetails</span><span class="p">]</span> <span class="o">=</span> <span class="p">(</span>
        <span class="kc">None</span>
    <span class="p">)</span>

    <span class="n">select_element_accessibility_issue_details</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span>
        <span class="n">SelectElementAccessibilityIssueDetails</span>
    <span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="n">sri_message_signature_issue_details</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span>
        <span class="n">SRIMessageSignatureIssueDetails</span>
    <span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">cookie_issue_details</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;cookieIssueDetails&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">cookie_issue_details</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">mixed_content_issue_details</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;mixedContentIssueDetails&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">(</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">mixed_content_issue_details</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
            <span class="p">)</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">blocked_by_response_issue_details</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;blockedByResponseIssueDetails&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">(</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">blocked_by_response_issue_details</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
            <span class="p">)</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">heavy_ad_issue_details</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;heavyAdIssueDetails&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">heavy_ad_issue_details</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">content_security_policy_issue_details</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;contentSecurityPolicyIssueDetails&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">(</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">content_security_policy_issue_details</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
            <span class="p">)</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">shared_array_buffer_issue_details</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;sharedArrayBufferIssueDetails&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">(</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">shared_array_buffer_issue_details</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
            <span class="p">)</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">low_text_contrast_issue_details</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;lowTextContrastIssueDetails&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">(</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">low_text_contrast_issue_details</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
            <span class="p">)</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">cors_issue_details</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;corsIssueDetails&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">cors_issue_details</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">attribution_reporting_issue_details</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;attributionReportingIssueDetails&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">(</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">attribution_reporting_issue_details</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
            <span class="p">)</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">quirks_mode_issue_details</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;quirksModeIssueDetails&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">quirks_mode_issue_details</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">partitioning_blob_url_issue_details</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;partitioningBlobURLIssueDetails&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">(</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">partitioning_blob_url_issue_details</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
            <span class="p">)</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">navigator_user_agent_issue_details</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;navigatorUserAgentIssueDetails&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">(</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">navigator_user_agent_issue_details</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
            <span class="p">)</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">generic_issue_details</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;genericIssueDetails&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">generic_issue_details</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">deprecation_issue_details</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;deprecationIssueDetails&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">deprecation_issue_details</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">client_hint_issue_details</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;clientHintIssueDetails&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">client_hint_issue_details</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">federated_auth_request_issue_details</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;federatedAuthRequestIssueDetails&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">(</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">federated_auth_request_issue_details</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
            <span class="p">)</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">bounce_tracking_issue_details</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;bounceTrackingIssueDetails&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">(</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">bounce_tracking_issue_details</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
            <span class="p">)</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">cookie_deprecation_metadata_issue_details</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;cookieDeprecationMetadataIssueDetails&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">(</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">cookie_deprecation_metadata_issue_details</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
            <span class="p">)</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">stylesheet_loading_issue_details</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;stylesheetLoadingIssueDetails&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">(</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">stylesheet_loading_issue_details</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
            <span class="p">)</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">property_rule_issue_details</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;propertyRuleIssueDetails&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">(</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">property_rule_issue_details</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
            <span class="p">)</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">federated_auth_user_info_request_issue_details</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;federatedAuthUserInfoRequestIssueDetails&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">(</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">federated_auth_user_info_request_issue_details</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
            <span class="p">)</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">shared_dictionary_issue_details</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;sharedDictionaryIssueDetails&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">(</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">shared_dictionary_issue_details</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
            <span class="p">)</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">select_element_accessibility_issue_details</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;selectElementAccessibilityIssueDetails&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">(</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">select_element_accessibility_issue_details</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
            <span class="p">)</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">sri_message_signature_issue_details</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;sriMessageSignatureIssueDetails&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">(</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">sri_message_signature_issue_details</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
            <span class="p">)</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">InspectorIssueDetails</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">cookie_issue_details</span><span class="o">=</span><span class="p">(</span>
                <span class="n">CookieIssueDetails</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;cookieIssueDetails&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;cookieIssueDetails&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">mixed_content_issue_details</span><span class="o">=</span><span class="p">(</span>
                <span class="n">MixedContentIssueDetails</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;mixedContentIssueDetails&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;mixedContentIssueDetails&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">blocked_by_response_issue_details</span><span class="o">=</span><span class="p">(</span>
                <span class="n">BlockedByResponseIssueDetails</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span>
                    <span class="n">json</span><span class="p">[</span><span class="s2">&quot;blockedByResponseIssueDetails&quot;</span><span class="p">]</span>
                <span class="p">)</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;blockedByResponseIssueDetails&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">heavy_ad_issue_details</span><span class="o">=</span><span class="p">(</span>
                <span class="n">HeavyAdIssueDetails</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;heavyAdIssueDetails&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;heavyAdIssueDetails&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">content_security_policy_issue_details</span><span class="o">=</span><span class="p">(</span>
                <span class="n">ContentSecurityPolicyIssueDetails</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span>
                    <span class="n">json</span><span class="p">[</span><span class="s2">&quot;contentSecurityPolicyIssueDetails&quot;</span><span class="p">]</span>
                <span class="p">)</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;contentSecurityPolicyIssueDetails&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">shared_array_buffer_issue_details</span><span class="o">=</span><span class="p">(</span>
                <span class="n">SharedArrayBufferIssueDetails</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span>
                    <span class="n">json</span><span class="p">[</span><span class="s2">&quot;sharedArrayBufferIssueDetails&quot;</span><span class="p">]</span>
                <span class="p">)</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;sharedArrayBufferIssueDetails&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">low_text_contrast_issue_details</span><span class="o">=</span><span class="p">(</span>
                <span class="n">LowTextContrastIssueDetails</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span>
                    <span class="n">json</span><span class="p">[</span><span class="s2">&quot;lowTextContrastIssueDetails&quot;</span><span class="p">]</span>
                <span class="p">)</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;lowTextContrastIssueDetails&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">cors_issue_details</span><span class="o">=</span><span class="p">(</span>
                <span class="n">CorsIssueDetails</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;corsIssueDetails&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;corsIssueDetails&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">attribution_reporting_issue_details</span><span class="o">=</span><span class="p">(</span>
                <span class="n">AttributionReportingIssueDetails</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span>
                    <span class="n">json</span><span class="p">[</span><span class="s2">&quot;attributionReportingIssueDetails&quot;</span><span class="p">]</span>
                <span class="p">)</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;attributionReportingIssueDetails&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">quirks_mode_issue_details</span><span class="o">=</span><span class="p">(</span>
                <span class="n">QuirksModeIssueDetails</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;quirksModeIssueDetails&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;quirksModeIssueDetails&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">partitioning_blob_url_issue_details</span><span class="o">=</span><span class="p">(</span>
                <span class="n">PartitioningBlobURLIssueDetails</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span>
                    <span class="n">json</span><span class="p">[</span><span class="s2">&quot;partitioningBlobURLIssueDetails&quot;</span><span class="p">]</span>
                <span class="p">)</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;partitioningBlobURLIssueDetails&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">navigator_user_agent_issue_details</span><span class="o">=</span><span class="p">(</span>
                <span class="n">NavigatorUserAgentIssueDetails</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span>
                    <span class="n">json</span><span class="p">[</span><span class="s2">&quot;navigatorUserAgentIssueDetails&quot;</span><span class="p">]</span>
                <span class="p">)</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;navigatorUserAgentIssueDetails&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">generic_issue_details</span><span class="o">=</span><span class="p">(</span>
                <span class="n">GenericIssueDetails</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;genericIssueDetails&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;genericIssueDetails&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">deprecation_issue_details</span><span class="o">=</span><span class="p">(</span>
                <span class="n">DeprecationIssueDetails</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;deprecationIssueDetails&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;deprecationIssueDetails&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">client_hint_issue_details</span><span class="o">=</span><span class="p">(</span>
                <span class="n">ClientHintIssueDetails</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;clientHintIssueDetails&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;clientHintIssueDetails&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">federated_auth_request_issue_details</span><span class="o">=</span><span class="p">(</span>
                <span class="n">FederatedAuthRequestIssueDetails</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span>
                    <span class="n">json</span><span class="p">[</span><span class="s2">&quot;federatedAuthRequestIssueDetails&quot;</span><span class="p">]</span>
                <span class="p">)</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;federatedAuthRequestIssueDetails&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">bounce_tracking_issue_details</span><span class="o">=</span><span class="p">(</span>
                <span class="n">BounceTrackingIssueDetails</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;bounceTrackingIssueDetails&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;bounceTrackingIssueDetails&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">cookie_deprecation_metadata_issue_details</span><span class="o">=</span><span class="p">(</span>
                <span class="n">CookieDeprecationMetadataIssueDetails</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span>
                    <span class="n">json</span><span class="p">[</span><span class="s2">&quot;cookieDeprecationMetadataIssueDetails&quot;</span><span class="p">]</span>
                <span class="p">)</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;cookieDeprecationMetadataIssueDetails&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">stylesheet_loading_issue_details</span><span class="o">=</span><span class="p">(</span>
                <span class="n">StylesheetLoadingIssueDetails</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span>
                    <span class="n">json</span><span class="p">[</span><span class="s2">&quot;stylesheetLoadingIssueDetails&quot;</span><span class="p">]</span>
                <span class="p">)</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;stylesheetLoadingIssueDetails&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">property_rule_issue_details</span><span class="o">=</span><span class="p">(</span>
                <span class="n">PropertyRuleIssueDetails</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;propertyRuleIssueDetails&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;propertyRuleIssueDetails&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">federated_auth_user_info_request_issue_details</span><span class="o">=</span><span class="p">(</span>
                <span class="n">FederatedAuthUserInfoRequestIssueDetails</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span>
                    <span class="n">json</span><span class="p">[</span><span class="s2">&quot;federatedAuthUserInfoRequestIssueDetails&quot;</span><span class="p">]</span>
                <span class="p">)</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;federatedAuthUserInfoRequestIssueDetails&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span>
                   <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">shared_dictionary_issue_details</span><span class="o">=</span><span class="p">(</span>
                <span class="n">SharedDictionaryIssueDetails</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span>
                    <span class="n">json</span><span class="p">[</span><span class="s2">&quot;sharedDictionaryIssueDetails&quot;</span><span class="p">]</span>
                <span class="p">)</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;sharedDictionaryIssueDetails&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">select_element_accessibility_issue_details</span><span class="o">=</span><span class="p">(</span>
                <span class="n">SelectElementAccessibilityIssueDetails</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span>
                    <span class="n">json</span><span class="p">[</span><span class="s2">&quot;selectElementAccessibilityIssueDetails&quot;</span><span class="p">]</span>
                <span class="p">)</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;selectElementAccessibilityIssueDetails&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">sri_message_signature_issue_details</span><span class="o">=</span><span class="p">(</span>
                <span class="n">SRIMessageSignatureIssueDetails</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span>
                    <span class="n">json</span><span class="p">[</span><span class="s2">&quot;sriMessageSignatureIssueDetails&quot;</span><span class="p">]</span>
                <span class="p">)</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;sriMessageSignatureIssueDetails&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="IssueId">
<a class="viewcode-back" href="../../../nodriver/cdp/audits.html#nodriver.cdp.audits.IssueId">[docs]</a>
<span class="k">class</span> <span class="nc">IssueId</span><span class="p">(</span><span class="nb">str</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    A unique id for a DevTools inspector issue. Allows other entities (e.g.</span>
<span class="sd">    exceptions, CDP message, console messages, etc.) to reference an issue.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">IssueId</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span>

    <span class="k">def</span> <span class="fm">__repr__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="s2">&quot;IssueId(</span><span class="si">{}</span><span class="s2">)&quot;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="fm">__repr__</span><span class="p">())</span></div>



<div class="viewcode-block" id="InspectorIssue">
<a class="viewcode-back" href="../../../nodriver/cdp/audits.html#nodriver.cdp.audits.InspectorIssue">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">InspectorIssue</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    An inspector issue reported from the back-end.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">code</span><span class="p">:</span> <span class="n">InspectorIssueCode</span>

    <span class="n">details</span><span class="p">:</span> <span class="n">InspectorIssueDetails</span>

    <span class="c1">#: A unique id for this issue. May be omitted if no other entity (e.g.</span>
    <span class="c1">#: exception, CDP message, etc.) is referencing this issue.</span>
    <span class="n">issue_id</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">IssueId</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;code&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">code</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;details&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">details</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">issue_id</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;issueId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">issue_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">InspectorIssue</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">code</span><span class="o">=</span><span class="n">InspectorIssueCode</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;code&quot;</span><span class="p">]),</span>
            <span class="n">details</span><span class="o">=</span><span class="n">InspectorIssueDetails</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;details&quot;</span><span class="p">]),</span>
            <span class="n">issue_id</span><span class="o">=</span><span class="p">(</span>
                <span class="n">IssueId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;issueId&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;issueId&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="get_encoded_response">
<a class="viewcode-back" href="../../../nodriver/cdp/audits.html#nodriver.cdp.audits.get_encoded_response">[docs]</a>
<span class="k">def</span> <span class="nf">get_encoded_response</span><span class="p">(</span>
        <span class="n">request_id</span><span class="p">:</span> <span class="n">network</span><span class="o">.</span><span class="n">RequestId</span><span class="p">,</span>
        <span class="n">encoding</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span>
        <span class="n">quality</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">float</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
        <span class="n">size_only</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">bool</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span>
    <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">typing</span><span class="o">.</span><span class="n">Tuple</span><span class="p">[</span><span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">],</span> <span class="nb">int</span><span class="p">,</span> <span class="nb">int</span><span class="p">]</span>
<span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Returns the response body and size if it were re-encoded with the specified settings. Only</span>
<span class="sd">    applies to images.</span>

<span class="sd">    :param request_id: Identifier of the network request to get content for.</span>
<span class="sd">    :param encoding: The encoding to use.</span>
<span class="sd">    :param quality: *(Optional)* The quality of the encoding (0-1). (defaults to 1)</span>
<span class="sd">    :param size_only: *(Optional)* Whether to only return the size information (defaults to false).</span>
<span class="sd">    :returns: A tuple with the following items:</span>

<span class="sd">        0. **body** - *(Optional)* The encoded body as a base64 string. Omitted if sizeOnly is true. (Encoded as a base64 string when passed over JSON)</span>
<span class="sd">        1. **originalSize** - Size before re-encoding.</span>
<span class="sd">        2. **encodedSize** - Size after re-encoding.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;requestId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">request_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;encoding&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">encoding</span>
    <span class="k">if</span> <span class="n">quality</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;quality&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">quality</span>
    <span class="k">if</span> <span class="n">size_only</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;sizeOnly&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">size_only</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Audits.getEncodedResponse&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span>
    <span class="k">return</span> <span class="p">(</span>
        <span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;body&quot;</span><span class="p">])</span> <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;body&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span> <span class="k">else</span> <span class="kc">None</span><span class="p">,</span>
        <span class="nb">int</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;originalSize&quot;</span><span class="p">]),</span>
        <span class="nb">int</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;encodedSize&quot;</span><span class="p">]),</span>
    <span class="p">)</span></div>



<div class="viewcode-block" id="disable">
<a class="viewcode-back" href="../../../nodriver/cdp/audits.html#nodriver.cdp.audits.disable">[docs]</a>
<span class="k">def</span> <span class="nf">disable</span><span class="p">()</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Disables issues domain, prevents further issues from being reported to the client.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Audits.disable&quot;</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span></div>



<div class="viewcode-block" id="enable">
<a class="viewcode-back" href="../../../nodriver/cdp/audits.html#nodriver.cdp.audits.enable">[docs]</a>
<span class="k">def</span> <span class="nf">enable</span><span class="p">()</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Enables issues domain, sends the issues collected so far to the client by means of the</span>
<span class="sd">    ``issueAdded`` event.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Audits.enable&quot;</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span></div>



<div class="viewcode-block" id="check_contrast">
<a class="viewcode-back" href="../../../nodriver/cdp/audits.html#nodriver.cdp.audits.check_contrast">[docs]</a>
<span class="k">def</span> <span class="nf">check_contrast</span><span class="p">(</span>
        <span class="n">report_aaa</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">bool</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Runs the contrast check for the target page. Found issues are reported</span>
<span class="sd">    using Audits.issueAdded event.</span>

<span class="sd">    :param report_aaa: *(Optional)* Whether to report WCAG AAA level issues. Default is false.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="k">if</span> <span class="n">report_aaa</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;reportAAA&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">report_aaa</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Audits.checkContrast&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span></div>



<div class="viewcode-block" id="check_forms_issues">
<a class="viewcode-back" href="../../../nodriver/cdp/audits.html#nodriver.cdp.audits.check_forms_issues">[docs]</a>
<span class="k">def</span> <span class="nf">check_forms_issues</span><span class="p">()</span> <span class="o">-&gt;</span> <span class="p">(</span>
        <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">GenericIssueDetails</span><span class="p">]]</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Runs the form issues check for the target page. Found issues are reported</span>
<span class="sd">    using Audits.issueAdded event.</span>

<span class="sd">    :returns:</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Audits.checkFormsIssues&quot;</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span>
    <span class="k">return</span> <span class="p">[</span><span class="n">GenericIssueDetails</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;formIssues&quot;</span><span class="p">]]</span></div>



<div class="viewcode-block" id="IssueAdded">
<a class="viewcode-back" href="../../../nodriver/cdp/audits.html#nodriver.cdp.audits.IssueAdded">[docs]</a>
<span class="nd">@event_class</span><span class="p">(</span><span class="s2">&quot;Audits.issueAdded&quot;</span><span class="p">)</span>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">IssueAdded</span><span class="p">:</span>
    <span class="n">issue</span><span class="p">:</span> <span class="n">InspectorIssue</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">IssueAdded</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">issue</span><span class="o">=</span><span class="n">InspectorIssue</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;issue&quot;</span><span class="p">]))</span></div>

</pre></div>
        </article>
      </div>
      <footer>
        
        <div class="related-pages">
          
          
        </div>
        <div class="bottom-of-page">
          <div class="left-details">
            <div class="copyright">
                Copyright &#169; 2023, Author
            </div>
            Made with <a href="https://www.sphinx-doc.org/">Sphinx</a> and <a class="muted-link" href="https://pradyunsg.me">@pradyunsg</a>'s
            
            <a href="https://github.com/pradyunsg/furo">Furo</a>
            
          </div>
          <div class="right-details">
            
          </div>
        </div>
        
      </footer>
    </div>
    <aside class="toc-drawer no-toc">
      
      
      
    </aside>
  </div>
</div><script src="../../../_static/documentation_options.js?v=5929fcd5"></script>
    <script src="../../../_static/doctools.js?v=888ff710"></script>
    <script src="../../../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../../../_static/scripts/furo.js?v=32e29ea5"></script>
    </body>
</html>