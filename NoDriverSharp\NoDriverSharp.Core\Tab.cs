using System.Text.Json;
using Microsoft.Extensions.Logging;
using NoDriverSharp.CDP.Common;
using NoDriverSharp.CDP.Domains;

namespace NoDriverSharp.Core;

/// <summary>
/// Represents a browser tab/page and provides methods for navigation and DOM interaction
/// </summary>
public class Tab : IAsyncDisposable
{
    private readonly ILogger _logger;
    private readonly Browser _browser;
    private readonly string _targetId;
    private readonly string _websocketUrl;
    private Connection? _connection;
    private bool _disposed = false;

    /// <summary>
    /// The browser instance this tab belongs to
    /// </summary>
    public Browser Browser => _browser;

    /// <summary>
    /// The target ID for this tab
    /// </summary>
    public string TargetId => _targetId;

    /// <summary>
    /// The current URL of this tab
    /// </summary>
    public string Url { get; private set; } = string.Empty;

    /// <summary>
    /// The title of this tab
    /// </summary>
    public string Title { get; private set; } = string.Empty;

    /// <summary>
    /// Whether this tab is currently active/focused
    /// </summary>
    public bool IsActive { get; private set; }

    /// <summary>
    /// The WebSocket URL for this tab's connection
    /// </summary>
    public string WebSocketUrl => _websocketUrl;

    /// <summary>
    /// Event fired when the page navigates to a new URL
    /// </summary>
    public event EventHandler<string>? Navigated;

    /// <summary>
    /// Event fired when the page title changes
    /// </summary>
    public event EventHandler<string>? TitleChanged;

    private Tab(string targetId, string websocketUrl, Browser browser, ILogger logger)
    {
        _targetId = targetId ?? throw new ArgumentNullException(nameof(targetId));
        _websocketUrl = websocketUrl ?? throw new ArgumentNullException(nameof(websocketUrl));
        _browser = browser ?? throw new ArgumentNullException(nameof(browser));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Creates a new Tab instance from target information
    /// </summary>
    internal static async Task<Tab> CreateFromTargetAsync(JsonElement targetInfo, Browser browser, ILogger logger, CancellationToken cancellationToken = default)
    {
        var targetId = targetInfo.GetProperty("id").GetString()!;
        var websocketUrl = targetInfo.GetProperty("webSocketDebuggerUrl").GetString()!;
        var url = targetInfo.TryGetProperty("url", out var urlProp) ? urlProp.GetString() ?? string.Empty : string.Empty;
        var title = targetInfo.TryGetProperty("title", out var titleProp) ? titleProp.GetString() ?? string.Empty : string.Empty;

        var tab = new Tab(targetId, websocketUrl, browser, logger)
        {
            Url = url,
            Title = title
        };

        await tab.ConnectAsync(cancellationToken);
        return tab;
    }

    /// <summary>
    /// Establishes connection to this tab
    /// </summary>
    private async Task ConnectAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(Tab));

        _connection = new Connection(_websocketUrl, _logger);
        await _connection.ConnectAsync(cancellationToken);

        // Enable required domains
        await _connection.SendCommandAsync(new Page.Enable(), cancellationToken);
        await _connection.SendCommandAsync(new DOM.Enable(), cancellationToken);

        // Set up event handlers
        _connection.AddEventHandler("Page.frameNavigated", OnFrameNavigated);
        _connection.AddEventHandler("Page.loadEventFired", OnLoadEventFired);

        _logger.LogDebug("Connected to tab {TargetId}", _targetId);
    }

    /// <summary>
    /// Navigates to the specified URL
    /// </summary>
    public async Task<Tab> NavigateAsync(string url, CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(Tab));

        if (_connection == null)
            throw new InvalidOperationException("Tab is not connected");

        try
        {
            _logger.LogDebug("Navigating to: {Url}", url);
            
            var command = new Page.Navigate(url);
            var result = await _connection.SendCommandAsync<Page.Navigate.Result>(command, cancellationToken);

            if (!string.IsNullOrEmpty(result.ErrorText))
            {
                throw new NavigationException($"Navigation failed: {result.ErrorText}");
            }

            // Wait for navigation to complete
            await WaitForNavigationAsync(cancellationToken);

            _logger.LogDebug("Navigation completed successfully");
            return this;
        }
        catch (Exception ex) when (!(ex is NavigationException))
        {
            _logger.LogError(ex, "Failed to navigate to {Url}", url);
            throw new NavigationException($"Failed to navigate to {url}", ex);
        }
    }

    /// <summary>
    /// Reloads the current page
    /// </summary>
    public async Task<Tab> ReloadAsync(bool ignoreCache = false, CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(Tab));

        if (_connection == null)
            throw new InvalidOperationException("Tab is not connected");

        try
        {
            _logger.LogDebug("Reloading page (ignoreCache: {IgnoreCache})", ignoreCache);
            
            var command = new Page.Reload(ignoreCache);
            await _connection.SendCommandAsync(command, cancellationToken);

            // Wait for reload to complete
            await WaitForNavigationAsync(cancellationToken);

            _logger.LogDebug("Page reloaded successfully");
            return this;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to reload page");
            throw new NavigationException("Failed to reload page", ex);
        }
    }

    /// <summary>
    /// Waits for navigation to complete
    /// </summary>
    private async Task WaitForNavigationAsync(CancellationToken cancellationToken = default)
    {
        var timeout = DateTime.UtcNow.Add(_browser.Config.NavigationTimeout);
        var navigationCompleted = false;

        // Set up temporary event handler for load completion
        Task OnLoadComplete(JsonElement _)
        {
            navigationCompleted = true;
            return Task.CompletedTask;
        }

        _connection!.AddEventHandler("Page.loadEventFired", OnLoadComplete);

        try
        {
            while (!navigationCompleted && DateTime.UtcNow < timeout)
            {
                await Task.Delay(100, cancellationToken);
            }

            if (!navigationCompleted)
            {
                throw new TimeoutException(_browser.Config.NavigationTimeout, "Navigation did not complete within timeout");
            }
        }
        finally
        {
            _connection.RemoveEventHandler("Page.loadEventFired", OnLoadComplete);
        }
    }

    /// <summary>
    /// Takes a screenshot of the current page
    /// </summary>
    public async Task<byte[]> CaptureScreenshotAsync(string format = "png", int? quality = null, CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(Tab));

        if (_connection == null)
            throw new InvalidOperationException("Tab is not connected");

        try
        {
            var command = new Page.CaptureScreenshot(format, quality);
            var result = await _connection.SendCommandAsync<Page.CaptureScreenshot.Result>(command, cancellationToken);
            
            return Convert.FromBase64String(result.Data);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to capture screenshot");
            throw new ElementException("Failed to capture screenshot", ex);
        }
    }

    /// <summary>
    /// Saves a screenshot to the specified file path
    /// </summary>
    public async Task SaveScreenshotAsync(string filePath, string format = "png", int? quality = null, CancellationToken cancellationToken = default)
    {
        var screenshotData = await CaptureScreenshotAsync(format, quality, cancellationToken);
        await File.WriteAllBytesAsync(filePath, screenshotData, cancellationToken);
    }

    /// <summary>
    /// Closes this tab
    /// </summary>
    public async Task CloseAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed)
            return;

        try
        {
            if (_connection != null)
            {
                await _connection.CloseAsync();
                _connection.Dispose();
                _connection = null;
            }

            // Close the tab via HTTP API
            var httpClient = new HttpClient();
            await httpClient.GetAsync($"http://{_browser.Config.Host}:{_browser.Config.Port}/json/close/{_targetId}", cancellationToken);

            _logger.LogDebug("Tab {TargetId} closed", _targetId);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error while closing tab {TargetId}", _targetId);
        }
    }

    /// <summary>
    /// Event handler for frame navigation
    /// </summary>
    private Task OnFrameNavigated(JsonElement eventData)
    {
        try
        {
            if (eventData.TryGetProperty("frame", out var frame) &&
                frame.TryGetProperty("url", out var urlProp))
            {
                var newUrl = urlProp.GetString() ?? string.Empty;
                if (Url != newUrl)
                {
                    Url = newUrl;
                    Navigated?.Invoke(this, newUrl);
                    _logger.LogDebug("Tab navigated to: {Url}", newUrl);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error handling frame navigation event");
        }

        return Task.CompletedTask;
    }

    /// <summary>
    /// Event handler for load event
    /// </summary>
    private Task OnLoadEventFired(JsonElement eventData)
    {
        _logger.LogTrace("Page load event fired for tab {TargetId}", _targetId);
        return Task.CompletedTask;
    }

    public async ValueTask DisposeAsync()
    {
        if (!_disposed)
        {
            _disposed = true;
            await CloseAsync();
        }
    }
}
