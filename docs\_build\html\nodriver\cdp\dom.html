<!doctype html>
<html class="no-js" lang="en" data-content_root="../../">
  <head><meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width,initial-scale=1"/>
    <meta name="color-scheme" content="light dark"><meta name="viewport" content="width=device-width, initial-scale=1" />
<link rel="index" title="Index" href="../../genindex.html" /><link rel="search" title="Search" href="../../search.html" /><link rel="next" title="DOMDebugger" href="dom_debugger.html" /><link rel="prev" title="DeviceOrientation" href="device_orientation.html" />

    <!-- Generated with Sphinx 7.2.6 and Furo 2023.09.10 -->
        <title>DOM - nodriver documentation</title>
      <link rel="stylesheet" type="text/css" href="../../_static/pygments.css?v=fa44fd50" />
    <link rel="stylesheet" type="text/css" href="../../_static/styles/furo.css?v=135e06be" />
    <link rel="stylesheet" type="text/css" href="../../_static/styles/furo-extensions.css?v=36a5483c" />
    <link rel="stylesheet" type="text/css" href="../../_static/./custom.css?v=3934642d" />
    
    


<style>
  body {
    --color-code-background: #eeffcc;
  --color-code-foreground: black;
  
  }
  @media not print {
    body[data-theme="dark"] {
      --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8eefba;
  --color-background-primary: #111112;
  --color-problematic: #e7aeae;
  
    }
    @media (prefers-color-scheme: dark) {
      body:not([data-theme="light"]) {
        --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8eefba;
  --color-background-primary: #111112;
  --color-problematic: #e7aeae;
  
      }
    }
  }
</style></head>
  <body>
    
    <script>
      document.body.dataset.theme = localStorage.getItem("theme") || "auto";
    </script>
    

<svg xmlns="http://www.w3.org/2000/svg" style="display: none;">
  <symbol id="svg-toc" viewBox="0 0 24 24">
    <title>Contents</title>
    <svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 1024 1024">
      <path d="M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM115.4 518.9L271.7 642c5.8 4.6 14.4.5 14.4-6.9V388.9c0-7.4-8.5-11.5-14.4-6.9L115.4 505.1a8.74 8.74 0 0 0 0 13.8z"/>
    </svg>
  </symbol>
  <symbol id="svg-menu" viewBox="0 0 24 24">
    <title>Menu</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-menu">
      <line x1="3" y1="12" x2="21" y2="12"></line>
      <line x1="3" y1="6" x2="21" y2="6"></line>
      <line x1="3" y1="18" x2="21" y2="18"></line>
    </svg>
  </symbol>
  <symbol id="svg-arrow-right" viewBox="0 0 24 24">
    <title>Expand</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-chevron-right">
      <polyline points="9 18 15 12 9 6"></polyline>
    </svg>
  </symbol>
  <symbol id="svg-sun" viewBox="0 0 24 24">
    <title>Light mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="feather-sun">
      <circle cx="12" cy="12" r="5"></circle>
      <line x1="12" y1="1" x2="12" y2="3"></line>
      <line x1="12" y1="21" x2="12" y2="23"></line>
      <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
      <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
      <line x1="1" y1="12" x2="3" y2="12"></line>
      <line x1="21" y1="12" x2="23" y2="12"></line>
      <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
      <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
    </svg>
  </symbol>
  <symbol id="svg-moon" viewBox="0 0 24 24">
    <title>Dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-moon">
      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
      <path d="M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z" />
    </svg>
  </symbol>
  <symbol id="svg-sun-half" viewBox="0 0 24 24">
    <title>Auto light/dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-shadow">
      <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
      <circle cx="12" cy="12" r="9" />
      <path d="M13 12h5" />
      <path d="M13 15h4" />
      <path d="M13 18h1" />
      <path d="M13 9h4" />
      <path d="M13 6h1" />
    </svg>
  </symbol>
</svg>

<input type="checkbox" class="sidebar-toggle" name="__navigation" id="__navigation">
<input type="checkbox" class="sidebar-toggle" name="__toc" id="__toc">
<label class="overlay sidebar-overlay" for="__navigation">
  <div class="visually-hidden">Hide navigation sidebar</div>
</label>
<label class="overlay toc-overlay" for="__toc">
  <div class="visually-hidden">Hide table of contents sidebar</div>
</label>



<div class="page">
  <header class="mobile-header">
    <div class="header-left">
      <label class="nav-overlay-icon" for="__navigation">
        <div class="visually-hidden">Toggle site navigation sidebar</div>
        <i class="icon"><svg><use href="#svg-menu"></use></svg></i>
      </label>
    </div>
    <div class="header-center">
      <a href="../../index.html"><div class="brand">nodriver  documentation</div></a>
    </div>
    <div class="header-right">
      <div class="theme-toggle-container theme-toggle-header">
        <button class="theme-toggle">
          <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
          <svg class="theme-icon-when-auto"><use href="#svg-sun-half"></use></svg>
          <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
          <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
        </button>
      </div>
      <label class="toc-overlay-icon toc-header-icon" for="__toc">
        <div class="visually-hidden">Toggle table of contents sidebar</div>
        <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
      </label>
    </div>
  </header>
  <aside class="sidebar-drawer">
    <div class="sidebar-container">
      
      <div class="sidebar-sticky"><div class="sidebar-scroll"><a class="sidebar-brand" href="../../index.html">
  
  
  <span class="sidebar-brand-text">nodriver  documentation</span>
  
</a><form class="sidebar-search-container" method="get" action="../../search.html" role="search">
  <input class="sidebar-search" placeholder="Search" name="q" aria-label="Search">
  <input type="hidden" name="check_keywords" value="yes">
  <input type="hidden" name="area" value="default">
</form>
<div id="searchbox"></div><div class="sidebar-tree">
  <ul>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html">Quickstart guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#usage-example">usage example</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#more-complete-example">More complete example</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#custom-starting-options">Custom starting options</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#alternative-custom-options">Alternative custom options</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#proxies-socks5-authenticated-too">Proxies (socks5, authenticated too)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#concrete-example">Concrete example</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../classes/browser.html">Browser class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../classes/tab.html">Tab class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../classes/element.html">Element class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../classes/others_and_helpers.html">Other classes and Helper classes</a></li>
</ul>
<ul class="current">
<li class="toctree-l1 current has-children"><a class="reference internal" href="../cdp.html">CDP object</a><input checked="" class="toctree-checkbox" id="toctree-checkbox-1" name="toctree-checkbox-1" role="switch" type="checkbox"/><label for="toctree-checkbox-1"><div class="visually-hidden">Toggle navigation of CDP object</div><i class="icon"><svg><use href="#svg-arrow-right"></use></svg></i></label><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="accessibility.html">Accessibility</a></li>
<li class="toctree-l2"><a class="reference internal" href="animation.html">Animation</a></li>
<li class="toctree-l2"><a class="reference internal" href="audits.html">Audits</a></li>
<li class="toctree-l2"><a class="reference internal" href="autofill.html">Autofill</a></li>
<li class="toctree-l2"><a class="reference internal" href="background_service.html">BackgroundService</a></li>
<li class="toctree-l2"><a class="reference internal" href="bluetooth_emulation.html">BluetoothEmulation</a></li>
<li class="toctree-l2"><a class="reference internal" href="browser.html">Browser</a></li>
<li class="toctree-l2"><a class="reference internal" href="cache_storage.html">CacheStorage</a></li>
<li class="toctree-l2"><a class="reference internal" href="cast.html">Cast</a></li>
<li class="toctree-l2"><a class="reference internal" href="console.html">Console</a></li>
<li class="toctree-l2"><a class="reference internal" href="css.html">CSS</a></li>
<li class="toctree-l2"><a class="reference internal" href="debugger.html">Debugger</a></li>
<li class="toctree-l2"><a class="reference internal" href="device_access.html">DeviceAccess</a></li>
<li class="toctree-l2"><a class="reference internal" href="device_orientation.html">DeviceOrientation</a></li>
<li class="toctree-l2 current current-page"><a class="current reference internal" href="#">DOM</a></li>
<li class="toctree-l2"><a class="reference internal" href="dom_debugger.html">DOMDebugger</a></li>
<li class="toctree-l2"><a class="reference internal" href="dom_snapshot.html">DOMSnapshot</a></li>
<li class="toctree-l2"><a class="reference internal" href="dom_storage.html">DOMStorage</a></li>
<li class="toctree-l2"><a class="reference internal" href="emulation.html">Emulation</a></li>
<li class="toctree-l2"><a class="reference internal" href="event_breakpoints.html">EventBreakpoints</a></li>
<li class="toctree-l2"><a class="reference internal" href="extensions.html">Extensions</a></li>
<li class="toctree-l2"><a class="reference internal" href="fed_cm.html">FedCm</a></li>
<li class="toctree-l2"><a class="reference internal" href="fetch.html">Fetch</a></li>
<li class="toctree-l2"><a class="reference internal" href="file_system.html">FileSystem</a></li>
<li class="toctree-l2"><a class="reference internal" href="headless_experimental.html">HeadlessExperimental</a></li>
<li class="toctree-l2"><a class="reference internal" href="heap_profiler.html">HeapProfiler</a></li>
<li class="toctree-l2"><a class="reference internal" href="indexed_db.html">IndexedDB</a></li>
<li class="toctree-l2"><a class="reference internal" href="input_.html">Input</a></li>
<li class="toctree-l2"><a class="reference internal" href="inspector.html">Inspector</a></li>
<li class="toctree-l2"><a class="reference internal" href="io.html">IO</a></li>
<li class="toctree-l2"><a class="reference internal" href="layer_tree.html">LayerTree</a></li>
<li class="toctree-l2"><a class="reference internal" href="log.html">Log</a></li>
<li class="toctree-l2"><a class="reference internal" href="media.html">Media</a></li>
<li class="toctree-l2"><a class="reference internal" href="memory.html">Memory</a></li>
<li class="toctree-l2"><a class="reference internal" href="network.html">Network</a></li>
<li class="toctree-l2"><a class="reference internal" href="overlay.html">Overlay</a></li>
<li class="toctree-l2"><a class="reference internal" href="page.html">Page</a></li>
<li class="toctree-l2"><a class="reference internal" href="performance.html">Performance</a></li>
<li class="toctree-l2"><a class="reference internal" href="performance_timeline.html">PerformanceTimeline</a></li>
<li class="toctree-l2"><a class="reference internal" href="preload.html">Preload</a></li>
<li class="toctree-l2"><a class="reference internal" href="profiler.html">Profiler</a></li>
<li class="toctree-l2"><a class="reference internal" href="pwa.html">PWA</a></li>
<li class="toctree-l2"><a class="reference internal" href="runtime.html">Runtime</a></li>
<li class="toctree-l2"><a class="reference internal" href="schema.html">Schema</a></li>
<li class="toctree-l2"><a class="reference internal" href="security.html">Security</a></li>
<li class="toctree-l2"><a class="reference internal" href="service_worker.html">ServiceWorker</a></li>
<li class="toctree-l2"><a class="reference internal" href="storage.html">Storage</a></li>
<li class="toctree-l2"><a class="reference internal" href="system_info.html">SystemInfo</a></li>
<li class="toctree-l2"><a class="reference internal" href="target.html">Target</a></li>
<li class="toctree-l2"><a class="reference internal" href="tethering.html">Tethering</a></li>
<li class="toctree-l2"><a class="reference internal" href="tracing.html">Tracing</a></li>
<li class="toctree-l2"><a class="reference internal" href="web_audio.html">WebAudio</a></li>
<li class="toctree-l2"><a class="reference internal" href="web_authn.html">WebAuthn</a></li>
</ul>
</li>
</ul>

</div>
</div>
      </div>
      
    </div>
  </aside>
  <div class="main">
    <div class="content">
      <div class="article-container">
        <a href="#" class="back-to-top muted-link">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
            <path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8v12z"></path>
          </svg>
          <span>Back to top</span>
        </a>
        <div class="content-icon-container">
          
<div class="theme-toggle-container theme-toggle-content">
            <button class="theme-toggle">
              <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
              <svg class="theme-icon-when-auto"><use href="#svg-sun-half"></use></svg>
              <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
              <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
            </button>
          </div>
          <label class="toc-overlay-icon toc-content-icon" for="__toc">
            <div class="visually-hidden">Toggle table of contents sidebar</div>
            <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
          </label>
        </div>
        <article role="main">
          <section id="dom">
<h1>DOM<a class="headerlink" href="#dom" title="Link to this heading">#</a></h1>
<p>This domain exposes DOM read/write operations. Each DOM Node is represented with its mirror object
that has an <a class="reference external" href="https://docs.python.org/3/library/functions.html#id" title="(in Python v3.13)"><code class="xref any docutils literal notranslate"><span class="pre">id</span></code></a>. This <a class="reference external" href="https://docs.python.org/3/library/functions.html#id" title="(in Python v3.13)"><code class="xref any docutils literal notranslate"><span class="pre">id</span></code></a> can be used to get additional information on the Node, resolve it into
the JavaScript object wrapper, etc. It is important that client receives DOM events only for the
nodes that are known to the client. Backend keeps track of the nodes that were sent to the client
and never sends the same node twice. It is client’s responsibility to collect information about
the nodes that were sent to the client. Note that <code class="xref any docutils literal notranslate"><span class="pre">iframe</span></code> owner elements will return
corresponding document elements as their child nodes.</p>
<ul class="simple" id="module-nodriver.cdp.dom">
<li><p><a class="reference internal" href="#types">Types</a></p></li>
<li><p><a class="reference internal" href="#commands">Commands</a></p></li>
<li><p><a class="reference internal" href="#events">Events</a></p></li>
</ul>
<section id="types">
<h2>Types<a class="headerlink" href="#types" title="Link to this heading">#</a></h2>
<p>Generally, you do not need to instantiate CDP types
yourself. Instead, the API creates objects for you as return
values from commands, and then you can use those objects as
arguments to other commands.</p>
<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.dom.NodeId">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">NodeId</span></span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#NodeId"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.NodeId" title="Link to this definition">#</a></dt>
<dd><p>Unique DOM node identifier.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.dom.BackendNodeId">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">BackendNodeId</span></span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#BackendNodeId"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.BackendNodeId" title="Link to this definition">#</a></dt>
<dd><p>Unique DOM node identifier used to reference a node that may not have been pushed to the
front-end.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.dom.BackendNode">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">BackendNode</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">node_type</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">node_name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">backend_node_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#BackendNode"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.BackendNode" title="Link to this definition">#</a></dt>
<dd><p>Backend node with a friendly name.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.BackendNode.node_type">
<span class="sig-name descname"><span class="pre">node_type</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom.BackendNode.node_type" title="Link to this definition">#</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">Node</span></code>’s nodeType.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.BackendNode.node_name">
<span class="sig-name descname"><span class="pre">node_name</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom.BackendNode.node_name" title="Link to this definition">#</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">Node</span></code>’s nodeName.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.BackendNode.backend_node_id">
<span class="sig-name descname"><span class="pre">backend_node_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.dom.BackendNodeId" title="nodriver.cdp.dom.BackendNodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">BackendNodeId</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom.BackendNode.backend_node_id" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.dom.PseudoType">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">PseudoType</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">names</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">module</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">qualname</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">boundary</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#PseudoType"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.PseudoType" title="Link to this definition">#</a></dt>
<dd><p>Pseudo element type.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.PseudoType.FIRST_LINE">
<span class="sig-name descname"><span class="pre">FIRST_LINE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'first-line'</span></em><a class="headerlink" href="#nodriver.cdp.dom.PseudoType.FIRST_LINE" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.PseudoType.FIRST_LETTER">
<span class="sig-name descname"><span class="pre">FIRST_LETTER</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'first-letter'</span></em><a class="headerlink" href="#nodriver.cdp.dom.PseudoType.FIRST_LETTER" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.PseudoType.CHECKMARK">
<span class="sig-name descname"><span class="pre">CHECKMARK</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'checkmark'</span></em><a class="headerlink" href="#nodriver.cdp.dom.PseudoType.CHECKMARK" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.PseudoType.BEFORE">
<span class="sig-name descname"><span class="pre">BEFORE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'before'</span></em><a class="headerlink" href="#nodriver.cdp.dom.PseudoType.BEFORE" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.PseudoType.AFTER">
<span class="sig-name descname"><span class="pre">AFTER</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'after'</span></em><a class="headerlink" href="#nodriver.cdp.dom.PseudoType.AFTER" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.PseudoType.PICKER_ICON">
<span class="sig-name descname"><span class="pre">PICKER_ICON</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'picker-icon'</span></em><a class="headerlink" href="#nodriver.cdp.dom.PseudoType.PICKER_ICON" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.PseudoType.MARKER">
<span class="sig-name descname"><span class="pre">MARKER</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'marker'</span></em><a class="headerlink" href="#nodriver.cdp.dom.PseudoType.MARKER" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.PseudoType.BACKDROP">
<span class="sig-name descname"><span class="pre">BACKDROP</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'backdrop'</span></em><a class="headerlink" href="#nodriver.cdp.dom.PseudoType.BACKDROP" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.PseudoType.COLUMN">
<span class="sig-name descname"><span class="pre">COLUMN</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'column'</span></em><a class="headerlink" href="#nodriver.cdp.dom.PseudoType.COLUMN" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.PseudoType.SELECTION">
<span class="sig-name descname"><span class="pre">SELECTION</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'selection'</span></em><a class="headerlink" href="#nodriver.cdp.dom.PseudoType.SELECTION" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.PseudoType.SEARCH_TEXT">
<span class="sig-name descname"><span class="pre">SEARCH_TEXT</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'search-text'</span></em><a class="headerlink" href="#nodriver.cdp.dom.PseudoType.SEARCH_TEXT" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.PseudoType.TARGET_TEXT">
<span class="sig-name descname"><span class="pre">TARGET_TEXT</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'target-text'</span></em><a class="headerlink" href="#nodriver.cdp.dom.PseudoType.TARGET_TEXT" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.PseudoType.SPELLING_ERROR">
<span class="sig-name descname"><span class="pre">SPELLING_ERROR</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'spelling-error'</span></em><a class="headerlink" href="#nodriver.cdp.dom.PseudoType.SPELLING_ERROR" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.PseudoType.GRAMMAR_ERROR">
<span class="sig-name descname"><span class="pre">GRAMMAR_ERROR</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'grammar-error'</span></em><a class="headerlink" href="#nodriver.cdp.dom.PseudoType.GRAMMAR_ERROR" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.PseudoType.HIGHLIGHT">
<span class="sig-name descname"><span class="pre">HIGHLIGHT</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'highlight'</span></em><a class="headerlink" href="#nodriver.cdp.dom.PseudoType.HIGHLIGHT" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.PseudoType.FIRST_LINE_INHERITED">
<span class="sig-name descname"><span class="pre">FIRST_LINE_INHERITED</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'first-line-inherited'</span></em><a class="headerlink" href="#nodriver.cdp.dom.PseudoType.FIRST_LINE_INHERITED" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.PseudoType.SCROLL_MARKER">
<span class="sig-name descname"><span class="pre">SCROLL_MARKER</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'scroll-marker'</span></em><a class="headerlink" href="#nodriver.cdp.dom.PseudoType.SCROLL_MARKER" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.PseudoType.SCROLL_MARKER_GROUP">
<span class="sig-name descname"><span class="pre">SCROLL_MARKER_GROUP</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'scroll-marker-group'</span></em><a class="headerlink" href="#nodriver.cdp.dom.PseudoType.SCROLL_MARKER_GROUP" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.PseudoType.SCROLL_BUTTON">
<span class="sig-name descname"><span class="pre">SCROLL_BUTTON</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'scroll-button'</span></em><a class="headerlink" href="#nodriver.cdp.dom.PseudoType.SCROLL_BUTTON" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.PseudoType.SCROLLBAR">
<span class="sig-name descname"><span class="pre">SCROLLBAR</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'scrollbar'</span></em><a class="headerlink" href="#nodriver.cdp.dom.PseudoType.SCROLLBAR" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.PseudoType.SCROLLBAR_THUMB">
<span class="sig-name descname"><span class="pre">SCROLLBAR_THUMB</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'scrollbar-thumb'</span></em><a class="headerlink" href="#nodriver.cdp.dom.PseudoType.SCROLLBAR_THUMB" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.PseudoType.SCROLLBAR_BUTTON">
<span class="sig-name descname"><span class="pre">SCROLLBAR_BUTTON</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'scrollbar-button'</span></em><a class="headerlink" href="#nodriver.cdp.dom.PseudoType.SCROLLBAR_BUTTON" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.PseudoType.SCROLLBAR_TRACK">
<span class="sig-name descname"><span class="pre">SCROLLBAR_TRACK</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'scrollbar-track'</span></em><a class="headerlink" href="#nodriver.cdp.dom.PseudoType.SCROLLBAR_TRACK" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.PseudoType.SCROLLBAR_TRACK_PIECE">
<span class="sig-name descname"><span class="pre">SCROLLBAR_TRACK_PIECE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'scrollbar-track-piece'</span></em><a class="headerlink" href="#nodriver.cdp.dom.PseudoType.SCROLLBAR_TRACK_PIECE" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.PseudoType.SCROLLBAR_CORNER">
<span class="sig-name descname"><span class="pre">SCROLLBAR_CORNER</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'scrollbar-corner'</span></em><a class="headerlink" href="#nodriver.cdp.dom.PseudoType.SCROLLBAR_CORNER" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.PseudoType.RESIZER">
<span class="sig-name descname"><span class="pre">RESIZER</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'resizer'</span></em><a class="headerlink" href="#nodriver.cdp.dom.PseudoType.RESIZER" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.PseudoType.INPUT_LIST_BUTTON">
<span class="sig-name descname"><span class="pre">INPUT_LIST_BUTTON</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'input-list-button'</span></em><a class="headerlink" href="#nodriver.cdp.dom.PseudoType.INPUT_LIST_BUTTON" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.PseudoType.VIEW_TRANSITION">
<span class="sig-name descname"><span class="pre">VIEW_TRANSITION</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'view-transition'</span></em><a class="headerlink" href="#nodriver.cdp.dom.PseudoType.VIEW_TRANSITION" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.PseudoType.VIEW_TRANSITION_GROUP">
<span class="sig-name descname"><span class="pre">VIEW_TRANSITION_GROUP</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'view-transition-group'</span></em><a class="headerlink" href="#nodriver.cdp.dom.PseudoType.VIEW_TRANSITION_GROUP" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.PseudoType.VIEW_TRANSITION_IMAGE_PAIR">
<span class="sig-name descname"><span class="pre">VIEW_TRANSITION_IMAGE_PAIR</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'view-transition-image-pair'</span></em><a class="headerlink" href="#nodriver.cdp.dom.PseudoType.VIEW_TRANSITION_IMAGE_PAIR" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.PseudoType.VIEW_TRANSITION_OLD">
<span class="sig-name descname"><span class="pre">VIEW_TRANSITION_OLD</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'view-transition-old'</span></em><a class="headerlink" href="#nodriver.cdp.dom.PseudoType.VIEW_TRANSITION_OLD" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.PseudoType.VIEW_TRANSITION_NEW">
<span class="sig-name descname"><span class="pre">VIEW_TRANSITION_NEW</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'view-transition-new'</span></em><a class="headerlink" href="#nodriver.cdp.dom.PseudoType.VIEW_TRANSITION_NEW" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.PseudoType.PLACEHOLDER">
<span class="sig-name descname"><span class="pre">PLACEHOLDER</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'placeholder'</span></em><a class="headerlink" href="#nodriver.cdp.dom.PseudoType.PLACEHOLDER" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.PseudoType.FILE_SELECTOR_BUTTON">
<span class="sig-name descname"><span class="pre">FILE_SELECTOR_BUTTON</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'file-selector-button'</span></em><a class="headerlink" href="#nodriver.cdp.dom.PseudoType.FILE_SELECTOR_BUTTON" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.PseudoType.DETAILS_CONTENT">
<span class="sig-name descname"><span class="pre">DETAILS_CONTENT</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'details-content'</span></em><a class="headerlink" href="#nodriver.cdp.dom.PseudoType.DETAILS_CONTENT" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.PseudoType.PICKER">
<span class="sig-name descname"><span class="pre">PICKER</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'picker'</span></em><a class="headerlink" href="#nodriver.cdp.dom.PseudoType.PICKER" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.dom.ShadowRootType">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">ShadowRootType</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">names</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">module</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">qualname</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">boundary</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#ShadowRootType"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.ShadowRootType" title="Link to this definition">#</a></dt>
<dd><p>Shadow root type.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.ShadowRootType.USER_AGENT">
<span class="sig-name descname"><span class="pre">USER_AGENT</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'user-agent'</span></em><a class="headerlink" href="#nodriver.cdp.dom.ShadowRootType.USER_AGENT" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.ShadowRootType.OPEN_">
<span class="sig-name descname"><span class="pre">OPEN_</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'open'</span></em><a class="headerlink" href="#nodriver.cdp.dom.ShadowRootType.OPEN_" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.ShadowRootType.CLOSED">
<span class="sig-name descname"><span class="pre">CLOSED</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'closed'</span></em><a class="headerlink" href="#nodriver.cdp.dom.ShadowRootType.CLOSED" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.dom.CompatibilityMode">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">CompatibilityMode</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">names</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">module</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">qualname</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">boundary</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#CompatibilityMode"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.CompatibilityMode" title="Link to this definition">#</a></dt>
<dd><p>Document compatibility mode.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.CompatibilityMode.QUIRKS_MODE">
<span class="sig-name descname"><span class="pre">QUIRKS_MODE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'QuirksMode'</span></em><a class="headerlink" href="#nodriver.cdp.dom.CompatibilityMode.QUIRKS_MODE" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.CompatibilityMode.LIMITED_QUIRKS_MODE">
<span class="sig-name descname"><span class="pre">LIMITED_QUIRKS_MODE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'LimitedQuirksMode'</span></em><a class="headerlink" href="#nodriver.cdp.dom.CompatibilityMode.LIMITED_QUIRKS_MODE" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.CompatibilityMode.NO_QUIRKS_MODE">
<span class="sig-name descname"><span class="pre">NO_QUIRKS_MODE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'NoQuirksMode'</span></em><a class="headerlink" href="#nodriver.cdp.dom.CompatibilityMode.NO_QUIRKS_MODE" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.dom.PhysicalAxes">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">PhysicalAxes</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">names</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">module</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">qualname</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">boundary</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#PhysicalAxes"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.PhysicalAxes" title="Link to this definition">#</a></dt>
<dd><p>ContainerSelector physical axes</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.PhysicalAxes.HORIZONTAL">
<span class="sig-name descname"><span class="pre">HORIZONTAL</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'Horizontal'</span></em><a class="headerlink" href="#nodriver.cdp.dom.PhysicalAxes.HORIZONTAL" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.PhysicalAxes.VERTICAL">
<span class="sig-name descname"><span class="pre">VERTICAL</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'Vertical'</span></em><a class="headerlink" href="#nodriver.cdp.dom.PhysicalAxes.VERTICAL" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.PhysicalAxes.BOTH">
<span class="sig-name descname"><span class="pre">BOTH</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'Both'</span></em><a class="headerlink" href="#nodriver.cdp.dom.PhysicalAxes.BOTH" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.dom.LogicalAxes">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">LogicalAxes</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">names</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">module</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">qualname</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">boundary</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#LogicalAxes"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.LogicalAxes" title="Link to this definition">#</a></dt>
<dd><p>ContainerSelector logical axes</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.LogicalAxes.INLINE">
<span class="sig-name descname"><span class="pre">INLINE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'Inline'</span></em><a class="headerlink" href="#nodriver.cdp.dom.LogicalAxes.INLINE" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.LogicalAxes.BLOCK">
<span class="sig-name descname"><span class="pre">BLOCK</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'Block'</span></em><a class="headerlink" href="#nodriver.cdp.dom.LogicalAxes.BLOCK" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.LogicalAxes.BOTH">
<span class="sig-name descname"><span class="pre">BOTH</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'Both'</span></em><a class="headerlink" href="#nodriver.cdp.dom.LogicalAxes.BOTH" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.dom.ScrollOrientation">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">ScrollOrientation</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">names</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">module</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">qualname</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">boundary</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#ScrollOrientation"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.ScrollOrientation" title="Link to this definition">#</a></dt>
<dd><p>Physical scroll orientation</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.ScrollOrientation.HORIZONTAL">
<span class="sig-name descname"><span class="pre">HORIZONTAL</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'horizontal'</span></em><a class="headerlink" href="#nodriver.cdp.dom.ScrollOrientation.HORIZONTAL" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.ScrollOrientation.VERTICAL">
<span class="sig-name descname"><span class="pre">VERTICAL</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'vertical'</span></em><a class="headerlink" href="#nodriver.cdp.dom.ScrollOrientation.VERTICAL" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.dom.Node">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">Node</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">node_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">backend_node_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">node_type</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">node_name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">local_name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">node_value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">parent_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">child_node_count</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">children</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">attributes</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">document_url</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">base_url</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">public_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">system_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">internal_subset</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">xml_version</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">name</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pseudo_type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pseudo_identifier</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">shadow_root_type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">frame_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">content_document</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">shadow_roots</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">template_content</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pseudo_elements</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">imported_document</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">distributed_nodes</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">is_svg</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">compatibility_mode</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">assigned_slot</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">is_scrollable</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#Node"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.Node" title="Link to this definition">#</a></dt>
<dd><p>DOM interaction is implemented in terms of mirror objects that represent the actual DOM nodes.
DOMNode is a base node mirror type.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.Node.node_id">
<span class="sig-name descname"><span class="pre">node_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom.Node.node_id" title="Link to this definition">#</a></dt>
<dd><p>Node identifier that is passed into the rest of the DOM messages as the <code class="docutils literal notranslate"><span class="pre">nodeId</span></code>. Backend
will only push node with given <code class="docutils literal notranslate"><span class="pre">id</span></code> once. It is aware of all requested nodes and will only
fire DOM events for nodes known to the client.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.Node.backend_node_id">
<span class="sig-name descname"><span class="pre">backend_node_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.dom.BackendNodeId" title="nodriver.cdp.dom.BackendNodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">BackendNodeId</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom.Node.backend_node_id" title="Link to this definition">#</a></dt>
<dd><p>The BackendNodeId for this node.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.Node.node_type">
<span class="sig-name descname"><span class="pre">node_type</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom.Node.node_type" title="Link to this definition">#</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">Node</span></code>’s nodeType.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.Node.node_name">
<span class="sig-name descname"><span class="pre">node_name</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom.Node.node_name" title="Link to this definition">#</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">Node</span></code>’s nodeName.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.Node.local_name">
<span class="sig-name descname"><span class="pre">local_name</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom.Node.local_name" title="Link to this definition">#</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">Node</span></code>’s localName.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.Node.node_value">
<span class="sig-name descname"><span class="pre">node_value</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom.Node.node_value" title="Link to this definition">#</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">Node</span></code>’s nodeValue.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.Node.parent_id">
<span class="sig-name descname"><span class="pre">parent_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom.Node.parent_id" title="Link to this definition">#</a></dt>
<dd><p>The id of the parent node if any.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.Node.child_node_count">
<span class="sig-name descname"><span class="pre">child_node_count</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom.Node.child_node_count" title="Link to this definition">#</a></dt>
<dd><p>Child count for <code class="docutils literal notranslate"><span class="pre">Container</span></code> nodes.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.Node.children">
<span class="sig-name descname"><span class="pre">children</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.dom.Node" title="nodriver.cdp.dom.Node"><code class="xref py py-class docutils literal notranslate"><span class="pre">Node</span></code></a><span class="pre">]]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom.Node.children" title="Link to this definition">#</a></dt>
<dd><p>Child nodes of this node when requested with children.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.Node.attributes">
<span class="sig-name descname"><span class="pre">attributes</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom.Node.attributes" title="Link to this definition">#</a></dt>
<dd><p>Attributes of the <code class="docutils literal notranslate"><span class="pre">Element</span></code> node in the form of flat array <code class="docutils literal notranslate"><span class="pre">[name1,</span> <span class="pre">value1,</span> <span class="pre">name2,</span> <span class="pre">value2]</span></code>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.Node.document_url">
<span class="sig-name descname"><span class="pre">document_url</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom.Node.document_url" title="Link to this definition">#</a></dt>
<dd><p>Document URL that <code class="docutils literal notranslate"><span class="pre">Document</span></code> or <code class="docutils literal notranslate"><span class="pre">FrameOwner</span></code> node points to.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.Node.base_url">
<span class="sig-name descname"><span class="pre">base_url</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom.Node.base_url" title="Link to this definition">#</a></dt>
<dd><p>Base URL that <code class="docutils literal notranslate"><span class="pre">Document</span></code> or <code class="docutils literal notranslate"><span class="pre">FrameOwner</span></code> node uses for URL completion.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.Node.public_id">
<span class="sig-name descname"><span class="pre">public_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom.Node.public_id" title="Link to this definition">#</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">DocumentType</span></code>’s publicId.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.Node.system_id">
<span class="sig-name descname"><span class="pre">system_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom.Node.system_id" title="Link to this definition">#</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">DocumentType</span></code>’s systemId.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.Node.internal_subset">
<span class="sig-name descname"><span class="pre">internal_subset</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom.Node.internal_subset" title="Link to this definition">#</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">DocumentType</span></code>’s internalSubset.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.Node.xml_version">
<span class="sig-name descname"><span class="pre">xml_version</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom.Node.xml_version" title="Link to this definition">#</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">Document</span></code>’s XML version in case of XML documents.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.Node.name">
<span class="sig-name descname"><span class="pre">name</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom.Node.name" title="Link to this definition">#</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">Attr</span></code>’s name.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.Node.value">
<span class="sig-name descname"><span class="pre">value</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom.Node.value" title="Link to this definition">#</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">Attr</span></code>’s value.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.Node.pseudo_type">
<span class="sig-name descname"><span class="pre">pseudo_type</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.dom.PseudoType" title="nodriver.cdp.dom.PseudoType"><code class="xref py py-class docutils literal notranslate"><span class="pre">PseudoType</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom.Node.pseudo_type" title="Link to this definition">#</a></dt>
<dd><p>Pseudo element type for this node.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.Node.pseudo_identifier">
<span class="sig-name descname"><span class="pre">pseudo_identifier</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom.Node.pseudo_identifier" title="Link to this definition">#</a></dt>
<dd><p>Pseudo element identifier for this node. Only present if there is a
valid pseudoType.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.Node.shadow_root_type">
<span class="sig-name descname"><span class="pre">shadow_root_type</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.dom.ShadowRootType" title="nodriver.cdp.dom.ShadowRootType"><code class="xref py py-class docutils literal notranslate"><span class="pre">ShadowRootType</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom.Node.shadow_root_type" title="Link to this definition">#</a></dt>
<dd><p>Shadow root type.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.Node.frame_id">
<span class="sig-name descname"><span class="pre">frame_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="page.html#nodriver.cdp.page.FrameId" title="nodriver.cdp.page.FrameId"><code class="xref py py-class docutils literal notranslate"><span class="pre">FrameId</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom.Node.frame_id" title="Link to this definition">#</a></dt>
<dd><p>Frame ID for frame owner elements.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.Node.content_document">
<span class="sig-name descname"><span class="pre">content_document</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.dom.Node" title="nodriver.cdp.dom.Node"><code class="xref py py-class docutils literal notranslate"><span class="pre">Node</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom.Node.content_document" title="Link to this definition">#</a></dt>
<dd><p>Content document for frame owner elements.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.Node.shadow_roots">
<span class="sig-name descname"><span class="pre">shadow_roots</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.dom.Node" title="nodriver.cdp.dom.Node"><code class="xref py py-class docutils literal notranslate"><span class="pre">Node</span></code></a><span class="pre">]]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom.Node.shadow_roots" title="Link to this definition">#</a></dt>
<dd><p>Shadow root list for given element host.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.Node.template_content">
<span class="sig-name descname"><span class="pre">template_content</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.dom.Node" title="nodriver.cdp.dom.Node"><code class="xref py py-class docutils literal notranslate"><span class="pre">Node</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom.Node.template_content" title="Link to this definition">#</a></dt>
<dd><p>Content document fragment for template elements.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.Node.pseudo_elements">
<span class="sig-name descname"><span class="pre">pseudo_elements</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.dom.Node" title="nodriver.cdp.dom.Node"><code class="xref py py-class docutils literal notranslate"><span class="pre">Node</span></code></a><span class="pre">]]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom.Node.pseudo_elements" title="Link to this definition">#</a></dt>
<dd><p>Pseudo elements associated with this node.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.Node.imported_document">
<span class="sig-name descname"><span class="pre">imported_document</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.dom.Node" title="nodriver.cdp.dom.Node"><code class="xref py py-class docutils literal notranslate"><span class="pre">Node</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom.Node.imported_document" title="Link to this definition">#</a></dt>
<dd><p>Deprecated, as the HTML Imports API has been removed (crbug.com/937746).
This property used to return the imported document for the HTMLImport links.
The property is always undefined now.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.Node.distributed_nodes">
<span class="sig-name descname"><span class="pre">distributed_nodes</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.dom.BackendNode" title="nodriver.cdp.dom.BackendNode"><code class="xref py py-class docutils literal notranslate"><span class="pre">BackendNode</span></code></a><span class="pre">]]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom.Node.distributed_nodes" title="Link to this definition">#</a></dt>
<dd><p>Distributed nodes for given insertion point.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.Node.is_svg">
<span class="sig-name descname"><span class="pre">is_svg</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom.Node.is_svg" title="Link to this definition">#</a></dt>
<dd><p>Whether the node is SVG.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.Node.compatibility_mode">
<span class="sig-name descname"><span class="pre">compatibility_mode</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.dom.CompatibilityMode" title="nodriver.cdp.dom.CompatibilityMode"><code class="xref py py-class docutils literal notranslate"><span class="pre">CompatibilityMode</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom.Node.compatibility_mode" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.Node.assigned_slot">
<span class="sig-name descname"><span class="pre">assigned_slot</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.dom.BackendNode" title="nodriver.cdp.dom.BackendNode"><code class="xref py py-class docutils literal notranslate"><span class="pre">BackendNode</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom.Node.assigned_slot" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.Node.is_scrollable">
<span class="sig-name descname"><span class="pre">is_scrollable</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom.Node.is_scrollable" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.dom.DetachedElementInfo">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">DetachedElementInfo</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">tree_node</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">retained_node_ids</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#DetachedElementInfo"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.DetachedElementInfo" title="Link to this definition">#</a></dt>
<dd><p>A structure to hold the top-level node of a detached tree and an array of its retained descendants.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.DetachedElementInfo.tree_node">
<span class="sig-name descname"><span class="pre">tree_node</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.dom.Node" title="nodriver.cdp.dom.Node"><code class="xref py py-class docutils literal notranslate"><span class="pre">Node</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom.DetachedElementInfo.tree_node" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.DetachedElementInfo.retained_node_ids">
<span class="sig-name descname"><span class="pre">retained_node_ids</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.dom.DetachedElementInfo.retained_node_ids" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.dom.RGBA">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">RGBA</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">r</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">g</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">b</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">a</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#RGBA"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.RGBA" title="Link to this definition">#</a></dt>
<dd><p>A structure holding an RGBA color.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.RGBA.r">
<span class="sig-name descname"><span class="pre">r</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom.RGBA.r" title="Link to this definition">#</a></dt>
<dd><p>The red component, in the [0-255] range.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.RGBA.g">
<span class="sig-name descname"><span class="pre">g</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom.RGBA.g" title="Link to this definition">#</a></dt>
<dd><p>The green component, in the [0-255] range.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.RGBA.b">
<span class="sig-name descname"><span class="pre">b</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom.RGBA.b" title="Link to this definition">#</a></dt>
<dd><p>The blue component, in the [0-255] range.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.RGBA.a">
<span class="sig-name descname"><span class="pre">a</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom.RGBA.a" title="Link to this definition">#</a></dt>
<dd><p>1).</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>The alpha component, in the [0-1] <a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#range" title="(in Python v3.13)">range</a> (default</p>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.dom.Quad">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">Quad</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">iterable</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">()</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#Quad"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.Quad" title="Link to this definition">#</a></dt>
<dd><p>An array of quad vertices, x immediately followed by y for each point, points clock-wise.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.dom.BoxModel">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">BoxModel</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">content</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">padding</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">border</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">margin</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">width</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">height</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">shape_outside</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#BoxModel"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.BoxModel" title="Link to this definition">#</a></dt>
<dd><p>Box model.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.BoxModel.content">
<span class="sig-name descname"><span class="pre">content</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.dom.Quad" title="nodriver.cdp.dom.Quad"><code class="xref py py-class docutils literal notranslate"><span class="pre">Quad</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom.BoxModel.content" title="Link to this definition">#</a></dt>
<dd><p>Content box</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.BoxModel.padding">
<span class="sig-name descname"><span class="pre">padding</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.dom.Quad" title="nodriver.cdp.dom.Quad"><code class="xref py py-class docutils literal notranslate"><span class="pre">Quad</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom.BoxModel.padding" title="Link to this definition">#</a></dt>
<dd><p>Padding box</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.BoxModel.border">
<span class="sig-name descname"><span class="pre">border</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.dom.Quad" title="nodriver.cdp.dom.Quad"><code class="xref py py-class docutils literal notranslate"><span class="pre">Quad</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom.BoxModel.border" title="Link to this definition">#</a></dt>
<dd><p>Border box</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.BoxModel.margin">
<span class="sig-name descname"><span class="pre">margin</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.dom.Quad" title="nodriver.cdp.dom.Quad"><code class="xref py py-class docutils literal notranslate"><span class="pre">Quad</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom.BoxModel.margin" title="Link to this definition">#</a></dt>
<dd><p>Margin box</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.BoxModel.width">
<span class="sig-name descname"><span class="pre">width</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom.BoxModel.width" title="Link to this definition">#</a></dt>
<dd><p>Node width</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.BoxModel.height">
<span class="sig-name descname"><span class="pre">height</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom.BoxModel.height" title="Link to this definition">#</a></dt>
<dd><p>Node height</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.BoxModel.shape_outside">
<span class="sig-name descname"><span class="pre">shape_outside</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.dom.ShapeOutsideInfo" title="nodriver.cdp.dom.ShapeOutsideInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">ShapeOutsideInfo</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.dom.BoxModel.shape_outside" title="Link to this definition">#</a></dt>
<dd><p>Shape outside coordinates</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.dom.ShapeOutsideInfo">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">ShapeOutsideInfo</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">bounds</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">shape</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">margin_shape</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#ShapeOutsideInfo"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.ShapeOutsideInfo" title="Link to this definition">#</a></dt>
<dd><p>CSS Shape Outside details.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.ShapeOutsideInfo.bounds">
<span class="sig-name descname"><span class="pre">bounds</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.dom.Quad" title="nodriver.cdp.dom.Quad"><code class="xref py py-class docutils literal notranslate"><span class="pre">Quad</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom.ShapeOutsideInfo.bounds" title="Link to this definition">#</a></dt>
<dd><p>Shape bounds</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.ShapeOutsideInfo.shape">
<span class="sig-name descname"><span class="pre">shape</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.dom.ShapeOutsideInfo.shape" title="Link to this definition">#</a></dt>
<dd><p>Shape coordinate details</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.ShapeOutsideInfo.margin_shape">
<span class="sig-name descname"><span class="pre">margin_shape</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.dom.ShapeOutsideInfo.margin_shape" title="Link to this definition">#</a></dt>
<dd><p>Margin shape bounds</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.dom.Rect">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">Rect</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">y</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">width</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">height</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#Rect"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.Rect" title="Link to this definition">#</a></dt>
<dd><p>Rectangle.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.Rect.x">
<span class="sig-name descname"><span class="pre">x</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom.Rect.x" title="Link to this definition">#</a></dt>
<dd><p>X coordinate</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.Rect.y">
<span class="sig-name descname"><span class="pre">y</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom.Rect.y" title="Link to this definition">#</a></dt>
<dd><p>Y coordinate</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.Rect.width">
<span class="sig-name descname"><span class="pre">width</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom.Rect.width" title="Link to this definition">#</a></dt>
<dd><p>Rectangle width</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.Rect.height">
<span class="sig-name descname"><span class="pre">height</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom.Rect.height" title="Link to this definition">#</a></dt>
<dd><p>Rectangle height</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.dom.CSSComputedStyleProperty">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">CSSComputedStyleProperty</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#CSSComputedStyleProperty"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.CSSComputedStyleProperty" title="Link to this definition">#</a></dt>
<dd><dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.CSSComputedStyleProperty.name">
<span class="sig-name descname"><span class="pre">name</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom.CSSComputedStyleProperty.name" title="Link to this definition">#</a></dt>
<dd><p>Computed style property name.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.CSSComputedStyleProperty.value">
<span class="sig-name descname"><span class="pre">value</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom.CSSComputedStyleProperty.value" title="Link to this definition">#</a></dt>
<dd><p>Computed style property value.</p>
</dd></dl>

</dd></dl>

</section>
<section id="commands">
<h2>Commands<a class="headerlink" href="#commands" title="Link to this heading">#</a></h2>
<p>Each command is a generator function. The return
type <code class="docutils literal notranslate"><span class="pre">Generator[x,</span> <span class="pre">y,</span> <span class="pre">z]</span></code> indicates that the generator
<em>yields</em> arguments of type <code class="docutils literal notranslate"><span class="pre">x</span></code>, it must be resumed with
an argument of type <code class="docutils literal notranslate"><span class="pre">y</span></code>, and it returns type <code class="docutils literal notranslate"><span class="pre">z</span></code>. In
this library, types <code class="docutils literal notranslate"><span class="pre">x</span></code> and <code class="docutils literal notranslate"><span class="pre">y</span></code> are the same for all
commands, and <code class="docutils literal notranslate"><span class="pre">z</span></code> is the return type you should pay attention
to. For more information, see
<a class="reference internal" href="../../readme.html#getting-started-commands"><span class="std std-ref">Getting Started: Commands</span></a>.</p>
<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.dom.collect_class_names_from_subtree">
<span class="sig-name descname"><span class="pre">collect_class_names_from_subtree</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">node_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#collect_class_names_from_subtree"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.collect_class_names_from_subtree" title="Link to this definition">#</a></dt>
<dd><p>Collects class names for the node with given id and all of it’s child nodes.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a></span>) – Id of the node to collect class names.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>Class name list.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.dom.copy_to">
<span class="sig-name descname"><span class="pre">copy_to</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">node_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">target_node_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">insert_before_node_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#copy_to"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.copy_to" title="Link to this definition">#</a></dt>
<dd><p>Creates a deep copy of the specified node and places it into the target container before the
given anchor.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a></span>) – Id of the node to copy.</p></li>
<li><p><strong>target_node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a></span>) – Id of the element to drop the copy into.</p></li>
<li><p><strong>insert_before_node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a>]</span>) – <em>(Optional)</em> Drop the copy before this node (if absent, the copy becomes the last child of <code class="docutils literal notranslate"><span class="pre">`targetNodeId`</span></code>).</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a>]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>Id of the node clone.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.dom.describe_node">
<span class="sig-name descname"><span class="pre">describe_node</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">node_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">backend_node_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">object_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">depth</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pierce</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#describe_node"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.describe_node" title="Link to this definition">#</a></dt>
<dd><p>Describes node given its id, does not require domain to be enabled. Does not start tracking any
objects, can be used for automation.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a>]</span>) – <em>(Optional)</em> Identifier of the node.</p></li>
<li><p><strong>backend_node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="#nodriver.cdp.dom.BackendNodeId" title="nodriver.cdp.dom.BackendNodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">BackendNodeId</span></code></a>]</span>) – <em>(Optional)</em> Identifier of the backend node.</p></li>
<li><p><strong>object_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="runtime.html#nodriver.cdp.runtime.RemoteObjectId" title="nodriver.cdp.runtime.RemoteObjectId"><code class="xref py py-class docutils literal notranslate"><span class="pre">RemoteObjectId</span></code></a>]</span>) – <em>(Optional)</em> JavaScript object id of the node wrapper.</p></li>
<li><p><strong>depth</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a>]</span>) – <em>(Optional)</em> The maximum depth at which children should be retrieved, defaults to 1. Use -1 for the entire subtree or provide an integer larger than 0.</p></li>
<li><p><strong>pierce</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <em>(Optional)</em> Whether or not iframes and shadow roots should be traversed when returning the subtree (default is false).</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference internal" href="#nodriver.cdp.dom.Node" title="nodriver.cdp.dom.Node"><code class="xref py py-class docutils literal notranslate"><span class="pre">Node</span></code></a>]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>Node description.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.dom.disable">
<span class="sig-name descname"><span class="pre">disable</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#disable"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.disable" title="Link to this definition">#</a></dt>
<dd><p>Disables DOM agent for the given page.</p>
<dl class="field-list simple">
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.dom.discard_search_results">
<span class="sig-name descname"><span class="pre">discard_search_results</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">search_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#discard_search_results"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.discard_search_results" title="Link to this definition">#</a></dt>
<dd><p>Discards search results from the session with the given id. <code class="docutils literal notranslate"><span class="pre">getSearchResults</span></code> should no longer
be called for that search.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>search_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – Unique search session identifier.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.dom.enable">
<span class="sig-name descname"><span class="pre">enable</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">include_whitespace</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#enable"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.enable" title="Link to this definition">#</a></dt>
<dd><p>Enables DOM agent for the given page.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>include_whitespace</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]</span>) – <strong>(EXPERIMENTAL)</strong> <em>(Optional)</em> Whether to include whitespaces in the children array of returned Nodes.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.dom.focus">
<span class="sig-name descname"><span class="pre">focus</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">node_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">backend_node_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">object_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#focus"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.focus" title="Link to this definition">#</a></dt>
<dd><p>Focuses the given element.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a>]</span>) – <em>(Optional)</em> Identifier of the node.</p></li>
<li><p><strong>backend_node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="#nodriver.cdp.dom.BackendNodeId" title="nodriver.cdp.dom.BackendNodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">BackendNodeId</span></code></a>]</span>) – <em>(Optional)</em> Identifier of the backend node.</p></li>
<li><p><strong>object_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="runtime.html#nodriver.cdp.runtime.RemoteObjectId" title="nodriver.cdp.runtime.RemoteObjectId"><code class="xref py py-class docutils literal notranslate"><span class="pre">RemoteObjectId</span></code></a>]</span>) – <em>(Optional)</em> JavaScript object id of the node wrapper.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.dom.get_anchor_element">
<span class="sig-name descname"><span class="pre">get_anchor_element</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">node_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">anchor_specifier</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#get_anchor_element"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.get_anchor_element" title="Link to this definition">#</a></dt>
<dd><p>Returns the target anchor element of the given anchor query according to
<a class="reference external" href="https://www.w3.org/TR/css-anchor-position-1/#target">https://www.w3.org/TR/css-anchor-position-1/#target</a>.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a></span>) – Id of the positioned element from which to find the anchor.</p></li>
<li><p><strong>anchor_specifier</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]</span>) – <em>(Optional)</em> An optional anchor specifier, as defined in <a class="reference external" href="https://www.w3.org/TR/css-anchor-position-1/#anchor-specifier">https://www.w3.org/TR/css-anchor-position-1/#anchor-specifier</a>. If not provided, it will return the implicit anchor element for the given positioned element.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a>]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>The anchor element of the given anchor query.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.dom.get_attributes">
<span class="sig-name descname"><span class="pre">get_attributes</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">node_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#get_attributes"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.get_attributes" title="Link to this definition">#</a></dt>
<dd><p>Returns attributes for the specified node.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a></span>) – Id of the node to retrieve attributes for.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>An interleaved array of node attribute names and values.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.dom.get_box_model">
<span class="sig-name descname"><span class="pre">get_box_model</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">node_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">backend_node_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">object_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#get_box_model"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.get_box_model" title="Link to this definition">#</a></dt>
<dd><p>Returns boxes for the given node.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a>]</span>) – <em>(Optional)</em> Identifier of the node.</p></li>
<li><p><strong>backend_node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="#nodriver.cdp.dom.BackendNodeId" title="nodriver.cdp.dom.BackendNodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">BackendNodeId</span></code></a>]</span>) – <em>(Optional)</em> Identifier of the backend node.</p></li>
<li><p><strong>object_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="runtime.html#nodriver.cdp.runtime.RemoteObjectId" title="nodriver.cdp.runtime.RemoteObjectId"><code class="xref py py-class docutils literal notranslate"><span class="pre">RemoteObjectId</span></code></a>]</span>) – <em>(Optional)</em> JavaScript object id of the node wrapper.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference internal" href="#nodriver.cdp.dom.BoxModel" title="nodriver.cdp.dom.BoxModel"><code class="xref py py-class docutils literal notranslate"><span class="pre">BoxModel</span></code></a>]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>Box model for the node.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.dom.get_container_for_node">
<span class="sig-name descname"><span class="pre">get_container_for_node</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">node_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">container_name</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">physical_axes</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">logical_axes</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">queries_scroll_state</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#get_container_for_node"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.get_container_for_node" title="Link to this definition">#</a></dt>
<dd><p>Returns the query container of the given node based on container query
conditions: containerName, physical and logical axes, and whether it queries
scroll-state. If no axes are provided and queriesScrollState is false, the
style container is returned, which is the direct parent or the closest
element with a matching container-name.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a></span>) – </p></li>
<li><p><strong>container_name</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]</span>) – <em>(Optional)</em></p></li>
<li><p><strong>physical_axes</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="#nodriver.cdp.dom.PhysicalAxes" title="nodriver.cdp.dom.PhysicalAxes"><code class="xref py py-class docutils literal notranslate"><span class="pre">PhysicalAxes</span></code></a>]</span>) – <em>(Optional)</em></p></li>
<li><p><strong>logical_axes</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="#nodriver.cdp.dom.LogicalAxes" title="nodriver.cdp.dom.LogicalAxes"><code class="xref py py-class docutils literal notranslate"><span class="pre">LogicalAxes</span></code></a>]</span>) – <em>(Optional)</em></p></li>
<li><p><strong>queries_scroll_state</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <em>(Optional)</em></p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a>]]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p><em>(Optional)</em> The container node for the given node, or null if not found.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.dom.get_content_quads">
<span class="sig-name descname"><span class="pre">get_content_quads</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">node_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">backend_node_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">object_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#get_content_quads"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.get_content_quads" title="Link to this definition">#</a></dt>
<dd><p>Returns quads that describe node position on the page. This method
might return multiple quads for inline nodes.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a>]</span>) – <em>(Optional)</em> Identifier of the node.</p></li>
<li><p><strong>backend_node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="#nodriver.cdp.dom.BackendNodeId" title="nodriver.cdp.dom.BackendNodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">BackendNodeId</span></code></a>]</span>) – <em>(Optional)</em> Identifier of the backend node.</p></li>
<li><p><strong>object_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="runtime.html#nodriver.cdp.runtime.RemoteObjectId" title="nodriver.cdp.runtime.RemoteObjectId"><code class="xref py py-class docutils literal notranslate"><span class="pre">RemoteObjectId</span></code></a>]</span>) – <em>(Optional)</em> JavaScript object id of the node wrapper.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.dom.Quad" title="nodriver.cdp.dom.Quad"><code class="xref py py-class docutils literal notranslate"><span class="pre">Quad</span></code></a>]]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>Quads that describe node layout relative to viewport.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.dom.get_detached_dom_nodes">
<span class="sig-name descname"><span class="pre">get_detached_dom_nodes</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#get_detached_dom_nodes"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.get_detached_dom_nodes" title="Link to this definition">#</a></dt>
<dd><p>Returns list of detached nodes</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.dom.DetachedElementInfo" title="nodriver.cdp.dom.DetachedElementInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">DetachedElementInfo</span></code></a>]]</span></p>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>The list of detached nodes</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.dom.get_document">
<span class="sig-name descname"><span class="pre">get_document</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">depth</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pierce</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#get_document"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.get_document" title="Link to this definition">#</a></dt>
<dd><p>Returns the root DOM node (and optionally the subtree) to the caller.
Implicitly enables the DOM domain events for the current target.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>depth</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a>]</span>) – <em>(Optional)</em> The maximum depth at which children should be retrieved, defaults to 1. Use -1 for the entire subtree or provide an integer larger than 0.</p></li>
<li><p><strong>pierce</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <em>(Optional)</em> Whether or not iframes and shadow roots should be traversed when returning the subtree (default is false).</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference internal" href="#nodriver.cdp.dom.Node" title="nodriver.cdp.dom.Node"><code class="xref py py-class docutils literal notranslate"><span class="pre">Node</span></code></a>]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>Resulting node.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.dom.get_element_by_relation">
<span class="sig-name descname"><span class="pre">get_element_by_relation</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">node_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">relation</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#get_element_by_relation"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.get_element_by_relation" title="Link to this definition">#</a></dt>
<dd><p>Returns the NodeId of the matched element according to certain relations.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a></span>) – Id of the node from which to query the relation.</p></li>
<li><p><strong>relation</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – Type of relation to get.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a>]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>NodeId of the element matching the queried relation.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.dom.get_file_info">
<span class="sig-name descname"><span class="pre">get_file_info</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#get_file_info"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.get_file_info" title="Link to this definition">#</a></dt>
<dd><p>Returns file information for the given
File wrapper.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>object_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="runtime.html#nodriver.cdp.runtime.RemoteObjectId" title="nodriver.cdp.runtime.RemoteObjectId"><code class="xref py py-class docutils literal notranslate"><span class="pre">RemoteObjectId</span></code></a></span>) – JavaScript object id of the node wrapper.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.dom.get_flattened_document">
<span class="sig-name descname"><span class="pre">get_flattened_document</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">depth</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pierce</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#get_flattened_document"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.get_flattened_document" title="Link to this definition">#</a></dt>
<dd><p>Returns the root DOM node (and optionally the subtree) to the caller.
Deprecated, as it is not designed to work well with the rest of the DOM agent.
Use DOMSnapshot.captureSnapshot instead.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 1.3.</span></p>
</div>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>depth</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a>]</span>) – <em>(Optional)</em> The maximum depth at which children should be retrieved, defaults to 1. Use -1 for the entire subtree or provide an integer larger than 0.</p></li>
<li><p><strong>pierce</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <em>(Optional)</em> Whether or not iframes and shadow roots should be traversed when returning the subtree (default is false).</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.dom.Node" title="nodriver.cdp.dom.Node"><code class="xref py py-class docutils literal notranslate"><span class="pre">Node</span></code></a>]]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>Resulting node.</p>
</dd>
</dl>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 1.3.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.dom.get_frame_owner">
<span class="sig-name descname"><span class="pre">get_frame_owner</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">frame_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#get_frame_owner"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.get_frame_owner" title="Link to this definition">#</a></dt>
<dd><p>Returns iframe node that owns iframe with the given domain.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>frame_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="page.html#nodriver.cdp.page.FrameId" title="nodriver.cdp.page.FrameId"><code class="xref py py-class docutils literal notranslate"><span class="pre">FrameId</span></code></a></span>) – </p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Tuple" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Tuple</span></code></a>[<a class="reference internal" href="#nodriver.cdp.dom.BackendNodeId" title="nodriver.cdp.dom.BackendNodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">BackendNodeId</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a>]]]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p><p>A tuple with the following items:</p>
<ol class="arabic simple" start="0">
<li><p><strong>backendNodeId</strong> - Resulting node.</p></li>
<li><p><strong>nodeId</strong> - <em>(Optional)</em> Id of the node at given coordinates, only when enabled and requested document.</p></li>
</ol>
</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.dom.get_node_for_location">
<span class="sig-name descname"><span class="pre">get_node_for_location</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">y</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">include_user_agent_shadow_dom</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">ignore_pointer_events_none</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#get_node_for_location"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.get_node_for_location" title="Link to this definition">#</a></dt>
<dd><p>Returns node id at given location. Depending on whether DOM domain is enabled, nodeId is
either returned or not.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>x</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></span>) – X coordinate.</p></li>
<li><p><strong>y</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></span>) – Y coordinate.</p></li>
<li><p><strong>include_user_agent_shadow_dom</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <em>(Optional)</em> False to skip to the nearest non-UA shadow root ancestor (default: false).</p></li>
<li><p><strong>ignore_pointer_events_none</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <em>(Optional)</em> Whether to ignore pointer-events: none on elements and hit test them.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Tuple" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Tuple</span></code></a>[<a class="reference internal" href="#nodriver.cdp.dom.BackendNodeId" title="nodriver.cdp.dom.BackendNodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">BackendNodeId</span></code></a>, <a class="reference internal" href="page.html#nodriver.cdp.page.FrameId" title="nodriver.cdp.page.FrameId"><code class="xref py py-class docutils literal notranslate"><span class="pre">FrameId</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a>]]]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p><p>A tuple with the following items:</p>
<ol class="arabic simple" start="0">
<li><p><strong>backendNodeId</strong> - Resulting node.</p></li>
<li><p><strong>frameId</strong> - Frame this node belongs to.</p></li>
<li><p><strong>nodeId</strong> - <em>(Optional)</em> Id of the node at given coordinates, only when enabled and requested document.</p></li>
</ol>
</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.dom.get_node_stack_traces">
<span class="sig-name descname"><span class="pre">get_node_stack_traces</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">node_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#get_node_stack_traces"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.get_node_stack_traces" title="Link to this definition">#</a></dt>
<dd><p>Gets stack traces associated with a Node. As of now, only provides stack trace for Node creation.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a></span>) – Id of the node to get stack traces for.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="runtime.html#nodriver.cdp.runtime.StackTrace" title="nodriver.cdp.runtime.StackTrace"><code class="xref py py-class docutils literal notranslate"><span class="pre">StackTrace</span></code></a>]]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p><em>(Optional)</em> Creation stack trace, if available.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.dom.get_nodes_for_subtree_by_style">
<span class="sig-name descname"><span class="pre">get_nodes_for_subtree_by_style</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">node_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">computed_styles</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pierce</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#get_nodes_for_subtree_by_style"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.get_nodes_for_subtree_by_style" title="Link to this definition">#</a></dt>
<dd><p>Finds nodes with a given computed style in a subtree.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a></span>) – Node ID pointing to the root of a subtree.</p></li>
<li><p><strong>computed_styles</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.dom.CSSComputedStyleProperty" title="nodriver.cdp.dom.CSSComputedStyleProperty"><code class="xref py py-class docutils literal notranslate"><span class="pre">CSSComputedStyleProperty</span></code></a>]</span>) – The style to filter nodes by (includes nodes if any of properties matches).</p></li>
<li><p><strong>pierce</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <em>(Optional)</em> Whether or not iframes and shadow roots in the same target should be traversed when returning the results (default is false).</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a>]]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>Resulting nodes.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.dom.get_outer_html">
<span class="sig-name descname"><span class="pre">get_outer_html</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">node_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">backend_node_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">object_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#get_outer_html"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.get_outer_html" title="Link to this definition">#</a></dt>
<dd><p>Returns node’s HTML markup.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a>]</span>) – <em>(Optional)</em> Identifier of the node.</p></li>
<li><p><strong>backend_node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="#nodriver.cdp.dom.BackendNodeId" title="nodriver.cdp.dom.BackendNodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">BackendNodeId</span></code></a>]</span>) – <em>(Optional)</em> Identifier of the backend node.</p></li>
<li><p><strong>object_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="runtime.html#nodriver.cdp.runtime.RemoteObjectId" title="nodriver.cdp.runtime.RemoteObjectId"><code class="xref py py-class docutils literal notranslate"><span class="pre">RemoteObjectId</span></code></a>]</span>) – <em>(Optional)</em> JavaScript object id of the node wrapper.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>Outer HTML markup.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.dom.get_querying_descendants_for_container">
<span class="sig-name descname"><span class="pre">get_querying_descendants_for_container</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">node_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#get_querying_descendants_for_container"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.get_querying_descendants_for_container" title="Link to this definition">#</a></dt>
<dd><p>Returns the descendants of a container query container that have
container queries against this container.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a></span>) – Id of the container node to find querying descendants from.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a>]]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>Descendant nodes with container queries against the given container.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.dom.get_relayout_boundary">
<span class="sig-name descname"><span class="pre">get_relayout_boundary</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">node_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#get_relayout_boundary"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.get_relayout_boundary" title="Link to this definition">#</a></dt>
<dd><p>Returns the id of the nearest ancestor that is a relayout boundary.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a></span>) – Id of the node.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a>]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>Relayout boundary node id for the given node.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.dom.get_search_results">
<span class="sig-name descname"><span class="pre">get_search_results</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">search_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">from_index</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">to_index</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#get_search_results"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.get_search_results" title="Link to this definition">#</a></dt>
<dd><p>Returns search results from given <code class="docutils literal notranslate"><span class="pre">fromIndex</span></code> to given <code class="docutils literal notranslate"><span class="pre">toIndex</span></code> from the search with the given
identifier.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>search_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – Unique search session identifier.</p></li>
<li><p><strong>from_index</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></span>) – Start index of the search result to be returned.</p></li>
<li><p><strong>to_index</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></span>) – End index of the search result to be returned.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a>]]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>Ids of the search result nodes.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.dom.get_top_layer_elements">
<span class="sig-name descname"><span class="pre">get_top_layer_elements</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#get_top_layer_elements"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.get_top_layer_elements" title="Link to this definition">#</a></dt>
<dd><p>Returns NodeIds of current top layer elements.
Top layer is rendered closest to the user within a viewport, therefore its elements always
appear on top of all other content.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a>]]</span></p>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>NodeIds of top layer elements</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.dom.hide_highlight">
<span class="sig-name descname"><span class="pre">hide_highlight</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#hide_highlight"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.hide_highlight" title="Link to this definition">#</a></dt>
<dd><p>Hides any highlight.</p>
<dl class="field-list simple">
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.dom.highlight_node">
<span class="sig-name descname"><span class="pre">highlight_node</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#highlight_node"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.highlight_node" title="Link to this definition">#</a></dt>
<dd><p>Highlights DOM node.</p>
<dl class="field-list simple">
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.dom.highlight_rect">
<span class="sig-name descname"><span class="pre">highlight_rect</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#highlight_rect"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.highlight_rect" title="Link to this definition">#</a></dt>
<dd><p>Highlights given rectangle.</p>
<dl class="field-list simple">
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.dom.mark_undoable_state">
<span class="sig-name descname"><span class="pre">mark_undoable_state</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#mark_undoable_state"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.mark_undoable_state" title="Link to this definition">#</a></dt>
<dd><p>Marks last undoable state.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.dom.move_to">
<span class="sig-name descname"><span class="pre">move_to</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">node_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">target_node_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">insert_before_node_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#move_to"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.move_to" title="Link to this definition">#</a></dt>
<dd><p>Moves node into the new container, places it before the given anchor.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a></span>) – Id of the node to move.</p></li>
<li><p><strong>target_node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a></span>) – Id of the element to drop the moved node into.</p></li>
<li><p><strong>insert_before_node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a>]</span>) – <em>(Optional)</em> Drop node before this one (if absent, the moved node becomes the last child of <code class="docutils literal notranslate"><span class="pre">`targetNodeId`</span></code>).</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a>]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>New id of the moved node.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.dom.perform_search">
<span class="sig-name descname"><span class="pre">perform_search</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">query</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">include_user_agent_shadow_dom</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#perform_search"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.perform_search" title="Link to this definition">#</a></dt>
<dd><p>Searches for a given string in the DOM tree. Use <code class="docutils literal notranslate"><span class="pre">getSearchResults</span></code> to access search results or
<code class="docutils literal notranslate"><span class="pre">cancelSearch</span></code> to end this search session.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>query</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – Plain text or query selector or XPath search query.</p></li>
<li><p><strong>include_user_agent_shadow_dom</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <em>(Optional)</em> True to search in user agent shadow DOM.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Tuple" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Tuple</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a>]]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p><p>A tuple with the following items:</p>
<ol class="arabic simple" start="0">
<li><p><strong>searchId</strong> - Unique search session identifier.</p></li>
<li><p><strong>resultCount</strong> - Number of search results.</p></li>
</ol>
</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.dom.push_node_by_path_to_frontend">
<span class="sig-name descname"><span class="pre">push_node_by_path_to_frontend</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#push_node_by_path_to_frontend"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.push_node_by_path_to_frontend" title="Link to this definition">#</a></dt>
<dd><p>Requests that the node is sent to the caller given its path. // FIXME, use XPath</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>path</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – Path to node in the proprietary format.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a>]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>Id of the node for given path.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.dom.push_nodes_by_backend_ids_to_frontend">
<span class="sig-name descname"><span class="pre">push_nodes_by_backend_ids_to_frontend</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">backend_node_ids</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#push_nodes_by_backend_ids_to_frontend"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.push_nodes_by_backend_ids_to_frontend" title="Link to this definition">#</a></dt>
<dd><p>Requests that a batch of nodes is sent to the caller given their backend node ids.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>backend_node_ids</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.dom.BackendNodeId" title="nodriver.cdp.dom.BackendNodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">BackendNodeId</span></code></a>]</span>) – The array of backend node ids.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a>]]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>The array of ids of pushed nodes that correspond to the backend ids specified in backendNodeIds.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.dom.query_selector">
<span class="sig-name descname"><span class="pre">query_selector</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">node_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">selector</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#query_selector"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.query_selector" title="Link to this definition">#</a></dt>
<dd><p>Executes <code class="docutils literal notranslate"><span class="pre">querySelector</span></code> on a given node.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a></span>) – Id of the node to query upon.</p></li>
<li><p><strong>selector</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – Selector string.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a>]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>Query selector result.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.dom.query_selector_all">
<span class="sig-name descname"><span class="pre">query_selector_all</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">node_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">selector</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#query_selector_all"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.query_selector_all" title="Link to this definition">#</a></dt>
<dd><p>Executes <code class="docutils literal notranslate"><span class="pre">querySelectorAll</span></code> on a given node.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a></span>) – Id of the node to query upon.</p></li>
<li><p><strong>selector</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – Selector string.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a>]]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>Query selector result.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.dom.redo">
<span class="sig-name descname"><span class="pre">redo</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#redo"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.redo" title="Link to this definition">#</a></dt>
<dd><p>Re-does the last undone action.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.dom.remove_attribute">
<span class="sig-name descname"><span class="pre">remove_attribute</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">node_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#remove_attribute"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.remove_attribute" title="Link to this definition">#</a></dt>
<dd><p>Removes attribute with given name from an element with given id.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a></span>) – Id of the element to remove attribute from.</p></li>
<li><p><strong>name</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – Name of the attribute to remove.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.dom.remove_node">
<span class="sig-name descname"><span class="pre">remove_node</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">node_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#remove_node"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.remove_node" title="Link to this definition">#</a></dt>
<dd><p>Removes node with given id.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a></span>) – Id of the node to remove.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.dom.request_child_nodes">
<span class="sig-name descname"><span class="pre">request_child_nodes</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">node_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">depth</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pierce</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#request_child_nodes"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.request_child_nodes" title="Link to this definition">#</a></dt>
<dd><p>Requests that children of the node with given id are returned to the caller in form of
<code class="docutils literal notranslate"><span class="pre">setChildNodes</span></code> events where not only immediate children are retrieved, but all children down to
the specified depth.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a></span>) – Id of the node to get children for.</p></li>
<li><p><strong>depth</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a>]</span>) – <em>(Optional)</em> The maximum depth at which children should be retrieved, defaults to 1. Use -1 for the entire subtree or provide an integer larger than 0.</p></li>
<li><p><strong>pierce</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <em>(Optional)</em> Whether or not iframes and shadow roots should be traversed when returning the sub-tree (default is false).</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.dom.request_node">
<span class="sig-name descname"><span class="pre">request_node</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#request_node"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.request_node" title="Link to this definition">#</a></dt>
<dd><p>Requests that the node is sent to the caller given the JavaScript node object reference. All
nodes that form the path from the node to the root are also sent to the client as a series of
<code class="docutils literal notranslate"><span class="pre">setChildNodes</span></code> notifications.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>object_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="runtime.html#nodriver.cdp.runtime.RemoteObjectId" title="nodriver.cdp.runtime.RemoteObjectId"><code class="xref py py-class docutils literal notranslate"><span class="pre">RemoteObjectId</span></code></a></span>) – JavaScript object id to convert into node.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a>]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>Node id for given object.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.dom.resolve_node">
<span class="sig-name descname"><span class="pre">resolve_node</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">node_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">backend_node_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">object_group</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">execution_context_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#resolve_node"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.resolve_node" title="Link to this definition">#</a></dt>
<dd><p>Resolves the JavaScript node object for a given NodeId or BackendNodeId.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a>]</span>) – <em>(Optional)</em> Id of the node to resolve.</p></li>
<li><p><strong>backend_node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="#nodriver.cdp.dom.BackendNodeId" title="nodriver.cdp.dom.BackendNodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">BackendNodeId</span></code></a>]</span>) – <em>(Optional)</em> Backend identifier of the node to resolve.</p></li>
<li><p><strong>object_group</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]</span>) – <em>(Optional)</em> Symbolic group name that can be used to release multiple objects.</p></li>
<li><p><strong>execution_context_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="runtime.html#nodriver.cdp.runtime.ExecutionContextId" title="nodriver.cdp.runtime.ExecutionContextId"><code class="xref py py-class docutils literal notranslate"><span class="pre">ExecutionContextId</span></code></a>]</span>) – <em>(Optional)</em> Execution context in which to resolve the node.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference internal" href="runtime.html#nodriver.cdp.runtime.RemoteObject" title="nodriver.cdp.runtime.RemoteObject"><code class="xref py py-class docutils literal notranslate"><span class="pre">RemoteObject</span></code></a>]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>JavaScript object wrapper for given node.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.dom.scroll_into_view_if_needed">
<span class="sig-name descname"><span class="pre">scroll_into_view_if_needed</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">node_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">backend_node_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">object_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">rect</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#scroll_into_view_if_needed"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.scroll_into_view_if_needed" title="Link to this definition">#</a></dt>
<dd><p>Scrolls the specified rect of the given node into view if not already visible.
Note: exactly one between nodeId, backendNodeId and objectId should be passed
to identify the node.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a>]</span>) – <em>(Optional)</em> Identifier of the node.</p></li>
<li><p><strong>backend_node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="#nodriver.cdp.dom.BackendNodeId" title="nodriver.cdp.dom.BackendNodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">BackendNodeId</span></code></a>]</span>) – <em>(Optional)</em> Identifier of the backend node.</p></li>
<li><p><strong>object_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="runtime.html#nodriver.cdp.runtime.RemoteObjectId" title="nodriver.cdp.runtime.RemoteObjectId"><code class="xref py py-class docutils literal notranslate"><span class="pre">RemoteObjectId</span></code></a>]</span>) – <em>(Optional)</em> JavaScript object id of the node wrapper.</p></li>
<li><p><strong>rect</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="#nodriver.cdp.dom.Rect" title="nodriver.cdp.dom.Rect"><code class="xref py py-class docutils literal notranslate"><span class="pre">Rect</span></code></a>]</span>) – <em>(Optional)</em> The rect to be scrolled into view, relative to the node’s border box, in CSS pixels. When omitted, center of the node will be used, similar to Element.scrollIntoView.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.dom.set_attribute_value">
<span class="sig-name descname"><span class="pre">set_attribute_value</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">node_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#set_attribute_value"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.set_attribute_value" title="Link to this definition">#</a></dt>
<dd><p>Sets attribute for an element with given id.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a></span>) – Id of the element to set attribute for.</p></li>
<li><p><strong>name</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – Attribute name.</p></li>
<li><p><strong>value</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – Attribute value.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.dom.set_attributes_as_text">
<span class="sig-name descname"><span class="pre">set_attributes_as_text</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">node_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">text</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">name</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#set_attributes_as_text"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.set_attributes_as_text" title="Link to this definition">#</a></dt>
<dd><p>Sets attributes on element with given id. This method is useful when user edits some existing
attribute value and types in several attribute name/value pairs.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a></span>) – Id of the element to set attributes for.</p></li>
<li><p><strong>text</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – Text with a number of attributes. Will parse this text using HTML parser.</p></li>
<li><p><strong>name</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]</span>) – <em>(Optional)</em> Attribute name to replace with new attributes derived from text in case text parsed successfully.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.dom.set_file_input_files">
<span class="sig-name descname"><span class="pre">set_file_input_files</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">files</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">node_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">backend_node_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">object_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#set_file_input_files"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.set_file_input_files" title="Link to this definition">#</a></dt>
<dd><p>Sets files for the given file input element.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>files</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]</span>) – Array of file paths to set.</p></li>
<li><p><strong>node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a>]</span>) – <em>(Optional)</em> Identifier of the node.</p></li>
<li><p><strong>backend_node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="#nodriver.cdp.dom.BackendNodeId" title="nodriver.cdp.dom.BackendNodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">BackendNodeId</span></code></a>]</span>) – <em>(Optional)</em> Identifier of the backend node.</p></li>
<li><p><strong>object_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="runtime.html#nodriver.cdp.runtime.RemoteObjectId" title="nodriver.cdp.runtime.RemoteObjectId"><code class="xref py py-class docutils literal notranslate"><span class="pre">RemoteObjectId</span></code></a>]</span>) – <em>(Optional)</em> JavaScript object id of the node wrapper.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.dom.set_inspected_node">
<span class="sig-name descname"><span class="pre">set_inspected_node</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">node_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#set_inspected_node"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.set_inspected_node" title="Link to this definition">#</a></dt>
<dd><p>Enables console to refer to the node with given id via $x (see Command Line API for more details
$x functions).</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a></span>) – DOM node id to be accessible by means of $x command line API.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.dom.set_node_name">
<span class="sig-name descname"><span class="pre">set_node_name</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">node_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#set_node_name"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.set_node_name" title="Link to this definition">#</a></dt>
<dd><p>Sets node name for a node with given id.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a></span>) – Id of the node to set name for.</p></li>
<li><p><strong>name</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – New node’s name.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a>]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>New node’s id.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.dom.set_node_stack_traces_enabled">
<span class="sig-name descname"><span class="pre">set_node_stack_traces_enabled</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">enable</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#set_node_stack_traces_enabled"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.set_node_stack_traces_enabled" title="Link to this definition">#</a></dt>
<dd><p>Sets if stack traces should be captured for Nodes. See <code class="docutils literal notranslate"><span class="pre">Node.getNodeStackTraces</span></code>. Default is disabled.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>enable</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a></span>) – Enable or disable.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.dom.set_node_value">
<span class="sig-name descname"><span class="pre">set_node_value</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">node_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#set_node_value"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.set_node_value" title="Link to this definition">#</a></dt>
<dd><p>Sets node value for a node with given id.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a></span>) – Id of the node to set value for.</p></li>
<li><p><strong>value</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – New node’s value.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.dom.set_outer_html">
<span class="sig-name descname"><span class="pre">set_outer_html</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">node_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">outer_html</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#set_outer_html"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.set_outer_html" title="Link to this definition">#</a></dt>
<dd><p>Sets node HTML markup, returns new node id.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a></span>) – Id of the node to set markup for.</p></li>
<li><p><strong>outer_html</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – Outer HTML markup to set.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.dom.undo">
<span class="sig-name descname"><span class="pre">undo</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#undo"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.undo" title="Link to this definition">#</a></dt>
<dd><p>Undoes the last performed action.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

</section>
<section id="events">
<h2>Events<a class="headerlink" href="#events" title="Link to this heading">#</a></h2>
<p>Generally, you do not need to instantiate CDP events
yourself. Instead, the API creates events for you and then
you use the event’s attributes.</p>
<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.dom.AttributeModified">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">AttributeModified</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">node_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#AttributeModified"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.AttributeModified" title="Link to this definition">#</a></dt>
<dd><p>Fired when <code class="docutils literal notranslate"><span class="pre">Element</span></code>’s attribute is modified.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.AttributeModified.node_id">
<span class="sig-name descname"><span class="pre">node_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom.AttributeModified.node_id" title="Link to this definition">#</a></dt>
<dd><p>Id of the node that has changed.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.AttributeModified.name">
<span class="sig-name descname"><span class="pre">name</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom.AttributeModified.name" title="Link to this definition">#</a></dt>
<dd><p>Attribute name.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.AttributeModified.value">
<span class="sig-name descname"><span class="pre">value</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom.AttributeModified.value" title="Link to this definition">#</a></dt>
<dd><p>Attribute value.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.dom.AttributeRemoved">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">AttributeRemoved</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">node_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#AttributeRemoved"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.AttributeRemoved" title="Link to this definition">#</a></dt>
<dd><p>Fired when <code class="docutils literal notranslate"><span class="pre">Element</span></code>’s attribute is removed.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.AttributeRemoved.node_id">
<span class="sig-name descname"><span class="pre">node_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom.AttributeRemoved.node_id" title="Link to this definition">#</a></dt>
<dd><p>Id of the node that has changed.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.AttributeRemoved.name">
<span class="sig-name descname"><span class="pre">name</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom.AttributeRemoved.name" title="Link to this definition">#</a></dt>
<dd><p>A ttribute name.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.dom.CharacterDataModified">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">CharacterDataModified</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">node_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">character_data</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#CharacterDataModified"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.CharacterDataModified" title="Link to this definition">#</a></dt>
<dd><p>Mirrors <code class="docutils literal notranslate"><span class="pre">DOMCharacterDataModified</span></code> event.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.CharacterDataModified.node_id">
<span class="sig-name descname"><span class="pre">node_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom.CharacterDataModified.node_id" title="Link to this definition">#</a></dt>
<dd><p>Id of the node that has changed.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.CharacterDataModified.character_data">
<span class="sig-name descname"><span class="pre">character_data</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom.CharacterDataModified.character_data" title="Link to this definition">#</a></dt>
<dd><p>New text value.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.dom.ChildNodeCountUpdated">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">ChildNodeCountUpdated</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">node_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">child_node_count</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#ChildNodeCountUpdated"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.ChildNodeCountUpdated" title="Link to this definition">#</a></dt>
<dd><p>Fired when <code class="docutils literal notranslate"><span class="pre">Container</span></code>’s child node count has changed.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.ChildNodeCountUpdated.node_id">
<span class="sig-name descname"><span class="pre">node_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom.ChildNodeCountUpdated.node_id" title="Link to this definition">#</a></dt>
<dd><p>Id of the node that has changed.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.ChildNodeCountUpdated.child_node_count">
<span class="sig-name descname"><span class="pre">child_node_count</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom.ChildNodeCountUpdated.child_node_count" title="Link to this definition">#</a></dt>
<dd><p>New node count.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.dom.ChildNodeInserted">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">ChildNodeInserted</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">parent_node_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">previous_node_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">node</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#ChildNodeInserted"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.ChildNodeInserted" title="Link to this definition">#</a></dt>
<dd><p>Mirrors <code class="docutils literal notranslate"><span class="pre">DOMNodeInserted</span></code> event.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.ChildNodeInserted.parent_node_id">
<span class="sig-name descname"><span class="pre">parent_node_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom.ChildNodeInserted.parent_node_id" title="Link to this definition">#</a></dt>
<dd><p>Id of the node that has changed.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.ChildNodeInserted.previous_node_id">
<span class="sig-name descname"><span class="pre">previous_node_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom.ChildNodeInserted.previous_node_id" title="Link to this definition">#</a></dt>
<dd><p>Id of the previous sibling.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.ChildNodeInserted.node">
<span class="sig-name descname"><span class="pre">node</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.dom.Node" title="nodriver.cdp.dom.Node"><code class="xref py py-class docutils literal notranslate"><span class="pre">Node</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom.ChildNodeInserted.node" title="Link to this definition">#</a></dt>
<dd><p>Inserted node data.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.dom.ChildNodeRemoved">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">ChildNodeRemoved</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">parent_node_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">node_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#ChildNodeRemoved"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.ChildNodeRemoved" title="Link to this definition">#</a></dt>
<dd><p>Mirrors <code class="docutils literal notranslate"><span class="pre">DOMNodeRemoved</span></code> event.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.ChildNodeRemoved.parent_node_id">
<span class="sig-name descname"><span class="pre">parent_node_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom.ChildNodeRemoved.parent_node_id" title="Link to this definition">#</a></dt>
<dd><p>Parent id.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.ChildNodeRemoved.node_id">
<span class="sig-name descname"><span class="pre">node_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom.ChildNodeRemoved.node_id" title="Link to this definition">#</a></dt>
<dd><p>Id of the node that has been removed.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.dom.DistributedNodesUpdated">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">DistributedNodesUpdated</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">insertion_point_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">distributed_nodes</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#DistributedNodesUpdated"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.DistributedNodesUpdated" title="Link to this definition">#</a></dt>
<dd><p><strong>EXPERIMENTAL</strong></p>
<p>Called when distribution is changed.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.DistributedNodesUpdated.insertion_point_id">
<span class="sig-name descname"><span class="pre">insertion_point_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom.DistributedNodesUpdated.insertion_point_id" title="Link to this definition">#</a></dt>
<dd><p>Insertion point where distributed nodes were updated.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.DistributedNodesUpdated.distributed_nodes">
<span class="sig-name descname"><span class="pre">distributed_nodes</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.dom.BackendNode" title="nodriver.cdp.dom.BackendNode"><code class="xref py py-class docutils literal notranslate"><span class="pre">BackendNode</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.dom.DistributedNodesUpdated.distributed_nodes" title="Link to this definition">#</a></dt>
<dd><p>Distributed nodes for given insertion point.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.dom.DocumentUpdated">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">DocumentUpdated</span></span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#DocumentUpdated"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.DocumentUpdated" title="Link to this definition">#</a></dt>
<dd><p>Fired when <code class="docutils literal notranslate"><span class="pre">Document</span></code> has been totally updated. Node ids are no longer valid.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.dom.InlineStyleInvalidated">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">InlineStyleInvalidated</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">node_ids</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#InlineStyleInvalidated"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.InlineStyleInvalidated" title="Link to this definition">#</a></dt>
<dd><p><strong>EXPERIMENTAL</strong></p>
<p>Fired when <code class="docutils literal notranslate"><span class="pre">Element</span></code>’s inline style is modified via a CSS property modification.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.InlineStyleInvalidated.node_ids">
<span class="sig-name descname"><span class="pre">node_ids</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.dom.InlineStyleInvalidated.node_ids" title="Link to this definition">#</a></dt>
<dd><p>Ids of the nodes for which the inline styles have been invalidated.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.dom.PseudoElementAdded">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">PseudoElementAdded</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">parent_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pseudo_element</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#PseudoElementAdded"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.PseudoElementAdded" title="Link to this definition">#</a></dt>
<dd><p><strong>EXPERIMENTAL</strong></p>
<p>Called when a pseudo element is added to an element.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.PseudoElementAdded.parent_id">
<span class="sig-name descname"><span class="pre">parent_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom.PseudoElementAdded.parent_id" title="Link to this definition">#</a></dt>
<dd><p>Pseudo element’s parent element id.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.PseudoElementAdded.pseudo_element">
<span class="sig-name descname"><span class="pre">pseudo_element</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.dom.Node" title="nodriver.cdp.dom.Node"><code class="xref py py-class docutils literal notranslate"><span class="pre">Node</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom.PseudoElementAdded.pseudo_element" title="Link to this definition">#</a></dt>
<dd><p>The added pseudo element.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.dom.TopLayerElementsUpdated">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">TopLayerElementsUpdated</span></span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#TopLayerElementsUpdated"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.TopLayerElementsUpdated" title="Link to this definition">#</a></dt>
<dd><p><strong>EXPERIMENTAL</strong></p>
<p>Called when top layer elements are changed.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.dom.ScrollableFlagUpdated">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">ScrollableFlagUpdated</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">node_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">is_scrollable</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#ScrollableFlagUpdated"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.ScrollableFlagUpdated" title="Link to this definition">#</a></dt>
<dd><p><strong>EXPERIMENTAL</strong></p>
<p>Fired when a node’s scrollability state changes.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.ScrollableFlagUpdated.node_id">
<span class="sig-name descname"><span class="pre">node_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom.ScrollableFlagUpdated.node_id" title="Link to this definition">#</a></dt>
<dd><p>The id of the node.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.ScrollableFlagUpdated.is_scrollable">
<span class="sig-name descname"><span class="pre">is_scrollable</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom.ScrollableFlagUpdated.is_scrollable" title="Link to this definition">#</a></dt>
<dd><p>If the node is scrollable.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.dom.PseudoElementRemoved">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">PseudoElementRemoved</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">parent_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pseudo_element_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#PseudoElementRemoved"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.PseudoElementRemoved" title="Link to this definition">#</a></dt>
<dd><p><strong>EXPERIMENTAL</strong></p>
<p>Called when a pseudo element is removed from an element.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.PseudoElementRemoved.parent_id">
<span class="sig-name descname"><span class="pre">parent_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom.PseudoElementRemoved.parent_id" title="Link to this definition">#</a></dt>
<dd><p>Pseudo element’s parent element id.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.PseudoElementRemoved.pseudo_element_id">
<span class="sig-name descname"><span class="pre">pseudo_element_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom.PseudoElementRemoved.pseudo_element_id" title="Link to this definition">#</a></dt>
<dd><p>The removed pseudo element id.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.dom.SetChildNodes">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">SetChildNodes</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">parent_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">nodes</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#SetChildNodes"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.SetChildNodes" title="Link to this definition">#</a></dt>
<dd><p>Fired when backend wants to provide client with the missing DOM structure. This happens upon
most of the calls requesting node ids.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.SetChildNodes.parent_id">
<span class="sig-name descname"><span class="pre">parent_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom.SetChildNodes.parent_id" title="Link to this definition">#</a></dt>
<dd><p>Parent node id to populate with children.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.SetChildNodes.nodes">
<span class="sig-name descname"><span class="pre">nodes</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.dom.Node" title="nodriver.cdp.dom.Node"><code class="xref py py-class docutils literal notranslate"><span class="pre">Node</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.dom.SetChildNodes.nodes" title="Link to this definition">#</a></dt>
<dd><p>Child nodes array.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.dom.ShadowRootPopped">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">ShadowRootPopped</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">host_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">root_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#ShadowRootPopped"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.ShadowRootPopped" title="Link to this definition">#</a></dt>
<dd><p><strong>EXPERIMENTAL</strong></p>
<p>Called when shadow root is popped from the element.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.ShadowRootPopped.host_id">
<span class="sig-name descname"><span class="pre">host_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom.ShadowRootPopped.host_id" title="Link to this definition">#</a></dt>
<dd><p>Host element id.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.ShadowRootPopped.root_id">
<span class="sig-name descname"><span class="pre">root_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom.ShadowRootPopped.root_id" title="Link to this definition">#</a></dt>
<dd><p>Shadow root id.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.dom.ShadowRootPushed">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">ShadowRootPushed</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">host_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">root</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/dom.html#ShadowRootPushed"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.dom.ShadowRootPushed" title="Link to this definition">#</a></dt>
<dd><p><strong>EXPERIMENTAL</strong></p>
<p>Called when shadow root is pushed into the element.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.ShadowRootPushed.host_id">
<span class="sig-name descname"><span class="pre">host_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom.ShadowRootPushed.host_id" title="Link to this definition">#</a></dt>
<dd><p>Host element id.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.dom.ShadowRootPushed.root">
<span class="sig-name descname"><span class="pre">root</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.dom.Node" title="nodriver.cdp.dom.Node"><code class="xref py py-class docutils literal notranslate"><span class="pre">Node</span></code></a></em><a class="headerlink" href="#nodriver.cdp.dom.ShadowRootPushed.root" title="Link to this definition">#</a></dt>
<dd><p>Shadow root.</p>
</dd></dl>

</dd></dl>

</section>
</section>

        </article>
      </div>
      <footer>
        
        <div class="related-pages">
          <a class="next-page" href="dom_debugger.html">
              <div class="page-info">
                <div class="context">
                  <span>Next</span>
                </div>
                <div class="title">DOMDebugger</div>
              </div>
              <svg class="furo-related-icon"><use href="#svg-arrow-right"></use></svg>
            </a>
          <a class="prev-page" href="device_orientation.html">
              <svg class="furo-related-icon"><use href="#svg-arrow-right"></use></svg>
              <div class="page-info">
                <div class="context">
                  <span>Previous</span>
                </div>
                
                <div class="title">DeviceOrientation</div>
                
              </div>
            </a>
        </div>
        <div class="bottom-of-page">
          <div class="left-details">
            <div class="copyright">
                Copyright &#169; 2023, Author
            </div>
            Made with <a href="https://www.sphinx-doc.org/">Sphinx</a> and <a class="muted-link" href="https://pradyunsg.me">@pradyunsg</a>'s
            
            <a href="https://github.com/pradyunsg/furo">Furo</a>
            
          </div>
          <div class="right-details">
            
          </div>
        </div>
        
      </footer>
    </div>
    <aside class="toc-drawer">
      
      
      <div class="toc-sticky toc-scroll">
        <div class="toc-title-container">
          <span class="toc-title">
            On this page
          </span>
        </div>
        <div class="toc-tree-container">
          <div class="toc-tree">
            <ul>
<li><a class="reference internal" href="#">DOM</a><ul>
<li><a class="reference internal" href="#types">Types</a><ul>
<li><a class="reference internal" href="#nodriver.cdp.dom.NodeId"><code class="docutils literal notranslate"><span class="pre">NodeId</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.BackendNodeId"><code class="docutils literal notranslate"><span class="pre">BackendNodeId</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.BackendNode"><code class="docutils literal notranslate"><span class="pre">BackendNode</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.dom.BackendNode.node_type"><code class="docutils literal notranslate"><span class="pre">BackendNode.node_type</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.BackendNode.node_name"><code class="docutils literal notranslate"><span class="pre">BackendNode.node_name</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.BackendNode.backend_node_id"><code class="docutils literal notranslate"><span class="pre">BackendNode.backend_node_id</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.dom.PseudoType"><code class="docutils literal notranslate"><span class="pre">PseudoType</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.dom.PseudoType.FIRST_LINE"><code class="docutils literal notranslate"><span class="pre">PseudoType.FIRST_LINE</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.PseudoType.FIRST_LETTER"><code class="docutils literal notranslate"><span class="pre">PseudoType.FIRST_LETTER</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.PseudoType.CHECKMARK"><code class="docutils literal notranslate"><span class="pre">PseudoType.CHECKMARK</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.PseudoType.BEFORE"><code class="docutils literal notranslate"><span class="pre">PseudoType.BEFORE</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.PseudoType.AFTER"><code class="docutils literal notranslate"><span class="pre">PseudoType.AFTER</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.PseudoType.PICKER_ICON"><code class="docutils literal notranslate"><span class="pre">PseudoType.PICKER_ICON</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.PseudoType.MARKER"><code class="docutils literal notranslate"><span class="pre">PseudoType.MARKER</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.PseudoType.BACKDROP"><code class="docutils literal notranslate"><span class="pre">PseudoType.BACKDROP</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.PseudoType.COLUMN"><code class="docutils literal notranslate"><span class="pre">PseudoType.COLUMN</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.PseudoType.SELECTION"><code class="docutils literal notranslate"><span class="pre">PseudoType.SELECTION</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.PseudoType.SEARCH_TEXT"><code class="docutils literal notranslate"><span class="pre">PseudoType.SEARCH_TEXT</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.PseudoType.TARGET_TEXT"><code class="docutils literal notranslate"><span class="pre">PseudoType.TARGET_TEXT</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.PseudoType.SPELLING_ERROR"><code class="docutils literal notranslate"><span class="pre">PseudoType.SPELLING_ERROR</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.PseudoType.GRAMMAR_ERROR"><code class="docutils literal notranslate"><span class="pre">PseudoType.GRAMMAR_ERROR</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.PseudoType.HIGHLIGHT"><code class="docutils literal notranslate"><span class="pre">PseudoType.HIGHLIGHT</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.PseudoType.FIRST_LINE_INHERITED"><code class="docutils literal notranslate"><span class="pre">PseudoType.FIRST_LINE_INHERITED</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.PseudoType.SCROLL_MARKER"><code class="docutils literal notranslate"><span class="pre">PseudoType.SCROLL_MARKER</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.PseudoType.SCROLL_MARKER_GROUP"><code class="docutils literal notranslate"><span class="pre">PseudoType.SCROLL_MARKER_GROUP</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.PseudoType.SCROLL_BUTTON"><code class="docutils literal notranslate"><span class="pre">PseudoType.SCROLL_BUTTON</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.PseudoType.SCROLLBAR"><code class="docutils literal notranslate"><span class="pre">PseudoType.SCROLLBAR</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.PseudoType.SCROLLBAR_THUMB"><code class="docutils literal notranslate"><span class="pre">PseudoType.SCROLLBAR_THUMB</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.PseudoType.SCROLLBAR_BUTTON"><code class="docutils literal notranslate"><span class="pre">PseudoType.SCROLLBAR_BUTTON</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.PseudoType.SCROLLBAR_TRACK"><code class="docutils literal notranslate"><span class="pre">PseudoType.SCROLLBAR_TRACK</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.PseudoType.SCROLLBAR_TRACK_PIECE"><code class="docutils literal notranslate"><span class="pre">PseudoType.SCROLLBAR_TRACK_PIECE</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.PseudoType.SCROLLBAR_CORNER"><code class="docutils literal notranslate"><span class="pre">PseudoType.SCROLLBAR_CORNER</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.PseudoType.RESIZER"><code class="docutils literal notranslate"><span class="pre">PseudoType.RESIZER</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.PseudoType.INPUT_LIST_BUTTON"><code class="docutils literal notranslate"><span class="pre">PseudoType.INPUT_LIST_BUTTON</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.PseudoType.VIEW_TRANSITION"><code class="docutils literal notranslate"><span class="pre">PseudoType.VIEW_TRANSITION</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.PseudoType.VIEW_TRANSITION_GROUP"><code class="docutils literal notranslate"><span class="pre">PseudoType.VIEW_TRANSITION_GROUP</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.PseudoType.VIEW_TRANSITION_IMAGE_PAIR"><code class="docutils literal notranslate"><span class="pre">PseudoType.VIEW_TRANSITION_IMAGE_PAIR</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.PseudoType.VIEW_TRANSITION_OLD"><code class="docutils literal notranslate"><span class="pre">PseudoType.VIEW_TRANSITION_OLD</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.PseudoType.VIEW_TRANSITION_NEW"><code class="docutils literal notranslate"><span class="pre">PseudoType.VIEW_TRANSITION_NEW</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.PseudoType.PLACEHOLDER"><code class="docutils literal notranslate"><span class="pre">PseudoType.PLACEHOLDER</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.PseudoType.FILE_SELECTOR_BUTTON"><code class="docutils literal notranslate"><span class="pre">PseudoType.FILE_SELECTOR_BUTTON</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.PseudoType.DETAILS_CONTENT"><code class="docutils literal notranslate"><span class="pre">PseudoType.DETAILS_CONTENT</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.PseudoType.PICKER"><code class="docutils literal notranslate"><span class="pre">PseudoType.PICKER</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.dom.ShadowRootType"><code class="docutils literal notranslate"><span class="pre">ShadowRootType</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.dom.ShadowRootType.USER_AGENT"><code class="docutils literal notranslate"><span class="pre">ShadowRootType.USER_AGENT</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.ShadowRootType.OPEN_"><code class="docutils literal notranslate"><span class="pre">ShadowRootType.OPEN_</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.ShadowRootType.CLOSED"><code class="docutils literal notranslate"><span class="pre">ShadowRootType.CLOSED</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.dom.CompatibilityMode"><code class="docutils literal notranslate"><span class="pre">CompatibilityMode</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.dom.CompatibilityMode.QUIRKS_MODE"><code class="docutils literal notranslate"><span class="pre">CompatibilityMode.QUIRKS_MODE</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.CompatibilityMode.LIMITED_QUIRKS_MODE"><code class="docutils literal notranslate"><span class="pre">CompatibilityMode.LIMITED_QUIRKS_MODE</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.CompatibilityMode.NO_QUIRKS_MODE"><code class="docutils literal notranslate"><span class="pre">CompatibilityMode.NO_QUIRKS_MODE</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.dom.PhysicalAxes"><code class="docutils literal notranslate"><span class="pre">PhysicalAxes</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.dom.PhysicalAxes.HORIZONTAL"><code class="docutils literal notranslate"><span class="pre">PhysicalAxes.HORIZONTAL</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.PhysicalAxes.VERTICAL"><code class="docutils literal notranslate"><span class="pre">PhysicalAxes.VERTICAL</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.PhysicalAxes.BOTH"><code class="docutils literal notranslate"><span class="pre">PhysicalAxes.BOTH</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.dom.LogicalAxes"><code class="docutils literal notranslate"><span class="pre">LogicalAxes</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.dom.LogicalAxes.INLINE"><code class="docutils literal notranslate"><span class="pre">LogicalAxes.INLINE</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.LogicalAxes.BLOCK"><code class="docutils literal notranslate"><span class="pre">LogicalAxes.BLOCK</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.LogicalAxes.BOTH"><code class="docutils literal notranslate"><span class="pre">LogicalAxes.BOTH</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.dom.ScrollOrientation"><code class="docutils literal notranslate"><span class="pre">ScrollOrientation</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.dom.ScrollOrientation.HORIZONTAL"><code class="docutils literal notranslate"><span class="pre">ScrollOrientation.HORIZONTAL</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.ScrollOrientation.VERTICAL"><code class="docutils literal notranslate"><span class="pre">ScrollOrientation.VERTICAL</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.dom.Node"><code class="docutils literal notranslate"><span class="pre">Node</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.dom.Node.node_id"><code class="docutils literal notranslate"><span class="pre">Node.node_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.Node.backend_node_id"><code class="docutils literal notranslate"><span class="pre">Node.backend_node_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.Node.node_type"><code class="docutils literal notranslate"><span class="pre">Node.node_type</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.Node.node_name"><code class="docutils literal notranslate"><span class="pre">Node.node_name</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.Node.local_name"><code class="docutils literal notranslate"><span class="pre">Node.local_name</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.Node.node_value"><code class="docutils literal notranslate"><span class="pre">Node.node_value</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.Node.parent_id"><code class="docutils literal notranslate"><span class="pre">Node.parent_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.Node.child_node_count"><code class="docutils literal notranslate"><span class="pre">Node.child_node_count</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.Node.children"><code class="docutils literal notranslate"><span class="pre">Node.children</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.Node.attributes"><code class="docutils literal notranslate"><span class="pre">Node.attributes</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.Node.document_url"><code class="docutils literal notranslate"><span class="pre">Node.document_url</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.Node.base_url"><code class="docutils literal notranslate"><span class="pre">Node.base_url</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.Node.public_id"><code class="docutils literal notranslate"><span class="pre">Node.public_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.Node.system_id"><code class="docutils literal notranslate"><span class="pre">Node.system_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.Node.internal_subset"><code class="docutils literal notranslate"><span class="pre">Node.internal_subset</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.Node.xml_version"><code class="docutils literal notranslate"><span class="pre">Node.xml_version</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.Node.name"><code class="docutils literal notranslate"><span class="pre">Node.name</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.Node.value"><code class="docutils literal notranslate"><span class="pre">Node.value</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.Node.pseudo_type"><code class="docutils literal notranslate"><span class="pre">Node.pseudo_type</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.Node.pseudo_identifier"><code class="docutils literal notranslate"><span class="pre">Node.pseudo_identifier</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.Node.shadow_root_type"><code class="docutils literal notranslate"><span class="pre">Node.shadow_root_type</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.Node.frame_id"><code class="docutils literal notranslate"><span class="pre">Node.frame_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.Node.content_document"><code class="docutils literal notranslate"><span class="pre">Node.content_document</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.Node.shadow_roots"><code class="docutils literal notranslate"><span class="pre">Node.shadow_roots</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.Node.template_content"><code class="docutils literal notranslate"><span class="pre">Node.template_content</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.Node.pseudo_elements"><code class="docutils literal notranslate"><span class="pre">Node.pseudo_elements</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.Node.imported_document"><code class="docutils literal notranslate"><span class="pre">Node.imported_document</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.Node.distributed_nodes"><code class="docutils literal notranslate"><span class="pre">Node.distributed_nodes</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.Node.is_svg"><code class="docutils literal notranslate"><span class="pre">Node.is_svg</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.Node.compatibility_mode"><code class="docutils literal notranslate"><span class="pre">Node.compatibility_mode</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.Node.assigned_slot"><code class="docutils literal notranslate"><span class="pre">Node.assigned_slot</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.Node.is_scrollable"><code class="docutils literal notranslate"><span class="pre">Node.is_scrollable</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.dom.DetachedElementInfo"><code class="docutils literal notranslate"><span class="pre">DetachedElementInfo</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.dom.DetachedElementInfo.tree_node"><code class="docutils literal notranslate"><span class="pre">DetachedElementInfo.tree_node</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.DetachedElementInfo.retained_node_ids"><code class="docutils literal notranslate"><span class="pre">DetachedElementInfo.retained_node_ids</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.dom.RGBA"><code class="docutils literal notranslate"><span class="pre">RGBA</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.dom.RGBA.r"><code class="docutils literal notranslate"><span class="pre">RGBA.r</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.RGBA.g"><code class="docutils literal notranslate"><span class="pre">RGBA.g</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.RGBA.b"><code class="docutils literal notranslate"><span class="pre">RGBA.b</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.RGBA.a"><code class="docutils literal notranslate"><span class="pre">RGBA.a</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.dom.Quad"><code class="docutils literal notranslate"><span class="pre">Quad</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.BoxModel"><code class="docutils literal notranslate"><span class="pre">BoxModel</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.dom.BoxModel.content"><code class="docutils literal notranslate"><span class="pre">BoxModel.content</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.BoxModel.padding"><code class="docutils literal notranslate"><span class="pre">BoxModel.padding</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.BoxModel.border"><code class="docutils literal notranslate"><span class="pre">BoxModel.border</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.BoxModel.margin"><code class="docutils literal notranslate"><span class="pre">BoxModel.margin</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.BoxModel.width"><code class="docutils literal notranslate"><span class="pre">BoxModel.width</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.BoxModel.height"><code class="docutils literal notranslate"><span class="pre">BoxModel.height</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.BoxModel.shape_outside"><code class="docutils literal notranslate"><span class="pre">BoxModel.shape_outside</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.dom.ShapeOutsideInfo"><code class="docutils literal notranslate"><span class="pre">ShapeOutsideInfo</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.dom.ShapeOutsideInfo.bounds"><code class="docutils literal notranslate"><span class="pre">ShapeOutsideInfo.bounds</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.ShapeOutsideInfo.shape"><code class="docutils literal notranslate"><span class="pre">ShapeOutsideInfo.shape</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.ShapeOutsideInfo.margin_shape"><code class="docutils literal notranslate"><span class="pre">ShapeOutsideInfo.margin_shape</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.dom.Rect"><code class="docutils literal notranslate"><span class="pre">Rect</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.dom.Rect.x"><code class="docutils literal notranslate"><span class="pre">Rect.x</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.Rect.y"><code class="docutils literal notranslate"><span class="pre">Rect.y</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.Rect.width"><code class="docutils literal notranslate"><span class="pre">Rect.width</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.Rect.height"><code class="docutils literal notranslate"><span class="pre">Rect.height</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.dom.CSSComputedStyleProperty"><code class="docutils literal notranslate"><span class="pre">CSSComputedStyleProperty</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.dom.CSSComputedStyleProperty.name"><code class="docutils literal notranslate"><span class="pre">CSSComputedStyleProperty.name</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.CSSComputedStyleProperty.value"><code class="docutils literal notranslate"><span class="pre">CSSComputedStyleProperty.value</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#commands">Commands</a><ul>
<li><a class="reference internal" href="#nodriver.cdp.dom.collect_class_names_from_subtree"><code class="docutils literal notranslate"><span class="pre">collect_class_names_from_subtree()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.copy_to"><code class="docutils literal notranslate"><span class="pre">copy_to()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.describe_node"><code class="docutils literal notranslate"><span class="pre">describe_node()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.disable"><code class="docutils literal notranslate"><span class="pre">disable()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.discard_search_results"><code class="docutils literal notranslate"><span class="pre">discard_search_results()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.enable"><code class="docutils literal notranslate"><span class="pre">enable()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.focus"><code class="docutils literal notranslate"><span class="pre">focus()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.get_anchor_element"><code class="docutils literal notranslate"><span class="pre">get_anchor_element()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.get_attributes"><code class="docutils literal notranslate"><span class="pre">get_attributes()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.get_box_model"><code class="docutils literal notranslate"><span class="pre">get_box_model()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.get_container_for_node"><code class="docutils literal notranslate"><span class="pre">get_container_for_node()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.get_content_quads"><code class="docutils literal notranslate"><span class="pre">get_content_quads()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.get_detached_dom_nodes"><code class="docutils literal notranslate"><span class="pre">get_detached_dom_nodes()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.get_document"><code class="docutils literal notranslate"><span class="pre">get_document()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.get_element_by_relation"><code class="docutils literal notranslate"><span class="pre">get_element_by_relation()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.get_file_info"><code class="docutils literal notranslate"><span class="pre">get_file_info()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.get_flattened_document"><code class="docutils literal notranslate"><span class="pre">get_flattened_document()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.get_frame_owner"><code class="docutils literal notranslate"><span class="pre">get_frame_owner()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.get_node_for_location"><code class="docutils literal notranslate"><span class="pre">get_node_for_location()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.get_node_stack_traces"><code class="docutils literal notranslate"><span class="pre">get_node_stack_traces()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.get_nodes_for_subtree_by_style"><code class="docutils literal notranslate"><span class="pre">get_nodes_for_subtree_by_style()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.get_outer_html"><code class="docutils literal notranslate"><span class="pre">get_outer_html()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.get_querying_descendants_for_container"><code class="docutils literal notranslate"><span class="pre">get_querying_descendants_for_container()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.get_relayout_boundary"><code class="docutils literal notranslate"><span class="pre">get_relayout_boundary()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.get_search_results"><code class="docutils literal notranslate"><span class="pre">get_search_results()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.get_top_layer_elements"><code class="docutils literal notranslate"><span class="pre">get_top_layer_elements()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.hide_highlight"><code class="docutils literal notranslate"><span class="pre">hide_highlight()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.highlight_node"><code class="docutils literal notranslate"><span class="pre">highlight_node()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.highlight_rect"><code class="docutils literal notranslate"><span class="pre">highlight_rect()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.mark_undoable_state"><code class="docutils literal notranslate"><span class="pre">mark_undoable_state()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.move_to"><code class="docutils literal notranslate"><span class="pre">move_to()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.perform_search"><code class="docutils literal notranslate"><span class="pre">perform_search()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.push_node_by_path_to_frontend"><code class="docutils literal notranslate"><span class="pre">push_node_by_path_to_frontend()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.push_nodes_by_backend_ids_to_frontend"><code class="docutils literal notranslate"><span class="pre">push_nodes_by_backend_ids_to_frontend()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.query_selector"><code class="docutils literal notranslate"><span class="pre">query_selector()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.query_selector_all"><code class="docutils literal notranslate"><span class="pre">query_selector_all()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.redo"><code class="docutils literal notranslate"><span class="pre">redo()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.remove_attribute"><code class="docutils literal notranslate"><span class="pre">remove_attribute()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.remove_node"><code class="docutils literal notranslate"><span class="pre">remove_node()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.request_child_nodes"><code class="docutils literal notranslate"><span class="pre">request_child_nodes()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.request_node"><code class="docutils literal notranslate"><span class="pre">request_node()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.resolve_node"><code class="docutils literal notranslate"><span class="pre">resolve_node()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.scroll_into_view_if_needed"><code class="docutils literal notranslate"><span class="pre">scroll_into_view_if_needed()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.set_attribute_value"><code class="docutils literal notranslate"><span class="pre">set_attribute_value()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.set_attributes_as_text"><code class="docutils literal notranslate"><span class="pre">set_attributes_as_text()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.set_file_input_files"><code class="docutils literal notranslate"><span class="pre">set_file_input_files()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.set_inspected_node"><code class="docutils literal notranslate"><span class="pre">set_inspected_node()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.set_node_name"><code class="docutils literal notranslate"><span class="pre">set_node_name()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.set_node_stack_traces_enabled"><code class="docutils literal notranslate"><span class="pre">set_node_stack_traces_enabled()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.set_node_value"><code class="docutils literal notranslate"><span class="pre">set_node_value()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.set_outer_html"><code class="docutils literal notranslate"><span class="pre">set_outer_html()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.undo"><code class="docutils literal notranslate"><span class="pre">undo()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#events">Events</a><ul>
<li><a class="reference internal" href="#nodriver.cdp.dom.AttributeModified"><code class="docutils literal notranslate"><span class="pre">AttributeModified</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.dom.AttributeModified.node_id"><code class="docutils literal notranslate"><span class="pre">AttributeModified.node_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.AttributeModified.name"><code class="docutils literal notranslate"><span class="pre">AttributeModified.name</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.AttributeModified.value"><code class="docutils literal notranslate"><span class="pre">AttributeModified.value</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.dom.AttributeRemoved"><code class="docutils literal notranslate"><span class="pre">AttributeRemoved</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.dom.AttributeRemoved.node_id"><code class="docutils literal notranslate"><span class="pre">AttributeRemoved.node_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.AttributeRemoved.name"><code class="docutils literal notranslate"><span class="pre">AttributeRemoved.name</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.dom.CharacterDataModified"><code class="docutils literal notranslate"><span class="pre">CharacterDataModified</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.dom.CharacterDataModified.node_id"><code class="docutils literal notranslate"><span class="pre">CharacterDataModified.node_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.CharacterDataModified.character_data"><code class="docutils literal notranslate"><span class="pre">CharacterDataModified.character_data</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.dom.ChildNodeCountUpdated"><code class="docutils literal notranslate"><span class="pre">ChildNodeCountUpdated</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.dom.ChildNodeCountUpdated.node_id"><code class="docutils literal notranslate"><span class="pre">ChildNodeCountUpdated.node_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.ChildNodeCountUpdated.child_node_count"><code class="docutils literal notranslate"><span class="pre">ChildNodeCountUpdated.child_node_count</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.dom.ChildNodeInserted"><code class="docutils literal notranslate"><span class="pre">ChildNodeInserted</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.dom.ChildNodeInserted.parent_node_id"><code class="docutils literal notranslate"><span class="pre">ChildNodeInserted.parent_node_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.ChildNodeInserted.previous_node_id"><code class="docutils literal notranslate"><span class="pre">ChildNodeInserted.previous_node_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.ChildNodeInserted.node"><code class="docutils literal notranslate"><span class="pre">ChildNodeInserted.node</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.dom.ChildNodeRemoved"><code class="docutils literal notranslate"><span class="pre">ChildNodeRemoved</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.dom.ChildNodeRemoved.parent_node_id"><code class="docutils literal notranslate"><span class="pre">ChildNodeRemoved.parent_node_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.ChildNodeRemoved.node_id"><code class="docutils literal notranslate"><span class="pre">ChildNodeRemoved.node_id</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.dom.DistributedNodesUpdated"><code class="docutils literal notranslate"><span class="pre">DistributedNodesUpdated</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.dom.DistributedNodesUpdated.insertion_point_id"><code class="docutils literal notranslate"><span class="pre">DistributedNodesUpdated.insertion_point_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.DistributedNodesUpdated.distributed_nodes"><code class="docutils literal notranslate"><span class="pre">DistributedNodesUpdated.distributed_nodes</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.dom.DocumentUpdated"><code class="docutils literal notranslate"><span class="pre">DocumentUpdated</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.InlineStyleInvalidated"><code class="docutils literal notranslate"><span class="pre">InlineStyleInvalidated</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.dom.InlineStyleInvalidated.node_ids"><code class="docutils literal notranslate"><span class="pre">InlineStyleInvalidated.node_ids</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.dom.PseudoElementAdded"><code class="docutils literal notranslate"><span class="pre">PseudoElementAdded</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.dom.PseudoElementAdded.parent_id"><code class="docutils literal notranslate"><span class="pre">PseudoElementAdded.parent_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.PseudoElementAdded.pseudo_element"><code class="docutils literal notranslate"><span class="pre">PseudoElementAdded.pseudo_element</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.dom.TopLayerElementsUpdated"><code class="docutils literal notranslate"><span class="pre">TopLayerElementsUpdated</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.ScrollableFlagUpdated"><code class="docutils literal notranslate"><span class="pre">ScrollableFlagUpdated</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.dom.ScrollableFlagUpdated.node_id"><code class="docutils literal notranslate"><span class="pre">ScrollableFlagUpdated.node_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.ScrollableFlagUpdated.is_scrollable"><code class="docutils literal notranslate"><span class="pre">ScrollableFlagUpdated.is_scrollable</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.dom.PseudoElementRemoved"><code class="docutils literal notranslate"><span class="pre">PseudoElementRemoved</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.dom.PseudoElementRemoved.parent_id"><code class="docutils literal notranslate"><span class="pre">PseudoElementRemoved.parent_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.PseudoElementRemoved.pseudo_element_id"><code class="docutils literal notranslate"><span class="pre">PseudoElementRemoved.pseudo_element_id</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.dom.SetChildNodes"><code class="docutils literal notranslate"><span class="pre">SetChildNodes</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.dom.SetChildNodes.parent_id"><code class="docutils literal notranslate"><span class="pre">SetChildNodes.parent_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.SetChildNodes.nodes"><code class="docutils literal notranslate"><span class="pre">SetChildNodes.nodes</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.dom.ShadowRootPopped"><code class="docutils literal notranslate"><span class="pre">ShadowRootPopped</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.dom.ShadowRootPopped.host_id"><code class="docutils literal notranslate"><span class="pre">ShadowRootPopped.host_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.ShadowRootPopped.root_id"><code class="docutils literal notranslate"><span class="pre">ShadowRootPopped.root_id</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.dom.ShadowRootPushed"><code class="docutils literal notranslate"><span class="pre">ShadowRootPushed</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.dom.ShadowRootPushed.host_id"><code class="docutils literal notranslate"><span class="pre">ShadowRootPushed.host_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.dom.ShadowRootPushed.root"><code class="docutils literal notranslate"><span class="pre">ShadowRootPushed.root</span></code></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>

          </div>
        </div>
      </div>
      
      
    </aside>
  </div>
</div><script src="../../_static/documentation_options.js?v=5929fcd5"></script>
    <script src="../../_static/doctools.js?v=888ff710"></script>
    <script src="../../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../../_static/scripts/furo.js?v=32e29ea5"></script>
    </body>
</html>