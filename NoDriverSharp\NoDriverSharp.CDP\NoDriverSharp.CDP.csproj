<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <LangVersion>latest</LangVersion>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <PackageId>NoDriverSharp.CDP</PackageId>
    <Version>1.0.0</Version>
    <Authors>NoDriverSharp Contributors</Authors>
    <Description>Chrome DevTools Protocol classes for NoDriverSharp</Description>
    <PackageTags>cdp chrome devtools protocol</PackageTags>
    <RepositoryUrl>https://github.com/NoDriverSharp/NoDriverSharp</RepositoryUrl>
    <PackageLicenseExpression>MIT</PackageLicenseExpression>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="System.Text.Json" Version="8.0.5" />
  </ItemGroup>

</Project>
