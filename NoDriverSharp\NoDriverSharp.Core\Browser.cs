using System.Diagnostics;
using System.Text.Json;
using Microsoft.Extensions.Logging;

namespace NoDriverSharp.Core;

/// <summary>
/// Represents a browser instance and manages its lifecycle
/// </summary>
public class Browser : IAsyncDisposable
{
    private readonly ILogger<Browser> _logger;
    private readonly Config _config;
    private Process? _browserProcess;
    private Connection? _connection;
    private readonly List<Tab> _tabs = new();
    private bool _disposed = false;
    private string? _tempUserDataDir;

    /// <summary>
    /// Configuration used for this browser instance
    /// </summary>
    public Config Config => _config;

    /// <summary>
    /// All tabs currently open in this browser
    /// </summary>
    public IReadOnlyList<Tab> Tabs => _tabs.AsReadOnly();

    /// <summary>
    /// The main tab (first tab opened)
    /// </summary>
    public Tab? MainTab => _tabs.FirstOrDefault();

    /// <summary>
    /// Whether the browser process is still running
    /// </summary>
    public bool IsRunning => _browserProcess?.HasExited == false;

    /// <summary>
    /// The debugging URL for this browser instance
    /// </summary>
    public string? DebuggerUrl { get; private set; }

    /// <summary>
    /// Event fired when a new tab is created
    /// </summary>
    public event EventHandler<Tab>? TabCreated;

    /// <summary>
    /// Event fired when a tab is closed
    /// </summary>
    public event EventHandler<Tab>? TabClosed;

    private Browser(Config config, ILogger<Browser>? logger = null)
    {
        _config = config ?? throw new ArgumentNullException(nameof(config));
        _logger = logger ?? Microsoft.Extensions.Logging.Abstractions.NullLogger<Browser>.Instance;
    }

    /// <summary>
    /// Creates and starts a new browser instance
    /// </summary>
    public static async Task<Browser> CreateAsync(Config? config = null, ILogger<Browser>? logger = null, CancellationToken cancellationToken = default)
    {
        config ??= new Config();
        var browser = new Browser(config, logger);
        await browser.StartAsync(cancellationToken);
        return browser;
    }

    /// <summary>
    /// Starts the browser process and establishes connection
    /// </summary>
    private async Task StartAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(Browser));

        try
        {
            // Find browser executable if not specified
            var executablePath = _config.BrowserExecutablePath ?? Config.FindBrowserExecutable();
            if (string.IsNullOrEmpty(executablePath))
            {
                throw new BrowserStartupException("Could not find Chrome/Chromium executable. Please specify BrowserExecutablePath in config.");
            }

            // Create temp user data dir if needed
            if (string.IsNullOrEmpty(_config.UserDataDir))
            {
                _tempUserDataDir = Config.CreateTempUserDataDir();
                _config.UserDataDir = _tempUserDataDir;
            }

            // Find available port if not specified
            if (_config.Port == 0)
            {
                _config.Port = FindAvailablePort();
            }

            // Start browser process
            await StartBrowserProcessAsync(executablePath, cancellationToken);

            // Wait for debugging endpoint to be available
            await WaitForDebuggerEndpointAsync(cancellationToken);

            // Connect to browser and discover targets
            await ConnectToBrowserAsync(cancellationToken);

            _logger.LogInformation("Browser started successfully on port {Port}", _config.Port);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start browser");
            await DisposeAsync();
            throw;
        }
    }

    /// <summary>
    /// Starts the browser process
    /// </summary>
    private async Task StartBrowserProcessAsync(string executablePath, CancellationToken cancellationToken)
    {
        var args = _config.GetAllBrowserArgs();
        var startInfo = new ProcessStartInfo
        {
            FileName = executablePath,
            Arguments = string.Join(" ", args.Select(arg => arg.Contains(' ') ? $"\"{arg}\"" : arg)),
            UseShellExecute = false,
            RedirectStandardOutput = true,
            RedirectStandardError = true,
            CreateNoWindow = true
        };

        _logger.LogDebug("Starting browser: {FileName} {Arguments}", startInfo.FileName, startInfo.Arguments);

        _browserProcess = Process.Start(startInfo);
        if (_browserProcess == null)
        {
            throw new BrowserStartupException("Failed to start browser process");
        }

        // Give the browser a moment to start
        await Task.Delay(1000, cancellationToken);

        if (_browserProcess.HasExited)
        {
            var exitCode = _browserProcess.ExitCode;
            throw new BrowserStartupException($"Browser process exited immediately with code {exitCode}");
        }
    }

    /// <summary>
    /// Waits for the debugging endpoint to become available
    /// </summary>
    private async Task WaitForDebuggerEndpointAsync(CancellationToken cancellationToken)
    {
        var timeout = DateTime.UtcNow.Add(_config.StartupTimeout);
        var httpClient = new HttpClient();

        while (DateTime.UtcNow < timeout)
        {
            try
            {
                var response = await httpClient.GetStringAsync($"http://{_config.Host}:{_config.Port}/json/version", cancellationToken);
                var versionInfo = JsonSerializer.Deserialize<JsonElement>(response);
                
                if (versionInfo.TryGetProperty("webSocketDebuggerUrl", out var wsUrl))
                {
                    DebuggerUrl = wsUrl.GetString();
                    _logger.LogDebug("Debugger endpoint available: {Url}", DebuggerUrl);
                    return;
                }
            }
            catch (Exception ex) when (ex is HttpRequestException or TaskCanceledException)
            {
                // Endpoint not ready yet, continue waiting
            }

            await Task.Delay(100, cancellationToken);
        }

        throw new BrowserStartupException($"Debugger endpoint did not become available within {_config.StartupTimeout.TotalSeconds} seconds");
    }

    /// <summary>
    /// Connects to the browser and discovers initial targets
    /// </summary>
    private async Task ConnectToBrowserAsync(CancellationToken cancellationToken)
    {
        if (string.IsNullOrEmpty(DebuggerUrl))
            throw new ConnectionException("Debugger URL is not available");

        _connection = new Connection(DebuggerUrl, _logger);
        await _connection.ConnectAsync(cancellationToken);

        // Discover existing targets
        await DiscoverTargetsAsync(cancellationToken);
    }

    /// <summary>
    /// Discovers and connects to browser targets (tabs)
    /// </summary>
    private async Task DiscoverTargetsAsync(CancellationToken cancellationToken)
    {
        var httpClient = new HttpClient();
        var response = await httpClient.GetStringAsync($"http://{_config.Host}:{_config.Port}/json", cancellationToken);
        var targets = JsonSerializer.Deserialize<JsonElement[]>(response);

        foreach (var target in targets)
        {
            if (target.TryGetProperty("type", out var type) && type.GetString() == "page")
            {
                var tab = await Tab.CreateFromTargetAsync(target, this, _logger, cancellationToken);
                _tabs.Add(tab);
                TabCreated?.Invoke(this, tab);
            }
        }

        // If no tabs exist, create one
        if (_tabs.Count == 0)
        {
            await GetAsync("about:blank", cancellationToken: cancellationToken);
        }
    }

    /// <summary>
    /// Navigates to a URL, optionally in a new tab or window
    /// </summary>
    public async Task<Tab> GetAsync(string url, bool newTab = false, bool newWindow = false, CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(Browser));

        if (newTab || newWindow || _tabs.Count == 0)
        {
            return await CreateNewTabAsync(url, newWindow, cancellationToken);
        }
        else
        {
            var tab = MainTab!;
            await tab.NavigateAsync(url, cancellationToken);
            return tab;
        }
    }

    /// <summary>
    /// Creates a new tab
    /// </summary>
    private async Task<Tab> CreateNewTabAsync(string url, bool newWindow, CancellationToken cancellationToken)
    {
        var httpClient = new HttpClient();
        var createUrl = $"http://{_config.Host}:{_config.Port}/json/new?{Uri.EscapeDataString(url)}";
        
        var response = await httpClient.GetStringAsync(createUrl, cancellationToken);
        var targetInfo = JsonSerializer.Deserialize<JsonElement>(response);

        var tab = await Tab.CreateFromTargetAsync(targetInfo, this, _logger, cancellationToken);
        _tabs.Add(tab);
        TabCreated?.Invoke(this, tab);

        return tab;
    }

    /// <summary>
    /// Closes a specific tab
    /// </summary>
    public async Task CloseTabAsync(Tab tab, CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(Browser));

        if (!_tabs.Contains(tab))
            return;

        await tab.CloseAsync(cancellationToken);
        _tabs.Remove(tab);
        TabClosed?.Invoke(this, tab);
    }

    /// <summary>
    /// Stops the browser and cleans up resources
    /// </summary>
    public async Task StopAsync()
    {
        if (_disposed)
            return;

        try
        {
            // Close all tabs
            var tabCloseTasks = _tabs.Select(tab => tab.CloseAsync()).ToArray();
            await Task.WhenAll(tabCloseTasks);
            _tabs.Clear();

            // Close connection
            if (_connection != null)
            {
                await _connection.CloseAsync();
                _connection.Dispose();
                _connection = null;
            }

            // Stop browser process
            if (_browserProcess != null && !_browserProcess.HasExited)
            {
                _browserProcess.Kill();
                await _browserProcess.WaitForExitAsync();
            }

            // Clean up temp directory
            if (!_config.KeepUserDataDir && !string.IsNullOrEmpty(_tempUserDataDir) && Directory.Exists(_tempUserDataDir))
            {
                try
                {
                    Directory.Delete(_tempUserDataDir, true);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to delete temporary user data directory: {Dir}", _tempUserDataDir);
                }
            }

            _logger.LogInformation("Browser stopped successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error while stopping browser");
        }
    }

    /// <summary>
    /// Finds an available port for the debugging interface
    /// </summary>
    private static int FindAvailablePort()
    {
        using var socket = new System.Net.Sockets.TcpListener(System.Net.IPAddress.Loopback, 0);
        socket.Start();
        var port = ((System.Net.IPEndPoint)socket.LocalEndpoint).Port;
        socket.Stop();
        return port;
    }

    public async ValueTask DisposeAsync()
    {
        if (!_disposed)
        {
            _disposed = true;
            await StopAsync();
            _browserProcess?.Dispose();
        }
    }
}
