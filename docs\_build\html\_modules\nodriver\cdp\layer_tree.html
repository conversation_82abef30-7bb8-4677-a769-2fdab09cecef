<!doctype html>
<html class="no-js" lang="en" data-content_root="../../../">
  <head><meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width,initial-scale=1"/>
    <meta name="color-scheme" content="light dark"><link rel="index" title="Index" href="../../../genindex.html" /><link rel="search" title="Search" href="../../../search.html" />

    <!-- Generated with Sphinx 7.2.6 and Furo 2023.09.10 -->
        <title>nodriver.cdp.layer_tree - nodriver documentation</title>
      <link rel="stylesheet" type="text/css" href="../../../_static/pygments.css?v=fa44fd50" />
    <link rel="stylesheet" type="text/css" href="../../../_static/styles/furo.css?v=135e06be" />
    <link rel="stylesheet" type="text/css" href="../../../_static/styles/furo-extensions.css?v=36a5483c" />
    <link rel="stylesheet" type="text/css" href="../../../_static/./custom.css?v=3934642d" />
    
    


<style>
  body {
    --color-code-background: #eeffcc;
  --color-code-foreground: black;
  
  }
  @media not print {
    body[data-theme="dark"] {
      --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8eefba;
  --color-background-primary: #111112;
  --color-problematic: #e7aeae;
  
    }
    @media (prefers-color-scheme: dark) {
      body:not([data-theme="light"]) {
        --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8eefba;
  --color-background-primary: #111112;
  --color-problematic: #e7aeae;
  
      }
    }
  }
</style></head>
  <body>
    
    <script>
      document.body.dataset.theme = localStorage.getItem("theme") || "auto";
    </script>
    

<svg xmlns="http://www.w3.org/2000/svg" style="display: none;">
  <symbol id="svg-toc" viewBox="0 0 24 24">
    <title>Contents</title>
    <svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 1024 1024">
      <path d="M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM115.4 518.9L271.7 642c5.8 4.6 14.4.5 14.4-6.9V388.9c0-7.4-8.5-11.5-14.4-6.9L115.4 505.1a8.74 8.74 0 0 0 0 13.8z"/>
    </svg>
  </symbol>
  <symbol id="svg-menu" viewBox="0 0 24 24">
    <title>Menu</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-menu">
      <line x1="3" y1="12" x2="21" y2="12"></line>
      <line x1="3" y1="6" x2="21" y2="6"></line>
      <line x1="3" y1="18" x2="21" y2="18"></line>
    </svg>
  </symbol>
  <symbol id="svg-arrow-right" viewBox="0 0 24 24">
    <title>Expand</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-chevron-right">
      <polyline points="9 18 15 12 9 6"></polyline>
    </svg>
  </symbol>
  <symbol id="svg-sun" viewBox="0 0 24 24">
    <title>Light mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="feather-sun">
      <circle cx="12" cy="12" r="5"></circle>
      <line x1="12" y1="1" x2="12" y2="3"></line>
      <line x1="12" y1="21" x2="12" y2="23"></line>
      <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
      <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
      <line x1="1" y1="12" x2="3" y2="12"></line>
      <line x1="21" y1="12" x2="23" y2="12"></line>
      <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
      <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
    </svg>
  </symbol>
  <symbol id="svg-moon" viewBox="0 0 24 24">
    <title>Dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-moon">
      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
      <path d="M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z" />
    </svg>
  </symbol>
  <symbol id="svg-sun-half" viewBox="0 0 24 24">
    <title>Auto light/dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-shadow">
      <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
      <circle cx="12" cy="12" r="9" />
      <path d="M13 12h5" />
      <path d="M13 15h4" />
      <path d="M13 18h1" />
      <path d="M13 9h4" />
      <path d="M13 6h1" />
    </svg>
  </symbol>
</svg>

<input type="checkbox" class="sidebar-toggle" name="__navigation" id="__navigation">
<input type="checkbox" class="sidebar-toggle" name="__toc" id="__toc">
<label class="overlay sidebar-overlay" for="__navigation">
  <div class="visually-hidden">Hide navigation sidebar</div>
</label>
<label class="overlay toc-overlay" for="__toc">
  <div class="visually-hidden">Hide table of contents sidebar</div>
</label>



<div class="page">
  <header class="mobile-header">
    <div class="header-left">
      <label class="nav-overlay-icon" for="__navigation">
        <div class="visually-hidden">Toggle site navigation sidebar</div>
        <i class="icon"><svg><use href="#svg-menu"></use></svg></i>
      </label>
    </div>
    <div class="header-center">
      <a href="../../../index.html"><div class="brand">nodriver  documentation</div></a>
    </div>
    <div class="header-right">
      <div class="theme-toggle-container theme-toggle-header">
        <button class="theme-toggle">
          <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
          <svg class="theme-icon-when-auto"><use href="#svg-sun-half"></use></svg>
          <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
          <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
        </button>
      </div>
      <label class="toc-overlay-icon toc-header-icon no-toc" for="__toc">
        <div class="visually-hidden">Toggle table of contents sidebar</div>
        <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
      </label>
    </div>
  </header>
  <aside class="sidebar-drawer">
    <div class="sidebar-container">
      
      <div class="sidebar-sticky"><div class="sidebar-scroll"><a class="sidebar-brand" href="../../../index.html">
  
  
  <span class="sidebar-brand-text">nodriver  documentation</span>
  
</a><form class="sidebar-search-container" method="get" action="../../../search.html" role="search">
  <input class="sidebar-search" placeholder="Search" name="q" aria-label="Search">
  <input type="hidden" name="check_keywords" value="yes">
  <input type="hidden" name="area" value="default">
</form>
<div id="searchbox"></div><div class="sidebar-tree">
  <ul>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html">Quickstart guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#usage-example">usage example</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#more-complete-example">More complete example</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#custom-starting-options">Custom starting options</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#alternative-custom-options">Alternative custom options</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#proxies-socks5-authenticated-too">Proxies (socks5, authenticated too)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#concrete-example">Concrete example</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/classes/browser.html">Browser class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/classes/tab.html">Tab class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/classes/element.html">Element class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/classes/others_and_helpers.html">Other classes and Helper classes</a></li>
</ul>
<ul>
<li class="toctree-l1 has-children"><a class="reference internal" href="../../../nodriver/cdp.html">CDP object</a><input class="toctree-checkbox" id="toctree-checkbox-1" name="toctree-checkbox-1" role="switch" type="checkbox"/><label for="toctree-checkbox-1"><div class="visually-hidden">Toggle navigation of CDP object</div><i class="icon"><svg><use href="#svg-arrow-right"></use></svg></i></label><ul>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/accessibility.html">Accessibility</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/animation.html">Animation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/audits.html">Audits</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/autofill.html">Autofill</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/background_service.html">BackgroundService</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/bluetooth_emulation.html">BluetoothEmulation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/browser.html">Browser</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/cache_storage.html">CacheStorage</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/cast.html">Cast</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/console.html">Console</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/css.html">CSS</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/debugger.html">Debugger</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/device_access.html">DeviceAccess</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/device_orientation.html">DeviceOrientation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/dom.html">DOM</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/dom_debugger.html">DOMDebugger</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/dom_snapshot.html">DOMSnapshot</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/dom_storage.html">DOMStorage</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/emulation.html">Emulation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/event_breakpoints.html">EventBreakpoints</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/extensions.html">Extensions</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/fed_cm.html">FedCm</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/fetch.html">Fetch</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/file_system.html">FileSystem</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/headless_experimental.html">HeadlessExperimental</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/heap_profiler.html">HeapProfiler</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/indexed_db.html">IndexedDB</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/input_.html">Input</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/inspector.html">Inspector</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/io.html">IO</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/layer_tree.html">LayerTree</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/log.html">Log</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/media.html">Media</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/memory.html">Memory</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/network.html">Network</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/overlay.html">Overlay</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/page.html">Page</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/performance.html">Performance</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/performance_timeline.html">PerformanceTimeline</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/preload.html">Preload</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/profiler.html">Profiler</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/pwa.html">PWA</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/runtime.html">Runtime</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/schema.html">Schema</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/security.html">Security</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/service_worker.html">ServiceWorker</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/storage.html">Storage</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/system_info.html">SystemInfo</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/target.html">Target</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/tethering.html">Tethering</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/tracing.html">Tracing</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/web_audio.html">WebAudio</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/web_authn.html">WebAuthn</a></li>
</ul>
</li>
</ul>

</div>
</div>
      </div>
      
    </div>
  </aside>
  <div class="main">
    <div class="content">
      <div class="article-container">
        <a href="#" class="back-to-top muted-link">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
            <path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8v12z"></path>
          </svg>
          <span>Back to top</span>
        </a>
        <div class="content-icon-container">
          <div class="theme-toggle-container theme-toggle-content">
            <button class="theme-toggle">
              <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
              <svg class="theme-icon-when-auto"><use href="#svg-sun-half"></use></svg>
              <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
              <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
            </button>
          </div>
          <label class="toc-overlay-icon toc-content-icon no-toc" for="__toc">
            <div class="visually-hidden">Toggle table of contents sidebar</div>
            <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
          </label>
        </div>
        <article role="main">
          <h1>Source code for nodriver.cdp.layer_tree</h1><div class="highlight"><pre>
<span></span><span class="c1"># DO NOT EDIT THIS FILE!</span>
<span class="c1">#</span>
<span class="c1"># This file is generated from the CDP specification. If you need to make</span>
<span class="c1"># changes, edit the generator and regenerate all of the modules.</span>
<span class="c1">#</span>
<span class="c1"># CDP domain: LayerTree (experimental)</span>

<span class="kn">from</span> <span class="nn">__future__</span> <span class="kn">import</span> <span class="n">annotations</span>

<span class="kn">import</span> <span class="nn">typing</span>
<span class="kn">from</span> <span class="nn">dataclasses</span> <span class="kn">import</span> <span class="n">dataclass</span>

<span class="kn">from</span> <span class="nn">.</span> <span class="kn">import</span> <span class="n">dom</span>
<span class="kn">from</span> <span class="nn">.util</span> <span class="kn">import</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">event_class</span>


<div class="viewcode-block" id="LayerId">
<a class="viewcode-back" href="../../../nodriver/cdp/layer_tree.html#nodriver.cdp.layer_tree.LayerId">[docs]</a>
<span class="k">class</span> <span class="nc">LayerId</span><span class="p">(</span><span class="nb">str</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Unique Layer identifier.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">LayerId</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span>

    <span class="k">def</span> <span class="fm">__repr__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="s2">&quot;LayerId(</span><span class="si">{}</span><span class="s2">)&quot;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="fm">__repr__</span><span class="p">())</span></div>



<div class="viewcode-block" id="SnapshotId">
<a class="viewcode-back" href="../../../nodriver/cdp/layer_tree.html#nodriver.cdp.layer_tree.SnapshotId">[docs]</a>
<span class="k">class</span> <span class="nc">SnapshotId</span><span class="p">(</span><span class="nb">str</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Unique snapshot identifier.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">SnapshotId</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span>

    <span class="k">def</span> <span class="fm">__repr__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="s2">&quot;SnapshotId(</span><span class="si">{}</span><span class="s2">)&quot;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="fm">__repr__</span><span class="p">())</span></div>



<div class="viewcode-block" id="ScrollRect">
<a class="viewcode-back" href="../../../nodriver/cdp/layer_tree.html#nodriver.cdp.layer_tree.ScrollRect">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">ScrollRect</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Rectangle where scrolling happens on the main thread.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: Rectangle itself.</span>
    <span class="n">rect</span><span class="p">:</span> <span class="n">dom</span><span class="o">.</span><span class="n">Rect</span>

    <span class="c1">#: Reason for rectangle to force scrolling on the main thread</span>
    <span class="n">type_</span><span class="p">:</span> <span class="nb">str</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;rect&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">rect</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;type&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">type_</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">ScrollRect</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">rect</span><span class="o">=</span><span class="n">dom</span><span class="o">.</span><span class="n">Rect</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;rect&quot;</span><span class="p">]),</span>
            <span class="n">type_</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;type&quot;</span><span class="p">]),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="StickyPositionConstraint">
<a class="viewcode-back" href="../../../nodriver/cdp/layer_tree.html#nodriver.cdp.layer_tree.StickyPositionConstraint">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">StickyPositionConstraint</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Sticky position constraints.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: Layout rectangle of the sticky element before being shifted</span>
    <span class="n">sticky_box_rect</span><span class="p">:</span> <span class="n">dom</span><span class="o">.</span><span class="n">Rect</span>

    <span class="c1">#: Layout rectangle of the containing block of the sticky element</span>
    <span class="n">containing_block_rect</span><span class="p">:</span> <span class="n">dom</span><span class="o">.</span><span class="n">Rect</span>

    <span class="c1">#: The nearest sticky layer that shifts the sticky box</span>
    <span class="n">nearest_layer_shifting_sticky_box</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">LayerId</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: The nearest sticky layer that shifts the containing block</span>
    <span class="n">nearest_layer_shifting_containing_block</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">LayerId</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;stickyBoxRect&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">sticky_box_rect</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;containingBlockRect&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">containing_block_rect</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">nearest_layer_shifting_sticky_box</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;nearestLayerShiftingStickyBox&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">(</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">nearest_layer_shifting_sticky_box</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
            <span class="p">)</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">nearest_layer_shifting_containing_block</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;nearestLayerShiftingContainingBlock&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">(</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">nearest_layer_shifting_containing_block</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
            <span class="p">)</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">StickyPositionConstraint</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">sticky_box_rect</span><span class="o">=</span><span class="n">dom</span><span class="o">.</span><span class="n">Rect</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;stickyBoxRect&quot;</span><span class="p">]),</span>
            <span class="n">containing_block_rect</span><span class="o">=</span><span class="n">dom</span><span class="o">.</span><span class="n">Rect</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;containingBlockRect&quot;</span><span class="p">]),</span>
            <span class="n">nearest_layer_shifting_sticky_box</span><span class="o">=</span><span class="p">(</span>
                <span class="n">LayerId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;nearestLayerShiftingStickyBox&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;nearestLayerShiftingStickyBox&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">nearest_layer_shifting_containing_block</span><span class="o">=</span><span class="p">(</span>
                <span class="n">LayerId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;nearestLayerShiftingContainingBlock&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;nearestLayerShiftingContainingBlock&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="PictureTile">
<a class="viewcode-back" href="../../../nodriver/cdp/layer_tree.html#nodriver.cdp.layer_tree.PictureTile">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">PictureTile</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Serialized fragment of layer picture along with its offset within the layer.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: Offset from owning layer left boundary</span>
    <span class="n">x</span><span class="p">:</span> <span class="nb">float</span>

    <span class="c1">#: Offset from owning layer top boundary</span>
    <span class="n">y</span><span class="p">:</span> <span class="nb">float</span>

    <span class="c1">#: Base64-encoded snapshot data. (Encoded as a base64 string when passed over JSON)</span>
    <span class="n">picture</span><span class="p">:</span> <span class="nb">str</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;x&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">x</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;y&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">y</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;picture&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">picture</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">PictureTile</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">x</span><span class="o">=</span><span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;x&quot;</span><span class="p">]),</span>
            <span class="n">y</span><span class="o">=</span><span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;y&quot;</span><span class="p">]),</span>
            <span class="n">picture</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;picture&quot;</span><span class="p">]),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="Layer">
<a class="viewcode-back" href="../../../nodriver/cdp/layer_tree.html#nodriver.cdp.layer_tree.Layer">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">Layer</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Information about a compositing layer.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: The unique id for this layer.</span>
    <span class="n">layer_id</span><span class="p">:</span> <span class="n">LayerId</span>

    <span class="c1">#: Offset from parent layer, X coordinate.</span>
    <span class="n">offset_x</span><span class="p">:</span> <span class="nb">float</span>

    <span class="c1">#: Offset from parent layer, Y coordinate.</span>
    <span class="n">offset_y</span><span class="p">:</span> <span class="nb">float</span>

    <span class="c1">#: Layer width.</span>
    <span class="n">width</span><span class="p">:</span> <span class="nb">float</span>

    <span class="c1">#: Layer height.</span>
    <span class="n">height</span><span class="p">:</span> <span class="nb">float</span>

    <span class="c1">#: Indicates how many time this layer has painted.</span>
    <span class="n">paint_count</span><span class="p">:</span> <span class="nb">int</span>

    <span class="c1">#: Indicates whether this layer hosts any content, rather than being used for</span>
    <span class="c1">#: transform/scrolling purposes only.</span>
    <span class="n">draws_content</span><span class="p">:</span> <span class="nb">bool</span>

    <span class="c1">#: The id of parent (not present for root).</span>
    <span class="n">parent_layer_id</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">LayerId</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: The backend id for the node associated with this layer.</span>
    <span class="n">backend_node_id</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">dom</span><span class="o">.</span><span class="n">BackendNodeId</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: Transformation matrix for layer, default is identity matrix</span>
    <span class="n">transform</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="nb">float</span><span class="p">]]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: Transform anchor point X, absent if no transform specified</span>
    <span class="n">anchor_x</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">float</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: Transform anchor point Y, absent if no transform specified</span>
    <span class="n">anchor_y</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">float</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: Transform anchor point Z, absent if no transform specified</span>
    <span class="n">anchor_z</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">float</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: Set if layer is not visible.</span>
    <span class="n">invisible</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">bool</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: Rectangles scrolling on main thread only.</span>
    <span class="n">scroll_rects</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">ScrollRect</span><span class="p">]]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: Sticky position constraint information</span>
    <span class="n">sticky_position_constraint</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">StickyPositionConstraint</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;layerId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">layer_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;offsetX&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">offset_x</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;offsetY&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">offset_y</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;width&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">width</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;height&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">height</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;paintCount&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">paint_count</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;drawsContent&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">draws_content</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">parent_layer_id</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;parentLayerId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">parent_layer_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">backend_node_id</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;backendNodeId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">backend_node_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">transform</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;transform&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">transform</span><span class="p">]</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">anchor_x</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;anchorX&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">anchor_x</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">anchor_y</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;anchorY&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">anchor_y</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">anchor_z</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;anchorZ&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">anchor_z</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">invisible</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;invisible&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">invisible</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">scroll_rects</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;scrollRects&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">scroll_rects</span><span class="p">]</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">sticky_position_constraint</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;stickyPositionConstraint&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">sticky_position_constraint</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Layer</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">layer_id</span><span class="o">=</span><span class="n">LayerId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;layerId&quot;</span><span class="p">]),</span>
            <span class="n">offset_x</span><span class="o">=</span><span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;offsetX&quot;</span><span class="p">]),</span>
            <span class="n">offset_y</span><span class="o">=</span><span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;offsetY&quot;</span><span class="p">]),</span>
            <span class="n">width</span><span class="o">=</span><span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;width&quot;</span><span class="p">]),</span>
            <span class="n">height</span><span class="o">=</span><span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;height&quot;</span><span class="p">]),</span>
            <span class="n">paint_count</span><span class="o">=</span><span class="nb">int</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;paintCount&quot;</span><span class="p">]),</span>
            <span class="n">draws_content</span><span class="o">=</span><span class="nb">bool</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;drawsContent&quot;</span><span class="p">]),</span>
            <span class="n">parent_layer_id</span><span class="o">=</span><span class="p">(</span>
                <span class="n">LayerId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;parentLayerId&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;parentLayerId&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">backend_node_id</span><span class="o">=</span><span class="p">(</span>
                <span class="n">dom</span><span class="o">.</span><span class="n">BackendNodeId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;backendNodeId&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;backendNodeId&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">transform</span><span class="o">=</span><span class="p">(</span>
                <span class="p">[</span><span class="nb">float</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;transform&quot;</span><span class="p">]]</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;transform&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">anchor_x</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;anchorX&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;anchorX&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">anchor_y</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;anchorY&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;anchorY&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">anchor_z</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;anchorZ&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;anchorZ&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">invisible</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">bool</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;invisible&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;invisible&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">scroll_rects</span><span class="o">=</span><span class="p">(</span>
                <span class="p">[</span><span class="n">ScrollRect</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;scrollRects&quot;</span><span class="p">]]</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;scrollRects&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">sticky_position_constraint</span><span class="o">=</span><span class="p">(</span>
                <span class="n">StickyPositionConstraint</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;stickyPositionConstraint&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;stickyPositionConstraint&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="PaintProfile">
<a class="viewcode-back" href="../../../nodriver/cdp/layer_tree.html#nodriver.cdp.layer_tree.PaintProfile">[docs]</a>
<span class="k">class</span> <span class="nc">PaintProfile</span><span class="p">(</span><span class="nb">list</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Array of timings, one per paint step.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="nb">float</span><span class="p">]:</span>
        <span class="k">return</span> <span class="bp">self</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="nb">float</span><span class="p">])</span> <span class="o">-&gt;</span> <span class="n">PaintProfile</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span>

    <span class="k">def</span> <span class="fm">__repr__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="s2">&quot;PaintProfile(</span><span class="si">{}</span><span class="s2">)&quot;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="fm">__repr__</span><span class="p">())</span></div>



<div class="viewcode-block" id="compositing_reasons">
<a class="viewcode-back" href="../../../nodriver/cdp/layer_tree.html#nodriver.cdp.layer_tree.compositing_reasons">[docs]</a>
<span class="k">def</span> <span class="nf">compositing_reasons</span><span class="p">(</span>
        <span class="n">layer_id</span><span class="p">:</span> <span class="n">LayerId</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span>
    <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">typing</span><span class="o">.</span><span class="n">Tuple</span><span class="p">[</span><span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">],</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">]]</span>
<span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Provides the reasons why the given layer was composited.</span>

<span class="sd">    :param layer_id: The id of the layer for which we want to get the reasons it was composited.</span>
<span class="sd">    :returns: A tuple with the following items:</span>

<span class="sd">        0. **compositingReasons** - A list of strings specifying reasons for the given layer to become composited.</span>
<span class="sd">        1. **compositingReasonIds** - A list of strings specifying reason IDs for the given layer to become composited.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;layerId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">layer_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;LayerTree.compositingReasons&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span>
    <span class="k">return</span> <span class="p">(</span>
        <span class="p">[</span><span class="nb">str</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;compositingReasons&quot;</span><span class="p">]],</span>
        <span class="p">[</span><span class="nb">str</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;compositingReasonIds&quot;</span><span class="p">]],</span>
    <span class="p">)</span></div>



<div class="viewcode-block" id="disable">
<a class="viewcode-back" href="../../../nodriver/cdp/layer_tree.html#nodriver.cdp.layer_tree.disable">[docs]</a>
<span class="k">def</span> <span class="nf">disable</span><span class="p">()</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Disables compositing tree inspection.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;LayerTree.disable&quot;</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span></div>



<div class="viewcode-block" id="enable">
<a class="viewcode-back" href="../../../nodriver/cdp/layer_tree.html#nodriver.cdp.layer_tree.enable">[docs]</a>
<span class="k">def</span> <span class="nf">enable</span><span class="p">()</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Enables compositing tree inspection.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;LayerTree.enable&quot;</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span></div>



<div class="viewcode-block" id="load_snapshot">
<a class="viewcode-back" href="../../../nodriver/cdp/layer_tree.html#nodriver.cdp.layer_tree.load_snapshot">[docs]</a>
<span class="k">def</span> <span class="nf">load_snapshot</span><span class="p">(</span>
        <span class="n">tiles</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">PictureTile</span><span class="p">],</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">SnapshotId</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Returns the snapshot identifier.</span>

<span class="sd">    :param tiles: An array of tiles composing the snapshot.</span>
<span class="sd">    :returns: The id of the snapshot.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;tiles&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">tiles</span><span class="p">]</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;LayerTree.loadSnapshot&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span>
    <span class="k">return</span> <span class="n">SnapshotId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;snapshotId&quot;</span><span class="p">])</span></div>



<div class="viewcode-block" id="make_snapshot">
<a class="viewcode-back" href="../../../nodriver/cdp/layer_tree.html#nodriver.cdp.layer_tree.make_snapshot">[docs]</a>
<span class="k">def</span> <span class="nf">make_snapshot</span><span class="p">(</span>
        <span class="n">layer_id</span><span class="p">:</span> <span class="n">LayerId</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">SnapshotId</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Returns the layer snapshot identifier.</span>

<span class="sd">    :param layer_id: The id of the layer.</span>
<span class="sd">    :returns: The id of the layer snapshot.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;layerId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">layer_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;LayerTree.makeSnapshot&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span>
    <span class="k">return</span> <span class="n">SnapshotId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;snapshotId&quot;</span><span class="p">])</span></div>



<div class="viewcode-block" id="profile_snapshot">
<a class="viewcode-back" href="../../../nodriver/cdp/layer_tree.html#nodriver.cdp.layer_tree.profile_snapshot">[docs]</a>
<span class="k">def</span> <span class="nf">profile_snapshot</span><span class="p">(</span>
        <span class="n">snapshot_id</span><span class="p">:</span> <span class="n">SnapshotId</span><span class="p">,</span>
        <span class="n">min_repeat_count</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">int</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
        <span class="n">min_duration</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">float</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
        <span class="n">clip_rect</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">dom</span><span class="o">.</span><span class="n">Rect</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">PaintProfile</span><span class="p">]]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    :param snapshot_id: The id of the layer snapshot.</span>
<span class="sd">    :param min_repeat_count: *(Optional)* The maximum number of times to replay the snapshot (1, if not specified).</span>
<span class="sd">    :param min_duration: *(Optional)* The minimum duration (in seconds) to replay the snapshot.</span>
<span class="sd">    :param clip_rect: *(Optional)* The clip rectangle to apply when replaying the snapshot.</span>
<span class="sd">    :returns: The array of paint profiles, one per run.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;snapshotId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">snapshot_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="k">if</span> <span class="n">min_repeat_count</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;minRepeatCount&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">min_repeat_count</span>
    <span class="k">if</span> <span class="n">min_duration</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;minDuration&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">min_duration</span>
    <span class="k">if</span> <span class="n">clip_rect</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;clipRect&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">clip_rect</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;LayerTree.profileSnapshot&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span>
    <span class="k">return</span> <span class="p">[</span><span class="n">PaintProfile</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;timings&quot;</span><span class="p">]]</span></div>



<div class="viewcode-block" id="release_snapshot">
<a class="viewcode-back" href="../../../nodriver/cdp/layer_tree.html#nodriver.cdp.layer_tree.release_snapshot">[docs]</a>
<span class="k">def</span> <span class="nf">release_snapshot</span><span class="p">(</span>
        <span class="n">snapshot_id</span><span class="p">:</span> <span class="n">SnapshotId</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Releases layer snapshot captured by the back-end.</span>

<span class="sd">    :param snapshot_id: The id of the layer snapshot.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;snapshotId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">snapshot_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;LayerTree.releaseSnapshot&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span></div>



<div class="viewcode-block" id="replay_snapshot">
<a class="viewcode-back" href="../../../nodriver/cdp/layer_tree.html#nodriver.cdp.layer_tree.replay_snapshot">[docs]</a>
<span class="k">def</span> <span class="nf">replay_snapshot</span><span class="p">(</span>
        <span class="n">snapshot_id</span><span class="p">:</span> <span class="n">SnapshotId</span><span class="p">,</span>
        <span class="n">from_step</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">int</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
        <span class="n">to_step</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">int</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
        <span class="n">scale</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">float</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="nb">str</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Replays the layer snapshot and returns the resulting bitmap.</span>

<span class="sd">    :param snapshot_id: The id of the layer snapshot.</span>
<span class="sd">    :param from_step: *(Optional)* The first step to replay from (replay from the very start if not specified).</span>
<span class="sd">    :param to_step: *(Optional)* The last step to replay to (replay till the end if not specified).</span>
<span class="sd">    :param scale: *(Optional)* The scale to apply while replaying (defaults to 1).</span>
<span class="sd">    :returns: A data: URL for resulting image.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;snapshotId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">snapshot_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="k">if</span> <span class="n">from_step</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;fromStep&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">from_step</span>
    <span class="k">if</span> <span class="n">to_step</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;toStep&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">to_step</span>
    <span class="k">if</span> <span class="n">scale</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;scale&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">scale</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;LayerTree.replaySnapshot&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span>
    <span class="k">return</span> <span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;dataURL&quot;</span><span class="p">])</span></div>



<div class="viewcode-block" id="snapshot_command_log">
<a class="viewcode-back" href="../../../nodriver/cdp/layer_tree.html#nodriver.cdp.layer_tree.snapshot_command_log">[docs]</a>
<span class="k">def</span> <span class="nf">snapshot_command_log</span><span class="p">(</span>
        <span class="n">snapshot_id</span><span class="p">:</span> <span class="n">SnapshotId</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="nb">dict</span><span class="p">]]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Replays the layer snapshot and returns canvas log.</span>

<span class="sd">    :param snapshot_id: The id of the layer snapshot.</span>
<span class="sd">    :returns: The array of canvas function calls.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;snapshotId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">snapshot_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;LayerTree.snapshotCommandLog&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span>
    <span class="k">return</span> <span class="p">[</span><span class="nb">dict</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;commandLog&quot;</span><span class="p">]]</span></div>



<div class="viewcode-block" id="LayerPainted">
<a class="viewcode-back" href="../../../nodriver/cdp/layer_tree.html#nodriver.cdp.layer_tree.LayerPainted">[docs]</a>
<span class="nd">@event_class</span><span class="p">(</span><span class="s2">&quot;LayerTree.layerPainted&quot;</span><span class="p">)</span>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">LayerPainted</span><span class="p">:</span>
    <span class="c1">#: The id of the painted layer.</span>
    <span class="n">layer_id</span><span class="p">:</span> <span class="n">LayerId</span>
    <span class="c1">#: Clip rectangle.</span>
    <span class="n">clip</span><span class="p">:</span> <span class="n">dom</span><span class="o">.</span><span class="n">Rect</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">LayerPainted</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">layer_id</span><span class="o">=</span><span class="n">LayerId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;layerId&quot;</span><span class="p">]),</span>
            <span class="n">clip</span><span class="o">=</span><span class="n">dom</span><span class="o">.</span><span class="n">Rect</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;clip&quot;</span><span class="p">]),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="LayerTreeDidChange">
<a class="viewcode-back" href="../../../nodriver/cdp/layer_tree.html#nodriver.cdp.layer_tree.LayerTreeDidChange">[docs]</a>
<span class="nd">@event_class</span><span class="p">(</span><span class="s2">&quot;LayerTree.layerTreeDidChange&quot;</span><span class="p">)</span>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">LayerTreeDidChange</span><span class="p">:</span>
    <span class="c1">#: Layer tree, absent if not in the compositing mode.</span>
    <span class="n">layers</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">Layer</span><span class="p">]]</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">LayerTreeDidChange</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">layers</span><span class="o">=</span><span class="p">(</span>
                <span class="p">[</span><span class="n">Layer</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;layers&quot;</span><span class="p">]]</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;layers&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">)</span>
        <span class="p">)</span></div>

</pre></div>
        </article>
      </div>
      <footer>
        
        <div class="related-pages">
          
          
        </div>
        <div class="bottom-of-page">
          <div class="left-details">
            <div class="copyright">
                Copyright &#169; 2023, Author
            </div>
            Made with <a href="https://www.sphinx-doc.org/">Sphinx</a> and <a class="muted-link" href="https://pradyunsg.me">@pradyunsg</a>'s
            
            <a href="https://github.com/pradyunsg/furo">Furo</a>
            
          </div>
          <div class="right-details">
            
          </div>
        </div>
        
      </footer>
    </div>
    <aside class="toc-drawer no-toc">
      
      
      
    </aside>
  </div>
</div><script src="../../../_static/documentation_options.js?v=5929fcd5"></script>
    <script src="../../../_static/doctools.js?v=888ff710"></script>
    <script src="../../../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../../../_static/scripts/furo.js?v=32e29ea5"></script>
    </body>
</html>