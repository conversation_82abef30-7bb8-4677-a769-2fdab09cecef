<!doctype html>
<html class="no-js" lang="en" data-content_root="../../">
  <head><meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width,initial-scale=1"/>
    <meta name="color-scheme" content="light dark"><meta name="viewport" content="width=device-width, initial-scale=1" />
<link rel="index" title="Index" href="../../genindex.html" /><link rel="search" title="Search" href="../../search.html" /><link rel="next" title="Other classes and Helper classes" href="others_and_helpers.html" /><link rel="prev" title="Tab class" href="tab.html" />

    <!-- Generated with Sphinx 7.2.6 and Furo 2023.09.10 -->
        <title>Element class - nodriver documentation</title>
      <link rel="stylesheet" type="text/css" href="../../_static/pygments.css?v=fa44fd50" />
    <link rel="stylesheet" type="text/css" href="../../_static/styles/furo.css?v=135e06be" />
    <link rel="stylesheet" type="text/css" href="../../_static/styles/furo-extensions.css?v=36a5483c" />
    <link rel="stylesheet" type="text/css" href="../../_static/./custom.css?v=3934642d" />
    
    


<style>
  body {
    --color-code-background: #eeffcc;
  --color-code-foreground: black;
  
  }
  @media not print {
    body[data-theme="dark"] {
      --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8eefba;
  --color-background-primary: #111112;
  --color-problematic: #e7aeae;
  
    }
    @media (prefers-color-scheme: dark) {
      body:not([data-theme="light"]) {
        --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8eefba;
  --color-background-primary: #111112;
  --color-problematic: #e7aeae;
  
      }
    }
  }
</style></head>
  <body>
    
    <script>
      document.body.dataset.theme = localStorage.getItem("theme") || "auto";
    </script>
    

<svg xmlns="http://www.w3.org/2000/svg" style="display: none;">
  <symbol id="svg-toc" viewBox="0 0 24 24">
    <title>Contents</title>
    <svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 1024 1024">
      <path d="M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM115.4 518.9L271.7 642c5.8 4.6 14.4.5 14.4-6.9V388.9c0-7.4-8.5-11.5-14.4-6.9L115.4 505.1a8.74 8.74 0 0 0 0 13.8z"/>
    </svg>
  </symbol>
  <symbol id="svg-menu" viewBox="0 0 24 24">
    <title>Menu</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-menu">
      <line x1="3" y1="12" x2="21" y2="12"></line>
      <line x1="3" y1="6" x2="21" y2="6"></line>
      <line x1="3" y1="18" x2="21" y2="18"></line>
    </svg>
  </symbol>
  <symbol id="svg-arrow-right" viewBox="0 0 24 24">
    <title>Expand</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-chevron-right">
      <polyline points="9 18 15 12 9 6"></polyline>
    </svg>
  </symbol>
  <symbol id="svg-sun" viewBox="0 0 24 24">
    <title>Light mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="feather-sun">
      <circle cx="12" cy="12" r="5"></circle>
      <line x1="12" y1="1" x2="12" y2="3"></line>
      <line x1="12" y1="21" x2="12" y2="23"></line>
      <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
      <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
      <line x1="1" y1="12" x2="3" y2="12"></line>
      <line x1="21" y1="12" x2="23" y2="12"></line>
      <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
      <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
    </svg>
  </symbol>
  <symbol id="svg-moon" viewBox="0 0 24 24">
    <title>Dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-moon">
      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
      <path d="M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z" />
    </svg>
  </symbol>
  <symbol id="svg-sun-half" viewBox="0 0 24 24">
    <title>Auto light/dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-shadow">
      <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
      <circle cx="12" cy="12" r="9" />
      <path d="M13 12h5" />
      <path d="M13 15h4" />
      <path d="M13 18h1" />
      <path d="M13 9h4" />
      <path d="M13 6h1" />
    </svg>
  </symbol>
</svg>

<input type="checkbox" class="sidebar-toggle" name="__navigation" id="__navigation">
<input type="checkbox" class="sidebar-toggle" name="__toc" id="__toc">
<label class="overlay sidebar-overlay" for="__navigation">
  <div class="visually-hidden">Hide navigation sidebar</div>
</label>
<label class="overlay toc-overlay" for="__toc">
  <div class="visually-hidden">Hide table of contents sidebar</div>
</label>



<div class="page">
  <header class="mobile-header">
    <div class="header-left">
      <label class="nav-overlay-icon" for="__navigation">
        <div class="visually-hidden">Toggle site navigation sidebar</div>
        <i class="icon"><svg><use href="#svg-menu"></use></svg></i>
      </label>
    </div>
    <div class="header-center">
      <a href="../../index.html"><div class="brand">nodriver  documentation</div></a>
    </div>
    <div class="header-right">
      <div class="theme-toggle-container theme-toggle-header">
        <button class="theme-toggle">
          <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
          <svg class="theme-icon-when-auto"><use href="#svg-sun-half"></use></svg>
          <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
          <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
        </button>
      </div>
      <label class="toc-overlay-icon toc-header-icon" for="__toc">
        <div class="visually-hidden">Toggle table of contents sidebar</div>
        <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
      </label>
    </div>
  </header>
  <aside class="sidebar-drawer">
    <div class="sidebar-container">
      
      <div class="sidebar-sticky"><div class="sidebar-scroll"><a class="sidebar-brand" href="../../index.html">
  
  
  <span class="sidebar-brand-text">nodriver  documentation</span>
  
</a><form class="sidebar-search-container" method="get" action="../../search.html" role="search">
  <input class="sidebar-search" placeholder="Search" name="q" aria-label="Search">
  <input type="hidden" name="check_keywords" value="yes">
  <input type="hidden" name="area" value="default">
</form>
<div id="searchbox"></div><div class="sidebar-tree">
  <ul>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html">Quickstart guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#usage-example">usage example</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#more-complete-example">More complete example</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#custom-starting-options">Custom starting options</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#alternative-custom-options">Alternative custom options</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#proxies-socks5-authenticated-too">Proxies (socks5, authenticated too)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#concrete-example">Concrete example</a></li>
</ul>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="browser.html">Browser class</a></li>
<li class="toctree-l1"><a class="reference internal" href="tab.html">Tab class</a></li>
<li class="toctree-l1 current current-page"><a class="current reference internal" href="#">Element class</a></li>
<li class="toctree-l1"><a class="reference internal" href="others_and_helpers.html">Other classes and Helper classes</a></li>
</ul>
<ul>
<li class="toctree-l1 has-children"><a class="reference internal" href="../cdp.html">CDP object</a><input class="toctree-checkbox" id="toctree-checkbox-1" name="toctree-checkbox-1" role="switch" type="checkbox"/><label for="toctree-checkbox-1"><div class="visually-hidden">Toggle navigation of CDP object</div><i class="icon"><svg><use href="#svg-arrow-right"></use></svg></i></label><ul>
<li class="toctree-l2"><a class="reference internal" href="../cdp/accessibility.html">Accessibility</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/animation.html">Animation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/audits.html">Audits</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/autofill.html">Autofill</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/background_service.html">BackgroundService</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/bluetooth_emulation.html">BluetoothEmulation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/browser.html">Browser</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/cache_storage.html">CacheStorage</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/cast.html">Cast</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/console.html">Console</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/css.html">CSS</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/debugger.html">Debugger</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/device_access.html">DeviceAccess</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/device_orientation.html">DeviceOrientation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/dom.html">DOM</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/dom_debugger.html">DOMDebugger</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/dom_snapshot.html">DOMSnapshot</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/dom_storage.html">DOMStorage</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/emulation.html">Emulation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/event_breakpoints.html">EventBreakpoints</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/extensions.html">Extensions</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/fed_cm.html">FedCm</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/fetch.html">Fetch</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/file_system.html">FileSystem</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/headless_experimental.html">HeadlessExperimental</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/heap_profiler.html">HeapProfiler</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/indexed_db.html">IndexedDB</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/input_.html">Input</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/inspector.html">Inspector</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/io.html">IO</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/layer_tree.html">LayerTree</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/log.html">Log</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/media.html">Media</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/memory.html">Memory</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/network.html">Network</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/overlay.html">Overlay</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/page.html">Page</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/performance.html">Performance</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/performance_timeline.html">PerformanceTimeline</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/preload.html">Preload</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/profiler.html">Profiler</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/pwa.html">PWA</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/runtime.html">Runtime</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/schema.html">Schema</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/security.html">Security</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/service_worker.html">ServiceWorker</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/storage.html">Storage</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/system_info.html">SystemInfo</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/target.html">Target</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/tethering.html">Tethering</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/tracing.html">Tracing</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/web_audio.html">WebAudio</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/web_authn.html">WebAuthn</a></li>
</ul>
</li>
</ul>

</div>
</div>
      </div>
      
    </div>
  </aside>
  <div class="main">
    <div class="content">
      <div class="article-container">
        <a href="#" class="back-to-top muted-link">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
            <path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8v12z"></path>
          </svg>
          <span>Back to top</span>
        </a>
        <div class="content-icon-container">
          
<div class="theme-toggle-container theme-toggle-content">
            <button class="theme-toggle">
              <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
              <svg class="theme-icon-when-auto"><use href="#svg-sun-half"></use></svg>
              <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
              <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
            </button>
          </div>
          <label class="toc-overlay-icon toc-content-icon" for="__toc">
            <div class="visually-hidden">Toggle table of contents sidebar</div>
            <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
          </label>
        </div>
        <article role="main">
          <section id="element-class">
<span id="element"></span><h1>Element class<a class="headerlink" href="#element-class" title="Link to this heading">#</a></h1>
<p>Some words about the Element class</p>
<dl class="py class">
<dt class="sig sig-object py" id="nodriver.Element">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">Element</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">node</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">tab</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">tree</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/element.html#Element"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Element" title="Link to this definition">#</a></dt>
<dd><dl class="py property">
<dt class="sig sig-object py" id="nodriver.Element.tag">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">tag</span></span><a class="headerlink" href="#nodriver.Element.tag" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="nodriver.Element.tag_name">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">tag_name</span></span><a class="headerlink" href="#nodriver.Element.tag_name" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="nodriver.Element.node_id">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">node_id</span></span><a class="headerlink" href="#nodriver.Element.node_id" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="nodriver.Element.backend_node_id">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">backend_node_id</span></span><a class="headerlink" href="#nodriver.Element.backend_node_id" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="nodriver.Element.node_type">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">node_type</span></span><a class="headerlink" href="#nodriver.Element.node_type" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="nodriver.Element.node_name">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">node_name</span></span><a class="headerlink" href="#nodriver.Element.node_name" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="nodriver.Element.local_name">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">local_name</span></span><a class="headerlink" href="#nodriver.Element.local_name" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="nodriver.Element.node_value">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">node_value</span></span><a class="headerlink" href="#nodriver.Element.node_value" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="nodriver.Element.parent_id">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">parent_id</span></span><a class="headerlink" href="#nodriver.Element.parent_id" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="nodriver.Element.child_node_count">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">child_node_count</span></span><a class="headerlink" href="#nodriver.Element.child_node_count" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="nodriver.Element.attributes">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">attributes</span></span><a class="headerlink" href="#nodriver.Element.attributes" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="nodriver.Element.document_url">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">document_url</span></span><a class="headerlink" href="#nodriver.Element.document_url" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="nodriver.Element.base_url">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">base_url</span></span><a class="headerlink" href="#nodriver.Element.base_url" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="nodriver.Element.public_id">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">public_id</span></span><a class="headerlink" href="#nodriver.Element.public_id" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="nodriver.Element.system_id">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">system_id</span></span><a class="headerlink" href="#nodriver.Element.system_id" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="nodriver.Element.internal_subset">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">internal_subset</span></span><a class="headerlink" href="#nodriver.Element.internal_subset" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="nodriver.Element.xml_version">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">xml_version</span></span><a class="headerlink" href="#nodriver.Element.xml_version" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="nodriver.Element.value">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">value</span></span><a class="headerlink" href="#nodriver.Element.value" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="nodriver.Element.pseudo_type">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">pseudo_type</span></span><a class="headerlink" href="#nodriver.Element.pseudo_type" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="nodriver.Element.pseudo_identifier">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">pseudo_identifier</span></span><a class="headerlink" href="#nodriver.Element.pseudo_identifier" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="nodriver.Element.shadow_root_type">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">shadow_root_type</span></span><a class="headerlink" href="#nodriver.Element.shadow_root_type" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="nodriver.Element.frame_id">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">frame_id</span></span><a class="headerlink" href="#nodriver.Element.frame_id" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="nodriver.Element.content_document">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">content_document</span></span><a class="headerlink" href="#nodriver.Element.content_document" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="nodriver.Element.shadow_roots">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">shadow_roots</span></span><a class="headerlink" href="#nodriver.Element.shadow_roots" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="nodriver.Element.template_content">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">template_content</span></span><a class="headerlink" href="#nodriver.Element.template_content" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="nodriver.Element.pseudo_elements">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">pseudo_elements</span></span><a class="headerlink" href="#nodriver.Element.pseudo_elements" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="nodriver.Element.imported_document">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">imported_document</span></span><a class="headerlink" href="#nodriver.Element.imported_document" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="nodriver.Element.distributed_nodes">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">distributed_nodes</span></span><a class="headerlink" href="#nodriver.Element.distributed_nodes" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="nodriver.Element.is_svg">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">is_svg</span></span><a class="headerlink" href="#nodriver.Element.is_svg" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="nodriver.Element.compatibility_mode">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">compatibility_mode</span></span><a class="headerlink" href="#nodriver.Element.compatibility_mode" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="nodriver.Element.assigned_slot">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">assigned_slot</span></span><a class="headerlink" href="#nodriver.Element.assigned_slot" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="nodriver.Element.tab">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">tab</span></span><a class="headerlink" href="#nodriver.Element.tab" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="nodriver.Element.shadow_children">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">shadow_children</span></span><a class="headerlink" href="#nodriver.Element.shadow_children" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Element.save_to_dom">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">save_to_dom</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/element.html#Element.save_to_dom"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Element.save_to_dom" title="Link to this definition">#</a></dt>
<dd><p>saves element to dom
:return:
:rtype:</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Element.remove_from_dom">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">remove_from_dom</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/element.html#Element.remove_from_dom"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Element.remove_from_dom" title="Link to this definition">#</a></dt>
<dd><p>removes the element from dom</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Element.update">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">update</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">_node</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/element.html#Element.update"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Element.update" title="Link to this definition">#</a></dt>
<dd><p>updates element to retrieve more properties. for example this enables
<a class="reference internal" href="#nodriver.Element.children" title="nodriver.Element.children"><code class="xref py py-obj docutils literal notranslate"><span class="pre">children</span></code></a> and <a class="reference internal" href="#nodriver.Element.parent" title="nodriver.Element.parent"><code class="xref py py-obj docutils literal notranslate"><span class="pre">parent</span></code></a> attributes.</p>
<p>also resolves js opbject which is stored object in <a class="reference internal" href="#nodriver.Element.remote_object" title="nodriver.Element.remote_object"><code class="xref py py-obj docutils literal notranslate"><span class="pre">remote_object</span></code></a></p>
<p>usually you will get element nodes by the usage of</p>
<p><a class="reference internal" href="tab.html#nodriver.Tab.query_selector_all" title="nodriver.Tab.query_selector_all"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Tab.query_selector_all()</span></code></a></p>
<p><a class="reference internal" href="tab.html#nodriver.Tab.find_elements_by_text" title="nodriver.Tab.find_elements_by_text"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Tab.find_elements_by_text()</span></code></a></p>
<p>those elements are already updated and you can browse through children directly.</p>
<p>The reason for a seperate call instead of doing it at initialization,
is because when you are retrieving 100+ elements this becomes quite expensive.</p>
<p>therefore, it is not advised to call this method on a bunch of blocks (100+) at the same time.</p>
<dl class="field-list simple">
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p></p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p></p>
</dd>
</dl>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="nodriver.Element.node">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">node</span></span><a class="headerlink" href="#nodriver.Element.node" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="nodriver.Element.tree">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">tree</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="../cdp/dom.html#nodriver.cdp.dom.Node" title="nodriver.cdp.dom.Node"><span class="pre">Node</span></a></em><a class="headerlink" href="#nodriver.Element.tree" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="nodriver.Element.attrs">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">attrs</span></span><a class="headerlink" href="#nodriver.Element.attrs" title="Link to this definition">#</a></dt>
<dd><p>attributes are stored here, however, you can set them directly on the element object as well.
:return:
:rtype:</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="nodriver.Element.parent">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">parent</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.Element" title="nodriver.core.element.Element"><span class="pre">Element</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></em><a class="headerlink" href="#nodriver.Element.parent" title="Link to this definition">#</a></dt>
<dd><p>get the parent element (node) of current element(node)
:return:
:rtype:</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="nodriver.Element.children">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">children</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><span class="pre">List</span></a><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#nodriver.Element" title="nodriver.core.element.Element"><span class="pre">Element</span></a><span class="p"><span class="pre">]</span></span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></em><a class="headerlink" href="#nodriver.Element.children" title="Link to this definition">#</a></dt>
<dd><p>returns the elements’ children. those children also have a children property
so you can browse through the entire tree as well.
:return:
:rtype:</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="nodriver.Element.remote_object">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">remote_object</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="../cdp/runtime.html#nodriver.cdp.runtime.RemoteObject" title="nodriver.cdp.runtime.RemoteObject"><span class="pre">RemoteObject</span></a></em><a class="headerlink" href="#nodriver.Element.remote_object" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="nodriver.Element.object_id">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">object_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="../cdp/runtime.html#nodriver.cdp.runtime.RemoteObjectId" title="nodriver.cdp.runtime.RemoteObjectId"><span class="pre">RemoteObjectId</span></a></em><a class="headerlink" href="#nodriver.Element.object_id" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Element.click">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">click</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/element.html#Element.click"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Element.click" title="Link to this definition">#</a></dt>
<dd><p>Click the element.</p>
<dl class="field-list simple">
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p></p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p></p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Element.get_js_attributes">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">get_js_attributes</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/element.html#Element.get_js_attributes"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Element.get_js_attributes" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Element.apply">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">apply</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">js_function</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">return_by_value</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/element.html#Element.apply"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Element.apply" title="Link to this definition">#</a></dt>
<dd><p>apply javascript to this element. the given js_function string should accept the js element as parameter,
and can be a arrow function, or function declaration.
eg:</p>
<blockquote>
<div><ul class="simple">
<li><p>‘(elem) =&gt; { elem.value = “blabla”; consolelog(elem); alert(JSON.stringify(elem); } ‘</p></li>
<li><p>‘elem =&gt; elem.play()’</p></li>
<li><p>function myFunction(elem) { alert(elem) }</p></li>
</ul>
</div></blockquote>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>js_function</strong> (<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><em>str</em></a>) – the js function definition which received this element.</p></li>
<li><p><strong>return_by_value</strong> – </p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p></p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p></p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Element.get_position">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">get_position</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">abs</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/element.html#Element.get_position"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Element.get_position" title="Link to this definition">#</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><span class="sphinx_autodoc_typehints-type"><code class="xref py py-class docutils literal notranslate"><span class="pre">Position</span></code></span></p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Element.mouse_click">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">mouse_click</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">button</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'left'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">buttons</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">modifiers</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">_until_event</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/element.html#Element.mouse_click"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Element.mouse_click" title="Link to this definition">#</a></dt>
<dd><p>native click (on element) . note: this likely does not work atm, use click() instead</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>button</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – str (default = “left”)</p></li>
<li><p><strong>buttons</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a>]</span>) – which button (default 1 = left)</p></li>
<li><p><strong>modifiers</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a>]</span>) – <em>(Optional)</em> Bit field representing pressed modifier keys.
Alt=1, Ctrl=2, Meta/Command=4, Shift=8 (default: 0).</p></li>
<li><p><strong>_until_event</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#type" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">type</span></code></a>]</span>) – internal. event to wait for before returning</p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p></p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Element.click_mouse">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">click_mouse</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">button</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'left'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">buttons</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">modifiers</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">_until_event</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#nodriver.Element.click_mouse" title="Link to this definition">#</a></dt>
<dd><p>native click (on element) . note: this likely does not work atm, use click() instead</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>button</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – str (default = “left”)</p></li>
<li><p><strong>buttons</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a>]</span>) – which button (default 1 = left)</p></li>
<li><p><strong>modifiers</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a>]</span>) – <em>(Optional)</em> Bit field representing pressed modifier keys.
Alt=1, Ctrl=2, Meta/Command=4, Shift=8 (default: 0).</p></li>
<li><p><strong>_until_event</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#type" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">type</span></code></a>]</span>) – internal. event to wait for before returning</p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p></p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Element.mouse_move">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">mouse_move</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/element.html#Element.mouse_move"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Element.mouse_move" title="Link to this definition">#</a></dt>
<dd><p>moves mouse (not click), to element position. when an element has an
hover/mouseover effect, this would trigger it</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Element.mouse_drag">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">mouse_drag</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">destination</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">relative</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">steps</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/element.html#Element.mouse_drag"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Element.mouse_drag" title="Link to this definition">#</a></dt>
<dd><p>drag an element to another element or target coordinates. dragging of elements should be supported  by the site of course</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>destination</strong> (<a class="reference internal" href="#nodriver.Element" title="nodriver.Element"><em>Element</em></a><em> or </em><em>coordinate as x</em><em>,</em><em>y tuple</em>) – another element where to drag to, or a tuple (x,y) of ints representing coordinate</p></li>
<li><p><strong>relative</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a></span>) – when True, treats coordinate as relative. for example (-100, 200) will move left 100px and down 200px</p></li>
<li><p><strong>steps</strong> (<a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><em>int</em></a>) – move in &lt;steps&gt; points, this could make it look more “natural” (default 1),
but also a lot slower.
for very smooth action use 50-100</p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p></p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p></p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Element.scroll_into_view">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">scroll_into_view</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/element.html#Element.scroll_into_view"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Element.scroll_into_view" title="Link to this definition">#</a></dt>
<dd><p>scrolls element into view</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Element.clear_input">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">clear_input</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">_until_event</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/element.html#Element.clear_input"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Element.clear_input" title="Link to this definition">#</a></dt>
<dd><p>clears an input field</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Element.send_keys">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">send_keys</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">text</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/element.html#Element.send_keys"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Element.send_keys" title="Link to this definition">#</a></dt>
<dd><p>send text to an input field, or any other html element.</p>
<p>hint, if you ever get stuck where using py:meth:<cite>~click</cite>
does not work, sending the keystroke n or rn or a spacebar work wonders!</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>text</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – text to send</p>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>None</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Element.send_file">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">send_file</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">file_paths</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/element.html#Element.send_file"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Element.send_file" title="Link to this definition">#</a></dt>
<dd><p>some form input require a file (upload), a full path needs to be provided.
this method sends 1 or more file(s) to the input field.</p>
<p>needles to say, but make sure the field accepts multiple files if you want to send more files.
otherwise the browser might crash.</p>
<p>example :
<cite>await fileinputElement.send_file(‘c:/temp/image.png’, ‘c:/users/<USER>/lol.gif’)</cite></p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Element.focus">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">focus</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/element.html#Element.focus"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Element.focus" title="Link to this definition">#</a></dt>
<dd><p>focus the current element. often useful in form (select) fields</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Element.select_option">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">select_option</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/element.html#Element.select_option"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Element.select_option" title="Link to this definition">#</a></dt>
<dd><p>for form (select) fields. when you have queried the options you can call this method on the option object.
02/08/2024: fixed the problem where events are not fired when programattically selecting an option.</p>
<p>calling <code class="xref py py-func docutils literal notranslate"><span class="pre">option.select_option()</span></code> will use that option as selected value.
does not work in all cases.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Element.set_value">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">set_value</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/element.html#Element.set_value"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Element.set_value" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Element.set_text">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">set_text</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/element.html#Element.set_text"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Element.set_text" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Element.get_html">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">get_html</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/element.html#Element.get_html"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Element.get_html" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="nodriver.Element.text">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">text</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></em><a class="headerlink" href="#nodriver.Element.text" title="Link to this definition">#</a></dt>
<dd><p>gets the text contents of this element
note: this includes text in the form of script content, as those are also just ‘text nodes’</p>
<dl class="field-list simple">
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p></p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p></p>
</dd>
</dl>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="nodriver.Element.text_all">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">text_all</span></span><a class="headerlink" href="#nodriver.Element.text_all" title="Link to this definition">#</a></dt>
<dd><p>gets the text contents of this element, and it’s children in a concatenated string
note: this includes text in the form of script content, as those are also just ‘text nodes’
:return:
:rtype:</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Element.query_selector_all">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">query_selector_all</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">selector</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/element.html#Element.query_selector_all"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Element.query_selector_all" title="Link to this definition">#</a></dt>
<dd><p>like js querySelectorAll()</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Element.query_selector">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">query_selector</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">selector</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/element.html#Element.query_selector"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Element.query_selector" title="Link to this definition">#</a></dt>
<dd><p>like js querySelector()</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Element.save_screenshot">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">save_screenshot</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">filename</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'auto'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">format</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'jpeg'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">scale</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/element.html#Element.save_screenshot"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Element.save_screenshot" title="Link to this definition">#</a></dt>
<dd><p>Saves a screenshot of this element (only)
This is not the same as <a class="reference internal" href="tab.html#nodriver.Tab.save_screenshot" title="nodriver.Tab.save_screenshot"><code class="xref py py-obj docutils literal notranslate"><span class="pre">Tab.save_screenshot</span></code></a>, which saves a “regular” screenshot</p>
<p>When the element is hidden, or has no size, or is otherwise not capturable, a RuntimeError is raised</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>filename</strong> (<em>PathLike</em>) – uses this as the save path</p></li>
<li><p><strong>format</strong> (<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><em>str</em></a>) – jpeg or png (defaults to jpeg)</p></li>
<li><p><strong>scale</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Union" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Union</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span>) – the scale of the screenshot, eg: 1 = size as is, 2 = double, 0.5 is half</p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>the path/filename of saved screenshot</p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)">str</a></p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Element.flash">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">flash</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">duration</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0.5</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/element.html#Element.flash"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Element.flash" title="Link to this definition">#</a></dt>
<dd><p>displays for a short time a red dot on the element (only if the element itself is visible)</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>coords</strong> (<em>x</em><em>,</em><em>y</em>) – x,y</p></li>
<li><p><strong>duration</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Union" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Union</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a>]</span>) – seconds (default 0.5)</p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p></p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p></p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Element.highlight_overlay">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">highlight_overlay</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/element.html#Element.highlight_overlay"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Element.highlight_overlay" title="Link to this definition">#</a></dt>
<dd><p>highlights the element devtools-style. To remove the highlight,
call the method again.
:return:
:rtype:</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Element.record_video">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">record_video</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">filename</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">folder</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">duration</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/element.html#Element.record_video"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Element.record_video" title="Link to this definition">#</a></dt>
<dd><p>experimental option.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>filename</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]</span>) – the desired filename</p></li>
<li><p><strong>folder</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]</span>) – the download folder path</p></li>
<li><p><strong>duration</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Union" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Union</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span>) – record for this many seconds and then download</p></li>
</ul>
</dd>
</dl>
<p>on html5 video nodes, you can call this method to start recording of the video.</p>
<p>when any of the follow happens:</p>
<ul class="simple">
<li><p>video ends</p></li>
<li><p>calling videoelement(‘pause’)</p></li>
<li><p>video stops</p></li>
</ul>
<p>the video recorded will be downloaded.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Element.is_recording">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">is_recording</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/element.html#Element.is_recording"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Element.is_recording" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

</section>

        </article>
      </div>
      <footer>
        
        <div class="related-pages">
          <a class="next-page" href="others_and_helpers.html">
              <div class="page-info">
                <div class="context">
                  <span>Next</span>
                </div>
                <div class="title">Other classes and Helper classes</div>
              </div>
              <svg class="furo-related-icon"><use href="#svg-arrow-right"></use></svg>
            </a>
          <a class="prev-page" href="tab.html">
              <svg class="furo-related-icon"><use href="#svg-arrow-right"></use></svg>
              <div class="page-info">
                <div class="context">
                  <span>Previous</span>
                </div>
                
                <div class="title">Tab class</div>
                
              </div>
            </a>
        </div>
        <div class="bottom-of-page">
          <div class="left-details">
            <div class="copyright">
                Copyright &#169; 2023, Author
            </div>
            Made with <a href="https://www.sphinx-doc.org/">Sphinx</a> and <a class="muted-link" href="https://pradyunsg.me">@pradyunsg</a>'s
            
            <a href="https://github.com/pradyunsg/furo">Furo</a>
            
          </div>
          <div class="right-details">
            
          </div>
        </div>
        
      </footer>
    </div>
    <aside class="toc-drawer">
      
      
      <div class="toc-sticky toc-scroll">
        <div class="toc-title-container">
          <span class="toc-title">
            On this page
          </span>
        </div>
        <div class="toc-tree-container">
          <div class="toc-tree">
            <ul>
<li><a class="reference internal" href="#">Element class</a><ul>
<li><a class="reference internal" href="#nodriver.Element"><code class="docutils literal notranslate"><span class="pre">Element</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.Element.tag"><code class="docutils literal notranslate"><span class="pre">Element.tag</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.tag_name"><code class="docutils literal notranslate"><span class="pre">Element.tag_name</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.node_id"><code class="docutils literal notranslate"><span class="pre">Element.node_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.backend_node_id"><code class="docutils literal notranslate"><span class="pre">Element.backend_node_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.node_type"><code class="docutils literal notranslate"><span class="pre">Element.node_type</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.node_name"><code class="docutils literal notranslate"><span class="pre">Element.node_name</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.local_name"><code class="docutils literal notranslate"><span class="pre">Element.local_name</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.node_value"><code class="docutils literal notranslate"><span class="pre">Element.node_value</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.parent_id"><code class="docutils literal notranslate"><span class="pre">Element.parent_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.child_node_count"><code class="docutils literal notranslate"><span class="pre">Element.child_node_count</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.attributes"><code class="docutils literal notranslate"><span class="pre">Element.attributes</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.document_url"><code class="docutils literal notranslate"><span class="pre">Element.document_url</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.base_url"><code class="docutils literal notranslate"><span class="pre">Element.base_url</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.public_id"><code class="docutils literal notranslate"><span class="pre">Element.public_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.system_id"><code class="docutils literal notranslate"><span class="pre">Element.system_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.internal_subset"><code class="docutils literal notranslate"><span class="pre">Element.internal_subset</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.xml_version"><code class="docutils literal notranslate"><span class="pre">Element.xml_version</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.value"><code class="docutils literal notranslate"><span class="pre">Element.value</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.pseudo_type"><code class="docutils literal notranslate"><span class="pre">Element.pseudo_type</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.pseudo_identifier"><code class="docutils literal notranslate"><span class="pre">Element.pseudo_identifier</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.shadow_root_type"><code class="docutils literal notranslate"><span class="pre">Element.shadow_root_type</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.frame_id"><code class="docutils literal notranslate"><span class="pre">Element.frame_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.content_document"><code class="docutils literal notranslate"><span class="pre">Element.content_document</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.shadow_roots"><code class="docutils literal notranslate"><span class="pre">Element.shadow_roots</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.template_content"><code class="docutils literal notranslate"><span class="pre">Element.template_content</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.pseudo_elements"><code class="docutils literal notranslate"><span class="pre">Element.pseudo_elements</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.imported_document"><code class="docutils literal notranslate"><span class="pre">Element.imported_document</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.distributed_nodes"><code class="docutils literal notranslate"><span class="pre">Element.distributed_nodes</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.is_svg"><code class="docutils literal notranslate"><span class="pre">Element.is_svg</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.compatibility_mode"><code class="docutils literal notranslate"><span class="pre">Element.compatibility_mode</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.assigned_slot"><code class="docutils literal notranslate"><span class="pre">Element.assigned_slot</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.tab"><code class="docutils literal notranslate"><span class="pre">Element.tab</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.shadow_children"><code class="docutils literal notranslate"><span class="pre">Element.shadow_children</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.save_to_dom"><code class="docutils literal notranslate"><span class="pre">Element.save_to_dom()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.remove_from_dom"><code class="docutils literal notranslate"><span class="pre">Element.remove_from_dom()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.update"><code class="docutils literal notranslate"><span class="pre">Element.update()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.node"><code class="docutils literal notranslate"><span class="pre">Element.node</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.tree"><code class="docutils literal notranslate"><span class="pre">Element.tree</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.attrs"><code class="docutils literal notranslate"><span class="pre">Element.attrs</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.parent"><code class="docutils literal notranslate"><span class="pre">Element.parent</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.children"><code class="docutils literal notranslate"><span class="pre">Element.children</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.remote_object"><code class="docutils literal notranslate"><span class="pre">Element.remote_object</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.object_id"><code class="docutils literal notranslate"><span class="pre">Element.object_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.click"><code class="docutils literal notranslate"><span class="pre">Element.click()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.get_js_attributes"><code class="docutils literal notranslate"><span class="pre">Element.get_js_attributes()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.apply"><code class="docutils literal notranslate"><span class="pre">Element.apply()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.get_position"><code class="docutils literal notranslate"><span class="pre">Element.get_position()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.mouse_click"><code class="docutils literal notranslate"><span class="pre">Element.mouse_click()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.click_mouse"><code class="docutils literal notranslate"><span class="pre">Element.click_mouse()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.mouse_move"><code class="docutils literal notranslate"><span class="pre">Element.mouse_move()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.mouse_drag"><code class="docutils literal notranslate"><span class="pre">Element.mouse_drag()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.scroll_into_view"><code class="docutils literal notranslate"><span class="pre">Element.scroll_into_view()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.clear_input"><code class="docutils literal notranslate"><span class="pre">Element.clear_input()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.send_keys"><code class="docutils literal notranslate"><span class="pre">Element.send_keys()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.send_file"><code class="docutils literal notranslate"><span class="pre">Element.send_file()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.focus"><code class="docutils literal notranslate"><span class="pre">Element.focus()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.select_option"><code class="docutils literal notranslate"><span class="pre">Element.select_option()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.set_value"><code class="docutils literal notranslate"><span class="pre">Element.set_value()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.set_text"><code class="docutils literal notranslate"><span class="pre">Element.set_text()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.get_html"><code class="docutils literal notranslate"><span class="pre">Element.get_html()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.text"><code class="docutils literal notranslate"><span class="pre">Element.text</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.text_all"><code class="docutils literal notranslate"><span class="pre">Element.text_all</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.query_selector_all"><code class="docutils literal notranslate"><span class="pre">Element.query_selector_all()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.query_selector"><code class="docutils literal notranslate"><span class="pre">Element.query_selector()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.save_screenshot"><code class="docutils literal notranslate"><span class="pre">Element.save_screenshot()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.flash"><code class="docutils literal notranslate"><span class="pre">Element.flash()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.highlight_overlay"><code class="docutils literal notranslate"><span class="pre">Element.highlight_overlay()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.record_video"><code class="docutils literal notranslate"><span class="pre">Element.record_video()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Element.is_recording"><code class="docutils literal notranslate"><span class="pre">Element.is_recording()</span></code></a></li>
</ul>
</li>
</ul>
</li>
</ul>

          </div>
        </div>
      </div>
      
      
    </aside>
  </div>
</div><script src="../../_static/documentation_options.js?v=5929fcd5"></script>
    <script src="../../_static/doctools.js?v=888ff710"></script>
    <script src="../../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../../_static/scripts/furo.js?v=32e29ea5"></script>
    </body>
</html>