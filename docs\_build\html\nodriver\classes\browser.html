<!doctype html>
<html class="no-js" lang="en" data-content_root="../../">
  <head><meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width,initial-scale=1"/>
    <meta name="color-scheme" content="light dark"><meta name="viewport" content="width=device-width, initial-scale=1" />
<link rel="index" title="Index" href="../../genindex.html" /><link rel="search" title="Search" href="../../search.html" /><link rel="next" title="Tab class" href="tab.html" /><link rel="prev" title="Quickstart guide" href="../quickstart.html" />

    <!-- Generated with Sphinx 7.2.6 and Furo 2023.09.10 -->
        <title>Browser class - nodriver documentation</title>
      <link rel="stylesheet" type="text/css" href="../../_static/pygments.css?v=fa44fd50" />
    <link rel="stylesheet" type="text/css" href="../../_static/styles/furo.css?v=135e06be" />
    <link rel="stylesheet" type="text/css" href="../../_static/styles/furo-extensions.css?v=36a5483c" />
    <link rel="stylesheet" type="text/css" href="../../_static/./custom.css?v=3934642d" />
    
    


<style>
  body {
    --color-code-background: #eeffcc;
  --color-code-foreground: black;
  
  }
  @media not print {
    body[data-theme="dark"] {
      --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8eefba;
  --color-background-primary: #111112;
  --color-problematic: #e7aeae;
  
    }
    @media (prefers-color-scheme: dark) {
      body:not([data-theme="light"]) {
        --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8eefba;
  --color-background-primary: #111112;
  --color-problematic: #e7aeae;
  
      }
    }
  }
</style></head>
  <body>
    
    <script>
      document.body.dataset.theme = localStorage.getItem("theme") || "auto";
    </script>
    

<svg xmlns="http://www.w3.org/2000/svg" style="display: none;">
  <symbol id="svg-toc" viewBox="0 0 24 24">
    <title>Contents</title>
    <svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 1024 1024">
      <path d="M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM115.4 518.9L271.7 642c5.8 4.6 14.4.5 14.4-6.9V388.9c0-7.4-8.5-11.5-14.4-6.9L115.4 505.1a8.74 8.74 0 0 0 0 13.8z"/>
    </svg>
  </symbol>
  <symbol id="svg-menu" viewBox="0 0 24 24">
    <title>Menu</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-menu">
      <line x1="3" y1="12" x2="21" y2="12"></line>
      <line x1="3" y1="6" x2="21" y2="6"></line>
      <line x1="3" y1="18" x2="21" y2="18"></line>
    </svg>
  </symbol>
  <symbol id="svg-arrow-right" viewBox="0 0 24 24">
    <title>Expand</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-chevron-right">
      <polyline points="9 18 15 12 9 6"></polyline>
    </svg>
  </symbol>
  <symbol id="svg-sun" viewBox="0 0 24 24">
    <title>Light mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="feather-sun">
      <circle cx="12" cy="12" r="5"></circle>
      <line x1="12" y1="1" x2="12" y2="3"></line>
      <line x1="12" y1="21" x2="12" y2="23"></line>
      <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
      <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
      <line x1="1" y1="12" x2="3" y2="12"></line>
      <line x1="21" y1="12" x2="23" y2="12"></line>
      <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
      <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
    </svg>
  </symbol>
  <symbol id="svg-moon" viewBox="0 0 24 24">
    <title>Dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-moon">
      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
      <path d="M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z" />
    </svg>
  </symbol>
  <symbol id="svg-sun-half" viewBox="0 0 24 24">
    <title>Auto light/dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-shadow">
      <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
      <circle cx="12" cy="12" r="9" />
      <path d="M13 12h5" />
      <path d="M13 15h4" />
      <path d="M13 18h1" />
      <path d="M13 9h4" />
      <path d="M13 6h1" />
    </svg>
  </symbol>
</svg>

<input type="checkbox" class="sidebar-toggle" name="__navigation" id="__navigation">
<input type="checkbox" class="sidebar-toggle" name="__toc" id="__toc">
<label class="overlay sidebar-overlay" for="__navigation">
  <div class="visually-hidden">Hide navigation sidebar</div>
</label>
<label class="overlay toc-overlay" for="__toc">
  <div class="visually-hidden">Hide table of contents sidebar</div>
</label>



<div class="page">
  <header class="mobile-header">
    <div class="header-left">
      <label class="nav-overlay-icon" for="__navigation">
        <div class="visually-hidden">Toggle site navigation sidebar</div>
        <i class="icon"><svg><use href="#svg-menu"></use></svg></i>
      </label>
    </div>
    <div class="header-center">
      <a href="../../index.html"><div class="brand">nodriver  documentation</div></a>
    </div>
    <div class="header-right">
      <div class="theme-toggle-container theme-toggle-header">
        <button class="theme-toggle">
          <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
          <svg class="theme-icon-when-auto"><use href="#svg-sun-half"></use></svg>
          <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
          <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
        </button>
      </div>
      <label class="toc-overlay-icon toc-header-icon" for="__toc">
        <div class="visually-hidden">Toggle table of contents sidebar</div>
        <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
      </label>
    </div>
  </header>
  <aside class="sidebar-drawer">
    <div class="sidebar-container">
      
      <div class="sidebar-sticky"><div class="sidebar-scroll"><a class="sidebar-brand" href="../../index.html">
  
  
  <span class="sidebar-brand-text">nodriver  documentation</span>
  
</a><form class="sidebar-search-container" method="get" action="../../search.html" role="search">
  <input class="sidebar-search" placeholder="Search" name="q" aria-label="Search">
  <input type="hidden" name="check_keywords" value="yes">
  <input type="hidden" name="area" value="default">
</form>
<div id="searchbox"></div><div class="sidebar-tree">
  <ul>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html">Quickstart guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#usage-example">usage example</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#more-complete-example">More complete example</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#custom-starting-options">Custom starting options</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#alternative-custom-options">Alternative custom options</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#proxies-socks5-authenticated-too">Proxies (socks5, authenticated too)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#concrete-example">Concrete example</a></li>
</ul>
<ul class="current">
<li class="toctree-l1 current current-page"><a class="current reference internal" href="#">Browser class</a></li>
<li class="toctree-l1"><a class="reference internal" href="tab.html">Tab class</a></li>
<li class="toctree-l1"><a class="reference internal" href="element.html">Element class</a></li>
<li class="toctree-l1"><a class="reference internal" href="others_and_helpers.html">Other classes and Helper classes</a></li>
</ul>
<ul>
<li class="toctree-l1 has-children"><a class="reference internal" href="../cdp.html">CDP object</a><input class="toctree-checkbox" id="toctree-checkbox-1" name="toctree-checkbox-1" role="switch" type="checkbox"/><label for="toctree-checkbox-1"><div class="visually-hidden">Toggle navigation of CDP object</div><i class="icon"><svg><use href="#svg-arrow-right"></use></svg></i></label><ul>
<li class="toctree-l2"><a class="reference internal" href="../cdp/accessibility.html">Accessibility</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/animation.html">Animation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/audits.html">Audits</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/autofill.html">Autofill</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/background_service.html">BackgroundService</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/bluetooth_emulation.html">BluetoothEmulation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/browser.html">Browser</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/cache_storage.html">CacheStorage</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/cast.html">Cast</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/console.html">Console</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/css.html">CSS</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/debugger.html">Debugger</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/device_access.html">DeviceAccess</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/device_orientation.html">DeviceOrientation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/dom.html">DOM</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/dom_debugger.html">DOMDebugger</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/dom_snapshot.html">DOMSnapshot</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/dom_storage.html">DOMStorage</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/emulation.html">Emulation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/event_breakpoints.html">EventBreakpoints</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/extensions.html">Extensions</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/fed_cm.html">FedCm</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/fetch.html">Fetch</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/file_system.html">FileSystem</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/headless_experimental.html">HeadlessExperimental</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/heap_profiler.html">HeapProfiler</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/indexed_db.html">IndexedDB</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/input_.html">Input</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/inspector.html">Inspector</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/io.html">IO</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/layer_tree.html">LayerTree</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/log.html">Log</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/media.html">Media</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/memory.html">Memory</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/network.html">Network</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/overlay.html">Overlay</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/page.html">Page</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/performance.html">Performance</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/performance_timeline.html">PerformanceTimeline</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/preload.html">Preload</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/profiler.html">Profiler</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/pwa.html">PWA</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/runtime.html">Runtime</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/schema.html">Schema</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/security.html">Security</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/service_worker.html">ServiceWorker</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/storage.html">Storage</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/system_info.html">SystemInfo</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/target.html">Target</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/tethering.html">Tethering</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/tracing.html">Tracing</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/web_audio.html">WebAudio</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/web_authn.html">WebAuthn</a></li>
</ul>
</li>
</ul>

</div>
</div>
      </div>
      
    </div>
  </aside>
  <div class="main">
    <div class="content">
      <div class="article-container">
        <a href="#" class="back-to-top muted-link">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
            <path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8v12z"></path>
          </svg>
          <span>Back to top</span>
        </a>
        <div class="content-icon-container">
          
<div class="theme-toggle-container theme-toggle-content">
            <button class="theme-toggle">
              <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
              <svg class="theme-icon-when-auto"><use href="#svg-sun-half"></use></svg>
              <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
              <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
            </button>
          </div>
          <label class="toc-overlay-icon toc-content-icon" for="__toc">
            <div class="visually-hidden">Toggle table of contents sidebar</div>
            <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
          </label>
        </div>
        <article role="main">
          <section id="browser-class">
<span id="browser"></span><h1>Browser class<a class="headerlink" href="#browser-class" title="Link to this heading">#</a></h1>
<section id="cookies">
<h2>cookies<a class="headerlink" href="#cookies" title="Link to this heading">#</a></h2>
<p>You can load and save all cookies from the browser.</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="c1"># save. when no filepath is given, it is saved in &#39;.session.dat&#39;</span>
<span class="k">await</span> <span class="n">browser</span><span class="o">.</span><span class="n">cookies</span><span class="o">.</span><span class="n">save</span><span class="p">()</span>
</pre></div>
</div>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="c1"># load. when no filepath is given, it is loaded from &#39;.session.dat&#39;</span>
<span class="k">await</span> <span class="n">browser</span><span class="o">.</span><span class="n">cookies</span><span class="o">.</span><span class="n">load</span><span class="p">()</span>
</pre></div>
</div>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="c1"># export for requests or other library</span>
<span class="n">requests_style_cookies</span> <span class="o">=</span> <span class="k">await</span> <span class="n">browser</span><span class="o">.</span><span class="n">cookies</span><span class="o">.</span><span class="n">get_all</span><span class="p">(</span><span class="n">requests_cookie_format</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>

<span class="c1"># use in requests:</span>
<span class="n">session</span> <span class="o">=</span> <span class="n">requests</span><span class="o">.</span><span class="n">Session</span><span class="p">()</span>
<span class="k">for</span> <span class="n">cookie</span> <span class="ow">in</span> <span class="n">requests_style_cookies</span><span class="p">:</span>
    <span class="n">session</span><span class="o">.</span><span class="n">cookies</span><span class="o">.</span><span class="n">set_cookie</span><span class="p">(</span><span class="n">cookie</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="id1">
<h2>Browser class<a class="headerlink" href="#id1" title="Link to this heading">#</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="nodriver.Browser">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">Browser</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">config</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwargs</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/browser.html#Browser"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Browser" title="Link to this definition">#</a></dt>
<dd><p>The Browser object is the “root” of the hierarchy and contains a reference
to the browser parent process.
there should usually be only 1 instance of this.</p>
<p>All opened tabs, extra browser screens and resources will not cause a new Browser process,
but rather create additional <a class="reference internal" href="tab.html#nodriver.Tab" title="nodriver.Tab"><code class="xref py py-class docutils literal notranslate"><span class="pre">nodriver.Tab</span></code></a> objects.</p>
<p>So, besides starting your instance and first/additional tabs, you don’t actively use it a lot under normal conditions.</p>
<dl class="simple">
<dt>Tab objects will represent and control</dt><dd><ul class="simple">
<li><p>tabs (as you know them)</p></li>
<li><p>browser windows (new window)</p></li>
<li><p>iframe</p></li>
<li><p>background processes</p></li>
</ul>
</dd>
</dl>
<p>note:
the Browser object is not instantiated by __init__ but using the asynchronous <a class="reference internal" href="#nodriver.Browser.create" title="nodriver.Browser.create"><code class="xref py py-meth docutils literal notranslate"><span class="pre">nodriver.Browser.create()</span></code></a> method.</p>
<p>note:
in Chromium based browsers, there is a parent process which keeps running all the time, even if
there are no visible browser windows. sometimes it’s stubborn to close it, so make sure after using
this library, the browser is correctly and fully closed/exited/killed.</p>
<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Browser.create">
<em class="property"><span class="pre">async</span><span class="w"> </span><span class="pre">classmethod</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">create</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">config</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">user_data_dir</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">headless</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">browser_executable_path</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">browser_args</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">sandbox</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">host</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">port</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwargs</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/browser.html#Browser.create"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Browser.create" title="Link to this definition">#</a></dt>
<dd><p>entry point for creating an instance</p>
<dl class="field-list simple">
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.Browser" title="nodriver.core.browser.Browser"><code class="xref py py-class docutils literal notranslate"><span class="pre">Browser</span></code></a></span></p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.Browser.config">
<span class="sig-name descname"><span class="pre">config</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="others_and_helpers.html#nodriver.Config" title="nodriver.core.config.Config"><code class="xref py py-class docutils literal notranslate"><span class="pre">Config</span></code></a></em><a class="headerlink" href="#nodriver.Browser.config" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.Browser.targets">
<span class="sig-name descname"><span class="pre">targets</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a></em><a class="headerlink" href="#nodriver.Browser.targets" title="Link to this definition">#</a></dt>
<dd><p>current targets (all types</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.Browser.connection">
<span class="sig-name descname"><span class="pre">connection</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><code class="xref py py-class docutils literal notranslate"><span class="pre">Connection</span></code></em><a class="headerlink" href="#nodriver.Browser.connection" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="nodriver.Browser.websocket_url">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">websocket_url</span></span><a class="headerlink" href="#nodriver.Browser.websocket_url" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="nodriver.Browser.main_tab">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">main_tab</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="tab.html#nodriver.Tab" title="nodriver.core.tab.Tab"><span class="pre">Tab</span></a></em><a class="headerlink" href="#nodriver.Browser.main_tab" title="Link to this definition">#</a></dt>
<dd><p>returns the target which was launched with the browser</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="nodriver.Browser.tabs">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">tabs</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><span class="pre">List</span></a><span class="p"><span class="pre">[</span></span><a class="reference internal" href="tab.html#nodriver.Tab" title="nodriver.core.tab.Tab"><span class="pre">Tab</span></a><span class="p"><span class="pre">]</span></span></em><a class="headerlink" href="#nodriver.Browser.tabs" title="Link to this definition">#</a></dt>
<dd><p>returns the current targets which are of type “page”
:return:</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="nodriver.Browser.cookies">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">cookies</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">CookieJar</span></em><a class="headerlink" href="#nodriver.Browser.cookies" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="nodriver.Browser.stopped">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">stopped</span></span><a class="headerlink" href="#nodriver.Browser.stopped" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Browser.wait">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">wait</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">time</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0.1</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/browser.html#Browser.wait"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Browser.wait" title="Link to this definition">#</a></dt>
<dd><p>wait for &lt;time&gt; seconds. important to use, especially in between page navigation</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>time</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Union" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Union</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a>]</span>) – </p>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p></p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Browser.sleep">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">sleep</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">time</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0.1</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#nodriver.Browser.sleep" title="Link to this definition">#</a></dt>
<dd><p>alias for wait</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Browser.get">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">get</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">url</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'chrome://welcome'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">new_tab</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">new_window</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/browser.html#Browser.get"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Browser.get" title="Link to this definition">#</a></dt>
<dd><p>top level get. utilizes the first tab to retrieve given url.</p>
<p>convenience function known from selenium.
this function handles waits/sleeps and detects when DOM events fired, so it’s the safest
way of navigating.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>url</strong> – the url to navigate to</p></li>
<li><p><strong>new_tab</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a></span>) – open new tab</p></li>
<li><p><strong>new_window</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a></span>) – open new window</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="tab.html#nodriver.Tab" title="nodriver.core.tab.Tab"><code class="xref py py-class docutils literal notranslate"><span class="pre">Tab</span></code></a></span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>Page</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Browser.create_context">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">create_context</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">url</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'chrome://welcome'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">new_tab</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">new_window</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">dispose_on_detach</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">proxy_server</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">proxy_bypass_list</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">origins_with_universal_network_access</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/browser.html#Browser.create_context"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Browser.create_context" title="Link to this definition">#</a></dt>
<dd><p>creates a new browser context - mostly useful if you want to use proxies for different browser instances
since chrome usually can only use 1 proxy per browser.
socks5 with authentication is supported by using a forwarder proxy, the
correct string to use socks proxy with username/password auth is socks://USERNAME:PASSWORD&#64;SERVER:PORT</p>
<p>dispose_on_detach – (EXPERIMENTAL) (Optional) If specified, disposes this context when debugging session disconnects.
proxy_server – (EXPERIMENTAL) (Optional) Proxy server, similar to the one passed to –proxy-server
proxy_bypass_list – (EXPERIMENTAL) (Optional) Proxy bypass list, similar to the one passed to –proxy-bypass-list
origins_with_universal_network_access – (EXPERIMENTAL) (Optional) An optional list of origins to grant unlimited cross-origin access to. Parts of the URL other than those constituting origin are ignored.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>new_window</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a></span>) – </p></li>
<li><p><strong>new_tab</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a></span>) – </p></li>
<li><p><strong>url</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – </p></li>
<li><p><strong>dispose_on_detach</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a></span>) – </p></li>
<li><p><strong>proxy_server</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – </p></li>
<li><p><strong>proxy_bypass_list</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]</span>) – </p></li>
<li><p><strong>origins_with_universal_network_access</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]</span>) – </p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p></p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p></p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Browser.start">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">start</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/browser.html#Browser.start"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Browser.start" title="Link to this definition">#</a></dt>
<dd><p>launches the actual browser</p>
<dl class="field-list simple">
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.Browser" title="nodriver.core.browser.Browser"><code class="xref py py-class docutils literal notranslate"><span class="pre">Browser</span></code></a></span></p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Browser.grant_all_permissions">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">grant_all_permissions</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/browser.html#Browser.grant_all_permissions"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Browser.grant_all_permissions" title="Link to this definition">#</a></dt>
<dd><dl class="simple">
<dt>grant permissions for:</dt><dd><p>accessibilityEvents
audioCapture
backgroundSync
backgroundFetch
clipboardReadWrite
clipboardSanitizedWrite
displayCapture
durableStorage
geolocation
idleDetection
localFonts
midi
midiSysex
nfc
notifications
paymentHandler
periodicBackgroundSync
protectedMediaIdentifier
sensors
storageAccess
topLevelStorageAccess
videoCapture
videoCapturePanTiltZoom
wakeLockScreen
wakeLockSystem
windowManagement</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Browser.tile_windows">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">tile_windows</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">windows</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">max_columns</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/browser.html#Browser.tile_windows"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Browser.tile_windows" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Browser.update_targets">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">update_targets</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/browser.html#Browser.update_targets"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Browser.update_targets" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Browser.stop">
<span class="sig-name descname"><span class="pre">stop</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/browser.html#Browser.stop"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Browser.stop" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

</section>
</section>

        </article>
      </div>
      <footer>
        
        <div class="related-pages">
          <a class="next-page" href="tab.html">
              <div class="page-info">
                <div class="context">
                  <span>Next</span>
                </div>
                <div class="title">Tab class</div>
              </div>
              <svg class="furo-related-icon"><use href="#svg-arrow-right"></use></svg>
            </a>
          <a class="prev-page" href="../quickstart.html">
              <svg class="furo-related-icon"><use href="#svg-arrow-right"></use></svg>
              <div class="page-info">
                <div class="context">
                  <span>Previous</span>
                </div>
                
                <div class="title">Quickstart guide</div>
                
              </div>
            </a>
        </div>
        <div class="bottom-of-page">
          <div class="left-details">
            <div class="copyright">
                Copyright &#169; 2023, Author
            </div>
            Made with <a href="https://www.sphinx-doc.org/">Sphinx</a> and <a class="muted-link" href="https://pradyunsg.me">@pradyunsg</a>'s
            
            <a href="https://github.com/pradyunsg/furo">Furo</a>
            
          </div>
          <div class="right-details">
            
          </div>
        </div>
        
      </footer>
    </div>
    <aside class="toc-drawer">
      
      
      <div class="toc-sticky toc-scroll">
        <div class="toc-title-container">
          <span class="toc-title">
            On this page
          </span>
        </div>
        <div class="toc-tree-container">
          <div class="toc-tree">
            <ul>
<li><a class="reference internal" href="#">Browser class</a><ul>
<li><a class="reference internal" href="#cookies">cookies</a></li>
<li><a class="reference internal" href="#id1">Browser class</a><ul>
<li><a class="reference internal" href="#nodriver.Browser"><code class="docutils literal notranslate"><span class="pre">Browser</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.Browser.create"><code class="docutils literal notranslate"><span class="pre">Browser.create()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Browser.config"><code class="docutils literal notranslate"><span class="pre">Browser.config</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Browser.targets"><code class="docutils literal notranslate"><span class="pre">Browser.targets</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Browser.connection"><code class="docutils literal notranslate"><span class="pre">Browser.connection</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Browser.websocket_url"><code class="docutils literal notranslate"><span class="pre">Browser.websocket_url</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Browser.main_tab"><code class="docutils literal notranslate"><span class="pre">Browser.main_tab</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Browser.tabs"><code class="docutils literal notranslate"><span class="pre">Browser.tabs</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Browser.cookies"><code class="docutils literal notranslate"><span class="pre">Browser.cookies</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Browser.stopped"><code class="docutils literal notranslate"><span class="pre">Browser.stopped</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Browser.wait"><code class="docutils literal notranslate"><span class="pre">Browser.wait()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Browser.sleep"><code class="docutils literal notranslate"><span class="pre">Browser.sleep()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Browser.get"><code class="docutils literal notranslate"><span class="pre">Browser.get()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Browser.create_context"><code class="docutils literal notranslate"><span class="pre">Browser.create_context()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Browser.start"><code class="docutils literal notranslate"><span class="pre">Browser.start()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Browser.grant_all_permissions"><code class="docutils literal notranslate"><span class="pre">Browser.grant_all_permissions()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Browser.tile_windows"><code class="docutils literal notranslate"><span class="pre">Browser.tile_windows()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Browser.update_targets"><code class="docutils literal notranslate"><span class="pre">Browser.update_targets()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Browser.stop"><code class="docutils literal notranslate"><span class="pre">Browser.stop()</span></code></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>

          </div>
        </div>
      </div>
      
      
    </aside>
  </div>
</div><script src="../../_static/documentation_options.js?v=5929fcd5"></script>
    <script src="../../_static/doctools.js?v=888ff710"></script>
    <script src="../../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../../_static/scripts/furo.js?v=32e29ea5"></script>
    </body>
</html>