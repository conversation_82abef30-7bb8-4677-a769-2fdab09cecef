<!doctype html>
<html class="no-js" lang="en" data-content_root="../../">
  <head><meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width,initial-scale=1"/>
    <meta name="color-scheme" content="light dark"><meta name="viewport" content="width=device-width, initial-scale=1" />
<link rel="index" title="Index" href="../../genindex.html" /><link rel="search" title="Search" href="../../search.html" /><link rel="next" title="Tethering" href="tethering.html" /><link rel="prev" title="SystemInfo" href="system_info.html" />

    <!-- Generated with Sphinx 7.2.6 and Furo 2023.09.10 -->
        <title>Target - nodriver documentation</title>
      <link rel="stylesheet" type="text/css" href="../../_static/pygments.css?v=fa44fd50" />
    <link rel="stylesheet" type="text/css" href="../../_static/styles/furo.css?v=135e06be" />
    <link rel="stylesheet" type="text/css" href="../../_static/styles/furo-extensions.css?v=36a5483c" />
    <link rel="stylesheet" type="text/css" href="../../_static/./custom.css?v=3934642d" />
    
    


<style>
  body {
    --color-code-background: #eeffcc;
  --color-code-foreground: black;
  
  }
  @media not print {
    body[data-theme="dark"] {
      --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8eefba;
  --color-background-primary: #111112;
  --color-problematic: #e7aeae;
  
    }
    @media (prefers-color-scheme: dark) {
      body:not([data-theme="light"]) {
        --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8eefba;
  --color-background-primary: #111112;
  --color-problematic: #e7aeae;
  
      }
    }
  }
</style></head>
  <body>
    
    <script>
      document.body.dataset.theme = localStorage.getItem("theme") || "auto";
    </script>
    

<svg xmlns="http://www.w3.org/2000/svg" style="display: none;">
  <symbol id="svg-toc" viewBox="0 0 24 24">
    <title>Contents</title>
    <svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 1024 1024">
      <path d="M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM115.4 518.9L271.7 642c5.8 4.6 14.4.5 14.4-6.9V388.9c0-7.4-8.5-11.5-14.4-6.9L115.4 505.1a8.74 8.74 0 0 0 0 13.8z"/>
    </svg>
  </symbol>
  <symbol id="svg-menu" viewBox="0 0 24 24">
    <title>Menu</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-menu">
      <line x1="3" y1="12" x2="21" y2="12"></line>
      <line x1="3" y1="6" x2="21" y2="6"></line>
      <line x1="3" y1="18" x2="21" y2="18"></line>
    </svg>
  </symbol>
  <symbol id="svg-arrow-right" viewBox="0 0 24 24">
    <title>Expand</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-chevron-right">
      <polyline points="9 18 15 12 9 6"></polyline>
    </svg>
  </symbol>
  <symbol id="svg-sun" viewBox="0 0 24 24">
    <title>Light mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="feather-sun">
      <circle cx="12" cy="12" r="5"></circle>
      <line x1="12" y1="1" x2="12" y2="3"></line>
      <line x1="12" y1="21" x2="12" y2="23"></line>
      <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
      <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
      <line x1="1" y1="12" x2="3" y2="12"></line>
      <line x1="21" y1="12" x2="23" y2="12"></line>
      <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
      <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
    </svg>
  </symbol>
  <symbol id="svg-moon" viewBox="0 0 24 24">
    <title>Dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-moon">
      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
      <path d="M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z" />
    </svg>
  </symbol>
  <symbol id="svg-sun-half" viewBox="0 0 24 24">
    <title>Auto light/dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-shadow">
      <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
      <circle cx="12" cy="12" r="9" />
      <path d="M13 12h5" />
      <path d="M13 15h4" />
      <path d="M13 18h1" />
      <path d="M13 9h4" />
      <path d="M13 6h1" />
    </svg>
  </symbol>
</svg>

<input type="checkbox" class="sidebar-toggle" name="__navigation" id="__navigation">
<input type="checkbox" class="sidebar-toggle" name="__toc" id="__toc">
<label class="overlay sidebar-overlay" for="__navigation">
  <div class="visually-hidden">Hide navigation sidebar</div>
</label>
<label class="overlay toc-overlay" for="__toc">
  <div class="visually-hidden">Hide table of contents sidebar</div>
</label>



<div class="page">
  <header class="mobile-header">
    <div class="header-left">
      <label class="nav-overlay-icon" for="__navigation">
        <div class="visually-hidden">Toggle site navigation sidebar</div>
        <i class="icon"><svg><use href="#svg-menu"></use></svg></i>
      </label>
    </div>
    <div class="header-center">
      <a href="../../index.html"><div class="brand">nodriver  documentation</div></a>
    </div>
    <div class="header-right">
      <div class="theme-toggle-container theme-toggle-header">
        <button class="theme-toggle">
          <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
          <svg class="theme-icon-when-auto"><use href="#svg-sun-half"></use></svg>
          <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
          <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
        </button>
      </div>
      <label class="toc-overlay-icon toc-header-icon" for="__toc">
        <div class="visually-hidden">Toggle table of contents sidebar</div>
        <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
      </label>
    </div>
  </header>
  <aside class="sidebar-drawer">
    <div class="sidebar-container">
      
      <div class="sidebar-sticky"><div class="sidebar-scroll"><a class="sidebar-brand" href="../../index.html">
  
  
  <span class="sidebar-brand-text">nodriver  documentation</span>
  
</a><form class="sidebar-search-container" method="get" action="../../search.html" role="search">
  <input class="sidebar-search" placeholder="Search" name="q" aria-label="Search">
  <input type="hidden" name="check_keywords" value="yes">
  <input type="hidden" name="area" value="default">
</form>
<div id="searchbox"></div><div class="sidebar-tree">
  <ul>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html">Quickstart guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#usage-example">usage example</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#more-complete-example">More complete example</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#custom-starting-options">Custom starting options</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#alternative-custom-options">Alternative custom options</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#proxies-socks5-authenticated-too">Proxies (socks5, authenticated too)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#concrete-example">Concrete example</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../classes/browser.html">Browser class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../classes/tab.html">Tab class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../classes/element.html">Element class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../classes/others_and_helpers.html">Other classes and Helper classes</a></li>
</ul>
<ul class="current">
<li class="toctree-l1 current has-children"><a class="reference internal" href="../cdp.html">CDP object</a><input checked="" class="toctree-checkbox" id="toctree-checkbox-1" name="toctree-checkbox-1" role="switch" type="checkbox"/><label for="toctree-checkbox-1"><div class="visually-hidden">Toggle navigation of CDP object</div><i class="icon"><svg><use href="#svg-arrow-right"></use></svg></i></label><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="accessibility.html">Accessibility</a></li>
<li class="toctree-l2"><a class="reference internal" href="animation.html">Animation</a></li>
<li class="toctree-l2"><a class="reference internal" href="audits.html">Audits</a></li>
<li class="toctree-l2"><a class="reference internal" href="autofill.html">Autofill</a></li>
<li class="toctree-l2"><a class="reference internal" href="background_service.html">BackgroundService</a></li>
<li class="toctree-l2"><a class="reference internal" href="bluetooth_emulation.html">BluetoothEmulation</a></li>
<li class="toctree-l2"><a class="reference internal" href="browser.html">Browser</a></li>
<li class="toctree-l2"><a class="reference internal" href="cache_storage.html">CacheStorage</a></li>
<li class="toctree-l2"><a class="reference internal" href="cast.html">Cast</a></li>
<li class="toctree-l2"><a class="reference internal" href="console.html">Console</a></li>
<li class="toctree-l2"><a class="reference internal" href="css.html">CSS</a></li>
<li class="toctree-l2"><a class="reference internal" href="debugger.html">Debugger</a></li>
<li class="toctree-l2"><a class="reference internal" href="device_access.html">DeviceAccess</a></li>
<li class="toctree-l2"><a class="reference internal" href="device_orientation.html">DeviceOrientation</a></li>
<li class="toctree-l2"><a class="reference internal" href="dom.html">DOM</a></li>
<li class="toctree-l2"><a class="reference internal" href="dom_debugger.html">DOMDebugger</a></li>
<li class="toctree-l2"><a class="reference internal" href="dom_snapshot.html">DOMSnapshot</a></li>
<li class="toctree-l2"><a class="reference internal" href="dom_storage.html">DOMStorage</a></li>
<li class="toctree-l2"><a class="reference internal" href="emulation.html">Emulation</a></li>
<li class="toctree-l2"><a class="reference internal" href="event_breakpoints.html">EventBreakpoints</a></li>
<li class="toctree-l2"><a class="reference internal" href="extensions.html">Extensions</a></li>
<li class="toctree-l2"><a class="reference internal" href="fed_cm.html">FedCm</a></li>
<li class="toctree-l2"><a class="reference internal" href="fetch.html">Fetch</a></li>
<li class="toctree-l2"><a class="reference internal" href="file_system.html">FileSystem</a></li>
<li class="toctree-l2"><a class="reference internal" href="headless_experimental.html">HeadlessExperimental</a></li>
<li class="toctree-l2"><a class="reference internal" href="heap_profiler.html">HeapProfiler</a></li>
<li class="toctree-l2"><a class="reference internal" href="indexed_db.html">IndexedDB</a></li>
<li class="toctree-l2"><a class="reference internal" href="input_.html">Input</a></li>
<li class="toctree-l2"><a class="reference internal" href="inspector.html">Inspector</a></li>
<li class="toctree-l2"><a class="reference internal" href="io.html">IO</a></li>
<li class="toctree-l2"><a class="reference internal" href="layer_tree.html">LayerTree</a></li>
<li class="toctree-l2"><a class="reference internal" href="log.html">Log</a></li>
<li class="toctree-l2"><a class="reference internal" href="media.html">Media</a></li>
<li class="toctree-l2"><a class="reference internal" href="memory.html">Memory</a></li>
<li class="toctree-l2"><a class="reference internal" href="network.html">Network</a></li>
<li class="toctree-l2"><a class="reference internal" href="overlay.html">Overlay</a></li>
<li class="toctree-l2"><a class="reference internal" href="page.html">Page</a></li>
<li class="toctree-l2"><a class="reference internal" href="performance.html">Performance</a></li>
<li class="toctree-l2"><a class="reference internal" href="performance_timeline.html">PerformanceTimeline</a></li>
<li class="toctree-l2"><a class="reference internal" href="preload.html">Preload</a></li>
<li class="toctree-l2"><a class="reference internal" href="profiler.html">Profiler</a></li>
<li class="toctree-l2"><a class="reference internal" href="pwa.html">PWA</a></li>
<li class="toctree-l2"><a class="reference internal" href="runtime.html">Runtime</a></li>
<li class="toctree-l2"><a class="reference internal" href="schema.html">Schema</a></li>
<li class="toctree-l2"><a class="reference internal" href="security.html">Security</a></li>
<li class="toctree-l2"><a class="reference internal" href="service_worker.html">ServiceWorker</a></li>
<li class="toctree-l2"><a class="reference internal" href="storage.html">Storage</a></li>
<li class="toctree-l2"><a class="reference internal" href="system_info.html">SystemInfo</a></li>
<li class="toctree-l2 current current-page"><a class="current reference internal" href="#">Target</a></li>
<li class="toctree-l2"><a class="reference internal" href="tethering.html">Tethering</a></li>
<li class="toctree-l2"><a class="reference internal" href="tracing.html">Tracing</a></li>
<li class="toctree-l2"><a class="reference internal" href="web_audio.html">WebAudio</a></li>
<li class="toctree-l2"><a class="reference internal" href="web_authn.html">WebAuthn</a></li>
</ul>
</li>
</ul>

</div>
</div>
      </div>
      
    </div>
  </aside>
  <div class="main">
    <div class="content">
      <div class="article-container">
        <a href="#" class="back-to-top muted-link">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
            <path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8v12z"></path>
          </svg>
          <span>Back to top</span>
        </a>
        <div class="content-icon-container">
          
<div class="theme-toggle-container theme-toggle-content">
            <button class="theme-toggle">
              <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
              <svg class="theme-icon-when-auto"><use href="#svg-sun-half"></use></svg>
              <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
              <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
            </button>
          </div>
          <label class="toc-overlay-icon toc-content-icon" for="__toc">
            <div class="visually-hidden">Toggle table of contents sidebar</div>
            <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
          </label>
        </div>
        <article role="main">
          <section id="target">
<h1>Target<a class="headerlink" href="#target" title="Link to this heading">#</a></h1>
<p>Supports additional targets discovery and allows to attach to them.</p>
<ul class="simple" id="module-nodriver.cdp.target">
<li><p><a class="reference internal" href="#types">Types</a></p></li>
<li><p><a class="reference internal" href="#commands">Commands</a></p></li>
<li><p><a class="reference internal" href="#events">Events</a></p></li>
</ul>
<section id="types">
<h2>Types<a class="headerlink" href="#types" title="Link to this heading">#</a></h2>
<p>Generally, you do not need to instantiate CDP types
yourself. Instead, the API creates objects for you as return
values from commands, and then you can use those objects as
arguments to other commands.</p>
<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.target.TargetID">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">TargetID</span></span><a class="reference internal" href="../../_modules/nodriver/cdp/target.html#TargetID"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.target.TargetID" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.target.SessionID">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">SessionID</span></span><a class="reference internal" href="../../_modules/nodriver/cdp/target.html#SessionID"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.target.SessionID" title="Link to this definition">#</a></dt>
<dd><p>Unique identifier of attached debugging session.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.target.TargetInfo">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">TargetInfo</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">target_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type_</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">title</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">url</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">attached</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">can_access_opener</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">opener_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">opener_frame_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">browser_context_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">subtype</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/target.html#TargetInfo"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.target.TargetInfo" title="Link to this definition">#</a></dt>
<dd><dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.target.TargetInfo.target_id">
<span class="sig-name descname"><span class="pre">target_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.target.TargetID" title="nodriver.cdp.target.TargetID"><code class="xref py py-class docutils literal notranslate"><span class="pre">TargetID</span></code></a></em><a class="headerlink" href="#nodriver.cdp.target.TargetInfo.target_id" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.target.TargetInfo.type_">
<span class="sig-name descname"><span class="pre">type_</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.target.TargetInfo.type_" title="Link to this definition">#</a></dt>
<dd><p>//source.chromium.org/chromium/chromium/src/+/main:content/browser/devtools/devtools_agent_host_impl.cc?ss=chromium&amp;q=f:devtools%20-f:out%20%22::kTypeTab%5B%5D%22</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>List of types</p>
</dd>
<dt class="field-even">Type<span class="colon">:</span></dt>
<dd class="field-even"><p>https</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.target.TargetInfo.title">
<span class="sig-name descname"><span class="pre">title</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.target.TargetInfo.title" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.target.TargetInfo.url">
<span class="sig-name descname"><span class="pre">url</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.target.TargetInfo.url" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.target.TargetInfo.attached">
<span class="sig-name descname"><span class="pre">attached</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a></em><a class="headerlink" href="#nodriver.cdp.target.TargetInfo.attached" title="Link to this definition">#</a></dt>
<dd><p>Whether the target has an attached client.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.target.TargetInfo.can_access_opener">
<span class="sig-name descname"><span class="pre">can_access_opener</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a></em><a class="headerlink" href="#nodriver.cdp.target.TargetInfo.can_access_opener" title="Link to this definition">#</a></dt>
<dd><p>Whether the target has access to the originating window.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.target.TargetInfo.opener_id">
<span class="sig-name descname"><span class="pre">opener_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.target.TargetID" title="nodriver.cdp.target.TargetID"><code class="xref py py-class docutils literal notranslate"><span class="pre">TargetID</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.target.TargetInfo.opener_id" title="Link to this definition">#</a></dt>
<dd><p>Opener target Id</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.target.TargetInfo.opener_frame_id">
<span class="sig-name descname"><span class="pre">opener_frame_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="page.html#nodriver.cdp.page.FrameId" title="nodriver.cdp.page.FrameId"><code class="xref py py-class docutils literal notranslate"><span class="pre">FrameId</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.target.TargetInfo.opener_frame_id" title="Link to this definition">#</a></dt>
<dd><p>Frame id of originating window (is only set if target has an opener).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.target.TargetInfo.browser_context_id">
<span class="sig-name descname"><span class="pre">browser_context_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="browser.html#nodriver.cdp.browser.BrowserContextID" title="nodriver.cdp.browser.BrowserContextID"><code class="xref py py-class docutils literal notranslate"><span class="pre">BrowserContextID</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.target.TargetInfo.browser_context_id" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.target.TargetInfo.subtype">
<span class="sig-name descname"><span class="pre">subtype</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.target.TargetInfo.subtype" title="Link to this definition">#</a></dt>
<dd><p>Provides additional details for specific target types. For example, for
the type of “page”, this may be set to “prerender”.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.target.FilterEntry">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">FilterEntry</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">exclude</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type_</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/target.html#FilterEntry"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.target.FilterEntry" title="Link to this definition">#</a></dt>
<dd><p>A filter used by target query/discovery/auto-attach operations.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.target.FilterEntry.exclude">
<span class="sig-name descname"><span class="pre">exclude</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.target.FilterEntry.exclude" title="Link to this definition">#</a></dt>
<dd><p>If set, causes exclusion of matching targets from the list.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.target.FilterEntry.type_">
<span class="sig-name descname"><span class="pre">type_</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.target.FilterEntry.type_" title="Link to this definition">#</a></dt>
<dd><p>If not present, matches any type.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.target.TargetFilter">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">TargetFilter</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">iterable</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">()</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/target.html#TargetFilter"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.target.TargetFilter" title="Link to this definition">#</a></dt>
<dd><p>The entries in TargetFilter are matched sequentially against targets and
the first entry that matches determines if the target is included or not,
depending on the value of <code class="docutils literal notranslate"><span class="pre">exclude</span></code> field in the entry.
If filter is not specified, the one assumed is
[{type: “browser”, exclude: true}, {type: “tab”, exclude: true}, {}]
(i.e. include everything but <code class="docutils literal notranslate"><span class="pre">browser</span></code> and <code class="docutils literal notranslate"><span class="pre">tab</span></code>).</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.target.RemoteLocation">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">RemoteLocation</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">host</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">port</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/target.html#RemoteLocation"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.target.RemoteLocation" title="Link to this definition">#</a></dt>
<dd><dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.target.RemoteLocation.host">
<span class="sig-name descname"><span class="pre">host</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.target.RemoteLocation.host" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.target.RemoteLocation.port">
<span class="sig-name descname"><span class="pre">port</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></em><a class="headerlink" href="#nodriver.cdp.target.RemoteLocation.port" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.target.WindowState">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">WindowState</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">names</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">module</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">qualname</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">boundary</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/target.html#WindowState"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.target.WindowState" title="Link to this definition">#</a></dt>
<dd><p>The state of the target window.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.target.WindowState.NORMAL">
<span class="sig-name descname"><span class="pre">NORMAL</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'normal'</span></em><a class="headerlink" href="#nodriver.cdp.target.WindowState.NORMAL" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.target.WindowState.MINIMIZED">
<span class="sig-name descname"><span class="pre">MINIMIZED</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'minimized'</span></em><a class="headerlink" href="#nodriver.cdp.target.WindowState.MINIMIZED" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.target.WindowState.MAXIMIZED">
<span class="sig-name descname"><span class="pre">MAXIMIZED</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'maximized'</span></em><a class="headerlink" href="#nodriver.cdp.target.WindowState.MAXIMIZED" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.target.WindowState.FULLSCREEN">
<span class="sig-name descname"><span class="pre">FULLSCREEN</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'fullscreen'</span></em><a class="headerlink" href="#nodriver.cdp.target.WindowState.FULLSCREEN" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

</section>
<section id="commands">
<h2>Commands<a class="headerlink" href="#commands" title="Link to this heading">#</a></h2>
<p>Each command is a generator function. The return
type <code class="docutils literal notranslate"><span class="pre">Generator[x,</span> <span class="pre">y,</span> <span class="pre">z]</span></code> indicates that the generator
<em>yields</em> arguments of type <code class="docutils literal notranslate"><span class="pre">x</span></code>, it must be resumed with
an argument of type <code class="docutils literal notranslate"><span class="pre">y</span></code>, and it returns type <code class="docutils literal notranslate"><span class="pre">z</span></code>. In
this library, types <code class="docutils literal notranslate"><span class="pre">x</span></code> and <code class="docutils literal notranslate"><span class="pre">y</span></code> are the same for all
commands, and <code class="docutils literal notranslate"><span class="pre">z</span></code> is the return type you should pay attention
to. For more information, see
<a class="reference internal" href="../../readme.html#getting-started-commands"><span class="std std-ref">Getting Started: Commands</span></a>.</p>
<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.target.activate_target">
<span class="sig-name descname"><span class="pre">activate_target</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">target_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/target.html#activate_target"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.target.activate_target" title="Link to this definition">#</a></dt>
<dd><p>Activates (focuses) the target.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>target_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.target.TargetID" title="nodriver.cdp.target.TargetID"><code class="xref py py-class docutils literal notranslate"><span class="pre">TargetID</span></code></a></span>) – </p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.target.attach_to_browser_target">
<span class="sig-name descname"><span class="pre">attach_to_browser_target</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/target.html#attach_to_browser_target"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.target.attach_to_browser_target" title="Link to this definition">#</a></dt>
<dd><p>Attaches to the browser target, only uses flat sessionId mode.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference internal" href="#nodriver.cdp.target.SessionID" title="nodriver.cdp.target.SessionID"><code class="xref py py-class docutils literal notranslate"><span class="pre">SessionID</span></code></a>]</span></p>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>Id assigned to the session.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.target.attach_to_target">
<span class="sig-name descname"><span class="pre">attach_to_target</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">target_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">flatten</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/target.html#attach_to_target"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.target.attach_to_target" title="Link to this definition">#</a></dt>
<dd><p>Attaches to the target with given id.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>target_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.target.TargetID" title="nodriver.cdp.target.TargetID"><code class="xref py py-class docutils literal notranslate"><span class="pre">TargetID</span></code></a></span>) – </p></li>
<li><p><strong>flatten</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <em>(Optional)</em> Enables “flat” access to the session via specifying sessionId attribute in the commands. We plan to make this the default, deprecate non-flattened mode, and eventually retire it. See crbug.com/991325.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference internal" href="#nodriver.cdp.target.SessionID" title="nodriver.cdp.target.SessionID"><code class="xref py py-class docutils literal notranslate"><span class="pre">SessionID</span></code></a>]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>Id assigned to the session.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.target.auto_attach_related">
<span class="sig-name descname"><span class="pre">auto_attach_related</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">target_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">wait_for_debugger_on_start</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">filter_</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/target.html#auto_attach_related"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.target.auto_attach_related" title="Link to this definition">#</a></dt>
<dd><p>Adds the specified target to the list of targets that will be monitored for any related target
creation (such as child frames, child workers and new versions of service worker) and reported
through <code class="docutils literal notranslate"><span class="pre">attachedToTarget</span></code>. The specified target is also auto-attached.
This cancels the effect of any previous <code class="docutils literal notranslate"><span class="pre">setAutoAttach</span></code> and is also cancelled by subsequent
<code class="docutils literal notranslate"><span class="pre">setAutoAttach</span></code>. Only available at the Browser target.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>target_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.target.TargetID" title="nodriver.cdp.target.TargetID"><code class="xref py py-class docutils literal notranslate"><span class="pre">TargetID</span></code></a></span>) – </p></li>
<li><p><strong>wait_for_debugger_on_start</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a></span>) – Whether to pause new targets when attaching to them. Use <code class="docutils literal notranslate"><span class="pre">`Runtime.runIfWaitingForDebugger`</span></code> to run paused targets.</p></li>
<li><p><strong>filter</strong> – <strong>(EXPERIMENTAL)</strong> <em>(Optional)</em> Only targets matching filter will be attached.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.target.close_target">
<span class="sig-name descname"><span class="pre">close_target</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">target_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/target.html#close_target"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.target.close_target" title="Link to this definition">#</a></dt>
<dd><p>Closes the target. If the target is a page that gets closed too.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>target_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.target.TargetID" title="nodriver.cdp.target.TargetID"><code class="xref py py-class docutils literal notranslate"><span class="pre">TargetID</span></code></a></span>) – </p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>Always set to true. If an error occurs, the response indicates protocol error.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.target.create_browser_context">
<span class="sig-name descname"><span class="pre">create_browser_context</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">dispose_on_detach</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">proxy_server</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">proxy_bypass_list</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">origins_with_universal_network_access</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/target.html#create_browser_context"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.target.create_browser_context" title="Link to this definition">#</a></dt>
<dd><p>Creates a new empty BrowserContext. Similar to an incognito profile but you can have more than
one.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>dispose_on_detach</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <strong>(EXPERIMENTAL)</strong> <em>(Optional)</em> If specified, disposes this context when debugging session disconnects.</p></li>
<li><p><strong>proxy_server</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]</span>) – <strong>(EXPERIMENTAL)</strong> <em>(Optional)</em> Proxy server, similar to the one passed to –proxy-server</p></li>
<li><p><strong>proxy_bypass_list</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]</span>) – <strong>(EXPERIMENTAL)</strong> <em>(Optional)</em> Proxy bypass list, similar to the one passed to –proxy-bypass-list</p></li>
<li><p><strong>origins_with_universal_network_access</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]]</span>) – <strong>(EXPERIMENTAL)</strong> <em>(Optional)</em> An optional list of origins to grant unlimited cross-origin access to. Parts of the URL other than those constituting origin are ignored.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference internal" href="browser.html#nodriver.cdp.browser.BrowserContextID" title="nodriver.cdp.browser.BrowserContextID"><code class="xref py py-class docutils literal notranslate"><span class="pre">BrowserContextID</span></code></a>]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>The id of the context created.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.target.create_target">
<span class="sig-name descname"><span class="pre">create_target</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">url</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">left</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">top</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">width</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">height</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">window_state</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">browser_context_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">enable_begin_frame_control</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">new_window</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">background</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">for_tab</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/target.html#create_target"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.target.create_target" title="Link to this definition">#</a></dt>
<dd><p>Creates a new page.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>url</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – The initial URL the page will be navigated to. An empty string indicates <a class="reference external" href="about:blank">about:blank</a>.</p></li>
<li><p><strong>left</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a>]</span>) – <strong>(EXPERIMENTAL)</strong> <em>(Optional)</em> Frame left origin in DIP (requires newWindow to be true or headless shell).</p></li>
<li><p><strong>top</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a>]</span>) – <strong>(EXPERIMENTAL)</strong> <em>(Optional)</em> Frame top origin in DIP (requires newWindow to be true or headless shell).</p></li>
<li><p><strong>width</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a>]</span>) – <em>(Optional)</em> Frame width in DIP (requires newWindow to be true or headless shell).</p></li>
<li><p><strong>height</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a>]</span>) – <em>(Optional)</em> Frame height in DIP (requires newWindow to be true or headless shell).</p></li>
<li><p><strong>window_state</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="#nodriver.cdp.target.WindowState" title="nodriver.cdp.target.WindowState"><code class="xref py py-class docutils literal notranslate"><span class="pre">WindowState</span></code></a>]</span>) – <em>(Optional)</em> Frame window state (requires newWindow to be true or headless shell). Default is normal.</p></li>
<li><p><strong>browser_context_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="browser.html#nodriver.cdp.browser.BrowserContextID" title="nodriver.cdp.browser.BrowserContextID"><code class="xref py py-class docutils literal notranslate"><span class="pre">BrowserContextID</span></code></a>]</span>) – <strong>(EXPERIMENTAL)</strong> <em>(Optional)</em> The browser context to create the page in.</p></li>
<li><p><strong>enable_begin_frame_control</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <strong>(EXPERIMENTAL)</strong> <em>(Optional)</em> Whether BeginFrames for this target will be controlled via DevTools (headless shell only, not supported on MacOS yet, false by default).</p></li>
<li><p><strong>new_window</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <em>(Optional)</em> Whether to create a new Window or Tab (false by default, not supported by headless shell).</p></li>
<li><p><strong>background</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <em>(Optional)</em> Whether to create the target in background or foreground (false by default, not supported by headless shell).</p></li>
<li><p><strong>for_tab</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <strong>(EXPERIMENTAL)</strong> <em>(Optional)</em> Whether to create the target of type “tab”.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference internal" href="#nodriver.cdp.target.TargetID" title="nodriver.cdp.target.TargetID"><code class="xref py py-class docutils literal notranslate"><span class="pre">TargetID</span></code></a>]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>The id of the page opened.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.target.detach_from_target">
<span class="sig-name descname"><span class="pre">detach_from_target</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">session_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">target_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/target.html#detach_from_target"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.target.detach_from_target" title="Link to this definition">#</a></dt>
<dd><p>Detaches session with given id.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>session_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="#nodriver.cdp.target.SessionID" title="nodriver.cdp.target.SessionID"><code class="xref py py-class docutils literal notranslate"><span class="pre">SessionID</span></code></a>]</span>) – <em>(Optional)</em> Session to detach.</p></li>
<li><p><strong>target_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="#nodriver.cdp.target.TargetID" title="nodriver.cdp.target.TargetID"><code class="xref py py-class docutils literal notranslate"><span class="pre">TargetID</span></code></a>]</span>) – <strong>(DEPRECATED)</strong> <em>(Optional)</em> Deprecated.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.target.dispose_browser_context">
<span class="sig-name descname"><span class="pre">dispose_browser_context</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">browser_context_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/target.html#dispose_browser_context"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.target.dispose_browser_context" title="Link to this definition">#</a></dt>
<dd><p>Deletes a BrowserContext. All the belonging pages will be closed without calling their
beforeunload hooks.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>browser_context_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="browser.html#nodriver.cdp.browser.BrowserContextID" title="nodriver.cdp.browser.BrowserContextID"><code class="xref py py-class docutils literal notranslate"><span class="pre">BrowserContextID</span></code></a></span>) – </p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.target.expose_dev_tools_protocol">
<span class="sig-name descname"><span class="pre">expose_dev_tools_protocol</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">target_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">binding_name</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">inherit_permissions</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/target.html#expose_dev_tools_protocol"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.target.expose_dev_tools_protocol" title="Link to this definition">#</a></dt>
<dd><p>Inject object to the target’s main frame that provides a communication
channel with browser target.</p>
<p>Injected object will be available as <code class="docutils literal notranslate"><span class="pre">window[bindingName]</span></code>.</p>
<p>The object has the following API:
- <code class="docutils literal notranslate"><span class="pre">binding.send(json)</span></code> - a method to send messages over the remote debugging protocol
- <code class="docutils literal notranslate"><span class="pre">binding.onmessage</span> <span class="pre">=</span> <span class="pre">json</span> <span class="pre">=&gt;</span> <span class="pre">handleMessage(json)</span></code> - a callback that will be called for the protocol notifications and command responses.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>target_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.target.TargetID" title="nodriver.cdp.target.TargetID"><code class="xref py py-class docutils literal notranslate"><span class="pre">TargetID</span></code></a></span>) – </p></li>
<li><p><strong>binding_name</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]</span>) – <em>(Optional)</em> Binding name, ‘cdp’ if not specified.</p></li>
<li><p><strong>inherit_permissions</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <em>(Optional)</em> If true, inherits the current root session’s permissions (default: false).</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.target.get_browser_contexts">
<span class="sig-name descname"><span class="pre">get_browser_contexts</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/target.html#get_browser_contexts"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.target.get_browser_contexts" title="Link to this definition">#</a></dt>
<dd><p>Returns all browser contexts created with <code class="docutils literal notranslate"><span class="pre">Target.createBrowserContext</span></code> method.</p>
<dl class="field-list simple">
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="browser.html#nodriver.cdp.browser.BrowserContextID" title="nodriver.cdp.browser.BrowserContextID"><code class="xref py py-class docutils literal notranslate"><span class="pre">BrowserContextID</span></code></a>]]</span></p>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>An array of browser context ids.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.target.get_target_info">
<span class="sig-name descname"><span class="pre">get_target_info</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">target_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/target.html#get_target_info"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.target.get_target_info" title="Link to this definition">#</a></dt>
<dd><p>Returns information about a target.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>target_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="#nodriver.cdp.target.TargetID" title="nodriver.cdp.target.TargetID"><code class="xref py py-class docutils literal notranslate"><span class="pre">TargetID</span></code></a>]</span>) – <em>(Optional)</em></p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference internal" href="#nodriver.cdp.target.TargetInfo" title="nodriver.cdp.target.TargetInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">TargetInfo</span></code></a>]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.target.get_targets">
<span class="sig-name descname"><span class="pre">get_targets</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">filter_</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/target.html#get_targets"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.target.get_targets" title="Link to this definition">#</a></dt>
<dd><p>Retrieves a list of available targets.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>filter</strong> – <strong>(EXPERIMENTAL)</strong> <em>(Optional)</em> Only targets matching filter will be reported. If filter is not specified and target discovery is currently enabled, a filter used for target discovery is used for consistency.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.target.TargetInfo" title="nodriver.cdp.target.TargetInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">TargetInfo</span></code></a>]]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>The list of targets.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.target.send_message_to_target">
<span class="sig-name descname"><span class="pre">send_message_to_target</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">message</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">session_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">target_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/target.html#send_message_to_target"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.target.send_message_to_target" title="Link to this definition">#</a></dt>
<dd><p>Sends protocol message over session with given id.
Consider using flat mode instead; see commands attachToTarget, setAutoAttach,
and crbug.com/991325.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 1.3.</span></p>
</div>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>message</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – </p></li>
<li><p><strong>session_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="#nodriver.cdp.target.SessionID" title="nodriver.cdp.target.SessionID"><code class="xref py py-class docutils literal notranslate"><span class="pre">SessionID</span></code></a>]</span>) – <em>(Optional)</em> Identifier of the session.</p></li>
<li><p><strong>target_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="#nodriver.cdp.target.TargetID" title="nodriver.cdp.target.TargetID"><code class="xref py py-class docutils literal notranslate"><span class="pre">TargetID</span></code></a>]</span>) – <strong>(DEPRECATED)</strong> <em>(Optional)</em> Deprecated.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 1.3.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.target.set_auto_attach">
<span class="sig-name descname"><span class="pre">set_auto_attach</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">auto_attach</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">wait_for_debugger_on_start</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">flatten</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">filter_</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/target.html#set_auto_attach"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.target.set_auto_attach" title="Link to this definition">#</a></dt>
<dd><p>Controls whether to automatically attach to new targets which are considered to be related to
this one. When turned on, attaches to all existing related targets as well. When turned off,
automatically detaches from all currently attached targets.
This also clears all targets added by <code class="docutils literal notranslate"><span class="pre">autoAttachRelated</span></code> from the list of targets to watch
for creation of related targets.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>auto_attach</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a></span>) – Whether to auto-attach to related targets.</p></li>
<li><p><strong>wait_for_debugger_on_start</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a></span>) – Whether to pause new targets when attaching to them. Use <code class="docutils literal notranslate"><span class="pre">`Runtime.runIfWaitingForDebugger`</span></code> to run paused targets.</p></li>
<li><p><strong>flatten</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <strong>(EXPERIMENTAL)</strong> <em>(Optional)</em> Enables “flat” access to the session via specifying sessionId attribute in the commands. We plan to make this the default, deprecate non-flattened mode, and eventually retire it. See crbug.com/991325.</p></li>
<li><p><strong>filter</strong> – <strong>(EXPERIMENTAL)</strong> <em>(Optional)</em> Only targets matching filter will be attached.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.target.set_discover_targets">
<span class="sig-name descname"><span class="pre">set_discover_targets</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">discover</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">filter_</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/target.html#set_discover_targets"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.target.set_discover_targets" title="Link to this definition">#</a></dt>
<dd><p>Controls whether to discover available targets and notify via
<code class="docutils literal notranslate"><span class="pre">targetCreated/targetInfoChanged/targetDestroyed</span></code> events.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>discover</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a></span>) – Whether to discover available targets.</p></li>
<li><p><strong>filter</strong> – <strong>(EXPERIMENTAL)</strong> <em>(Optional)</em> Only targets matching filter will be attached. If <code class="docutils literal notranslate"><span class="pre">`discover``</span></code> is false, <code class="docutils literal notranslate"><span class="pre">``filter`</span></code> must be omitted or empty.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.target.set_remote_locations">
<span class="sig-name descname"><span class="pre">set_remote_locations</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">locations</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/target.html#set_remote_locations"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.target.set_remote_locations" title="Link to this definition">#</a></dt>
<dd><p>Enables target discovery for the specified locations, when <code class="docutils literal notranslate"><span class="pre">setDiscoverTargets</span></code> was set to
<code class="docutils literal notranslate"><span class="pre">true</span></code>.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>locations</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.target.RemoteLocation" title="nodriver.cdp.target.RemoteLocation"><code class="xref py py-class docutils literal notranslate"><span class="pre">RemoteLocation</span></code></a>]</span>) – List of remote locations.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

</section>
<section id="events">
<h2>Events<a class="headerlink" href="#events" title="Link to this heading">#</a></h2>
<p>Generally, you do not need to instantiate CDP events
yourself. Instead, the API creates events for you and then
you use the event’s attributes.</p>
<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.target.AttachedToTarget">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">AttachedToTarget</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">session_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">target_info</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">waiting_for_debugger</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/target.html#AttachedToTarget"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.target.AttachedToTarget" title="Link to this definition">#</a></dt>
<dd><p><strong>EXPERIMENTAL</strong></p>
<p>Issued when attached to target because of auto-attach or <code class="docutils literal notranslate"><span class="pre">attachToTarget</span></code> command.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.target.AttachedToTarget.session_id">
<span class="sig-name descname"><span class="pre">session_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.target.SessionID" title="nodriver.cdp.target.SessionID"><code class="xref py py-class docutils literal notranslate"><span class="pre">SessionID</span></code></a></em><a class="headerlink" href="#nodriver.cdp.target.AttachedToTarget.session_id" title="Link to this definition">#</a></dt>
<dd><p>Identifier assigned to the session used to send/receive messages.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.target.AttachedToTarget.target_info">
<span class="sig-name descname"><span class="pre">target_info</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.target.TargetInfo" title="nodriver.cdp.target.TargetInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">TargetInfo</span></code></a></em><a class="headerlink" href="#nodriver.cdp.target.AttachedToTarget.target_info" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.target.AttachedToTarget.waiting_for_debugger">
<span class="sig-name descname"><span class="pre">waiting_for_debugger</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a></em><a class="headerlink" href="#nodriver.cdp.target.AttachedToTarget.waiting_for_debugger" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.target.DetachedFromTarget">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">DetachedFromTarget</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">session_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">target_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/target.html#DetachedFromTarget"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.target.DetachedFromTarget" title="Link to this definition">#</a></dt>
<dd><p><strong>EXPERIMENTAL</strong></p>
<p>Issued when detached from target for any reason (including <code class="docutils literal notranslate"><span class="pre">detachFromTarget</span></code> command). Can be
issued multiple times per target if multiple sessions have been attached to it.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.target.DetachedFromTarget.session_id">
<span class="sig-name descname"><span class="pre">session_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.target.SessionID" title="nodriver.cdp.target.SessionID"><code class="xref py py-class docutils literal notranslate"><span class="pre">SessionID</span></code></a></em><a class="headerlink" href="#nodriver.cdp.target.DetachedFromTarget.session_id" title="Link to this definition">#</a></dt>
<dd><p>Detached session identifier.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.target.DetachedFromTarget.target_id">
<span class="sig-name descname"><span class="pre">target_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.target.TargetID" title="nodriver.cdp.target.TargetID"><code class="xref py py-class docutils literal notranslate"><span class="pre">TargetID</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.target.DetachedFromTarget.target_id" title="Link to this definition">#</a></dt>
<dd><p>Deprecated.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.target.ReceivedMessageFromTarget">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">ReceivedMessageFromTarget</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">session_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">message</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">target_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/target.html#ReceivedMessageFromTarget"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.target.ReceivedMessageFromTarget" title="Link to this definition">#</a></dt>
<dd><p>Notifies about a new protocol message received from the session (as reported in
<code class="docutils literal notranslate"><span class="pre">attachedToTarget</span></code> event).</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.target.ReceivedMessageFromTarget.session_id">
<span class="sig-name descname"><span class="pre">session_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.target.SessionID" title="nodriver.cdp.target.SessionID"><code class="xref py py-class docutils literal notranslate"><span class="pre">SessionID</span></code></a></em><a class="headerlink" href="#nodriver.cdp.target.ReceivedMessageFromTarget.session_id" title="Link to this definition">#</a></dt>
<dd><p>Identifier of a session which sends a message.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.target.ReceivedMessageFromTarget.message">
<span class="sig-name descname"><span class="pre">message</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.target.ReceivedMessageFromTarget.message" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.target.ReceivedMessageFromTarget.target_id">
<span class="sig-name descname"><span class="pre">target_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.target.TargetID" title="nodriver.cdp.target.TargetID"><code class="xref py py-class docutils literal notranslate"><span class="pre">TargetID</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.target.ReceivedMessageFromTarget.target_id" title="Link to this definition">#</a></dt>
<dd><p>Deprecated.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.target.TargetCreated">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">TargetCreated</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">target_info</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/target.html#TargetCreated"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.target.TargetCreated" title="Link to this definition">#</a></dt>
<dd><p>Issued when a possible inspection target is created.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.target.TargetCreated.target_info">
<span class="sig-name descname"><span class="pre">target_info</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.target.TargetInfo" title="nodriver.cdp.target.TargetInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">TargetInfo</span></code></a></em><a class="headerlink" href="#nodriver.cdp.target.TargetCreated.target_info" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.target.TargetDestroyed">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">TargetDestroyed</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">target_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/target.html#TargetDestroyed"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.target.TargetDestroyed" title="Link to this definition">#</a></dt>
<dd><p>Issued when a target is destroyed.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.target.TargetDestroyed.target_id">
<span class="sig-name descname"><span class="pre">target_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.target.TargetID" title="nodriver.cdp.target.TargetID"><code class="xref py py-class docutils literal notranslate"><span class="pre">TargetID</span></code></a></em><a class="headerlink" href="#nodriver.cdp.target.TargetDestroyed.target_id" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.target.TargetCrashed">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">TargetCrashed</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">target_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">status</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">error_code</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/target.html#TargetCrashed"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.target.TargetCrashed" title="Link to this definition">#</a></dt>
<dd><p>Issued when a target has crashed.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.target.TargetCrashed.target_id">
<span class="sig-name descname"><span class="pre">target_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.target.TargetID" title="nodriver.cdp.target.TargetID"><code class="xref py py-class docutils literal notranslate"><span class="pre">TargetID</span></code></a></em><a class="headerlink" href="#nodriver.cdp.target.TargetCrashed.target_id" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.target.TargetCrashed.status">
<span class="sig-name descname"><span class="pre">status</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.target.TargetCrashed.status" title="Link to this definition">#</a></dt>
<dd><p>Termination status type.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.target.TargetCrashed.error_code">
<span class="sig-name descname"><span class="pre">error_code</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></em><a class="headerlink" href="#nodriver.cdp.target.TargetCrashed.error_code" title="Link to this definition">#</a></dt>
<dd><p>Termination error code.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.target.TargetInfoChanged">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">TargetInfoChanged</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">target_info</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/target.html#TargetInfoChanged"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.target.TargetInfoChanged" title="Link to this definition">#</a></dt>
<dd><p>Issued when some information about a target has changed. This only happens between
<code class="docutils literal notranslate"><span class="pre">targetCreated</span></code> and <code class="docutils literal notranslate"><span class="pre">targetDestroyed</span></code>.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.target.TargetInfoChanged.target_info">
<span class="sig-name descname"><span class="pre">target_info</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.target.TargetInfo" title="nodriver.cdp.target.TargetInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">TargetInfo</span></code></a></em><a class="headerlink" href="#nodriver.cdp.target.TargetInfoChanged.target_info" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

</section>
</section>

        </article>
      </div>
      <footer>
        
        <div class="related-pages">
          <a class="next-page" href="tethering.html">
              <div class="page-info">
                <div class="context">
                  <span>Next</span>
                </div>
                <div class="title">Tethering</div>
              </div>
              <svg class="furo-related-icon"><use href="#svg-arrow-right"></use></svg>
            </a>
          <a class="prev-page" href="system_info.html">
              <svg class="furo-related-icon"><use href="#svg-arrow-right"></use></svg>
              <div class="page-info">
                <div class="context">
                  <span>Previous</span>
                </div>
                
                <div class="title">SystemInfo</div>
                
              </div>
            </a>
        </div>
        <div class="bottom-of-page">
          <div class="left-details">
            <div class="copyright">
                Copyright &#169; 2023, Author
            </div>
            Made with <a href="https://www.sphinx-doc.org/">Sphinx</a> and <a class="muted-link" href="https://pradyunsg.me">@pradyunsg</a>'s
            
            <a href="https://github.com/pradyunsg/furo">Furo</a>
            
          </div>
          <div class="right-details">
            
          </div>
        </div>
        
      </footer>
    </div>
    <aside class="toc-drawer">
      
      
      <div class="toc-sticky toc-scroll">
        <div class="toc-title-container">
          <span class="toc-title">
            On this page
          </span>
        </div>
        <div class="toc-tree-container">
          <div class="toc-tree">
            <ul>
<li><a class="reference internal" href="#">Target</a><ul>
<li><a class="reference internal" href="#types">Types</a><ul>
<li><a class="reference internal" href="#nodriver.cdp.target.TargetID"><code class="docutils literal notranslate"><span class="pre">TargetID</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.target.SessionID"><code class="docutils literal notranslate"><span class="pre">SessionID</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.target.TargetInfo"><code class="docutils literal notranslate"><span class="pre">TargetInfo</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.target.TargetInfo.target_id"><code class="docutils literal notranslate"><span class="pre">TargetInfo.target_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.target.TargetInfo.type_"><code class="docutils literal notranslate"><span class="pre">TargetInfo.type_</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.target.TargetInfo.title"><code class="docutils literal notranslate"><span class="pre">TargetInfo.title</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.target.TargetInfo.url"><code class="docutils literal notranslate"><span class="pre">TargetInfo.url</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.target.TargetInfo.attached"><code class="docutils literal notranslate"><span class="pre">TargetInfo.attached</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.target.TargetInfo.can_access_opener"><code class="docutils literal notranslate"><span class="pre">TargetInfo.can_access_opener</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.target.TargetInfo.opener_id"><code class="docutils literal notranslate"><span class="pre">TargetInfo.opener_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.target.TargetInfo.opener_frame_id"><code class="docutils literal notranslate"><span class="pre">TargetInfo.opener_frame_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.target.TargetInfo.browser_context_id"><code class="docutils literal notranslate"><span class="pre">TargetInfo.browser_context_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.target.TargetInfo.subtype"><code class="docutils literal notranslate"><span class="pre">TargetInfo.subtype</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.target.FilterEntry"><code class="docutils literal notranslate"><span class="pre">FilterEntry</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.target.FilterEntry.exclude"><code class="docutils literal notranslate"><span class="pre">FilterEntry.exclude</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.target.FilterEntry.type_"><code class="docutils literal notranslate"><span class="pre">FilterEntry.type_</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.target.TargetFilter"><code class="docutils literal notranslate"><span class="pre">TargetFilter</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.target.RemoteLocation"><code class="docutils literal notranslate"><span class="pre">RemoteLocation</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.target.RemoteLocation.host"><code class="docutils literal notranslate"><span class="pre">RemoteLocation.host</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.target.RemoteLocation.port"><code class="docutils literal notranslate"><span class="pre">RemoteLocation.port</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.target.WindowState"><code class="docutils literal notranslate"><span class="pre">WindowState</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.target.WindowState.NORMAL"><code class="docutils literal notranslate"><span class="pre">WindowState.NORMAL</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.target.WindowState.MINIMIZED"><code class="docutils literal notranslate"><span class="pre">WindowState.MINIMIZED</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.target.WindowState.MAXIMIZED"><code class="docutils literal notranslate"><span class="pre">WindowState.MAXIMIZED</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.target.WindowState.FULLSCREEN"><code class="docutils literal notranslate"><span class="pre">WindowState.FULLSCREEN</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#commands">Commands</a><ul>
<li><a class="reference internal" href="#nodriver.cdp.target.activate_target"><code class="docutils literal notranslate"><span class="pre">activate_target()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.target.attach_to_browser_target"><code class="docutils literal notranslate"><span class="pre">attach_to_browser_target()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.target.attach_to_target"><code class="docutils literal notranslate"><span class="pre">attach_to_target()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.target.auto_attach_related"><code class="docutils literal notranslate"><span class="pre">auto_attach_related()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.target.close_target"><code class="docutils literal notranslate"><span class="pre">close_target()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.target.create_browser_context"><code class="docutils literal notranslate"><span class="pre">create_browser_context()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.target.create_target"><code class="docutils literal notranslate"><span class="pre">create_target()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.target.detach_from_target"><code class="docutils literal notranslate"><span class="pre">detach_from_target()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.target.dispose_browser_context"><code class="docutils literal notranslate"><span class="pre">dispose_browser_context()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.target.expose_dev_tools_protocol"><code class="docutils literal notranslate"><span class="pre">expose_dev_tools_protocol()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.target.get_browser_contexts"><code class="docutils literal notranslate"><span class="pre">get_browser_contexts()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.target.get_target_info"><code class="docutils literal notranslate"><span class="pre">get_target_info()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.target.get_targets"><code class="docutils literal notranslate"><span class="pre">get_targets()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.target.send_message_to_target"><code class="docutils literal notranslate"><span class="pre">send_message_to_target()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.target.set_auto_attach"><code class="docutils literal notranslate"><span class="pre">set_auto_attach()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.target.set_discover_targets"><code class="docutils literal notranslate"><span class="pre">set_discover_targets()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.target.set_remote_locations"><code class="docutils literal notranslate"><span class="pre">set_remote_locations()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#events">Events</a><ul>
<li><a class="reference internal" href="#nodriver.cdp.target.AttachedToTarget"><code class="docutils literal notranslate"><span class="pre">AttachedToTarget</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.target.AttachedToTarget.session_id"><code class="docutils literal notranslate"><span class="pre">AttachedToTarget.session_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.target.AttachedToTarget.target_info"><code class="docutils literal notranslate"><span class="pre">AttachedToTarget.target_info</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.target.AttachedToTarget.waiting_for_debugger"><code class="docutils literal notranslate"><span class="pre">AttachedToTarget.waiting_for_debugger</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.target.DetachedFromTarget"><code class="docutils literal notranslate"><span class="pre">DetachedFromTarget</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.target.DetachedFromTarget.session_id"><code class="docutils literal notranslate"><span class="pre">DetachedFromTarget.session_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.target.DetachedFromTarget.target_id"><code class="docutils literal notranslate"><span class="pre">DetachedFromTarget.target_id</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.target.ReceivedMessageFromTarget"><code class="docutils literal notranslate"><span class="pre">ReceivedMessageFromTarget</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.target.ReceivedMessageFromTarget.session_id"><code class="docutils literal notranslate"><span class="pre">ReceivedMessageFromTarget.session_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.target.ReceivedMessageFromTarget.message"><code class="docutils literal notranslate"><span class="pre">ReceivedMessageFromTarget.message</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.target.ReceivedMessageFromTarget.target_id"><code class="docutils literal notranslate"><span class="pre">ReceivedMessageFromTarget.target_id</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.target.TargetCreated"><code class="docutils literal notranslate"><span class="pre">TargetCreated</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.target.TargetCreated.target_info"><code class="docutils literal notranslate"><span class="pre">TargetCreated.target_info</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.target.TargetDestroyed"><code class="docutils literal notranslate"><span class="pre">TargetDestroyed</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.target.TargetDestroyed.target_id"><code class="docutils literal notranslate"><span class="pre">TargetDestroyed.target_id</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.target.TargetCrashed"><code class="docutils literal notranslate"><span class="pre">TargetCrashed</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.target.TargetCrashed.target_id"><code class="docutils literal notranslate"><span class="pre">TargetCrashed.target_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.target.TargetCrashed.status"><code class="docutils literal notranslate"><span class="pre">TargetCrashed.status</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.target.TargetCrashed.error_code"><code class="docutils literal notranslate"><span class="pre">TargetCrashed.error_code</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.target.TargetInfoChanged"><code class="docutils literal notranslate"><span class="pre">TargetInfoChanged</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.target.TargetInfoChanged.target_info"><code class="docutils literal notranslate"><span class="pre">TargetInfoChanged.target_info</span></code></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>

          </div>
        </div>
      </div>
      
      
    </aside>
  </div>
</div><script src="../../_static/documentation_options.js?v=5929fcd5"></script>
    <script src="../../_static/doctools.js?v=888ff710"></script>
    <script src="../../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../../_static/scripts/furo.js?v=32e29ea5"></script>
    </body>
</html>