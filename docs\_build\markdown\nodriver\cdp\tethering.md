# Tethering

The Tethering domain defines methods and events for browser port binding.

*This CDP domain is experimental.*

<a id="module-nodriver.cdp.tethering"></a>
* [Types]()
* [Commands]()
* [Events]()

## Types

*There are no types in this module.*

## Commands

Each command is a generator function. The return
type `Generator[x, y, z]` indicates that the generator
*yields* arguments of type `x`, it must be resumed with
an argument of type `y`, and it returns type `z`. In
this library, types `x` and `y` are the same for all
commands, and `z` is the return type you should pay attention
to. For more information, see
[Getting Started: Commands](../../readme.md#getting-started-commands).

### bind(port)

Request browser port binding.

* **Parameters:**
  **port** ([`int`](https://docs.python.org/3/library/functions.html#int)) – Port number to bind.
* **Return type:**
  [`Generator`](https://docs.python.org/3/library/typing.html#typing.Generator)[[`Dict`](https://docs.python.org/3/library/typing.html#typing.Dict)[[`str`](https://docs.python.org/3/library/stdtypes.html#str), [`Any`](https://docs.python.org/3/library/typing.html#typing.Any)], [`Dict`](https://docs.python.org/3/library/typing.html#typing.Dict)[[`str`](https://docs.python.org/3/library/stdtypes.html#str), [`Any`](https://docs.python.org/3/library/typing.html#typing.Any)], [`None`](https://docs.python.org/3/library/constants.html#None)]

### unbind(port)

Request browser port unbinding.

* **Parameters:**
  **port** ([`int`](https://docs.python.org/3/library/functions.html#int)) – Port number to unbind.
* **Return type:**
  [`Generator`](https://docs.python.org/3/library/typing.html#typing.Generator)[[`Dict`](https://docs.python.org/3/library/typing.html#typing.Dict)[[`str`](https://docs.python.org/3/library/stdtypes.html#str), [`Any`](https://docs.python.org/3/library/typing.html#typing.Any)], [`Dict`](https://docs.python.org/3/library/typing.html#typing.Dict)[[`str`](https://docs.python.org/3/library/stdtypes.html#str), [`Any`](https://docs.python.org/3/library/typing.html#typing.Any)], [`None`](https://docs.python.org/3/library/constants.html#None)]

## Events

Generally, you do not need to instantiate CDP events
yourself. Instead, the API creates events for you and then
you use the event’s attributes.

### *class* Accepted(port, connection_id)

Informs that port was successfully bound and got a specified connection id.

#### port*: [`int`](https://docs.python.org/3/library/functions.html#int)*

Port number that was successfully bound.

#### connection_id*: [`str`](https://docs.python.org/3/library/stdtypes.html#str)*

Connection id to be used.
