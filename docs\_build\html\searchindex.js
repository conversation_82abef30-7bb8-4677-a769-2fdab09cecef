Search.setIndex({"docnames": ["index", "nodriver/cdp", "nodriver/cdp/accessibility", "nodriver/cdp/animation", "nodriver/cdp/audits", "nodriver/cdp/autofill", "nodriver/cdp/background_service", "nodriver/cdp/bluetooth_emulation", "nodriver/cdp/browser", "nodriver/cdp/cache_storage", "nodriver/cdp/cast", "nodriver/cdp/console", "nodriver/cdp/css", "nodriver/cdp/debugger", "nodriver/cdp/device_access", "nodriver/cdp/device_orientation", "nodriver/cdp/dom", "nodriver/cdp/dom_debugger", "nodriver/cdp/dom_snapshot", "nodriver/cdp/dom_storage", "nodriver/cdp/emulation", "nodriver/cdp/event_breakpoints", "nodriver/cdp/extensions", "nodriver/cdp/fed_cm", "nodriver/cdp/fetch", "nodriver/cdp/file_system", "nodriver/cdp/headless_experimental", "nodriver/cdp/heap_profiler", "nodriver/cdp/indexed_db", "nodriver/cdp/input_", "nodriver/cdp/inspector", "nodriver/cdp/io", "nodriver/cdp/layer_tree", "nodriver/cdp/log", "nodriver/cdp/media", "nodriver/cdp/memory", "nodriver/cdp/network", "nodriver/cdp/overlay", "nodriver/cdp/page", "nodriver/cdp/performance", "nodriver/cdp/performance_timeline", "nodriver/cdp/preload", "nodriver/cdp/profiler", "nodriver/cdp/pwa", "nodriver/cdp/runtime", "nodriver/cdp/schema", "nodriver/cdp/security", "nodriver/cdp/service_worker", "nodriver/cdp/storage", "nodriver/cdp/system_info", "nodriver/cdp/target", "nodriver/cdp/tethering", "nodriver/cdp/tracing", "nodriver/cdp/web_audio", "nodriver/cdp/web_authn", "nodriver/classes/browser", "nodriver/classes/element", "nodriver/classes/others_and_helpers", "nodriver/classes/tab", "nodriver/quickstart", "readme", "style"], "filenames": ["index.rst", "nodriver/cdp.rst", "nodriver/cdp/accessibility.rst", "nodriver/cdp/animation.rst", "nodriver/cdp/audits.rst", "nodriver/cdp/autofill.rst", "nodriver/cdp/background_service.rst", "nodriver/cdp/bluetooth_emulation.rst", "nodriver/cdp/browser.rst", "nodriver/cdp/cache_storage.rst", "nodriver/cdp/cast.rst", "nodriver/cdp/console.rst", "nodriver/cdp/css.rst", "nodriver/cdp/debugger.rst", "nodriver/cdp/device_access.rst", "nodriver/cdp/device_orientation.rst", "nodriver/cdp/dom.rst", "nodriver/cdp/dom_debugger.rst", "nodriver/cdp/dom_snapshot.rst", "nodriver/cdp/dom_storage.rst", "nodriver/cdp/emulation.rst", "nodriver/cdp/event_breakpoints.rst", "nodriver/cdp/extensions.rst", "nodriver/cdp/fed_cm.rst", "nodriver/cdp/fetch.rst", "nodriver/cdp/file_system.rst", "nodriver/cdp/headless_experimental.rst", "nodriver/cdp/heap_profiler.rst", "nodriver/cdp/indexed_db.rst", "nodriver/cdp/input_.rst", "nodriver/cdp/inspector.rst", "nodriver/cdp/io.rst", "nodriver/cdp/layer_tree.rst", "nodriver/cdp/log.rst", "nodriver/cdp/media.rst", "nodriver/cdp/memory.rst", "nodriver/cdp/network.rst", "nodriver/cdp/overlay.rst", "nodriver/cdp/page.rst", "nodriver/cdp/performance.rst", "nodriver/cdp/performance_timeline.rst", "nodriver/cdp/preload.rst", "nodriver/cdp/profiler.rst", "nodriver/cdp/pwa.rst", "nodriver/cdp/runtime.rst", "nodriver/cdp/schema.rst", "nodriver/cdp/security.rst", "nodriver/cdp/service_worker.rst", "nodriver/cdp/storage.rst", "nodriver/cdp/system_info.rst", "nodriver/cdp/target.rst", "nodriver/cdp/tethering.rst", "nodriver/cdp/tracing.rst", "nodriver/cdp/web_audio.rst", "nodriver/cdp/web_authn.rst", "nodriver/classes/browser.rst", "nodriver/classes/element.rst", "nodriver/classes/others_and_helpers.rst", "nodriver/classes/tab.rst", "nodriver/quickstart.rst", "readme.rst", "style.rst"], "titles": ["NODRIVER", "CDP object", "Accessibility", "Animation", "Audits", "Autofill", "BackgroundService", "BluetoothEmulation", "Browser", "CacheStorage", "Cast", "Console", "CSS", "Debugger", "DeviceAccess", "DeviceOrientation", "DOM", "DOMDebugger", "DOMSnapshot", "DOMStorage", "Emulation", "EventBreakpoints", "Extensions", "FedCm", "Fetch", "FileSystem", "HeadlessExperimental", "HeapProfiler", "IndexedDB", "Input", "Inspector", "IO", "LayerTree", "Log", "Media", "Memory", "Network", "Overlay", "Page", "Performance", "PerformanceTimeline", "Preload", "Profiler", "PWA", "Runtime", "Schema", "Security", "ServiceWorker", "Storage", "SystemInfo", "Target", "Tethering", "Tracing", "WebAudio", "WebAuthn", "Browser class", "Element class", "Other classes and Helper classes", "Tab class", "Quickstart guide", "NODRIVER", "TITLE"], "terms": {"thi": [0, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60], "packag": [0, 57, 58, 59, 60], "provid": [0, 2, 4, 12, 16, 17, 18, 20, 22, 24, 26, 32, 33, 36, 37, 38, 43, 44, 48, 49, 50, 52, 56, 57, 58, 60], "next": [0, 12, 13, 20, 23, 28, 44, 58, 59, 60], "level": [0, 2, 4, 11, 16, 33, 34, 35, 36, 44, 46, 49, 52, 55, 58, 59, 60], "webscrap": [0, 60], "browser": [0, 1, 2, 10, 12, 20, 22, 23, 24, 34, 35, 36, 38, 43, 44, 48, 49, 50, 51, 56, 58, 59, 60], "autom": [0, 8, 16, 20, 38, 54, 60], "us": [0, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 16, 17, 18, 19, 20, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 59, 60], "rel": [0, 16, 20, 29, 36, 38, 42, 48, 56, 58, 60], "simpl": [0, 12, 42, 58, 59, 60], "interfac": [0, 17, 41, 54, 58, 60], "i": [0, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59], "offici": [0, 60], "successor": [0, 60], "undetect": [0, 59, 60], "chromedriv": [0, 59, 60], "python": [0, 58, 60], "No": [0, 8, 46, 60], "more": [0, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60], "webdriv": [0, 60], "selenium": [0, 55, 58, 60], "direct": [0, 3, 4, 12, 16, 20, 38, 60], "commun": [0, 50, 60], "even": [0, 20, 23, 38, 44, 46, 55, 57, 60], "better": [0, 36, 60], "resist": [0, 60], "against": [0, 16, 50, 60], "web": [0, 2, 6, 7, 10, 12, 20, 25, 36, 38, 40, 43, 53, 60], "applicatinon": [0, 60], "firewal": [0, 60], "waf": [0, 60], "": [0, 2, 3, 4, 5, 6, 7, 8, 10, 11, 12, 13, 14, 16, 17, 18, 19, 20, 23, 24, 27, 29, 30, 32, 33, 34, 36, 37, 38, 39, 40, 41, 42, 43, 44, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60], "while": [0, 13, 27, 29, 31, 32, 36, 38, 39, 44, 59, 60], "perform": [0, 1, 2, 8, 13, 16, 29, 40, 44, 58, 60], "get": [0, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60], "massiv": [0, 60], "boost": [0, 60], "modul": [0, 2, 4, 5, 6, 7, 8, 9, 12, 13, 15, 16, 17, 18, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 35, 36, 37, 38, 41, 43, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 58, 60], "contrari": [0, 60], "fulli": [0, 55, 60], "asynchron": [0, 44, 55, 60], "what": [0, 2, 20, 23, 24, 27, 34, 36, 48, 59], "make": [0, 8, 13, 24, 36, 38, 44, 50, 55, 56, 58, 60], "differ": [0, 4, 8, 12, 20, 24, 34, 36, 37, 38, 41, 44, 48, 55, 58, 59, 60], "from": [0, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 52, 53, 54, 55, 56, 57, 58, 59, 60], "other": [0, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 16, 17, 18, 19, 20, 22, 23, 24, 25, 26, 27, 28, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 52, 53, 54, 55, 56, 59, 60], "known": [0, 13, 16, 33, 36, 49, 52, 55, 58, 60], "optim": [0, 26, 38, 42, 60], "stai": [0, 13, 53, 60], "most": [0, 9, 13, 16, 27, 28, 36, 38, 44, 58, 60], "anti": [0, 60], "bot": [0, 58, 60], "solut": [0, 4, 38, 60], "anoth": [0, 12, 13, 24, 31, 36, 44, 56, 58, 60], "focu": [0, 16, 20, 38, 56, 59, 60], "point": [0, 13, 16, 18, 20, 29, 32, 35, 36, 42, 43, 55, 56, 57, 58, 60], "usabl": [0, 60], "prototyp": [0, 44, 60], "so": [0, 4, 5, 11, 12, 13, 20, 23, 33, 36, 44, 55, 56, 58, 59, 60], "expect": [0, 8, 13, 24, 35, 36, 38, 44, 59, 60], "lot": [0, 55, 56, 58, 60], "work": [0, 12, 16, 20, 37, 56, 57, 58, 59, 60], "method": [0, 2, 8, 9, 12, 16, 24, 29, 31, 36, 38, 39, 40, 44, 48, 49, 50, 51, 55, 56, 57, 60], "paramet": [0, 2, 3, 4, 5, 6, 7, 8, 9, 10, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 31, 32, 33, 35, 36, 37, 38, 39, 40, 42, 43, 44, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 60], "have": [0, 2, 4, 8, 12, 13, 16, 17, 18, 20, 24, 29, 34, 36, 42, 43, 44, 46, 48, 50, 52, 54, 56, 58, 60], "best": [0, 20, 58, 60], "practic": [0, 60], "default": [0, 2, 4, 8, 9, 12, 13, 16, 17, 18, 20, 24, 25, 26, 27, 28, 29, 32, 36, 37, 38, 43, 44, 46, 48, 50, 52, 54, 56, 57, 58, 60], "1": [0, 2, 4, 5, 6, 7, 8, 9, 11, 12, 13, 16, 17, 18, 20, 22, 23, 24, 26, 29, 32, 35, 36, 37, 38, 39, 41, 42, 43, 46, 47, 48, 49, 50, 52, 53, 54, 55, 56, 58, 59, 60], "2": [0, 5, 7, 18, 20, 29, 36, 46, 49, 56, 57, 58, 59, 60], "line": [0, 4, 5, 8, 11, 12, 13, 16, 17, 27, 33, 34, 36, 37, 38, 42, 44, 49, 59, 60], "up": [0, 2, 12, 20, 23, 29, 36, 44, 54, 58, 59, 60], "run": [0, 4, 13, 20, 26, 32, 35, 36, 38, 39, 41, 42, 44, 47, 48, 49, 50, 53, 55, 58, 59, 60], "config": [0, 33, 37, 44, 55, 59, 60], "conveni": [0, 55, 58, 60], "import": [0, 4, 12, 16, 18, 36, 55, 58, 59, 60], "It": [0, 4, 13, 16, 27, 36, 38, 41, 44, 57, 58, 60], "also": [0, 4, 8, 10, 12, 13, 16, 18, 20, 26, 27, 36, 37, 38, 40, 41, 43, 44, 48, 50, 56, 57, 58, 60], "easi": [0, 38, 60], "customiz": [0, 60], "everyth": [0, 50, 60], "entir": [0, 2, 12, 16, 17, 36, 38, 56, 58, 60], "arrai": [0, 5, 7, 9, 12, 13, 16, 17, 18, 25, 27, 28, 32, 35, 36, 37, 38, 42, 44, 46, 48, 49, 50, 60], "domain": [0, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 30, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 43, 44, 45, 47, 48, 49, 51, 53, 54, 58, 60], "event": [0, 55, 56, 58, 59, 60], "avail": [0, 8, 10, 12, 13, 16, 18, 20, 22, 24, 36, 38, 40, 44, 48, 49, 50, 54, 57, 58, 60], "A": [0, 2, 3, 4, 5, 6, 8, 9, 10, 12, 13, 14, 16, 18, 20, 24, 26, 27, 28, 29, 31, 32, 34, 35, 36, 37, 38, 41, 42, 43, 44, 46, 48, 49, 50, 52, 53, 59, 60, 61], "blaze": [0, 60], "fast": [0, 20, 60], "chrome": [0, 2, 5, 8, 26, 36, 38, 41, 43, 44, 52, 55, 58, 60], "ish": [0, 60], "librari": [0, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 60], "binari": [0, 36, 60], "depend": [0, 4, 12, 16, 29, 36, 38, 40, 48, 49, 50, 53, 60], "equal": [0, 17, 18, 29, 48, 60], "bizarr": [0, 60], "increas": [0, 12, 29, 36, 40, 42, 60], "less": [0, 34, 36, 60], "detect": [0, 35, 37, 38, 55, 58, 60], "code": [0, 4, 5, 7, 9, 13, 17, 21, 24, 29, 34, 36, 42, 44, 46, 49, 50, 59, 60], "fresh": [0, 36, 60], "profil": [0, 1, 5, 22, 27, 32, 35, 48, 49, 50, 59, 60], "each": [0, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 58, 59, 60], "clean": [0, 59, 60], "exit": [0, 55, 60], "save": [0, 5, 8, 38, 52, 55, 56, 58, 60], "load": [0, 2, 4, 12, 22, 24, 35, 36, 38, 41, 43, 46, 48, 55, 57, 58, 60], "cooki": [0, 4, 36, 38, 48, 59, 60], "file": [0, 8, 16, 22, 25, 29, 31, 34, 36, 38, 43, 56, 57, 58, 60], "repeat": [0, 29, 38, 60], "tediou": [0, 60], "login": [0, 58, 60], "step": [0, 13, 23, 32, 37, 56, 58, 60], "smart": [0, 38, 60], "element": [0, 2, 3, 4, 12, 16, 18, 20, 32, 34, 36, 37, 38, 40, 41, 44, 49, 58, 60], "lookup": [0, 36, 44, 57, 60], "selector": [0, 12, 16, 37, 56, 60], "text": [0, 2, 5, 9, 10, 11, 12, 13, 16, 18, 20, 24, 29, 33, 36, 38, 41, 44, 46, 56, 59, 60], "includ": [0, 2, 4, 12, 16, 18, 20, 27, 29, 36, 37, 38, 41, 44, 48, 50, 52, 56, 58, 60], "ifram": [0, 4, 16, 17, 18, 36, 38, 55, 58, 60], "content": [0, 2, 4, 9, 12, 13, 16, 18, 20, 24, 32, 36, 37, 38, 44, 46, 48, 50, 56, 58, 60], "could": [0, 4, 13, 36, 38, 56, 57, 58, 59, 60], "wait": [0, 36, 38, 43, 44, 55, 56, 58, 59, 60], "condit": [0, 12, 13, 16, 31, 36, 55, 58, 60], "appear": [0, 7, 14, 16, 20, 36, 37, 58, 59, 60], "sinc": [0, 8, 9, 12, 13, 16, 17, 18, 20, 26, 27, 35, 36, 37, 38, 39, 40, 42, 44, 46, 49, 50, 55, 58, 59, 60], "retri": [0, 36, 58, 60], "durat": [0, 3, 29, 32, 39, 40, 48, 53, 56, 58, 60], "timeout": [0, 13, 38, 44, 58, 60], "until": [0, 2, 12, 13, 24, 36, 38, 48, 58, 60], "found": [0, 4, 10, 12, 16, 36, 57, 58, 59, 60], "singl": [0, 2, 12, 13, 20, 24, 27, 36, 38, 41, 48, 49, 54, 58, 60], "tab": [0, 10, 38, 43, 50, 55, 56, 59, 60], "find": [0, 2, 4, 12, 16, 59, 60], "accept": [0, 12, 36, 38, 43, 51, 56, 58, 59, 60], "best_match": [0, 59, 60], "flag": [0, 4, 12, 17, 20, 22, 27, 28, 36, 38, 43, 44, 54, 58, 60], "which": [0, 2, 4, 8, 12, 13, 16, 17, 18, 19, 20, 24, 27, 29, 32, 34, 36, 38, 40, 41, 43, 44, 47, 48, 50, 55, 56, 57, 58, 59, 60], "naiv": [0, 58, 60], "return": [0, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 60], "first": [0, 12, 13, 16, 20, 27, 32, 36, 41, 42, 48, 50, 55, 57, 58, 60], "match": [0, 2, 12, 13, 16, 24, 36, 38, 44, 46, 48, 50, 54, 58, 60], "candid": [0, 29, 48, 60], "closest": [0, 16, 58, 60], "length": [0, 3, 12, 13, 18, 20, 36, 48, 58, 59, 60], "descript": [0, 2, 6, 7, 12, 13, 16, 36, 38, 43, 44, 45, 46, 49, 60], "__repr__": [0, 60], "repres": [0, 3, 4, 5, 12, 13, 16, 18, 19, 24, 29, 34, 36, 37, 41, 43, 44, 46, 48, 49, 55, 56, 58, 60], "html": [0, 5, 12, 16, 29, 36, 38, 41, 54, 56, 58, 60], "util": [0, 55, 58, 60], "function": [0, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 58, 60], "convert": [0, 16, 48, 57, 58, 60], "undetected_chromedriv": [0, 60], "instanc": [0, 3, 5, 44, 55, 60], "contintu": [0, 60], "pack": [0, 60], "helper": [0, 60], "oper": [0, 4, 6, 7, 12, 13, 16, 17, 21, 29, 31, 36, 48, 50, 58, 60], "quickstart": 0, "guid": [0, 8, 38, 52], "instal": [0, 22, 38, 43, 47], "usag": [0, 4, 12, 27, 38, 44, 48, 49, 56], "exampl": [0, 4, 5, 12, 26, 29, 38, 41, 42, 44, 49, 50, 56], "complet": [0, 2, 13, 16, 18, 26, 36, 38], "custom": [0, 8, 12, 13, 38], "option": [0, 2, 3, 4, 5, 7, 8, 9, 10, 11, 12, 13, 16, 17, 18, 19, 20, 22, 23, 24, 25, 26, 27, 28, 29, 31, 32, 33, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 46, 47, 48, 49, 50, 52, 53, 54, 55, 56, 58], "altern": [0, 2, 24, 36, 44], "proxi": [0, 36, 50, 55], "socks5": [0, 55], "authent": [0, 4, 24, 36, 54, 55], "too": [0, 36, 50, 57], "concret": [0, 4, 60], "class": [0, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 16, 17, 18, 19, 20, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], "creat": [0, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 16, 17, 18, 19, 20, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 57, 58, 59, 60], "target": [0, 1, 3, 4, 8, 12, 16, 20, 26, 30, 36, 37, 38, 40, 41, 43, 44, 46, 48, 55, 56, 58], "connect": [0, 7, 22, 30, 36, 44, 46, 51, 53, 55, 58, 60], "websocket_url": [0, 55, 58], "main_tab": [0, 55], "stop": [0, 10, 12, 13, 17, 21, 27, 33, 35, 38, 41, 42, 47, 52, 55, 56, 59, 60], "sleep": [0, 55, 58, 59, 60], "create_context": [0, 55, 59], "grant_all_permiss": [0, 55], "tile_window": [0, 55], "update_target": [0, 55], "activ": [0, 10, 12, 13, 20, 27, 29, 34, 36, 38, 41, 47, 48, 50, 55, 58], "add_handl": [0, 58, 60], "attach": [0, 18, 34, 36, 38, 43, 44, 50, 58], "back": [0, 4, 13, 29, 31, 32, 38, 44, 58], "bring_to_front": [0, 38, 58, 59, 60], "bypass_insecure_connection_warn": [0, 58, 60], "close": [0, 8, 16, 23, 31, 36, 38, 43, 50, 53, 55, 58, 59, 60], "disconnect": [0, 36, 50, 53, 55, 58], "download_fil": [0, 58], "evalu": [0, 12, 13, 27, 36, 38, 44, 58], "feed_cdp": [0, 58], "find_al": [0, 58, 60], "find_element_by_text": [0, 58], "find_elements_by_text": [0, 56, 58], "flash_point": [0, 58], "forward": [0, 20, 29, 36, 38, 55, 58], "fullscreen": [0, 8, 38, 50, 58], "get_all_linked_sourc": [0, 58], "get_all_url": [0, 58], "get_cont": [0, 58, 59, 60], "get_frame_resource_tre": [0, 58], "get_frame_resource_url": [0, 58], "get_frame_tre": [0, 38, 58], "get_local_storag": [0, 58, 60], "get_window": [0, 58], "inspector_open": [0, 58], "inspector_url": [0, 58], "js_dump": [0, 58], "maxim": [0, 8, 36, 50, 58], "medim": [0, 58], "minim": [0, 8, 27, 38, 50, 58], "mouse_click": [0, 56, 58, 59, 60], "mouse_drag": [0, 56, 58], "mouse_mov": [0, 56, 58], "open_external_inspector": [0, 58], "query_selector": [0, 16, 56, 58], "query_selector_al": [0, 16, 56, 58], "reload": [0, 13, 30, 36, 38, 44, 58, 59, 60], "remove_handl": [0, 58], "save_screenshot": [0, 56, 58, 59, 60], "scroll_bottom_reach": [0, 58], "scroll_down": [0, 58, 59, 60], "scroll_up": [0, 58], "search_frame_resourc": [0, 58], "select": [0, 2, 4, 14, 16, 18, 29, 34, 37, 38, 56, 59, 60], "select_al": [0, 59, 60], "send": [0, 4, 11, 16, 26, 27, 29, 33, 34, 36, 38, 41, 42, 48, 50, 53, 56, 59, 60], "set_download_path": [0, 58], "set_local_storag": [0, 58, 60], "set_window_s": [0, 58], "set_window_st": [0, 58], "template_loc": 0, "verify_cf": 0, "wait_for": [0, 58], "websocket": [0, 36, 38, 58], "xpath": [0, 16, 58, 60], "tag": [0, 12, 13, 18, 20, 38, 41, 47, 52, 56, 58], "tag_nam": [0, 56], "node_id": [0, 2, 4, 12, 16, 17, 27, 37, 40, 41, 53, 56], "backend_node_id": [0, 2, 3, 16, 17, 18, 32, 37, 38, 41, 56], "node_typ": [0, 16, 18, 53, 56], "node_nam": [0, 16, 18, 56], "local_nam": [0, 16, 56], "node_valu": [0, 16, 18, 56], "parent_id": [0, 2, 16, 38, 44, 56], "child_node_count": [0, 16, 56], "attribut": [0, 2, 3, 4, 5, 6, 7, 8, 10, 11, 12, 13, 14, 16, 17, 18, 19, 20, 23, 24, 27, 29, 30, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 44, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58], "document_url": [0, 16, 18, 36, 56], "base_url": [0, 16, 18, 29, 56], "public_id": [0, 16, 18, 56], "system_id": [0, 16, 18, 56], "internal_subset": [0, 16, 56], "xml_version": [0, 16, 56], "valu": [0, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 16, 17, 18, 19, 20, 22, 23, 24, 25, 26, 27, 28, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 52, 53, 54, 56, 57, 58], "pseudo_typ": [0, 12, 16, 18, 56], "pseudo_identifi": [0, 12, 16, 18, 56], "shadow_root_typ": [0, 16, 18, 56], "frame_id": [0, 2, 4, 5, 8, 12, 16, 18, 24, 36, 37, 38, 40, 48, 56], "content_docu": [0, 16, 56], "shadow_root": [0, 16, 56], "template_cont": [0, 16, 56], "pseudo_el": [0, 12, 16, 56], "imported_docu": [0, 16, 56], "distributed_nod": [0, 16, 56], "is_svg": [0, 16, 56], "compatibility_mod": [0, 16, 56], "assigned_slot": [0, 16, 56], "shadow_children": [0, 56], "save_to_dom": [0, 56], "remove_from_dom": [0, 56], "updat": [0, 2, 3, 4, 6, 12, 16, 20, 26, 27, 36, 38, 41, 42, 44, 48, 54, 56, 57, 58], "node": [0, 2, 3, 5, 12, 16, 17, 18, 27, 32, 35, 37, 38, 41, 42, 53, 56, 58], "tree": [0, 2, 12, 16, 18, 27, 32, 38, 48, 56, 58], "attr": [0, 12, 16, 56, 60], "parent": [0, 2, 12, 16, 18, 32, 38, 44, 55, 56, 60], "children": [0, 2, 12, 16, 17, 27, 37, 38, 42, 56, 60], "remote_object": [0, 56], "object_id": [0, 2, 13, 16, 17, 27, 31, 37, 44, 56], "get_js_attribut": [0, 56], "appli": [0, 2, 4, 8, 12, 13, 20, 24, 32, 38, 52, 56, 60], "get_posit": [0, 56], "click_mous": [0, 56], "scroll_into_view": [0, 56], "clear_input": [0, 56], "send_kei": [0, 56, 59, 60], "send_fil": [0, 56], "select_opt": [0, 56], "set_valu": [0, 56], "set_text": [0, 56], "get_html": [0, 56], "text_al": [0, 56, 60], "flash": [0, 56, 58, 59, 60], "highlight_overlai": [0, 56], "record_video": [0, 56, 60], "is_record": [0, 6, 56], "browser_arg": [0, 55, 57, 59], "user_data_dir": [0, 55, 57, 59], "uses_custom_data_dir": [0, 57], "add_extens": [0, 57], "add_argu": [0, 57], "contradict": 0, "clear": [0, 4, 6, 15, 19, 20, 22, 28, 33, 36, 38, 44, 48, 50, 54, 56, 57], "copi": [0, 16, 29, 57], "fromkei": [0, 57], "item": [0, 4, 8, 9, 12, 13, 16, 18, 19, 24, 25, 26, 28, 29, 31, 32, 35, 36, 37, 38, 42, 43, 44, 48, 49, 52, 57, 58], "kei": [0, 6, 7, 8, 9, 12, 19, 22, 25, 28, 29, 36, 38, 41, 43, 44, 46, 48, 54, 56, 57, 58, 59, 60], "pop": [0, 16, 57], "popitem": [0, 57], "setdefault": [0, 57], "cdict": [0, 57], "access": [0, 1, 4, 16, 27, 33, 36, 37, 38, 48, 50, 55, 57], "type": [0, 55, 56, 57, 58, 59, 60], "axnodeid": [0, 2], "axvaluetyp": [0, 2], "boolean": [0, 2, 12, 13, 44], "tristat": [0, 2], "boolean_or_undefin": [0, 2], "idref": [0, 2], "idref_list": [0, 2], "integ": [0, 2, 16, 17, 27, 28, 36, 44, 48], "node_list": [0, 2], "number": [0, 2, 5, 8, 9, 11, 12, 13, 16, 17, 18, 20, 27, 28, 29, 31, 32, 33, 34, 35, 36, 37, 38, 40, 42, 44, 48, 51, 52, 53, 58, 59, 60], "string": [0, 2, 4, 7, 8, 9, 12, 13, 16, 18, 20, 24, 26, 28, 29, 32, 35, 36, 38, 44, 46, 48, 49, 50, 52, 54, 55, 56, 58, 59, 60], "computed_str": [0, 2], "token": [0, 2, 36, 38, 48], "token_list": [0, 2], "dom_rel": [0, 2], "role": [0, 2, 48, 59, 60], "internal_rol": [0, 2], "value_undefin": [0, 2], "axvaluesourcetyp": [0, 2], "implicit": [0, 2, 12, 16], "style": [0, 2, 3, 12, 16, 18, 20, 34, 37, 38, 56], "placehold": [0, 2, 16], "related_el": [0, 2], "axvaluenativesourcetyp": [0, 2], "figcapt": [0, 2], "label": [0, 2, 36, 37, 38, 44], "labelfor": [0, 2], "labelwrap": [0, 2], "legend": [0, 2], "rubyannot": [0, 2], "tablecapt": [0, 2], "titl": [0, 2, 12, 18, 23, 29, 37, 38, 39, 42, 46, 50, 59, 60], "axvaluesourc": [0, 2], "type_": [0, 2, 3, 4, 7, 12, 13, 17, 20, 25, 28, 29, 32, 36, 38, 40, 44, 48, 49, 50], "attribute_valu": [0, 2], "supersed": [0, 2, 20], "native_sourc": [0, 2], "native_source_valu": [0, 2], "invalid": [0, 2, 13, 16, 36, 58, 60], "invalid_reason": [0, 2], "axrelatednod": [0, 2], "backend_dom_node_id": [0, 2], "axproperti": [0, 2], "name": [0, 2, 3, 4, 5, 6, 7, 8, 9, 10, 12, 13, 14, 16, 17, 18, 20, 21, 22, 23, 24, 25, 27, 28, 29, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 52, 53, 54, 57, 58, 59, 60], "axvalu": [0, 2], "related_nod": [0, 2], "sourc": [0, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], "axpropertynam": [0, 2], "action": [0, 2, 8, 16, 23, 38, 41, 43, 46, 56, 57, 58], "busi": [0, 2], "disabl": [0, 2, 3, 4, 5, 6, 7, 10, 11, 12, 13, 14, 16, 18, 19, 20, 21, 23, 24, 26, 27, 28, 30, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 44, 46, 47, 48, 53, 54, 60], "edit": [0, 2, 12, 13, 16, 29], "focus": [0, 2, 16, 20, 50], "hidden": [0, 2, 20, 38, 56, 58], "hidden_root": [0, 2], "keyshortcut": [0, 2], "settabl": [0, 2], "roledescript": [0, 2], "live": [0, 2, 13], "atom": [0, 2], "relev": [0, 2, 17, 37, 38, 41], "root": [0, 2, 12, 16, 17, 18, 27, 32, 34, 38, 42, 48, 50, 55, 58, 60], "autocomplet": [0, 2, 5, 57], "has_popup": [0, 2], "multiselect": [0, 2], "orient": [0, 2, 3, 15, 16, 20, 38], "multilin": [0, 2], "readonli": [0, 2], "requir": [0, 2, 8, 12, 16, 26, 28, 36, 38, 43, 50, 56, 60], "valuemin": [0, 2], "valuemax": [0, 2], "valuetext": [0, 2], "check": [0, 2, 4, 13, 18, 43, 58], "expand": [0, 2], "modal": [0, 2], "press": [0, 2, 29, 38, 56, 58], "activedescend": [0, 2], "control": [0, 2, 8, 29, 36, 37, 38, 43, 44, 50, 52, 55, 58], "describedbi": [0, 2], "detail": [0, 2, 4, 8, 13, 16, 18, 24, 27, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 58], "errormessag": [0, 2, 41], "flowto": [0, 2], "labelledbi": [0, 2], "own": [0, 2, 16, 18, 29, 32, 42, 44], "url": [0, 2, 4, 8, 9, 11, 12, 13, 16, 17, 18, 23, 24, 29, 32, 33, 36, 38, 40, 41, 42, 43, 44, 46, 48, 50, 55, 58, 59, 60], "axnod": [0, 2], "ignor": [0, 2, 4, 12, 13, 16, 18, 29, 36, 38, 43, 46, 50, 52, 54, 55, 58], "ignored_reason": [0, 2], "chrome_rol": [0, 2], "properti": [0, 2, 4, 12, 13, 16, 18, 34, 38, 41, 44, 53, 54, 55, 56, 57, 58], "child_id": [0, 2], "command": [0, 56], "enabl": [0, 2, 3, 4, 5, 6, 7, 8, 10, 11, 12, 13, 14, 16, 17, 18, 19, 20, 22, 23, 24, 26, 27, 28, 29, 30, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 46, 47, 48, 50, 52, 53, 54, 56], "get_ax_node_and_ancestor": [0, 2], "get_child_ax_nod": [0, 2], "get_full_ax_tre": [0, 2], "get_partial_ax_tre": [0, 2], "get_root_ax_nod": [0, 2], "query_ax_tre": [0, 2], "loadcomplet": [0, 2], "nodesupd": [0, 2], "anim": [0, 1, 12, 26], "id_": [0, 2, 3, 5, 10, 14, 22, 27, 29, 36, 38, 41, 42, 44, 48, 49], "paused_st": [0, 3], "play_stat": [0, 3], "playback_r": [0, 3], "start_tim": [0, 3, 42], "current_tim": [0, 3, 53], "css_id": [0, 3], "view_or_scroll_timelin": [0, 3], "vieworscrolltimelin": [0, 3], "axi": [0, 3, 12, 29, 37], "source_node_id": [0, 3], "start_offset": [0, 3, 12, 42], "end_offset": [0, 3, 12, 42], "subject_node_id": [0, 3], "animationeffect": [0, 3], "delai": [0, 3, 20, 23, 29, 38, 52], "end_delai": [0, 3], "iteration_start": [0, 3], "iter": [0, 3, 16, 18, 19, 32, 50, 57], "fill": [0, 3, 5, 20, 37, 46, 59, 60], "eas": [0, 3], "keyframes_rul": [0, 3], "keyframesrul": [0, 3], "keyfram": [0, 3, 12], "keyframestyl": [0, 3], "offset": [0, 3, 8, 12, 13, 18, 20, 31, 32, 38, 42, 58], "get_current_tim": [0, 3], "get_playback_r": [0, 3], "release_anim": [0, 3], "resolve_anim": [0, 3], "seek_anim": [0, 3], "set_paus": [0, 3], "set_playback_r": [0, 3], "set_tim": [0, 3], "animationcancel": [0, 3], "animationcr": [0, 3], "animationstart": [0, 3], "animationupd": [0, 3], "audit": [0, 1, 29], "affectedcooki": [0, 4], "path": [0, 4, 8, 9, 16, 22, 25, 28, 36, 38, 56, 57, 58, 59, 60], "affectedrequest": [0, 4], "request_id": [0, 4, 24, 36, 41, 48], "affectedfram": [0, 4], "cookieexclusionreason": [0, 4], "exclude_same_site_unspecified_treated_as_lax": [0, 4], "exclude_same_site_none_insecur": [0, 4], "exclude_same_site_lax": [0, 4], "exclude_same_site_strict": [0, 4], "exclude_invalid_same_parti": [0, 4], "exclude_same_party_cross_party_context": [0, 4], "exclude_domain_non_ascii": [0, 4], "exclude_third_party_cookie_blocked_in_first_party_set": [0, 4], "exclude_third_party_phaseout": [0, 4], "exclude_port_mismatch": [0, 4], "exclude_scheme_mismatch": [0, 4], "cookiewarningreason": [0, 4], "warn_same_site_unspecified_cross_site_context": [0, 4], "warn_same_site_none_insecur": [0, 4], "warn_same_site_unspecified_lax_allow_unsaf": [0, 4], "warn_same_site_strict_lax_downgrade_strict": [0, 4], "warn_same_site_strict_cross_downgrade_strict": [0, 4], "warn_same_site_strict_cross_downgrade_lax": [0, 4], "warn_same_site_lax_cross_downgrade_strict": [0, 4], "warn_same_site_lax_cross_downgrade_lax": [0, 4], "warn_attribute_value_exceeds_max_s": [0, 4], "warn_domain_non_ascii": [0, 4], "warn_third_party_phaseout": [0, 4], "warn_cross_site_redirect_downgrade_changes_inclus": [0, 4], "warn_deprecation_trial_metadata": [0, 4], "warn_third_party_cookie_heurist": [0, 4], "cookieoper": [0, 4], "set_cooki": [0, 4, 36, 48, 55], "read_cooki": [0, 4], "insighttyp": [0, 4], "git_hub_resourc": [0, 4], "grace_period": [0, 4], "heurist": [0, 4, 5, 36, 38], "cookieissueinsight": [0, 4], "table_entry_url": [0, 4], "cookieissuedetail": [0, 4], "cookie_warning_reason": [0, 4], "cookie_exclusion_reason": [0, 4], "raw_cookie_lin": [0, 4], "site_for_cooki": [0, 4], "cookie_url": [0, 4], "request": [0, 2, 4, 8, 9, 10, 12, 14, 16, 18, 20, 24, 26, 28, 33, 36, 37, 38, 44, 46, 48, 51, 52, 55, 58], "insight": [0, 4], "mixedcontentresolutionstatu": [0, 4], "mixed_content_block": [0, 4], "mixed_content_automatically_upgrad": [0, 4], "mixed_content_warn": [0, 4], "mixedcontentresourcetyp": [0, 4], "attribution_src": [0, 4], "audio": [0, 4, 53, 58], "beacon": [0, 4], "csp_report": [0, 4], "download": [0, 4, 8, 12, 36, 38, 41, 56, 58], "event_sourc": [0, 4, 36], "favicon": [0, 4], "font": [0, 4, 12, 36, 38], "form": [0, 4, 5, 13, 16, 36, 38, 56, 58, 60], "frame": [0, 2, 4, 5, 8, 12, 13, 16, 18, 20, 24, 26, 28, 29, 36, 37, 38, 40, 41, 44, 48, 49, 50, 58], "imag": [0, 4, 8, 12, 16, 20, 26, 32, 36, 38, 40, 49, 56, 58], "json": [0, 4, 7, 8, 9, 13, 24, 26, 32, 36, 38, 41, 44, 50, 52, 54, 56, 57], "manifest": [0, 4, 36, 37, 38, 43, 57], "ping": [0, 4, 36], "plugin_data": [0, 4], "plugin_resourc": [0, 4], "prefetch": [0, 4, 36, 41], "resourc": [0, 4, 8, 11, 12, 13, 17, 20, 24, 33, 36, 38, 41, 46, 48, 55, 58], "script": [0, 4, 12, 13, 17, 18, 20, 36, 38, 41, 42, 44, 47, 48, 56, 58, 59, 60], "service_work": [0, 4, 48], "shared_work": [0, 4, 38], "speculation_rul": [0, 4], "stylesheet": [0, 4, 12, 36], "track": [0, 4, 12, 13, 16, 19, 27, 29, 36, 37, 38, 44, 46, 48], "video": [0, 4, 34, 49, 56, 58], "worker": [0, 4, 6, 13, 33, 35, 36, 38, 44, 50, 58], "xml_http_request": [0, 4], "xslt": [0, 4], "mixedcontentissuedetail": [0, 4], "resolution_statu": [0, 4], "insecure_url": [0, 4], "main_resource_url": [0, 4], "resource_typ": [0, 4, 24, 36], "blockedbyresponsereason": [0, 4], "coep_frame_resource_needs_coep_head": [0, 4, 36], "coop_sandboxed_i_frame_cannot_navigate_to_coop_pag": [0, 4], "corp_not_same_origin": [0, 4, 36], "corp_not_same_origin_after_defaulted_to_same_origin_by_coep": [0, 4, 36], "corp_not_same_origin_after_defaulted_to_same_origin_by_dip": [0, 4, 36], "corp_not_same_origin_after_defaulted_to_same_origin_by_coep_and_dip": [0, 4, 36], "corp_not_same_sit": [0, 4, 36], "sri_message_signature_mismatch": [0, 4, 36], "blockedbyresponseissuedetail": [0, 4], "reason": [0, 2, 4, 13, 24, 30, 32, 36, 38, 41, 42, 44, 50, 56], "parent_fram": [0, 4], "blocked_fram": [0, 4], "heavyadresolutionstatu": [0, 4], "heavy_ad_block": [0, 4], "heavy_ad_warn": [0, 4], "heavyadreason": [0, 4], "network_total_limit": [0, 4], "cpu_total_limit": [0, 4], "cpu_peak_limit": [0, 4], "heavyadissuedetail": [0, 4], "resolut": [0, 4, 49], "contentsecuritypolicyviolationtyp": [0, 4], "k_inline_viol": [0, 4], "k_eval_viol": [0, 4], "k_url_viol": [0, 4], "k_sri_viol": [0, 4], "k_trusted_types_sink_viol": [0, 4], "k_trusted_types_policy_viol": [0, 4], "k_wasm_eval_viol": [0, 4], "sourcecodeloc": [0, 4], "line_numb": [0, 4, 13, 17, 33, 36, 38, 44, 47], "column_numb": [0, 4, 13, 17, 36, 38, 44, 47], "script_id": [0, 4, 13, 17, 38, 42, 44], "contentsecuritypolicyissuedetail": [0, 4], "violated_direct": [0, 4], "is_report_onli": [0, 4], "content_security_policy_violation_typ": [0, 4], "blocked_url": [0, 4], "frame_ancestor": [0, 4], "source_code_loc": [0, 4], "violating_node_id": [0, 4], "sharedarraybufferissuetyp": [0, 4], "transfer_issu": [0, 4], "creation_issu": [0, 4], "sharedarraybufferissuedetail": [0, 4], "is_warn": [0, 4], "lowtextcontrastissuedetail": [0, 4], "violating_node_selector": [0, 4], "contrast_ratio": [0, 4], "threshold_aa": [0, 4], "threshold_aaa": [0, 4], "font_siz": [0, 4, 38], "font_weight": [0, 4, 12], "corsissuedetail": [0, 4], "cors_error_statu": [0, 4, 36], "locat": [0, 4, 12, 13, 16, 24, 27, 29, 36, 38, 42, 43, 44, 50, 58], "initiator_origin": [0, 4], "resource_ip_address_spac": [0, 4, 36], "client_security_st": [0, 4, 36], "attributionreportingissuetyp": [0, 4], "permission_policy_dis": [0, 4], "untrustworthy_reporting_origin": [0, 4], "insecure_context": [0, 4], "invalid_head": [0, 4], "invalid_register_trigger_head": [0, 4], "source_and_trigger_head": [0, 4], "source_ignor": [0, 4], "trigger_ignor": [0, 4], "os_source_ignor": [0, 4], "os_trigger_ignor": [0, 4], "invalid_register_os_source_head": [0, 4], "invalid_register_os_trigger_head": [0, 4], "web_and_os_head": [0, 4], "no_web_or_os_support": [0, 4], "navigation_registration_without_transient_user_activ": [0, 4], "invalid_info_head": [0, 4], "no_register_source_head": [0, 4], "no_register_trigger_head": [0, 4], "no_register_os_source_head": [0, 4], "no_register_os_trigger_head": [0, 4], "navigation_registration_unique_scope_already_set": [0, 4], "shareddictionaryerror": [0, 4], "use_error_cross_origin_no_cors_request": [0, 4], "use_error_dictionary_load_failur": [0, 4], "use_error_matching_dictionary_not_us": [0, 4], "use_error_unexpected_content_dictionary_head": [0, 4], "write_error_coss_origin_no_cors_request": [0, 4], "write_error_disallowed_by_set": [0, 4], "write_error_expired_respons": [0, 4], "write_error_feature_dis": [0, 4], "write_error_insufficient_resourc": [0, 4], "write_error_invalid_match_field": [0, 4], "write_error_invalid_structured_head": [0, 4], "write_error_navigation_request": [0, 4], "write_error_no_match_field": [0, 4], "write_error_non_list_match_dest_field": [0, 4], "write_error_non_secure_context": [0, 4], "write_error_non_string_id_field": [0, 4], "write_error_non_string_in_match_dest_list": [0, 4], "write_error_non_string_match_field": [0, 4], "write_error_non_token_type_field": [0, 4], "write_error_request_abort": [0, 4], "write_error_shutting_down": [0, 4], "write_error_too_long_id_field": [0, 4], "write_error_unsupported_typ": [0, 4], "srimessagesignatureerror": [0, 4], "missing_signature_head": [0, 4], "missing_signature_input_head": [0, 4], "invalid_signature_head": [0, 4], "invalid_signature_input_head": [0, 4], "signature_header_value_is_not_byte_sequ": [0, 4], "signature_header_value_is_parameter": [0, 4], "signature_header_value_is_incorrect_length": [0, 4], "signature_input_header_missing_label": [0, 4], "signature_input_header_value_not_inner_list": [0, 4], "signature_input_header_value_missing_compon": [0, 4], "signature_input_header_invalid_component_typ": [0, 4], "signature_input_header_invalid_component_nam": [0, 4], "signature_input_header_invalid_header_component_paramet": [0, 4], "signature_input_header_invalid_derived_component_paramet": [0, 4], "signature_input_header_key_id_length": [0, 4], "signature_input_header_invalid_paramet": [0, 4], "signature_input_header_missing_required_paramet": [0, 4], "validation_failed_signature_expir": [0, 4], "validation_failed_invalid_length": [0, 4], "validation_failed_signature_mismatch": [0, 4], "attributionreportingissuedetail": [0, 4], "violation_typ": [0, 4, 17], "invalid_paramet": [0, 4], "quirksmodeissuedetail": [0, 4], "is_limited_quirks_mod": [0, 4], "document_node_id": [0, 4], "loader_id": [0, 4, 36, 38, 41], "navigatoruseragentissuedetail": [0, 4], "shareddictionaryissuedetail": [0, 4], "shared_dictionary_error": [0, 4], "srimessagesignatureissuedetail": [0, 4], "error": [0, 2, 4, 5, 7, 9, 10, 13, 16, 20, 23, 24, 34, 36, 38, 39, 41, 43, 44, 46, 47, 50, 58], "signature_bas": [0, 4], "genericissueerrortyp": [0, 4], "form_label_for_name_error": [0, 4], "form_duplicate_id_for_input_error": [0, 4], "form_input_with_no_label_error": [0, 4], "form_autocomplete_attribute_empty_error": [0, 4], "form_empty_id_and_name_attributes_for_input_error": [0, 4], "form_aria_labelled_by_to_non_existing_id": [0, 4], "form_input_assigned_autocomplete_value_to_id_or_name_attribute_error": [0, 4], "form_label_has_neither_for_nor_nested_input": [0, 4], "form_label_for_matches_non_existing_id_error": [0, 4], "form_input_has_wrong_but_well_intended_autocomplete_value_error": [0, 4], "response_was_blocked_by_orb": [0, 4], "genericissuedetail": [0, 4], "error_typ": [0, 4, 34, 41, 46], "violating_node_attribut": [0, 4], "deprecationissuedetail": [0, 4], "affected_fram": [0, 4], "bouncetrackingissuedetail": [0, 4], "tracking_sit": [0, 4], "cookiedeprecationmetadataissuedetail": [0, 4], "allowed_sit": [0, 4], "opt_out_percentag": [0, 4], "is_opt_out_top_level": [0, 4], "clienthintissuereason": [0, 4], "meta_tag_allow_list_invalid_origin": [0, 4], "meta_tag_modified_html": [0, 4], "federatedauthrequestissuedetail": [0, 4], "federated_auth_request_issue_reason": [0, 4], "federatedauthrequestissuereason": [0, 4], "should_embargo": [0, 4], "too_many_request": [0, 4], "well_known_http_not_found": [0, 4], "well_known_no_respons": [0, 4], "well_known_invalid_respons": [0, 4], "well_known_list_empti": [0, 4], "well_known_invalid_content_typ": [0, 4], "config_not_in_well_known": [0, 4], "well_known_too_big": [0, 4], "config_http_not_found": [0, 4], "config_no_respons": [0, 4], "config_invalid_respons": [0, 4], "config_invalid_content_typ": [0, 4], "client_metadata_http_not_found": [0, 4], "client_metadata_no_respons": [0, 4], "client_metadata_invalid_respons": [0, 4], "client_metadata_invalid_content_typ": [0, 4], "idp_not_potentially_trustworthi": [0, 4], "disabled_in_set": [0, 4], "disabled_in_flag": [0, 4], "error_fetching_signin": [0, 4], "invalid_signin_respons": [0, 4], "accounts_http_not_found": [0, 4], "accounts_no_respons": [0, 4], "accounts_invalid_respons": [0, 4], "accounts_list_empti": [0, 4], "accounts_invalid_content_typ": [0, 4], "id_token_http_not_found": [0, 4], "id_token_no_respons": [0, 4], "id_token_invalid_respons": [0, 4], "id_token_idp_error_respons": [0, 4], "id_token_cross_site_idp_error_respons": [0, 4], "id_token_invalid_request": [0, 4], "id_token_invalid_content_typ": [0, 4], "error_id_token": [0, 4], "cancel": [0, 3, 4, 8, 14, 24, 29, 36, 37, 38, 41, 44, 46, 50], "rp_page_not_vis": [0, 4], "silent_mediation_failur": [0, 4], "third_party_cookies_block": [0, 4], "not_signed_in_with_idp": [0, 4], "missing_transient_user_activ": [0, 4], "replaced_by_active_mod": [0, 4], "invalid_fields_specifi": [0, 4], "relying_party_origin_is_opaqu": [0, 4], "type_not_match": [0, 4], "ui_dismissed_no_embargo": [0, 4], "cors_error": [0, 4, 36], "federatedauthuserinforequestissuedetail": [0, 4], "federated_auth_user_info_request_issue_reason": [0, 4], "federatedauthuserinforequestissuereason": [0, 4], "not_same_origin": [0, 4], "not_ifram": [0, 4], "not_potentially_trustworthi": [0, 4], "no_api_permiss": [0, 4], "no_account_sharing_permiss": [0, 4], "invalid_config_or_well_known": [0, 4], "invalid_accounts_respons": [0, 4], "no_returning_user_from_fetched_account": [0, 4], "clienthintissuedetail": [0, 4], "client_hint_issue_reason": [0, 4], "failedrequestinfo": [0, 4], "failure_messag": [0, 4], "partitioningbloburlinfo": [0, 4], "blocked_cross_partition_fetch": [0, 4], "enforce_noopener_for_navig": [0, 4], "partitioningbloburlissuedetail": [0, 4], "partitioning_blob_url_info": [0, 4], "selectelementaccessibilityissuereason": [0, 4], "disallowed_select_child": [0, 4], "disallowed_opt_group_child": [0, 4], "non_phrasing_content_option_child": [0, 4], "interactive_content_option_child": [0, 4], "interactive_content_legend_child": [0, 4], "selectelementaccessibilityissuedetail": [0, 4], "select_element_accessibility_issue_reason": [0, 4], "has_disallowed_attribut": [0, 4], "stylesheetloadingissuereason": [0, 4], "late_import_rul": [0, 4], "request_fail": [0, 4], "stylesheetloadingissuedetail": [0, 4], "style_sheet_loading_issue_reason": [0, 4], "failed_request_info": [0, 4], "propertyruleissuereason": [0, 4], "invalid_syntax": [0, 4], "invalid_initial_valu": [0, 4], "invalid_inherit": [0, 4], "invalid_nam": [0, 4], "propertyruleissuedetail": [0, 4], "property_rule_issue_reason": [0, 4], "property_valu": [0, 4], "inspectorissuecod": [0, 4], "cookie_issu": [0, 4], "mixed_content_issu": [0, 4], "blocked_by_response_issu": [0, 4], "heavy_ad_issu": [0, 4], "content_security_policy_issu": [0, 4], "shared_array_buffer_issu": [0, 4], "low_text_contrast_issu": [0, 4], "cors_issu": [0, 4], "attribution_reporting_issu": [0, 4], "quirks_mode_issu": [0, 4], "partitioning_blob_url_issu": [0, 4], "navigator_user_agent_issu": [0, 4], "generic_issu": [0, 4], "deprecation_issu": [0, 4], "client_hint_issu": [0, 4], "federated_auth_request_issu": [0, 4], "bounce_tracking_issu": [0, 4], "cookie_deprecation_metadata_issu": [0, 4], "stylesheet_loading_issu": [0, 4], "federated_auth_user_info_request_issu": [0, 4], "property_rule_issu": [0, 4], "shared_dictionary_issu": [0, 4], "select_element_accessibility_issu": [0, 4], "sri_message_signature_issu": [0, 4], "inspectorissuedetail": [0, 4], "cookie_issue_detail": [0, 4], "mixed_content_issue_detail": [0, 4], "blocked_by_response_issue_detail": [0, 4], "heavy_ad_issue_detail": [0, 4], "content_security_policy_issue_detail": [0, 4], "shared_array_buffer_issue_detail": [0, 4], "low_text_contrast_issue_detail": [0, 4], "cors_issue_detail": [0, 4], "attribution_reporting_issue_detail": [0, 4], "quirks_mode_issue_detail": [0, 4], "partitioning_blob_url_issue_detail": [0, 4], "navigator_user_agent_issue_detail": [0, 4], "generic_issue_detail": [0, 4], "deprecation_issue_detail": [0, 4], "client_hint_issue_detail": [0, 4], "federated_auth_request_issue_detail": [0, 4], "bounce_tracking_issue_detail": [0, 4], "cookie_deprecation_metadata_issue_detail": [0, 4], "stylesheet_loading_issue_detail": [0, 4], "property_rule_issue_detail": [0, 4], "federated_auth_user_info_request_issue_detail": [0, 4], "shared_dictionary_issue_detail": [0, 4], "select_element_accessibility_issue_detail": [0, 4], "sri_message_signature_issue_detail": [0, 4], "issueid": [0, 4], "inspectorissu": [0, 4], "issue_id": [0, 4], "check_contrast": [0, 4], "check_forms_issu": [0, 4], "get_encoded_respons": [0, 4], "issuead": [0, 4], "issu": [0, 4, 10, 11, 12, 13, 24, 29, 33, 36, 38, 44, 46, 48, 50, 52], "autofil": [0, 1, 38], "creditcard": [0, 5], "expiry_month": [0, 5], "expiry_year": [0, 5], "cvc": [0, 5], "addressfield": [0, 5], "field": [0, 4, 5, 8, 12, 24, 29, 36, 38, 40, 44, 46, 50, 53, 56, 58, 59, 60], "address": [0, 5, 7, 35, 36, 38], "addressui": [0, 5], "address_field": [0, 5], "fillingstrategi": [0, 5], "autocomplete_attribut": [0, 5], "autofill_inf": [0, 5], "filledfield": [0, 5], "html_type": [0, 5], "autofill_typ": [0, 5], "filling_strategi": [0, 5], "field_id": [0, 5], "set_address": [0, 5], "trigger": [0, 3, 5, 13, 23, 29, 33, 36, 37, 38, 41, 42, 46, 52, 54, 56], "addressformfil": [0, 5], "filled_field": [0, 5], "address_ui": [0, 5], "backgroundservic": [0, 1], "servicenam": [0, 6], "background_fetch": [0, 6, 8], "background_sync": [0, 6, 8], "push_messag": [0, 6], "notif": [0, 3, 5, 6, 8, 11, 16, 20, 30, 33, 35, 36, 37, 38, 44, 48, 50, 55], "payment_handl": [0, 6, 8], "periodic_background_sync": [0, 6, 8], "eventmetadata": [0, 6], "backgroundserviceev": [0, 6], "timestamp": [0, 6, 12, 20, 25, 26, 27, 29, 33, 34, 36, 38, 39, 42, 44], "origin": [0, 3, 4, 6, 8, 9, 11, 12, 17, 19, 20, 24, 28, 36, 38, 41, 44, 47, 48, 50, 55, 60], "service_worker_registration_id": [0, 6], "servic": [0, 6, 36, 38, 48, 50, 52], "event_nam": [0, 6, 17, 21, 36], "instance_id": [0, 6], "event_metadata": [0, 6], "storage_kei": [0, 6, 9, 19, 25, 28, 48], "clear_ev": [0, 6], "set_record": [0, 6], "start_observ": [0, 6], "stop_observ": [0, 6], "recordingstatechang": [0, 6], "backgroundserviceeventreceiv": [0, 6], "background_service_ev": [0, 6], "bluetoothemul": [0, 1], "centralst": [0, 7], "absent": [0, 7, 8, 12, 16, 24, 32, 36], "powered_off": [0, 7], "powered_on": [0, 7], "gattoperationtyp": [0, 7], "discoveri": [0, 7, 50], "manufacturerdata": [0, 7], "data": [0, 2, 5, 6, 7, 9, 12, 13, 16, 18, 20, 22, 24, 26, 28, 29, 31, 32, 34, 35, 36, 37, 38, 42, 44, 47, 48, 52, 53], "scanrecord": [0, 7], "uuid": [0, 7, 31, 35], "tx_power": [0, 7], "manufacturer_data": [0, 7], "scanentri": [0, 7], "device_address": [0, 7], "rssi": [0, 7], "scan_record": [0, 7], "set_simulated_central_st": [0, 7], "simulate_advertis": [0, 7], "simulate_gatt_operation_respons": [0, 7], "simulate_preconnected_peripher": [0, 7], "gattoperationreceiv": [0, 7], "browsercontextid": [0, 8, 48, 50], "windowid": [0, 8], "windowst": [0, 8, 50], "normal": [0, 8, 12, 20, 23, 29, 44, 50, 55, 58, 59], "bound": [0, 8, 16, 18, 28, 38, 51, 57, 58], "left": [0, 8, 20, 29, 31, 32, 38, 50, 56, 58], "top": [0, 8, 13, 16, 20, 29, 32, 36, 38, 42, 44, 50, 55, 58], "width": [0, 8, 12, 16, 18, 20, 32, 37, 38, 49, 50, 58], "height": [0, 8, 12, 16, 18, 20, 32, 37, 38, 49, 50, 58], "window_st": [0, 8, 50], "permissiontyp": [0, 8], "ar": [0, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60], "audio_captur": [0, 8], "automatic_fullscreen": [0, 8], "camera_pan_tilt_zoom": [0, 8], "captured_surface_control": [0, 8, 38], "clipboard_read_writ": [0, 8], "clipboard_sanitized_writ": [0, 8], "display_captur": [0, 8, 38], "durable_storag": [0, 8], "geoloc": [0, 8, 20, 38, 55], "hand_track": [0, 8], "idle_detect": [0, 8, 38], "keyboard_lock": [0, 8, 38], "local_font": [0, 8, 38], "local_network_access": [0, 8], "midi": [0, 8, 38, 55], "midi_sysex": [0, 8], "nfc": [0, 8, 54, 55], "pointer_lock": [0, 8], "protected_media_identifi": [0, 8], "sensor": [0, 8, 20, 38, 55], "smart_card": [0, 8, 38], "speaker_select": [0, 8, 38], "storage_access": [0, 8, 36, 38], "top_level_storage_access": [0, 8, 36], "video_captur": [0, 8], "vr": [0, 8], "wake_lock_screen": [0, 8], "wake_lock_system": [0, 8], "web_app_instal": [0, 8, 38], "web_print": [0, 8, 38], "window_manag": [0, 8, 38], "permissionset": [0, 8], "grant": [0, 4, 8, 38, 50, 55], "deni": [0, 8, 38], "prompt": [0, 8, 14, 38], "permissiondescriptor": [0, 8], "sysex": [0, 8], "user_visible_onli": [0, 8], "allow_without_sanit": [0, 8], "allow_without_gestur": [0, 8], "pan_tilt_zoom": [0, 8], "browsercommandid": [0, 8], "open_tab_search": [0, 8], "close_tab_search": [0, 8], "bucket": [0, 8, 9, 25, 28, 48, 52], "low": [0, 7, 8, 36, 49], "high": [0, 8, 36, 38], "count": [0, 8, 9, 16, 27, 28, 29, 35, 36, 38, 42, 48], "histogram": [0, 8], "sum_": [0, 8], "privacysandboxapi": [0, 8], "bidding_and_auction_servic": [0, 8], "trusted_key_valu": [0, 8], "add_privacy_sandbox_coordinator_key_config": [0, 8], "add_privacy_sandbox_enrollment_overrid": [0, 8], "cancel_download": [0, 8], "crash": [0, 8, 30, 38, 50, 56, 58], "crash_gpu_process": [0, 8], "execute_browser_command": [0, 8], "get_browser_command_lin": [0, 8], "get_histogram": [0, 8], "get_vers": [0, 8], "get_window_bound": [0, 8], "get_window_for_target": [0, 8], "grant_permiss": [0, 8], "reset_permiss": [0, 8], "set_dock_til": [0, 8], "set_download_behavior": [0, 8, 38], "set_permiss": [0, 8], "set_window_bound": [0, 8], "downloadwillbegin": [0, 8, 38], "suggested_filenam": [0, 8, 38], "downloadprogress": [0, 8, 38], "total_byt": [0, 8, 38], "received_byt": [0, 8, 38], "state": [0, 2, 3, 4, 6, 7, 8, 12, 13, 16, 20, 24, 27, 36, 38, 41, 43, 44, 46, 48, 49, 50, 54, 58], "cachestorag": [0, 1], "cacheid": [0, 9], "cachedresponsetyp": [0, 9], "basic": [0, 9, 24, 36], "cor": [0, 4, 9, 36], "opaque_respons": [0, 9], "opaque_redirect": [0, 9], "dataentri": [0, 9, 28], "request_url": [0, 9, 36, 46], "request_method": [0, 9], "request_head": [0, 9, 36], "response_tim": [0, 9, 36], "response_statu": [0, 9], "response_status_text": [0, 9, 24], "response_typ": [0, 9], "response_head": [0, 9, 24, 36], "cach": [0, 9, 35, 36, 38, 47, 48, 58], "cache_id": [0, 9], "security_origin": [0, 9, 19, 28, 38], "cache_nam": [0, 9, 48], "storage_bucket": [0, 9, 25, 28, 48], "header": [0, 4, 9, 12, 20, 24, 36, 38, 41, 44, 47], "cachedrespons": [0, 9], "bodi": [0, 4, 9, 12, 13, 24, 36, 44], "delete_cach": [0, 9], "delete_entri": [0, 9], "request_cache_nam": [0, 9], "request_cached_respons": [0, 9], "request_entri": [0, 9], "cast": [0, 1], "sink": [0, 10, 17], "session": [0, 8, 10, 16, 19, 22, 36, 48, 50, 55, 59, 60], "set_sink_to_us": [0, 10], "start_desktop_mirror": [0, 10], "start_tab_mirror": [0, 10], "stop_cast": [0, 10], "sinksupd": [0, 10], "issueupd": [0, 10], "issue_messag": [0, 10], "consol": [0, 1, 4, 16, 27, 42, 44, 58], "consolemessag": [0, 11], "column": [0, 11, 12, 13, 16, 17, 36, 37, 38, 44], "clear_messag": [0, 11], "messagead": [0, 11], "messag": [0, 4, 10, 11, 16, 33, 34, 36, 37, 38, 44, 47, 50, 52], "css": [0, 1, 3, 4, 16, 20, 29, 37, 38, 58], "stylesheetid": [0, 12], "stylesheetorigin": [0, 12], "inject": [0, 12, 38, 50], "user_ag": [0, 12, 16, 20, 36], "inspector": [0, 1, 4, 12, 36, 58], "regular": [0, 12, 17, 21, 24, 56, 58], "pseudoelementmatch": [0, 12], "cssanimationstyl": [0, 12], "inheritedstyleentri": [0, 12], "matched_css_rul": [0, 12], "inline_styl": [0, 12], "inheritedanimatedstyleentri": [0, 12], "animation_styl": [0, 12], "transitions_styl": [0, 12], "inheritedpseudoelementmatch": [0, 12], "rulematch": [0, 12], "rule": [0, 3, 4, 12, 13, 36, 41, 44, 48], "matching_selector": [0, 12], "range_": [0, 12], "specif": [0, 4, 6, 7, 8, 12, 13, 34, 36, 41, 43, 44, 48, 50, 54], "b": [0, 12, 16, 38, 54, 61], "c": [0, 8, 12, 20, 34, 36, 43, 56], "selectorlist": [0, 12], "cssstylesheethead": [0, 12], "style_sheet_id": [0, 12], "source_url": [0, 12, 44, 47], "is_inlin": [0, 12], "is_mut": [0, 12], "is_construct": [0, 12], "start_lin": [0, 12, 13], "start_column": [0, 12, 13], "end_lin": [0, 12, 13], "end_column": [0, 12, 13], "source_map_url": [0, 12, 13], "owner_nod": [0, 12], "has_source_url": [0, 12, 13], "loading_fail": [0, 12], "cssrule": [0, 12], "selector_list": [0, 12], "nesting_selector": [0, 12], "media": [0, 1, 12, 20, 36, 38, 43], "container_queri": [0, 12], "support": [0, 7, 8, 12, 13, 20, 22, 24, 26, 29, 31, 36, 38, 40, 43, 45, 49, 50, 52, 53, 54, 55, 56], "layer": [0, 12, 16, 24, 32, 37], "scope": [0, 12, 13, 36, 38, 43, 44, 48, 54, 59, 60], "rule_typ": [0, 12], "starting_styl": [0, 12], "cssruletyp": [0, 12], "media_rul": [0, 12], "supports_rul": [0, 12], "container_rul": [0, 12], "layer_rul": [0, 12], "scope_rul": [0, 12], "style_rul": [0, 12], "starting_style_rul": [0, 12], "ruleusag": [0, 12], "sourcerang": [0, 12], "shorthandentri": [0, 12], "csscomputedstyleproperti": [0, 12, 16], "cssstyle": [0, 12], "css_properti": [0, 12], "shorthand_entri": [0, 12], "css_text": [0, 12], "cssproperti": [0, 12], "parsed_ok": [0, 12], "longhand_properti": [0, 12], "cssmedia": [0, 12], "media_list": [0, 12], "mediaqueri": [0, 12], "express": [0, 12, 13, 44, 58], "mediaqueryexpress": [0, 12], "unit": [0, 12], "value_rang": [0, 12], "computed_length": [0, 12], "csscontainerqueri": [0, 12], "physical_ax": [0, 12, 16], "logical_ax": [0, 12, 16], "queries_scroll_st": [0, 12, 16], "csssupport": [0, 12], "cssscope": [0, 12], "csslayer": [0, 12], "cssstartingstyl": [0, 12], "csslayerdata": [0, 12], "order": [0, 12, 18, 20, 27, 34, 37, 38, 43, 48, 57], "sub_lay": [0, 12], "platformfontusag": [0, 12], "family_nam": [0, 5, 12], "post_script_nam": [0, 12], "is_custom_font": [0, 12], "glyph_count": [0, 12], "fontvariationaxi": [0, 12], "min_valu": [0, 12, 53], "max_valu": [0, 12, 53], "default_valu": [0, 12, 53], "fontfac": [0, 12], "font_famili": [0, 12, 38], "font_styl": [0, 12], "font_vari": [0, 12], "font_stretch": [0, 12], "font_displai": [0, 12], "unicode_rang": [0, 12], "src": [0, 4, 7, 8, 12, 29, 36, 38, 50, 58, 59, 60], "platform_font_famili": [0, 12], "font_variation_ax": [0, 12], "csstryrul": [0, 12], "csspositiontryrul": [0, 12], "csskeyframesrul": [0, 12], "animation_nam": [0, 12], "csspropertyregistr": [0, 12], "property_nam": [0, 12], "inherit": [0, 12, 16, 44, 50, 57], "syntax": [0, 12, 36], "initial_valu": [0, 12, 41], "cssfontpalettevaluesrul": [0, 12], "font_palette_nam": [0, 12], "csspropertyrul": [0, 12], "cssfunctionparamet": [0, 12], "cssfunctionconditionnod": [0, 12], "condition_text": [0, 12], "cssfunctionnod": [0, 12], "cssfunctionrul": [0, 12], "csskeyframerul": [0, 12], "key_text": [0, 12], "styledeclarationedit": [0, 12], "add_rul": [0, 12], "collect_class_nam": [0, 12], "create_style_sheet": [0, 12], "force_pseudo_st": [0, 12], "force_starting_styl": [0, 12], "get_animated_styles_for_nod": [0, 12], "get_background_color": [0, 12], "get_computed_style_for_nod": [0, 12], "get_inline_styles_for_nod": [0, 12], "get_layers_for_nod": [0, 12], "get_location_for_selector": [0, 12], "get_longhand_properti": [0, 12], "get_matched_styles_for_nod": [0, 12], "get_media_queri": [0, 12], "get_platform_fonts_for_nod": [0, 12], "get_style_sheet_text": [0, 12], "resolve_valu": [0, 12], "set_container_query_text": [0, 12], "set_effective_property_value_for_nod": [0, 12], "set_keyframe_kei": [0, 12], "set_local_fonts_en": [0, 12], "set_media_text": [0, 12], "set_property_rule_property_nam": [0, 12], "set_rule_selector": [0, 12], "set_scope_text": [0, 12], "set_style_sheet_text": [0, 12], "set_style_text": [0, 12], "set_supports_text": [0, 12], "start_rule_usage_track": [0, 12], "stop_rule_usage_track": [0, 12], "take_computed_style_upd": [0, 12], "take_coverage_delta": [0, 12], "track_computed_style_upd": [0, 12], "track_computed_style_updates_for_nod": [0, 12], "fontsupd": [0, 12], "mediaqueryresultchang": [0, 12], "stylesheetad": [0, 12], "stylesheetchang": [0, 12], "stylesheetremov": [0, 12], "computedstyleupd": [0, 12], "debugg": [0, 1, 21, 36, 38, 44], "breakpointid": [0, 13], "callframeid": [0, 13], "scriptposit": [0, 13], "locationrang": [0, 13], "end": [0, 3, 4, 12, 13, 16, 29, 31, 32, 36, 38, 42, 44, 48, 52, 56], "callfram": [0, 13, 27, 42, 44], "call_frame_id": [0, 13], "function_nam": [0, 13, 42, 44], "scope_chain": [0, 13], "function_loc": [0, 13], "return_valu": [0, 13], "can_be_restart": [0, 13], "object_": [0, 13, 44], "start_loc": [0, 13], "end_loc": [0, 13], "searchmatch": [0, 13, 36, 38, 58], "line_cont": [0, 13], "breakloc": [0, 13], "wasmdisassemblychunk": [0, 13], "bytecode_offset": [0, 13], "scriptlanguag": [0, 13], "java_script": [0, 13], "web_assembli": [0, 13], "debugsymbol": [0, 13], "external_url": [0, 13], "resolvedbreakpoint": [0, 13], "breakpoint_id": [0, 13], "continue_to_loc": [0, 13], "disassemble_wasm_modul": [0, 13], "evaluate_on_call_fram": [0, 13], "get_possible_breakpoint": [0, 13], "get_script_sourc": [0, 13], "get_stack_trac": [0, 13], "get_wasm_bytecod": [0, 13], "next_wasm_disassembly_chunk": [0, 13], "paus": [0, 3, 13, 20, 24, 27, 38, 44, 50, 56, 60], "pause_on_async_cal": [0, 13], "remove_breakpoint": [0, 13], "restart_fram": [0, 13], "resum": [0, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], "search_in_cont": [0, 13], "set_async_call_stack_depth": [0, 13, 44], "set_blackbox_execution_context": [0, 13], "set_blackbox_pattern": [0, 13], "set_blackboxed_rang": [0, 13], "set_breakpoint": [0, 13], "set_breakpoint_by_url": [0, 13], "set_breakpoint_on_function_cal": [0, 13], "set_breakpoints_act": [0, 13], "set_instrumentation_breakpoint": [0, 13, 17, 21], "set_pause_on_except": [0, 13], "set_return_valu": [0, 13], "set_script_sourc": [0, 13], "set_skip_all_paus": [0, 13], "set_variable_valu": [0, 13], "step_into": [0, 13], "step_out": [0, 13], "step_ov": [0, 13], "breakpointresolv": [0, 13], "call_fram": [0, 13, 27, 42, 44], "hit_breakpoint": [0, 13], "async_stack_trac": [0, 13], "async_stack_trace_id": [0, 13], "async_call_stack_trace_id": [0, 13], "scriptfailedtopars": [0, 13], "execution_context_id": [0, 13, 16, 44], "hash_": [0, 13], "build_id": [0, 13], "execution_context_aux_data": [0, 13], "is_modul": [0, 13], "stack_trac": [0, 13, 33, 44], "code_offset": [0, 13], "script_languag": [0, 13], "embedder_nam": [0, 13], "scriptpars": [0, 13], "is_live_edit": [0, 13], "debug_symbol": [0, 13], "resolved_breakpoint": [0, 13], "deviceaccess": [0, 1], "requestid": [0, 4, 14, 24, 33, 36, 41, 48], "deviceid": [0, 14], "promptdevic": [0, 14], "cancel_prompt": [0, 14], "select_prompt": [0, 14], "devicerequestprompt": [0, 14], "devic": [0, 7, 10, 14, 15, 20, 29, 37, 38, 49, 54], "deviceorient": [0, 1], "clear_device_orientation_overrid": [0, 15, 38], "set_device_orientation_overrid": [0, 15, 38], "dom": [0, 1, 2, 5, 8, 12, 13, 17, 18, 19, 29, 35, 37, 38, 40, 44, 54, 55, 56, 58], "nodeid": [0, 2, 12, 16, 17, 37], "backendnodeid": [0, 2, 3, 4, 5, 12, 16, 17, 18, 32, 37, 38, 40, 41], "backendnod": [0, 16], "pseudotyp": [0, 12, 16, 18], "first_lin": [0, 16], "first_lett": [0, 16], "checkmark": [0, 16], "befor": [0, 4, 12, 13, 16, 20, 23, 24, 26, 27, 31, 32, 36, 38, 39, 40, 42, 48, 56, 58, 59, 60], "after": [0, 4, 12, 13, 16, 20, 24, 29, 30, 35, 36, 37, 38, 44, 55, 58], "picker_icon": [0, 16], "marker": [0, 16, 29, 52], "backdrop": [0, 16], "search_text": [0, 16], "target_text": [0, 16], "spelling_error": [0, 16], "grammar_error": [0, 16], "highlight": [0, 16, 37, 56], "first_line_inherit": [0, 16], "scroll_mark": [0, 16], "scroll_marker_group": [0, 16], "scroll_button": [0, 16], "scrollbar": [0, 16, 20, 38], "scrollbar_thumb": [0, 16], "scrollbar_button": [0, 16], "scrollbar_track": [0, 16], "scrollbar_track_piec": [0, 16], "scrollbar_corn": [0, 16], "resiz": [0, 12, 16, 20, 37], "input_list_button": [0, 16], "view_transit": [0, 16], "view_transition_group": [0, 16], "view_transition_image_pair": [0, 16], "view_transition_old": [0, 16], "view_transition_new": [0, 16], "file_selector_button": [0, 16], "details_cont": [0, 16], "picker": [0, 16], "shadowroottyp": [0, 16, 18], "open_": [0, 16], "compatibilitymod": [0, 16], "quirks_mod": [0, 16], "limited_quirks_mod": [0, 16], "no_quirks_mod": [0, 16], "physicalax": [0, 12, 16], "horizont": [0, 16, 18, 20, 38], "vertic": [0, 16, 18, 20, 38], "both": [0, 16, 29, 36, 38], "logicalax": [0, 12, 16], "inlin": [0, 12, 16, 18, 38, 41, 58], "block": [0, 4, 12, 16, 32, 36, 38, 42, 44, 49, 56, 58], "scrollorient": [0, 3, 16], "is_scrol": [0, 16], "detachedelementinfo": [0, 16], "tree_nod": [0, 16], "retained_node_id": [0, 16], "rgba": [0, 16, 20, 37], "r": [0, 16], "g": [0, 2, 4, 5, 12, 16, 20, 29, 36, 38, 44, 46, 49, 52, 54], "quad": [0, 16, 37], "boxmodel": [0, 16], "pad": [0, 16, 37], "border": [0, 16, 37], "margin": [0, 16, 37, 38], "shape_outsid": [0, 16], "shapeoutsideinfo": [0, 16], "shape": [0, 16, 37], "margin_shap": [0, 16], "rect": [0, 16, 18, 32, 37, 38, 40], "x": [0, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60], "y": [0, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 58], "collect_class_names_from_subtre": [0, 16], "copy_to": [0, 16], "describe_nod": [0, 16], "discard_search_result": [0, 16], "get_anchor_el": [0, 16], "get_attribut": [0, 16], "get_box_model": [0, 16], "get_container_for_nod": [0, 16], "get_content_quad": [0, 16], "get_detached_dom_nod": [0, 16], "get_docu": [0, 16], "get_element_by_rel": [0, 16], "get_file_info": [0, 16], "get_flattened_docu": [0, 16], "get_frame_own": [0, 16], "get_node_for_loc": [0, 16], "get_node_stack_trac": [0, 16], "get_nodes_for_subtree_by_styl": [0, 16], "get_outer_html": [0, 16], "get_querying_descendants_for_contain": [0, 16], "get_relayout_boundari": [0, 16], "get_search_result": [0, 16], "get_top_layer_el": [0, 16], "hide_highlight": [0, 16, 37], "highlight_nod": [0, 16, 37], "highlight_rect": [0, 16, 37], "mark_undoable_st": [0, 16], "move_to": [0, 16], "perform_search": [0, 16], "push_node_by_path_to_frontend": [0, 16], "push_nodes_by_backend_ids_to_frontend": [0, 16], "redo": [0, 16], "remove_attribut": [0, 16], "remove_nod": [0, 16], "request_child_nod": [0, 16], "request_nod": [0, 16], "resolve_nod": [0, 16], "scroll_into_view_if_need": [0, 16], "set_attribute_valu": [0, 16], "set_attributes_as_text": [0, 16], "set_file_input_fil": [0, 16], "set_inspected_nod": [0, 16], "set_node_nam": [0, 16], "set_node_stack_traces_en": [0, 16], "set_node_valu": [0, 16], "set_outer_html": [0, 16], "undo": [0, 16], "attributemodifi": [0, 16], "attributeremov": [0, 16], "characterdatamodifi": [0, 16], "character_data": [0, 16], "childnodecountupd": [0, 16], "childnodeinsert": [0, 16], "parent_node_id": [0, 16], "previous_node_id": [0, 16], "childnoderemov": [0, 16], "distributednodesupd": [0, 16], "insertion_point_id": [0, 16], "documentupd": [0, 16], "inlinestyleinvalid": [0, 16], "pseudoelementad": [0, 16], "toplayerelementsupd": [0, 16], "scrollableflagupd": [0, 16], "pseudoelementremov": [0, 16], "pseudo_element_id": [0, 16], "setchildnod": [0, 16], "shadowrootpop": [0, 16], "host_id": [0, 16], "root_id": [0, 16], "shadowrootpush": [0, 16], "domdebugg": [0, 1], "dombreakpointtyp": [0, 17], "subtree_modifi": [0, 17], "attribute_modifi": [0, 17], "node_remov": [0, 17], "cspviolationtyp": [0, 17], "trustedtype_sink_viol": [0, 17], "trustedtype_policy_viol": [0, 17], "eventlisten": [0, 17, 18], "use_captur": [0, 17], "passiv": [0, 17], "onc": [0, 13, 16, 17, 21, 22, 36, 38, 44], "handler": [0, 17, 36, 38, 43, 44, 58], "original_handl": [0, 17], "get_event_listen": [0, 17], "remove_dom_breakpoint": [0, 17], "remove_event_listener_breakpoint": [0, 17], "remove_instrumentation_breakpoint": [0, 17, 21], "remove_xhr_breakpoint": [0, 17], "set_break_on_csp_viol": [0, 17], "set_dom_breakpoint": [0, 17], "set_event_listener_breakpoint": [0, 17], "set_xhr_breakpoint": [0, 17], "domsnapshot": [0, 1, 16], "domnod": [0, 16, 18], "text_valu": [0, 18], "input_valu": [0, 18], "input_check": [0, 18], "option_select": [0, 18], "child_node_index": [0, 18], "pseudo_element_index": [0, 18], "layout_node_index": [0, 18], "content_languag": [0, 18], "document_encod": [0, 18], "content_document_index": [0, 18], "is_click": [0, 18], "event_listen": [0, 18], "current_source_url": [0, 18], "origin_url": [0, 18], "scroll_offset_x": [0, 18, 38], "scroll_offset_i": [0, 18, 38], "inlinetextbox": [0, 18], "bounding_box": [0, 18], "start_character_index": [0, 18], "num_charact": [0, 18], "layouttreenod": [0, 18], "dom_node_index": [0, 18], "layout_text": [0, 18], "inline_text_nod": [0, 18], "style_index": [0, 18], "paint_ord": [0, 18], "is_stacking_context": [0, 18], "computedstyl": [0, 18], "namevalu": [0, 18], "stringindex": [0, 18], "arrayofstr": [0, 18], "rarestringdata": [0, 18], "index": [0, 12, 16, 18, 27, 28, 36, 38, 44], "rarebooleandata": [0, 18], "rareintegerdata": [0, 18], "rectangl": [0, 16, 18, 32, 37, 38], "documentsnapshot": [0, 18], "encoding_nam": [0, 18], "layout": [0, 4, 16, 18, 26, 29, 32, 37, 38, 40], "text_box": [0, 18], "content_width": [0, 18], "content_height": [0, 18], "nodetreesnapshot": [0, 18], "parent_index": [0, 18], "layouttreesnapshot": [0, 18], "node_index": [0, 18], "stacking_context": [0, 18], "offset_rect": [0, 18], "scroll_rect": [0, 18, 32], "client_rect": [0, 18], "blended_background_color": [0, 18], "text_color_opac": [0, 18], "textboxsnapshot": [0, 18], "layout_index": [0, 18], "capture_snapshot": [0, 18, 38], "get_snapshot": [0, 18], "domstorag": [0, 1], "serializedstoragekei": [0, 19, 25, 48], "storageid": [0, 19], "is_local_storag": [0, 19], "get_dom_storage_item": [0, 19], "remove_dom_storage_item": [0, 19], "set_dom_storage_item": [0, 19], "domstorageitemad": [0, 19], "storage_id": [0, 19], "new_valu": [0, 13, 19], "domstorageitemremov": [0, 19], "domstorageitemupd": [0, 19], "old_valu": [0, 19], "domstorageitemsclear": [0, 19], "emul": [0, 1, 29, 36, 37, 38], "safeareainset": [0, 20], "top_max": [0, 20], "left_max": [0, 20], "bottom": [0, 20, 29, 38, 58], "bottom_max": [0, 20], "right": [0, 20, 29, 34, 38], "right_max": [0, 20], "screenorient": [0, 20, 38], "angl": [0, 20, 29], "displayfeatur": [0, 20], "mask_length": [0, 20], "devicepostur": [0, 20], "mediafeatur": [0, 20], "virtualtimepolici": [0, 20], "advanc": [0, 20], "pause_if_network_fetches_pend": [0, 20], "useragentbrandvers": [0, 20], "brand": [0, 20], "version": [0, 8, 13, 16, 17, 18, 20, 26, 28, 36, 37, 38, 39, 45, 46, 47, 49, 50], "useragentmetadata": [0, 20, 36], "platform": [0, 6, 8, 12, 20, 29, 36, 37, 38, 40, 49, 52, 53], "platform_vers": [0, 20], "architectur": [0, 20], "model": [0, 4, 16, 20, 38, 49], "mobil": [0, 20, 38], "full_version_list": [0, 20], "full_vers": [0, 20], "bit": [0, 20, 29, 38, 48, 54, 56, 58], "wow64": [0, 20, 38], "sensortyp": [0, 20], "absolute_orient": [0, 20], "acceleromet": [0, 20, 38], "ambient_light": [0, 20], "graviti": [0, 20], "gyroscop": [0, 20, 38], "linear_acceler": [0, 20], "magnetomet": [0, 20, 38], "relative_orient": [0, 20], "sensormetadata": [0, 20], "minimum_frequ": [0, 20], "maximum_frequ": [0, 20], "sensorreadingsingl": [0, 20], "sensorreadingxyz": [0, 20], "z": [0, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], "sensorreadingquaternion": [0, 20], "w": [0, 20], "sensorread": [0, 20], "xyz": [0, 20], "quaternion": [0, 20], "pressuresourc": [0, 20], "cpu": [0, 4, 20, 42, 49], "pressurest": [0, 20], "nomin": [0, 20], "fair": [0, 20], "seriou": [0, 20], "critic": [0, 20, 35, 38], "pressuremetadata": [0, 20], "disabledimagetyp": [0, 20], "avif": [0, 20], "webp": [0, 20, 26, 49], "can_emul": [0, 20], "clear_device_metrics_overrid": [0, 20, 38], "clear_device_posture_overrid": [0, 20], "clear_display_features_overrid": [0, 20], "clear_geolocation_overrid": [0, 20, 38], "clear_idle_overrid": [0, 20], "get_overridden_sensor_inform": [0, 20], "reset_page_scale_factor": [0, 20], "set_auto_dark_mode_overrid": [0, 20], "set_automation_overrid": [0, 20], "set_cpu_throttling_r": [0, 20], "set_default_background_color_overrid": [0, 20], "set_device_metrics_overrid": [0, 20, 38], "set_device_posture_overrid": [0, 20], "set_disabled_image_typ": [0, 20], "set_display_features_overrid": [0, 20], "set_document_cookie_dis": [0, 20], "set_emit_touch_events_for_mous": [0, 20], "set_emulated_media": [0, 20], "set_emulated_vision_defici": [0, 20], "set_focus_emulation_en": [0, 20], "set_geolocation_overrid": [0, 20, 38], "set_hardware_concurrency_overrid": [0, 20], "set_idle_overrid": [0, 20], "set_locale_overrid": [0, 20], "set_navigator_overrid": [0, 20], "set_page_scale_factor": [0, 20], "set_pressure_source_override_en": [0, 20], "set_pressure_state_overrid": [0, 20], "set_safe_area_insets_overrid": [0, 20], "set_script_execution_dis": [0, 20], "set_scrollbars_hidden": [0, 20], "set_sensor_override_en": [0, 20], "set_sensor_override_read": [0, 20], "set_timezone_overrid": [0, 20], "set_touch_emulation_en": [0, 20, 38], "set_user_agent_overrid": [0, 20, 36], "set_virtual_time_polici": [0, 20], "set_visible_s": [0, 20], "virtualtimebudgetexpir": [0, 20], "eventbreakpoint": [0, 1], "extens": [0, 1, 12, 37, 38, 43, 54, 57], "storagearea": [0, 22], "local": [0, 12, 13, 19, 20, 22, 36, 38, 43, 48, 58, 59], "sync": [0, 22, 34, 38, 52], "manag": [0, 8, 22, 38, 41], "clear_storage_item": [0, 22], "get_storage_item": [0, 22], "load_unpack": [0, 22], "remove_storage_item": [0, 22], "set_storage_item": [0, 22], "uninstal": [0, 22, 43], "fedcm": [0, 1], "loginst": [0, 23], "sign_in": [0, 23], "sign_up": [0, 23], "dialogtyp": [0, 23, 38], "account_choos": [0, 23], "auto_reauthn": [0, 23], "confirm_idp_login": [0, 23], "dialogbutton": [0, 23], "confirm_idp_login_continu": [0, 23], "error_got_it": [0, 23], "error_more_detail": [0, 23], "accounturltyp": [0, 23], "terms_of_servic": [0, 23], "privacy_polici": [0, 23], "account": [0, 23, 38, 59, 60], "account_id": [0, 23], "email": [0, 23, 59, 60], "given_nam": [0, 5, 23], "picture_url": [0, 23], "idp_config_url": [0, 23], "idp_login_url": [0, 23], "login_st": [0, 23], "terms_of_service_url": [0, 23], "privacy_policy_url": [0, 23], "click_dialog_button": [0, 23], "dismiss_dialog": [0, 23], "open_url": [0, 23], "reset_cooldown": [0, 23], "select_account": [0, 23], "dialogshown": [0, 23], "dialog_id": [0, 23], "dialog_typ": [0, 23], "subtitl": [0, 23], "dialogclos": [0, 23], "fetch": [0, 1, 2, 9, 20, 28, 36, 38, 48, 53], "requeststag": [0, 24], "respons": [0, 4, 7, 9, 14, 16, 24, 26, 36, 38, 47, 50, 54], "requestpattern": [0, 24, 36], "url_pattern": [0, 24, 36], "request_stag": [0, 24], "headerentri": [0, 24], "authchalleng": [0, 24, 36], "scheme": [0, 24, 36, 38], "realm": [0, 24, 36], "authchallengerespons": [0, 24, 36], "usernam": [0, 24, 36, 55], "password": [0, 5, 24, 36, 55], "continue_request": [0, 24], "continue_respons": [0, 24], "continue_with_auth": [0, 24], "fail_request": [0, 24], "fulfill_request": [0, 24], "get_response_bodi": [0, 24, 36], "take_response_body_as_stream": [0, 24], "requestpaus": [0, 24, 36], "response_error_reason": [0, 24, 36], "response_status_cod": [0, 24, 36], "network_id": [0, 24], "redirected_request_id": [0, 24], "authrequir": [0, 24], "auth_challeng": [0, 24, 36], "filesystem": [0, 1, 22], "last_modifi": [0, 25, 38], "size": [0, 4, 8, 12, 13, 20, 25, 26, 27, 31, 35, 36, 37, 38, 40, 44, 48, 49, 52, 53, 54, 56, 58], "directori": [0, 25], "nested_directori": [0, 25], "nested_fil": [0, 25], "bucketfilesystemloc": [0, 25], "path_compon": [0, 25], "bucket_nam": [0, 25], "get_directori": [0, 25], "headlessexperiment": [0, 1], "screenshotparam": [0, 26], "format_": [0, 26, 38], "qualiti": [0, 4, 26, 38], "optimize_for_spe": [0, 26, 38], "begin_fram": [0, 26], "heapprofil": [0, 1], "heapsnapshotobjectid": [0, 27], "samplingheapprofilenod": [0, 27], "self_siz": [0, 27], "samplingheapprofilesampl": [0, 27], "ordin": [0, 27], "samplingheapprofil": [0, 27], "head": [0, 27, 58], "sampl": [0, 8, 27, 35, 42, 52, 53], "add_inspected_heap_object": [0, 27], "collect_garbag": [0, 27], "get_heap_object_id": [0, 27], "get_object_by_heap_object_id": [0, 27], "get_sampling_profil": [0, 27, 35], "start_sampl": [0, 27, 35], "start_tracking_heap_object": [0, 27], "stop_sampl": [0, 27, 35], "stop_tracking_heap_object": [0, 27], "take_heap_snapshot": [0, 27], "addheapsnapshotchunk": [0, 27], "chunk": [0, 13, 27, 31, 36], "heapstatsupd": [0, 27], "stats_upd": [0, 27], "lastseenobjectid": [0, 27], "last_seen_object_id": [0, 27], "reportheapsnapshotprogress": [0, 27], "done": [0, 5, 8, 27, 38, 48, 58], "total": [0, 4, 8, 13, 16, 27, 35, 36, 38, 44, 48, 52, 58], "finish": [0, 2, 4, 27, 36, 43, 53, 59], "resetprofil": [0, 27], "indexeddb": [0, 1, 48], "databasewithobjectstor": [0, 28], "object_stor": [0, 28], "objectstor": [0, 28, 48], "key_path": [0, 28], "auto_incr": [0, 28], "objectstoreindex": [0, 28], "uniqu": [0, 2, 3, 4, 8, 9, 13, 16, 24, 27, 28, 29, 32, 34, 36, 38, 41, 42, 44, 50, 53], "multi_entri": [0, 28], "date": [0, 28, 36, 38, 46, 58], "keyrang": [0, 28], "lower_open": [0, 28], "upper_open": [0, 28], "lower": [0, 28], "upper": [0, 28], "primary_kei": [0, 28], "keypath": [0, 28], "clear_object_stor": [0, 28], "delete_databas": [0, 28], "delete_object_store_entri": [0, 28], "get_metadata": [0, 28], "request_data": [0, 28], "request_databas": [0, 28], "request_database_nam": [0, 28], "input": [0, 1, 16, 18, 20, 31, 38, 43, 44, 56, 59, 60, 61], "touchpoint": [0, 29], "radius_x": [0, 29], "radius_i": [0, 29], "rotation_angl": [0, 29], "forc": [0, 12, 13, 20, 29, 32, 38, 52], "tangential_pressur": [0, 29], "tilt_x": [0, 29], "tilt_i": [0, 29], "twist": [0, 29], "gesturesourcetyp": [0, 29], "touch": [0, 20, 29, 38], "mous": [0, 18, 20, 29, 38, 56, 58], "mousebutton": [0, 29], "none": [0, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], "middl": [0, 29], "timesinceepoch": [0, 6, 20, 25, 29, 36, 38, 40, 46, 48], "dragdataitem": [0, 29], "mime_typ": [0, 29, 36, 38], "dragdata": [0, 29], "drag_operations_mask": [0, 29], "cancel_drag": [0, 29], "dispatch_drag_ev": [0, 29], "dispatch_key_ev": [0, 29], "dispatch_mouse_ev": [0, 29], "dispatch_touch_ev": [0, 29], "emulate_touch_from_mouse_ev": [0, 29], "ime_set_composit": [0, 29], "insert_text": [0, 29], "set_ignore_input_ev": [0, 29], "set_intercept_drag": [0, 29], "synthesize_pinch_gestur": [0, 29], "synthesize_scroll_gestur": [0, 29], "synthesize_tap_gestur": [0, 29], "dragintercept": [0, 29], "detach": [0, 16, 30, 38, 50], "targetcrash": [0, 30, 50], "targetreloadedaftercrash": [0, 30], "io": [0, 1, 8, 20, 23, 24, 36, 38, 40, 41, 48, 53, 54, 58], "streamhandl": [0, 24, 31, 36, 38, 52], "read": [0, 9, 12, 13, 16, 20, 24, 31, 36, 38, 58], "resolve_blob": [0, 31], "layertre": [0, 1], "layerid": [0, 32], "snapshotid": [0, 32], "scrollrect": [0, 18, 32], "stickypositionconstraint": [0, 32], "sticky_box_rect": [0, 32], "containing_block_rect": [0, 32], "nearest_layer_shifting_sticky_box": [0, 32], "nearest_layer_shifting_containing_block": [0, 32], "picturetil": [0, 32], "pictur": [0, 32, 38], "layer_id": [0, 32], "offset_x": [0, 32, 38], "offset_i": [0, 32, 38], "paint_count": [0, 32], "draws_cont": [0, 32], "parent_layer_id": [0, 32], "transform": [0, 32], "anchor_x": [0, 32], "anchor_i": [0, 32], "anchor_z": [0, 32], "invis": [0, 32], "sticky_position_constraint": [0, 32], "paintprofil": [0, 32], "compositing_reason": [0, 32], "load_snapshot": [0, 32], "make_snapshot": [0, 32], "profile_snapshot": [0, 32], "release_snapshot": [0, 32], "replay_snapshot": [0, 32], "snapshot_command_log": [0, 32], "layerpaint": [0, 32], "clip": [0, 32, 38], "layertreedidchang": [0, 32], "log": [0, 1, 11, 32, 34, 36, 52, 59, 60], "logentri": [0, 33], "categori": [0, 4, 33, 46, 52], "network_request_id": [0, 33], "worker_id": [0, 33], "arg": [0, 33, 44, 57, 59], "violationset": [0, 33], "threshold": [0, 33], "start_violations_report": [0, 33], "stop_violations_report": [0, 33], "entryad": [0, 33], "entri": [0, 4, 7, 9, 12, 24, 28, 33, 34, 36, 38, 44, 47, 48, 50, 55], "playerid": [0, 34], "playermessag": [0, 34], "playerproperti": [0, 34], "playerev": [0, 34], "playererrorsourceloc": [0, 34], "playererror": [0, 34], "stack": [0, 13, 16, 18, 24, 33, 34, 35, 36, 38, 42, 44, 52], "caus": [0, 2, 4, 8, 13, 20, 24, 27, 34, 36, 37, 38, 41, 43, 50, 55], "playerpropertieschang": [0, 34], "player_id": [0, 34], "playereventsad": [0, 34], "playermessageslog": [0, 34], "playererrorsrais": [0, 34], "playerscr": [0, 34], "player": [0, 34], "memori": [0, 1, 27, 38, 44, 52], "pressurelevel": [0, 35], "moder": [0, 35], "samplingprofilenod": [0, 35], "samplingprofil": [0, 35], "base_address": [0, 35], "domcount": [0, 35], "forcibly_purge_java_script_memori": [0, 35], "get_all_time_sampling_profil": [0, 35], "get_browser_sampling_profil": [0, 35], "get_dom_count": [0, 35], "get_dom_counters_for_leak_detect": [0, 35], "prepare_for_leak_detect": [0, 35], "set_pressure_notifications_suppress": [0, 35], "simulate_pressure_notif": [0, 35], "network": [0, 1, 4, 12, 24, 33, 38, 41, 44, 46, 48, 58], "resourcetyp": [0, 24, 36, 38], "document": [0, 2, 3, 4, 12, 16, 18, 20, 29, 35, 36, 38, 40, 41, 58], "text_track": [0, 36], "xhr": [0, 17, 36, 38], "web_socket": [0, 36, 38], "signed_exchang": [0, 36], "csp_violation_report": [0, 36], "preflight": [0, 36], "loaderid": [0, 4, 36, 38, 41], "interceptionid": [0, 36], "errorreason": [0, 24, 36], "fail": [0, 4, 12, 13, 24, 26, 36, 38, 43, 54], "abort": [0, 23, 36], "timed_out": [0, 36], "access_deni": [0, 36], "connection_clos": [0, 36], "connection_reset": [0, 36], "connection_refus": [0, 36], "connection_abort": [0, 36], "connection_fail": [0, 36], "name_not_resolv": [0, 36], "internet_disconnect": [0, 36], "address_unreach": [0, 36], "blocked_by_cli": [0, 36, 41], "blocked_by_respons": [0, 4, 36], "monotonictim": [0, 36, 38], "connectiontyp": [0, 36], "cellular2g": [0, 36], "cellular3g": [0, 36], "cellular4g": [0, 36], "bluetooth": [0, 7, 36, 38], "ethernet": [0, 36], "wifi": [0, 36], "wimax": [0, 36], "cookiesamesit": [0, 36], "strict": [0, 36, 48], "lax": [0, 36], "cookieprior": [0, 36], "medium": [0, 36], "cookiesourceschem": [0, 36], "unset": [0, 20, 36, 43, 58], "non_secur": [0, 36], "secur": [0, 1, 9, 19, 28, 36, 38, 44, 48, 60], "resourcetim": [0, 36], "request_tim": [0, 36], "proxy_start": [0, 36], "proxy_end": [0, 36], "dns_start": [0, 36], "dns_end": [0, 36], "connect_start": [0, 36], "connect_end": [0, 36], "ssl_start": [0, 36], "ssl_end": [0, 36], "worker_start": [0, 36], "worker_readi": [0, 36], "worker_fetch_start": [0, 36], "worker_respond_with_settl": [0, 36], "send_start": [0, 36], "send_end": [0, 36], "push_start": [0, 36], "push_end": [0, 36], "receive_headers_start": [0, 36], "receive_headers_end": [0, 36], "worker_router_evaluation_start": [0, 36], "worker_cache_lookup_start": [0, 36], "resourceprior": [0, 36], "very_low": [0, 36], "very_high": [0, 36], "postdataentri": [0, 36], "bytes_": [0, 36], "initial_prior": [0, 36], "referrer_polici": [0, 36, 38], "url_frag": [0, 36, 38], "post_data": [0, 24, 36], "has_post_data": [0, 36], "post_data_entri": [0, 36], "mixed_content_typ": [0, 36, 46], "is_link_preload": [0, 36], "trust_token_param": [0, 36], "is_same_sit": [0, 36], "signedcertificatetimestamp": [0, 36], "statu": [0, 4, 8, 9, 13, 24, 36, 38, 41, 47, 49, 50], "log_descript": [0, 36], "log_id": [0, 36], "hash_algorithm": [0, 36], "signature_algorithm": [0, 36], "signature_data": [0, 36], "securitydetail": [0, 36], "protocol": [0, 8, 24, 36, 38, 45, 46, 50, 52, 53, 54, 58], "key_exchang": [0, 36, 46], "cipher": [0, 36, 46], "certificate_id": [0, 36], "subject_nam": [0, 36, 46], "san_list": [0, 36], "issuer": [0, 36, 46, 48], "valid_from": [0, 36, 46], "valid_to": [0, 36, 46], "signed_certificate_timestamp_list": [0, 36], "certificate_transparency_compli": [0, 36], "encrypted_client_hello": [0, 36], "key_exchange_group": [0, 36, 46], "mac": [0, 36, 46, 49], "server_signature_algorithm": [0, 36], "certificatetransparencycompli": [0, 36], "unknown": [0, 36, 38, 46, 49], "not_compli": [0, 36], "compliant": [0, 36], "blockedreason": [0, 36], "csp": [0, 4, 17, 36, 38, 44], "mixed_cont": [0, 36, 41], "subresource_filt": [0, 36], "content_typ": [0, 36], "coop_sandboxed_iframe_cannot_navigate_to_coop_pag": [0, 36], "corserror": [0, 4, 36], "disallowed_by_mod": [0, 36], "invalid_respons": [0, 36], "wildcard_origin_not_allow": [0, 36], "missing_allow_origin_head": [0, 36], "multiple_allow_origin_valu": [0, 36], "invalid_allow_origin_valu": [0, 36], "allow_origin_mismatch": [0, 36], "invalid_allow_credenti": [0, 36], "cors_disabled_schem": [0, 36], "preflight_invalid_statu": [0, 36], "preflight_disallowed_redirect": [0, 36], "preflight_wildcard_origin_not_allow": [0, 36], "preflight_missing_allow_origin_head": [0, 36], "preflight_multiple_allow_origin_valu": [0, 36], "preflight_invalid_allow_origin_valu": [0, 36], "preflight_allow_origin_mismatch": [0, 36], "preflight_invalid_allow_credenti": [0, 36], "preflight_missing_allow_extern": [0, 36], "preflight_invalid_allow_extern": [0, 36], "preflight_missing_allow_private_network": [0, 36], "preflight_invalid_allow_private_network": [0, 36], "invalid_allow_methods_preflight_respons": [0, 36], "invalid_allow_headers_preflight_respons": [0, 36], "method_disallowed_by_preflight_respons": [0, 36], "header_disallowed_by_preflight_respons": [0, 36], "redirect_contains_credenti": [0, 36], "insecure_private_network": [0, 36], "invalid_private_network_access": [0, 36], "unexpected_private_network_access": [0, 36], "no_cors_redirect_mode_not_follow": [0, 36], "preflight_missing_private_network_access_id": [0, 36], "preflight_missing_private_network_access_nam": [0, 36], "private_network_access_permission_unavail": [0, 36], "private_network_access_permission_deni": [0, 36], "local_network_access_permission_deni": [0, 36], "corserrorstatu": [0, 4, 36], "failed_paramet": [0, 36], "serviceworkerresponsesourc": [0, 36], "cache_storag": [0, 36, 48], "http_cach": [0, 36], "fallback_cod": [0, 36], "trusttokenparam": [0, 36], "refresh_polici": [0, 36], "trusttokenoperationtyp": [0, 36], "issuanc": [0, 36, 38], "redempt": [0, 36, 38, 48], "sign": [0, 23, 36, 43, 48, 59, 60], "alternateprotocolusag": [0, 36], "alternative_job_won_without_rac": [0, 36], "alternative_job_won_rac": [0, 36], "main_job_won_rac": [0, 36], "mapping_miss": [0, 36], "broken": [0, 36, 46], "dns_alpn_h3_job_won_without_rac": [0, 36], "dns_alpn_h3_job_won_rac": [0, 36], "unspecified_reason": [0, 36], "serviceworkerroutersourc": [0, 36], "fetch_ev": [0, 36], "race_network_and_fetch_handl": [0, 36], "serviceworkerrouterinfo": [0, 36], "rule_id_match": [0, 36], "matched_source_typ": [0, 36], "actual_source_typ": [0, 36], "status_text": [0, 36], "charset": [0, 36], "connection_reus": [0, 36], "connection_id": [0, 36, 51], "encoded_data_length": [0, 36], "security_st": [0, 36, 46], "headers_text": [0, 36], "request_headers_text": [0, 36], "remote_ip_address": [0, 36], "remote_port": [0, 36], "from_disk_cach": [0, 36], "from_service_work": [0, 36], "from_prefetch_cach": [0, 36], "from_early_hint": [0, 36], "service_worker_router_info": [0, 36], "time": [0, 2, 3, 4, 12, 13, 20, 23, 26, 27, 29, 32, 33, 34, 36, 39, 40, 42, 44, 46, 47, 48, 50, 53, 55, 56, 58], "service_worker_response_sourc": [0, 36], "cache_storage_cache_nam": [0, 36], "alternate_protocol_usag": [0, 36], "security_detail": [0, 36], "websocketrequest": [0, 36], "websocketrespons": [0, 36], "websocketfram": [0, 36], "opcod": [0, 36], "mask": [0, 20, 36, 37], "payload_data": [0, 36], "cachedresourc": [0, 36], "body_s": [0, 36], "initi": [0, 6, 13, 20, 24, 26, 36, 38, 41, 42, 43, 44, 50, 56], "cookiepartitionkei": [0, 36], "top_level_sit": [0, 36], "has_cross_site_ancestor": [0, 36], "http_onli": [0, 36], "prioriti": [0, 2, 12, 36, 46, 48], "same_parti": [0, 36], "source_schem": [0, 36], "source_port": [0, 36], "expir": [0, 36, 38, 46, 48], "same_sit": [0, 36], "partition_kei": [0, 36], "partition_key_opaqu": [0, 36], "setcookieblockedreason": [0, 36], "secure_onli": [0, 36], "same_site_strict": [0, 36], "same_site_lax": [0, 36], "same_site_unspecified_treated_as_lax": [0, 36], "same_site_none_insecur": [0, 36], "user_prefer": [0, 36], "third_party_phaseout": [0, 36], "third_party_blocked_in_first_party_set": [0, 36], "syntax_error": [0, 36], "scheme_not_support": [0, 36], "overwrite_secur": [0, 36], "invalid_domain": [0, 36], "invalid_prefix": [0, 36], "unknown_error": [0, 36], "schemeful_same_site_strict": [0, 36], "schemeful_same_site_lax": [0, 36], "schemeful_same_site_unspecified_treated_as_lax": [0, 36], "same_party_from_cross_party_context": [0, 36], "same_party_conflicts_with_other_attribut": [0, 36], "name_value_pair_exceeds_max_s": [0, 36], "disallowed_charact": [0, 36], "no_cookie_cont": [0, 36], "cookieblockedreason": [0, 36], "not_on_path": [0, 36], "domain_mismatch": [0, 36], "port_mismatch": [0, 36], "scheme_mismatch": [0, 36], "cookieexemptionreason": [0, 36], "user_set": [0, 36], "tpcd_metadata": [0, 36], "tpcd_deprecation_tri": [0, 36], "top_level_tpcd_deprecation_tri": [0, 36], "tpcd_heurist": [0, 36], "enterprise_polici": [0, 36], "same_site_none_cookies_in_sandbox": [0, 36], "blockedsetcookiewithreason": [0, 36], "blocked_reason": [0, 36], "cookie_lin": [0, 36], "exemptedsetcookiewithreason": [0, 36], "exemption_reason": [0, 36], "associatedcooki": [0, 36], "cookieparam": [0, 36, 48], "interceptionstag": [0, 36], "headers_receiv": [0, 36], "interception_stag": [0, 36], "signedexchangesignatur": [0, 36], "signatur": [0, 36, 46, 54], "integr": [0, 34, 36, 52], "validity_url": [0, 36], "cert_url": [0, 36], "cert_sha256": [0, 36], "certif": [0, 36, 46, 58, 60], "signedexchangehead": [0, 36], "response_cod": [0, 24, 36], "header_integr": [0, 36], "signedexchangeerrorfield": [0, 36], "signature_sig": [0, 36], "signature_integr": [0, 36], "signature_cert_url": [0, 36], "signature_cert_sha256": [0, 36], "signature_validity_url": [0, 36], "signature_timestamp": [0, 36], "signedexchangeerror": [0, 36], "signature_index": [0, 36], "error_field": [0, 36], "signedexchangeinfo": [0, 36], "outer_respons": [0, 36], "contentencod": [0, 36], "deflat": [0, 36], "gzip": [0, 36, 52], "br": [0, 36], "zstd": [0, 36], "directsocketdnsquerytyp": [0, 36], "ipv4": [0, 36, 38], "ipv6": [0, 36, 38], "directtcpsocketopt": [0, 36], "no_delai": [0, 36], "keep_alive_delai": [0, 36], "send_buffer_s": [0, 36], "receive_buffer_s": [0, 36], "dns_query_typ": [0, 36], "privatenetworkrequestpolici": [0, 36], "allow": [0, 4, 5, 7, 8, 13, 17, 20, 23, 24, 28, 29, 34, 36, 38, 41, 42, 43, 44, 46, 50, 53, 54, 58], "block_from_insecure_to_more_priv": [0, 36], "warn_from_insecure_to_more_priv": [0, 36], "preflight_block": [0, 36], "preflight_warn": [0, 36], "permission_block": [0, 36], "permission_warn": [0, 36], "ipaddressspac": [0, 4, 36], "privat": [0, 36, 38, 44, 54], "public": [0, 4, 7, 36, 38], "connecttim": [0, 36], "clientsecurityst": [0, 4, 36], "initiator_is_secure_context": [0, 36], "initiator_ip_address_spac": [0, 36], "private_network_request_polici": [0, 36], "crossoriginopenerpolicyvalu": [0, 36], "same_origin": [0, 36, 38], "same_origin_allow_popup": [0, 36], "restrict_properti": [0, 36], "unsafe_non": [0, 36], "same_origin_plus_coep": [0, 36], "restrict_properties_plus_coep": [0, 36], "noopener_allow_popup": [0, 36], "crossoriginopenerpolicystatu": [0, 36], "report_only_valu": [0, 36], "reporting_endpoint": [0, 36], "report_only_reporting_endpoint": [0, 36], "crossoriginembedderpolicyvalu": [0, 36], "credentialless": [0, 36], "require_corp": [0, 36], "crossoriginembedderpolicystatu": [0, 36], "contentsecuritypolicysourc": [0, 36], "http": [0, 4, 7, 8, 9, 12, 16, 20, 23, 24, 25, 26, 29, 36, 38, 40, 41, 43, 46, 48, 50, 53, 54, 58, 59, 60], "meta": [0, 20, 29, 36, 38, 44, 56, 58], "contentsecuritypolicystatu": [0, 36], "effective_direct": [0, 36], "is_enforc": [0, 36], "securityisolationstatu": [0, 36], "coop": [0, 4, 36], "coep": [0, 4, 36], "reportstatu": [0, 36], "queu": [0, 36], "pend": [0, 20, 36, 38, 41, 48, 52], "marked_for_remov": [0, 36], "success": [0, 4, 13, 36, 38, 41, 48, 52, 54], "reportid": [0, 36], "reportingapireport": [0, 36], "initiator_url": [0, 36], "destin": [0, 36, 38, 53, 56], "depth": [0, 2, 13, 16, 17, 36, 44], "completed_attempt": [0, 36], "reportingapiendpoint": [0, 36], "group_nam": [0, 36], "loadnetworkresourcepageresult": [0, 36], "net_error": [0, 36], "net_error_nam": [0, 36], "http_status_cod": [0, 36], "stream": [0, 13, 24, 31, 36, 38, 52], "loadnetworkresourceopt": [0, 36], "disable_cach": [0, 36], "include_credenti": [0, 36], "can_clear_browser_cach": [0, 36], "can_clear_browser_cooki": [0, 36], "can_emulate_network_condit": [0, 36], "clear_accepted_encodings_overrid": [0, 36], "clear_browser_cach": [0, 36], "clear_browser_cooki": [0, 36], "continue_intercepted_request": [0, 36], "delete_cooki": [0, 36, 38], "emulate_network_condit": [0, 36], "enable_reporting_api": [0, 36], "get_all_cooki": [0, 36], "get_certif": [0, 36], "get_cooki": [0, 36, 48], "get_request_post_data": [0, 36], "get_response_body_for_intercept": [0, 36], "get_security_isolation_statu": [0, 36], "load_network_resourc": [0, 36], "replay_xhr": [0, 36], "search_in_response_bodi": [0, 36], "set_accepted_encod": [0, 36], "set_attach_debug_stack": [0, 36], "set_blocked_ur_l": [0, 36], "set_bypass_service_work": [0, 36], "set_cache_dis": [0, 36], "set_cookie_control": [0, 36], "set_extra_http_head": [0, 36], "set_request_intercept": [0, 36], "stream_resource_cont": [0, 36], "take_response_body_for_interception_as_stream": [0, 36], "datareceiv": [0, 36], "data_length": [0, 36], "eventsourcemessagereceiv": [0, 36], "event_id": [0, 36, 46, 48], "loadingfail": [0, 36, 48], "error_text": [0, 36], "loadingfinish": [0, 36, 48], "requestintercept": [0, 36], "interception_id": [0, 36], "is_navigation_request": [0, 36], "is_download": [0, 36], "redirect_url": [0, 36], "requestservedfromcach": [0, 36], "requestwillbes": [0, 24, 36, 48, 58], "wall_tim": [0, 36], "redirect_has_extra_info": [0, 36], "redirect_respons": [0, 36], "has_user_gestur": [0, 36], "resourcechangedprior": [0, 36], "new_prior": [0, 36], "signedexchangereceiv": [0, 36], "info": [0, 4, 36, 37, 46, 49], "responsereceiv": [0, 36], "has_extra_info": [0, 36], "websocketclos": [0, 36], "websocketcr": [0, 36], "websocketframeerror": [0, 36], "error_messag": [0, 36, 41, 47], "websocketframereceiv": [0, 36], "websocketframes": [0, 36], "websockethandshakeresponsereceiv": [0, 36], "websocketwillsendhandshakerequest": [0, 36], "webtransportcr": [0, 36], "transport_id": [0, 36], "webtransportconnectionestablish": [0, 36], "webtransportclos": [0, 36], "directtcpsocketcr": [0, 36], "identifi": [0, 2, 4, 5, 6, 7, 8, 9, 12, 13, 16, 17, 18, 19, 20, 24, 27, 29, 32, 33, 36, 37, 38, 40, 41, 42, 43, 44, 48, 50], "remote_addr": [0, 36], "directtcpsocketopen": [0, 36], "local_addr": [0, 36], "local_port": [0, 36], "directtcpsocketabort": [0, 36], "directtcpsocketclos": [0, 36], "requestwillbesentextrainfo": [0, 36], "associated_cooki": [0, 36], "connect_tim": [0, 36], "site_has_cookie_in_other_partit": [0, 36], "responsereceivedextrainfo": [0, 36], "blocked_cooki": [0, 36], "status_cod": [0, 36], "cookie_partition_kei": [0, 36], "cookie_partition_key_opaqu": [0, 36], "exempted_cooki": [0, 36], "responsereceivedearlyhint": [0, 36], "trusttokenoperationdon": [0, 36], "top_level_origin": [0, 36], "issuer_origin": [0, 36, 48], "issued_token_count": [0, 36], "policyupd": [0, 36], "subresourcewebbundlemetadatareceiv": [0, 36], "subresourcewebbundlemetadataerror": [0, 36], "subresourcewebbundleinnerresponsepars": [0, 36], "inner_request_id": [0, 36], "inner_request_url": [0, 36], "bundle_request_id": [0, 36], "subresourcewebbundleinnerresponseerror": [0, 36], "reportingapireportad": [0, 36], "report": [0, 4, 11, 12, 13, 17, 20, 21, 24, 26, 27, 33, 34, 36, 38, 39, 40, 42, 44, 48, 50, 52], "reportingapireportupd": [0, 36], "reportingapiendpointschangedfororigin": [0, 36], "endpoint": [0, 36, 38, 48], "overlai": [0, 1, 20, 38], "sourceorderconfig": [0, 37], "parent_outline_color": [0, 37], "child_outline_color": [0, 37], "gridhighlightconfig": [0, 37], "show_grid_extension_lin": [0, 37], "show_positive_line_numb": [0, 37], "show_negative_line_numb": [0, 37], "show_area_nam": [0, 37], "show_line_nam": [0, 37], "show_track_s": [0, 37], "grid_border_color": [0, 37], "cell_border_color": [0, 37], "row_line_color": [0, 37], "column_line_color": [0, 37], "grid_border_dash": [0, 37], "cell_border_dash": [0, 37], "row_line_dash": [0, 37], "column_line_dash": [0, 37], "row_gap_color": [0, 37], "row_hatch_color": [0, 37], "column_gap_color": [0, 37], "column_hatch_color": [0, 37], "area_border_color": [0, 37], "grid_background_color": [0, 37], "flexcontainerhighlightconfig": [0, 37], "container_bord": [0, 37], "line_separ": [0, 37], "item_separ": [0, 37], "main_distributed_spac": [0, 37], "cross_distributed_spac": [0, 37], "row_gap_spac": [0, 37], "column_gap_spac": [0, 37], "cross_align": [0, 37], "flexitemhighlightconfig": [0, 37], "base_size_box": [0, 37], "base_size_bord": [0, 37], "flexibility_arrow": [0, 37], "linestyl": [0, 37], "color": [0, 12, 16, 18, 20, 37, 38], "pattern": [0, 13, 24, 36, 37, 48], "boxstyl": [0, 37], "fill_color": [0, 37], "hatch_color": [0, 37], "contrastalgorithm": [0, 37], "aa": [0, 37], "aaa": [0, 4, 37], "apca": [0, 37], "highlightconfig": [0, 37], "show_info": [0, 37], "show_styl": [0, 37], "show_rul": [0, 37], "show_accessibility_info": [0, 37], "show_extension_lin": [0, 37], "content_color": [0, 37], "padding_color": [0, 37], "border_color": [0, 37], "margin_color": [0, 37], "event_target_color": [0, 37], "shape_color": [0, 37], "shape_margin_color": [0, 37], "css_grid_color": [0, 37], "color_format": [0, 37], "grid_highlight_config": [0, 37], "flex_container_highlight_config": [0, 37], "flex_item_highlight_config": [0, 37], "contrast_algorithm": [0, 37], "container_query_container_highlight_config": [0, 37], "colorformat": [0, 37], "rgb": [0, 37], "hsl": [0, 37], "hwb": [0, 37], "hex_": [0, 37], "gridnodehighlightconfig": [0, 37], "flexnodehighlightconfig": [0, 37], "scrollsnapcontainerhighlightconfig": [0, 37], "snapport_bord": [0, 37], "snap_area_bord": [0, 37], "scroll_margin_color": [0, 37], "scroll_padding_color": [0, 37], "scrollsnaphighlightconfig": [0, 37], "scroll_snap_container_highlight_config": [0, 37], "hingeconfig": [0, 37], "outline_color": [0, 37], "windowcontrolsoverlayconfig": [0, 37], "show_css": [0, 37], "selected_platform": [0, 37], "theme_color": [0, 37, 38], "containerqueryhighlightconfig": [0, 37], "containerquerycontainerhighlightconfig": [0, 37], "descendant_bord": [0, 37], "isolatedelementhighlightconfig": [0, 37], "isolation_mode_highlight_config": [0, 37], "isolationmodehighlightconfig": [0, 37], "resizer_color": [0, 37], "resizer_handle_color": [0, 37], "mask_color": [0, 37], "inspectmod": [0, 37], "search_for_nod": [0, 37], "search_for_ua_shadow_dom": [0, 37], "capture_area_screenshot": [0, 37], "show_dist": [0, 37], "get_grid_highlight_objects_for_test": [0, 37], "get_highlight_object_for_test": [0, 37], "get_source_order_highlight_object_for_test": [0, 37], "highlight_fram": [0, 37], "highlight_quad": [0, 37], "highlight_source_ord": [0, 37], "set_inspect_mod": [0, 37], "set_paused_in_debugger_messag": [0, 37], "set_show_ad_highlight": [0, 37], "set_show_container_query_overlai": [0, 37], "set_show_debug_bord": [0, 37], "set_show_flex_overlai": [0, 37], "set_show_fps_count": [0, 37], "set_show_grid_overlai": [0, 37], "set_show_hing": [0, 37], "set_show_hit_test_bord": [0, 37], "set_show_isolated_el": [0, 37], "set_show_layout_shift_region": [0, 37], "set_show_paint_rect": [0, 37], "set_show_scroll_bottleneck_rect": [0, 37], "set_show_scroll_snap_overlai": [0, 37], "set_show_viewport_size_on_res": [0, 37], "set_show_web_vit": [0, 37], "set_show_window_controls_overlai": [0, 37], "inspectnoderequest": [0, 37], "nodehighlightrequest": [0, 37], "screenshotrequest": [0, 37], "viewport": [0, 12, 16, 20, 29, 37, 38, 58], "inspectmodecancel": [0, 37], "page": [0, 1, 2, 3, 4, 8, 10, 12, 13, 16, 18, 20, 24, 29, 36, 37, 43, 44, 46, 48, 50, 55, 58, 59, 60], "frameid": [0, 2, 4, 5, 8, 12, 13, 16, 18, 24, 36, 37, 38, 40, 41, 44, 48, 50], "adframetyp": [0, 38], "child": [0, 2, 5, 12, 16, 18, 27, 37, 38, 42, 48, 50], "adframeexplan": [0, 38], "parent_is_ad": [0, 38], "created_by_ad_script": [0, 38], "matched_blocking_rul": [0, 38], "adframestatu": [0, 38], "ad_frame_typ": [0, 38], "explan": [0, 38, 46], "adscriptid": [0, 38], "debugger_id": [0, 38, 44], "securecontexttyp": [0, 38], "secure_localhost": [0, 38], "insecure_schem": [0, 38], "insecure_ancestor": [0, 38], "crossoriginisolatedcontexttyp": [0, 38], "isol": [0, 4, 13, 36, 37, 38, 42, 43, 44], "not_isol": [0, 38], "not_isolated_feature_dis": [0, 38], "gatedapifeatur": [0, 38], "shared_array_buff": [0, 38], "shared_array_buffers_transfer_allow": [0, 38], "performance_measure_memori": [0, 38], "performance_profil": [0, 38], "permissionspolicyfeatur": [0, 38], "all_screens_captur": [0, 38], "ambient_light_sensor": [0, 38], "attribution_report": [0, 38], "autoplai": [0, 38], "browsing_top": [0, 38], "camera": [0, 8, 38], "ch_dpr": [0, 38], "ch_device_memori": [0, 38], "ch_downlink": [0, 38], "ch_ect": [0, 38], "ch_prefers_color_schem": [0, 38], "ch_prefers_reduced_mot": [0, 38], "ch_prefers_reduced_transpar": [0, 38], "ch_rtt": [0, 38], "ch_save_data": [0, 38], "ch_ua": [0, 38], "ch_ua_arch": [0, 38], "ch_ua_bit": [0, 38], "ch_ua_high_entropy_valu": [0, 38], "ch_ua_platform": [0, 38], "ch_ua_model": [0, 38], "ch_ua_mobil": [0, 38], "ch_ua_form_factor": [0, 38], "ch_ua_full_vers": [0, 38], "ch_ua_full_version_list": [0, 38], "ch_ua_platform_vers": [0, 38], "ch_ua_wow64": [0, 38], "ch_viewport_height": [0, 38], "ch_viewport_width": [0, 38], "ch_width": [0, 38], "clipboard_read": [0, 38], "clipboard_writ": [0, 38], "compute_pressur": [0, 38], "controlled_fram": [0, 38], "cross_origin_isol": [0, 38], "deferred_fetch": [0, 38], "deferred_fetch_minim": [0, 38], "digital_credentials_get": [0, 38], "direct_socket": [0, 36, 38], "direct_sockets_priv": [0, 38], "document_domain": [0, 38], "encrypted_media": [0, 38], "execution_while_out_of_viewport": [0, 38], "execution_while_not_rend": [0, 38], "fenced_unpartitioned_storage_read": [0, 38], "focus_without_user_activ": [0, 38], "frobul": [0, 38], "gamepad": [0, 38], "hid": [0, 38], "identity_credentials_get": [0, 38], "interest_cohort": [0, 38], "join_ad_interest_group": [0, 38], "keyboard_map": [0, 38], "language_detector": [0, 38], "media_playback_while_not_vis": [0, 38], "microphon": [0, 38], "otp_credenti": [0, 38], "payment": [0, 38], "picture_in_pictur": [0, 38], "popin": [0, 38], "private_aggreg": [0, 38], "private_state_token_issu": [0, 38], "private_state_token_redempt": [0, 38], "publickey_credentials_cr": [0, 38], "publickey_credentials_get": [0, 38], "rewrit": [0, 38], "run_ad_auct": [0, 38], "screen_wake_lock": [0, 38], "serial": [0, 32, 38, 44, 48, 52], "shared_autofil": [0, 38], "shared_storag": [0, 38, 48], "shared_storage_select_url": [0, 38], "sub_app": [0, 38], "summar": [0, 38], "sync_xhr": [0, 38], "translat": [0, 38, 58], "unload": [0, 38], "usb": [0, 7, 38, 54], "usb_unrestrict": [0, 38], "vertical_scrol": [0, 38], "web_shar": [0, 38], "writer": [0, 38], "xr_spatial_track": [0, 38], "permissionspolicyblockreason": [0, 38], "iframe_attribut": [0, 38], "in_fenced_frame_tre": [0, 38], "in_isolated_app": [0, 38], "permissionspolicyblockloc": [0, 38], "block_reason": [0, 38], "permissionspolicyfeaturest": [0, 38], "origintrialtokenstatu": [0, 38], "not_support": [0, 38, 41], "insecur": [0, 38, 46, 60], "wrong_origin": [0, 38], "invalid_signatur": [0, 38], "malform": [0, 4, 38], "wrong_vers": [0, 38], "feature_dis": [0, 38], "token_dis": [0, 38], "feature_disabled_for_us": [0, 38], "unknown_tri": [0, 38], "origintrialstatu": [0, 38], "valid_token_not_provid": [0, 38], "os_not_support": [0, 38], "trial_not_allow": [0, 38], "origintrialusagerestrict": [0, 38], "subset": [0, 18, 38], "origintrialtoken": [0, 38], "match_sub_domain": [0, 38], "trial_nam": [0, 38], "expiry_tim": [0, 38], "is_third_parti": [0, 38], "usage_restrict": [0, 38], "origintrialtokenwithstatu": [0, 38], "raw_token_text": [0, 38], "parsed_token": [0, 38], "origintri": [0, 38], "tokens_with_statu": [0, 38], "securityorigindetail": [0, 38], "is_localhost": [0, 38], "domain_and_registri": [0, 38], "secure_context_typ": [0, 38], "cross_origin_isolated_context_typ": [0, 38], "gated_api_featur": [0, 38], "security_origin_detail": [0, 38], "unreachable_url": [0, 38], "ad_frame_statu": [0, 38], "frameresourc": [0, 38], "content_s": [0, 38], "frameresourcetre": [0, 38], "child_fram": [0, 38], "frametre": [0, 38], "scriptidentifi": [0, 38], "transitiontyp": [0, 38], "link": [0, 4, 12, 16, 29, 36, 38, 41, 43, 48, 58], "address_bar": [0, 38], "auto_bookmark": [0, 38], "auto_subfram": [0, 38], "manual_subfram": [0, 38], "gener": [0, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 58], "auto_toplevel": [0, 38], "form_submit": [0, 38], "keyword": [0, 12, 38], "keyword_gener": [0, 38], "navigationentri": [0, 38], "user_typed_url": [0, 38], "transition_typ": [0, 38], "screencastframemetadata": [0, 38], "offset_top": [0, 38], "page_scale_factor": [0, 20, 38], "device_width": [0, 38], "device_height": [0, 38], "alert": [0, 38, 56], "confirm": [0, 38], "beforeunload": [0, 38, 50], "appmanifesterror": [0, 38], "appmanifestparsedproperti": [0, 38], "layoutviewport": [0, 38], "page_x": [0, 38], "page_i": [0, 38], "client_width": [0, 38], "client_height": [0, 38], "visualviewport": [0, 38, 58], "scale": [0, 20, 29, 32, 38, 56], "zoom": [0, 29, 38], "fontfamili": [0, 38], "standard": [0, 24, 28, 38], "fix": [0, 37, 38, 46, 56], "serif": [0, 38], "sans_serif": [0, 38], "cursiv": [0, 38], "fantasi": [0, 38], "math": [0, 38], "scriptfontfamili": [0, 38], "fontsiz": [0, 38], "clientnavigationreason": [0, 38], "anchor_click": [0, 38], "form_submission_get": [0, 38], "form_submission_post": [0, 38], "http_header_refresh": [0, 38], "initial_frame_navig": [0, 38], "meta_tag_refresh": [0, 38], "page_block_interstiti": [0, 38], "script_initi": [0, 38], "clientnavigationdisposit": [0, 38], "current_tab": [0, 38], "new_tab": [0, 38, 55, 58, 59, 60], "new_window": [0, 38, 50, 55, 58, 59, 60], "installabilityerrorargu": [0, 38], "installabilityerror": [0, 38], "error_id": [0, 38], "error_argu": [0, 38], "referrerpolici": [0, 38], "no_referr": [0, 38], "no_referrer_when_downgrad": [0, 38], "origin_when_cross_origin": [0, 38], "strict_origin": [0, 38], "strict_origin_when_cross_origin": [0, 38], "unsafe_url": [0, 38], "compilationcacheparam": [0, 38], "eager": [0, 38], "filefilt": [0, 38], "filehandl": [0, 38, 43], "launch_typ": [0, 38], "icon": [0, 16, 38], "imageresourc": [0, 38], "launchhandl": [0, 38], "client_mod": [0, 38], "protocolhandl": [0, 38], "relatedappl": [0, 38], "scopeextens": [0, 38], "has_origin_wildcard": [0, 38], "screenshot": [0, 20, 26, 37, 38, 56, 58], "form_factor": [0, 38], "sharetarget": [0, 38], "enctyp": [0, 38], "shortcut": [0, 29, 38], "webappmanifest": [0, 38], "background_color": [0, 38], "dir_": [0, 38], "displai": [0, 5, 12, 14, 20, 24, 26, 36, 37, 38, 41, 56], "display_overrid": [0, 38], "file_handl": [0, 38], "lang": [0, 38, 57, 59], "launch_handl": [0, 38], "prefer_related_appl": [0, 38], "protocol_handl": [0, 38], "related_appl": [0, 38], "scope_extens": [0, 38], "share_target": [0, 38], "short_nam": [0, 38], "start_url": [0, 38], "autoresponsemod": [0, 38], "auto_accept": [0, 38], "auto_reject": [0, 38], "auto_opt_out": [0, 38], "navigationtyp": [0, 38], "navig": [0, 4, 18, 20, 36, 38, 41, 44, 48, 50, 55, 58, 59], "back_forward_cache_restor": [0, 38], "backforwardcachenotrestoredreason": [0, 38], "not_primary_main_fram": [0, 38], "back_forward_cache_dis": [0, 38], "related_active_contents_exist": [0, 38], "http_status_not_ok": [0, 38], "scheme_not_http_or_http": [0, 38], "was_granted_media_access": [0, 38], "disable_for_render_frame_host_cal": [0, 38], "domain_not_allow": [0, 38], "http_method_not_get": [0, 38], "subframe_is_navig": [0, 38], "cache_limit": [0, 38], "java_script_execut": [0, 38], "renderer_process_kil": [0, 38, 41], "renderer_process_crash": [0, 38, 41], "scheduler_tracked_feature_us": [0, 38], "conflicting_browsing_inst": [0, 38], "cache_flush": [0, 38], "service_worker_version_activ": [0, 38], "session_restor": [0, 38], "service_worker_post_messag": [0, 38], "entered_back_forward_cache_before_service_worker_host_ad": [0, 38], "render_frame_host_reused_same_sit": [0, 38], "render_frame_host_reused_cross_sit": [0, 38], "service_worker_claim": [0, 38], "ignore_event_and_evict": [0, 38], "have_inner_cont": [0, 38], "timeout_putting_in_cach": [0, 38], "back_forward_cache_disabled_by_low_memori": [0, 38], "back_forward_cache_disabled_by_command_lin": [0, 38], "network_request_datapipe_drained_as_bytes_consum": [0, 38], "network_request_redirect": [0, 38], "network_request_timeout": [0, 38], "network_exceeds_buffer_limit": [0, 38], "navigation_cancelled_while_restor": [0, 38], "not_most_recent_navigation_entri": [0, 38], "back_forward_cache_disabled_for_prerend": [0, 38], "user_agent_override_diff": [0, 38], "foreground_cache_limit": [0, 38], "browsing_instance_not_swap": [0, 38], "back_forward_cache_disabled_for_deleg": [0, 38], "unload_handler_exists_in_main_fram": [0, 38], "unload_handler_exists_in_sub_fram": [0, 38], "service_worker_unregistr": [0, 38], "cache_control_no_stor": [0, 38], "cache_control_no_store_cookie_modifi": [0, 38], "cache_control_no_store_http_only_cookie_modifi": [0, 38], "no_response_head": [0, 38], "activation_navigations_disallowed_for_bug1234857": [0, 38], "error_docu": [0, 38], "fenced_frames_embedd": [0, 38], "cookie_dis": [0, 38], "http_auth_requir": [0, 38], "cookie_flush": [0, 38], "broadcast_channel_on_messag": [0, 38], "web_view_settings_chang": [0, 38], "web_view_java_script_object_chang": [0, 38], "web_view_message_listener_inject": [0, 38], "web_view_safe_browsing_allowlist_chang": [0, 38], "web_view_document_start_javascript_chang": [0, 38], "web_transport": [0, 38], "web_rtc": [0, 38], "main_resource_has_cache_control_no_stor": [0, 38], "main_resource_has_cache_control_no_cach": [0, 38], "subresource_has_cache_control_no_stor": [0, 38], "subresource_has_cache_control_no_cach": [0, 38], "contains_plugin": [0, 38], "document_load": [0, 38], "outstanding_network_request_oth": [0, 38], "requested_midi_permiss": [0, 38], "requested_audio_capture_permiss": [0, 38], "requested_video_capture_permiss": [0, 38], "requested_back_forward_cache_blocked_sensor": [0, 38], "requested_background_work_permiss": [0, 38], "broadcast_channel": [0, 38], "web_xr": [0, 38], "web_lock": [0, 38], "web_hid": [0, 38], "requested_storage_access_gr": [0, 38], "web_nfc": [0, 38], "outstanding_network_request_fetch": [0, 38], "outstanding_network_request_xhr": [0, 38], "app_bann": [0, 38], "print": [0, 4, 34, 38, 58, 59, 60], "web_databas": [0, 38], "speech_recogn": [0, 38], "idle_manag": [0, 38], "payment_manag": [0, 38], "speech_synthesi": [0, 38], "web_otp_servic": [0, 38], "outstanding_network_request_direct_socket": [0, 38], "injected_javascript": [0, 38], "injected_style_sheet": [0, 38], "keepalive_request": [0, 38], "indexed_db_ev": [0, 38], "dummi": [0, 38], "js_network_request_received_cache_control_no_store_resourc": [0, 38], "web_rtc_sticki": [0, 38], "web_transport_sticki": [0, 38], "web_socket_sticki": [0, 38], "live_media_stream_track": [0, 38], "unload_handl": [0, 38], "parser_abort": [0, 38], "content_security_handl": [0, 38], "content_web_authentication_api": [0, 38], "content_file_choos": [0, 38], "content_seri": [0, 38], "content_file_system_access": [0, 38], "content_media_devices_dispatcher_host": [0, 38], "content_web_bluetooth": [0, 38], "content_web_usb": [0, 38], "content_media_session_servic": [0, 38], "content_screen_read": [0, 38], "content_discard": [0, 38], "embedder_popup_blocker_tab_help": [0, 38], "embedder_safe_browsing_triggered_popup_block": [0, 38], "embedder_safe_browsing_threat_detail": [0, 38], "embedder_app_banner_manag": [0, 38], "embedder_dom_distiller_viewer_sourc": [0, 38], "embedder_dom_distiller_self_deleting_request_deleg": [0, 38], "embedder_oom_intervention_tab_help": [0, 38], "embedder_offline_pag": [0, 38], "embedder_chrome_password_manager_client_bind_credential_manag": [0, 38], "embedder_permission_request_manag": [0, 38], "embedder_modal_dialog": [0, 38], "embedder_extens": [0, 38], "embedder_extension_messag": [0, 38], "embedder_extension_messaging_for_open_port": [0, 38], "embedder_extension_sent_message_to_cached_fram": [0, 38], "requested_by_web_view_cli": [0, 38], "post_message_by_web_view_cli": [0, 38], "cache_control_no_store_device_bound_session_termin": [0, 38], "cache_limit_prun": [0, 38], "backforwardcachenotrestoredreasontyp": [0, 38], "support_pend": [0, 38], "page_support_need": [0, 38], "circumstanti": [0, 38], "backforwardcacheblockingdetail": [0, 38], "backforwardcachenotrestoredexplan": [0, 38], "context": [0, 2, 4, 8, 12, 13, 16, 17, 18, 23, 29, 34, 36, 38, 44, 48, 50, 53, 55, 59], "backforwardcachenotrestoredexplanationtre": [0, 38], "add_compilation_cach": [0, 38], "add_script_to_evaluate_on_load": [0, 38], "add_script_to_evaluate_on_new_docu": [0, 38], "capture_screenshot": [0, 38], "clear_compilation_cach": [0, 38], "create_isolated_world": [0, 38], "generate_test_report": [0, 38], "get_ad_script_id": [0, 38], "get_app_id": [0, 38], "get_app_manifest": [0, 38], "get_installability_error": [0, 38], "get_layout_metr": [0, 38], "get_manifest_icon": [0, 38], "get_navigation_histori": [0, 38], "get_origin_tri": [0, 38], "get_permissions_policy_st": [0, 38], "get_resource_cont": [0, 38], "get_resource_tre": [0, 38], "handle_java_script_dialog": [0, 38], "navigate_to_history_entri": [0, 38], "print_to_pdf": [0, 38], "produce_compilation_cach": [0, 38], "remove_script_to_evaluate_on_load": [0, 38], "remove_script_to_evaluate_on_new_docu": [0, 38], "reset_navigation_histori": [0, 38], "screencast_frame_ack": [0, 38], "search_in_resourc": [0, 38], "set_ad_blocking_en": [0, 38], "set_bypass_csp": [0, 38], "set_document_cont": [0, 38], "set_font_famili": [0, 38], "set_font_s": [0, 38], "set_intercept_file_chooser_dialog": [0, 38], "set_lifecycle_events_en": [0, 38], "set_prerendering_allow": [0, 38], "set_rph_registration_mod": [0, 38], "set_spc_transaction_mod": [0, 38], "set_web_lifecycle_st": [0, 38], "start_screencast": [0, 38], "stop_load": [0, 38], "stop_screencast": [0, 38], "wait_for_debugg": [0, 38], "domcontenteventfir": [0, 38], "filechooseropen": [0, 38], "mode": [0, 3, 4, 13, 16, 20, 26, 32, 37, 38, 43, 44, 50, 52, 58, 60], "frameattach": [0, 38], "parent_frame_id": [0, 38], "frameclearedschedulednavig": [0, 38], "framedetach": [0, 38], "framesubtreewillbedetach": [0, 38], "framenavig": [0, 38], "documentopen": [0, 38], "frameres": [0, 38], "framestartednavig": [0, 38], "navigation_typ": [0, 38], "framerequestednavig": [0, 38], "disposit": [0, 38], "frameschedulednavig": [0, 38], "framestartedload": [0, 38], "framestoppedload": [0, 38], "interstitialhidden": [0, 38], "interstitialshown": [0, 38], "javascriptdialogclos": [0, 38], "result": [0, 12, 13, 16, 20, 22, 24, 26, 27, 32, 36, 37, 38, 42, 43, 44, 48, 52, 58], "user_input": [0, 38], "javascriptdialogopen": [0, 38], "has_browser_handl": [0, 38], "default_prompt": [0, 38], "lifecycleev": [0, 38], "backforwardcachenotus": [0, 38], "not_restored_explan": [0, 38], "not_restored_explanations_tre": [0, 38], "loadeventfir": [0, 38], "navigatedwithindocu": [0, 38], "screencastfram": [0, 38], "metadata": [0, 4, 20, 28, 38, 48], "session_id": [0, 38, 50], "screencastvisibilitychang": [0, 38], "visibl": [0, 3, 12, 16, 20, 26, 32, 38, 44, 46, 55, 56], "windowopen": [0, 38], "window_nam": [0, 38], "window_featur": [0, 38], "user_gestur": [0, 38, 44], "compilationcacheproduc": [0, 38], "metric": [0, 20, 38, 39], "get_metr": [0, 39], "set_time_domain": [0, 39], "performancetimelin": [0, 1], "largestcontentfulpaint": [0, 40], "render_tim": [0, 40], "load_tim": [0, 40], "element_id": [0, 40], "layoutshiftattribut": [0, 40], "previous_rect": [0, 40], "current_rect": [0, 40], "layoutshift": [0, 40], "had_recent_input": [0, 40], "last_input_tim": [0, 40], "timelineev": [0, 40], "lcp_detail": [0, 40], "layout_shift_detail": [0, 40], "timelineeventad": [0, 40], "preload": [0, 1, 36], "rulesetid": [0, 41], "ruleset": [0, 41], "source_text": [0, 41], "ruleseterrortyp": [0, 41], "source_is_not_json_object": [0, 41], "invalid_rules_skip": [0, 41], "speculationact": [0, 41], "prerend": [0, 38, 41, 50], "speculationtargethint": [0, 41], "blank": [0, 41, 50], "self": [0, 37, 41, 58], "preloadingattemptkei": [0, 41], "target_hint": [0, 41], "preloadingattemptsourc": [0, 41], "rule_set_id": [0, 41], "preloadpipelineid": [0, 41], "prerenderfinalstatu": [0, 41], "destroi": [0, 41, 44, 50, 53], "low_end_devic": [0, 41], "invalid_scheme_redirect": [0, 41], "invalid_scheme_navig": [0, 41], "navigation_request_blocked_by_csp": [0, 41], "main_frame_navig": [0, 41], "mojo_binder_polici": [0, 41], "trigger_destroi": [0, 41], "navigation_not_commit": [0, 41], "navigation_bad_http_statu": [0, 41], "client_cert_request": [0, 41], "navigation_request_network_error": [0, 41], "cancel_all_hosts_for_test": [0, 41], "did_fail_load": [0, 41], "ssl_certificate_error": [0, 41], "login_auth_request": [0, 41], "ua_change_requires_reload": [0, 41], "audio_output_device_request": [0, 41], "trigger_background": [0, 41], "memory_limit_exceed": [0, 41], "data_saver_en": [0, 41], "trigger_url_has_effective_url": [0, 41], "activated_before_start": [0, 41], "inactive_page_restrict": [0, 41], "start_fail": [0, 41], "timeout_background": [0, 41], "cross_site_redirect_in_initial_navig": [0, 41], "cross_site_navigation_in_initial_navig": [0, 41], "same_site_cross_origin_redirect_not_opt_in_in_initial_navig": [0, 41], "same_site_cross_origin_navigation_not_opt_in_in_initial_navig": [0, 41], "activation_navigation_parameter_mismatch": [0, 41], "activated_in_background": [0, 41], "embedder_host_disallow": [0, 41], "activation_navigation_destroyed_before_success": [0, 41], "tab_closed_by_user_gestur": [0, 41], "tab_closed_without_user_gestur": [0, 41], "primary_main_frame_renderer_process_crash": [0, 41], "primary_main_frame_renderer_process_kil": [0, 41], "activation_frame_policy_not_compat": [0, 41], "preloading_dis": [0, 41], "battery_saver_en": [0, 41], "activated_during_main_frame_navig": [0, 41], "preloading_unsupported_by_web_cont": [0, 41], "cross_site_redirect_in_main_frame_navig": [0, 41], "cross_site_navigation_in_main_frame_navig": [0, 41], "same_site_cross_origin_redirect_not_opt_in_in_main_frame_navig": [0, 41], "same_site_cross_origin_navigation_not_opt_in_in_main_frame_navig": [0, 41], "memory_pressure_on_trigg": [0, 41], "memory_pressure_after_trigg": [0, 41], "prerendering_disabled_by_dev_tool": [0, 41], "speculation_rule_remov": [0, 41], "activated_with_auxiliary_browsing_context": [0, 41], "max_num_of_running_eager_prerenders_exceed": [0, 41], "max_num_of_running_non_eager_prerenders_exceed": [0, 41], "max_num_of_running_embedder_prerenders_exceed": [0, 41], "prerendering_url_has_effective_url": [0, 41], "redirected_prerendering_url_has_effective_url": [0, 41], "activation_url_has_effective_url": [0, 41], "java_script_interface_ad": [0, 41], "java_script_interface_remov": [0, 41], "all_prerendering_cancel": [0, 41], "window_clos": [0, 41], "slow_network": [0, 41], "other_prerendered_page_activ": [0, 41], "v8_optimizer_dis": [0, 41], "prerender_failed_during_prefetch": [0, 41], "browsing_data_remov": [0, 41], "preloadingstatu": [0, 41], "readi": [0, 4, 41], "failur": [0, 4, 36, 41], "prefetchstatu": [0, 41], "prefetch_allow": [0, 41], "prefetch_failed_ineligible_redirect": [0, 41], "prefetch_failed_invalid_redirect": [0, 41], "prefetch_failed_mime_not_support": [0, 41], "prefetch_failed_net_error": [0, 41], "prefetch_failed_non2_xx": [0, 41], "prefetch_evicted_after_browsing_data_remov": [0, 41], "prefetch_evicted_after_candidate_remov": [0, 41], "prefetch_evicted_for_newer_prefetch": [0, 41], "prefetch_heldback": [0, 41], "prefetch_ineligible_retry_aft": [0, 41], "prefetch_is_privacy_decoi": [0, 41], "prefetch_is_stal": [0, 41], "prefetch_not_eligible_browser_context_off_the_record": [0, 41], "prefetch_not_eligible_data_saver_en": [0, 41], "prefetch_not_eligible_existing_proxi": [0, 41], "prefetch_not_eligible_host_is_non_uniqu": [0, 41], "prefetch_not_eligible_non_default_storage_partit": [0, 41], "prefetch_not_eligible_same_site_cross_origin_prefetch_required_proxi": [0, 41], "prefetch_not_eligible_scheme_is_not_http": [0, 41], "prefetch_not_eligible_user_has_cooki": [0, 41], "prefetch_not_eligible_user_has_service_work": [0, 41], "prefetch_not_eligible_user_has_service_worker_no_fetch_handl": [0, 41], "prefetch_not_eligible_redirect_from_service_work": [0, 41], "prefetch_not_eligible_redirect_to_service_work": [0, 41], "prefetch_not_eligible_battery_saver_en": [0, 41], "prefetch_not_eligible_preloading_dis": [0, 41], "prefetch_not_finished_in_tim": [0, 41], "prefetch_not_start": [0, 41], "prefetch_not_used_cookies_chang": [0, 41], "prefetch_proxy_not_avail": [0, 41], "prefetch_response_us": [0, 41], "prefetch_successful_but_not_us": [0, 41], "prefetch_not_used_probe_fail": [0, 41], "prerendermismatchedhead": [0, 41], "header_nam": [0, 41], "activation_valu": [0, 41], "rulesetupd": [0, 41], "rule_set": [0, 41], "rulesetremov": [0, 41], "preloadenabledstateupd": [0, 41], "disabled_by_prefer": [0, 41], "disabled_by_data_sav": [0, 41], "disabled_by_battery_sav": [0, 41], "disabled_by_holdback_prefetch_speculation_rul": [0, 41], "disabled_by_holdback_prerender_speculation_rul": [0, 41], "prefetchstatusupd": [0, 41], "pipeline_id": [0, 41], "initiating_frame_id": [0, 41], "prefetch_url": [0, 41], "prefetch_statu": [0, 41], "prerenderstatusupd": [0, 41], "prerender_statu": [0, 41], "disallowed_mojo_interfac": [0, 41], "mismatched_head": [0, 41], "preloadingattemptsourcesupd": [0, 41], "preloading_attempt_sourc": [0, 41], "profilenod": [0, 42], "hit_count": [0, 42], "deopt_reason": [0, 42], "position_tick": [0, 42], "end_tim": [0, 42], "time_delta": [0, 42], "positiontickinfo": [0, 42], "tick": [0, 36, 42], "coveragerang": [0, 42], "functioncoverag": [0, 42], "rang": [0, 12, 13, 16, 26, 28, 29, 38, 42, 52], "is_block_coverag": [0, 42], "scriptcoverag": [0, 42], "get_best_effort_coverag": [0, 42], "set_sampling_interv": [0, 42], "start_precise_coverag": [0, 42], "stop_precise_coverag": [0, 42], "take_precise_coverag": [0, 42], "consoleprofilefinish": [0, 42], "consoleprofilestart": [0, 42], "precisecoveragedeltaupd": [0, 42], "occas": [0, 42], "pwa": [0, 1, 37, 38], "filehandleraccept": [0, 43], "media_typ": [0, 43], "file_extens": [0, 43], "display_nam": [0, 43], "displaymod": [0, 43], "standalon": [0, 43], "change_app_user_set": [0, 43], "get_os_app_st": [0, 43], "launch": [0, 38, 43, 49, 55], "launch_files_in_app": [0, 43], "open_current_page_in_app": [0, 43], "runtim": [0, 1, 11, 13, 38, 50], "scriptid": [0, 4, 13, 17, 38, 42, 44], "serializationopt": [0, 44], "max_depth": [0, 13, 44], "additional_paramet": [0, 44], "deepserializedvalu": [0, 44], "weak_local_object_refer": [0, 44], "remoteobjectid": [0, 2, 13, 16, 17, 27, 31, 37, 44, 56], "unserializablevalu": [0, 44], "remoteobject": [0, 3, 13, 16, 17, 27, 28, 33, 44, 56, 58], "subtyp": [0, 2, 44, 50], "class_nam": [0, 44], "unserializable_valu": [0, 44], "deep_serialized_valu": [0, 44], "preview": [0, 13, 44], "custom_preview": [0, 44], "custompreview": [0, 44], "body_getter_id": [0, 44], "objectpreview": [0, 44], "overflow": [0, 44], "propertypreview": [0, 44], "value_preview": [0, 44], "entrypreview": [0, 44], "propertydescriptor": [0, 44], "configur": [0, 7, 8, 20, 33, 36, 37, 38, 44, 52, 54], "enumer": [0, 12, 13, 44], "writabl": [0, 44], "set_": [0, 44], "was_thrown": [0, 44], "is_own": [0, 44], "symbol": [0, 13, 16, 27, 44], "internalpropertydescriptor": [0, 44], "privatepropertydescriptor": [0, 44], "callargu": [0, 13, 44], "executioncontextid": [0, 13, 16, 38, 44], "executioncontextdescript": [0, 38, 44], "unique_id": [0, 13, 44], "aux_data": [0, 44], "exceptiondetail": [0, 13, 44, 58], "exception_id": [0, 44], "except": [0, 4, 13, 29, 36, 44, 48, 58], "exception_meta_data": [0, 44], "timedelta": [0, 13, 44], "stacktrac": [0, 13, 16, 33, 36, 38, 44], "uniquedebuggerid": [0, 13, 38, 44], "stacktraceid": [0, 13, 44], "add_bind": [0, 44], "await_promis": [0, 44, 58], "call_function_on": [0, 44], "compile_script": [0, 44], "discard_console_entri": [0, 44], "get_exception_detail": [0, 44], "get_heap_usag": [0, 44], "get_isolate_id": [0, 44], "get_properti": [0, 44], "global_lexical_scope_nam": [0, 44], "query_object": [0, 44], "release_object": [0, 44], "release_object_group": [0, 44], "remove_bind": [0, 44], "run_if_waiting_for_debugg": [0, 44], "run_script": [0, 44], "set_custom_object_formatter_en": [0, 44], "set_max_call_stack_size_to_captur": [0, 44], "terminate_execut": [0, 44], "bindingcal": [0, 44], "payload": [0, 36, 44], "consoleapical": [0, 44], "exceptionrevok": [0, 44], "exceptionthrown": [0, 44], "exception_detail": [0, 44], "executioncontextcr": [0, 44], "executioncontextdestroi": [0, 44], "execution_context_unique_id": [0, 44], "executioncontextsclear": [0, 44], "inspectrequest": [0, 44], "hint": [0, 4, 20, 36, 38, 41, 44, 56], "schema": [0, 1], "get_domain": [0, 45], "certificateid": [0, 36, 46], "mixedcontenttyp": [0, 36, 46], "blockabl": [0, 46], "optionally_block": [0, 46], "securityst": [0, 36, 46], "neutral": [0, 46], "insecure_broken": [0, 46], "certificatesecurityst": [0, 46], "certificate_has_weak_signatur": [0, 46], "certificate_has_sha1_signatur": [0, 46], "modern_ssl": [0, 46], "obsolete_ssl_protocol": [0, 46], "obsolete_ssl_key_exchang": [0, 46], "obsolete_ssl_ciph": [0, 46], "obsolete_ssl_signatur": [0, 46], "certificate_network_error": [0, 46], "safetytipstatu": [0, 46], "bad_reput": [0, 46], "lookalik": [0, 46, 58], "safetytipinfo": [0, 46], "safety_tip_statu": [0, 46], "safe_url": [0, 46], "visiblesecurityst": [0, 46], "security_state_issue_id": [0, 46], "certificate_security_st": [0, 46], "safety_tip_info": [0, 46], "securitystateexplan": [0, 46], "summari": [0, 46], "recommend": [0, 4, 38, 46, 54, 59], "insecurecontentstatu": [0, 46], "ran_mixed_cont": [0, 46], "displayed_mixed_cont": [0, 46], "contained_mixed_form": [0, 46], "ran_content_with_cert_error": [0, 46], "displayed_content_with_cert_error": [0, 46], "ran_insecure_content_styl": [0, 46], "displayed_insecure_content_styl": [0, 46], "certificateerroract": [0, 46], "continu": [0, 13, 20, 24, 36, 46, 58, 59, 60], "handle_certificate_error": [0, 46], "set_ignore_certificate_error": [0, 46], "set_override_certificate_error": [0, 46], "certificateerror": [0, 46], "visiblesecuritystatechang": [0, 46], "visible_security_st": [0, 46], "securitystatechang": [0, 46], "scheme_is_cryptograph": [0, 46], "insecure_content_statu": [0, 46], "servicework": [0, 1, 4, 36, 58], "registrationid": [0, 6, 47], "serviceworkerregistr": [0, 47], "registration_id": [0, 47], "scope_url": [0, 47], "is_delet": [0, 47], "serviceworkerversionrunningstatu": [0, 47], "serviceworkerversionstatu": [0, 47], "new": [0, 2, 4, 6, 8, 11, 12, 13, 16, 18, 20, 26, 27, 33, 34, 36, 38, 42, 43, 44, 47, 50, 53, 55, 57, 58], "redund": [0, 47], "serviceworkervers": [0, 47], "version_id": [0, 47], "script_url": [0, 47], "running_statu": [0, 47], "script_last_modifi": [0, 47], "script_response_tim": [0, 47], "controlled_cli": [0, 47], "target_id": [0, 8, 47, 50], "router_rul": [0, 47], "serviceworkererrormessag": [0, 47], "deliver_push_messag": [0, 47], "dispatch_periodic_sync_ev": [0, 47], "dispatch_sync_ev": [0, 47], "inspect_work": [0, 47], "set_force_update_on_page_load": [0, 47], "skip_wait": [0, 47], "start_work": [0, 47], "stop_all_work": [0, 47], "stop_work": [0, 47], "unregist": [0, 47, 48], "update_registr": [0, 47], "workererrorreport": [0, 47], "workerregistrationupd": [0, 47], "registr": [0, 4, 12, 47, 48], "workerversionupd": [0, 47], "storag": [0, 1, 6, 9, 19, 22, 25, 28, 31, 36, 38, 44, 54, 58], "storagetyp": [0, 48], "file_system": [0, 48], "local_storag": [0, 48], "shader_cach": [0, 48], "websql": [0, 48], "interest_group": [0, 48], "all_": [0, 48], "usagefortyp": [0, 48], "storage_typ": [0, 48], "trusttoken": [0, 36, 48], "interestgroupauctionid": [0, 48], "interestgroupaccesstyp": [0, 48], "join": [0, 34, 38, 48, 59, 60], "leav": [0, 8, 13, 48, 59], "bid": [0, 48], "win": [0, 48], "additional_bid": [0, 48], "additional_bid_win": [0, 48], "top_level_bid": [0, 48], "top_level_additional_bid": [0, 48], "interestgroupauctioneventtyp": [0, 48], "config_resolv": [0, 48], "interestgroupauctionfetchtyp": [0, 48], "bidder_j": [0, 48], "bidder_wasm": [0, 48], "seller_j": [0, 48], "bidder_trusted_sign": [0, 48], "seller_trusted_sign": [0, 48], "sharedstorageaccesstyp": [0, 48], "document_add_modul": [0, 48], "document_select_url": [0, 48], "document_run": [0, 48], "document_set": [0, 48], "document_append": [0, 48], "document_delet": [0, 48], "document_clear": [0, 48], "document_get": [0, 48], "worklet_set": [0, 48], "worklet_append": [0, 48], "worklet_delet": [0, 48], "worklet_clear": [0, 48], "worklet_get": [0, 48], "worklet_kei": [0, 48], "worklet_entri": [0, 48], "worklet_length": [0, 48], "worklet_remaining_budget": [0, 48], "header_set": [0, 48], "header_append": [0, 48], "header_delet": [0, 48], "header_clear": [0, 48], "sharedstorageentri": [0, 48], "sharedstoragemetadata": [0, 48], "creation_tim": [0, 48], "remaining_budget": [0, 48], "bytes_us": [0, 48], "sharedstoragereportingmetadata": [0, 48], "event_typ": [0, 40, 48], "reporting_url": [0, 48], "sharedstorageurlwithmetadata": [0, 48], "reporting_metadata": [0, 48], "sharedstorageaccessparam": [0, 48], "script_source_url": [0, 48], "operation_nam": [0, 48], "serialized_data": [0, 48], "urls_with_metadata": [0, 48], "ignore_if_pres": [0, 48], "storagebucketsdur": [0, 48], "relax": [0, 48], "storagebucket": [0, 9, 28, 48], "storagebucketinfo": [0, 48], "quota": [0, 48], "persist": [0, 37, 44, 48], "durabl": [0, 48], "attributionreportingsourcetyp": [0, 48], "unsignedint64asbase10": [0, 48], "unsignedint128asbase16": [0, 48], "signedint64asbase10": [0, 48], "attributionreportingfilterdataentri": [0, 48], "attributionreportingfilterconfig": [0, 48], "filter_valu": [0, 48], "lookback_window": [0, 48], "attributionreportingfilterpair": [0, 48], "filter": [0, 16, 18, 22, 24, 36, 38, 40, 41, 48, 50, 52], "not_filt": [0, 48], "attributionreportingaggregationkeysentri": [0, 48], "attributionreportingeventreportwindow": [0, 48], "attributionreportingtriggerspec": [0, 48], "trigger_data": [0, 48], "event_report_window": [0, 48], "attributionreportingtriggerdatamatch": [0, 48], "exact": [0, 18, 36, 48], "modulu": [0, 48], "attributionreportingaggregatabledebugreportingdata": [0, 48], "key_piec": [0, 48], "attributionreportingaggregatabledebugreportingconfig": [0, 48], "debug_data": [0, 48], "budget": [0, 20, 48], "aggregation_coordinator_origin": [0, 48], "attributionscopesdata": [0, 48], "limit": [0, 4, 13, 36, 48], "max_event_st": [0, 48], "attributionreportingsourceregistr": [0, 48], "expiri": [0, 5, 48], "trigger_spec": [0, 48], "aggregatable_report_window": [0, 48], "source_origin": [0, 48], "reporting_origin": [0, 48], "destination_sit": [0, 48], "filter_data": [0, 48], "aggregation_kei": [0, 48], "trigger_data_match": [0, 48], "destination_limit_prior": [0, 48], "aggregatable_debug_reporting_config": [0, 48], "max_event_level_report": [0, 48], "debug_kei": [0, 48], "scopes_data": [0, 48], "attributionreportingsourceregistrationresult": [0, 48], "internal_error": [0, 48], "insufficient_source_capac": [0, 48], "insufficient_unique_destination_capac": [0, 48], "excessive_reporting_origin": [0, 48], "prohibited_by_browser_polici": [0, 48], "success_nois": [0, 48], "destination_reporting_limit_reach": [0, 48], "destination_global_limit_reach": [0, 48], "destination_both_limits_reach": [0, 48], "reporting_origins_per_site_limit_reach": [0, 48], "exceeds_max_channel_capac": [0, 48], "exceeds_max_scopes_channel_capac": [0, 48], "exceeds_max_trigger_state_cardin": [0, 48], "exceeds_max_event_states_limit": [0, 48], "destination_per_day_reporting_limit_reach": [0, 48], "attributionreportingsourceregistrationtimeconfig": [0, 48], "exclud": [0, 13, 27, 38, 48, 50, 52], "attributionreportingaggregatablevaluedictentri": [0, 48], "filtering_id": [0, 48], "attributionreportingaggregatablevalueentri": [0, 48], "attributionreportingeventtriggerdata": [0, 48], "dedup_kei": [0, 48], "attributionreportingaggregatabletriggerdata": [0, 48], "source_kei": [0, 48], "attributionreportingaggregatablededupkei": [0, 48], "attributionreportingtriggerregistr": [0, 48], "aggregatable_dedup_kei": [0, 48], "event_trigger_data": [0, 48], "aggregatable_trigger_data": [0, 48], "aggregatable_valu": [0, 48], "aggregatable_filtering_id_max_byt": [0, 48], "debug_report": [0, 48], "source_registration_time_config": [0, 48], "trigger_context_id": [0, 48], "attributionreportingeventlevelresult": [0, 48], "success_dropped_lower_prior": [0, 48], "no_capacity_for_attribution_destin": [0, 48], "no_matching_sourc": [0, 48], "dedupl": [0, 48], "excessive_attribut": [0, 48], "priority_too_low": [0, 48], "never_attributed_sourc": [0, 48], "no_matching_source_filter_data": [0, 48], "no_matching_configur": [0, 48], "excessive_report": [0, 48], "falsely_attributed_sourc": [0, 48], "report_window_pass": [0, 48], "not_regist": [0, 48], "report_window_not_start": [0, 48], "no_matching_trigger_data": [0, 48], "attributionreportingaggregatableresult": [0, 48], "no_histogram": [0, 48], "insufficient_budget": [0, 48], "insufficient_named_budget": [0, 48], "relatedwebsiteset": [0, 48], "primary_sit": [0, 48], "associated_sit": [0, 48], "service_sit": [0, 48], "clear_cooki": [0, 48], "clear_data_for_origin": [0, 48], "clear_data_for_storage_kei": [0, 48], "clear_shared_storage_entri": [0, 48], "clear_trust_token": [0, 48], "delete_shared_storage_entri": [0, 48], "delete_storage_bucket": [0, 48], "get_affected_urls_for_third_party_cookie_metadata": [0, 48], "get_interest_group_detail": [0, 48], "get_related_website_set": [0, 48], "get_shared_storage_entri": [0, 48], "get_shared_storage_metadata": [0, 48], "get_storage_key_for_fram": [0, 48], "get_trust_token": [0, 48], "get_usage_and_quota": [0, 48], "override_quota_for_origin": [0, 48], "reset_shared_storage_budget": [0, 48], "run_bounce_tracking_mitig": [0, 48], "send_pending_attribution_report": [0, 48], "set_attribution_reporting_local_testing_mod": [0, 48], "set_attribution_reporting_track": [0, 48], "set_interest_group_auction_track": [0, 48], "set_interest_group_track": [0, 48], "set_shared_storage_entri": [0, 48], "set_shared_storage_track": [0, 48], "set_storage_bucket_track": [0, 48], "track_cache_storage_for_origin": [0, 48], "track_cache_storage_for_storage_kei": [0, 48], "track_indexed_db_for_origin": [0, 48], "track_indexed_db_for_storage_kei": [0, 48], "untrack_cache_storage_for_origin": [0, 48], "untrack_cache_storage_for_storage_kei": [0, 48], "untrack_indexed_db_for_origin": [0, 48], "untrack_indexed_db_for_storage_kei": [0, 48], "cachestoragecontentupd": [0, 48], "bucket_id": [0, 48], "cachestoragelistupd": [0, 48], "indexeddbcontentupd": [0, 48], "database_nam": [0, 28, 48], "object_store_nam": [0, 28, 48], "indexeddblistupd": [0, 48], "interestgroupaccess": [0, 48], "access_tim": [0, 48], "owner_origin": [0, 48], "component_seller_origin": [0, 48], "bid_curr": [0, 48], "unique_auction_id": [0, 48], "interestgroupauctioneventoccur": [0, 48], "event_tim": [0, 48], "parent_auction_id": [0, 48], "auction_config": [0, 48], "interestgroupauctionnetworkrequestcr": [0, 48], "auction": [0, 38, 48], "sharedstorageaccess": [0, 48], "main_frame_id": [0, 48], "param": [0, 48, 53, 58], "storagebucketcreatedorupd": [0, 48], "bucket_info": [0, 48], "storagebucketdelet": [0, 48], "attributionreportingsourceregist": [0, 48], "attributionreportingtriggerregist": [0, 48], "event_level": [0, 48], "aggregat": [0, 48], "systeminfo": [0, 1], "gpudevic": [0, 49], "vendor_id": [0, 49], "device_id": [0, 14, 49], "vendor_str": [0, 49], "device_str": [0, 49], "driver_vendor": [0, 49], "driver_vers": [0, 49], "sub_sys_id": [0, 49], "revis": [0, 8, 49], "videodecodeacceleratorcap": [0, 49], "max_resolut": [0, 49], "min_resolut": [0, 49], "videoencodeacceleratorcap": [0, 49], "max_framerate_numer": [0, 49], "max_framerate_denomin": [0, 49], "subsamplingformat": [0, 49], "yuv420": [0, 49], "yuv422": [0, 49], "yuv444": [0, 49], "imagetyp": [0, 49], "jpeg": [0, 26, 38, 49, 56, 58], "imagedecodeacceleratorcap": [0, 49], "image_typ": [0, 20, 49], "max_dimens": [0, 49], "min_dimens": [0, 49], "subsampl": [0, 49], "gpuinfo": [0, 49], "driver_bug_workaround": [0, 49], "video_decod": [0, 49], "video_encod": [0, 49], "image_decod": [0, 49], "aux_attribut": [0, 49], "feature_statu": [0, 49], "processinfo": [0, 49], "cpu_tim": [0, 49], "get_feature_st": [0, 49], "get_info": [0, 49], "get_process_info": [0, 49], "targetid": [0, 8, 43, 47, 50], "sessionid": [0, 50], "targetinfo": [0, 50, 58], "can_access_open": [0, 50], "opener_id": [0, 50], "opener_frame_id": [0, 50], "browser_context_id": [0, 8, 48, 50], "filterentri": [0, 50], "targetfilt": [0, 50], "remoteloc": [0, 50], "host": [0, 8, 16, 20, 32, 36, 50, 55, 57], "port": [0, 36, 50, 51, 55, 57], "activate_target": [0, 50], "attach_to_browser_target": [0, 50], "attach_to_target": [0, 50], "auto_attach_rel": [0, 50], "close_target": [0, 50], "create_browser_context": [0, 50], "create_target": [0, 50], "detach_from_target": [0, 50], "dispose_browser_context": [0, 50], "expose_dev_tools_protocol": [0, 50], "get_browser_context": [0, 50], "get_target_info": [0, 50], "get_target": [0, 50], "send_message_to_target": [0, 50], "set_auto_attach": [0, 50], "set_discover_target": [0, 50], "set_remote_loc": [0, 50], "attachedtotarget": [0, 50], "target_info": [0, 50], "waiting_for_debugg": [0, 50], "detachedfromtarget": [0, 50], "receivedmessagefromtarget": [0, 50], "targetcr": [0, 50], "targetdestroi": [0, 50], "error_cod": [0, 50], "targetinfochang": [0, 50], "tether": [0, 1], "bind": [0, 44, 50, 51], "unbind": [0, 51], "trace": [0, 1, 13, 16, 33, 34, 36, 38, 44], "memorydumpconfig": [0, 52], "traceconfig": [0, 52], "record_mod": [0, 52], "trace_buffer_size_in_kb": [0, 52], "enable_sampl": [0, 52], "enable_systrac": [0, 52], "enable_argument_filt": [0, 52], "included_categori": [0, 52], "excluded_categori": [0, 52], "synthetic_delai": [0, 52], "memory_dump_config": [0, 52], "streamformat": [0, 52], "proto": [0, 43, 52], "streamcompress": [0, 52], "memorydumplevelofdetail": [0, 52], "background": [0, 6, 12, 18, 20, 26, 37, 38, 50, 52, 55, 58], "light": [0, 20, 38, 52], "tracingbackend": [0, 52], "auto": [0, 28, 29, 38, 50, 52, 56, 58], "system": [0, 7, 20, 29, 38, 44, 49, 52, 58], "get_categori": [0, 52], "record_clock_sync_mark": [0, 52], "request_memory_dump": [0, 52], "bufferusag": [0, 52], "percent_ful": [0, 52], "event_count": [0, 52], "datacollect": [0, 52], "tracingcomplet": [0, 52], "data_loss_occur": [0, 52], "trace_format": [0, 52], "stream_compress": [0, 52], "webaudio": [0, 1], "graphobjectid": [0, 53], "contexttyp": [0, 53], "realtim": [0, 53], "offlin": [0, 36, 53], "contextst": [0, 53], "suspend": [0, 53], "interrupt": [0, 13, 53], "nodetyp": [0, 16, 18, 53], "channelcountmod": [0, 53], "clamped_max": [0, 53], "explicit": [0, 2, 20, 38, 53], "max_": [0, 53], "channelinterpret": [0, 53], "discret": [0, 31, 38, 53, 54], "speaker": [0, 38, 53], "paramtyp": [0, 53], "automationr": [0, 53], "a_rat": [0, 53], "k_rate": [0, 53], "contextrealtimedata": [0, 53], "render_capac": [0, 53], "callback_interval_mean": [0, 53], "callback_interval_vari": [0, 53], "baseaudiocontext": [0, 53], "context_id": [0, 44, 53], "context_typ": [0, 53], "context_st": [0, 53], "callback_buffer_s": [0, 53], "max_output_channel_count": [0, 53], "sample_r": [0, 53], "realtime_data": [0, 53], "audiolisten": [0, 53], "listener_id": [0, 53], "audionod": [0, 53], "number_of_input": [0, 53], "number_of_output": [0, 53], "channel_count": [0, 53], "channel_count_mod": [0, 53], "channel_interpret": [0, 53], "audioparam": [0, 53], "param_id": [0, 53], "param_typ": [0, 53], "rate": [0, 3, 20, 53], "get_realtime_data": [0, 53], "contextcr": [0, 53], "contextwillbedestroi": [0, 53], "contextchang": [0, 53], "audiolistenercr": [0, 53], "listen": [0, 17, 18, 53], "audiolistenerwillbedestroi": [0, 53], "audionodecr": [0, 53], "audionodewillbedestroi": [0, 53], "audioparamcr": [0, 53], "audioparamwillbedestroi": [0, 53], "nodesconnect": [0, 53], "source_id": [0, 53], "destination_id": [0, 53], "source_output_index": [0, 53], "destination_input_index": [0, 53], "nodesdisconnect": [0, 53], "nodeparamconnect": [0, 53], "nodeparamdisconnect": [0, 53], "webauthn": [0, 1], "authenticatorid": [0, 54], "authenticatorprotocol": [0, 54], "u2f": [0, 54], "ctap2": [0, 54], "ctap2vers": [0, 54], "ctap2_0": [0, 54], "ctap2_1": [0, 54], "authenticatortransport": [0, 54], "ble": [0, 54], "cabl": [0, 54], "intern": [0, 3, 27, 35, 38, 44, 46, 54, 56, 58], "virtualauthenticatoropt": [0, 54], "transport": [0, 36, 46, 54], "ctap2_vers": [0, 54], "has_resident_kei": [0, 54], "has_user_verif": [0, 54], "has_large_blob": [0, 54], "has_cred_blob": [0, 54], "has_min_pin_length": [0, 54], "has_prf": [0, 54], "automatic_presence_simul": [0, 54], "is_user_verifi": [0, 54], "default_backup_elig": [0, 54], "default_backup_st": [0, 54], "credenti": [0, 38, 54], "credential_id": [0, 54], "is_resident_credenti": [0, 54], "private_kei": [0, 54], "sign_count": [0, 54], "rp_id": [0, 54], "user_handl": [0, 54], "large_blob": [0, 54], "backup_elig": [0, 54], "backup_st": [0, 54], "user_nam": [0, 54], "user_display_nam": [0, 54], "add_credenti": [0, 54], "add_virtual_authent": [0, 54], "clear_credenti": [0, 54], "get_credenti": [0, 54], "remove_credenti": [0, 54], "remove_virtual_authent": [0, 54], "set_automatic_presence_simul": [0, 54], "set_credential_properti": [0, 54], "set_response_override_bit": [0, 54], "set_user_verifi": [0, 54], "credentialad": [0, 54], "authenticator_id": [0, 54], "credentialdelet": [0, 54], "credentialupd": [0, 54], "credentialassert": [0, 54], "cdp": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 60], "experiment": [2, 3, 4, 5, 6, 7, 8, 9, 10, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 32, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56], "you": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60], "do": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 16, 17, 18, 19, 20, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 58], "need": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 16, 17, 18, 19, 20, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 59, 60], "instanti": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 16, 17, 18, 19, 20, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55], "yourself": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 16, 17, 18, 19, 20, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 58], "instead": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 16, 17, 18, 19, 20, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 59, 60], "api": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 16, 17, 18, 19, 20, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 58], "object": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 16, 17, 18, 19, 20, 22, 23, 24, 25, 26, 27, 28, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 52, 53, 54, 55, 56, 57, 58], "can": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 16, 17, 18, 19, 20, 22, 23, 24, 25, 26, 27, 28, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 52, 53, 54, 55, 56, 58, 59, 60], "those": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 16, 17, 18, 19, 20, 22, 23, 24, 25, 26, 27, 28, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 52, 53, 54, 55, 56, 58], "argument": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 60], "qualnam": [2, 4, 5, 6, 7, 8, 9, 12, 13, 16, 17, 20, 22, 23, 24, 29, 35, 36, 37, 38, 41, 43, 46, 47, 48, 49, 50, 52, 53, 54], "start": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 60], "boundari": [2, 4, 5, 6, 7, 8, 9, 12, 13, 16, 17, 20, 22, 23, 24, 29, 32, 35, 36, 37, 38, 41, 43, 44, 46, 47, 48, 49, 50, 52, 53, 54], "enum": [2, 4, 12, 13, 18, 20, 34, 38, 48, 53], "possibl": [2, 4, 13, 38, 41, 48, 50, 58], "booleanorundefin": 2, "idreflist": 2, "nodelist": 2, "computedstr": 2, "tokenlist": 2, "domrel": 2, "internalrol": 2, "valueundefin": 2, "relatedel": 2, "nativ": [2, 17, 21, 29, 35, 38, 56, 57, 58], "particular": [2, 3, 13, 17, 21, 44, 48], "comput": [2, 12, 16, 18, 20, 38], "ax": [2, 12, 16], "The": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 59, 60], "str": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60], "ani": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 58], "bool": [2, 3, 4, 6, 7, 8, 12, 13, 16, 17, 18, 19, 20, 23, 24, 26, 27, 28, 29, 31, 32, 35, 36, 37, 38, 40, 41, 42, 43, 44, 46, 47, 48, 49, 50, 52, 54, 55, 56, 57, 58], "whether": [2, 4, 5, 8, 12, 13, 16, 17, 18, 19, 20, 23, 26, 29, 32, 36, 37, 38, 42, 44, 46, 48, 50, 52, 54, 58], "higher": [2, 12], "markup": [2, 16, 29, 38], "e": [2, 3, 4, 5, 12, 13, 20, 23, 26, 29, 36, 38, 41, 44, 46, 49, 50, 52, 54, 57], "list": [2, 3, 4, 5, 6, 7, 8, 9, 10, 12, 13, 14, 16, 17, 18, 19, 20, 22, 23, 24, 25, 27, 28, 29, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 52, 54, 55, 56, 57, 58], "being": [2, 4, 7, 8, 11, 13, 19, 20, 21, 23, 27, 32, 33, 34, 36, 38, 40, 41, 42, 46, 48, 59, 60], "relat": [2, 4, 5, 6, 12, 16, 18, 20, 29, 36, 37, 38, 40, 48, 49, 50], "current": [2, 3, 4, 8, 10, 12, 13, 16, 18, 20, 26, 27, 28, 29, 34, 35, 36, 38, 39, 40, 41, 42, 43, 44, 48, 50, 53, 55, 56, 58, 60], "One": [2, 4, 29, 48], "applic": [2, 12, 27, 36, 40, 46], "contribut": [2, 27, 46], "everi": [2, 4, 6, 12, 36, 38, 44], "region": [2, 37, 38], "widget": 2, "relationship": 2, "between": [2, 12, 16, 18, 26, 27, 29, 35, 37, 42, 50, 55, 58], "than": [2, 16, 17, 20, 32, 36, 38, 44, 50, 55, 57, 58], "sibl": [2, 16], "hiddenroot": 2, "haspopup": 2, "collect": [2, 4, 11, 12, 13, 16, 18, 27, 33, 35, 38, 39, 42, 44, 52], "why": [2, 4, 13, 30, 32, 36, 38, 44], "raw": [2, 4, 27, 36], "all": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60], "id": [2, 3, 4, 5, 6, 8, 9, 12, 13, 14, 16, 17, 18, 22, 24, 27, 31, 32, 34, 36, 37, 38, 40, 41, 42, 43, 44, 46, 48, 49, 50, 51, 52, 53, 54], "backend": [2, 12, 13, 16, 20, 27, 28, 32, 36, 37, 38, 42, 44, 52], "associ": [2, 6, 8, 12, 13, 16, 18, 29, 32, 33, 36, 38, 41, 44, 48, 49, 54], "indic": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 58], "yield": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], "must": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 58], "an": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60], "In": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 57], "same": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 58], "should": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 58], "pai": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], "attent": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], "For": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 58], "inform": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], "see": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 58, 59, 60], "dict": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 57, 58, 60], "remain": [2, 48], "consist": [2, 5, 12, 35, 38, 50, 52], "call": [2, 4, 6, 8, 12, 13, 16, 20, 23, 24, 32, 33, 34, 35, 36, 37, 38, 39, 42, 43, 44, 48, 50, 56, 58], "turn": [2, 20, 50, 52], "impact": [2, 43], "ancestor": [2, 12, 16, 36], "been": [2, 3, 4, 6, 7, 11, 12, 13, 16, 18, 20, 22, 23, 27, 29, 30, 36, 38, 42, 46, 48, 50, 52, 53], "previous": [2, 12, 13, 20, 38, 40, 46], "javascript": [2, 13, 16, 17, 18, 20, 21, 33, 36, 37, 38, 42, 44, 52, 56, 58], "wrapper": [2, 13, 16, 31, 37], "whose": [2, 3, 12, 48], "resid": 2, "If": [2, 4, 5, 7, 8, 9, 12, 13, 16, 17, 20, 22, 24, 26, 27, 28, 34, 35, 36, 38, 41, 43, 44, 46, 48, 50, 52, 54, 55, 57], "omit": [2, 4, 8, 12, 13, 16, 20, 24, 36, 37, 38, 41, 44, 46, 50], "int": [2, 4, 7, 8, 9, 11, 12, 13, 16, 17, 18, 20, 23, 24, 26, 27, 28, 29, 31, 32, 33, 34, 35, 36, 37, 38, 42, 43, 44, 46, 47, 48, 49, 50, 51, 54, 55, 56, 58], "maximum": [2, 8, 12, 13, 16, 17, 20, 31, 32, 38, 44, 49, 54, 58], "descend": [2, 16, 37], "retriev": [2, 8, 12, 16, 17, 18, 20, 22, 25, 27, 35, 39, 44, 50, 54, 55, 56, 58], "full": [2, 12, 18, 20, 34, 38, 44, 46, 53, 56, 58], "fetch_rel": 2, "partial": 2, "exist": [2, 3, 4, 6, 8, 12, 13, 16, 20, 22, 23, 36, 38, 43, 44, 48, 50, 53, 59], "true": [2, 4, 8, 12, 13, 16, 18, 20, 24, 26, 27, 28, 29, 35, 36, 37, 38, 44, 46, 48, 50, 52, 54, 55, 56, 57, 59, 60, 61], "plu": 2, "its": [2, 12, 13, 16, 19, 24, 29, 32, 36, 38, 42, 43, 44, 48, 49, 52, 58], "accessible_nam": 2, "queri": [2, 8, 12, 13, 16, 19, 20, 29, 36, 37, 38, 48, 49, 50, 56, 58], "subtre": [2, 16, 17, 38], "specifi": [2, 4, 5, 8, 9, 12, 13, 16, 18, 20, 22, 24, 28, 31, 32, 36, 37, 38, 40, 41, 42, 44, 48, 49, 50, 51, 52, 54, 55, 57, 58, 59], "doe": [2, 3, 4, 5, 11, 12, 13, 16, 20, 24, 36, 38, 43, 44, 46, 56, 57, 59, 60], "neither": [2, 44], "accessiblenam": 2, "mirror": [2, 10, 16, 44], "sent": [2, 7, 13, 16, 19, 20, 24, 27, 36, 38, 40, 42, 44, 46, 48, 52, 58], "assist": 2, "technologi": [2, 36], "when": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 16, 17, 18, 23, 24, 26, 27, 29, 30, 32, 33, 34, 36, 37, 38, 40, 41, 42, 44, 46, 48, 50, 52, 54, 55, 56, 58, 59, 60], "ha": [2, 3, 4, 6, 7, 8, 11, 12, 13, 16, 18, 20, 22, 23, 24, 27, 28, 29, 30, 32, 36, 37, 38, 41, 42, 43, 46, 48, 50, 53, 56, 57, 58], "chang": [2, 10, 12, 13, 16, 18, 20, 27, 29, 36, 38, 42, 43, 44, 46, 48, 50, 53, 59], "plai": [3, 56], "float": [3, 4, 8, 9, 12, 13, 15, 16, 18, 20, 25, 26, 27, 28, 29, 32, 33, 35, 36, 38, 39, 40, 42, 44, 47, 48, 49, 52, 53, 55, 56, 58], "playback": [3, 10, 38], "millisecond": [3, 13, 20, 26, 29, 36, 44, 52], "base": [3, 11, 12, 13, 16, 17, 18, 20, 29, 34, 35, 36, 37, 38, 42, 44, 55, 57, 58], "percentag": [3, 58], "0": [3, 4, 12, 13, 16, 17, 18, 20, 24, 26, 29, 34, 36, 38, 44, 49, 52, 55, 56, 58, 59, 60], "100": [3, 12, 26, 36, 38, 53, 56, 58], "scroll": [3, 12, 16, 18, 29, 32, 37, 38, 56, 58], "driven": 3, "transit": [3, 12, 16, 38], "view": [3, 16, 20, 38, 56, 57, 58], "timelin": [3, 40], "contain": [3, 4, 5, 8, 9, 12, 13, 16, 18, 20, 24, 29, 30, 32, 36, 37, 38, 42, 44, 52, 55, 57, 58, 60], "posit": [3, 4, 8, 12, 13, 16, 18, 20, 24, 29, 32, 36, 37, 38, 42, 56, 58], "pixel": [3, 8, 16, 20, 29, 37, 38, 40, 49, 58], "princip": 3, "box": [3, 16, 18, 24, 32, 36, 37, 58], "scrollport": 3, "defin": [3, 5, 6, 8, 12, 13, 16, 18, 22, 29, 36, 37, 38, 43, 46, 49, 51], "progress": [3, 8, 38], "scrolltimelin": 3, "releas": [3, 13, 16, 27, 29, 32, 42, 44], "set": [3, 4, 5, 6, 7, 8, 10, 12, 13, 16, 17, 18, 20, 21, 22, 23, 24, 26, 28, 29, 31, 32, 33, 34, 36, 37, 38, 39, 41, 43, 44, 46, 48, 50, 52, 54, 56, 57, 58, 59, 60], "longer": [3, 16, 37, 38, 46], "manipul": [3, 13], "seek": [3, 31], "animation_id": 3, "remot": [3, 10, 22, 27, 30, 31, 36, 44, 50], "correspond": [3, 13, 16, 18, 23, 24, 27, 34, 36, 38, 41, 43, 44, 48, 57], "within": [3, 12, 13, 16, 29, 32, 34, 36, 40], "wa": [3, 4, 5, 12, 13, 17, 18, 20, 23, 24, 26, 29, 32, 33, 34, 36, 38, 41, 42, 43, 44, 46, 47, 48, 50, 51, 55], "investig": 4, "violat": [4, 17, 33], "improv": 4, "about": [4, 5, 8, 12, 16, 26, 27, 30, 32, 36, 38, 41, 44, 46, 48, 49, 50, 56], "affect": [4, 18, 20, 24, 36], "follow": [4, 5, 7, 8, 9, 12, 13, 16, 18, 20, 24, 26, 28, 31, 32, 35, 36, 38, 42, 43, 44, 48, 49, 50, 52, 56, 57, 58], "three": 4, "excludesamesiteunspecifiedtreatedaslax": 4, "excludesamesitenoneinsecur": 4, "excludesamesitelax": 4, "excludesamesitestrict": 4, "excludeinvalidsameparti": 4, "excludesamepartycrosspartycontext": 4, "excludedomainnonascii": 4, "excludethirdpartycookieblockedinfirstpartyset": 4, "excludethirdpartyphaseout": 4, "excludeportmismatch": 4, "excludeschememismatch": 4, "warnsamesiteunspecifiedcrosssitecontext": 4, "warnsamesitenoneinsecur": 4, "warnsamesiteunspecifiedlaxallowunsaf": 4, "warnsamesitestrictlaxdowngradestrict": 4, "warnsamesitestrictcrossdowngradestrict": 4, "warnsamesitestrictcrossdowngradelax": 4, "warnsamesitelaxcrossdowngradestrict": 4, "warnsamesitelaxcrossdowngradelax": 4, "warnattributevalueexceedsmaxs": 4, "warndomainnonascii": 4, "warnthirdpartyphaseout": 4, "warncrosssiteredirectdowngradechangesinclus": 4, "warndeprecationtrialmetadata": 4, "warnthirdpartycookieheurist": 4, "setcooki": 4, "readcooki": 4, "fall": 4, "under": [4, 25, 55, 58], "githubresourc": 4, "graceperiod": 4, "suggest": [4, 8, 36, 38, 46], "tabl": [4, 18], "third": [4, 27, 36], "parti": [4, 36, 48, 54], "migrat": 4, "necessari": [4, 41], "front": [4, 12, 16, 38], "difficult": 4, "With": 4, "we": [4, 12, 13, 32, 34, 36, 41, 50, 58, 59, 60], "convei": 4, "without": [4, 8, 13, 27, 36, 38, 43, 48, 50, 58, 60], "site": [4, 8, 36, 38, 41, 43, 48, 56, 58], "mai": [4, 8, 12, 13, 16, 18, 20, 24, 26, 27, 31, 36, 37, 38, 40, 42, 43, 44, 48, 50, 53, 58, 59, 60], "addit": [4, 6, 12, 13, 16, 29, 36, 38, 49, 50, 55], "rawcookielin": 4, "problem": [4, 13, 56], "where": [4, 5, 9, 12, 13, 16, 31, 32, 34, 35, 36, 38, 41, 42, 44, 56, 58], "syntact": 4, "semant": [4, 36], "wai": [4, 24, 36, 43, 44, 55, 58], "valid": [4, 8, 12, 13, 16, 18, 28, 29, 36, 38, 41, 46, 47], "mixedcontentblock": 4, "mixedcontentautomaticallyupgrad": 4, "mixedcontentwarn": 4, "attributionsrc": 4, "cspreport": 4, "eventsourc": [4, 36], "plugindata": 4, "pluginresourc": 4, "sharedwork": [4, 38], "speculationrul": [4, 41], "xmlhttprequest": [4, 17, 36], "mix": [4, 36, 46], "resolv": [4, 12, 13, 16, 36, 44, 54, 56], "unsaf": [4, 22, 44], "j": [4, 23, 34, 56, 58], "mark": [4, 16, 36, 42], "becaus": [4, 34, 36, 38, 48, 50, 52, 56, 59, 60], "map": [4, 12, 13, 36, 38, 44, 54], "blink": [4, 8, 29, 36, 38], "mojom": [4, 41, 52], "requestcontexttyp": 4, "replac": [4, 12, 13, 16, 20, 29, 36, 41, 57], "requestdestin": 4, "alwai": [4, 13, 16, 20, 34, 36, 38, 46, 50, 58, 59, 60], "submiss": [4, 38], "necessarili": 4, "These": [4, 23, 29, 48], "refin": [4, 36], "net": [4, 24, 36], "coepframeresourceneedscoephead": 4, "coopsandboxediframecannotnavigatetocooppag": 4, "corpnotsameorigin": 4, "corpnotsameoriginafterdefaultedtosameoriginbycoep": 4, "corpnotsameoriginafterdefaultedtosameoriginbydip": 4, "corpnotsameoriginafterdefaultedtosameoriginbycoepanddip": 4, "corpnotsamesit": 4, "srimessagesignaturemismatch": 4, "onli": [4, 8, 9, 10, 12, 13, 16, 18, 20, 23, 24, 26, 27, 29, 31, 32, 36, 38, 41, 42, 44, 46, 48, 49, 50, 52, 55, 56, 58, 59, 60], "extend": [4, 24, 36, 43], "some": [4, 12, 16, 24, 31, 36, 37, 43, 44, 48, 50, 52, 53, 56, 59], "futur": [4, 26, 36, 44, 58], "heavyadblock": 4, "heavyadwarn": 4, "networktotallimit": 4, "cputotallimit": 4, "cpupeaklimit": 4, "either": [4, 13, 16, 20, 23, 24, 31, 36, 37, 38, 41, 42, 43, 44, 52, 57, 58], "warn": [4, 44, 46, 58, 60], "ad": [4, 10, 11, 12, 16, 17, 33, 36, 37, 38, 40, 41, 44, 48, 50, 54, 58], "peak": 4, "kinlineviol": 4, "kevalviol": 4, "kurlviol": 4, "ksriviol": 4, "ktrustedtypessinkviol": 4, "ktrustedtypespolicyviol": 4, "kwasmevalviol": 4, "transferissu": 4, "creationissu": 4, "aris": 4, "sab": 4, "transfer": [4, 38, 52], "cross": [4, 36, 37, 38, 44, 50, 55], "rfc1918": 4, "enforc": 4, "permissionpolicydis": 4, "untrustworthyreportingorigin": 4, "insecurecontext": 4, "invalidhead": 4, "invalidregistertriggerhead": 4, "sourceandtriggerhead": 4, "sourceignor": 4, "triggerignor": 4, "ossourceignor": 4, "ostriggerignor": 4, "invalidregisterossourcehead": 4, "invalidregisterostriggerhead": 4, "webandoshead": 4, "noweborossupport": 4, "navigationregistrationwithouttransientuseractiv": 4, "invalidinfohead": 4, "noregistersourcehead": 4, "noregistertriggerhead": 4, "noregisterossourcehead": 4, "noregisterostriggerhead": 4, "navigationregistrationuniquescopealreadyset": 4, "useerrorcrossoriginnocorsrequest": 4, "useerrordictionaryloadfailur": 4, "useerrormatchingdictionarynotus": 4, "useerrorunexpectedcontentdictionaryhead": 4, "writeerrorcossoriginnocorsrequest": 4, "writeerrordisallowedbyset": 4, "writeerrorexpiredrespons": 4, "writeerrorfeaturedis": 4, "writeerrorinsufficientresourc": 4, "writeerrorinvalidmatchfield": 4, "writeerrorinvalidstructuredhead": 4, "writeerrornavigationrequest": 4, "writeerrornomatchfield": 4, "writeerrornonlistmatchdestfield": 4, "writeerrornonsecurecontext": 4, "writeerrornonstringidfield": 4, "writeerrornonstringinmatchdestlist": 4, "writeerrornonstringmatchfield": 4, "writeerrornontokentypefield": 4, "writeerrorrequestabort": 4, "writeerrorshuttingdown": 4, "writeerrortoolongidfield": 4, "writeerrorunsupportedtyp": 4, "missingsignaturehead": 4, "missingsignatureinputhead": 4, "invalidsignaturehead": 4, "invalidsignatureinputhead": 4, "signatureheadervalueisnotbytesequ": 4, "signatureheadervalueisparameter": 4, "signatureheadervalueisincorrectlength": 4, "signatureinputheadermissinglabel": 4, "signatureinputheadervaluenotinnerlist": 4, "signatureinputheadervaluemissingcompon": 4, "signatureinputheaderinvalidcomponenttyp": 4, "signatureinputheaderinvalidcomponentnam": 4, "signatureinputheaderinvalidheadercomponentparamet": 4, "signatureinputheaderinvalidderivedcomponentparamet": 4, "signatureinputheaderkeyidlength": 4, "signatureinputheaderinvalidparamet": 4, "signatureinputheadermissingrequiredparamet": 4, "validationfailedsignatureexpir": 4, "validationfailedinvalidlength": 4, "validationfailedsignaturemismatch": 4, "around": [4, 29, 37, 52], "explain": [4, 38, 46], "github": [4, 8, 20, 23, 36, 38, 40, 41, 48, 53, 54, 58, 59, 60], "com": [4, 16, 38, 40, 41, 43, 44, 48, 50, 58, 59, 60], "wicg": [4, 20, 36, 38, 40, 41, 48], "quirk": 4, "fals": [4, 8, 12, 13, 16, 17, 18, 26, 29, 36, 37, 38, 46, 48, 50, 54, 55, 56, 57, 58, 59, 61], "mean": [4, 11, 16, 24, 27, 29, 33, 36, 37, 38, 44, 46, 53], "formlabelfornameerror": 4, "formduplicateidforinputerror": 4, "forminputwithnolabelerror": 4, "formautocompleteattributeemptyerror": 4, "formemptyidandnameattributesforinputerror": 4, "formarialabelledbytononexistingid": 4, "forminputassignedautocompletevaluetoidornameattributeerror": 4, "formlabelhasneitherfornornestedinput": 4, "formlabelformatchesnonexistingiderror": 4, "forminputhaswrongbutwellintendedautocompletevalueerror": 4, "responsewasblockedbyorb": 4, "errortyp": [4, 41], "aggreg": [4, 36, 38], "frontend": [4, 38], "deprec": [4, 11, 13, 16, 17, 18, 20, 26, 27, 36, 37, 38, 39, 44, 45, 46, 50, 52], "chromium": [4, 8, 20, 29, 34, 36, 38, 50, 55], "org": [4, 7, 8, 12, 16, 20, 25, 29, 36, 38, 43, 46, 50, 54], "main": [4, 7, 8, 29, 32, 36, 37, 38, 41, 47, 49, 50, 58, 59, 60], "third_parti": [4, 8, 29, 36], "render": [4, 5, 8, 12, 16, 18, 20, 26, 29, 35, 36, 38, 53], "core": [4, 7, 29, 36, 57], "readm": 4, "md": [4, 38, 41], "json5": [4, 38], "redirect": [4, 24, 36], "chain": [4, 12, 13, 44, 46], "tracker": [4, 48], "thei": [4, 6, 12, 13, 34, 36, 41, 43, 44, 54, 58], "don": [4, 42, 55, 59, 60], "t": [4, 13, 24, 29, 36, 37, 38, 41, 42, 44, 48, 55, 57, 58, 59, 60], "receiv": [4, 5, 7, 8, 12, 13, 16, 24, 34, 36, 38, 47, 48, 50, 56, 58], "user": [4, 8, 12, 14, 16, 20, 23, 29, 36, 37, 38, 41, 43, 44, 46, 54, 56, 58, 60], "interact": [4, 10, 16, 23, 29, 43, 58, 59, 60], "note": [4, 8, 13, 16, 18, 20, 24, 26, 34, 35, 36, 38, 39, 40, 41, 43, 44, 46, 48, 52, 55, 56, 58], "etld": 4, "test": [4, 7, 8, 16, 23, 37, 38, 54, 58], "80": 4, "bounc": [4, 48], "would": [4, 18, 20, 23, 29, 36, 38, 40, 44, 52, 56, 58], "permit": [4, 21], "due": [4, 24, 36, 37, 38, 42, 44, 59, 60], "global": [4, 8, 13, 18, 38, 44, 48, 52], "web_pag": 4, "metatagallowlistinvalidorigin": 4, "metatagmodifiedhtml": 4, "feder": 4, "alongsid": 4, "requestidtokenstatu": 4, "devtool": [4, 8, 12, 31, 46, 48, 50, 56, 58], "inspector_issu": 4, "case": [4, 12, 13, 16, 26, 36, 38, 41, 44, 48, 56, 57, 58], "shouldembargo": 4, "toomanyrequest": 4, "wellknownhttpnotfound": 4, "wellknownnorespons": 4, "wellknowninvalidrespons": 4, "wellknownlistempti": 4, "wellknowninvalidcontenttyp": 4, "confignotinwellknown": 4, "wellknowntoobig": 4, "confighttpnotfound": 4, "confignorespons": 4, "configinvalidrespons": 4, "configinvalidcontenttyp": 4, "clientmetadatahttpnotfound": 4, "clientmetadatanorespons": 4, "clientmetadatainvalidrespons": 4, "clientmetadatainvalidcontenttyp": 4, "idpnotpotentiallytrustworthi": 4, "disabledinset": 4, "disabledinflag": 4, "errorfetchingsignin": 4, "invalidsigninrespons": 4, "accountshttpnotfound": 4, "accountsnorespons": 4, "accountsinvalidrespons": 4, "accountslistempti": 4, "accountsinvalidcontenttyp": 4, "idtokenhttpnotfound": 4, "idtokennorespons": 4, "idtokeninvalidrespons": 4, "idtokenidperrorrespons": 4, "idtokencrosssiteidperrorrespons": 4, "idtokeninvalidrequest": 4, "idtokeninvalidcontenttyp": 4, "erroridtoken": 4, "rppagenotvis": 4, "silentmediationfailur": 4, "thirdpartycookiesblock": 4, "notsignedinwithidp": 4, "missingtransientuseractiv": 4, "replacedbyactivemod": 4, "invalidfieldsspecifi": 4, "relyingpartyoriginisopaqu": 4, "typenotmatch": 4, "uidismissednoembargo": 4, "getuserinfo": 4, "federatedauthuserinforequestresult": 4, "notsameorigin": 4, "notifram": 4, "notpotentiallytrustworthi": 4, "noapipermiss": 4, "noaccountsharingpermiss": 4, "invalidconfigorwellknown": 4, "invalidaccountsrespons": 4, "noreturninguserfromfetchedaccount": 4, "client": [4, 11, 12, 13, 16, 18, 19, 20, 22, 24, 33, 35, 36, 37, 38, 41, 44, 46, 50, 54], "old": [4, 13, 16], "featur": [4, 6, 8, 12, 20, 36, 38, 49], "encourag": 4, "ones": [4, 13, 36, 41, 43], "guidanc": 4, "blockedcrosspartitionfetch": 4, "enforcenoopenerfornavig": 4, "bloburl": 4, "partit": [4, 36], "blob": [4, 31, 38, 41, 54], "disallowedselectchild": 4, "disallowedoptgroupchild": 4, "nonphrasingcontentoptionchild": 4, "interactivecontentoptionchild": 4, "interactivecontentlegendchild": 4, "lateimportrul": 4, "requestfail": 4, "referenc": [4, 12, 13, 44], "couldn": 4, "invalidsyntax": 4, "invalidinitialvalu": 4, "invalidinherit": 4, "invalidnam": 4, "lead": [4, 58], "discard": [4, 16, 27, 31, 44], "pars": [4, 12, 13, 16, 36, 38], "one": [4, 9, 12, 13, 16, 20, 23, 24, 26, 27, 28, 29, 32, 34, 36, 38, 43, 44, 46, 50, 52, 54, 55, 58, 59, 60], "kind": [4, 12], "cookieissu": 4, "mixedcontentissu": 4, "blockedbyresponseissu": 4, "heavyadissu": 4, "contentsecuritypolicyissu": 4, "sharedarraybufferissu": 4, "lowtextcontrastissu": 4, "corsissu": 4, "attributionreportingissu": 4, "quirksmodeissu": 4, "partitioningbloburlissu": 4, "navigatoruseragentissu": 4, "genericissu": 4, "deprecationissu": 4, "clienthintissu": 4, "federatedauthrequestissu": 4, "bouncetrackingissu": 4, "cookiedeprecationmetadataissu": 4, "stylesheetloadingissu": 4, "federatedauthuserinforequestissu": 4, "propertyruleissu": 4, "shareddictionaryissu": 4, "selectelementaccessibilityissu": 4, "srimessagesignatureissu": 4, "struct": [4, 48], "hold": [4, 12, 13, 16, 27, 36, 38, 42, 52, 58], "pleas": [4, 36, 37, 38, 58], "add": [4, 37, 44, 48, 50, 54, 57, 58], "entiti": [4, 49], "etc": [4, 5, 13, 16, 20, 29, 34, 35, 36, 38, 44, 49, 57], "refer": [4, 13, 16, 18, 27, 29, 38, 44, 55, 58], "report_aaa": 4, "contrast": [4, 13, 37], "wcag": 4, "prevent": [4, 11, 19, 20, 29, 33, 36, 38, 42, 44, 58], "further": [4, 11, 13, 33, 44, 59, 60], "far": [4, 11, 33, 36], "encod": [4, 7, 8, 9, 13, 18, 24, 26, 31, 32, 35, 36, 38, 49, 52, 54], "size_onli": 4, "were": [4, 5, 12, 16, 27, 29, 31, 36, 44, 47, 48, 52], "re": [4, 16, 34, 44, 58, 59, 60], "tupl": [4, 8, 9, 12, 13, 16, 18, 24, 26, 28, 31, 32, 35, 36, 38, 42, 43, 44, 48, 49, 52, 56, 57, 58], "base64": [4, 7, 8, 9, 13, 24, 26, 31, 32, 36, 38, 52, 54], "sizeonli": 4, "pass": [4, 6, 7, 8, 9, 12, 13, 16, 18, 24, 25, 26, 27, 32, 34, 36, 38, 40, 42, 50, 52, 54, 55], "over": [4, 7, 8, 9, 13, 24, 26, 29, 32, 36, 37, 38, 46, 50, 52, 54, 58], "originals": 4, "encodeds": 4, "16": [5, 18, 26, 29, 36], "digit": [5, 38], "credit": 5, "card": [5, 38], "owner": [5, 12, 16, 18, 37], "month": [5, 59, 60], "4": [5, 23, 29, 38, 49, 56, 58, 60], "year": [5, 59, 60], "3": [5, 7, 13, 16, 17, 18, 20, 26, 36, 37, 38, 39, 46, 50, 59, 60], "verif": [5, 54, 59, 60], "jon": 5, "how": [5, 24, 32, 36, 52, 58], "like": [5, 13, 24, 36, 42, 44, 56, 57, 58, 59], "ui": [5, 34, 44, 46, 54], "two": [5, 23, 36, 53], "dimension": 5, "inner": 5, "surfac": [5, 10, 38], "give_nam": 5, "citi": 5, "munich": 5, "zip": 5, "81456": 5, "dimens": [5, 20, 38, 49], "represent": [5, 12, 24, 34, 36, 44], "autocompleteattribut": 5, "autofillinf": 5, "actual": [5, 8, 12, 13, 16, 36, 38, 46, 48, 55, 58, 59, 60], "strategi": 5, "belong": [5, 6, 12, 16, 38, 44, 50], "develop": [5, 7, 25, 36, 41, 48], "verifi": [5, 23, 58], "implement": [5, 12, 16, 34, 44, 59, 60], "fieldid": 5, "cannot": [5, 8, 13, 36, 37, 43, 44, 58], "serv": [5, 36, 38, 44], "anchor": [5, 16, 18, 32, 38], "out": [5, 13, 20, 29, 38, 41, 44, 50, 57, 59, 60], "emit": [5, 8, 29, 36, 38, 41], "2d": 5, "independ": [6, 37, 38], "share": [6, 18, 38, 41, 48], "backgroundfetch": [6, 8, 55], "backgroundsync": [6, 8, 55], "pushmessag": 6, "paymenthandl": [6, 8, 55], "periodicbackgroundsync": [6, 8, 55], "pair": [6, 16, 18, 24, 36, 48, 57], "along": [6, 20, 24, 29, 32, 36, 38, 44, 48], "second": [6, 9, 12, 26, 27, 29, 32, 36, 38, 40, 41, 42, 48, 49, 53, 55, 56, 58, 59, 60], "group": [6, 13, 16, 27, 36, 38, 44, 46, 48], "togeth": [6, 18, 41, 44], "store": [6, 7, 9, 28, 29, 36, 48, 52, 54, 56], "should_record": 6, "record": [6, 9, 12, 27, 28, 36, 40, 42, 48, 52, 56], "afterward": 6, "virtual": [7, 13, 20, 29, 54], "variou": [7, 13, 37], "central": 7, "power": [7, 38, 58], "off": [7, 12, 20, 50, 59, 60], "gatt": 7, "manufactur": 7, "compani": 7, "bitbucket": 7, "sig": 7, "assigned_numb": 7, "company_identifi": 7, "yaml": 7, "byte": [7, 8, 13, 25, 27, 31, 35, 36, 38, 44, 48, 54], "advertis": 7, "packet": [7, 36], "extern": [7, 13, 38, 41, 44], "transmiss": 7, "broadcast": 7, "le_support": 7, "simul": [7, 20, 35], "energi": 7, "describ": [7, 10, 13, 16, 20, 27, 29, 44, 46, 49], "peripher": 7, "hci": 7, "vol": 7, "part": [7, 8, 24, 36, 38, 50, 55, 59, 60], "d": [7, 38, 57, 58], "Of": 7, "known_service_uuid": 7, "knownserviceuuid": 7, "alreadi": [7, 16, 36, 48, 56], "happen": [7, 13, 16, 23, 24, 32, 37, 38, 44, 48, 50, 56, 58], "window": [8, 12, 20, 29, 37, 38, 41, 43, 49, 50, 55, 58, 59], "edg": 8, "screen": [8, 20, 37, 38, 55, 58], "audiocaptur": [8, 55], "automaticfullscreen": 8, "camerapantiltzoom": 8, "capturedsurfacecontrol": 8, "clipboardreadwrit": [8, 55], "clipboardsanitizedwrit": [8, 55], "displaycaptur": [8, 55], "durablestorag": [8, 55], "handtrack": 8, "idledetect": [8, 55], "keyboardlock": [8, 38], "localfont": [8, 55], "localnetworkaccess": 8, "midisysex": [8, 55], "pointerlock": 8, "protectedmediaidentifi": [8, 55], "smartcard": [8, 38], "speakerselect": 8, "storageaccess": [8, 36, 55], "toplevelstorageaccess": [8, 36, 55], "videocaptur": [8, 55], "wakelockscreen": [8, 55], "wakelocksystem": [8, 55], "webappinstal": 8, "webprint": 8, "windowmanag": [8, 55], "definit": [8, 38, 43, 56], "permiss": [8, 38, 50, 55], "w3c": [8, 20, 38, 40, 54], "permission_descriptor": 8, "idl": [8, 20, 36, 38, 40], "push": [8, 12, 16, 36], "uservisibleonli": 8, "clipboard": [8, 38], "allowwithoutsanit": 8, "allowwithoutgestur": 8, "pantiltzoom": 8, "executebrowsercommand": 8, "opentabsearch": 8, "closetabsearch": 8, "minimum": [8, 12, 20, 32, 36, 38, 49], "inclus": [8, 12], "exclus": [8, 12, 13, 24, 44, 50], "sum": 8, "biddingandauctionservic": 8, "trustedkeyvalu": 8, "coordinator_origin": 8, "key_config": 8, "encrypt": [8, 36, 38], "given": [8, 12, 13, 16, 17, 18, 20, 22, 24, 27, 28, 29, 32, 36, 37, 38, 43, 44, 48, 49, 50, 54, 55, 56, 57, 58], "privaci": [8, 48], "sandbox": [8, 36, 48, 55, 57], "talk": 8, "trust": [8, 36, 48], "coordin": [8, 16, 18, 29, 32, 37, 56], "intend": [8, 38, 44], "coordinatororigin": 8, "browsercontext": [8, 50], "enrol": 8, "gracefulli": 8, "thread": [8, 32, 38, 49], "gpu": [8, 49], "process": [8, 20, 29, 35, 36, 37, 38, 43, 44, 46, 49, 55, 59, 60], "command_id": 8, "invok": [8, 21, 48], "telemetri": [8, 20], "switch": [8, 20], "commandlin": [8, 49], "delta": [8, 29, 42], "last": [8, 12, 13, 16, 27, 31, 32, 35, 38, 42, 47, 48, 57], "substr": [8, 9, 17, 18], "extract": [8, 38], "empti": [8, 9, 10, 12, 13, 20, 24, 28, 29, 36, 37, 38, 40, 44, 46, 49, 50, 54, 57, 58], "protocolvers": 8, "product": 8, "userag": 8, "agent": [8, 12, 16, 18, 20, 31, 34, 36, 44, 52], "jsversion": 8, "v8": [8, 13, 35, 44], "window_id": 8, "restor": [8, 20, 29, 34, 38], "reject": [8, 23, 44], "overrid": [8, 13, 15, 20, 24, 34, 36, 38, 40, 43, 44, 46, 48, 54], "reset": [8, 20, 23, 38, 42, 43, 48, 54], "badge_label": 8, "dock": 8, "tile": [8, 32], "png": [8, 26, 38, 56, 58], "behavior": [8, 13, 24, 29, 36, 38], "download_path": [8, 38], "events_en": 8, "otherwis": [8, 13, 20, 24, 26, 36, 38, 48, 49, 52, 54, 56, 57], "allowandnam": 8, "accord": [8, 12, 16, 18, 38, 43, 48], "descriptor": [8, 12, 37, 44], "combin": 8, "unspecifi": [8, 36], "unchang": 8, "fire": [8, 10, 12, 13, 16, 20, 24, 30, 36, 37, 38, 41, 46, 55, 56, 58], "begin": [8, 12, 13, 18, 24, 36, 38], "disk": [8, 36, 38], "opaquerespons": 9, "opaqueredirect": 9, "epoch": [9, 36, 40, 44], "opaqu": [9, 36, 54], "delet": [9, 28, 36, 38, 44, 48, 50, 54], "spec": [9, 38, 48, 53, 54], "At": [9, 28], "least": [9, 28, 29, 52], "securityorigin": [9, 28], "storagekei": [9, 28, 48], "skip_count": [9, 28], "page_s": [9, 28], "path_filt": 9, "skip": [9, 13, 16, 28, 58], "present": [9, 10, 12, 13, 16, 18, 24, 32, 36, 38, 40, 48, 50, 54, 57], "cachedataentri": 9, "returncount": 9, "pathfilt": 9, "There": [9, 12, 15, 17, 18, 21, 22, 25, 26, 28, 30, 31, 35, 43, 45, 46, 49, 51, 58], "observ": [10, 20, 24, 36], "presentation_url": 10, "compat": [10, 13, 16], "presentationurl": 10, "well": [10, 16, 18, 50, 56, 58, 59, 60], "remov": [10, 12, 13, 16, 17, 21, 22, 26, 34, 36, 38, 44, 48, 54, 56, 57, 58], "sink_nam": 10, "choos": [10, 52], "via": [10, 12, 13, 16, 18, 20, 27, 29, 36, 38, 41, 43, 44, 50, 52, 59], "sdk": 10, "desktop": 10, "whenev": [10, 12, 34, 36, 58], "softwar": 10, "outstand": 10, "issuemessag": 10, "sever": [11, 13, 16, 33, 46], "noth": [11, 20, 57, 58], "expos": [12, 13, 16, 27, 36, 40, 44, 52], "write": [12, 16, 38], "subsequ": [12, 13, 24, 50], "structur": [12, 16, 38, 41], "interchang": 12, "fornod": 12, "keep": [12, 16, 34, 52, 55, 58, 59, 60], "getstylesheet": 12, "pseudo": [12, 16, 18], "ident": [12, 32, 36, 38, 43], "come": [12, 20, 29, 41, 44, 57], "delimit": 12, "comma": [12, 48], "underli": [12, 36], "draft": [12, 36], "csswg": 12, "compon": [12, 16, 25, 36, 57], "metainform": 12, "construct": [12, 53], "cssstylesheet": 12, "non": [12, 13, 16, 24, 35, 37, 38, 40, 44, 48, 50, 52], "denot": [12, 13], "parser": [12, 16, 36], "written": 12, "mutabl": 12, "becom": [12, 16, 28, 32, 36, 38, 56], "modifi": [12, 13, 16, 17, 19, 24, 29, 36, 38, 47, 48, 56, 58], "cssom": 12, "them": [12, 16, 20, 24, 34, 38, 41, 44, 48, 50, 55, 56], "immedi": [12, 16, 20, 38, 42, 43, 44, 48, 54, 58], "creation": [12, 13, 16, 36, 38, 44, 50], "through": [12, 13, 21, 34, 41, 50, 54, 56], "zero": [12, 24, 36, 52, 54], "charact": [12, 18, 24, 36], "sourceurl": [12, 13], "comment": [12, 24, 43], "sheet": 12, "declar": [12, 44, 52, 56], "came": 12, "sort": [12, 13], "distanc": [12, 29, 37], "involv": [12, 48], "innermost": 12, "go": [12, 34, 38, 58, 59, 60], "outward": 12, "cascad": 12, "hierarchi": [12, 38, 55], "dure": [12, 13, 26, 36, 38, 41, 44, 48], "mediarul": 12, "supportsrul": 12, "containerrul": 12, "layerrul": 12, "scoperul": 12, "stylerul": 12, "startingstylerul": 12, "coverag": [12, 42], "shorthand": 12, "annot": 12, "impli": [12, 38, 44], "enclos": 12, "understood": [12, 36], "longhand": 12, "importrul": 12, "linkedsheet": 12, "inlinesheet": 12, "satisfi": 12, "physic": [12, 16, 20, 29, 36], "logic": [12, 13, 16], "determin": [12, 18, 24, 27, 36, 37, 38, 40, 44, 50], "sub": [12, 16, 38, 48, 49], "amount": [12, 48, 58], "glyph": 12, "famili": [12, 38], "postscript": 12, "variat": 12, "variabl": [12, 13, 20, 44], "k": [12, 53, 57, 59, 60], "human": [12, 44], "readabl": [12, 44], "languag": [12, 13, 18, 20, 36, 38, 58, 59], "en": [12, 25, 57, 59], "www": [12, 16, 36, 38, 43, 46, 58, 59, 60], "w3": [12, 16, 36, 46], "tr": [12, 16, 36, 46], "2008": 12, "rec": 12, "css2": 12, "20080411": 12, "platformfontfamili": 12, "fontvariationax": 12, "variant": [12, 13, 58], "weight": 12, "stretch": 12, "unicod": 12, "try": [12, 13, 58], "prelud": 12, "dash": [12, 37], "registerproperti": 12, "palett": 12, "section": [12, 13, 36, 58], "mutat": [12, 13], "rule_text": 12, "node_for_property_syntax_valid": 12, "insert": [12, 16, 28, 29, 57], "ruletext": 12, "regist": [12, 38, 43, 48, 53], "static": [12, 36, 59, 60], "produc": [12, 20, 24, 31, 38, 40, 44], "incorrect": 12, "var": 12, "newli": [12, 44], "special": 12, "assum": [12, 13, 36, 38, 50], "forced_pseudo_class": 12, "ensur": [12, 58, 60], "animationstyl": 12, "transitionsstyl": 12, "animationsstyl": 12, "backgroundcolor": 12, "behind": 12, "undefin": [12, 16, 20, 24, 44], "flat": [12, 16, 50], "simpli": 12, "gradient": 12, "anyth": [12, 44], "complic": 12, "had": [12, 18, 24, 36, 41], "computedfonts": 12, "12px": 12, "computedfontweight": 12, "explicitli": [12, 44, 52], "implicitli": [12, 16], "inlinestyl": 12, "attributesstyl": 12, "20": [12, 50], "engin": [12, 36], "getlayersfornod": 12, "nearest": [12, 16, 32], "shadow": [12, 16, 17, 18, 38, 58, 60], "selector_text": 12, "getlocationforselector": 12, "shorthand_nam": 12, "matchedcssrul": 12, "pseudoel": 12, "inheritedpseudoel": 12, "fallback": [12, 36], "activepositionfallbackindex": 12, "parentlayoutnodeid": 12, "textnod": 12, "statist": [12, 27, 42], "emploi": 12, "textual": [12, 24], "1em": 12, "calc": 12, "1px": 12, "2px": 12, "3px": 12, "substitut": [12, 24], "env": [12, 20], "revert": 12, "modif": [12, 16, 36], "takecoveragedelta": 12, "instrument": [12, 13, 17, 21, 36], "poll": [12, 42], "batch": [12, 16, 34], "obtain": [12, 18, 31, 36], "becam": 12, "monoton": [12, 36, 40, 42], "properties_to_track": 12, "takecomputedstyleupd": 12, "occur": [12, 21, 29, 31, 36, 41, 46, 48, 50, 53, 59, 60], "queue": [12, 36], "throttl": [12, 20, 36], "previou": [12, 13, 16, 20, 29, 40, 50], "successfulli": [12, 16, 26, 51, 60], "consid": [12, 13, 36, 50, 58], "metainfo": 12, "debug": [13, 17, 22, 30, 36, 37, 50, 54, 55, 58], "capabl": [13, 38, 49], "breakpoint": [13, 17, 21, 44], "execut": [13, 16, 17, 20, 35, 36, 38, 39, 42, 44], "explor": 13, "machin": [13, 49], "favor": [13, 27, 38, 44], "vm": 13, "restart": 13, "here": [13, 36, 44, 56, 58, 59], "guarante": [13, 36, 38, 43, 44, 58], "restartfram": 13, "veri": [13, 32, 56, 58], "rest": [13, 16, 59, 60], "artifici": [13, 27], "transient": 13, "search": [13, 16, 36, 38, 58, 60], "disassembl": 13, "bytecod": 13, "webassembli": 13, "wasm": 13, "target_call_fram": 13, "reach": [13, 53, 58], "streamid": 13, "larg": [13, 27, 48, 54], "disassembli": 13, "totalnumberoflin": 13, "functionbodyoffset": 13, "format": [13, 16, 26, 37, 38, 49, 52, 54, 56, 58], "start1": 13, "end1": 13, "start2": 13, "end2": 13, "max_scripts_cache_s": 13, "heap": [13, 27, 35, 44], "put": 13, "object_group": [13, 16, 27, 44], "include_command_line_api": [13, 38, 44], "silent": [13, 44], "return_by_valu": [13, 44, 56, 58], "generate_preview": [13, 44], "throw_on_side_effect": [13, 44], "rapid": 13, "handl": [13, 24, 29, 31, 36, 37, 38, 43, 44, 46, 52, 55, 58, 59, 60], "releaseobjectgroup": 13, "thrown": [13, 38, 44], "setpauseonexcept": [13, 44], "throw": [13, 44], "side": [13, 26, 29, 44], "effect": [13, 26, 37, 44, 48, 50, 56], "termin": [13, 30, 35, 44, 50], "restrict_to_funct": 13, "nest": [13, 25, 44], "scriptsourc": 13, "stack_trace_id": 13, "getscriptsourc": 13, "stream_id": 13, "now": [13, 16, 19, 20, 34, 36, 38, 46, 59, 60], "statement": 13, "parent_stack_trace_id": 13, "async": [13, 36, 44, 55, 56, 58, 59, 60], "schedul": [13, 20, 38, 48], "immediatli": 13, "To": [13, 20, 36, 56], "ward": 13, "miss": [13, 16, 20, 38], "stepinto": 13, "asyncstacktrac": 13, "asyncstacktraceid": 13, "terminate_on_resum": 13, "upon": [13, 16, 17, 20, 31, 33, 36, 37, 38, 58], "terminateexecut": 13, "case_sensit": [13, 36, 38], "is_regex": [13, 36, 38], "sensit": [13, 27, 36, 38], "treat": [13, 36, 38, 44, 56, 58], "regex": [13, 36, 38], "blackbox": 13, "final": [13, 18, 29, 38, 41, 48], "resort": 13, "unsuccess": 13, "skip_anonym": 13, "regexp": 13, "blacklist": 13, "interv": [13, 26, 27, 35, 42, 52, 53], "isn": [13, 36, 41, 44, 58], "actualloc": 13, "url_regex": 13, "script_hash": 13, "surviv": [13, 38, 44], "urlregex": 13, "hash": [13, 36], "deactiv": 13, "uncaught": 13, "caught": 13, "break": [13, 60], "script_sourc": [13, 38], "dry_run": 13, "allow_top_frame_edit": 13, "automat": [13, 20, 44, 50, 58, 59], "dry": 13, "long": [13, 27, 28, 36, 59, 60], "stackchang": 13, "ok": 13, "compileerror": 13, "scope_numb": 13, "variable_nam": 13, "manual": [13, 37, 38, 58], "closur": 13, "catch": 13, "break_on_async_cal": 13, "skip_list": 13, "task": [13, 20, 58], "skiplist": 13, "criteria": 13, "auxiliari": [13, 44], "hit": [13, 16, 21, 36, 37], "never": [13, 16, 59, 60], "sha": 13, "256": [13, 54], "embedd": [13, 38, 42, 44, 54], "isdefault": [13, 44], "es6": 13, "suppli": [13, 24], "uncollect": 13, "setbreakpointbyurl": 13, "equival": [13, 24, 36, 54, 58], "open": [14, 16, 28, 36, 38, 43, 44, 50, 55, 58, 59, 60], "respond": [14, 18, 24], "selectprompt": 14, "cancelprompt": 14, "overridden": [15, 20, 24, 36, 38, 46, 48], "alpha": [15, 16, 38], "beta": [15, 38], "gamma": [15, 38], "mock": [15, 20, 36, 38], "twice": 16, "friendli": [16, 38, 44], "nodenam": [16, 18], "letter": 16, "spell": 16, "grammar": 16, "button": [16, 23, 29, 56, 58, 59, 60], "thumb": 16, "piec": 16, "corner": [16, 57, 58], "quirksmod": 16, "limitedquirksmod": 16, "noquirksmod": 16, "containerselector": 16, "term": [16, 38], "awar": 16, "localnam": 16, "nodevalu": [16, 18], "name1": 16, "value1": 16, "name2": 16, "value2": 16, "frameown": [16, 18], "documenttyp": [16, 18], "publicid": [16, 18], "systemid": [16, 18], "internalsubset": 16, "xml": 16, "fragment": [16, 27, 32, 36, 38], "templat": [16, 18, 38, 58], "crbug": [16, 38, 41, 43, 44, 50], "937746": 16, "htmlimport": 16, "distribut": [16, 27, 37], "svg": 16, "retain": 16, "red": [16, 56], "255": 16, "green": 16, "blue": 16, "clock": [16, 52], "wise": 16, "outsid": [16, 37, 59, 60], "target_node_id": 16, "insert_before_node_id": 16, "deep": [16, 36, 44], "place": [16, 48], "drop": [16, 29, 35, 36], "targetnodeid": 16, "clone": 16, "pierc": [16, 17], "larger": [16, 17], "travers": [16, 17], "search_id": 16, "getsearchresult": 16, "include_whitespac": 16, "whitespac": [16, 57], "anchor_specifi": 16, "interleav": 16, "container_nam": 16, "containernam": 16, "queriesscrollst": 16, "null": [16, 34, 37, 41, 53], "might": [16, 18, 34, 36, 37, 44, 56, 58], "multipl": [16, 27, 28, 34, 36, 37, 38, 41, 44, 48, 50, 56, 58], "caller": [16, 23], "certain": [16, 42], "design": [16, 26], "capturesnapshot": [16, 18], "include_user_agent_shadow_dom": 16, "ignore_pointer_events_non": 16, "ua": [16, 18, 20, 36, 38], "pointer": [16, 29], "As": 16, "computed_styl": [16, 18], "outer": [16, 36, 44], "relayout": 16, "from_index": 16, "to_index": 16, "fromindex": 16, "toindex": 16, "therefor": [16, 56, 58], "hide": [16, 37], "undoabl": 16, "move": [16, 29, 34, 56, 58], "cancelsearch": 16, "plain": 16, "searchid": 16, "resultcount": 16, "fixm": 16, "proprietari": 16, "queryselector": [16, 56], "queryselectoral": [16, 56, 58], "undon": 16, "down": [16, 56, 58], "seri": [16, 24, 52], "exactli": [16, 24, 36, 44, 58], "objectid": [16, 37, 44], "center": [16, 58], "similar": [16, 22, 43, 50, 55, 58], "scrollintoview": 16, "Will": [16, 44, 49], "deriv": [16, 43], "captur": [16, 26, 32, 37, 38, 44, 56, 58], "getnodestacktrac": 16, "outer_html": 16, "ttribut": 16, "domcharacterdatamodifi": 16, "domnodeinsert": 16, "domnoderemov": 16, "scrollabl": [16, 38], "want": [16, 32, 55, 56, 58, 59, 60], "popul": [16, 36, 44], "trustedtyp": [17, 58], "polici": [17, 20, 36, 38, 44], "usecaptur": 17, "setdombreakpoint": 17, "target_nam": 17, "eventtarget": 17, "facilit": 18, "snapshot": [18, 27, 32, 38], "textarea": 18, "radio": 18, "checkbox": [18, 58, 60], "getsnapshot": 18, "click": [18, 29, 38, 43, 56, 58, 59], "natur": [18, 56, 58], "srcset": 18, "post": [18, 24, 36], "regard": 18, "stabl": 18, "textbox": 18, "surrog": 18, "utf": [18, 36], "layoutobject": 18, "layouttext": 18, "paint": [18, 32, 37, 38, 40], "includepaintord": 18, "whitelist": 18, "rare": 18, "flatten": [18, 50], "absolut": [18, 20, 22, 37, 48, 58], "includedomrect": 18, "blend": 18, "overlap": 18, "opac": 18, "include_paint_ord": 18, "include_dom_rect": 18, "include_blended_background_color": 18, "include_text_color_opac": 18, "white": [18, 58], "offsetrect": 18, "clientrect": 18, "achiev": 18, "computed_style_whitelist": 18, "include_event_listen": 18, "include_user_agent_shadow_tre": 18, "cachedstoragearea": 19, "deliv": [19, 20, 34, 36, 38, 52], "environ": [20, 44], "safe": 20, "area": [20, 22, 29, 37, 38, 41, 58], "inset": 20, "max": [20, 53, 58], "split": 20, "postur": 20, "pauseifnetworkfetchespend": 20, "sec": [20, 36, 40], "ch": [20, 36, 38], "ambient": [20, 38], "linear": 20, "acceler": [20, 29, 49], "tell": [20, 36, 44], "setdevicemetricsoverrid": 20, "setdevicepostureoverrid": 20, "again": [20, 34, 43, 56, 58, 59, 60], "setdisplayfeaturesoverrid": 20, "factor": [20, 29, 38, 46], "dark": [20, 37], "theme": [20, 37], "slow": 20, "slowdown": 20, "2x": 20, "device_scale_factor": [20, 38], "screen_width": [20, 38], "screen_height": [20, 38], "position_x": [20, 38], "position_i": [20, 38], "dont_set_visible_s": [20, 38], "screen_orient": [20, 38], "display_featur": 20, "device_postur": 20, "innerwidth": [20, 38], "innerheight": [20, 38], "10000000": [20, 38], "autos": [20, 38], "reli": [20, 38, 54, 58], "setvisibles": [20, 38], "multi": [20, 44], "segment": 20, "foldabl": 20, "pupul": 20, "coooki": 20, "gestur": [20, 29, 36, 38], "vision": 20, "defici": 20, "effort": 20, "physiolog": 20, "accur": [20, 42], "medic": 20, "recogn": 20, "latitud": [20, 38], "longitud": [20, 38], "accuraci": [20, 38], "unavail": [20, 38], "hardware_concurr": 20, "hardwar": [20, 53], "concurr": 20, "is_user_act": 20, "is_screen_unlock": 20, "isuseract": 20, "isscreenunlock": 20, "icu": 20, "en_u": 20, "pressur": [20, 29, 35, 38], "pressureobserv": 20, "setpressurestateoverrid": 20, "eventu": [20, 50], "setpressuresourceoverrideen": 20, "respect": [20, 37, 44, 48], "rather": [20, 32, 38, 55], "real": [20, 53, 54, 58], "attempt": [20, 36, 41, 58], "setsensoroverrideen": 20, "timezone_id": 20, "timezon": 20, "dep": 20, "git": 20, "faee8bc70570192d82d2978a71e2a615788597d1": 20, "misc": 20, "metazon": 20, "txt": 20, "max_touch_point": 20, "accept_languag": [20, 36], "user_agent_metadata": [20, 36], "useragentdata": [20, 36], "max_virtual_time_task_starvation_count": 20, "initial_virtual_tim": 20, "synthet": 20, "mani": [20, 32, 36, 56, 57, 58, 59], "elaps": 20, "deadlock": 20, "Not": [20, 25, 29, 36, 38], "android": 20, "dip": [20, 29, 36, 37, 38, 50], "similarli": [21, 24], "storage_area": 22, "unpack": [22, 59, 60], "cli": 22, "pipe": 22, "merg": 22, "dialog": [23, 24, 36, 38], "ever": [23, 56, 58], "rp": 23, "signin": 23, "signup": 23, "accountchoos": 23, "autoreauthn": 23, "confirmidplogin": 23, "confirmidplogincontinu": 23, "errorgotit": 23, "errormoredetail": 23, "termsofservic": 23, "privacypolici": 23, "identityrequestaccount": 23, "dialog_button": 23, "trigger_cooldown": 23, "disable_rejection_delai": 23, "promis": [23, 36, 44], "unimport": 23, "fedidcg": 23, "account_index": 23, "account_url_typ": 23, "cooldown": 23, "show": [23, 27, 37, 38, 44, 58, 59, 60], "recent": [23, 58], "dismiss": [23, 38], "primarili": 23, "appropri": [23, 29, 48], "below": [23, 38, 44], "let": [24, 34, 44, 58, 59, 60], "individu": [24, 36, 38], "stage": [24, 26, 36], "intercept": [24, 36, 38, 54], "wildcard": [24, 36], "escap": [24, 36], "backslash": [24, 36], "author": [24, 36], "challeng": [24, 36], "401": [24, 36], "407": [24, 36], "digest": [24, 36], "decis": [24, 36], "defer": [24, 36, 38], "popup": [24, 36], "possibli": [24, 36], "providecredenti": [24, 36], "intercept_respons": 24, "hop": 24, "response_phras": 24, "binary_response_head": 24, "responsecod": 24, "phrase": [24, 46], "separ": [24, 34, 36, 37, 41, 46, 48], "prefer": [24, 29, 38, 43, 58], "abov": [24, 38, 58], "unless": [24, 38, 44, 58], "utf8": 24, "transmit": [24, 36], "auth_challenge_respons": [24, 36], "handle_auth_request": 24, "failrequest": [24, 36], "fulfillrequest": [24, 36], "continuerequest": [24, 36], "continuewithauth": 24, "fetchrequest": 24, "error_reason": [24, 36], "server": [24, 36, 38, 47, 48, 50, 55], "mutual": [24, 44], "takeresponsebodyforinterceptionasstream": 24, "_redirect": 24, "received_": 24, "differenti": 24, "presenc": [24, 48, 54], "base64encod": [24, 31, 36, 38], "headersreceiv": [24, 36], "sequenti": [24, 31, 36, 50], "getresponsebodi": 24, "responseerrorreason": 24, "responsestatuscod": 24, "distinguish": [24, 36, 42], "301": 24, "302": 24, "303": 24, "307": 24, "308": 24, "redirectedrequestid": 24, "networkid": 24, "handleauthrequest": 24, "encount": [24, 36, 38], "directli": [25, 29, 56, 57], "mozilla": 25, "u": [25, 29, 34, 37, 57, 58, 59], "doc": [25, 38], "storage_api": 25, "bucketnam": 25, "bucket_file_system_loc": 25, "headless": [26, 50, 55, 57, 58, 59], "compress": [26, 36, 38, 52], "speed": [26, 29, 38, 59, 60], "frame_time_tick": 26, "no_display_upd": 26, "beginfram": [26, 50], "beginframecontrol": 26, "compositor": 26, "draw": [26, 37], "goo": 26, "gle": 26, "timetick": 26, "uptim": 26, "60": 26, "666": 26, "commit": [26, 29, 38], "drawn": 26, "onto": 26, "visual": [26, 38, 59, 60], "hasdamag": 26, "damag": 26, "thu": [26, 36, 58], "diagnost": 26, "screenshotdata": 26, "taken": [26, 27, 42], "rtype": [26, 38, 56, 58], "callsit": [27, 42], "alloc": [27, 35, 44], "across": [27, 35, 44, 49], "startsampl": [27, 35], "stopsampl": 27, "heap_object_id": 27, "sampling_interv": [27, 35], "include_objects_collected_by_major_gc": 27, "include_objects_collected_by_minor_gc": 27, "averag": [27, 35], "poisson": 27, "32768": 27, "By": 27, "still": [27, 36, 38, 41, 58], "aliv": 27, "getsamplingprofil": 27, "steadi": 27, "instruct": 27, "major": [27, 29], "gc": 27, "temporari": [27, 31, 36, 48], "minor": 27, "tune": 27, "latenc": [27, 36], "track_alloc": 27, "report_progress": 27, "treat_global_objects_as_root": 27, "capture_numeric_valu": 27, "expose_intern": 27, "exposeintern": 27, "numer": [27, 34, 49], "triplet": 27, "regularli": 27, "seen": 27, "databas": [28, 48], "unsign": [28, 36], "increment": [28, 40, 54], "primari": [28, 48, 49], "key_rang": 28, "entriescount": 28, "keygeneratorvalu": 28, "autoincr": 28, "index_nam": 28, "objectstoredataentri": 28, "hasmor": 28, "proce": [29, 31, 58], "toward": 29, "radiu": 29, "rotat": 29, "tangenti": 29, "plane": 29, "stylu": 29, "degre": 29, "90": 29, "tiltx": 29, "tilti": 29, "clockwis": 29, "pen": 29, "359": 29, "utc": [29, 36], "januari": [29, 36, 59, 60], "1970": [29, 36], "mime": 29, "drag": [29, 56, 58], "mimetyp": [29, 36, 38, 43], "uri": [29, 36], "filenam": [29, 56, 58], "dispatch": 29, "alt": [29, 56, 58], "ctrl": [29, 56, 58], "shift": [29, 32, 37, 38, 40, 56, 58], "8": [29, 36, 38, 54, 56, 58, 59, 60], "unmodified_text": 29, "key_identifi": 29, "windows_virtual_key_cod": 29, "native_virtual_key_cod": 29, "auto_repeat": 29, "is_keypad": 29, "is_system_kei": 29, "keyboard": [29, 38], "keyup": 29, "rawkeydown": 29, "0041": 29, "keya": 29, "altgr": 29, "keypad": 29, "selectal": 29, "execcommand": 29, "nsstandardkeybindingrespond": 29, "editor_command_nam": 29, "h": [29, 34, 36, 52], "click_count": 29, "delta_x": 29, "delta_i": 29, "pointer_typ": 29, "wheel": 29, "touch_point": 29, "touchend": 29, "touchcancel": 29, "touchstart": 29, "touchmov": 29, "per": [29, 32, 34, 36, 38, 44, 46, 48, 49, 50, 55, 59, 60], "compar": [29, 44, 58], "sequenc": [29, 52, 54], "selection_start": 29, "selection_end": 29, "replacement_start": 29, "replacement_end": 29, "im": 29, "imecommitcomposit": 29, "imesetcomposit": 29, "composit": [29, 32], "doesn": [29, 37, 48], "emoji": 29, "dispatchdragev": 29, "scale_factor": 29, "relative_spe": 29, "gesture_source_typ": 29, "synthes": [29, 52], "pinch": 29, "period": [29, 36, 48], "800": 29, "x_distanc": 29, "y_distanc": 29, "x_overscrol": 29, "y_overscrol": 29, "prevent_fl": 29, "repeat_count": 29, "repeat_delay_m": 29, "interaction_marker_nam": 29, "fling": 29, "swipe": 29, "250": 29, "tap_count": 29, "tap": 29, "touchdown": 29, "touchup": 29, "m": [29, 36], "50": [29, 56, 58], "doubl": [29, 56], "setinterceptdrag": 29, "output": [31, 53, 61], "eof": 31, "itself": [32, 36, 44, 56], "sticki": 32, "constraint": 32, "purpos": [32, 36, 54], "matrix": 32, "compositingreason": 32, "compositingreasonid": 32, "inspect": [32, 34, 37, 38, 44, 50, 53, 60], "compos": [32, 58], "snapshot_id": 32, "min_repeat_count": 32, "min_dur": 32, "clip_rect": 32, "replai": [32, 36], "from_step": 32, "to_step": 32, "bitmap": 32, "till": 32, "canva": 32, "medialogrecord": 34, "kmessag": 34, "medialogmessagelevel": 34, "thing": [34, 58], "dvlog": 34, "pipelinestatu": 34, "soon": [34, 52], "howev": [34, 36, 56, 58], "awai": 34, "introduc": 34, "hopefulli": 34, "kmediapropertychang": 34, "kmediaeventtrigg": 34, "kmediaerror": 34, "pipelinestatuscod": 34, "pipeline_statu": 34, "potenti": [34, 36, 38, 48], "ie": [34, 58], "decodererror": 34, "windowserror": 34, "extra": [34, 36, 38, 55], "hresult": 34, "codec": [34, 49], "propvalu": 34, "congest": 34, "chronolog": 34, "decim": 35, "hexadecim": 35, "0x": 35, "prefix": 35, "counter": [35, 37, 42, 54], "presum": 35, "volatil": 35, "oomintervent": 35, "purg": 35, "startup": 35, "retrun": 35, "jseventlisten": 35, "prepar": 35, "leak": 35, "spellcheck": 35, "essenti": 35, "garbag": [35, 42, 44, 52], "suppress": [35, 48], "suppress_random": 35, "random": [35, 59, 60], "perceiv": 36, "texttrack": 36, "signedexchang": 36, "cspviolationreport": 36, "loader": [36, 38], "timedout": 36, "accessdeni": 36, "connectionclos": 36, "connectionreset": 36, "connectionrefus": 36, "connectionabort": 36, "connectionfail": 36, "namenotresolv": 36, "internetdisconnect": 36, "addressunreach": 36, "blockedbycli": [36, 41], "blockedbyrespons": 36, "arbitrari": 36, "past": [36, 59, 60], "supposedli": 36, "samesit": 36, "tool": 36, "ietf": 36, "west": 36, "00": 36, "legaci": [36, 52], "abil": 36, "nonsecur": 36, "requesttim": 36, "baselin": 36, "dn": 36, "ssl": [36, 46], "handshak": 36, "settl": 36, "respondwith": 36, "rout": 36, "verylow": 36, "veryhigh": 36, "referr": [36, 38], "postdata": 36, "sct": 36, "unlik": [36, 38, 43, 44], "algorithm": [36, 37, 46], "tl": [36, 46], "quic": [36, 46], "exchang": [36, 46], "subject": [36, 46], "san": 36, "ip": [36, 38], "ca": [36, 46], "compli": 36, "transpar": [36, 37, 38], "clienthello": 36, "ec": [36, 46], "dh": [36, 46], "aead": [36, 46], "signatureschem": 36, "subresourc": 36, "corp": 36, "sri": 36, "mismatch": [36, 41], "disallowedbymod": 36, "invalidrespons": 36, "wildcardoriginnotallow": 36, "missingalloworiginhead": 36, "multiplealloworiginvalu": 36, "invalidalloworiginvalu": 36, "alloworiginmismatch": 36, "invalidallowcredenti": 36, "corsdisabledschem": 36, "preflightinvalidstatu": 36, "preflightdisallowedredirect": 36, "preflightwildcardoriginnotallow": 36, "preflightmissingalloworiginhead": 36, "preflightmultiplealloworiginvalu": 36, "preflightinvalidalloworiginvalu": 36, "preflightalloworiginmismatch": 36, "preflightinvalidallowcredenti": 36, "preflightmissingallowextern": 36, "preflightinvalidallowextern": 36, "preflightmissingallowprivatenetwork": 36, "preflightinvalidallowprivatenetwork": 36, "invalidallowmethodspreflightrespons": 36, "invalidallowheaderspreflightrespons": 36, "methoddisallowedbypreflightrespons": 36, "headerdisallowedbypreflightrespons": 36, "redirectcontainscredenti": 36, "insecureprivatenetwork": 36, "invalidprivatenetworkaccess": 36, "unexpectedprivatenetworkaccess": 36, "nocorsredirectmodenotfollow": 36, "preflightmissingprivatenetworkaccessid": 36, "preflightmissingprivatenetworkaccessnam": 36, "privatenetworkaccesspermissionunavail": 36, "privatenetworkaccesspermissiondeni": 36, "localnetworkaccesspermissiondeni": 36, "trust_token": 36, "srr": 36, "whom": 36, "alternativejobwonwithoutrac": 36, "alternativejobwonrac": 36, "mainjobwonrac": 36, "mappingmiss": 36, "dnsalpnh3jobwonwithoutrac": 36, "dnsalpnh3jobwonrac": 36, "unspecifiedreason": 36, "router": 36, "race": [36, 38], "reus": [36, 44], "matchedsourcetyp": 36, "matchedsourc": 36, "just": [36, 56, 57, 58, 59, 60], "payloaddata": 36, "visit": [36, 48], "toplevelsit": 36, "sameparti": 36, "65535": 36, "unix": 36, "secureonli": 36, "samesitestrict": 36, "samesitelax": 36, "samesiteunspecifiedtreatedaslax": 36, "samesitenoneinsecur": 36, "userprefer": 36, "thirdpartyphaseout": 36, "thirdpartyblockedinfirstpartyset": 36, "syntaxerror": 36, "schemenotsupport": 36, "overwritesecur": 36, "invaliddomain": 36, "invalidprefix": 36, "unknownerror": 36, "schemefulsamesitestrict": 36, "schemefulsamesitelax": 36, "schemefulsamesiteunspecifiedtreatedaslax": 36, "samepartyfromcrosspartycontext": 36, "samepartyconflictswithotherattribut": 36, "namevaluepairexceedsmaxs": 36, "disallowedcharact": 36, "nocookiecont": 36, "notonpath": 36, "domainmismatch": 36, "portmismatch": 36, "schememismatch": 36, "3pcd": 36, "exempt": 36, "userset": 36, "tpcdmetadata": 36, "tpcddeprecationtri": 36, "topleveltpcddeprecationtri": 36, "tpcdheurist": 36, "enterprisepolici": 36, "samesitenonecookiesinsandbox": 36, "sometim": [36, 55, 59, 60], "webpackag": 36, "yasskin": 36, "httpbi": 36, "impl": 36, "rfc": 36, "hex": [36, 37], "cert": 36, "sha256": 36, "cbor": 36, "signaturesig": 36, "signatureintegr": 36, "signaturecerturl": 36, "signaturecertsha256": 36, "signaturevalidityurl": 36, "signaturetimestamp": 36, "tcp_nodelai": 36, "blockfrominsecuretomorepriv": 36, "warnfrominsecuretomorepriv": 36, "preflightblock": 36, "preflightwarn": 36, "permissionblock": 36, "permissionwarn": 36, "sameorigin": [36, 38], "sameoriginallowpopup": 36, "restrictproperti": 36, "unsafenon": 36, "sameoriginpluscoep": 36, "restrictpropertiespluscoep": 36, "noopenerallowpopup": 36, "requirecorp": 36, "markedforremov": 36, "upload": [36, 56], "deliveri": 36, "made": [36, 44, 58], "later": [36, 44], "corb": 36, "setacceptedencod": 36, "raw_respons": 36, "isnavigationrequest": 36, "partitionkei": 36, "download_throughput": 36, "upload_throughput": 36, "connection_typ": 36, "packet_loss": 36, "packet_queue_length": 36, "packet_reord": 36, "internet": 36, "throughput": 36, "webrtc": [36, 38], "loss": 36, "percent": 36, "packetreord": 36, "max_total_buffer_s": 36, "max_resource_buffer_s": 36, "max_post_data_s": 36, "buffer": [36, 40, 44, 52, 53], "preserv": 36, "longest": 36, "getcooki": 36, "der": 36, "subfram": 36, "multipart": 36, "mandatori": 36, "withcredenti": 36, "bypass": [36, 38, 44, 50, 55], "toggl": [36, 38], "cache_dis": 36, "overwrit": [36, 57], "enable_third_party_cookie_restrict": 36, "disable_third_party_cookie_metadata": 36, "disable_third_party_cookie_heurist": 36, "bahavior": 36, "3pc": 36, "restrict": [36, 59, 60], "grace": [36, 48], "continueinterceptedrequest": 36, "datalength": 36, "net_error_list": 36, "likewis": 36, "auth": [36, 55], "redirectrespons": 36, "webtransport": [36, 38], "dispos": [36, 50, 55], "tcpsocket": 36, "latter": 36, "wire": 36, "proper": [36, 58], "duplic": 36, "concatent": 36, "n": [36, 38, 56], "headerstext": 36, "verbatim": 36, "space": [36, 37, 58], "establish": 36, "correct": [36, 55, 58], "200": [36, 52, 56, 58, 59, 60], "304": 36, "serializ": [36, 44, 57, 58], "103": 36, "earli": 36, "common": 36, "succeed": [36, 52], "alreadyexist": 36, "signifi": 36, "und": 36, "preemptiv": 36, "wbn": 36, "bundl": [36, 43, 48], "webpag": [36, 38, 58], "webbundl": 36, "And": 36, "enablereportingapi": 36, "atop": 37, "outlin": [37, 38], "grid": 37, "cell": 37, "ruler": 37, "shown": [37, 38, 46, 59, 60], "neg": 37, "rowlinecolor": 37, "columnlinecolor": 37, "row": 37, "rowlinedash": 37, "columnlinedash": 37, "gap": 37, "hatch": 37, "flex": 37, "justifi": 37, "align": 37, "arrow": [37, 56], "grew": 37, "shrank": 37, "solid": 37, "tooltip": 37, "a11i": 37, "ratio": [37, 38], "snapport": 37, "snap": 37, "dual": 37, "hing": 37, "bar": [37, 38], "app": [37, 38, 43], "cover": [37, 42], "searchfornod": 37, "searchforuashadowdom": 37, "captureareascreenshot": 37, "showdist": 37, "include_dist": 37, "include_styl": 37, "viewer": 37, "content_outline_color": 37, "reliabl": [37, 44], "highlightnod": 37, "highlight_config": 37, "source_order_config": 37, "enter": [37, 38, 58], "hover": [37, 56], "container_query_highlight_config": 37, "flex_node_highlight_config": 37, "fp": [37, 49], "grid_node_highlight_config": 37, "hinge_config": 37, "hidehing": 37, "isolated_element_highlight_config": 37, "bottleneck": 37, "scroll_snap_highlight_config": 37, "window_controls_overlay_config": 37, "setinspectmod": 37, "ask": [37, 59, 60], "parentisad": 38, "createdbyadscript": 38, "matchedblockingrul": 38, "securelocalhost": 38, "insecureschem": 38, "insecureancestor": 38, "notisol": 38, "notisolatedfeaturedis": 38, "sharedarraybuff": 38, "sharedarraybufferstransferallow": 38, "performancemeasurememori": 38, "performanceprofil": 38, "cpp": 38, "permissions_polici": 38, "permissions_policy_featur": 38, "brows": [38, 48, 56, 58], "topic": 38, "dpr": 38, "downlink": 38, "ect": 38, "reduc": 38, "motion": 38, "rtt": 38, "arch": 38, "entropi": [38, 48], "socket": 38, "fenc": 38, "unpartit": 38, "interest": [38, 48], "cohort": 38, "detector": 38, "otp": 38, "publickei": 38, "wake": 38, "lock": 38, "unrestrict": 38, "xr": 38, "spatial": 38, "iframeattribut": 38, "infencedframetre": 38, "inisolatedapp": 38, "trial": [38, 60], "notsupport": [38, 41], "wrongorigin": 38, "invalidsignatur": 38, "wrongvers": 38, "featuredis": 38, "tokendis": 38, "featuredisabledforus": 38, "unknowntri": 38, "validtokennotprovid": 38, "osnotsupport": 38, "trialnotallow": 38, "parsedtoken": 38, "parsabl": 38, "hostnam": 38, "localhost": [38, 59], "127": 38, "take": [38, 44, 46, 48, 58], "suffix": 38, "googl": [38, 48], "co": 38, "uk": 38, "gate": 38, "histori": [38, 58], "screencast": 38, "swap": 38, "pare": 38, "recover": 38, "ideal": 38, "sansserif": 38, "anchorclick": 38, "formsubmissionget": 38, "formsubmissionpost": 38, "httpheaderrefresh": 38, "initialframenavig": 38, "metatagrefresh": 38, "pageblockinterstiti": 38, "scriptiniti": 38, "currenttab": 38, "newtab": 38, "newwindow": [38, 50], "64": [38, 54], "suitabl": [38, 58], "noreferr": 38, "noreferrerwhendowngrad": 38, "originwhencrossorigin": 38, "strictorigin": 38, "strictoriginwhencrossorigin": 38, "unsafeurl": 38, "compil": [38, 44], "producecompilationcach": 38, "won": [38, 57, 58, 59], "comparison": 38, "mimic": 38, "understand": 38, "emb": 38, "sharetargetparam": 38, "experi": [38, 54, 58], "todo": [38, 41, 43, 59], "1231886": 38, "incub": 38, "gh": 38, "autoaccept": 38, "autoreject": 38, "autooptout": 38, "backforwardcacherestor": 38, "notprimarymainfram": 38, "backforwardcachedis": 38, "relatedactivecontentsexist": 38, "httpstatusnotok": 38, "schemenothttporhttp": 38, "wasgrantedmediaaccess": 38, "disableforrenderframehostcal": 38, "domainnotallow": 38, "httpmethodnotget": 38, "subframeisnavig": 38, "cachelimit": 38, "javascriptexecut": 38, "rendererprocesskil": [38, 41], "rendererprocesscrash": [38, 41], "schedulertrackedfeatureus": 38, "conflictingbrowsinginst": 38, "cacheflush": 38, "serviceworkerversionactiv": 38, "sessionrestor": 38, "serviceworkerpostmessag": 38, "enteredbackforwardcachebeforeserviceworkerhostad": 38, "renderframehostreused_samesit": 38, "renderframehostreused_crosssit": 38, "serviceworkerclaim": 38, "ignoreeventandevict": 38, "haveinnercont": 38, "timeoutputtingincach": 38, "backforwardcachedisabledbylowmemori": 38, "backforwardcachedisabledbycommandlin": 38, "networkrequestdatapipedrainedasbytesconsum": 38, "networkrequestredirect": 38, "networkrequesttimeout": 38, "networkexceedsbufferlimit": 38, "navigationcancelledwhilerestor": 38, "notmostrecentnavigationentri": 38, "backforwardcachedisabledforprerend": 38, "useragentoverridediff": 38, "foregroundcachelimit": 38, "browsinginstancenotswap": 38, "backforwardcachedisabledfordeleg": 38, "unloadhandlerexistsinmainfram": 38, "unloadhandlerexistsinsubfram": 38, "serviceworkerunregistr": 38, "cachecontrolnostor": 38, "cachecontrolnostorecookiemodifi": 38, "cachecontrolnostorehttponlycookiemodifi": 38, "noresponsehead": 38, "activationnavigationsdisallowedforbug1234857": 38, "errordocu": 38, "fencedframesembedd": 38, "cookiedis": 38, "httpauthrequir": 38, "cookieflush": 38, "broadcastchannelonmessag": 38, "webviewsettingschang": 38, "webviewjavascriptobjectchang": 38, "webviewmessagelistenerinject": 38, "webviewsafebrowsingallowlistchang": 38, "webviewdocumentstartjavascriptchang": 38, "mainresourcehascachecontrolnostor": 38, "mainresourcehascachecontrolnocach": 38, "subresourcehascachecontrolnostor": 38, "subresourcehascachecontrolnocach": 38, "containsplugin": 38, "documentload": 38, "outstandingnetworkrequestoth": 38, "requestedmidipermiss": 38, "requestedaudiocapturepermiss": 38, "requestedvideocapturepermiss": 38, "requestedbackforwardcacheblockedsensor": 38, "requestedbackgroundworkpermiss": 38, "broadcastchannel": 38, "webxr": 38, "weblock": 38, "webhid": 38, "webshar": 38, "requestedstorageaccessgr": 38, "webnfc": 38, "outstandingnetworkrequestfetch": 38, "outstandingnetworkrequestxhr": 38, "appbann": 38, "webdatabas": 38, "pictureinpictur": 38, "speechrecogn": 38, "idlemanag": 38, "paymentmanag": 38, "speechsynthesi": 38, "webotpservic": 38, "outstandingnetworkrequestdirectsocket": 38, "injectedjavascript": 38, "injectedstylesheet": 38, "keepaliverequest": 38, "indexeddbev": 38, "jsnetworkrequestreceivedcachecontrolnostoreresourc": 38, "webrtcsticki": 38, "webtransportsticki": 38, "websocketsticki": 38, "livemediastreamtrack": 38, "unloadhandl": 38, "parserabort": 38, "contentsecurityhandl": 38, "contentwebauthenticationapi": 38, "contentfilechoos": 38, "contentseri": 38, "contentfilesystemaccess": 38, "contentmediadevicesdispatcherhost": 38, "contentwebbluetooth": 38, "contentwebusb": 38, "contentmediasessionservic": 38, "contentscreenread": 38, "contentdiscard": 38, "embedderpopupblockertabhelp": 38, "embeddersafebrowsingtriggeredpopupblock": 38, "embeddersafebrowsingthreatdetail": 38, "embedderappbannermanag": 38, "embedderdomdistillerviewersourc": 38, "embedderdomdistillerselfdeletingrequestdeleg": 38, "embedderoominterventiontabhelp": 38, "embedderofflinepag": 38, "embedderchromepasswordmanagerclientbindcredentialmanag": 38, "embedderpermissionrequestmanag": 38, "embeddermodaldialog": 38, "embedderextens": 38, "embedderextensionmessag": 38, "embedderextensionmessagingforopenport": 38, "embedderextensionsentmessagetocachedfram": 38, "requestedbywebviewcli": 38, "postmessagebywebviewcli": 38, "cachecontrolnostoredeviceboundsessiontermin": 38, "cachelimitprun": 38, "supportpend": 38, "pagesupportneed": 38, "blockag": 38, "anonym": [38, 44], "seed": 38, "addscripttoevaluateonnewdocu": [38, 44], "world_nam": 38, "run_immedi": 38, "world": [38, 44], "bring": 38, "from_surfac": 38, "capture_beyond_viewport": 38, "beyond": [38, 42], "mhtml": 38, "tri": [38, 44], "hook": [38, 50], "minidump": 38, "grant_univeral_access": 38, "univers": 38, "caution": 38, "cookie_nam": 38, "cook": 38, "enable_file_chooser_opened_ev": 38, "regardless": [38, 43, 48], "setinterceptfilechooserdialog": 38, "webappenablemanifestid": 38, "appid": 38, "recommendedid": 38, "manifest_id": [38, 43], "manifestid": [38, 43], "csslayoutviewport": 38, "cssvisualviewport": 38, "contents": 38, "dp": 38, "csscontents": 38, "fact": 38, "currentindex": 38, "prompt_text": 38, "onbeforeunload": 38, "errortext": 38, "entry_id": 38, "landscap": 38, "display_header_foot": 38, "print_background": 38, "paper_width": 38, "paper_height": 38, "margin_top": 38, "margin_bottom": 38, "margin_left": 38, "margin_right": 38, "page_rang": 38, "header_templ": 38, "footer_templ": 38, "prefer_css_page_s": 38, "transfer_mod": [38, 52], "generate_tagged_pdf": 38, "generate_document_outlin": 38, "pdf": 38, "paper": 38, "footer": 38, "graphic": [38, 49], "inch": 38, "5": [38, 56, 58], "11": [38, 59, 60], "1cm": 38, "13": 38, "quietli": 38, "cap": 38, "greater": 38, "pagenumb": 38, "totalpag": 38, "span": [38, 58], "headertempl": 38, "fit": [38, 44], "choic": [38, 59, 60], "returnasstream": [38, 52], "append": 38, "ignore_cach": [38, 58], "script_to_evaluate_on_load": [38, 58], "refresh": 38, "dataurl": 38, "accident": [38, 44], "unintend": 38, "removescripttoevaluateonnewdocu": 38, "acknowledg": 38, "for_script": 38, "chooser": 38, "lifecycl": 38, "is_allow": 38, "short": [38, 46, 56, 59, 60], "1440085": 38, "12hvmfxyj5jc": 38, "ejr5omwsa2bqtjsbgglki6ziyx0_wpa": 38, "puppet": 38, "whatwg": 38, "multipag": 38, "rph": 38, "transact": 38, "sctn": [38, 54], "spc": 38, "max_width": 38, "max_height": 38, "every_nth_fram": 38, "th": 38, "runifwaitingfordebugg": [38, 50], "interceptfilechoos": 38, "frameset": 38, "though": 38, "interstiti": 38, "iff": [38, 41, 44, 52], "act": [38, 58], "engag": 38, "stall": 38, "handlejavascriptdialog": 38, "bfcach": 38, "backforwardcach": 38, "startscreencast": 38, "setgeneratecompilationcach": 38, "time_domain": 39, "performanceobserv": 40, "largest_contentful_paint": 40, "trim": 40, "instabl": 40, "layout_shift": 40, "score": 40, "performanceentri": 40, "entrytyp": 40, "lifetim": [40, 53], "reportperformancetimelin": 40, "speculationruleset": 41, "textcont": 41, "nav": 41, "specul": 41, "1425354": 41, "sourceisnotjsonobject": 41, "invalidrulesskip": 41, "although": [41, 58], "prefetchwithsubresourc": 41, "href": [41, 58, 60], "concept": 41, "pipelin": 41, "upgrad": 41, "finalstatu": 41, "prerender2": 41, "lowenddevic": 41, "invalidschemeredirect": 41, "invalidschemenavig": 41, "navigationrequestblockedbycsp": 41, "mainframenavig": 41, "mojobinderpolici": 41, "triggerdestroi": 41, "navigationnotcommit": 41, "navigationbadhttpstatu": 41, "clientcertrequest": 41, "navigationrequestnetworkerror": 41, "cancelallhostsfortest": 41, "didfailload": 41, "sslcertificateerror": 41, "loginauthrequest": 41, "uachangerequiresreload": 41, "audiooutputdevicerequest": 41, "mixedcont": 41, "triggerbackground": 41, "memorylimitexceed": 41, "datasaveren": 41, "triggerurlhaseffectiveurl": 41, "activatedbeforestart": 41, "inactivepagerestrict": 41, "startfail": 41, "timeoutbackground": 41, "crosssiteredirectininitialnavig": 41, "crosssitenavigationininitialnavig": 41, "samesitecrossoriginredirectnotoptinininitialnavig": 41, "samesitecrossoriginnavigationnotoptinininitialnavig": 41, "activationnavigationparametermismatch": 41, "activatedinbackground": 41, "embedderhostdisallow": 41, "activationnavigationdestroyedbeforesuccess": 41, "tabclosedbyusergestur": 41, "tabclosedwithoutusergestur": 41, "primarymainframerendererprocesscrash": 41, "primarymainframerendererprocesskil": 41, "activationframepolicynotcompat": 41, "preloadingdis": 41, "batterysaveren": 41, "activatedduringmainframenavig": 41, "preloadingunsupportedbywebcont": 41, "crosssiteredirectinmainframenavig": 41, "crosssitenavigationinmainframenavig": 41, "samesitecrossoriginredirectnotoptininmainframenavig": 41, "samesitecrossoriginnavigationnotoptininmainframenavig": 41, "memorypressureontrigg": 41, "memorypressureaftertrigg": 41, "prerenderingdisabledbydevtool": 41, "speculationruleremov": 41, "activatedwithauxiliarybrowsingcontext": 41, "maxnumofrunningeagerprerendersexceed": 41, "maxnumofrunningnoneagerprerendersexceed": 41, "maxnumofrunningembedderprerendersexceed": 41, "prerenderingurlhaseffectiveurl": 41, "redirectedprerenderingurlhaseffectiveurl": 41, "activationurlhaseffectiveurl": 41, "javascriptinterfacead": 41, "javascriptinterfaceremov": 41, "allprerenderingcancel": 41, "windowclos": 41, "slownetwork": 41, "otherprerenderedpageactiv": 41, "v8optimizerdis": 41, "prerenderfailedduringprefetch": 41, "browsingdataremov": 41, "preloadingtriggeringoutcom": 41, "1384419": 41, "revisit": 41, "aren": 41, "prefetchallow": 41, "prefetchfailedineligibleredirect": 41, "prefetchfailedinvalidredirect": 41, "prefetchfailedmimenotsupport": 41, "prefetchfailedneterror": 41, "prefetchfailednon2xx": 41, "prefetchevictedafterbrowsingdataremov": 41, "prefetchevictedaftercandidateremov": 41, "prefetchevictedfornewerprefetch": 41, "prefetchheldback": 41, "prefetchineligibleretryaft": 41, "prefetchisprivacydecoi": 41, "prefetchisstal": 41, "prefetchnoteligiblebrowsercontextofftherecord": 41, "prefetchnoteligibledatasaveren": 41, "prefetchnoteligibleexistingproxi": 41, "prefetchnoteligiblehostisnonuniqu": 41, "prefetchnoteligiblenondefaultstoragepartit": 41, "prefetchnoteligiblesamesitecrossoriginprefetchrequiredproxi": 41, "prefetchnoteligibleschemeisnothttp": 41, "prefetchnoteligibleuserhascooki": 41, "prefetchnoteligibleuserhasservicework": 41, "prefetchnoteligibleuserhasserviceworkernofetchhandl": 41, "prefetchnoteligibleredirectfromservicework": 41, "prefetchnoteligibleredirecttoservicework": 41, "prefetchnoteligiblebatterysaveren": 41, "prefetchnoteligiblepreloadingdis": 41, "prefetchnotfinishedintim": 41, "prefetchnotstart": 41, "prefetchnotusedcookieschang": 41, "prefetchproxynotavail": 41, "prefetchresponseus": 41, "prefetchsuccessfulbutnotus": 41, "prefetchnotusedprobefail": 41, "upsert": 41, "give": 41, "mojo": 41, "incompat": 41, "deoptim": 42, "microsecond": 42, "adjac": 42, "starttim": 42, "insid": 42, "granular": 42, "incomplet": 42, "call_count": 42, "allow_triggered_upd": 42, "precis": 42, "unnecessari": 42, "profileend": 42, "takeprecisecoverag": 42, "trig": 42, "replica": 43, "crsrc": 43, "web_appl": 43, "web_app_os_integration_st": 43, "drc": 43, "9910d3be894c8f142c977ba1023f30a656bc13fc": 43, "l": 43, "67": 43, "iana": 43, "assign": [43, 50, 57, 59, 60], "xhtml": 43, "link_captur": 43, "display_mod": 43, "unrecogn": 43, "desktoppwaslinkcapturingwithscopeextens": 43, "webappenablescopeextens": 43, "linkcaptur": 43, "339453269": 43, "chromeo": 43, "yet": [43, 50], "o": [43, 49, 52, 57], "webapp": 43, "commonli": 43, "dev": 43, "learn": 43, "badgecount": 43, "install_url_or_bundle_url": 43, "install_url": 43, "iwa": 43, "337872319": 43, "meet": 43, "swbn": 43, "allowlist": 43, "attachtotarget": [43, 50], "339454034": 43, "maintain": [44, 48], "generatepreview": 44, "returnbyvalu": 44, "maxnodedepth": 44, "includeshadowtre": 44, "met": 44, "primit": 44, "stringifi": [44, 56], "nan": 44, "infin": 44, "bigint": 44, "liter": 44, "sure": [44, 55, 56], "constructor": 44, "abbrevi": 44, "formatt": 44, "ml": 44, "hasbodi": 44, "bodygetterid": 44, "did": [44, 46], "accessor": 44, "getter": 44, "setter": 44, "convent": 44, "unserializ": 44, "dictionari": [44, 49, 57], "assert": [44, 54], "preced": 44, "debuggerid": 44, "execution_context_nam": 44, "executioncontextnam": 44, "unclear": 44, "bug": [44, 49], "1169639": 44, "executioncontext": 44, "worldnam": 44, "promise_object_id": 44, "strace": 44, "function_declar": 44, "unique_context_id": 44, "serialization_opt": 44, "overriden": 44, "await": [44, 55, 56, 59, 60], "objectgroup": 44, "contextid": 44, "persist_script": 44, "disable_break": 44, "repl_mod": 44, "allow_unsafe_eval_blocked_by_csp": 44, "uniquecontextid": 44, "offer": 44, "disablebreak": 44, "replmod": 44, "themselv": 44, "eval": 44, "settimeout": 44, "setinterv": 44, "callabl": [44, 58], "error_object_id": 44, "portion": 44, "useds": 44, "totals": 44, "embedderheapuseds": 44, "backingstorages": 44, "own_properti": 44, "accessor_properties_onli": 44, "non_indexed_properties_onli": 44, "internalproperti": 44, "privateproperti": 44, "const": 44, "prototype_object_id": 44, "unsubscrib": 44, "getstacktrac": 44, "parentid": 44, "logger": 44, "unnam": 44, "unhandl": 44, "revok": 44, "weak": 46, "sha1": 46, "modern": 46, "obsolet": 46, "highest": 46, "badreput": 46, "safeti": 46, "tip": 46, "reput": 46, "answer": 46, "handlecertificateerror": 46, "cryptograph": 46, "last_chanc": 47, "force_update_on_page_load": 47, "protect": 48, "audienc": 48, "additionalbid": 48, "additionalbidwin": 48, "toplevelbid": 48, "topleveladditionalbid": 48, "configresolv": 48, "bidderj": 48, "bidderwasm": 48, "sellerj": 48, "biddertrustedsign": 48, "sellertrustedsign": 48, "documentaddmodul": 48, "documentselecturl": 48, "documentrun": 48, "documentset": 48, "documentappend": 48, "documentdelet": 48, "documentclear": 48, "documentget": 48, "workletset": 48, "workletappend": 48, "workletdelet": 48, "workletclear": 48, "workletget": 48, "workletkei": 48, "workletentri": 48, "workletlength": 48, "workletremainingbudget": 48, "headerset": 48, "headerappend": 48, "headerdelet": 48, "headerclear": 48, "selecturl": 48, "absenc": 48, "vari": 48, "uint32": 48, "internalerror": 48, "insufficientsourcecapac": 48, "insufficientuniquedestinationcapac": 48, "excessivereportingorigin": 48, "prohibitedbybrowserpolici": 48, "successnois": 48, "destinationreportinglimitreach": 48, "destinationgloballimitreach": 48, "destinationbothlimitsreach": 48, "reportingoriginspersitelimitreach": 48, "exceedsmaxchannelcapac": 48, "exceedsmaxscopeschannelcapac": 48, "exceedsmaxtriggerstatecardin": 48, "exceedsmaxeventstateslimit": 48, "destinationperdayreportinglimitreach": 48, "successdroppedlowerprior": 48, "nocapacityforattributiondestin": 48, "nomatchingsourc": 48, "excessiveattribut": 48, "prioritytoolow": 48, "neverattributedsourc": 48, "nomatchingsourcefilterdata": 48, "nomatchingconfigur": 48, "excessivereport": 48, "falselyattributedsourc": 48, "reportwindowpass": 48, "notregist": 48, "reportwindownotstart": 48, "nomatchingtriggerdata": 48, "nohistogram": 48, "insufficientbudget": 48, "insufficientnamedbudget": 48, "websit": 48, "cctld": 48, "issuerorigin": 48, "intact": 48, "first_party_url": 48, "third_party_url": 48, "embed": 48, "turtledov": 48, "dictdef": 48, "generatebidinterestgroup": 48, "expirationtim": 48, "lifetimem": 48, "joiningorigin": 48, "overrideact": 48, "usagebreakdown": 48, "quota_s": 48, "quotas": 48, "ownerorigin": 48, "withdraw": 48, "nois": 48, "ignoreifpres": 48, "notifi": [48, 50, 53], "somethingbid": 48, "interestgroupauctionev": 48, "worklet": 48, "signal": [48, 52], "care": [48, 58], "wrap": [48, 52], "processor": 49, "pci": 49, "vendor": 49, "driver": [49, 59, 60], "sy": 49, "decod": 49, "vp9": 49, "framer": 49, "h264": 49, "fraction": [49, 52], "denomin": 49, "24": 49, "24000": 49, "1001": 49, "yuv": 49, "workaround": 49, "cumul": 49, "feature_st": 49, "modelnam": 49, "On": 49, "macbookpro": 49, "modelvers": 49, "10": [49, 58, 59, 60], "devtools_agent_host_impl": 50, "cc": 50, "ss": 50, "q": 50, "f": [50, 57], "22": 50, "ktypetab": 50, "5b": 50, "5d": 50, "plan": 50, "retir": 50, "991325": 50, "wait_for_debugger_on_start": 50, "filter_": 50, "monitor": 50, "setautoattach": 50, "dispose_on_detach": [50, 55], "proxy_serv": [50, 55, 59], "proxy_bypass_list": [50, 55, 59], "origins_with_universal_network_access": [50, 55], "incognito": 50, "unlimit": [50, 55], "constitut": [50, 55], "enable_begin_frame_control": 50, "for_tab": 50, "shell": 50, "maco": 50, "foreground": 50, "binding_nam": 50, "inherit_permiss": 50, "channel": [50, 53], "bindingnam": 50, "onmessag": 50, "handlemessag": 50, "callback": [50, 53, 58, 60], "createbrowsercontext": 50, "auto_attach": 50, "autoattachrel": 50, "watch": 50, "discov": 50, "setdiscovertarget": 50, "detachfromtarget": 50, "got": 51, "dump": [52, 57, 58], "infra": 52, "kilobyt": 52, "mb": 52, "memory_dump_request_arg": 52, "memory_instrument": 52, "perfetto": 52, "perfettoconfig": 52, "sync_id": 52, "determinist": 52, "level_of_detail": 52, "dumpguid": 52, "buffer_usage_reporting_interv": 52, "stream_format": 52, "trace_config": 52, "perfetto_config": 52, "tracing_backend": 52, "reportev": 52, "protobuf": 52, "approxim": 52, "flush": 52, "lost": 52, "ring": 52, "graph": 53, "audiocontext": 53, "audiocontextst": 53, "clamp": 53, "spent": 53, "divid": 53, "quantum": 53, "multipli": 53, "capac": 53, "glitch": 53, "varianc": 53, "outgo": 53, "largeblob": 54, "credblob": 54, "fidoalli": 54, "fido": 54, "v2": 54, "rd": 54, "20201208": 54, "minpinlength": 54, "p": [54, 59, 60], "20210615": 54, "prf": 54, "succe": 54, "backup": 54, "elig": 54, "BE": 54, "ecdsa": 54, "pkc": 54, "defaultbackupelig": 54, "defaultbackupst": 54, "publickeycredentialent": 54, "displaynam": 54, "publickeycredentialuserent": 54, "enable_ui": 54, "demo": 54, "closer": 54, "is_bogus_signatur": 54, "is_bad_uv": 54, "is_bad_up": 54, "isbogussignatur": 54, "isbaduv": 54, "isbadup": 54, "uv": 54, "publickeycredenti": 54, "signalunknowncredenti": 54, "signalcurrentuserdetail": 54, "filepath": 55, "dat": 55, "export": 55, "requests_style_cooki": 55, "get_al": 55, "requests_cookie_format": 55, "kwarg": [55, 57, 58], "usual": [55, 56], "nodriv": [55, 57, 58, 59], "besid": 55, "your": [55, 58, 59, 60], "know": 55, "__init__": 55, "stubborn": 55, "correctli": 55, "kill": 55, "classmethod": 55, "browser_executable_path": [55, 57, 59], "cookiejar": 55, "especi": [55, 58], "union": [55, 56, 58], "alia": [55, 58], "welcom": [55, 58], "safest": [55, 58], "mostli": 55, "sock": 55, "accessibilityev": 55, "videocapturepantiltzoom": 55, "max_column": 55, "word": 56, "_node": [56, 58], "opbject": 56, "seper": [56, 59], "quit": [56, 58], "expens": [56, 58], "advis": 56, "bunch": [56, 58], "js_function": 56, "eg": [56, 58], "elem": [56, 59, 60], "blabla": 56, "consolelog": 56, "myfunct": 56, "ab": 56, "_until_ev": [56, 58], "atm": 56, "mouseov": 56, "cours": 56, "100px": [56, 58], "200px": [56, 58], "look": [56, 58], "slower": [56, 58], "smooth": [56, 58], "stuck": [56, 58], "py": 56, "meth": 56, "keystrok": 56, "rn": 56, "spacebar": 56, "wonder": 56, "file_path": 56, "needl": 56, "sai": [56, 59, 60], "fileinputel": 56, "temp": 56, "myuser": [56, 59], "lol": 56, "gif": 56, "often": 56, "02": 56, "08": 56, "2024": 56, "programatt": 56, "concaten": 56, "runtimeerror": 56, "rais": [56, 57, 58], "pathlik": [56, 57, 58], "half": [56, 58], "dot": [56, 57], "coord": 56, "folder": [56, 57, 58, 59, 60], "desir": [56, 58], "html5": 56, "videoel": 56, "expert": [57, 60], "extension_path": 57, "crx": 57, "typevar": [57, 58], "built": [57, 60], "_contradict": 57, "AND": 57, "notat": 57, "obj": 57, "snake_cas": 57, "hyphen": 57, "underscor": 57, "_": 57, "recurs": 57, "shallow": 57, "els": 57, "v": 57, "keyerror": 57, "lifo": 57, "lack": 57, "factori": 57, "mechan": 58, "much": 58, "someth": [58, 61], "ll": [58, 59], "yoururlher": 58, "build": 58, "combo": 58, "consum": 58, "luckili": 58, "whole": 58, "pick": 58, "help": 58, "probabl": 58, "thousand": 58, "stuff": 58, "hood": 58, "breath": 58, "oftentim": 58, "faster": 58, "template_imag": 58, "english": [58, 60], "crop": 58, "easili": 58, "craft": 58, "THE": 58, "realli": 58, "namespac": 58, "event_type_or_domain": 58, "traffic": 58, "coroutin": 58, "lamba": 58, "lambda": [58, 59, 60], "crazi": 58, "moduletyp": 58, "kw": 58, "cmd": 58, "return_enclosing_el": 58, "tremend": 58, "tag_hint": 58, "narrow": 58, "div": 58, "img": 58, "seem": 58, "deseri": 58, "obj_nam": 58, "complex": 58, "thruth": 58, "returnvalu": 58, "pageyoffset": 58, "screenx": 58, "screeni": 58, "outerwidth": 58, "1050": 58, "outerheight": 58, "832": 58, "devicepixelratio": 58, "screenleft": 58, "screentop": 58, "stylemedia": 58, "onsearch": 58, "issecurecontext": 58, "timeorigin": 58, "1707823094767": 58, "9": 58, "connectstart": 58, "navigationstart": 58, "1707823094768": 58, "source_point": 58, "dest_point": 58, "handi": 58, "w3school": 58, "cssref": 58, "css_selector": 58, "php": 58, "haven": 58, "full_pag": 58, "pagin": 58, "25": 58, "mayb": 58, "quarter": 58, "1000": 58, "10x": 58, "include_fram": 58, "cdp_obj": 58, "_is_upd": 58, "infinit": 58, "loop": [58, 59, 60], "register": 58, "1280": 58, "1024": 58, "720": 58, "min": 58, "mini": 58, "mi": 58, "ma": 58, "maxi": 58, "fu": 58, "nor": 58, "cf": 58, "ultrafunkamsterdam": [58, 59, 60], "timeouterror": 58, "asyncio": [58, 59, 60], "clientconnect": 58, "my": 58, "person": 58, "favorit": 58, "insensit": 58, "abcdefghijklmnopqrstuvwxyz": 58, "goe": 59, "pip": [59, 60], "Or": 59, "aim": [59, 60], "project": [59, 60], "somewher": [59, 60], "ago": [59, 60], "quickli": [59, 60], "editor": [59, 60], "few": [59, 60], "uc": [59, 60], "def": [59, 60], "nowsecur": [59, 60], "nl": [59, 60], "__name__": [59, 60], "__main__": [59, 60], "me": [59, 60], "run_until_complet": [59, 60], "150": [59, 60], "page2": [59, 60], "twitter": [59, 60], "page3": [59, 60], "boilerpl": 59, "iso": 59, "somewebsit": 59, "proxied_tab": 59, "mypass": 59, "myproxyhost": 59, "whatismyip": 59, "basicconfig": [59, 60], "30": [59, 60], "februari": [59, 60], "march": [59, 60], "april": [59, 60], "june": [59, 60], "juli": [59, 60], "august": [59, 60], "septemb": [59, 60], "octob": [59, 60], "novemb": [59, 60], "decemb": [59, 60], "create_account": [59, 60], "phone": [59, 60], "small": [59, 60], "use_mail_instead": [59, 60], "randstr": [59, 60], "ascii_lett": [59, 60], "dai": [59, 60], "sel_month": [59, 60], "sel_dai": [59, 60], "sel_year": [59, 60], "randint": [59, 60], "bother": [59, 60], "leap": [59, 60], "28": [59, 60], "ag": [59, 60], "1980": [59, 60], "2005": [59, 60], "nag": [59, 60], "cookie_bar_accept": [59, 60], "next_btn": [59, 60], "btn": [59, 60], "revers": [59, 60], "sign_up_btn": [59, 60], "mail": [59, 60], "quick": 60, "cf_verifi": 60, "NOT": 60, "opencv": 60, "open_external_debugg": 60, "localstorag": 60, "someev": 60, "hack": 60, "experienc": 60, "anchor_elem": 60, "someotherth": 60, "html5video_el": 60, "el": 60, "currenttim": 60, "tab_win": 60, "all_result": 60, "first_submit_button": 60, "submit": 60, "inputs_in_form": 60}, "objects": {"nodriver": [[55, 0, 1, "", "Browser"], [57, 0, 1, "", "Config"], [56, 0, 1, "", "Element"], [58, 0, 1, "", "Tab"]], "nodriver.Browser": [[55, 1, 1, "", "config"], [55, 1, 1, "", "connection"], [55, 2, 1, "", "cookies"], [55, 3, 1, "", "create"], [55, 3, 1, "", "create_context"], [55, 3, 1, "", "get"], [55, 3, 1, "", "grant_all_permissions"], [55, 2, 1, "", "main_tab"], [55, 3, 1, "", "sleep"], [55, 3, 1, "", "start"], [55, 3, 1, "", "stop"], [55, 2, 1, "", "stopped"], [55, 2, 1, "", "tabs"], [55, 1, 1, "", "targets"], [55, 3, 1, "", "tile_windows"], [55, 3, 1, "", "update_targets"], [55, 3, 1, "", "wait"], [55, 2, 1, "", "websocket_url"]], "nodriver.Config": [[57, 3, 1, "", "add_argument"], [57, 3, 1, "", "add_extension"], [57, 2, 1, "", "browser_args"], [57, 2, 1, "", "user_data_dir"], [57, 2, 1, "", "uses_custom_data_dir"]], "nodriver.Element": [[56, 3, 1, "", "apply"], [56, 2, 1, "", "assigned_slot"], [56, 2, 1, "", "attributes"], [56, 2, 1, "", "attrs"], [56, 2, 1, "", "backend_node_id"], [56, 2, 1, "", "base_url"], [56, 2, 1, "", "child_node_count"], [56, 2, 1, "", "children"], [56, 3, 1, "", "clear_input"], [56, 3, 1, "", "click"], [56, 3, 1, "", "click_mouse"], [56, 2, 1, "", "compatibility_mode"], [56, 2, 1, "", "content_document"], [56, 2, 1, "", "distributed_nodes"], [56, 2, 1, "", "document_url"], [56, 3, 1, "", "flash"], [56, 3, 1, "", "focus"], [56, 2, 1, "", "frame_id"], [56, 3, 1, "", "get_html"], [56, 3, 1, "", "get_js_attributes"], [56, 3, 1, "", "get_position"], [56, 3, 1, "", "highlight_overlay"], [56, 2, 1, "", "imported_document"], [56, 2, 1, "", "internal_subset"], [56, 3, 1, "", "is_recording"], [56, 2, 1, "", "is_svg"], [56, 2, 1, "", "local_name"], [56, 3, 1, "", "mouse_click"], [56, 3, 1, "", "mouse_drag"], [56, 3, 1, "", "mouse_move"], [56, 2, 1, "", "node"], [56, 2, 1, "", "node_id"], [56, 2, 1, "", "node_name"], [56, 2, 1, "", "node_type"], [56, 2, 1, "", "node_value"], [56, 2, 1, "", "object_id"], [56, 2, 1, "", "parent"], [56, 2, 1, "", "parent_id"], [56, 2, 1, "", "pseudo_elements"], [56, 2, 1, "", "pseudo_identifier"], [56, 2, 1, "", "pseudo_type"], [56, 2, 1, "", "public_id"], [56, 3, 1, "", "query_selector"], [56, 3, 1, "", "query_selector_all"], [56, 3, 1, "", "record_video"], [56, 2, 1, "", "remote_object"], [56, 3, 1, "", "remove_from_dom"], [56, 3, 1, "", "save_screenshot"], [56, 3, 1, "", "save_to_dom"], [56, 3, 1, "", "scroll_into_view"], [56, 3, 1, "", "select_option"], [56, 3, 1, "", "send_file"], [56, 3, 1, "", "send_keys"], [56, 3, 1, "", "set_text"], [56, 3, 1, "", "set_value"], [56, 2, 1, "", "shadow_children"], [56, 2, 1, "", "shadow_root_type"], [56, 2, 1, "", "shadow_roots"], [56, 2, 1, "", "system_id"], [56, 2, 1, "", "tab"], [56, 2, 1, "", "tag"], [56, 2, 1, "", "tag_name"], [56, 2, 1, "", "template_content"], [56, 2, 1, "", "text"], [56, 2, 1, "", "text_all"], [56, 2, 1, "", "tree"], [56, 3, 1, "", "update"], [56, 2, 1, "", "value"], [56, 2, 1, "", "xml_version"]], "nodriver.Tab": [[58, 3, 1, "", "activate"], [58, 3, 1, "", "add_handler"], [58, 1, 1, "", "attached"], [58, 3, 1, "", "back"], [58, 3, 1, "", "bring_to_front"], [58, 2, 1, "", "browser"], [58, 3, 1, "", "bypass_insecure_connection_warning"], [58, 3, 1, "", "close"], [58, 2, 1, "", "closed"], [58, 3, 1, "", "connect"], [58, 3, 1, "", "disconnect"], [58, 3, 1, "", "download_file"], [58, 3, 1, "", "evaluate"], [58, 3, 1, "", "feed_cdp"], [58, 3, 1, "", "find"], [58, 3, 1, "", "find_all"], [58, 3, 1, "", "find_element_by_text"], [58, 3, 1, "", "find_elements_by_text"], [58, 3, 1, "", "flash_point"], [58, 3, 1, "", "forward"], [58, 3, 1, "", "fullscreen"], [58, 3, 1, "", "get"], [58, 3, 1, "", "get_all_linked_sources"], [58, 3, 1, "", "get_all_urls"], [58, 3, 1, "", "get_content"], [58, 3, 1, "", "get_frame_resource_tree"], [58, 3, 1, "", "get_frame_resource_urls"], [58, 3, 1, "", "get_frame_tree"], [58, 3, 1, "", "get_local_storage"], [58, 3, 1, "", "get_window"], [58, 3, 1, "", "inspector_open"], [58, 2, 1, "", "inspector_url"], [58, 3, 1, "", "js_dumps"], [58, 3, 1, "", "maximize"], [58, 3, 1, "", "medimize"], [58, 3, 1, "", "minimize"], [58, 3, 1, "", "mouse_click"], [58, 3, 1, "", "mouse_drag"], [58, 3, 1, "", "mouse_move"], [58, 3, 1, "", "open_external_inspector"], [58, 3, 1, "", "query_selector"], [58, 3, 1, "", "query_selector_all"], [58, 3, 1, "", "reload"], [58, 3, 1, "", "remove_handler"], [58, 3, 1, "", "save_screenshot"], [58, 3, 1, "", "scroll_bottom_reached"], [58, 3, 1, "", "scroll_down"], [58, 3, 1, "", "scroll_up"], [58, 3, 1, "", "search_frame_resources"], [58, 3, 1, "", "select"], [58, 3, 1, "", "select_all"], [58, 3, 1, "", "send"], [58, 3, 1, "", "set_download_path"], [58, 3, 1, "", "set_local_storage"], [58, 3, 1, "", "set_window_size"], [58, 3, 1, "", "set_window_state"], [58, 3, 1, "", "sleep"], [58, 2, 1, "", "target"], [58, 3, 1, "", "template_location"], [58, 3, 1, "", "verify_cf"], [58, 3, 1, "", "wait"], [58, 3, 1, "", "wait_for"], [58, 2, 1, "", "websocket"], [58, 3, 1, "", "xpath"]], "nodriver.cdp": [[2, 4, 0, "-", "accessibility"], [3, 4, 0, "-", "animation"], [4, 4, 0, "-", "audits"], [5, 4, 0, "-", "autofill"], [6, 4, 0, "-", "background_service"], [7, 4, 0, "-", "bluetooth_emulation"], [8, 4, 0, "-", "browser"], [9, 4, 0, "-", "cache_storage"], [10, 4, 0, "-", "cast"], [11, 4, 0, "-", "console"], [12, 4, 0, "-", "css"], [13, 4, 0, "-", "debugger"], [14, 4, 0, "-", "device_access"], [15, 4, 0, "-", "device_orientation"], [16, 4, 0, "-", "dom"], [17, 4, 0, "-", "dom_debugger"], [18, 4, 0, "-", "dom_snapshot"], [19, 4, 0, "-", "dom_storage"], [20, 4, 0, "-", "emulation"], [21, 4, 0, "-", "event_breakpoints"], [22, 4, 0, "-", "extensions"], [23, 4, 0, "-", "fed_cm"], [24, 4, 0, "-", "fetch"], [25, 4, 0, "-", "file_system"], [26, 4, 0, "-", "headless_experimental"], [27, 4, 0, "-", "heap_profiler"], [28, 4, 0, "-", "indexed_db"], [29, 4, 0, "-", "input_"], [30, 4, 0, "-", "inspector"], [31, 4, 0, "-", "io"], [32, 4, 0, "-", "layer_tree"], [33, 4, 0, "-", "log"], [34, 4, 0, "-", "media"], [35, 4, 0, "-", "memory"], [36, 4, 0, "-", "network"], [37, 4, 0, "-", "overlay"], [38, 4, 0, "-", "page"], [39, 4, 0, "-", "performance"], [40, 4, 0, "-", "performance_timeline"], [41, 4, 0, "-", "preload"], [42, 4, 0, "-", "profiler"], [43, 4, 0, "-", "pwa"], [44, 4, 0, "-", "runtime"], [45, 4, 0, "-", "schema"], [46, 4, 0, "-", "security"], [47, 4, 0, "-", "service_worker"], [48, 4, 0, "-", "storage"], [49, 4, 0, "-", "system_info"], [50, 4, 0, "-", "target"], [51, 4, 0, "-", "tethering"], [52, 4, 0, "-", "tracing"], [53, 4, 0, "-", "web_audio"], [54, 4, 0, "-", "web_authn"]], "nodriver.cdp.accessibility": [[2, 0, 1, "", "AXNode"], [2, 0, 1, "", "AXNodeId"], [2, 0, 1, "", "AXProperty"], [2, 0, 1, "", "AXPropertyName"], [2, 0, 1, "", "AXRelatedNode"], [2, 0, 1, "", "AXValue"], [2, 0, 1, "", "AXValueNativeSourceType"], [2, 0, 1, "", "AXValueSource"], [2, 0, 1, "", "AXValueSourceType"], [2, 0, 1, "", "AXValueType"], [2, 0, 1, "", "LoadComplete"], [2, 0, 1, "", "NodesUpdated"], [2, 5, 1, "", "disable"], [2, 5, 1, "", "enable"], [2, 5, 1, "", "get_ax_node_and_ancestors"], [2, 5, 1, "", "get_child_ax_nodes"], [2, 5, 1, "", "get_full_ax_tree"], [2, 5, 1, "", "get_partial_ax_tree"], [2, 5, 1, "", "get_root_ax_node"], [2, 5, 1, "", "query_ax_tree"]], "nodriver.cdp.accessibility.AXNode": [[2, 1, 1, "", "backend_dom_node_id"], [2, 1, 1, "", "child_ids"], [2, 1, 1, "", "chrome_role"], [2, 1, 1, "", "description"], [2, 1, 1, "", "frame_id"], [2, 1, 1, "", "ignored"], [2, 1, 1, "", "ignored_reasons"], [2, 1, 1, "", "name"], [2, 1, 1, "", "node_id"], [2, 1, 1, "", "parent_id"], [2, 1, 1, "", "properties"], [2, 1, 1, "", "role"], [2, 1, 1, "", "value"]], "nodriver.cdp.accessibility.AXProperty": [[2, 1, 1, "", "name"], [2, 1, 1, "", "value"]], "nodriver.cdp.accessibility.AXPropertyName": [[2, 1, 1, "", "ACTIONS"], [2, 1, 1, "", "ACTIVEDESCENDANT"], [2, 1, 1, "", "ATOMIC"], [2, 1, 1, "", "AUTOCOMPLETE"], [2, 1, 1, "", "BUSY"], [2, 1, 1, "", "CHECKED"], [2, 1, 1, "", "CONTROLS"], [2, 1, 1, "", "DESCRIBEDBY"], [2, 1, 1, "", "DETAILS"], [2, 1, 1, "", "DISABLED"], [2, 1, 1, "", "EDITABLE"], [2, 1, 1, "", "ERRORMESSAGE"], [2, 1, 1, "", "EXPANDED"], [2, 1, 1, "", "FLOWTO"], [2, 1, 1, "", "FOCUSABLE"], [2, 1, 1, "", "FOCUSED"], [2, 1, 1, "", "HAS_POPUP"], [2, 1, 1, "", "HIDDEN"], [2, 1, 1, "", "HIDDEN_ROOT"], [2, 1, 1, "", "INVALID"], [2, 1, 1, "", "KEYSHORTCUTS"], [2, 1, 1, "", "LABELLEDBY"], [2, 1, 1, "", "LEVEL"], [2, 1, 1, "", "LIVE"], [2, 1, 1, "", "MODAL"], [2, 1, 1, "", "MULTILINE"], [2, 1, 1, "", "MULTISELECTABLE"], [2, 1, 1, "", "ORIENTATION"], [2, 1, 1, "", "OWNS"], [2, 1, 1, "", "PRESSED"], [2, 1, 1, "", "READONLY"], [2, 1, 1, "", "RELEVANT"], [2, 1, 1, "", "REQUIRED"], [2, 1, 1, "", "ROLEDESCRIPTION"], [2, 1, 1, "", "ROOT"], [2, 1, 1, "", "SELECTED"], [2, 1, 1, "", "SETTABLE"], [2, 1, 1, "", "URL"], [2, 1, 1, "", "VALUEMAX"], [2, 1, 1, "", "VALUEMIN"], [2, 1, 1, "", "VALUETEXT"]], "nodriver.cdp.accessibility.AXRelatedNode": [[2, 1, 1, "", "backend_dom_node_id"], [2, 1, 1, "", "idref"], [2, 1, 1, "", "text"]], "nodriver.cdp.accessibility.AXValue": [[2, 1, 1, "", "related_nodes"], [2, 1, 1, "", "sources"], [2, 1, 1, "", "type_"], [2, 1, 1, "", "value"]], "nodriver.cdp.accessibility.AXValueNativeSourceType": [[2, 1, 1, "", "DESCRIPTION"], [2, 1, 1, "", "FIGCAPTION"], [2, 1, 1, "", "LABEL"], [2, 1, 1, "", "LABELFOR"], [2, 1, 1, "", "LABELWRAPPED"], [2, 1, 1, "", "LEGEND"], [2, 1, 1, "", "OTHER"], [2, 1, 1, "", "RUBYANNOTATION"], [2, 1, 1, "", "TABLECAPTION"], [2, 1, 1, "", "TITLE"]], "nodriver.cdp.accessibility.AXValueSource": [[2, 1, 1, "", "attribute"], [2, 1, 1, "", "attribute_value"], [2, 1, 1, "", "invalid"], [2, 1, 1, "", "invalid_reason"], [2, 1, 1, "", "native_source"], [2, 1, 1, "", "native_source_value"], [2, 1, 1, "", "superseded"], [2, 1, 1, "", "type_"], [2, 1, 1, "", "value"]], "nodriver.cdp.accessibility.AXValueSourceType": [[2, 1, 1, "", "ATTRIBUTE"], [2, 1, 1, "", "CONTENTS"], [2, 1, 1, "", "IMPLICIT"], [2, 1, 1, "", "PLACEHOLDER"], [2, 1, 1, "", "RELATED_ELEMENT"], [2, 1, 1, "", "STYLE"]], "nodriver.cdp.accessibility.AXValueType": [[2, 1, 1, "", "BOOLEAN"], [2, 1, 1, "", "BOOLEAN_OR_UNDEFINED"], [2, 1, 1, "", "COMPUTED_STRING"], [2, 1, 1, "", "DOM_RELATION"], [2, 1, 1, "", "IDREF"], [2, 1, 1, "", "IDREF_LIST"], [2, 1, 1, "", "INTEGER"], [2, 1, 1, "", "INTERNAL_ROLE"], [2, 1, 1, "", "NODE"], [2, 1, 1, "", "NODE_LIST"], [2, 1, 1, "", "NUMBER"], [2, 1, 1, "", "ROLE"], [2, 1, 1, "", "STRING"], [2, 1, 1, "", "TOKEN"], [2, 1, 1, "", "TOKEN_LIST"], [2, 1, 1, "", "TRISTATE"], [2, 1, 1, "", "VALUE_UNDEFINED"]], "nodriver.cdp.accessibility.LoadComplete": [[2, 1, 1, "", "root"]], "nodriver.cdp.accessibility.NodesUpdated": [[2, 1, 1, "", "nodes"]], "nodriver.cdp.animation": [[3, 0, 1, "", "Animation"], [3, 0, 1, "", "AnimationCanceled"], [3, 0, 1, "", "AnimationCreated"], [3, 0, 1, "", "AnimationEffect"], [3, 0, 1, "", "AnimationStarted"], [3, 0, 1, "", "AnimationUpdated"], [3, 0, 1, "", "KeyframeStyle"], [3, 0, 1, "", "KeyframesRule"], [3, 0, 1, "", "ViewOrScrollTimeline"], [3, 5, 1, "", "disable"], [3, 5, 1, "", "enable"], [3, 5, 1, "", "get_current_time"], [3, 5, 1, "", "get_playback_rate"], [3, 5, 1, "", "release_animations"], [3, 5, 1, "", "resolve_animation"], [3, 5, 1, "", "seek_animations"], [3, 5, 1, "", "set_paused"], [3, 5, 1, "", "set_playback_rate"], [3, 5, 1, "", "set_timing"]], "nodriver.cdp.animation.Animation": [[3, 1, 1, "", "css_id"], [3, 1, 1, "", "current_time"], [3, 1, 1, "", "id_"], [3, 1, 1, "", "name"], [3, 1, 1, "", "paused_state"], [3, 1, 1, "", "play_state"], [3, 1, 1, "", "playback_rate"], [3, 1, 1, "", "source"], [3, 1, 1, "", "start_time"], [3, 1, 1, "", "type_"], [3, 1, 1, "", "view_or_scroll_timeline"]], "nodriver.cdp.animation.AnimationCanceled": [[3, 1, 1, "", "id_"]], "nodriver.cdp.animation.AnimationCreated": [[3, 1, 1, "", "id_"]], "nodriver.cdp.animation.AnimationEffect": [[3, 1, 1, "", "backend_node_id"], [3, 1, 1, "", "delay"], [3, 1, 1, "", "direction"], [3, 1, 1, "", "duration"], [3, 1, 1, "", "easing"], [3, 1, 1, "", "end_delay"], [3, 1, 1, "", "fill"], [3, 1, 1, "", "iteration_start"], [3, 1, 1, "", "iterations"], [3, 1, 1, "", "keyframes_rule"]], "nodriver.cdp.animation.AnimationStarted": [[3, 1, 1, "", "animation"]], "nodriver.cdp.animation.AnimationUpdated": [[3, 1, 1, "", "animation"]], "nodriver.cdp.animation.KeyframeStyle": [[3, 1, 1, "", "easing"], [3, 1, 1, "", "offset"]], "nodriver.cdp.animation.KeyframesRule": [[3, 1, 1, "", "keyframes"], [3, 1, 1, "", "name"]], "nodriver.cdp.animation.ViewOrScrollTimeline": [[3, 1, 1, "", "axis"], [3, 1, 1, "", "end_offset"], [3, 1, 1, "", "source_node_id"], [3, 1, 1, "", "start_offset"], [3, 1, 1, "", "subject_node_id"]], "nodriver.cdp.audits": [[4, 0, 1, "", "AffectedCookie"], [4, 0, 1, "", "AffectedFrame"], [4, 0, 1, "", "AffectedRequest"], [4, 0, 1, "", "AttributionReportingIssueDetails"], [4, 0, 1, "", "AttributionReportingIssueType"], [4, 0, 1, "", "BlockedByResponseIssueDetails"], [4, 0, 1, "", "BlockedByResponseReason"], [4, 0, 1, "", "BounceTrackingIssueDetails"], [4, 0, 1, "", "ClientHintIssueDetails"], [4, 0, 1, "", "ClientHintIssueReason"], [4, 0, 1, "", "ContentSecurityPolicyIssueDetails"], [4, 0, 1, "", "ContentSecurityPolicyViolationType"], [4, 0, 1, "", "CookieDeprecationMetadataIssueDetails"], [4, 0, 1, "", "CookieExclusionReason"], [4, 0, 1, "", "CookieIssueDetails"], [4, 0, 1, "", "CookieIssueInsight"], [4, 0, 1, "", "CookieOperation"], [4, 0, 1, "", "CookieWarningReason"], [4, 0, 1, "", "CorsIssueDetails"], [4, 0, 1, "", "DeprecationIssueDetails"], [4, 0, 1, "", "FailedRequestInfo"], [4, 0, 1, "", "FederatedAuthRequestIssueDetails"], [4, 0, 1, "", "FederatedAuthRequestIssueReason"], [4, 0, 1, "", "FederatedAuthUserInfoRequestIssueDetails"], [4, 0, 1, "", "FederatedAuthUserInfoRequestIssueReason"], [4, 0, 1, "", "GenericIssueDetails"], [4, 0, 1, "", "GenericIssueErrorType"], [4, 0, 1, "", "HeavyAdIssueDetails"], [4, 0, 1, "", "HeavyAdReason"], [4, 0, 1, "", "HeavyAdResolutionStatus"], [4, 0, 1, "", "InsightType"], [4, 0, 1, "", "InspectorIssue"], [4, 0, 1, "", "InspectorIssueCode"], [4, 0, 1, "", "InspectorIssueDetails"], [4, 0, 1, "", "IssueAdded"], [4, 0, 1, "", "IssueId"], [4, 0, 1, "", "LowTextContrastIssueDetails"], [4, 0, 1, "", "MixedContentIssueDetails"], [4, 0, 1, "", "MixedContentResolutionStatus"], [4, 0, 1, "", "MixedContentResourceType"], [4, 0, 1, "", "NavigatorUserAgentIssueDetails"], [4, 0, 1, "", "PartitioningBlobURLInfo"], [4, 0, 1, "", "PartitioningBlobURLIssueDetails"], [4, 0, 1, "", "PropertyRuleIssueDetails"], [4, 0, 1, "", "PropertyRuleIssueReason"], [4, 0, 1, "", "QuirksModeIssueDetails"], [4, 0, 1, "", "SRIMessageSignatureError"], [4, 0, 1, "", "SRIMessageSignatureIssueDetails"], [4, 0, 1, "", "SelectElementAccessibilityIssueDetails"], [4, 0, 1, "", "SelectElementAccessibilityIssueReason"], [4, 0, 1, "", "SharedArrayBufferIssueDetails"], [4, 0, 1, "", "SharedArrayBufferIssueType"], [4, 0, 1, "", "SharedDictionaryError"], [4, 0, 1, "", "SharedDictionaryIssueDetails"], [4, 0, 1, "", "SourceCodeLocation"], [4, 0, 1, "", "StyleSheetLoadingIssueReason"], [4, 0, 1, "", "StylesheetLoadingIssueDetails"], [4, 5, 1, "", "check_contrast"], [4, 5, 1, "", "check_forms_issues"], [4, 5, 1, "", "disable"], [4, 5, 1, "", "enable"], [4, 5, 1, "", "get_encoded_response"]], "nodriver.cdp.audits.AffectedCookie": [[4, 1, 1, "", "domain"], [4, 1, 1, "", "name"], [4, 1, 1, "", "path"]], "nodriver.cdp.audits.AffectedFrame": [[4, 1, 1, "", "frame_id"]], "nodriver.cdp.audits.AffectedRequest": [[4, 1, 1, "", "request_id"], [4, 1, 1, "", "url"]], "nodriver.cdp.audits.AttributionReportingIssueDetails": [[4, 1, 1, "", "invalid_parameter"], [4, 1, 1, "", "request"], [4, 1, 1, "", "violating_node_id"], [4, 1, 1, "", "violation_type"]], "nodriver.cdp.audits.AttributionReportingIssueType": [[4, 1, 1, "", "INSECURE_CONTEXT"], [4, 1, 1, "", "INVALID_HEADER"], [4, 1, 1, "", "INVALID_INFO_HEADER"], [4, 1, 1, "", "INVALID_REGISTER_OS_SOURCE_HEADER"], [4, 1, 1, "", "INVALID_REGISTER_OS_TRIGGER_HEADER"], [4, 1, 1, "", "INVALID_REGISTER_TRIGGER_HEADER"], [4, 1, 1, "", "NAVIGATION_REGISTRATION_UNIQUE_SCOPE_ALREADY_SET"], [4, 1, 1, "", "NAVIGATION_REGISTRATION_WITHOUT_TRANSIENT_USER_ACTIVATION"], [4, 1, 1, "", "NO_REGISTER_OS_SOURCE_HEADER"], [4, 1, 1, "", "NO_REGISTER_OS_TRIGGER_HEADER"], [4, 1, 1, "", "NO_REGISTER_SOURCE_HEADER"], [4, 1, 1, "", "NO_REGISTER_TRIGGER_HEADER"], [4, 1, 1, "", "NO_WEB_OR_OS_SUPPORT"], [4, 1, 1, "", "OS_SOURCE_IGNORED"], [4, 1, 1, "", "OS_TRIGGER_IGNORED"], [4, 1, 1, "", "PERMISSION_POLICY_DISABLED"], [4, 1, 1, "", "SOURCE_AND_TRIGGER_HEADERS"], [4, 1, 1, "", "SOURCE_IGNORED"], [4, 1, 1, "", "TRIGGER_IGNORED"], [4, 1, 1, "", "UNTRUSTWORTHY_REPORTING_ORIGIN"], [4, 1, 1, "", "WEB_AND_OS_HEADERS"]], "nodriver.cdp.audits.BlockedByResponseIssueDetails": [[4, 1, 1, "", "blocked_frame"], [4, 1, 1, "", "parent_frame"], [4, 1, 1, "", "reason"], [4, 1, 1, "", "request"]], "nodriver.cdp.audits.BlockedByResponseReason": [[4, 1, 1, "", "COEP_FRAME_RESOURCE_NEEDS_COEP_HEADER"], [4, 1, 1, "", "COOP_SANDBOXED_I_FRAME_CANNOT_NAVIGATE_TO_COOP_PAGE"], [4, 1, 1, "", "CORP_NOT_SAME_ORIGIN"], [4, 1, 1, "", "CORP_NOT_SAME_ORIGIN_AFTER_DEFAULTED_TO_SAME_ORIGIN_BY_COEP"], [4, 1, 1, "", "CORP_NOT_SAME_ORIGIN_AFTER_DEFAULTED_TO_SAME_ORIGIN_BY_COEP_AND_DIP"], [4, 1, 1, "", "CORP_NOT_SAME_ORIGIN_AFTER_DEFAULTED_TO_SAME_ORIGIN_BY_DIP"], [4, 1, 1, "", "CORP_NOT_SAME_SITE"], [4, 1, 1, "", "SRI_MESSAGE_SIGNATURE_MISMATCH"]], "nodriver.cdp.audits.BounceTrackingIssueDetails": [[4, 1, 1, "", "tracking_sites"]], "nodriver.cdp.audits.ClientHintIssueDetails": [[4, 1, 1, "", "client_hint_issue_reason"], [4, 1, 1, "", "source_code_location"]], "nodriver.cdp.audits.ClientHintIssueReason": [[4, 1, 1, "", "META_TAG_ALLOW_LIST_INVALID_ORIGIN"], [4, 1, 1, "", "META_TAG_MODIFIED_HTML"]], "nodriver.cdp.audits.ContentSecurityPolicyIssueDetails": [[4, 1, 1, "", "blocked_url"], [4, 1, 1, "", "content_security_policy_violation_type"], [4, 1, 1, "", "frame_ancestor"], [4, 1, 1, "", "is_report_only"], [4, 1, 1, "", "source_code_location"], [4, 1, 1, "", "violated_directive"], [4, 1, 1, "", "violating_node_id"]], "nodriver.cdp.audits.ContentSecurityPolicyViolationType": [[4, 1, 1, "", "K_EVAL_VIOLATION"], [4, 1, 1, "", "K_INLINE_VIOLATION"], [4, 1, 1, "", "K_SRI_VIOLATION"], [4, 1, 1, "", "K_TRUSTED_TYPES_POLICY_VIOLATION"], [4, 1, 1, "", "K_TRUSTED_TYPES_SINK_VIOLATION"], [4, 1, 1, "", "K_URL_VIOLATION"], [4, 1, 1, "", "K_WASM_EVAL_VIOLATION"]], "nodriver.cdp.audits.CookieDeprecationMetadataIssueDetails": [[4, 1, 1, "", "allowed_sites"], [4, 1, 1, "", "is_opt_out_top_level"], [4, 1, 1, "", "operation"], [4, 1, 1, "", "opt_out_percentage"]], "nodriver.cdp.audits.CookieExclusionReason": [[4, 1, 1, "", "EXCLUDE_DOMAIN_NON_ASCII"], [4, 1, 1, "", "EXCLUDE_INVALID_SAME_PARTY"], [4, 1, 1, "", "EXCLUDE_PORT_MISMATCH"], [4, 1, 1, "", "EXCLUDE_SAME_PARTY_CROSS_PARTY_CONTEXT"], [4, 1, 1, "", "EXCLUDE_SAME_SITE_LAX"], [4, 1, 1, "", "EXCLUDE_SAME_SITE_NONE_INSECURE"], [4, 1, 1, "", "EXCLUDE_SAME_SITE_STRICT"], [4, 1, 1, "", "EXCLUDE_SAME_SITE_UNSPECIFIED_TREATED_AS_LAX"], [4, 1, 1, "", "EXCLUDE_SCHEME_MISMATCH"], [4, 1, 1, "", "EXCLUDE_THIRD_PARTY_COOKIE_BLOCKED_IN_FIRST_PARTY_SET"], [4, 1, 1, "", "EXCLUDE_THIRD_PARTY_PHASEOUT"]], "nodriver.cdp.audits.CookieIssueDetails": [[4, 1, 1, "", "cookie"], [4, 1, 1, "", "cookie_exclusion_reasons"], [4, 1, 1, "", "cookie_url"], [4, 1, 1, "", "cookie_warning_reasons"], [4, 1, 1, "", "insight"], [4, 1, 1, "", "operation"], [4, 1, 1, "", "raw_cookie_line"], [4, 1, 1, "", "request"], [4, 1, 1, "", "site_for_cookies"]], "nodriver.cdp.audits.CookieIssueInsight": [[4, 1, 1, "", "table_entry_url"], [4, 1, 1, "", "type_"]], "nodriver.cdp.audits.CookieOperation": [[4, 1, 1, "", "READ_COOKIE"], [4, 1, 1, "", "SET_COOKIE"]], "nodriver.cdp.audits.CookieWarningReason": [[4, 1, 1, "", "WARN_ATTRIBUTE_VALUE_EXCEEDS_MAX_SIZE"], [4, 1, 1, "", "WARN_CROSS_SITE_REDIRECT_DOWNGRADE_CHANGES_INCLUSION"], [4, 1, 1, "", "WARN_DEPRECATION_TRIAL_METADATA"], [4, 1, 1, "", "WARN_DOMAIN_NON_ASCII"], [4, 1, 1, "", "WARN_SAME_SITE_LAX_CROSS_DOWNGRADE_LAX"], [4, 1, 1, "", "WARN_SAME_SITE_LAX_CROSS_DOWNGRADE_STRICT"], [4, 1, 1, "", "WARN_SAME_SITE_NONE_INSECURE"], [4, 1, 1, "", "WARN_SAME_SITE_STRICT_CROSS_DOWNGRADE_LAX"], [4, 1, 1, "", "WARN_SAME_SITE_STRICT_CROSS_DOWNGRADE_STRICT"], [4, 1, 1, "", "WARN_SAME_SITE_STRICT_LAX_DOWNGRADE_STRICT"], [4, 1, 1, "", "WARN_SAME_SITE_UNSPECIFIED_CROSS_SITE_CONTEXT"], [4, 1, 1, "", "WARN_SAME_SITE_UNSPECIFIED_LAX_ALLOW_UNSAFE"], [4, 1, 1, "", "WARN_THIRD_PARTY_COOKIE_HEURISTIC"], [4, 1, 1, "", "WARN_THIRD_PARTY_PHASEOUT"]], "nodriver.cdp.audits.CorsIssueDetails": [[4, 1, 1, "", "client_security_state"], [4, 1, 1, "", "cors_error_status"], [4, 1, 1, "", "initiator_origin"], [4, 1, 1, "", "is_warning"], [4, 1, 1, "", "location"], [4, 1, 1, "", "request"], [4, 1, 1, "", "resource_ip_address_space"]], "nodriver.cdp.audits.DeprecationIssueDetails": [[4, 1, 1, "", "affected_frame"], [4, 1, 1, "", "source_code_location"], [4, 1, 1, "", "type_"]], "nodriver.cdp.audits.FailedRequestInfo": [[4, 1, 1, "", "failure_message"], [4, 1, 1, "", "request_id"], [4, 1, 1, "", "url"]], "nodriver.cdp.audits.FederatedAuthRequestIssueDetails": [[4, 1, 1, "", "federated_auth_request_issue_reason"]], "nodriver.cdp.audits.FederatedAuthRequestIssueReason": [[4, 1, 1, "", "ACCOUNTS_HTTP_NOT_FOUND"], [4, 1, 1, "", "ACCOUNTS_INVALID_CONTENT_TYPE"], [4, 1, 1, "", "ACCOUNTS_INVALID_RESPONSE"], [4, 1, 1, "", "ACCOUNTS_LIST_EMPTY"], [4, 1, 1, "", "ACCOUNTS_NO_RESPONSE"], [4, 1, 1, "", "CANCELED"], [4, 1, 1, "", "CLIENT_METADATA_HTTP_NOT_FOUND"], [4, 1, 1, "", "CLIENT_METADATA_INVALID_CONTENT_TYPE"], [4, 1, 1, "", "CLIENT_METADATA_INVALID_RESPONSE"], [4, 1, 1, "", "CLIENT_METADATA_NO_RESPONSE"], [4, 1, 1, "", "CONFIG_HTTP_NOT_FOUND"], [4, 1, 1, "", "CONFIG_INVALID_CONTENT_TYPE"], [4, 1, 1, "", "CONFIG_INVALID_RESPONSE"], [4, 1, 1, "", "CONFIG_NOT_IN_WELL_KNOWN"], [4, 1, 1, "", "CONFIG_NO_RESPONSE"], [4, 1, 1, "", "CORS_ERROR"], [4, 1, 1, "", "DISABLED_IN_FLAGS"], [4, 1, 1, "", "DISABLED_IN_SETTINGS"], [4, 1, 1, "", "ERROR_FETCHING_SIGNIN"], [4, 1, 1, "", "ERROR_ID_TOKEN"], [4, 1, 1, "", "IDP_NOT_POTENTIALLY_TRUSTWORTHY"], [4, 1, 1, "", "ID_TOKEN_CROSS_SITE_IDP_ERROR_RESPONSE"], [4, 1, 1, "", "ID_TOKEN_HTTP_NOT_FOUND"], [4, 1, 1, "", "ID_TOKEN_IDP_ERROR_RESPONSE"], [4, 1, 1, "", "ID_TOKEN_INVALID_CONTENT_TYPE"], [4, 1, 1, "", "ID_TOKEN_INVALID_REQUEST"], [4, 1, 1, "", "ID_TOKEN_INVALID_RESPONSE"], [4, 1, 1, "", "ID_TOKEN_NO_RESPONSE"], [4, 1, 1, "", "INVALID_FIELDS_SPECIFIED"], [4, 1, 1, "", "INVALID_SIGNIN_RESPONSE"], [4, 1, 1, "", "MISSING_TRANSIENT_USER_ACTIVATION"], [4, 1, 1, "", "NOT_SIGNED_IN_WITH_IDP"], [4, 1, 1, "", "RELYING_PARTY_ORIGIN_IS_OPAQUE"], [4, 1, 1, "", "REPLACED_BY_ACTIVE_MODE"], [4, 1, 1, "", "RP_PAGE_NOT_VISIBLE"], [4, 1, 1, "", "SHOULD_EMBARGO"], [4, 1, 1, "", "SILENT_MEDIATION_FAILURE"], [4, 1, 1, "", "THIRD_PARTY_COOKIES_BLOCKED"], [4, 1, 1, "", "TOO_MANY_REQUESTS"], [4, 1, 1, "", "TYPE_NOT_MATCHING"], [4, 1, 1, "", "UI_DISMISSED_NO_EMBARGO"], [4, 1, 1, "", "WELL_KNOWN_HTTP_NOT_FOUND"], [4, 1, 1, "", "WELL_KNOWN_INVALID_CONTENT_TYPE"], [4, 1, 1, "", "WELL_KNOWN_INVALID_RESPONSE"], [4, 1, 1, "", "WELL_KNOWN_LIST_EMPTY"], [4, 1, 1, "", "WELL_KNOWN_NO_RESPONSE"], [4, 1, 1, "", "WELL_KNOWN_TOO_BIG"]], "nodriver.cdp.audits.FederatedAuthUserInfoRequestIssueDetails": [[4, 1, 1, "", "federated_auth_user_info_request_issue_reason"]], "nodriver.cdp.audits.FederatedAuthUserInfoRequestIssueReason": [[4, 1, 1, "", "INVALID_ACCOUNTS_RESPONSE"], [4, 1, 1, "", "INVALID_CONFIG_OR_WELL_KNOWN"], [4, 1, 1, "", "NOT_IFRAME"], [4, 1, 1, "", "NOT_POTENTIALLY_TRUSTWORTHY"], [4, 1, 1, "", "NOT_SAME_ORIGIN"], [4, 1, 1, "", "NOT_SIGNED_IN_WITH_IDP"], [4, 1, 1, "", "NO_ACCOUNT_SHARING_PERMISSION"], [4, 1, 1, "", "NO_API_PERMISSION"], [4, 1, 1, "", "NO_RETURNING_USER_FROM_FETCHED_ACCOUNTS"]], "nodriver.cdp.audits.GenericIssueDetails": [[4, 1, 1, "", "error_type"], [4, 1, 1, "", "frame_id"], [4, 1, 1, "", "request"], [4, 1, 1, "", "violating_node_attribute"], [4, 1, 1, "", "violating_node_id"]], "nodriver.cdp.audits.GenericIssueErrorType": [[4, 1, 1, "", "FORM_ARIA_LABELLED_BY_TO_NON_EXISTING_ID"], [4, 1, 1, "", "FORM_AUTOCOMPLETE_ATTRIBUTE_EMPTY_ERROR"], [4, 1, 1, "", "FORM_DUPLICATE_ID_FOR_INPUT_ERROR"], [4, 1, 1, "", "FORM_EMPTY_ID_AND_NAME_ATTRIBUTES_FOR_INPUT_ERROR"], [4, 1, 1, "", "FORM_INPUT_ASSIGNED_AUTOCOMPLETE_VALUE_TO_ID_OR_NAME_ATTRIBUTE_ERROR"], [4, 1, 1, "", "FORM_INPUT_HAS_WRONG_BUT_WELL_INTENDED_AUTOCOMPLETE_VALUE_ERROR"], [4, 1, 1, "", "FORM_INPUT_WITH_NO_LABEL_ERROR"], [4, 1, 1, "", "FORM_LABEL_FOR_MATCHES_NON_EXISTING_ID_ERROR"], [4, 1, 1, "", "FORM_LABEL_FOR_NAME_ERROR"], [4, 1, 1, "", "FORM_LABEL_HAS_NEITHER_FOR_NOR_NESTED_INPUT"], [4, 1, 1, "", "RESPONSE_WAS_BLOCKED_BY_ORB"]], "nodriver.cdp.audits.HeavyAdIssueDetails": [[4, 1, 1, "", "frame"], [4, 1, 1, "", "reason"], [4, 1, 1, "", "resolution"]], "nodriver.cdp.audits.HeavyAdReason": [[4, 1, 1, "", "CPU_PEAK_LIMIT"], [4, 1, 1, "", "CPU_TOTAL_LIMIT"], [4, 1, 1, "", "NETWORK_TOTAL_LIMIT"]], "nodriver.cdp.audits.HeavyAdResolutionStatus": [[4, 1, 1, "", "HEAVY_AD_BLOCKED"], [4, 1, 1, "", "HEAVY_AD_WARNING"]], "nodriver.cdp.audits.InsightType": [[4, 1, 1, "", "GIT_HUB_RESOURCE"], [4, 1, 1, "", "GRACE_PERIOD"], [4, 1, 1, "", "HEURISTICS"]], "nodriver.cdp.audits.InspectorIssue": [[4, 1, 1, "", "code"], [4, 1, 1, "", "details"], [4, 1, 1, "", "issue_id"]], "nodriver.cdp.audits.InspectorIssueCode": [[4, 1, 1, "", "ATTRIBUTION_REPORTING_ISSUE"], [4, 1, 1, "", "BLOCKED_BY_RESPONSE_ISSUE"], [4, 1, 1, "", "BOUNCE_TRACKING_ISSUE"], [4, 1, 1, "", "CLIENT_HINT_ISSUE"], [4, 1, 1, "", "CONTENT_SECURITY_POLICY_ISSUE"], [4, 1, 1, "", "COOKIE_DEPRECATION_METADATA_ISSUE"], [4, 1, 1, "", "COOKIE_ISSUE"], [4, 1, 1, "", "CORS_ISSUE"], [4, 1, 1, "", "DEPRECATION_ISSUE"], [4, 1, 1, "", "FEDERATED_AUTH_REQUEST_ISSUE"], [4, 1, 1, "", "FEDERATED_AUTH_USER_INFO_REQUEST_ISSUE"], [4, 1, 1, "", "GENERIC_ISSUE"], [4, 1, 1, "", "HEAVY_AD_ISSUE"], [4, 1, 1, "", "LOW_TEXT_CONTRAST_ISSUE"], [4, 1, 1, "", "MIXED_CONTENT_ISSUE"], [4, 1, 1, "", "NAVIGATOR_USER_AGENT_ISSUE"], [4, 1, 1, "", "PARTITIONING_BLOB_URL_ISSUE"], [4, 1, 1, "", "PROPERTY_RULE_ISSUE"], [4, 1, 1, "", "QUIRKS_MODE_ISSUE"], [4, 1, 1, "", "SELECT_ELEMENT_ACCESSIBILITY_ISSUE"], [4, 1, 1, "", "SHARED_ARRAY_BUFFER_ISSUE"], [4, 1, 1, "", "SHARED_DICTIONARY_ISSUE"], [4, 1, 1, "", "SRI_MESSAGE_SIGNATURE_ISSUE"], [4, 1, 1, "", "STYLESHEET_LOADING_ISSUE"]], "nodriver.cdp.audits.InspectorIssueDetails": [[4, 1, 1, "", "attribution_reporting_issue_details"], [4, 1, 1, "", "blocked_by_response_issue_details"], [4, 1, 1, "", "bounce_tracking_issue_details"], [4, 1, 1, "", "client_hint_issue_details"], [4, 1, 1, "", "content_security_policy_issue_details"], [4, 1, 1, "", "cookie_deprecation_metadata_issue_details"], [4, 1, 1, "", "cookie_issue_details"], [4, 1, 1, "", "cors_issue_details"], [4, 1, 1, "", "deprecation_issue_details"], [4, 1, 1, "", "federated_auth_request_issue_details"], [4, 1, 1, "", "federated_auth_user_info_request_issue_details"], [4, 1, 1, "", "generic_issue_details"], [4, 1, 1, "", "heavy_ad_issue_details"], [4, 1, 1, "", "low_text_contrast_issue_details"], [4, 1, 1, "", "mixed_content_issue_details"], [4, 1, 1, "", "navigator_user_agent_issue_details"], [4, 1, 1, "", "partitioning_blob_url_issue_details"], [4, 1, 1, "", "property_rule_issue_details"], [4, 1, 1, "", "quirks_mode_issue_details"], [4, 1, 1, "", "select_element_accessibility_issue_details"], [4, 1, 1, "", "shared_array_buffer_issue_details"], [4, 1, 1, "", "shared_dictionary_issue_details"], [4, 1, 1, "", "sri_message_signature_issue_details"], [4, 1, 1, "", "stylesheet_loading_issue_details"]], "nodriver.cdp.audits.IssueAdded": [[4, 1, 1, "", "issue"]], "nodriver.cdp.audits.LowTextContrastIssueDetails": [[4, 1, 1, "", "contrast_ratio"], [4, 1, 1, "", "font_size"], [4, 1, 1, "", "font_weight"], [4, 1, 1, "", "threshold_aa"], [4, 1, 1, "", "threshold_aaa"], [4, 1, 1, "", "violating_node_id"], [4, 1, 1, "", "violating_node_selector"]], "nodriver.cdp.audits.MixedContentIssueDetails": [[4, 1, 1, "", "frame"], [4, 1, 1, "", "insecure_url"], [4, 1, 1, "", "main_resource_url"], [4, 1, 1, "", "request"], [4, 1, 1, "", "resolution_status"], [4, 1, 1, "", "resource_type"]], "nodriver.cdp.audits.MixedContentResolutionStatus": [[4, 1, 1, "", "MIXED_CONTENT_AUTOMATICALLY_UPGRADED"], [4, 1, 1, "", "MIXED_CONTENT_BLOCKED"], [4, 1, 1, "", "MIXED_CONTENT_WARNING"]], "nodriver.cdp.audits.MixedContentResourceType": [[4, 1, 1, "", "ATTRIBUTION_SRC"], [4, 1, 1, "", "AUDIO"], [4, 1, 1, "", "BEACON"], [4, 1, 1, "", "CSP_REPORT"], [4, 1, 1, "", "DOWNLOAD"], [4, 1, 1, "", "EVENT_SOURCE"], [4, 1, 1, "", "FAVICON"], [4, 1, 1, "", "FONT"], [4, 1, 1, "", "FORM"], [4, 1, 1, "", "FRAME"], [4, 1, 1, "", "IMAGE"], [4, 1, 1, "", "IMPORT"], [4, 1, 1, "", "JSON"], [4, 1, 1, "", "MANIFEST"], [4, 1, 1, "", "PING"], [4, 1, 1, "", "PLUGIN_DATA"], [4, 1, 1, "", "PLUGIN_RESOURCE"], [4, 1, 1, "", "PREFETCH"], [4, 1, 1, "", "RESOURCE"], [4, 1, 1, "", "SCRIPT"], [4, 1, 1, "", "SERVICE_WORKER"], [4, 1, 1, "", "SHARED_WORKER"], [4, 1, 1, "", "SPECULATION_RULES"], [4, 1, 1, "", "STYLESHEET"], [4, 1, 1, "", "TRACK"], [4, 1, 1, "", "VIDEO"], [4, 1, 1, "", "WORKER"], [4, 1, 1, "", "XML_HTTP_REQUEST"], [4, 1, 1, "", "XSLT"]], "nodriver.cdp.audits.NavigatorUserAgentIssueDetails": [[4, 1, 1, "", "location"], [4, 1, 1, "", "url"]], "nodriver.cdp.audits.PartitioningBlobURLInfo": [[4, 1, 1, "", "BLOCKED_CROSS_PARTITION_FETCHING"], [4, 1, 1, "", "ENFORCE_NOOPENER_FOR_NAVIGATION"]], "nodriver.cdp.audits.PartitioningBlobURLIssueDetails": [[4, 1, 1, "", "partitioning_blob_url_info"], [4, 1, 1, "", "url"]], "nodriver.cdp.audits.PropertyRuleIssueDetails": [[4, 1, 1, "", "property_rule_issue_reason"], [4, 1, 1, "", "property_value"], [4, 1, 1, "", "source_code_location"]], "nodriver.cdp.audits.PropertyRuleIssueReason": [[4, 1, 1, "", "INVALID_INHERITS"], [4, 1, 1, "", "INVALID_INITIAL_VALUE"], [4, 1, 1, "", "INVALID_NAME"], [4, 1, 1, "", "INVALID_SYNTAX"]], "nodriver.cdp.audits.QuirksModeIssueDetails": [[4, 1, 1, "", "document_node_id"], [4, 1, 1, "", "frame_id"], [4, 1, 1, "", "is_limited_quirks_mode"], [4, 1, 1, "", "loader_id"], [4, 1, 1, "", "url"]], "nodriver.cdp.audits.SRIMessageSignatureError": [[4, 1, 1, "", "INVALID_SIGNATURE_HEADER"], [4, 1, 1, "", "INVALID_SIGNATURE_INPUT_HEADER"], [4, 1, 1, "", "MISSING_SIGNATURE_HEADER"], [4, 1, 1, "", "MISSING_SIGNATURE_INPUT_HEADER"], [4, 1, 1, "", "SIGNATURE_HEADER_VALUE_IS_INCORRECT_LENGTH"], [4, 1, 1, "", "SIGNATURE_HEADER_VALUE_IS_NOT_BYTE_SEQUENCE"], [4, 1, 1, "", "SIGNATURE_HEADER_VALUE_IS_PARAMETERIZED"], [4, 1, 1, "", "SIGNATURE_INPUT_HEADER_INVALID_COMPONENT_NAME"], [4, 1, 1, "", "SIGNATURE_INPUT_HEADER_INVALID_COMPONENT_TYPE"], [4, 1, 1, "", "SIGNATURE_INPUT_HEADER_INVALID_DERIVED_COMPONENT_PARAMETER"], [4, 1, 1, "", "SIGNATURE_INPUT_HEADER_INVALID_HEADER_COMPONENT_PARAMETER"], [4, 1, 1, "", "SIGNATURE_INPUT_HEADER_INVALID_PARAMETER"], [4, 1, 1, "", "SIGNATURE_INPUT_HEADER_KEY_ID_LENGTH"], [4, 1, 1, "", "SIGNATURE_INPUT_HEADER_MISSING_LABEL"], [4, 1, 1, "", "SIGNATURE_INPUT_HEADER_MISSING_REQUIRED_PARAMETERS"], [4, 1, 1, "", "SIGNATURE_INPUT_HEADER_VALUE_MISSING_COMPONENTS"], [4, 1, 1, "", "SIGNATURE_INPUT_HEADER_VALUE_NOT_INNER_LIST"], [4, 1, 1, "", "VALIDATION_FAILED_INVALID_LENGTH"], [4, 1, 1, "", "VALIDATION_FAILED_SIGNATURE_EXPIRED"], [4, 1, 1, "", "VALIDATION_FAILED_SIGNATURE_MISMATCH"]], "nodriver.cdp.audits.SRIMessageSignatureIssueDetails": [[4, 1, 1, "", "error"], [4, 1, 1, "", "request"], [4, 1, 1, "", "signature_base"]], "nodriver.cdp.audits.SelectElementAccessibilityIssueDetails": [[4, 1, 1, "", "has_disallowed_attributes"], [4, 1, 1, "", "node_id"], [4, 1, 1, "", "select_element_accessibility_issue_reason"]], "nodriver.cdp.audits.SelectElementAccessibilityIssueReason": [[4, 1, 1, "", "DISALLOWED_OPT_GROUP_CHILD"], [4, 1, 1, "", "DISALLOWED_SELECT_CHILD"], [4, 1, 1, "", "INTERACTIVE_CONTENT_LEGEND_CHILD"], [4, 1, 1, "", "INTERACTIVE_CONTENT_OPTION_CHILD"], [4, 1, 1, "", "NON_PHRASING_CONTENT_OPTION_CHILD"]], "nodriver.cdp.audits.SharedArrayBufferIssueDetails": [[4, 1, 1, "", "is_warning"], [4, 1, 1, "", "source_code_location"], [4, 1, 1, "", "type_"]], "nodriver.cdp.audits.SharedArrayBufferIssueType": [[4, 1, 1, "", "CREATION_ISSUE"], [4, 1, 1, "", "TRANSFER_ISSUE"]], "nodriver.cdp.audits.SharedDictionaryError": [[4, 1, 1, "", "USE_ERROR_CROSS_ORIGIN_NO_CORS_REQUEST"], [4, 1, 1, "", "USE_ERROR_DICTIONARY_LOAD_FAILURE"], [4, 1, 1, "", "USE_ERROR_MATCHING_DICTIONARY_NOT_USED"], [4, 1, 1, "", "USE_ERROR_UNEXPECTED_CONTENT_DICTIONARY_HEADER"], [4, 1, 1, "", "WRITE_ERROR_COSS_ORIGIN_NO_CORS_REQUEST"], [4, 1, 1, "", "WRITE_ERROR_DISALLOWED_BY_SETTINGS"], [4, 1, 1, "", "WRITE_ERROR_EXPIRED_RESPONSE"], [4, 1, 1, "", "WRITE_ERROR_FEATURE_DISABLED"], [4, 1, 1, "", "WRITE_ERROR_INSUFFICIENT_RESOURCES"], [4, 1, 1, "", "WRITE_ERROR_INVALID_MATCH_FIELD"], [4, 1, 1, "", "WRITE_ERROR_INVALID_STRUCTURED_HEADER"], [4, 1, 1, "", "WRITE_ERROR_NAVIGATION_REQUEST"], [4, 1, 1, "", "WRITE_ERROR_NON_LIST_MATCH_DEST_FIELD"], [4, 1, 1, "", "WRITE_ERROR_NON_SECURE_CONTEXT"], [4, 1, 1, "", "WRITE_ERROR_NON_STRING_ID_FIELD"], [4, 1, 1, "", "WRITE_ERROR_NON_STRING_IN_MATCH_DEST_LIST"], [4, 1, 1, "", "WRITE_ERROR_NON_STRING_MATCH_FIELD"], [4, 1, 1, "", "WRITE_ERROR_NON_TOKEN_TYPE_FIELD"], [4, 1, 1, "", "WRITE_ERROR_NO_MATCH_FIELD"], [4, 1, 1, "", "WRITE_ERROR_REQUEST_ABORTED"], [4, 1, 1, "", "WRITE_ERROR_SHUTTING_DOWN"], [4, 1, 1, "", "WRITE_ERROR_TOO_LONG_ID_FIELD"], [4, 1, 1, "", "WRITE_ERROR_UNSUPPORTED_TYPE"]], "nodriver.cdp.audits.SharedDictionaryIssueDetails": [[4, 1, 1, "", "request"], [4, 1, 1, "", "shared_dictionary_error"]], "nodriver.cdp.audits.SourceCodeLocation": [[4, 1, 1, "", "column_number"], [4, 1, 1, "", "line_number"], [4, 1, 1, "", "script_id"], [4, 1, 1, "", "url"]], "nodriver.cdp.audits.StyleSheetLoadingIssueReason": [[4, 1, 1, "", "LATE_IMPORT_RULE"], [4, 1, 1, "", "REQUEST_FAILED"]], "nodriver.cdp.audits.StylesheetLoadingIssueDetails": [[4, 1, 1, "", "failed_request_info"], [4, 1, 1, "", "source_code_location"], [4, 1, 1, "", "style_sheet_loading_issue_reason"]], "nodriver.cdp.autofill": [[5, 0, 1, "", "Address"], [5, 0, 1, "", "AddressField"], [5, 0, 1, "", "AddressFields"], [5, 0, 1, "", "AddressFormFilled"], [5, 0, 1, "", "AddressUI"], [5, 0, 1, "", "CreditCard"], [5, 0, 1, "", "FilledField"], [5, 0, 1, "", "FillingStrategy"], [5, 5, 1, "", "disable"], [5, 5, 1, "", "enable"], [5, 5, 1, "", "set_addresses"], [5, 5, 1, "", "trigger"]], "nodriver.cdp.autofill.Address": [[5, 1, 1, "", "fields"]], "nodriver.cdp.autofill.AddressField": [[5, 1, 1, "", "name"], [5, 1, 1, "", "value"]], "nodriver.cdp.autofill.AddressFields": [[5, 1, 1, "", "fields"]], "nodriver.cdp.autofill.AddressFormFilled": [[5, 1, 1, "", "address_ui"], [5, 1, 1, "", "filled_fields"]], "nodriver.cdp.autofill.AddressUI": [[5, 1, 1, "", "address_fields"]], "nodriver.cdp.autofill.CreditCard": [[5, 1, 1, "", "cvc"], [5, 1, 1, "", "expiry_month"], [5, 1, 1, "", "expiry_year"], [5, 1, 1, "", "name"], [5, 1, 1, "", "number"]], "nodriver.cdp.autofill.FilledField": [[5, 1, 1, "", "autofill_type"], [5, 1, 1, "", "field_id"], [5, 1, 1, "", "filling_strategy"], [5, 1, 1, "", "frame_id"], [5, 1, 1, "", "html_type"], [5, 1, 1, "", "id_"], [5, 1, 1, "", "name"], [5, 1, 1, "", "value"]], "nodriver.cdp.autofill.FillingStrategy": [[5, 1, 1, "", "AUTOCOMPLETE_ATTRIBUTE"], [5, 1, 1, "", "AUTOFILL_INFERRED"]], "nodriver.cdp.background_service": [[6, 0, 1, "", "BackgroundServiceEvent"], [6, 0, 1, "", "BackgroundServiceEventReceived"], [6, 0, 1, "", "EventMetadata"], [6, 0, 1, "", "RecordingStateChanged"], [6, 0, 1, "", "ServiceName"], [6, 5, 1, "", "clear_events"], [6, 5, 1, "", "set_recording"], [6, 5, 1, "", "start_observing"], [6, 5, 1, "", "stop_observing"]], "nodriver.cdp.background_service.BackgroundServiceEvent": [[6, 1, 1, "", "event_metadata"], [6, 1, 1, "", "event_name"], [6, 1, 1, "", "instance_id"], [6, 1, 1, "", "origin"], [6, 1, 1, "", "service"], [6, 1, 1, "", "service_worker_registration_id"], [6, 1, 1, "", "storage_key"], [6, 1, 1, "", "timestamp"]], "nodriver.cdp.background_service.BackgroundServiceEventReceived": [[6, 1, 1, "", "background_service_event"]], "nodriver.cdp.background_service.EventMetadata": [[6, 1, 1, "", "key"], [6, 1, 1, "", "value"]], "nodriver.cdp.background_service.RecordingStateChanged": [[6, 1, 1, "", "is_recording"], [6, 1, 1, "", "service"]], "nodriver.cdp.background_service.ServiceName": [[6, 1, 1, "", "BACKGROUND_FETCH"], [6, 1, 1, "", "BACKGROUND_SYNC"], [6, 1, 1, "", "NOTIFICATIONS"], [6, 1, 1, "", "PAYMENT_HANDLER"], [6, 1, 1, "", "PERIODIC_BACKGROUND_SYNC"], [6, 1, 1, "", "PUSH_MESSAGING"]], "nodriver.cdp.bluetooth_emulation": [[7, 0, 1, "", "CentralState"], [7, 0, 1, "", "GATTOperationType"], [7, 0, 1, "", "GattOperationReceived"], [7, 0, 1, "", "ManufacturerData"], [7, 0, 1, "", "ScanEntry"], [7, 0, 1, "", "ScanRecord"], [7, 5, 1, "", "disable"], [7, 5, 1, "", "enable"], [7, 5, 1, "", "set_simulated_central_state"], [7, 5, 1, "", "simulate_advertisement"], [7, 5, 1, "", "simulate_gatt_operation_response"], [7, 5, 1, "", "simulate_preconnected_peripheral"]], "nodriver.cdp.bluetooth_emulation.CentralState": [[7, 1, 1, "", "ABSENT"], [7, 1, 1, "", "POWERED_OFF"], [7, 1, 1, "", "POWERED_ON"]], "nodriver.cdp.bluetooth_emulation.GATTOperationType": [[7, 1, 1, "", "CONNECTION"], [7, 1, 1, "", "DISCOVERY"]], "nodriver.cdp.bluetooth_emulation.GattOperationReceived": [[7, 1, 1, "", "address"], [7, 1, 1, "", "type_"]], "nodriver.cdp.bluetooth_emulation.ManufacturerData": [[7, 1, 1, "", "data"], [7, 1, 1, "", "key"]], "nodriver.cdp.bluetooth_emulation.ScanEntry": [[7, 1, 1, "", "device_address"], [7, 1, 1, "", "rssi"], [7, 1, 1, "", "scan_record"]], "nodriver.cdp.bluetooth_emulation.ScanRecord": [[7, 1, 1, "", "appearance"], [7, 1, 1, "", "manufacturer_data"], [7, 1, 1, "", "name"], [7, 1, 1, "", "tx_power"], [7, 1, 1, "", "uuids"]], "nodriver.cdp.browser": [[8, 0, 1, "", "Bounds"], [8, 0, 1, "", "BrowserCommandId"], [8, 0, 1, "", "BrowserContextID"], [8, 0, 1, "", "Bucket"], [8, 0, 1, "", "DownloadProgress"], [8, 0, 1, "", "DownloadWillBegin"], [8, 0, 1, "", "Histogram"], [8, 0, 1, "", "PermissionDescriptor"], [8, 0, 1, "", "PermissionSetting"], [8, 0, 1, "", "PermissionType"], [8, 0, 1, "", "PrivacySandboxAPI"], [8, 0, 1, "", "WindowID"], [8, 0, 1, "", "WindowState"], [8, 5, 1, "", "add_privacy_sandbox_coordinator_key_config"], [8, 5, 1, "", "add_privacy_sandbox_enrollment_override"], [8, 5, 1, "", "cancel_download"], [8, 5, 1, "", "close"], [8, 5, 1, "", "crash"], [8, 5, 1, "", "crash_gpu_process"], [8, 5, 1, "", "execute_browser_command"], [8, 5, 1, "", "get_browser_command_line"], [8, 5, 1, "", "get_histogram"], [8, 5, 1, "", "get_histograms"], [8, 5, 1, "", "get_version"], [8, 5, 1, "", "get_window_bounds"], [8, 5, 1, "", "get_window_for_target"], [8, 5, 1, "", "grant_permissions"], [8, 5, 1, "", "reset_permissions"], [8, 5, 1, "", "set_dock_tile"], [8, 5, 1, "", "set_download_behavior"], [8, 5, 1, "", "set_permission"], [8, 5, 1, "", "set_window_bounds"]], "nodriver.cdp.browser.Bounds": [[8, 1, 1, "", "height"], [8, 1, 1, "", "left"], [8, 1, 1, "", "top"], [8, 1, 1, "", "width"], [8, 1, 1, "", "window_state"]], "nodriver.cdp.browser.BrowserCommandId": [[8, 1, 1, "", "CLOSE_TAB_SEARCH"], [8, 1, 1, "", "OPEN_TAB_SEARCH"]], "nodriver.cdp.browser.Bucket": [[8, 1, 1, "", "count"], [8, 1, 1, "", "high"], [8, 1, 1, "", "low"]], "nodriver.cdp.browser.DownloadProgress": [[8, 1, 1, "", "guid"], [8, 1, 1, "", "received_bytes"], [8, 1, 1, "", "state"], [8, 1, 1, "", "total_bytes"]], "nodriver.cdp.browser.DownloadWillBegin": [[8, 1, 1, "", "frame_id"], [8, 1, 1, "", "guid"], [8, 1, 1, "", "suggested_filename"], [8, 1, 1, "", "url"]], "nodriver.cdp.browser.Histogram": [[8, 1, 1, "", "buckets"], [8, 1, 1, "", "count"], [8, 1, 1, "", "name"], [8, 1, 1, "", "sum_"]], "nodriver.cdp.browser.PermissionDescriptor": [[8, 1, 1, "", "allow_without_gesture"], [8, 1, 1, "", "allow_without_sanitization"], [8, 1, 1, "", "name"], [8, 1, 1, "", "pan_tilt_zoom"], [8, 1, 1, "", "sysex"], [8, 1, 1, "", "user_visible_only"]], "nodriver.cdp.browser.PermissionSetting": [[8, 1, 1, "", "DENIED"], [8, 1, 1, "", "GRANTED"], [8, 1, 1, "", "PROMPT"]], "nodriver.cdp.browser.PermissionType": [[8, 1, 1, "", "AR"], [8, 1, 1, "", "AUDIO_CAPTURE"], [8, 1, 1, "", "AUTOMATIC_FULLSCREEN"], [8, 1, 1, "", "BACKGROUND_FETCH"], [8, 1, 1, "", "BACKGROUND_SYNC"], [8, 1, 1, "", "CAMERA_PAN_TILT_ZOOM"], [8, 1, 1, "", "CAPTURED_SURFACE_CONTROL"], [8, 1, 1, "", "CLIPBOARD_READ_WRITE"], [8, 1, 1, "", "CLIPBOARD_SANITIZED_WRITE"], [8, 1, 1, "", "DISPLAY_CAPTURE"], [8, 1, 1, "", "DURABLE_STORAGE"], [8, 1, 1, "", "GEOLOCATION"], [8, 1, 1, "", "HAND_TRACKING"], [8, 1, 1, "", "IDLE_DETECTION"], [8, 1, 1, "", "KEYBOARD_LOCK"], [8, 1, 1, "", "LOCAL_FONTS"], [8, 1, 1, "", "LOCAL_NETWORK_ACCESS"], [8, 1, 1, "", "MIDI"], [8, 1, 1, "", "MIDI_SYSEX"], [8, 1, 1, "", "NFC"], [8, 1, 1, "", "NOTIFICATIONS"], [8, 1, 1, "", "PAYMENT_HANDLER"], [8, 1, 1, "", "PERIODIC_BACKGROUND_SYNC"], [8, 1, 1, "", "POINTER_LOCK"], [8, 1, 1, "", "PROTECTED_MEDIA_IDENTIFIER"], [8, 1, 1, "", "SENSORS"], [8, 1, 1, "", "SMART_CARD"], [8, 1, 1, "", "SPEAKER_SELECTION"], [8, 1, 1, "", "STORAGE_ACCESS"], [8, 1, 1, "", "TOP_LEVEL_STORAGE_ACCESS"], [8, 1, 1, "", "VIDEO_CAPTURE"], [8, 1, 1, "", "VR"], [8, 1, 1, "", "WAKE_LOCK_SCREEN"], [8, 1, 1, "", "WAKE_LOCK_SYSTEM"], [8, 1, 1, "", "WEB_APP_INSTALLATION"], [8, 1, 1, "", "WEB_PRINTING"], [8, 1, 1, "", "WINDOW_MANAGEMENT"]], "nodriver.cdp.browser.PrivacySandboxAPI": [[8, 1, 1, "", "BIDDING_AND_AUCTION_SERVICES"], [8, 1, 1, "", "TRUSTED_KEY_VALUE"]], "nodriver.cdp.browser.WindowState": [[8, 1, 1, "", "FULLSCREEN"], [8, 1, 1, "", "MAXIMIZED"], [8, 1, 1, "", "MINIMIZED"], [8, 1, 1, "", "NORMAL"]], "nodriver.cdp.cache_storage": [[9, 0, 1, "", "Cache"], [9, 0, 1, "", "CacheId"], [9, 0, 1, "", "CachedResponse"], [9, 0, 1, "", "CachedResponseType"], [9, 0, 1, "", "DataEntry"], [9, 0, 1, "", "Header"], [9, 5, 1, "", "delete_cache"], [9, 5, 1, "", "delete_entry"], [9, 5, 1, "", "request_cache_names"], [9, 5, 1, "", "request_cached_response"], [9, 5, 1, "", "request_entries"]], "nodriver.cdp.cache_storage.Cache": [[9, 1, 1, "", "cache_id"], [9, 1, 1, "", "cache_name"], [9, 1, 1, "", "security_origin"], [9, 1, 1, "", "storage_bucket"], [9, 1, 1, "", "storage_key"]], "nodriver.cdp.cache_storage.CachedResponse": [[9, 1, 1, "", "body"]], "nodriver.cdp.cache_storage.CachedResponseType": [[9, 1, 1, "", "BASIC"], [9, 1, 1, "", "CORS"], [9, 1, 1, "", "DEFAULT"], [9, 1, 1, "", "ERROR"], [9, 1, 1, "", "OPAQUE_REDIRECT"], [9, 1, 1, "", "OPAQUE_RESPONSE"]], "nodriver.cdp.cache_storage.DataEntry": [[9, 1, 1, "", "request_headers"], [9, 1, 1, "", "request_method"], [9, 1, 1, "", "request_url"], [9, 1, 1, "", "response_headers"], [9, 1, 1, "", "response_status"], [9, 1, 1, "", "response_status_text"], [9, 1, 1, "", "response_time"], [9, 1, 1, "", "response_type"]], "nodriver.cdp.cache_storage.Header": [[9, 1, 1, "", "name"], [9, 1, 1, "", "value"]], "nodriver.cdp.cast": [[10, 0, 1, "", "IssueUpdated"], [10, 0, 1, "", "Sink"], [10, 0, 1, "", "SinksUpdated"], [10, 5, 1, "", "disable"], [10, 5, 1, "", "enable"], [10, 5, 1, "", "set_sink_to_use"], [10, 5, 1, "", "start_desktop_mirroring"], [10, 5, 1, "", "start_tab_mirroring"], [10, 5, 1, "", "stop_casting"]], "nodriver.cdp.cast.IssueUpdated": [[10, 1, 1, "", "issue_message"]], "nodriver.cdp.cast.Sink": [[10, 1, 1, "", "id_"], [10, 1, 1, "", "name"], [10, 1, 1, "", "session"]], "nodriver.cdp.cast.SinksUpdated": [[10, 1, 1, "", "sinks"]], "nodriver.cdp.console": [[11, 0, 1, "", "ConsoleMessage"], [11, 0, 1, "", "MessageAdded"], [11, 5, 1, "", "clear_messages"], [11, 5, 1, "", "disable"], [11, 5, 1, "", "enable"]], "nodriver.cdp.console.ConsoleMessage": [[11, 1, 1, "", "column"], [11, 1, 1, "", "level"], [11, 1, 1, "", "line"], [11, 1, 1, "", "source"], [11, 1, 1, "", "text"], [11, 1, 1, "", "url"]], "nodriver.cdp.console.MessageAdded": [[11, 1, 1, "", "message"]], "nodriver.cdp.css": [[12, 0, 1, "", "CSSAnimationStyle"], [12, 0, 1, "", "CSSComputedStyleProperty"], [12, 0, 1, "", "CSSContainerQuery"], [12, 0, 1, "", "CSSFontPaletteValuesRule"], [12, 0, 1, "", "CSSFunctionConditionNode"], [12, 0, 1, "", "CSSFunctionNode"], [12, 0, 1, "", "CSSFunctionParameter"], [12, 0, 1, "", "CSSFunctionRule"], [12, 0, 1, "", "CSSKeyframeRule"], [12, 0, 1, "", "CSSKeyframesRule"], [12, 0, 1, "", "CSSLayer"], [12, 0, 1, "", "CSSLayerData"], [12, 0, 1, "", "CSSMedia"], [12, 0, 1, "", "CSSPositionTryRule"], [12, 0, 1, "", "CSSProperty"], [12, 0, 1, "", "CSSPropertyRegistration"], [12, 0, 1, "", "CSSPropertyRule"], [12, 0, 1, "", "CSSRule"], [12, 0, 1, "", "CSSRuleType"], [12, 0, 1, "", "CSSScope"], [12, 0, 1, "", "CSSStartingStyle"], [12, 0, 1, "", "CSSStyle"], [12, 0, 1, "", "CSSStyleSheetHeader"], [12, 0, 1, "", "CSSSupports"], [12, 0, 1, "", "CSSTryRule"], [12, 0, 1, "", "ComputedStyleUpdated"], [12, 0, 1, "", "FontFace"], [12, 0, 1, "", "FontVariationAxis"], [12, 0, 1, "", "FontsUpdated"], [12, 0, 1, "", "InheritedAnimatedStyleEntry"], [12, 0, 1, "", "InheritedPseudoElementMatches"], [12, 0, 1, "", "InheritedStyleEntry"], [12, 0, 1, "", "MediaQuery"], [12, 0, 1, "", "MediaQueryExpression"], [12, 0, 1, "", "MediaQueryResultChanged"], [12, 0, 1, "", "PlatformFontUsage"], [12, 0, 1, "", "PseudoElementMatches"], [12, 0, 1, "", "RuleMatch"], [12, 0, 1, "", "RuleUsage"], [12, 0, 1, "", "SelectorList"], [12, 0, 1, "", "ShorthandEntry"], [12, 0, 1, "", "SourceRange"], [12, 0, 1, "", "Specificity"], [12, 0, 1, "", "StyleDeclarationEdit"], [12, 0, 1, "", "StyleSheetAdded"], [12, 0, 1, "", "StyleSheetChanged"], [12, 0, 1, "", "StyleSheetId"], [12, 0, 1, "", "StyleSheetOrigin"], [12, 0, 1, "", "StyleSheetRemoved"], [12, 0, 1, "", "Value"], [12, 5, 1, "", "add_rule"], [12, 5, 1, "", "collect_class_names"], [12, 5, 1, "", "create_style_sheet"], [12, 5, 1, "", "disable"], [12, 5, 1, "", "enable"], [12, 5, 1, "", "force_pseudo_state"], [12, 5, 1, "", "force_starting_style"], [12, 5, 1, "", "get_animated_styles_for_node"], [12, 5, 1, "", "get_background_colors"], [12, 5, 1, "", "get_computed_style_for_node"], [12, 5, 1, "", "get_inline_styles_for_node"], [12, 5, 1, "", "get_layers_for_node"], [12, 5, 1, "", "get_location_for_selector"], [12, 5, 1, "", "get_longhand_properties"], [12, 5, 1, "", "get_matched_styles_for_node"], [12, 5, 1, "", "get_media_queries"], [12, 5, 1, "", "get_platform_fonts_for_node"], [12, 5, 1, "", "get_style_sheet_text"], [12, 5, 1, "", "resolve_values"], [12, 5, 1, "", "set_container_query_text"], [12, 5, 1, "", "set_effective_property_value_for_node"], [12, 5, 1, "", "set_keyframe_key"], [12, 5, 1, "", "set_local_fonts_enabled"], [12, 5, 1, "", "set_media_text"], [12, 5, 1, "", "set_property_rule_property_name"], [12, 5, 1, "", "set_rule_selector"], [12, 5, 1, "", "set_scope_text"], [12, 5, 1, "", "set_style_sheet_text"], [12, 5, 1, "", "set_style_texts"], [12, 5, 1, "", "set_supports_text"], [12, 5, 1, "", "start_rule_usage_tracking"], [12, 5, 1, "", "stop_rule_usage_tracking"], [12, 5, 1, "", "take_computed_style_updates"], [12, 5, 1, "", "take_coverage_delta"], [12, 5, 1, "", "track_computed_style_updates"], [12, 5, 1, "", "track_computed_style_updates_for_node"]], "nodriver.cdp.css.CSSAnimationStyle": [[12, 1, 1, "", "name"], [12, 1, 1, "", "style"]], "nodriver.cdp.css.CSSComputedStyleProperty": [[12, 1, 1, "", "name"], [12, 1, 1, "", "value"]], "nodriver.cdp.css.CSSContainerQuery": [[12, 1, 1, "", "logical_axes"], [12, 1, 1, "", "name"], [12, 1, 1, "", "physical_axes"], [12, 1, 1, "", "queries_scroll_state"], [12, 1, 1, "", "range_"], [12, 1, 1, "", "style_sheet_id"], [12, 1, 1, "", "text"]], "nodriver.cdp.css.CSSFontPaletteValuesRule": [[12, 1, 1, "", "font_palette_name"], [12, 1, 1, "", "origin"], [12, 1, 1, "", "style"], [12, 1, 1, "", "style_sheet_id"]], "nodriver.cdp.css.CSSFunctionConditionNode": [[12, 1, 1, "", "children"], [12, 1, 1, "", "condition_text"], [12, 1, 1, "", "container_queries"], [12, 1, 1, "", "media"], [12, 1, 1, "", "supports"]], "nodriver.cdp.css.CSSFunctionNode": [[12, 1, 1, "", "condition"], [12, 1, 1, "", "style"]], "nodriver.cdp.css.CSSFunctionParameter": [[12, 1, 1, "", "name"], [12, 1, 1, "", "type_"]], "nodriver.cdp.css.CSSFunctionRule": [[12, 1, 1, "", "children"], [12, 1, 1, "", "name"], [12, 1, 1, "", "origin"], [12, 1, 1, "", "parameters"], [12, 1, 1, "", "style_sheet_id"]], "nodriver.cdp.css.CSSKeyframeRule": [[12, 1, 1, "", "key_text"], [12, 1, 1, "", "origin"], [12, 1, 1, "", "style"], [12, 1, 1, "", "style_sheet_id"]], "nodriver.cdp.css.CSSKeyframesRule": [[12, 1, 1, "", "animation_name"], [12, 1, 1, "", "keyframes"]], "nodriver.cdp.css.CSSLayer": [[12, 1, 1, "", "range_"], [12, 1, 1, "", "style_sheet_id"], [12, 1, 1, "", "text"]], "nodriver.cdp.css.CSSLayerData": [[12, 1, 1, "", "name"], [12, 1, 1, "", "order"], [12, 1, 1, "", "sub_layers"]], "nodriver.cdp.css.CSSMedia": [[12, 1, 1, "", "media_list"], [12, 1, 1, "", "range_"], [12, 1, 1, "", "source"], [12, 1, 1, "", "source_url"], [12, 1, 1, "", "style_sheet_id"], [12, 1, 1, "", "text"]], "nodriver.cdp.css.CSSPositionTryRule": [[12, 1, 1, "", "active"], [12, 1, 1, "", "name"], [12, 1, 1, "", "origin"], [12, 1, 1, "", "style"], [12, 1, 1, "", "style_sheet_id"]], "nodriver.cdp.css.CSSProperty": [[12, 1, 1, "", "disabled"], [12, 1, 1, "", "implicit"], [12, 1, 1, "", "important"], [12, 1, 1, "", "longhand_properties"], [12, 1, 1, "", "name"], [12, 1, 1, "", "parsed_ok"], [12, 1, 1, "", "range_"], [12, 1, 1, "", "text"], [12, 1, 1, "", "value"]], "nodriver.cdp.css.CSSPropertyRegistration": [[12, 1, 1, "", "inherits"], [12, 1, 1, "", "initial_value"], [12, 1, 1, "", "property_name"], [12, 1, 1, "", "syntax"]], "nodriver.cdp.css.CSSPropertyRule": [[12, 1, 1, "", "origin"], [12, 1, 1, "", "property_name"], [12, 1, 1, "", "style"], [12, 1, 1, "", "style_sheet_id"]], "nodriver.cdp.css.CSSRule": [[12, 1, 1, "", "container_queries"], [12, 1, 1, "", "layers"], [12, 1, 1, "", "media"], [12, 1, 1, "", "nesting_selectors"], [12, 1, 1, "", "origin"], [12, 1, 1, "", "rule_types"], [12, 1, 1, "", "scopes"], [12, 1, 1, "", "selector_list"], [12, 1, 1, "", "starting_styles"], [12, 1, 1, "", "style"], [12, 1, 1, "", "style_sheet_id"], [12, 1, 1, "", "supports"]], "nodriver.cdp.css.CSSRuleType": [[12, 1, 1, "", "CONTAINER_RULE"], [12, 1, 1, "", "LAYER_RULE"], [12, 1, 1, "", "MEDIA_RULE"], [12, 1, 1, "", "SCOPE_RULE"], [12, 1, 1, "", "STARTING_STYLE_RULE"], [12, 1, 1, "", "STYLE_RULE"], [12, 1, 1, "", "SUPPORTS_RULE"]], "nodriver.cdp.css.CSSScope": [[12, 1, 1, "", "range_"], [12, 1, 1, "", "style_sheet_id"], [12, 1, 1, "", "text"]], "nodriver.cdp.css.CSSStartingStyle": [[12, 1, 1, "", "range_"], [12, 1, 1, "", "style_sheet_id"]], "nodriver.cdp.css.CSSStyle": [[12, 1, 1, "", "css_properties"], [12, 1, 1, "", "css_text"], [12, 1, 1, "", "range_"], [12, 1, 1, "", "shorthand_entries"], [12, 1, 1, "", "style_sheet_id"]], "nodriver.cdp.css.CSSStyleSheetHeader": [[12, 1, 1, "", "disabled"], [12, 1, 1, "", "end_column"], [12, 1, 1, "", "end_line"], [12, 1, 1, "", "frame_id"], [12, 1, 1, "", "has_source_url"], [12, 1, 1, "", "is_constructed"], [12, 1, 1, "", "is_inline"], [12, 1, 1, "", "is_mutable"], [12, 1, 1, "", "length"], [12, 1, 1, "", "loading_failed"], [12, 1, 1, "", "origin"], [12, 1, 1, "", "owner_node"], [12, 1, 1, "", "source_map_url"], [12, 1, 1, "", "source_url"], [12, 1, 1, "", "start_column"], [12, 1, 1, "", "start_line"], [12, 1, 1, "", "style_sheet_id"], [12, 1, 1, "", "title"]], "nodriver.cdp.css.CSSSupports": [[12, 1, 1, "", "active"], [12, 1, 1, "", "range_"], [12, 1, 1, "", "style_sheet_id"], [12, 1, 1, "", "text"]], "nodriver.cdp.css.CSSTryRule": [[12, 1, 1, "", "origin"], [12, 1, 1, "", "style"], [12, 1, 1, "", "style_sheet_id"]], "nodriver.cdp.css.ComputedStyleUpdated": [[12, 1, 1, "", "node_id"]], "nodriver.cdp.css.FontFace": [[12, 1, 1, "", "font_display"], [12, 1, 1, "", "font_family"], [12, 1, 1, "", "font_stretch"], [12, 1, 1, "", "font_style"], [12, 1, 1, "", "font_variant"], [12, 1, 1, "", "font_variation_axes"], [12, 1, 1, "", "font_weight"], [12, 1, 1, "", "platform_font_family"], [12, 1, 1, "", "src"], [12, 1, 1, "", "unicode_range"]], "nodriver.cdp.css.FontVariationAxis": [[12, 1, 1, "", "default_value"], [12, 1, 1, "", "max_value"], [12, 1, 1, "", "min_value"], [12, 1, 1, "", "name"], [12, 1, 1, "", "tag"]], "nodriver.cdp.css.FontsUpdated": [[12, 1, 1, "", "font"]], "nodriver.cdp.css.InheritedAnimatedStyleEntry": [[12, 1, 1, "", "animation_styles"], [12, 1, 1, "", "transitions_style"]], "nodriver.cdp.css.InheritedPseudoElementMatches": [[12, 1, 1, "", "pseudo_elements"]], "nodriver.cdp.css.InheritedStyleEntry": [[12, 1, 1, "", "inline_style"], [12, 1, 1, "", "matched_css_rules"]], "nodriver.cdp.css.MediaQuery": [[12, 1, 1, "", "active"], [12, 1, 1, "", "expressions"]], "nodriver.cdp.css.MediaQueryExpression": [[12, 1, 1, "", "computed_length"], [12, 1, 1, "", "feature"], [12, 1, 1, "", "unit"], [12, 1, 1, "", "value"], [12, 1, 1, "", "value_range"]], "nodriver.cdp.css.PlatformFontUsage": [[12, 1, 1, "", "family_name"], [12, 1, 1, "", "glyph_count"], [12, 1, 1, "", "is_custom_font"], [12, 1, 1, "", "post_script_name"]], "nodriver.cdp.css.PseudoElementMatches": [[12, 1, 1, "", "matches"], [12, 1, 1, "", "pseudo_identifier"], [12, 1, 1, "", "pseudo_type"]], "nodriver.cdp.css.RuleMatch": [[12, 1, 1, "", "matching_selectors"], [12, 1, 1, "", "rule"]], "nodriver.cdp.css.RuleUsage": [[12, 1, 1, "", "end_offset"], [12, 1, 1, "", "start_offset"], [12, 1, 1, "", "style_sheet_id"], [12, 1, 1, "", "used"]], "nodriver.cdp.css.SelectorList": [[12, 1, 1, "", "selectors"], [12, 1, 1, "", "text"]], "nodriver.cdp.css.ShorthandEntry": [[12, 1, 1, "", "important"], [12, 1, 1, "", "name"], [12, 1, 1, "", "value"]], "nodriver.cdp.css.SourceRange": [[12, 1, 1, "", "end_column"], [12, 1, 1, "", "end_line"], [12, 1, 1, "", "start_column"], [12, 1, 1, "", "start_line"]], "nodriver.cdp.css.Specificity": [[12, 1, 1, "", "a"], [12, 1, 1, "", "b"], [12, 1, 1, "", "c"]], "nodriver.cdp.css.StyleDeclarationEdit": [[12, 1, 1, "", "range_"], [12, 1, 1, "", "style_sheet_id"], [12, 1, 1, "", "text"]], "nodriver.cdp.css.StyleSheetAdded": [[12, 1, 1, "", "header"]], "nodriver.cdp.css.StyleSheetChanged": [[12, 1, 1, "", "style_sheet_id"]], "nodriver.cdp.css.StyleSheetOrigin": [[12, 1, 1, "", "INJECTED"], [12, 1, 1, "", "INSPECTOR"], [12, 1, 1, "", "REGULAR"], [12, 1, 1, "", "USER_AGENT"]], "nodriver.cdp.css.StyleSheetRemoved": [[12, 1, 1, "", "style_sheet_id"]], "nodriver.cdp.css.Value": [[12, 1, 1, "", "range_"], [12, 1, 1, "", "specificity"], [12, 1, 1, "", "text"]], "nodriver.cdp.debugger": [[13, 0, 1, "", "BreakLocation"], [13, 0, 1, "", "BreakpointId"], [13, 0, 1, "", "BreakpointResolved"], [13, 0, 1, "", "CallFrame"], [13, 0, 1, "", "CallFrameId"], [13, 0, 1, "", "DebugSymbols"], [13, 0, 1, "", "Location"], [13, 0, 1, "", "LocationRange"], [13, 0, 1, "", "Paused"], [13, 0, 1, "", "ResolvedBreakpoint"], [13, 0, 1, "", "Resumed"], [13, 0, 1, "", "Scope"], [13, 0, 1, "", "ScriptFailedToParse"], [13, 0, 1, "", "ScriptLanguage"], [13, 0, 1, "", "ScriptParsed"], [13, 0, 1, "", "ScriptPosition"], [13, 0, 1, "", "SearchMatch"], [13, 0, 1, "", "WasmDisassemblyChunk"], [13, 5, 1, "", "continue_to_location"], [13, 5, 1, "", "disable"], [13, 5, 1, "", "disassemble_wasm_module"], [13, 5, 1, "", "enable"], [13, 5, 1, "", "evaluate_on_call_frame"], [13, 5, 1, "", "get_possible_breakpoints"], [13, 5, 1, "", "get_script_source"], [13, 5, 1, "", "get_stack_trace"], [13, 5, 1, "", "get_wasm_bytecode"], [13, 5, 1, "", "next_wasm_disassembly_chunk"], [13, 5, 1, "", "pause"], [13, 5, 1, "", "pause_on_async_call"], [13, 5, 1, "", "remove_breakpoint"], [13, 5, 1, "", "restart_frame"], [13, 5, 1, "", "resume"], [13, 5, 1, "", "search_in_content"], [13, 5, 1, "", "set_async_call_stack_depth"], [13, 5, 1, "", "set_blackbox_execution_contexts"], [13, 5, 1, "", "set_blackbox_patterns"], [13, 5, 1, "", "set_blackboxed_ranges"], [13, 5, 1, "", "set_breakpoint"], [13, 5, 1, "", "set_breakpoint_by_url"], [13, 5, 1, "", "set_breakpoint_on_function_call"], [13, 5, 1, "", "set_breakpoints_active"], [13, 5, 1, "", "set_instrumentation_breakpoint"], [13, 5, 1, "", "set_pause_on_exceptions"], [13, 5, 1, "", "set_return_value"], [13, 5, 1, "", "set_script_source"], [13, 5, 1, "", "set_skip_all_pauses"], [13, 5, 1, "", "set_variable_value"], [13, 5, 1, "", "step_into"], [13, 5, 1, "", "step_out"], [13, 5, 1, "", "step_over"]], "nodriver.cdp.debugger.BreakLocation": [[13, 1, 1, "", "column_number"], [13, 1, 1, "", "line_number"], [13, 1, 1, "", "script_id"], [13, 1, 1, "", "type_"]], "nodriver.cdp.debugger.BreakpointResolved": [[13, 1, 1, "", "breakpoint_id"], [13, 1, 1, "", "location"]], "nodriver.cdp.debugger.CallFrame": [[13, 1, 1, "", "call_frame_id"], [13, 1, 1, "", "can_be_restarted"], [13, 1, 1, "", "function_location"], [13, 1, 1, "", "function_name"], [13, 1, 1, "", "location"], [13, 1, 1, "", "return_value"], [13, 1, 1, "", "scope_chain"], [13, 1, 1, "", "this"], [13, 1, 1, "", "url"]], "nodriver.cdp.debugger.DebugSymbols": [[13, 1, 1, "", "external_url"], [13, 1, 1, "", "type_"]], "nodriver.cdp.debugger.Location": [[13, 1, 1, "", "column_number"], [13, 1, 1, "", "line_number"], [13, 1, 1, "", "script_id"]], "nodriver.cdp.debugger.LocationRange": [[13, 1, 1, "", "end"], [13, 1, 1, "", "script_id"], [13, 1, 1, "", "start"]], "nodriver.cdp.debugger.Paused": [[13, 1, 1, "", "async_call_stack_trace_id"], [13, 1, 1, "", "async_stack_trace"], [13, 1, 1, "", "async_stack_trace_id"], [13, 1, 1, "", "call_frames"], [13, 1, 1, "", "data"], [13, 1, 1, "", "hit_breakpoints"], [13, 1, 1, "", "reason"]], "nodriver.cdp.debugger.ResolvedBreakpoint": [[13, 1, 1, "", "breakpoint_id"], [13, 1, 1, "", "location"]], "nodriver.cdp.debugger.Scope": [[13, 1, 1, "", "end_location"], [13, 1, 1, "", "name"], [13, 1, 1, "", "object_"], [13, 1, 1, "", "start_location"], [13, 1, 1, "", "type_"]], "nodriver.cdp.debugger.ScriptFailedToParse": [[13, 1, 1, "", "build_id"], [13, 1, 1, "", "code_offset"], [13, 1, 1, "", "embedder_name"], [13, 1, 1, "", "end_column"], [13, 1, 1, "", "end_line"], [13, 1, 1, "", "execution_context_aux_data"], [13, 1, 1, "", "execution_context_id"], [13, 1, 1, "", "has_source_url"], [13, 1, 1, "", "hash_"], [13, 1, 1, "", "is_module"], [13, 1, 1, "", "length"], [13, 1, 1, "", "script_id"], [13, 1, 1, "", "script_language"], [13, 1, 1, "", "source_map_url"], [13, 1, 1, "", "stack_trace"], [13, 1, 1, "", "start_column"], [13, 1, 1, "", "start_line"], [13, 1, 1, "", "url"]], "nodriver.cdp.debugger.ScriptLanguage": [[13, 1, 1, "", "JAVA_SCRIPT"], [13, 1, 1, "", "WEB_ASSEMBLY"]], "nodriver.cdp.debugger.ScriptParsed": [[13, 1, 1, "", "build_id"], [13, 1, 1, "", "code_offset"], [13, 1, 1, "", "debug_symbols"], [13, 1, 1, "", "embedder_name"], [13, 1, 1, "", "end_column"], [13, 1, 1, "", "end_line"], [13, 1, 1, "", "execution_context_aux_data"], [13, 1, 1, "", "execution_context_id"], [13, 1, 1, "", "has_source_url"], [13, 1, 1, "", "hash_"], [13, 1, 1, "", "is_live_edit"], [13, 1, 1, "", "is_module"], [13, 1, 1, "", "length"], [13, 1, 1, "", "resolved_breakpoints"], [13, 1, 1, "", "script_id"], [13, 1, 1, "", "script_language"], [13, 1, 1, "", "source_map_url"], [13, 1, 1, "", "stack_trace"], [13, 1, 1, "", "start_column"], [13, 1, 1, "", "start_line"], [13, 1, 1, "", "url"]], "nodriver.cdp.debugger.ScriptPosition": [[13, 1, 1, "", "column_number"], [13, 1, 1, "", "line_number"]], "nodriver.cdp.debugger.SearchMatch": [[13, 1, 1, "", "line_content"], [13, 1, 1, "", "line_number"]], "nodriver.cdp.debugger.WasmDisassemblyChunk": [[13, 1, 1, "", "bytecode_offsets"], [13, 1, 1, "", "lines"]], "nodriver.cdp.device_access": [[14, 0, 1, "", "DeviceId"], [14, 0, 1, "", "DeviceRequestPrompted"], [14, 0, 1, "", "PromptDevice"], [14, 0, 1, "", "RequestId"], [14, 5, 1, "", "cancel_prompt"], [14, 5, 1, "", "disable"], [14, 5, 1, "", "enable"], [14, 5, 1, "", "select_prompt"]], "nodriver.cdp.device_access.DeviceRequestPrompted": [[14, 1, 1, "", "devices"], [14, 1, 1, "", "id_"]], "nodriver.cdp.device_access.PromptDevice": [[14, 1, 1, "", "id_"], [14, 1, 1, "", "name"]], "nodriver.cdp.device_orientation": [[15, 5, 1, "", "clear_device_orientation_override"], [15, 5, 1, "", "set_device_orientation_override"]], "nodriver.cdp.dom": [[16, 0, 1, "", "AttributeModified"], [16, 0, 1, "", "AttributeRemoved"], [16, 0, 1, "", "BackendNode"], [16, 0, 1, "", "BackendNodeId"], [16, 0, 1, "", "BoxModel"], [16, 0, 1, "", "CSSComputedStyleProperty"], [16, 0, 1, "", "CharacterDataModified"], [16, 0, 1, "", "ChildNodeCountUpdated"], [16, 0, 1, "", "ChildNodeInserted"], [16, 0, 1, "", "ChildNodeRemoved"], [16, 0, 1, "", "CompatibilityMode"], [16, 0, 1, "", "DetachedElementInfo"], [16, 0, 1, "", "DistributedNodesUpdated"], [16, 0, 1, "", "DocumentUpdated"], [16, 0, 1, "", "InlineStyleInvalidated"], [16, 0, 1, "", "LogicalAxes"], [16, 0, 1, "", "Node"], [16, 0, 1, "", "NodeId"], [16, 0, 1, "", "PhysicalAxes"], [16, 0, 1, "", "PseudoElementAdded"], [16, 0, 1, "", "PseudoElementRemoved"], [16, 0, 1, "", "PseudoType"], [16, 0, 1, "", "Quad"], [16, 0, 1, "", "RGBA"], [16, 0, 1, "", "Rect"], [16, 0, 1, "", "ScrollOrientation"], [16, 0, 1, "", "ScrollableFlagUpdated"], [16, 0, 1, "", "SetChildNodes"], [16, 0, 1, "", "ShadowRootPopped"], [16, 0, 1, "", "ShadowRootPushed"], [16, 0, 1, "", "ShadowRootType"], [16, 0, 1, "", "ShapeOutsideInfo"], [16, 0, 1, "", "TopLayerElementsUpdated"], [16, 5, 1, "", "collect_class_names_from_subtree"], [16, 5, 1, "", "copy_to"], [16, 5, 1, "", "describe_node"], [16, 5, 1, "", "disable"], [16, 5, 1, "", "discard_search_results"], [16, 5, 1, "", "enable"], [16, 5, 1, "", "focus"], [16, 5, 1, "", "get_anchor_element"], [16, 5, 1, "", "get_attributes"], [16, 5, 1, "", "get_box_model"], [16, 5, 1, "", "get_container_for_node"], [16, 5, 1, "", "get_content_quads"], [16, 5, 1, "", "get_detached_dom_nodes"], [16, 5, 1, "", "get_document"], [16, 5, 1, "", "get_element_by_relation"], [16, 5, 1, "", "get_file_info"], [16, 5, 1, "", "get_flattened_document"], [16, 5, 1, "", "get_frame_owner"], [16, 5, 1, "", "get_node_for_location"], [16, 5, 1, "", "get_node_stack_traces"], [16, 5, 1, "", "get_nodes_for_subtree_by_style"], [16, 5, 1, "", "get_outer_html"], [16, 5, 1, "", "get_querying_descendants_for_container"], [16, 5, 1, "", "get_relayout_boundary"], [16, 5, 1, "", "get_search_results"], [16, 5, 1, "", "get_top_layer_elements"], [16, 5, 1, "", "hide_highlight"], [16, 5, 1, "", "highlight_node"], [16, 5, 1, "", "highlight_rect"], [16, 5, 1, "", "mark_undoable_state"], [16, 5, 1, "", "move_to"], [16, 5, 1, "", "perform_search"], [16, 5, 1, "", "push_node_by_path_to_frontend"], [16, 5, 1, "", "push_nodes_by_backend_ids_to_frontend"], [16, 5, 1, "", "query_selector"], [16, 5, 1, "", "query_selector_all"], [16, 5, 1, "", "redo"], [16, 5, 1, "", "remove_attribute"], [16, 5, 1, "", "remove_node"], [16, 5, 1, "", "request_child_nodes"], [16, 5, 1, "", "request_node"], [16, 5, 1, "", "resolve_node"], [16, 5, 1, "", "scroll_into_view_if_needed"], [16, 5, 1, "", "set_attribute_value"], [16, 5, 1, "", "set_attributes_as_text"], [16, 5, 1, "", "set_file_input_files"], [16, 5, 1, "", "set_inspected_node"], [16, 5, 1, "", "set_node_name"], [16, 5, 1, "", "set_node_stack_traces_enabled"], [16, 5, 1, "", "set_node_value"], [16, 5, 1, "", "set_outer_html"], [16, 5, 1, "", "undo"]], "nodriver.cdp.dom.AttributeModified": [[16, 1, 1, "", "name"], [16, 1, 1, "", "node_id"], [16, 1, 1, "", "value"]], "nodriver.cdp.dom.AttributeRemoved": [[16, 1, 1, "", "name"], [16, 1, 1, "", "node_id"]], "nodriver.cdp.dom.BackendNode": [[16, 1, 1, "", "backend_node_id"], [16, 1, 1, "", "node_name"], [16, 1, 1, "", "node_type"]], "nodriver.cdp.dom.BoxModel": [[16, 1, 1, "", "border"], [16, 1, 1, "", "content"], [16, 1, 1, "", "height"], [16, 1, 1, "", "margin"], [16, 1, 1, "", "padding"], [16, 1, 1, "", "shape_outside"], [16, 1, 1, "", "width"]], "nodriver.cdp.dom.CSSComputedStyleProperty": [[16, 1, 1, "", "name"], [16, 1, 1, "", "value"]], "nodriver.cdp.dom.CharacterDataModified": [[16, 1, 1, "", "character_data"], [16, 1, 1, "", "node_id"]], "nodriver.cdp.dom.ChildNodeCountUpdated": [[16, 1, 1, "", "child_node_count"], [16, 1, 1, "", "node_id"]], "nodriver.cdp.dom.ChildNodeInserted": [[16, 1, 1, "", "node"], [16, 1, 1, "", "parent_node_id"], [16, 1, 1, "", "previous_node_id"]], "nodriver.cdp.dom.ChildNodeRemoved": [[16, 1, 1, "", "node_id"], [16, 1, 1, "", "parent_node_id"]], "nodriver.cdp.dom.CompatibilityMode": [[16, 1, 1, "", "LIMITED_QUIRKS_MODE"], [16, 1, 1, "", "NO_QUIRKS_MODE"], [16, 1, 1, "", "QUIRKS_MODE"]], "nodriver.cdp.dom.DetachedElementInfo": [[16, 1, 1, "", "retained_node_ids"], [16, 1, 1, "", "tree_node"]], "nodriver.cdp.dom.DistributedNodesUpdated": [[16, 1, 1, "", "distributed_nodes"], [16, 1, 1, "", "insertion_point_id"]], "nodriver.cdp.dom.InlineStyleInvalidated": [[16, 1, 1, "", "node_ids"]], "nodriver.cdp.dom.LogicalAxes": [[16, 1, 1, "", "BLOCK"], [16, 1, 1, "", "BOTH"], [16, 1, 1, "", "INLINE"]], "nodriver.cdp.dom.Node": [[16, 1, 1, "", "assigned_slot"], [16, 1, 1, "", "attributes"], [16, 1, 1, "", "backend_node_id"], [16, 1, 1, "", "base_url"], [16, 1, 1, "", "child_node_count"], [16, 1, 1, "", "children"], [16, 1, 1, "", "compatibility_mode"], [16, 1, 1, "", "content_document"], [16, 1, 1, "", "distributed_nodes"], [16, 1, 1, "", "document_url"], [16, 1, 1, "", "frame_id"], [16, 1, 1, "", "imported_document"], [16, 1, 1, "", "internal_subset"], [16, 1, 1, "", "is_scrollable"], [16, 1, 1, "", "is_svg"], [16, 1, 1, "", "local_name"], [16, 1, 1, "", "name"], [16, 1, 1, "", "node_id"], [16, 1, 1, "", "node_name"], [16, 1, 1, "", "node_type"], [16, 1, 1, "", "node_value"], [16, 1, 1, "", "parent_id"], [16, 1, 1, "", "pseudo_elements"], [16, 1, 1, "", "pseudo_identifier"], [16, 1, 1, "", "pseudo_type"], [16, 1, 1, "", "public_id"], [16, 1, 1, "", "shadow_root_type"], [16, 1, 1, "", "shadow_roots"], [16, 1, 1, "", "system_id"], [16, 1, 1, "", "template_content"], [16, 1, 1, "", "value"], [16, 1, 1, "", "xml_version"]], "nodriver.cdp.dom.PhysicalAxes": [[16, 1, 1, "", "BOTH"], [16, 1, 1, "", "HORIZONTAL"], [16, 1, 1, "", "VERTICAL"]], "nodriver.cdp.dom.PseudoElementAdded": [[16, 1, 1, "", "parent_id"], [16, 1, 1, "", "pseudo_element"]], "nodriver.cdp.dom.PseudoElementRemoved": [[16, 1, 1, "", "parent_id"], [16, 1, 1, "", "pseudo_element_id"]], "nodriver.cdp.dom.PseudoType": [[16, 1, 1, "", "AFTER"], [16, 1, 1, "", "BACKDROP"], [16, 1, 1, "", "BEFORE"], [16, 1, 1, "", "CHECKMARK"], [16, 1, 1, "", "COLUMN"], [16, 1, 1, "", "DETAILS_CONTENT"], [16, 1, 1, "", "FILE_SELECTOR_BUTTON"], [16, 1, 1, "", "FIRST_LETTER"], [16, 1, 1, "", "FIRST_LINE"], [16, 1, 1, "", "FIRST_LINE_INHERITED"], [16, 1, 1, "", "GRAMMAR_ERROR"], [16, 1, 1, "", "HIGHLIGHT"], [16, 1, 1, "", "INPUT_LIST_BUTTON"], [16, 1, 1, "", "MARKER"], [16, 1, 1, "", "PICKER"], [16, 1, 1, "", "PICKER_ICON"], [16, 1, 1, "", "PLACEHOLDER"], [16, 1, 1, "", "RESIZER"], [16, 1, 1, "", "SCROLLBAR"], [16, 1, 1, "", "SCROLLBAR_BUTTON"], [16, 1, 1, "", "SCROLLBAR_CORNER"], [16, 1, 1, "", "SCROLLBAR_THUMB"], [16, 1, 1, "", "SCROLLBAR_TRACK"], [16, 1, 1, "", "SCROLLBAR_TRACK_PIECE"], [16, 1, 1, "", "SCROLL_BUTTON"], [16, 1, 1, "", "SCROLL_MARKER"], [16, 1, 1, "", "SCROLL_MARKER_GROUP"], [16, 1, 1, "", "SEARCH_TEXT"], [16, 1, 1, "", "SELECTION"], [16, 1, 1, "", "SPELLING_ERROR"], [16, 1, 1, "", "TARGET_TEXT"], [16, 1, 1, "", "VIEW_TRANSITION"], [16, 1, 1, "", "VIEW_TRANSITION_GROUP"], [16, 1, 1, "", "VIEW_TRANSITION_IMAGE_PAIR"], [16, 1, 1, "", "VIEW_TRANSITION_NEW"], [16, 1, 1, "", "VIEW_TRANSITION_OLD"]], "nodriver.cdp.dom.RGBA": [[16, 1, 1, "", "a"], [16, 1, 1, "", "b"], [16, 1, 1, "", "g"], [16, 1, 1, "", "r"]], "nodriver.cdp.dom.Rect": [[16, 1, 1, "", "height"], [16, 1, 1, "", "width"], [16, 1, 1, "", "x"], [16, 1, 1, "", "y"]], "nodriver.cdp.dom.ScrollOrientation": [[16, 1, 1, "", "HORIZONTAL"], [16, 1, 1, "", "VERTICAL"]], "nodriver.cdp.dom.ScrollableFlagUpdated": [[16, 1, 1, "", "is_scrollable"], [16, 1, 1, "", "node_id"]], "nodriver.cdp.dom.SetChildNodes": [[16, 1, 1, "", "nodes"], [16, 1, 1, "", "parent_id"]], "nodriver.cdp.dom.ShadowRootPopped": [[16, 1, 1, "", "host_id"], [16, 1, 1, "", "root_id"]], "nodriver.cdp.dom.ShadowRootPushed": [[16, 1, 1, "", "host_id"], [16, 1, 1, "", "root"]], "nodriver.cdp.dom.ShadowRootType": [[16, 1, 1, "", "CLOSED"], [16, 1, 1, "", "OPEN_"], [16, 1, 1, "", "USER_AGENT"]], "nodriver.cdp.dom.ShapeOutsideInfo": [[16, 1, 1, "", "bounds"], [16, 1, 1, "", "margin_shape"], [16, 1, 1, "", "shape"]], "nodriver.cdp.dom_debugger": [[17, 0, 1, "", "CSPViolationType"], [17, 0, 1, "", "DOMBreakpointType"], [17, 0, 1, "", "EventListener"], [17, 5, 1, "", "get_event_listeners"], [17, 5, 1, "", "remove_dom_breakpoint"], [17, 5, 1, "", "remove_event_listener_breakpoint"], [17, 5, 1, "", "remove_instrumentation_breakpoint"], [17, 5, 1, "", "remove_xhr_breakpoint"], [17, 5, 1, "", "set_break_on_csp_violation"], [17, 5, 1, "", "set_dom_breakpoint"], [17, 5, 1, "", "set_event_listener_breakpoint"], [17, 5, 1, "", "set_instrumentation_breakpoint"], [17, 5, 1, "", "set_xhr_breakpoint"]], "nodriver.cdp.dom_debugger.CSPViolationType": [[17, 1, 1, "", "TRUSTEDTYPE_POLICY_VIOLATION"], [17, 1, 1, "", "TRUSTEDTYPE_SINK_VIOLATION"]], "nodriver.cdp.dom_debugger.DOMBreakpointType": [[17, 1, 1, "", "ATTRIBUTE_MODIFIED"], [17, 1, 1, "", "NODE_REMOVED"], [17, 1, 1, "", "SUBTREE_MODIFIED"]], "nodriver.cdp.dom_debugger.EventListener": [[17, 1, 1, "", "backend_node_id"], [17, 1, 1, "", "column_number"], [17, 1, 1, "", "handler"], [17, 1, 1, "", "line_number"], [17, 1, 1, "", "once"], [17, 1, 1, "", "original_handler"], [17, 1, 1, "", "passive"], [17, 1, 1, "", "script_id"], [17, 1, 1, "", "type_"], [17, 1, 1, "", "use_capture"]], "nodriver.cdp.dom_snapshot": [[18, 0, 1, "", "ArrayOfStrings"], [18, 0, 1, "", "ComputedStyle"], [18, 0, 1, "", "DOMNode"], [18, 0, 1, "", "DocumentSnapshot"], [18, 0, 1, "", "InlineTextBox"], [18, 0, 1, "", "LayoutTreeNode"], [18, 0, 1, "", "LayoutTreeSnapshot"], [18, 0, 1, "", "NameValue"], [18, 0, 1, "", "NodeTreeSnapshot"], [18, 0, 1, "", "RareBooleanData"], [18, 0, 1, "", "RareIntegerData"], [18, 0, 1, "", "RareStringData"], [18, 0, 1, "", "Rectangle"], [18, 0, 1, "", "StringIndex"], [18, 0, 1, "", "TextBoxSnapshot"], [18, 5, 1, "", "capture_snapshot"], [18, 5, 1, "", "disable"], [18, 5, 1, "", "enable"], [18, 5, 1, "", "get_snapshot"]], "nodriver.cdp.dom_snapshot.ComputedStyle": [[18, 1, 1, "", "properties"]], "nodriver.cdp.dom_snapshot.DOMNode": [[18, 1, 1, "", "attributes"], [18, 1, 1, "", "backend_node_id"], [18, 1, 1, "", "base_url"], [18, 1, 1, "", "child_node_indexes"], [18, 1, 1, "", "content_document_index"], [18, 1, 1, "", "content_language"], [18, 1, 1, "", "current_source_url"], [18, 1, 1, "", "document_encoding"], [18, 1, 1, "", "document_url"], [18, 1, 1, "", "event_listeners"], [18, 1, 1, "", "frame_id"], [18, 1, 1, "", "input_checked"], [18, 1, 1, "", "input_value"], [18, 1, 1, "", "is_clickable"], [18, 1, 1, "", "layout_node_index"], [18, 1, 1, "", "node_name"], [18, 1, 1, "", "node_type"], [18, 1, 1, "", "node_value"], [18, 1, 1, "", "option_selected"], [18, 1, 1, "", "origin_url"], [18, 1, 1, "", "pseudo_element_indexes"], [18, 1, 1, "", "pseudo_type"], [18, 1, 1, "", "public_id"], [18, 1, 1, "", "scroll_offset_x"], [18, 1, 1, "", "scroll_offset_y"], [18, 1, 1, "", "shadow_root_type"], [18, 1, 1, "", "system_id"], [18, 1, 1, "", "text_value"]], "nodriver.cdp.dom_snapshot.DocumentSnapshot": [[18, 1, 1, "", "base_url"], [18, 1, 1, "", "content_height"], [18, 1, 1, "", "content_language"], [18, 1, 1, "", "content_width"], [18, 1, 1, "", "document_url"], [18, 1, 1, "", "encoding_name"], [18, 1, 1, "", "frame_id"], [18, 1, 1, "", "layout"], [18, 1, 1, "", "nodes"], [18, 1, 1, "", "public_id"], [18, 1, 1, "", "scroll_offset_x"], [18, 1, 1, "", "scroll_offset_y"], [18, 1, 1, "", "system_id"], [18, 1, 1, "", "text_boxes"], [18, 1, 1, "", "title"]], "nodriver.cdp.dom_snapshot.InlineTextBox": [[18, 1, 1, "", "bounding_box"], [18, 1, 1, "", "num_characters"], [18, 1, 1, "", "start_character_index"]], "nodriver.cdp.dom_snapshot.LayoutTreeNode": [[18, 1, 1, "", "bounding_box"], [18, 1, 1, "", "dom_node_index"], [18, 1, 1, "", "inline_text_nodes"], [18, 1, 1, "", "is_stacking_context"], [18, 1, 1, "", "layout_text"], [18, 1, 1, "", "paint_order"], [18, 1, 1, "", "style_index"]], "nodriver.cdp.dom_snapshot.LayoutTreeSnapshot": [[18, 1, 1, "", "blended_background_colors"], [18, 1, 1, "", "bounds"], [18, 1, 1, "", "client_rects"], [18, 1, 1, "", "node_index"], [18, 1, 1, "", "offset_rects"], [18, 1, 1, "", "paint_orders"], [18, 1, 1, "", "scroll_rects"], [18, 1, 1, "", "stacking_contexts"], [18, 1, 1, "", "styles"], [18, 1, 1, "", "text"], [18, 1, 1, "", "text_color_opacities"]], "nodriver.cdp.dom_snapshot.NameValue": [[18, 1, 1, "", "name"], [18, 1, 1, "", "value"]], "nodriver.cdp.dom_snapshot.NodeTreeSnapshot": [[18, 1, 1, "", "attributes"], [18, 1, 1, "", "backend_node_id"], [18, 1, 1, "", "content_document_index"], [18, 1, 1, "", "current_source_url"], [18, 1, 1, "", "input_checked"], [18, 1, 1, "", "input_value"], [18, 1, 1, "", "is_clickable"], [18, 1, 1, "", "node_name"], [18, 1, 1, "", "node_type"], [18, 1, 1, "", "node_value"], [18, 1, 1, "", "option_selected"], [18, 1, 1, "", "origin_url"], [18, 1, 1, "", "parent_index"], [18, 1, 1, "", "pseudo_identifier"], [18, 1, 1, "", "pseudo_type"], [18, 1, 1, "", "shadow_root_type"], [18, 1, 1, "", "text_value"]], "nodriver.cdp.dom_snapshot.RareBooleanData": [[18, 1, 1, "", "index"]], "nodriver.cdp.dom_snapshot.RareIntegerData": [[18, 1, 1, "", "index"], [18, 1, 1, "", "value"]], "nodriver.cdp.dom_snapshot.RareStringData": [[18, 1, 1, "", "index"], [18, 1, 1, "", "value"]], "nodriver.cdp.dom_snapshot.TextBoxSnapshot": [[18, 1, 1, "", "bounds"], [18, 1, 1, "", "layout_index"], [18, 1, 1, "", "length"], [18, 1, 1, "", "start"]], "nodriver.cdp.dom_storage": [[19, 0, 1, "", "DomStorageItemAdded"], [19, 0, 1, "", "DomStorageItemRemoved"], [19, 0, 1, "", "DomStorageItemUpdated"], [19, 0, 1, "", "DomStorageItemsCleared"], [19, 0, 1, "", "Item"], [19, 0, 1, "", "SerializedStorageKey"], [19, 0, 1, "", "StorageId"], [19, 5, 1, "", "clear"], [19, 5, 1, "", "disable"], [19, 5, 1, "", "enable"], [19, 5, 1, "", "get_dom_storage_items"], [19, 5, 1, "", "remove_dom_storage_item"], [19, 5, 1, "", "set_dom_storage_item"]], "nodriver.cdp.dom_storage.DomStorageItemAdded": [[19, 1, 1, "", "key"], [19, 1, 1, "", "new_value"], [19, 1, 1, "", "storage_id"]], "nodriver.cdp.dom_storage.DomStorageItemRemoved": [[19, 1, 1, "", "key"], [19, 1, 1, "", "storage_id"]], "nodriver.cdp.dom_storage.DomStorageItemUpdated": [[19, 1, 1, "", "key"], [19, 1, 1, "", "new_value"], [19, 1, 1, "", "old_value"], [19, 1, 1, "", "storage_id"]], "nodriver.cdp.dom_storage.DomStorageItemsCleared": [[19, 1, 1, "", "storage_id"]], "nodriver.cdp.dom_storage.StorageId": [[19, 1, 1, "", "is_local_storage"], [19, 1, 1, "", "security_origin"], [19, 1, 1, "", "storage_key"]], "nodriver.cdp.emulation": [[20, 0, 1, "", "DevicePosture"], [20, 0, 1, "", "DisabledImageType"], [20, 0, 1, "", "DisplayFeature"], [20, 0, 1, "", "MediaFeature"], [20, 0, 1, "", "PressureMetadata"], [20, 0, 1, "", "PressureSource"], [20, 0, 1, "", "PressureState"], [20, 0, 1, "", "SafeAreaInsets"], [20, 0, 1, "", "ScreenOrientation"], [20, 0, 1, "", "SensorMetadata"], [20, 0, 1, "", "SensorReading"], [20, 0, 1, "", "SensorReadingQuaternion"], [20, 0, 1, "", "SensorReadingSingle"], [20, 0, 1, "", "SensorReadingXYZ"], [20, 0, 1, "", "SensorType"], [20, 0, 1, "", "UserAgentBrandVersion"], [20, 0, 1, "", "UserAgentMetadata"], [20, 0, 1, "", "VirtualTimeBudgetExpired"], [20, 0, 1, "", "VirtualTimePolicy"], [20, 5, 1, "", "can_emulate"], [20, 5, 1, "", "clear_device_metrics_override"], [20, 5, 1, "", "clear_device_posture_override"], [20, 5, 1, "", "clear_display_features_override"], [20, 5, 1, "", "clear_geolocation_override"], [20, 5, 1, "", "clear_idle_override"], [20, 5, 1, "", "get_overridden_sensor_information"], [20, 5, 1, "", "reset_page_scale_factor"], [20, 5, 1, "", "set_auto_dark_mode_override"], [20, 5, 1, "", "set_automation_override"], [20, 5, 1, "", "set_cpu_throttling_rate"], [20, 5, 1, "", "set_default_background_color_override"], [20, 5, 1, "", "set_device_metrics_override"], [20, 5, 1, "", "set_device_posture_override"], [20, 5, 1, "", "set_disabled_image_types"], [20, 5, 1, "", "set_display_features_override"], [20, 5, 1, "", "set_document_cookie_disabled"], [20, 5, 1, "", "set_emit_touch_events_for_mouse"], [20, 5, 1, "", "set_emulated_media"], [20, 5, 1, "", "set_emulated_vision_deficiency"], [20, 5, 1, "", "set_focus_emulation_enabled"], [20, 5, 1, "", "set_geolocation_override"], [20, 5, 1, "", "set_hardware_concurrency_override"], [20, 5, 1, "", "set_idle_override"], [20, 5, 1, "", "set_locale_override"], [20, 5, 1, "", "set_navigator_overrides"], [20, 5, 1, "", "set_page_scale_factor"], [20, 5, 1, "", "set_pressure_source_override_enabled"], [20, 5, 1, "", "set_pressure_state_override"], [20, 5, 1, "", "set_safe_area_insets_override"], [20, 5, 1, "", "set_script_execution_disabled"], [20, 5, 1, "", "set_scrollbars_hidden"], [20, 5, 1, "", "set_sensor_override_enabled"], [20, 5, 1, "", "set_sensor_override_readings"], [20, 5, 1, "", "set_timezone_override"], [20, 5, 1, "", "set_touch_emulation_enabled"], [20, 5, 1, "", "set_user_agent_override"], [20, 5, 1, "", "set_virtual_time_policy"], [20, 5, 1, "", "set_visible_size"]], "nodriver.cdp.emulation.DevicePosture": [[20, 1, 1, "", "type_"]], "nodriver.cdp.emulation.DisabledImageType": [[20, 1, 1, "", "AVIF"], [20, 1, 1, "", "WEBP"]], "nodriver.cdp.emulation.DisplayFeature": [[20, 1, 1, "", "mask_length"], [20, 1, 1, "", "offset"], [20, 1, 1, "", "orientation"]], "nodriver.cdp.emulation.MediaFeature": [[20, 1, 1, "", "name"], [20, 1, 1, "", "value"]], "nodriver.cdp.emulation.PressureMetadata": [[20, 1, 1, "", "available"]], "nodriver.cdp.emulation.PressureSource": [[20, 1, 1, "", "CPU"]], "nodriver.cdp.emulation.PressureState": [[20, 1, 1, "", "CRITICAL"], [20, 1, 1, "", "FAIR"], [20, 1, 1, "", "NOMINAL"], [20, 1, 1, "", "SERIOUS"]], "nodriver.cdp.emulation.SafeAreaInsets": [[20, 1, 1, "", "bottom"], [20, 1, 1, "", "bottom_max"], [20, 1, 1, "", "left"], [20, 1, 1, "", "left_max"], [20, 1, 1, "", "right"], [20, 1, 1, "", "right_max"], [20, 1, 1, "", "top"], [20, 1, 1, "", "top_max"]], "nodriver.cdp.emulation.ScreenOrientation": [[20, 1, 1, "", "angle"], [20, 1, 1, "", "type_"]], "nodriver.cdp.emulation.SensorMetadata": [[20, 1, 1, "", "available"], [20, 1, 1, "", "maximum_frequency"], [20, 1, 1, "", "minimum_frequency"]], "nodriver.cdp.emulation.SensorReading": [[20, 1, 1, "", "quaternion"], [20, 1, 1, "", "single"], [20, 1, 1, "", "xyz"]], "nodriver.cdp.emulation.SensorReadingQuaternion": [[20, 1, 1, "", "w"], [20, 1, 1, "", "x"], [20, 1, 1, "", "y"], [20, 1, 1, "", "z"]], "nodriver.cdp.emulation.SensorReadingSingle": [[20, 1, 1, "", "value"]], "nodriver.cdp.emulation.SensorReadingXYZ": [[20, 1, 1, "", "x"], [20, 1, 1, "", "y"], [20, 1, 1, "", "z"]], "nodriver.cdp.emulation.SensorType": [[20, 1, 1, "", "ABSOLUTE_ORIENTATION"], [20, 1, 1, "", "ACCELEROMETER"], [20, 1, 1, "", "AMBIENT_LIGHT"], [20, 1, 1, "", "GRAVITY"], [20, 1, 1, "", "GYROSCOPE"], [20, 1, 1, "", "LINEAR_ACCELERATION"], [20, 1, 1, "", "MAGNETOMETER"], [20, 1, 1, "", "RELATIVE_ORIENTATION"]], "nodriver.cdp.emulation.UserAgentBrandVersion": [[20, 1, 1, "", "brand"], [20, 1, 1, "", "version"]], "nodriver.cdp.emulation.UserAgentMetadata": [[20, 1, 1, "", "architecture"], [20, 1, 1, "", "bitness"], [20, 1, 1, "", "brands"], [20, 1, 1, "", "full_version"], [20, 1, 1, "", "full_version_list"], [20, 1, 1, "", "mobile"], [20, 1, 1, "", "model"], [20, 1, 1, "", "platform"], [20, 1, 1, "", "platform_version"], [20, 1, 1, "", "wow64"]], "nodriver.cdp.emulation.VirtualTimePolicy": [[20, 1, 1, "", "ADVANCE"], [20, 1, 1, "", "PAUSE"], [20, 1, 1, "", "PAUSE_IF_NETWORK_FETCHES_PENDING"]], "nodriver.cdp.event_breakpoints": [[21, 5, 1, "", "disable"], [21, 5, 1, "", "remove_instrumentation_breakpoint"], [21, 5, 1, "", "set_instrumentation_breakpoint"]], "nodriver.cdp.extensions": [[22, 0, 1, "", "StorageArea"], [22, 5, 1, "", "clear_storage_items"], [22, 5, 1, "", "get_storage_items"], [22, 5, 1, "", "load_unpacked"], [22, 5, 1, "", "remove_storage_items"], [22, 5, 1, "", "set_storage_items"], [22, 5, 1, "", "uninstall"]], "nodriver.cdp.extensions.StorageArea": [[22, 1, 1, "", "LOCAL"], [22, 1, 1, "", "MANAGED"], [22, 1, 1, "", "SESSION"], [22, 1, 1, "", "SYNC"]], "nodriver.cdp.fed_cm": [[23, 0, 1, "", "Account"], [23, 0, 1, "", "AccountUrlType"], [23, 0, 1, "", "DialogButton"], [23, 0, 1, "", "DialogClosed"], [23, 0, 1, "", "DialogShown"], [23, 0, 1, "", "DialogType"], [23, 0, 1, "", "LoginState"], [23, 5, 1, "", "click_dialog_button"], [23, 5, 1, "", "disable"], [23, 5, 1, "", "dismiss_dialog"], [23, 5, 1, "", "enable"], [23, 5, 1, "", "open_url"], [23, 5, 1, "", "reset_cooldown"], [23, 5, 1, "", "select_account"]], "nodriver.cdp.fed_cm.Account": [[23, 1, 1, "", "account_id"], [23, 1, 1, "", "email"], [23, 1, 1, "", "given_name"], [23, 1, 1, "", "idp_config_url"], [23, 1, 1, "", "idp_login_url"], [23, 1, 1, "", "login_state"], [23, 1, 1, "", "name"], [23, 1, 1, "", "picture_url"], [23, 1, 1, "", "privacy_policy_url"], [23, 1, 1, "", "terms_of_service_url"]], "nodriver.cdp.fed_cm.AccountUrlType": [[23, 1, 1, "", "PRIVACY_POLICY"], [23, 1, 1, "", "TERMS_OF_SERVICE"]], "nodriver.cdp.fed_cm.DialogButton": [[23, 1, 1, "", "CONFIRM_IDP_LOGIN_CONTINUE"], [23, 1, 1, "", "ERROR_GOT_IT"], [23, 1, 1, "", "ERROR_MORE_DETAILS"]], "nodriver.cdp.fed_cm.DialogClosed": [[23, 1, 1, "", "dialog_id"]], "nodriver.cdp.fed_cm.DialogShown": [[23, 1, 1, "", "accounts"], [23, 1, 1, "", "dialog_id"], [23, 1, 1, "", "dialog_type"], [23, 1, 1, "", "subtitle"], [23, 1, 1, "", "title"]], "nodriver.cdp.fed_cm.DialogType": [[23, 1, 1, "", "ACCOUNT_CHOOSER"], [23, 1, 1, "", "AUTO_REAUTHN"], [23, 1, 1, "", "CONFIRM_IDP_LOGIN"], [23, 1, 1, "", "ERROR"]], "nodriver.cdp.fed_cm.LoginState": [[23, 1, 1, "", "SIGN_IN"], [23, 1, 1, "", "SIGN_UP"]], "nodriver.cdp.fetch": [[24, 0, 1, "", "AuthChallenge"], [24, 0, 1, "", "AuthChallengeResponse"], [24, 0, 1, "", "AuthRequired"], [24, 0, 1, "", "HeaderEntry"], [24, 0, 1, "", "RequestId"], [24, 0, 1, "", "RequestPattern"], [24, 0, 1, "", "RequestPaused"], [24, 0, 1, "", "RequestStage"], [24, 5, 1, "", "continue_request"], [24, 5, 1, "", "continue_response"], [24, 5, 1, "", "continue_with_auth"], [24, 5, 1, "", "disable"], [24, 5, 1, "", "enable"], [24, 5, 1, "", "fail_request"], [24, 5, 1, "", "fulfill_request"], [24, 5, 1, "", "get_response_body"], [24, 5, 1, "", "take_response_body_as_stream"]], "nodriver.cdp.fetch.AuthChallenge": [[24, 1, 1, "", "origin"], [24, 1, 1, "", "realm"], [24, 1, 1, "", "scheme"], [24, 1, 1, "", "source"]], "nodriver.cdp.fetch.AuthChallengeResponse": [[24, 1, 1, "", "password"], [24, 1, 1, "", "response"], [24, 1, 1, "", "username"]], "nodriver.cdp.fetch.AuthRequired": [[24, 1, 1, "", "auth_challenge"], [24, 1, 1, "", "frame_id"], [24, 1, 1, "", "request"], [24, 1, 1, "", "request_id"], [24, 1, 1, "", "resource_type"]], "nodriver.cdp.fetch.HeaderEntry": [[24, 1, 1, "", "name"], [24, 1, 1, "", "value"]], "nodriver.cdp.fetch.RequestPattern": [[24, 1, 1, "", "request_stage"], [24, 1, 1, "", "resource_type"], [24, 1, 1, "", "url_pattern"]], "nodriver.cdp.fetch.RequestPaused": [[24, 1, 1, "", "frame_id"], [24, 1, 1, "", "network_id"], [24, 1, 1, "", "redirected_request_id"], [24, 1, 1, "", "request"], [24, 1, 1, "", "request_id"], [24, 1, 1, "", "resource_type"], [24, 1, 1, "", "response_error_reason"], [24, 1, 1, "", "response_headers"], [24, 1, 1, "", "response_status_code"], [24, 1, 1, "", "response_status_text"]], "nodriver.cdp.fetch.RequestStage": [[24, 1, 1, "", "REQUEST"], [24, 1, 1, "", "RESPONSE"]], "nodriver.cdp.file_system": [[25, 0, 1, "", "BucketFileSystemLocator"], [25, 0, 1, "", "Directory"], [25, 0, 1, "", "File"], [25, 5, 1, "", "get_directory"]], "nodriver.cdp.file_system.BucketFileSystemLocator": [[25, 1, 1, "", "bucket_name"], [25, 1, 1, "", "path_components"], [25, 1, 1, "", "storage_key"]], "nodriver.cdp.file_system.Directory": [[25, 1, 1, "", "name"], [25, 1, 1, "", "nested_directories"], [25, 1, 1, "", "nested_files"]], "nodriver.cdp.file_system.File": [[25, 1, 1, "", "last_modified"], [25, 1, 1, "", "name"], [25, 1, 1, "", "size"], [25, 1, 1, "", "type_"]], "nodriver.cdp.headless_experimental": [[26, 0, 1, "", "ScreenshotParams"], [26, 5, 1, "", "begin_frame"], [26, 5, 1, "", "disable"], [26, 5, 1, "", "enable"]], "nodriver.cdp.headless_experimental.ScreenshotParams": [[26, 1, 1, "", "format_"], [26, 1, 1, "", "optimize_for_speed"], [26, 1, 1, "", "quality"]], "nodriver.cdp.heap_profiler": [[27, 0, 1, "", "AddHeapSnapshotChunk"], [27, 0, 1, "", "HeapSnapshotObjectId"], [27, 0, 1, "", "HeapStatsUpdate"], [27, 0, 1, "", "LastSeenObjectId"], [27, 0, 1, "", "ReportHeapSnapshotProgress"], [27, 0, 1, "", "ResetProfiles"], [27, 0, 1, "", "SamplingHeapProfile"], [27, 0, 1, "", "SamplingHeapProfileNode"], [27, 0, 1, "", "SamplingHeapProfileSample"], [27, 5, 1, "", "add_inspected_heap_object"], [27, 5, 1, "", "collect_garbage"], [27, 5, 1, "", "disable"], [27, 5, 1, "", "enable"], [27, 5, 1, "", "get_heap_object_id"], [27, 5, 1, "", "get_object_by_heap_object_id"], [27, 5, 1, "", "get_sampling_profile"], [27, 5, 1, "", "start_sampling"], [27, 5, 1, "", "start_tracking_heap_objects"], [27, 5, 1, "", "stop_sampling"], [27, 5, 1, "", "stop_tracking_heap_objects"], [27, 5, 1, "", "take_heap_snapshot"]], "nodriver.cdp.heap_profiler.AddHeapSnapshotChunk": [[27, 1, 1, "", "chunk"]], "nodriver.cdp.heap_profiler.HeapStatsUpdate": [[27, 1, 1, "", "stats_update"]], "nodriver.cdp.heap_profiler.LastSeenObjectId": [[27, 1, 1, "", "last_seen_object_id"], [27, 1, 1, "", "timestamp"]], "nodriver.cdp.heap_profiler.ReportHeapSnapshotProgress": [[27, 1, 1, "", "done"], [27, 1, 1, "", "finished"], [27, 1, 1, "", "total"]], "nodriver.cdp.heap_profiler.SamplingHeapProfile": [[27, 1, 1, "", "head"], [27, 1, 1, "", "samples"]], "nodriver.cdp.heap_profiler.SamplingHeapProfileNode": [[27, 1, 1, "", "call_frame"], [27, 1, 1, "", "children"], [27, 1, 1, "", "id_"], [27, 1, 1, "", "self_size"]], "nodriver.cdp.heap_profiler.SamplingHeapProfileSample": [[27, 1, 1, "", "node_id"], [27, 1, 1, "", "ordinal"], [27, 1, 1, "", "size"]], "nodriver.cdp.indexed_db": [[28, 0, 1, "", "DataEntry"], [28, 0, 1, "", "DatabaseWithObjectStores"], [28, 0, 1, "", "Key"], [28, 0, 1, "", "KeyPath"], [28, 0, 1, "", "KeyRange"], [28, 0, 1, "", "ObjectStore"], [28, 0, 1, "", "ObjectStoreIndex"], [28, 5, 1, "", "clear_object_store"], [28, 5, 1, "", "delete_database"], [28, 5, 1, "", "delete_object_store_entries"], [28, 5, 1, "", "disable"], [28, 5, 1, "", "enable"], [28, 5, 1, "", "get_metadata"], [28, 5, 1, "", "request_data"], [28, 5, 1, "", "request_database"], [28, 5, 1, "", "request_database_names"]], "nodriver.cdp.indexed_db.DataEntry": [[28, 1, 1, "", "key"], [28, 1, 1, "", "primary_key"], [28, 1, 1, "", "value"]], "nodriver.cdp.indexed_db.DatabaseWithObjectStores": [[28, 1, 1, "", "name"], [28, 1, 1, "", "object_stores"], [28, 1, 1, "", "version"]], "nodriver.cdp.indexed_db.Key": [[28, 1, 1, "", "array"], [28, 1, 1, "", "date"], [28, 1, 1, "", "number"], [28, 1, 1, "", "string"], [28, 1, 1, "", "type_"]], "nodriver.cdp.indexed_db.KeyPath": [[28, 1, 1, "", "array"], [28, 1, 1, "", "string"], [28, 1, 1, "", "type_"]], "nodriver.cdp.indexed_db.KeyRange": [[28, 1, 1, "", "lower"], [28, 1, 1, "", "lower_open"], [28, 1, 1, "", "upper"], [28, 1, 1, "", "upper_open"]], "nodriver.cdp.indexed_db.ObjectStore": [[28, 1, 1, "", "auto_increment"], [28, 1, 1, "", "indexes"], [28, 1, 1, "", "key_path"], [28, 1, 1, "", "name"]], "nodriver.cdp.indexed_db.ObjectStoreIndex": [[28, 1, 1, "", "key_path"], [28, 1, 1, "", "multi_entry"], [28, 1, 1, "", "name"], [28, 1, 1, "", "unique"]], "nodriver.cdp.input_": [[29, 0, 1, "", "DragData"], [29, 0, 1, "", "DragDataItem"], [29, 0, 1, "", "DragIntercepted"], [29, 0, 1, "", "GestureSourceType"], [29, 0, 1, "", "MouseButton"], [29, 0, 1, "", "TimeSinceEpoch"], [29, 0, 1, "", "TouchPoint"], [29, 5, 1, "", "cancel_dragging"], [29, 5, 1, "", "dispatch_drag_event"], [29, 5, 1, "", "dispatch_key_event"], [29, 5, 1, "", "dispatch_mouse_event"], [29, 5, 1, "", "dispatch_touch_event"], [29, 5, 1, "", "emulate_touch_from_mouse_event"], [29, 5, 1, "", "ime_set_composition"], [29, 5, 1, "", "insert_text"], [29, 5, 1, "", "set_ignore_input_events"], [29, 5, 1, "", "set_intercept_drags"], [29, 5, 1, "", "synthesize_pinch_gesture"], [29, 5, 1, "", "synthesize_scroll_gesture"], [29, 5, 1, "", "synthesize_tap_gesture"]], "nodriver.cdp.input_.DragData": [[29, 1, 1, "", "drag_operations_mask"], [29, 1, 1, "", "files"], [29, 1, 1, "", "items"]], "nodriver.cdp.input_.DragDataItem": [[29, 1, 1, "", "base_url"], [29, 1, 1, "", "data"], [29, 1, 1, "", "mime_type"], [29, 1, 1, "", "title"]], "nodriver.cdp.input_.DragIntercepted": [[29, 1, 1, "", "data"]], "nodriver.cdp.input_.GestureSourceType": [[29, 1, 1, "", "DEFAULT"], [29, 1, 1, "", "MOUSE"], [29, 1, 1, "", "TOUCH"]], "nodriver.cdp.input_.MouseButton": [[29, 1, 1, "", "BACK"], [29, 1, 1, "", "FORWARD"], [29, 1, 1, "", "LEFT"], [29, 1, 1, "", "MIDDLE"], [29, 1, 1, "", "NONE"], [29, 1, 1, "", "RIGHT"]], "nodriver.cdp.input_.TouchPoint": [[29, 1, 1, "", "force"], [29, 1, 1, "", "id_"], [29, 1, 1, "", "radius_x"], [29, 1, 1, "", "radius_y"], [29, 1, 1, "", "rotation_angle"], [29, 1, 1, "", "tangential_pressure"], [29, 1, 1, "", "tilt_x"], [29, 1, 1, "", "tilt_y"], [29, 1, 1, "", "twist"], [29, 1, 1, "", "x"], [29, 1, 1, "", "y"]], "nodriver.cdp.inspector": [[30, 0, 1, "", "Detached"], [30, 0, 1, "", "TargetCrashed"], [30, 0, 1, "", "TargetReloadedAfterCrash"], [30, 5, 1, "", "disable"], [30, 5, 1, "", "enable"]], "nodriver.cdp.inspector.Detached": [[30, 1, 1, "", "reason"]], "nodriver.cdp.io": [[31, 0, 1, "", "StreamHandle"], [31, 5, 1, "", "close"], [31, 5, 1, "", "read"], [31, 5, 1, "", "resolve_blob"]], "nodriver.cdp.layer_tree": [[32, 0, 1, "", "Layer"], [32, 0, 1, "", "LayerId"], [32, 0, 1, "", "LayerPainted"], [32, 0, 1, "", "LayerTreeDidChange"], [32, 0, 1, "", "PaintProfile"], [32, 0, 1, "", "PictureTile"], [32, 0, 1, "", "ScrollRect"], [32, 0, 1, "", "SnapshotId"], [32, 0, 1, "", "StickyPositionConstraint"], [32, 5, 1, "", "compositing_reasons"], [32, 5, 1, "", "disable"], [32, 5, 1, "", "enable"], [32, 5, 1, "", "load_snapshot"], [32, 5, 1, "", "make_snapshot"], [32, 5, 1, "", "profile_snapshot"], [32, 5, 1, "", "release_snapshot"], [32, 5, 1, "", "replay_snapshot"], [32, 5, 1, "", "snapshot_command_log"]], "nodriver.cdp.layer_tree.Layer": [[32, 1, 1, "", "anchor_x"], [32, 1, 1, "", "anchor_y"], [32, 1, 1, "", "anchor_z"], [32, 1, 1, "", "backend_node_id"], [32, 1, 1, "", "draws_content"], [32, 1, 1, "", "height"], [32, 1, 1, "", "invisible"], [32, 1, 1, "", "layer_id"], [32, 1, 1, "", "offset_x"], [32, 1, 1, "", "offset_y"], [32, 1, 1, "", "paint_count"], [32, 1, 1, "", "parent_layer_id"], [32, 1, 1, "", "scroll_rects"], [32, 1, 1, "", "sticky_position_constraint"], [32, 1, 1, "", "transform"], [32, 1, 1, "", "width"]], "nodriver.cdp.layer_tree.LayerPainted": [[32, 1, 1, "", "clip"], [32, 1, 1, "", "layer_id"]], "nodriver.cdp.layer_tree.LayerTreeDidChange": [[32, 1, 1, "", "layers"]], "nodriver.cdp.layer_tree.PictureTile": [[32, 1, 1, "", "picture"], [32, 1, 1, "", "x"], [32, 1, 1, "", "y"]], "nodriver.cdp.layer_tree.ScrollRect": [[32, 1, 1, "", "rect"], [32, 1, 1, "", "type_"]], "nodriver.cdp.layer_tree.StickyPositionConstraint": [[32, 1, 1, "", "containing_block_rect"], [32, 1, 1, "", "nearest_layer_shifting_containing_block"], [32, 1, 1, "", "nearest_layer_shifting_sticky_box"], [32, 1, 1, "", "sticky_box_rect"]], "nodriver.cdp.log": [[33, 0, 1, "", "EntryAdded"], [33, 0, 1, "", "LogEntry"], [33, 0, 1, "", "ViolationSetting"], [33, 5, 1, "", "clear"], [33, 5, 1, "", "disable"], [33, 5, 1, "", "enable"], [33, 5, 1, "", "start_violations_report"], [33, 5, 1, "", "stop_violations_report"]], "nodriver.cdp.log.EntryAdded": [[33, 1, 1, "", "entry"]], "nodriver.cdp.log.LogEntry": [[33, 1, 1, "", "args"], [33, 1, 1, "", "category"], [33, 1, 1, "", "level"], [33, 1, 1, "", "line_number"], [33, 1, 1, "", "network_request_id"], [33, 1, 1, "", "source"], [33, 1, 1, "", "stack_trace"], [33, 1, 1, "", "text"], [33, 1, 1, "", "timestamp"], [33, 1, 1, "", "url"], [33, 1, 1, "", "worker_id"]], "nodriver.cdp.log.ViolationSetting": [[33, 1, 1, "", "name"], [33, 1, 1, "", "threshold"]], "nodriver.cdp.media": [[34, 0, 1, "", "PlayerError"], [34, 0, 1, "", "PlayerErrorSourceLocation"], [34, 0, 1, "", "PlayerErrorsRaised"], [34, 0, 1, "", "PlayerEvent"], [34, 0, 1, "", "PlayerEventsAdded"], [34, 0, 1, "", "PlayerId"], [34, 0, 1, "", "PlayerMessage"], [34, 0, 1, "", "PlayerMessagesLogged"], [34, 0, 1, "", "PlayerPropertiesChanged"], [34, 0, 1, "", "PlayerProperty"], [34, 0, 1, "", "PlayersCreated"], [34, 0, 1, "", "Timestamp"], [34, 5, 1, "", "disable"], [34, 5, 1, "", "enable"]], "nodriver.cdp.media.PlayerError": [[34, 1, 1, "", "cause"], [34, 1, 1, "", "code"], [34, 1, 1, "", "data"], [34, 1, 1, "", "error_type"], [34, 1, 1, "", "stack"]], "nodriver.cdp.media.PlayerErrorSourceLocation": [[34, 1, 1, "", "file"], [34, 1, 1, "", "line"]], "nodriver.cdp.media.PlayerErrorsRaised": [[34, 1, 1, "", "errors"], [34, 1, 1, "", "player_id"]], "nodriver.cdp.media.PlayerEvent": [[34, 1, 1, "", "timestamp"], [34, 1, 1, "", "value"]], "nodriver.cdp.media.PlayerEventsAdded": [[34, 1, 1, "", "events"], [34, 1, 1, "", "player_id"]], "nodriver.cdp.media.PlayerMessage": [[34, 1, 1, "", "level"], [34, 1, 1, "", "message"]], "nodriver.cdp.media.PlayerMessagesLogged": [[34, 1, 1, "", "messages"], [34, 1, 1, "", "player_id"]], "nodriver.cdp.media.PlayerPropertiesChanged": [[34, 1, 1, "", "player_id"], [34, 1, 1, "", "properties"]], "nodriver.cdp.media.PlayerProperty": [[34, 1, 1, "", "name"], [34, 1, 1, "", "value"]], "nodriver.cdp.media.PlayersCreated": [[34, 1, 1, "", "players"]], "nodriver.cdp.memory": [[35, 0, 1, "", "DOMCounter"], [35, 0, 1, "", "Module"], [35, 0, 1, "", "PressureLevel"], [35, 0, 1, "", "SamplingProfile"], [35, 0, 1, "", "SamplingProfileNode"], [35, 5, 1, "", "forcibly_purge_java_script_memory"], [35, 5, 1, "", "get_all_time_sampling_profile"], [35, 5, 1, "", "get_browser_sampling_profile"], [35, 5, 1, "", "get_dom_counters"], [35, 5, 1, "", "get_dom_counters_for_leak_detection"], [35, 5, 1, "", "get_sampling_profile"], [35, 5, 1, "", "prepare_for_leak_detection"], [35, 5, 1, "", "set_pressure_notifications_suppressed"], [35, 5, 1, "", "simulate_pressure_notification"], [35, 5, 1, "", "start_sampling"], [35, 5, 1, "", "stop_sampling"]], "nodriver.cdp.memory.DOMCounter": [[35, 1, 1, "", "count"], [35, 1, 1, "", "name"]], "nodriver.cdp.memory.Module": [[35, 1, 1, "", "base_address"], [35, 1, 1, "", "name"], [35, 1, 1, "", "size"], [35, 1, 1, "", "uuid"]], "nodriver.cdp.memory.PressureLevel": [[35, 1, 1, "", "CRITICAL"], [35, 1, 1, "", "MODERATE"]], "nodriver.cdp.memory.SamplingProfile": [[35, 1, 1, "", "modules"], [35, 1, 1, "", "samples"]], "nodriver.cdp.memory.SamplingProfileNode": [[35, 1, 1, "", "size"], [35, 1, 1, "", "stack"], [35, 1, 1, "", "total"]], "nodriver.cdp.network": [[36, 0, 1, "", "AlternateProtocolUsage"], [36, 0, 1, "", "AssociatedCookie"], [36, 0, 1, "", "AuthChallenge"], [36, 0, 1, "", "AuthChallengeResponse"], [36, 0, 1, "", "BlockedReason"], [36, 0, 1, "", "BlockedSetCookieWithReason"], [36, 0, 1, "", "CachedResource"], [36, 0, 1, "", "CertificateTransparencyCompliance"], [36, 0, 1, "", "ClientSecurityState"], [36, 0, 1, "", "ConnectTiming"], [36, 0, 1, "", "ConnectionType"], [36, 0, 1, "", "ContentEncoding"], [36, 0, 1, "", "ContentSecurityPolicySource"], [36, 0, 1, "", "ContentSecurityPolicyStatus"], [36, 0, 1, "", "Cookie"], [36, 0, 1, "", "CookieBlockedReason"], [36, 0, 1, "", "CookieExemptionReason"], [36, 0, 1, "", "CookieParam"], [36, 0, 1, "", "CookiePartitionKey"], [36, 0, 1, "", "CookiePriority"], [36, 0, 1, "", "CookieSameSite"], [36, 0, 1, "", "CookieSourceScheme"], [36, 0, 1, "", "CorsError"], [36, 0, 1, "", "CorsErrorStatus"], [36, 0, 1, "", "CrossOriginEmbedderPolicyStatus"], [36, 0, 1, "", "CrossOriginEmbedderPolicyValue"], [36, 0, 1, "", "CrossOriginOpenerPolicyStatus"], [36, 0, 1, "", "CrossOriginOpenerPolicyValue"], [36, 0, 1, "", "DataReceived"], [36, 0, 1, "", "DirectSocketDnsQueryType"], [36, 0, 1, "", "DirectTCPSocketAborted"], [36, 0, 1, "", "DirectTCPSocketClosed"], [36, 0, 1, "", "DirectTCPSocketCreated"], [36, 0, 1, "", "DirectTCPSocketOpened"], [36, 0, 1, "", "DirectTCPSocketOptions"], [36, 0, 1, "", "ErrorReason"], [36, 0, 1, "", "EventSourceMessageReceived"], [36, 0, 1, "", "ExemptedSetCookieWithReason"], [36, 0, 1, "", "Headers"], [36, 0, 1, "", "IPAddressSpace"], [36, 0, 1, "", "Initiator"], [36, 0, 1, "", "InterceptionId"], [36, 0, 1, "", "InterceptionStage"], [36, 0, 1, "", "LoadNetworkResourceOptions"], [36, 0, 1, "", "LoadNetworkResourcePageResult"], [36, 0, 1, "", "LoaderId"], [36, 0, 1, "", "LoadingFailed"], [36, 0, 1, "", "LoadingFinished"], [36, 0, 1, "", "MonotonicTime"], [36, 0, 1, "", "PolicyUpdated"], [36, 0, 1, "", "PostDataEntry"], [36, 0, 1, "", "PrivateNetworkRequestPolicy"], [36, 0, 1, "", "ReportId"], [36, 0, 1, "", "ReportStatus"], [36, 0, 1, "", "ReportingApiEndpoint"], [36, 0, 1, "", "ReportingApiEndpointsChangedForOrigin"], [36, 0, 1, "", "ReportingApiReport"], [36, 0, 1, "", "ReportingApiReportAdded"], [36, 0, 1, "", "ReportingApiReportUpdated"], [36, 0, 1, "", "Request"], [36, 0, 1, "", "RequestId"], [36, 0, 1, "", "RequestIntercepted"], [36, 0, 1, "", "RequestPattern"], [36, 0, 1, "", "RequestServedFromCache"], [36, 0, 1, "", "RequestWillBeSent"], [36, 0, 1, "", "RequestWillBeSentExtraInfo"], [36, 0, 1, "", "ResourceChangedPriority"], [36, 0, 1, "", "ResourcePriority"], [36, 0, 1, "", "ResourceTiming"], [36, 0, 1, "", "ResourceType"], [36, 0, 1, "", "Response"], [36, 0, 1, "", "ResponseReceived"], [36, 0, 1, "", "ResponseReceivedEarlyHints"], [36, 0, 1, "", "ResponseReceivedExtraInfo"], [36, 0, 1, "", "SecurityDetails"], [36, 0, 1, "", "SecurityIsolationStatus"], [36, 0, 1, "", "ServiceWorkerResponseSource"], [36, 0, 1, "", "ServiceWorkerRouterInfo"], [36, 0, 1, "", "ServiceWorkerRouterSource"], [36, 0, 1, "", "SetCookieBlockedReason"], [36, 0, 1, "", "SignedCertificateTimestamp"], [36, 0, 1, "", "SignedExchangeError"], [36, 0, 1, "", "SignedExchangeErrorField"], [36, 0, 1, "", "SignedExchangeHeader"], [36, 0, 1, "", "SignedExchangeInfo"], [36, 0, 1, "", "SignedExchangeReceived"], [36, 0, 1, "", "SignedExchangeSignature"], [36, 0, 1, "", "SubresourceWebBundleInnerResponseError"], [36, 0, 1, "", "SubresourceWebBundleInnerResponseParsed"], [36, 0, 1, "", "SubresourceWebBundleMetadataError"], [36, 0, 1, "", "SubresourceWebBundleMetadataReceived"], [36, 0, 1, "", "TimeSinceEpoch"], [36, 0, 1, "", "TrustTokenOperationDone"], [36, 0, 1, "", "TrustTokenOperationType"], [36, 0, 1, "", "TrustTokenParams"], [36, 0, 1, "", "WebSocketClosed"], [36, 0, 1, "", "WebSocketCreated"], [36, 0, 1, "", "WebSocketFrame"], [36, 0, 1, "", "WebSocketFrameError"], [36, 0, 1, "", "WebSocketFrameReceived"], [36, 0, 1, "", "WebSocketFrameSent"], [36, 0, 1, "", "WebSocketHandshakeResponseReceived"], [36, 0, 1, "", "WebSocketRequest"], [36, 0, 1, "", "WebSocketResponse"], [36, 0, 1, "", "WebSocketWillSendHandshakeRequest"], [36, 0, 1, "", "WebTransportClosed"], [36, 0, 1, "", "WebTransportConnectionEstablished"], [36, 0, 1, "", "WebTransportCreated"], [36, 5, 1, "", "can_clear_browser_cache"], [36, 5, 1, "", "can_clear_browser_cookies"], [36, 5, 1, "", "can_emulate_network_conditions"], [36, 5, 1, "", "clear_accepted_encodings_override"], [36, 5, 1, "", "clear_browser_cache"], [36, 5, 1, "", "clear_browser_cookies"], [36, 5, 1, "", "continue_intercepted_request"], [36, 5, 1, "", "delete_cookies"], [36, 5, 1, "", "disable"], [36, 5, 1, "", "emulate_network_conditions"], [36, 5, 1, "", "enable"], [36, 5, 1, "", "enable_reporting_api"], [36, 5, 1, "", "get_all_cookies"], [36, 5, 1, "", "get_certificate"], [36, 5, 1, "", "get_cookies"], [36, 5, 1, "", "get_request_post_data"], [36, 5, 1, "", "get_response_body"], [36, 5, 1, "", "get_response_body_for_interception"], [36, 5, 1, "", "get_security_isolation_status"], [36, 5, 1, "", "load_network_resource"], [36, 5, 1, "", "replay_xhr"], [36, 5, 1, "", "search_in_response_body"], [36, 5, 1, "", "set_accepted_encodings"], [36, 5, 1, "", "set_attach_debug_stack"], [36, 5, 1, "", "set_blocked_ur_ls"], [36, 5, 1, "", "set_bypass_service_worker"], [36, 5, 1, "", "set_cache_disabled"], [36, 5, 1, "", "set_cookie"], [36, 5, 1, "", "set_cookie_controls"], [36, 5, 1, "", "set_cookies"], [36, 5, 1, "", "set_extra_http_headers"], [36, 5, 1, "", "set_request_interception"], [36, 5, 1, "", "set_user_agent_override"], [36, 5, 1, "", "stream_resource_content"], [36, 5, 1, "", "take_response_body_for_interception_as_stream"]], "nodriver.cdp.network.AlternateProtocolUsage": [[36, 1, 1, "", "ALTERNATIVE_JOB_WON_RACE"], [36, 1, 1, "", "ALTERNATIVE_JOB_WON_WITHOUT_RACE"], [36, 1, 1, "", "BROKEN"], [36, 1, 1, "", "DNS_ALPN_H3_JOB_WON_RACE"], [36, 1, 1, "", "DNS_ALPN_H3_JOB_WON_WITHOUT_RACE"], [36, 1, 1, "", "MAIN_JOB_WON_RACE"], [36, 1, 1, "", "MAPPING_MISSING"], [36, 1, 1, "", "UNSPECIFIED_REASON"]], "nodriver.cdp.network.AssociatedCookie": [[36, 1, 1, "", "blocked_reasons"], [36, 1, 1, "", "cookie"], [36, 1, 1, "", "exemption_reason"]], "nodriver.cdp.network.AuthChallenge": [[36, 1, 1, "", "origin"], [36, 1, 1, "", "realm"], [36, 1, 1, "", "scheme"], [36, 1, 1, "", "source"]], "nodriver.cdp.network.AuthChallengeResponse": [[36, 1, 1, "", "password"], [36, 1, 1, "", "response"], [36, 1, 1, "", "username"]], "nodriver.cdp.network.BlockedReason": [[36, 1, 1, "", "COEP_FRAME_RESOURCE_NEEDS_COEP_HEADER"], [36, 1, 1, "", "CONTENT_TYPE"], [36, 1, 1, "", "COOP_SANDBOXED_IFRAME_CANNOT_NAVIGATE_TO_COOP_PAGE"], [36, 1, 1, "", "CORP_NOT_SAME_ORIGIN"], [36, 1, 1, "", "CORP_NOT_SAME_ORIGIN_AFTER_DEFAULTED_TO_SAME_ORIGIN_BY_COEP"], [36, 1, 1, "", "CORP_NOT_SAME_ORIGIN_AFTER_DEFAULTED_TO_SAME_ORIGIN_BY_COEP_AND_DIP"], [36, 1, 1, "", "CORP_NOT_SAME_ORIGIN_AFTER_DEFAULTED_TO_SAME_ORIGIN_BY_DIP"], [36, 1, 1, "", "CORP_NOT_SAME_SITE"], [36, 1, 1, "", "CSP"], [36, 1, 1, "", "INSPECTOR"], [36, 1, 1, "", "MIXED_CONTENT"], [36, 1, 1, "", "ORIGIN"], [36, 1, 1, "", "OTHER"], [36, 1, 1, "", "SRI_MESSAGE_SIGNATURE_MISMATCH"], [36, 1, 1, "", "SUBRESOURCE_FILTER"]], "nodriver.cdp.network.BlockedSetCookieWithReason": [[36, 1, 1, "", "blocked_reasons"], [36, 1, 1, "", "cookie"], [36, 1, 1, "", "cookie_line"]], "nodriver.cdp.network.CachedResource": [[36, 1, 1, "", "body_size"], [36, 1, 1, "", "response"], [36, 1, 1, "", "type_"], [36, 1, 1, "", "url"]], "nodriver.cdp.network.CertificateTransparencyCompliance": [[36, 1, 1, "", "COMPLIANT"], [36, 1, 1, "", "NOT_COMPLIANT"], [36, 1, 1, "", "UNKNOWN"]], "nodriver.cdp.network.ClientSecurityState": [[36, 1, 1, "", "initiator_ip_address_space"], [36, 1, 1, "", "initiator_is_secure_context"], [36, 1, 1, "", "private_network_request_policy"]], "nodriver.cdp.network.ConnectTiming": [[36, 1, 1, "", "request_time"]], "nodriver.cdp.network.ConnectionType": [[36, 1, 1, "", "BLUETOOTH"], [36, 1, 1, "", "CELLULAR2G"], [36, 1, 1, "", "CELLULAR3G"], [36, 1, 1, "", "CELLULAR4G"], [36, 1, 1, "", "ETHERNET"], [36, 1, 1, "", "NONE"], [36, 1, 1, "", "OTHER"], [36, 1, 1, "", "WIFI"], [36, 1, 1, "", "WIMAX"]], "nodriver.cdp.network.ContentEncoding": [[36, 1, 1, "", "BR"], [36, 1, 1, "", "DEFLATE"], [36, 1, 1, "", "GZIP"], [36, 1, 1, "", "ZSTD"]], "nodriver.cdp.network.ContentSecurityPolicySource": [[36, 1, 1, "", "HTTP"], [36, 1, 1, "", "META"]], "nodriver.cdp.network.ContentSecurityPolicyStatus": [[36, 1, 1, "", "effective_directives"], [36, 1, 1, "", "is_enforced"], [36, 1, 1, "", "source"]], "nodriver.cdp.network.Cookie": [[36, 1, 1, "", "domain"], [36, 1, 1, "", "expires"], [36, 1, 1, "", "http_only"], [36, 1, 1, "", "name"], [36, 1, 1, "", "partition_key"], [36, 1, 1, "", "partition_key_opaque"], [36, 1, 1, "", "path"], [36, 1, 1, "", "priority"], [36, 1, 1, "", "same_party"], [36, 1, 1, "", "same_site"], [36, 1, 1, "", "secure"], [36, 1, 1, "", "session"], [36, 1, 1, "", "size"], [36, 1, 1, "", "source_port"], [36, 1, 1, "", "source_scheme"], [36, 1, 1, "", "value"]], "nodriver.cdp.network.CookieBlockedReason": [[36, 1, 1, "", "DOMAIN_MISMATCH"], [36, 1, 1, "", "NAME_VALUE_PAIR_EXCEEDS_MAX_SIZE"], [36, 1, 1, "", "NOT_ON_PATH"], [36, 1, 1, "", "PORT_MISMATCH"], [36, 1, 1, "", "SAME_PARTY_FROM_CROSS_PARTY_CONTEXT"], [36, 1, 1, "", "SAME_SITE_LAX"], [36, 1, 1, "", "SAME_SITE_NONE_INSECURE"], [36, 1, 1, "", "SAME_SITE_STRICT"], [36, 1, 1, "", "SAME_SITE_UNSPECIFIED_TREATED_AS_LAX"], [36, 1, 1, "", "SCHEMEFUL_SAME_SITE_LAX"], [36, 1, 1, "", "SCHEMEFUL_SAME_SITE_STRICT"], [36, 1, 1, "", "SCHEMEFUL_SAME_SITE_UNSPECIFIED_TREATED_AS_LAX"], [36, 1, 1, "", "SCHEME_MISMATCH"], [36, 1, 1, "", "SECURE_ONLY"], [36, 1, 1, "", "THIRD_PARTY_BLOCKED_IN_FIRST_PARTY_SET"], [36, 1, 1, "", "THIRD_PARTY_PHASEOUT"], [36, 1, 1, "", "UNKNOWN_ERROR"], [36, 1, 1, "", "USER_PREFERENCES"]], "nodriver.cdp.network.CookieExemptionReason": [[36, 1, 1, "", "ENTERPRISE_POLICY"], [36, 1, 1, "", "NONE"], [36, 1, 1, "", "SAME_SITE_NONE_COOKIES_IN_SANDBOX"], [36, 1, 1, "", "SCHEME"], [36, 1, 1, "", "STORAGE_ACCESS"], [36, 1, 1, "", "TOP_LEVEL_STORAGE_ACCESS"], [36, 1, 1, "", "TOP_LEVEL_TPCD_DEPRECATION_TRIAL"], [36, 1, 1, "", "TPCD_DEPRECATION_TRIAL"], [36, 1, 1, "", "TPCD_HEURISTICS"], [36, 1, 1, "", "TPCD_METADATA"], [36, 1, 1, "", "USER_SETTING"]], "nodriver.cdp.network.CookieParam": [[36, 1, 1, "", "domain"], [36, 1, 1, "", "expires"], [36, 1, 1, "", "http_only"], [36, 1, 1, "", "name"], [36, 1, 1, "", "partition_key"], [36, 1, 1, "", "path"], [36, 1, 1, "", "priority"], [36, 1, 1, "", "same_party"], [36, 1, 1, "", "same_site"], [36, 1, 1, "", "secure"], [36, 1, 1, "", "source_port"], [36, 1, 1, "", "source_scheme"], [36, 1, 1, "", "url"], [36, 1, 1, "", "value"]], "nodriver.cdp.network.CookiePartitionKey": [[36, 1, 1, "", "has_cross_site_ancestor"], [36, 1, 1, "", "top_level_site"]], "nodriver.cdp.network.CookiePriority": [[36, 1, 1, "", "HIGH"], [36, 1, 1, "", "LOW"], [36, 1, 1, "", "MEDIUM"]], "nodriver.cdp.network.CookieSameSite": [[36, 1, 1, "", "LAX"], [36, 1, 1, "", "NONE"], [36, 1, 1, "", "STRICT"]], "nodriver.cdp.network.CookieSourceScheme": [[36, 1, 1, "", "NON_SECURE"], [36, 1, 1, "", "SECURE"], [36, 1, 1, "", "UNSET"]], "nodriver.cdp.network.CorsError": [[36, 1, 1, "", "ALLOW_ORIGIN_MISMATCH"], [36, 1, 1, "", "CORS_DISABLED_SCHEME"], [36, 1, 1, "", "DISALLOWED_BY_MODE"], [36, 1, 1, "", "HEADER_DISALLOWED_BY_PREFLIGHT_RESPONSE"], [36, 1, 1, "", "INSECURE_PRIVATE_NETWORK"], [36, 1, 1, "", "INVALID_ALLOW_CREDENTIALS"], [36, 1, 1, "", "INVALID_ALLOW_HEADERS_PREFLIGHT_RESPONSE"], [36, 1, 1, "", "INVALID_ALLOW_METHODS_PREFLIGHT_RESPONSE"], [36, 1, 1, "", "INVALID_ALLOW_ORIGIN_VALUE"], [36, 1, 1, "", "INVALID_PRIVATE_NETWORK_ACCESS"], [36, 1, 1, "", "INVALID_RESPONSE"], [36, 1, 1, "", "LOCAL_NETWORK_ACCESS_PERMISSION_DENIED"], [36, 1, 1, "", "METHOD_DISALLOWED_BY_PREFLIGHT_RESPONSE"], [36, 1, 1, "", "MISSING_ALLOW_ORIGIN_HEADER"], [36, 1, 1, "", "MULTIPLE_ALLOW_ORIGIN_VALUES"], [36, 1, 1, "", "NO_CORS_REDIRECT_MODE_NOT_FOLLOW"], [36, 1, 1, "", "PREFLIGHT_ALLOW_ORIGIN_MISMATCH"], [36, 1, 1, "", "PREFLIGHT_DISALLOWED_REDIRECT"], [36, 1, 1, "", "PREFLIGHT_INVALID_ALLOW_CREDENTIALS"], [36, 1, 1, "", "PREFLIGHT_INVALID_ALLOW_EXTERNAL"], [36, 1, 1, "", "PREFLIGHT_INVALID_ALLOW_ORIGIN_VALUE"], [36, 1, 1, "", "PREFLIGHT_INVALID_ALLOW_PRIVATE_NETWORK"], [36, 1, 1, "", "PREFLIGHT_INVALID_STATUS"], [36, 1, 1, "", "PREFLIGHT_MISSING_ALLOW_EXTERNAL"], [36, 1, 1, "", "PREFLIGHT_MISSING_ALLOW_ORIGIN_HEADER"], [36, 1, 1, "", "PREFLIGHT_MISSING_ALLOW_PRIVATE_NETWORK"], [36, 1, 1, "", "PREFLIGHT_MISSING_PRIVATE_NETWORK_ACCESS_ID"], [36, 1, 1, "", "PREFLIGHT_MISSING_PRIVATE_NETWORK_ACCESS_NAME"], [36, 1, 1, "", "PREFLIGHT_MULTIPLE_ALLOW_ORIGIN_VALUES"], [36, 1, 1, "", "PREFLIGHT_WILDCARD_ORIGIN_NOT_ALLOWED"], [36, 1, 1, "", "PRIVATE_NETWORK_ACCESS_PERMISSION_DENIED"], [36, 1, 1, "", "PRIVATE_NETWORK_ACCESS_PERMISSION_UNAVAILABLE"], [36, 1, 1, "", "REDIRECT_CONTAINS_CREDENTIALS"], [36, 1, 1, "", "UNEXPECTED_PRIVATE_NETWORK_ACCESS"], [36, 1, 1, "", "WILDCARD_ORIGIN_NOT_ALLOWED"]], "nodriver.cdp.network.CorsErrorStatus": [[36, 1, 1, "", "cors_error"], [36, 1, 1, "", "failed_parameter"]], "nodriver.cdp.network.CrossOriginEmbedderPolicyStatus": [[36, 1, 1, "", "report_only_reporting_endpoint"], [36, 1, 1, "", "report_only_value"], [36, 1, 1, "", "reporting_endpoint"], [36, 1, 1, "", "value"]], "nodriver.cdp.network.CrossOriginEmbedderPolicyValue": [[36, 1, 1, "", "CREDENTIALLESS"], [36, 1, 1, "", "NONE"], [36, 1, 1, "", "REQUIRE_CORP"]], "nodriver.cdp.network.CrossOriginOpenerPolicyStatus": [[36, 1, 1, "", "report_only_reporting_endpoint"], [36, 1, 1, "", "report_only_value"], [36, 1, 1, "", "reporting_endpoint"], [36, 1, 1, "", "value"]], "nodriver.cdp.network.CrossOriginOpenerPolicyValue": [[36, 1, 1, "", "NOOPENER_ALLOW_POPUPS"], [36, 1, 1, "", "RESTRICT_PROPERTIES"], [36, 1, 1, "", "RESTRICT_PROPERTIES_PLUS_COEP"], [36, 1, 1, "", "SAME_ORIGIN"], [36, 1, 1, "", "SAME_ORIGIN_ALLOW_POPUPS"], [36, 1, 1, "", "SAME_ORIGIN_PLUS_COEP"], [36, 1, 1, "", "UNSAFE_NONE"]], "nodriver.cdp.network.DataReceived": [[36, 1, 1, "", "data"], [36, 1, 1, "", "data_length"], [36, 1, 1, "", "encoded_data_length"], [36, 1, 1, "", "request_id"], [36, 1, 1, "", "timestamp"]], "nodriver.cdp.network.DirectSocketDnsQueryType": [[36, 1, 1, "", "IPV4"], [36, 1, 1, "", "IPV6"]], "nodriver.cdp.network.DirectTCPSocketAborted": [[36, 1, 1, "", "error_message"], [36, 1, 1, "", "identifier"], [36, 1, 1, "", "timestamp"]], "nodriver.cdp.network.DirectTCPSocketClosed": [[36, 1, 1, "", "identifier"], [36, 1, 1, "", "timestamp"]], "nodriver.cdp.network.DirectTCPSocketCreated": [[36, 1, 1, "", "identifier"], [36, 1, 1, "", "initiator"], [36, 1, 1, "", "options"], [36, 1, 1, "", "remote_addr"], [36, 1, 1, "", "remote_port"], [36, 1, 1, "", "timestamp"]], "nodriver.cdp.network.DirectTCPSocketOpened": [[36, 1, 1, "", "identifier"], [36, 1, 1, "", "local_addr"], [36, 1, 1, "", "local_port"], [36, 1, 1, "", "remote_addr"], [36, 1, 1, "", "remote_port"], [36, 1, 1, "", "timestamp"]], "nodriver.cdp.network.DirectTCPSocketOptions": [[36, 1, 1, "", "dns_query_type"], [36, 1, 1, "", "keep_alive_delay"], [36, 1, 1, "", "no_delay"], [36, 1, 1, "", "receive_buffer_size"], [36, 1, 1, "", "send_buffer_size"]], "nodriver.cdp.network.ErrorReason": [[36, 1, 1, "", "ABORTED"], [36, 1, 1, "", "ACCESS_DENIED"], [36, 1, 1, "", "ADDRESS_UNREACHABLE"], [36, 1, 1, "", "BLOCKED_BY_CLIENT"], [36, 1, 1, "", "BLOCKED_BY_RESPONSE"], [36, 1, 1, "", "CONNECTION_ABORTED"], [36, 1, 1, "", "CONNECTION_CLOSED"], [36, 1, 1, "", "CONNECTION_FAILED"], [36, 1, 1, "", "CONNECTION_REFUSED"], [36, 1, 1, "", "CONNECTION_RESET"], [36, 1, 1, "", "FAILED"], [36, 1, 1, "", "INTERNET_DISCONNECTED"], [36, 1, 1, "", "NAME_NOT_RESOLVED"], [36, 1, 1, "", "TIMED_OUT"]], "nodriver.cdp.network.EventSourceMessageReceived": [[36, 1, 1, "", "data"], [36, 1, 1, "", "event_id"], [36, 1, 1, "", "event_name"], [36, 1, 1, "", "request_id"], [36, 1, 1, "", "timestamp"]], "nodriver.cdp.network.ExemptedSetCookieWithReason": [[36, 1, 1, "", "cookie"], [36, 1, 1, "", "cookie_line"], [36, 1, 1, "", "exemption_reason"]], "nodriver.cdp.network.IPAddressSpace": [[36, 1, 1, "", "LOCAL"], [36, 1, 1, "", "PRIVATE"], [36, 1, 1, "", "PUBLIC"], [36, 1, 1, "", "UNKNOWN"]], "nodriver.cdp.network.Initiator": [[36, 1, 1, "", "column_number"], [36, 1, 1, "", "line_number"], [36, 1, 1, "", "request_id"], [36, 1, 1, "", "stack"], [36, 1, 1, "", "type_"], [36, 1, 1, "", "url"]], "nodriver.cdp.network.InterceptionStage": [[36, 1, 1, "", "HEADERS_RECEIVED"], [36, 1, 1, "", "REQUEST"]], "nodriver.cdp.network.LoadNetworkResourceOptions": [[36, 1, 1, "", "disable_cache"], [36, 1, 1, "", "include_credentials"]], "nodriver.cdp.network.LoadNetworkResourcePageResult": [[36, 1, 1, "", "headers"], [36, 1, 1, "", "http_status_code"], [36, 1, 1, "", "net_error"], [36, 1, 1, "", "net_error_name"], [36, 1, 1, "", "stream"], [36, 1, 1, "", "success"]], "nodriver.cdp.network.LoadingFailed": [[36, 1, 1, "", "blocked_reason"], [36, 1, 1, "", "canceled"], [36, 1, 1, "", "cors_error_status"], [36, 1, 1, "", "error_text"], [36, 1, 1, "", "request_id"], [36, 1, 1, "", "timestamp"], [36, 1, 1, "", "type_"]], "nodriver.cdp.network.LoadingFinished": [[36, 1, 1, "", "encoded_data_length"], [36, 1, 1, "", "request_id"], [36, 1, 1, "", "timestamp"]], "nodriver.cdp.network.PostDataEntry": [[36, 1, 1, "", "bytes_"]], "nodriver.cdp.network.PrivateNetworkRequestPolicy": [[36, 1, 1, "", "ALLOW"], [36, 1, 1, "", "BLOCK_FROM_INSECURE_TO_MORE_PRIVATE"], [36, 1, 1, "", "PERMISSION_BLOCK"], [36, 1, 1, "", "PERMISSION_WARN"], [36, 1, 1, "", "PREFLIGHT_BLOCK"], [36, 1, 1, "", "PREFLIGHT_WARN"], [36, 1, 1, "", "WARN_FROM_INSECURE_TO_MORE_PRIVATE"]], "nodriver.cdp.network.ReportStatus": [[36, 1, 1, "", "MARKED_FOR_REMOVAL"], [36, 1, 1, "", "PENDING"], [36, 1, 1, "", "QUEUED"], [36, 1, 1, "", "SUCCESS"]], "nodriver.cdp.network.ReportingApiEndpoint": [[36, 1, 1, "", "group_name"], [36, 1, 1, "", "url"]], "nodriver.cdp.network.ReportingApiEndpointsChangedForOrigin": [[36, 1, 1, "", "endpoints"], [36, 1, 1, "", "origin"]], "nodriver.cdp.network.ReportingApiReport": [[36, 1, 1, "", "body"], [36, 1, 1, "", "completed_attempts"], [36, 1, 1, "", "depth"], [36, 1, 1, "", "destination"], [36, 1, 1, "", "id_"], [36, 1, 1, "", "initiator_url"], [36, 1, 1, "", "status"], [36, 1, 1, "", "timestamp"], [36, 1, 1, "", "type_"]], "nodriver.cdp.network.ReportingApiReportAdded": [[36, 1, 1, "", "report"]], "nodriver.cdp.network.ReportingApiReportUpdated": [[36, 1, 1, "", "report"]], "nodriver.cdp.network.Request": [[36, 1, 1, "", "has_post_data"], [36, 1, 1, "", "headers"], [36, 1, 1, "", "initial_priority"], [36, 1, 1, "", "is_link_preload"], [36, 1, 1, "", "is_same_site"], [36, 1, 1, "", "method"], [36, 1, 1, "", "mixed_content_type"], [36, 1, 1, "", "post_data"], [36, 1, 1, "", "post_data_entries"], [36, 1, 1, "", "referrer_policy"], [36, 1, 1, "", "trust_token_params"], [36, 1, 1, "", "url"], [36, 1, 1, "", "url_fragment"]], "nodriver.cdp.network.RequestIntercepted": [[36, 1, 1, "", "auth_challenge"], [36, 1, 1, "", "frame_id"], [36, 1, 1, "", "interception_id"], [36, 1, 1, "", "is_download"], [36, 1, 1, "", "is_navigation_request"], [36, 1, 1, "", "redirect_url"], [36, 1, 1, "", "request"], [36, 1, 1, "", "request_id"], [36, 1, 1, "", "resource_type"], [36, 1, 1, "", "response_error_reason"], [36, 1, 1, "", "response_headers"], [36, 1, 1, "", "response_status_code"]], "nodriver.cdp.network.RequestPattern": [[36, 1, 1, "", "interception_stage"], [36, 1, 1, "", "resource_type"], [36, 1, 1, "", "url_pattern"]], "nodriver.cdp.network.RequestServedFromCache": [[36, 1, 1, "", "request_id"]], "nodriver.cdp.network.RequestWillBeSent": [[36, 1, 1, "", "document_url"], [36, 1, 1, "", "frame_id"], [36, 1, 1, "", "has_user_gesture"], [36, 1, 1, "", "initiator"], [36, 1, 1, "", "loader_id"], [36, 1, 1, "", "redirect_has_extra_info"], [36, 1, 1, "", "redirect_response"], [36, 1, 1, "", "request"], [36, 1, 1, "", "request_id"], [36, 1, 1, "", "timestamp"], [36, 1, 1, "", "type_"], [36, 1, 1, "", "wall_time"]], "nodriver.cdp.network.RequestWillBeSentExtraInfo": [[36, 1, 1, "", "associated_cookies"], [36, 1, 1, "", "client_security_state"], [36, 1, 1, "", "connect_timing"], [36, 1, 1, "", "headers"], [36, 1, 1, "", "request_id"], [36, 1, 1, "", "site_has_cookie_in_other_partition"]], "nodriver.cdp.network.ResourceChangedPriority": [[36, 1, 1, "", "new_priority"], [36, 1, 1, "", "request_id"], [36, 1, 1, "", "timestamp"]], "nodriver.cdp.network.ResourcePriority": [[36, 1, 1, "", "HIGH"], [36, 1, 1, "", "LOW"], [36, 1, 1, "", "MEDIUM"], [36, 1, 1, "", "VERY_HIGH"], [36, 1, 1, "", "VERY_LOW"]], "nodriver.cdp.network.ResourceTiming": [[36, 1, 1, "", "connect_end"], [36, 1, 1, "", "connect_start"], [36, 1, 1, "", "dns_end"], [36, 1, 1, "", "dns_start"], [36, 1, 1, "", "proxy_end"], [36, 1, 1, "", "proxy_start"], [36, 1, 1, "", "push_end"], [36, 1, 1, "", "push_start"], [36, 1, 1, "", "receive_headers_end"], [36, 1, 1, "", "receive_headers_start"], [36, 1, 1, "", "request_time"], [36, 1, 1, "", "send_end"], [36, 1, 1, "", "send_start"], [36, 1, 1, "", "ssl_end"], [36, 1, 1, "", "ssl_start"], [36, 1, 1, "", "worker_cache_lookup_start"], [36, 1, 1, "", "worker_fetch_start"], [36, 1, 1, "", "worker_ready"], [36, 1, 1, "", "worker_respond_with_settled"], [36, 1, 1, "", "worker_router_evaluation_start"], [36, 1, 1, "", "worker_start"]], "nodriver.cdp.network.ResourceType": [[36, 1, 1, "", "CSP_VIOLATION_REPORT"], [36, 1, 1, "", "DOCUMENT"], [36, 1, 1, "", "EVENT_SOURCE"], [36, 1, 1, "", "FETCH"], [36, 1, 1, "", "FONT"], [36, 1, 1, "", "IMAGE"], [36, 1, 1, "", "MANIFEST"], [36, 1, 1, "", "MEDIA"], [36, 1, 1, "", "OTHER"], [36, 1, 1, "", "PING"], [36, 1, 1, "", "PREFETCH"], [36, 1, 1, "", "PREFLIGHT"], [36, 1, 1, "", "SCRIPT"], [36, 1, 1, "", "SIGNED_EXCHANGE"], [36, 1, 1, "", "STYLESHEET"], [36, 1, 1, "", "TEXT_TRACK"], [36, 1, 1, "", "WEB_SOCKET"], [36, 1, 1, "", "XHR"]], "nodriver.cdp.network.Response": [[36, 1, 1, "", "alternate_protocol_usage"], [36, 1, 1, "", "cache_storage_cache_name"], [36, 1, 1, "", "charset"], [36, 1, 1, "", "connection_id"], [36, 1, 1, "", "connection_reused"], [36, 1, 1, "", "encoded_data_length"], [36, 1, 1, "", "from_disk_cache"], [36, 1, 1, "", "from_early_hints"], [36, 1, 1, "", "from_prefetch_cache"], [36, 1, 1, "", "from_service_worker"], [36, 1, 1, "", "headers"], [36, 1, 1, "", "headers_text"], [36, 1, 1, "", "mime_type"], [36, 1, 1, "", "protocol"], [36, 1, 1, "", "remote_ip_address"], [36, 1, 1, "", "remote_port"], [36, 1, 1, "", "request_headers"], [36, 1, 1, "", "request_headers_text"], [36, 1, 1, "", "response_time"], [36, 1, 1, "", "security_details"], [36, 1, 1, "", "security_state"], [36, 1, 1, "", "service_worker_response_source"], [36, 1, 1, "", "service_worker_router_info"], [36, 1, 1, "", "status"], [36, 1, 1, "", "status_text"], [36, 1, 1, "", "timing"], [36, 1, 1, "", "url"]], "nodriver.cdp.network.ResponseReceived": [[36, 1, 1, "", "frame_id"], [36, 1, 1, "", "has_extra_info"], [36, 1, 1, "", "loader_id"], [36, 1, 1, "", "request_id"], [36, 1, 1, "", "response"], [36, 1, 1, "", "timestamp"], [36, 1, 1, "", "type_"]], "nodriver.cdp.network.ResponseReceivedEarlyHints": [[36, 1, 1, "", "headers"], [36, 1, 1, "", "request_id"]], "nodriver.cdp.network.ResponseReceivedExtraInfo": [[36, 1, 1, "", "blocked_cookies"], [36, 1, 1, "", "cookie_partition_key"], [36, 1, 1, "", "cookie_partition_key_opaque"], [36, 1, 1, "", "exempted_cookies"], [36, 1, 1, "", "headers"], [36, 1, 1, "", "headers_text"], [36, 1, 1, "", "request_id"], [36, 1, 1, "", "resource_ip_address_space"], [36, 1, 1, "", "status_code"]], "nodriver.cdp.network.SecurityDetails": [[36, 1, 1, "", "certificate_id"], [36, 1, 1, "", "certificate_transparency_compliance"], [36, 1, 1, "", "cipher"], [36, 1, 1, "", "encrypted_client_hello"], [36, 1, 1, "", "issuer"], [36, 1, 1, "", "key_exchange"], [36, 1, 1, "", "key_exchange_group"], [36, 1, 1, "", "mac"], [36, 1, 1, "", "protocol"], [36, 1, 1, "", "san_list"], [36, 1, 1, "", "server_signature_algorithm"], [36, 1, 1, "", "signed_certificate_timestamp_list"], [36, 1, 1, "", "subject_name"], [36, 1, 1, "", "valid_from"], [36, 1, 1, "", "valid_to"]], "nodriver.cdp.network.SecurityIsolationStatus": [[36, 1, 1, "", "coep"], [36, 1, 1, "", "coop"], [36, 1, 1, "", "csp"]], "nodriver.cdp.network.ServiceWorkerResponseSource": [[36, 1, 1, "", "CACHE_STORAGE"], [36, 1, 1, "", "FALLBACK_CODE"], [36, 1, 1, "", "HTTP_CACHE"], [36, 1, 1, "", "NETWORK"]], "nodriver.cdp.network.ServiceWorkerRouterInfo": [[36, 1, 1, "", "actual_source_type"], [36, 1, 1, "", "matched_source_type"], [36, 1, 1, "", "rule_id_matched"]], "nodriver.cdp.network.ServiceWorkerRouterSource": [[36, 1, 1, "", "CACHE"], [36, 1, 1, "", "FETCH_EVENT"], [36, 1, 1, "", "NETWORK"], [36, 1, 1, "", "RACE_NETWORK_AND_FETCH_HANDLER"]], "nodriver.cdp.network.SetCookieBlockedReason": [[36, 1, 1, "", "DISALLOWED_CHARACTER"], [36, 1, 1, "", "INVALID_DOMAIN"], [36, 1, 1, "", "INVALID_PREFIX"], [36, 1, 1, "", "NAME_VALUE_PAIR_EXCEEDS_MAX_SIZE"], [36, 1, 1, "", "NO_COOKIE_CONTENT"], [36, 1, 1, "", "OVERWRITE_SECURE"], [36, 1, 1, "", "SAME_PARTY_CONFLICTS_WITH_OTHER_ATTRIBUTES"], [36, 1, 1, "", "SAME_PARTY_FROM_CROSS_PARTY_CONTEXT"], [36, 1, 1, "", "SAME_SITE_LAX"], [36, 1, 1, "", "SAME_SITE_NONE_INSECURE"], [36, 1, 1, "", "SAME_SITE_STRICT"], [36, 1, 1, "", "SAME_SITE_UNSPECIFIED_TREATED_AS_LAX"], [36, 1, 1, "", "SCHEMEFUL_SAME_SITE_LAX"], [36, 1, 1, "", "SCHEMEFUL_SAME_SITE_STRICT"], [36, 1, 1, "", "SCHEMEFUL_SAME_SITE_UNSPECIFIED_TREATED_AS_LAX"], [36, 1, 1, "", "SCHEME_NOT_SUPPORTED"], [36, 1, 1, "", "SECURE_ONLY"], [36, 1, 1, "", "SYNTAX_ERROR"], [36, 1, 1, "", "THIRD_PARTY_BLOCKED_IN_FIRST_PARTY_SET"], [36, 1, 1, "", "THIRD_PARTY_PHASEOUT"], [36, 1, 1, "", "UNKNOWN_ERROR"], [36, 1, 1, "", "USER_PREFERENCES"]], "nodriver.cdp.network.SignedCertificateTimestamp": [[36, 1, 1, "", "hash_algorithm"], [36, 1, 1, "", "log_description"], [36, 1, 1, "", "log_id"], [36, 1, 1, "", "origin"], [36, 1, 1, "", "signature_algorithm"], [36, 1, 1, "", "signature_data"], [36, 1, 1, "", "status"], [36, 1, 1, "", "timestamp"]], "nodriver.cdp.network.SignedExchangeError": [[36, 1, 1, "", "error_field"], [36, 1, 1, "", "message"], [36, 1, 1, "", "signature_index"]], "nodriver.cdp.network.SignedExchangeErrorField": [[36, 1, 1, "", "SIGNATURE_CERT_SHA256"], [36, 1, 1, "", "SIGNATURE_CERT_URL"], [36, 1, 1, "", "SIGNATURE_INTEGRITY"], [36, 1, 1, "", "SIGNATURE_SIG"], [36, 1, 1, "", "SIGNATURE_TIMESTAMPS"], [36, 1, 1, "", "SIGNATURE_VALIDITY_URL"]], "nodriver.cdp.network.SignedExchangeHeader": [[36, 1, 1, "", "header_integrity"], [36, 1, 1, "", "request_url"], [36, 1, 1, "", "response_code"], [36, 1, 1, "", "response_headers"], [36, 1, 1, "", "signatures"]], "nodriver.cdp.network.SignedExchangeInfo": [[36, 1, 1, "", "errors"], [36, 1, 1, "", "header"], [36, 1, 1, "", "outer_response"], [36, 1, 1, "", "security_details"]], "nodriver.cdp.network.SignedExchangeReceived": [[36, 1, 1, "", "info"], [36, 1, 1, "", "request_id"]], "nodriver.cdp.network.SignedExchangeSignature": [[36, 1, 1, "", "cert_sha256"], [36, 1, 1, "", "cert_url"], [36, 1, 1, "", "certificates"], [36, 1, 1, "", "date"], [36, 1, 1, "", "expires"], [36, 1, 1, "", "integrity"], [36, 1, 1, "", "label"], [36, 1, 1, "", "signature"], [36, 1, 1, "", "validity_url"]], "nodriver.cdp.network.SubresourceWebBundleInnerResponseError": [[36, 1, 1, "", "bundle_request_id"], [36, 1, 1, "", "error_message"], [36, 1, 1, "", "inner_request_id"], [36, 1, 1, "", "inner_request_url"]], "nodriver.cdp.network.SubresourceWebBundleInnerResponseParsed": [[36, 1, 1, "", "bundle_request_id"], [36, 1, 1, "", "inner_request_id"], [36, 1, 1, "", "inner_request_url"]], "nodriver.cdp.network.SubresourceWebBundleMetadataError": [[36, 1, 1, "", "error_message"], [36, 1, 1, "", "request_id"]], "nodriver.cdp.network.SubresourceWebBundleMetadataReceived": [[36, 1, 1, "", "request_id"], [36, 1, 1, "", "urls"]], "nodriver.cdp.network.TrustTokenOperationDone": [[36, 1, 1, "", "issued_token_count"], [36, 1, 1, "", "issuer_origin"], [36, 1, 1, "", "request_id"], [36, 1, 1, "", "status"], [36, 1, 1, "", "top_level_origin"], [36, 1, 1, "", "type_"]], "nodriver.cdp.network.TrustTokenOperationType": [[36, 1, 1, "", "ISSUANCE"], [36, 1, 1, "", "REDEMPTION"], [36, 1, 1, "", "SIGNING"]], "nodriver.cdp.network.TrustTokenParams": [[36, 1, 1, "", "issuers"], [36, 1, 1, "", "operation"], [36, 1, 1, "", "refresh_policy"]], "nodriver.cdp.network.WebSocketClosed": [[36, 1, 1, "", "request_id"], [36, 1, 1, "", "timestamp"]], "nodriver.cdp.network.WebSocketCreated": [[36, 1, 1, "", "initiator"], [36, 1, 1, "", "request_id"], [36, 1, 1, "", "url"]], "nodriver.cdp.network.WebSocketFrame": [[36, 1, 1, "", "mask"], [36, 1, 1, "", "opcode"], [36, 1, 1, "", "payload_data"]], "nodriver.cdp.network.WebSocketFrameError": [[36, 1, 1, "", "error_message"], [36, 1, 1, "", "request_id"], [36, 1, 1, "", "timestamp"]], "nodriver.cdp.network.WebSocketFrameReceived": [[36, 1, 1, "", "request_id"], [36, 1, 1, "", "response"], [36, 1, 1, "", "timestamp"]], "nodriver.cdp.network.WebSocketFrameSent": [[36, 1, 1, "", "request_id"], [36, 1, 1, "", "response"], [36, 1, 1, "", "timestamp"]], "nodriver.cdp.network.WebSocketHandshakeResponseReceived": [[36, 1, 1, "", "request_id"], [36, 1, 1, "", "response"], [36, 1, 1, "", "timestamp"]], "nodriver.cdp.network.WebSocketRequest": [[36, 1, 1, "", "headers"]], "nodriver.cdp.network.WebSocketResponse": [[36, 1, 1, "", "headers"], [36, 1, 1, "", "headers_text"], [36, 1, 1, "", "request_headers"], [36, 1, 1, "", "request_headers_text"], [36, 1, 1, "", "status"], [36, 1, 1, "", "status_text"]], "nodriver.cdp.network.WebSocketWillSendHandshakeRequest": [[36, 1, 1, "", "request"], [36, 1, 1, "", "request_id"], [36, 1, 1, "", "timestamp"], [36, 1, 1, "", "wall_time"]], "nodriver.cdp.network.WebTransportClosed": [[36, 1, 1, "", "timestamp"], [36, 1, 1, "", "transport_id"]], "nodriver.cdp.network.WebTransportConnectionEstablished": [[36, 1, 1, "", "timestamp"], [36, 1, 1, "", "transport_id"]], "nodriver.cdp.network.WebTransportCreated": [[36, 1, 1, "", "initiator"], [36, 1, 1, "", "timestamp"], [36, 1, 1, "", "transport_id"], [36, 1, 1, "", "url"]], "nodriver.cdp.overlay": [[37, 0, 1, "", "BoxStyle"], [37, 0, 1, "", "ColorFormat"], [37, 0, 1, "", "ContainerQueryContainerHighlightConfig"], [37, 0, 1, "", "ContainerQueryHighlightConfig"], [37, 0, 1, "", "ContrastAlgorithm"], [37, 0, 1, "", "FlexContainerHighlightConfig"], [37, 0, 1, "", "FlexItemHighlightConfig"], [37, 0, 1, "", "FlexNodeHighlightConfig"], [37, 0, 1, "", "GridHighlightConfig"], [37, 0, 1, "", "GridNodeHighlightConfig"], [37, 0, 1, "", "HighlightConfig"], [37, 0, 1, "", "HingeConfig"], [37, 0, 1, "", "InspectMode"], [37, 0, 1, "", "InspectModeCanceled"], [37, 0, 1, "", "InspectNodeRequested"], [37, 0, 1, "", "IsolatedElementHighlightConfig"], [37, 0, 1, "", "IsolationModeHighlightConfig"], [37, 0, 1, "", "LineStyle"], [37, 0, 1, "", "NodeHighlightRequested"], [37, 0, 1, "", "ScreenshotRequested"], [37, 0, 1, "", "ScrollSnapContainerHighlightConfig"], [37, 0, 1, "", "ScrollSnapHighlightConfig"], [37, 0, 1, "", "SourceOrderConfig"], [37, 0, 1, "", "WindowControlsOverlayConfig"], [37, 5, 1, "", "disable"], [37, 5, 1, "", "enable"], [37, 5, 1, "", "get_grid_highlight_objects_for_test"], [37, 5, 1, "", "get_highlight_object_for_test"], [37, 5, 1, "", "get_source_order_highlight_object_for_test"], [37, 5, 1, "", "hide_highlight"], [37, 5, 1, "", "highlight_frame"], [37, 5, 1, "", "highlight_node"], [37, 5, 1, "", "highlight_quad"], [37, 5, 1, "", "highlight_rect"], [37, 5, 1, "", "highlight_source_order"], [37, 5, 1, "", "set_inspect_mode"], [37, 5, 1, "", "set_paused_in_debugger_message"], [37, 5, 1, "", "set_show_ad_highlights"], [37, 5, 1, "", "set_show_container_query_overlays"], [37, 5, 1, "", "set_show_debug_borders"], [37, 5, 1, "", "set_show_flex_overlays"], [37, 5, 1, "", "set_show_fps_counter"], [37, 5, 1, "", "set_show_grid_overlays"], [37, 5, 1, "", "set_show_hinge"], [37, 5, 1, "", "set_show_hit_test_borders"], [37, 5, 1, "", "set_show_isolated_elements"], [37, 5, 1, "", "set_show_layout_shift_regions"], [37, 5, 1, "", "set_show_paint_rects"], [37, 5, 1, "", "set_show_scroll_bottleneck_rects"], [37, 5, 1, "", "set_show_scroll_snap_overlays"], [37, 5, 1, "", "set_show_viewport_size_on_resize"], [37, 5, 1, "", "set_show_web_vitals"], [37, 5, 1, "", "set_show_window_controls_overlay"]], "nodriver.cdp.overlay.BoxStyle": [[37, 1, 1, "", "fill_color"], [37, 1, 1, "", "hatch_color"]], "nodriver.cdp.overlay.ColorFormat": [[37, 1, 1, "", "HEX_"], [37, 1, 1, "", "HSL"], [37, 1, 1, "", "HWB"], [37, 1, 1, "", "RGB"]], "nodriver.cdp.overlay.ContainerQueryContainerHighlightConfig": [[37, 1, 1, "", "container_border"], [37, 1, 1, "", "descendant_border"]], "nodriver.cdp.overlay.ContainerQueryHighlightConfig": [[37, 1, 1, "", "container_query_container_highlight_config"], [37, 1, 1, "", "node_id"]], "nodriver.cdp.overlay.ContrastAlgorithm": [[37, 1, 1, "", "AA"], [37, 1, 1, "", "AAA"], [37, 1, 1, "", "APCA"]], "nodriver.cdp.overlay.FlexContainerHighlightConfig": [[37, 1, 1, "", "column_gap_space"], [37, 1, 1, "", "container_border"], [37, 1, 1, "", "cross_alignment"], [37, 1, 1, "", "cross_distributed_space"], [37, 1, 1, "", "item_separator"], [37, 1, 1, "", "line_separator"], [37, 1, 1, "", "main_distributed_space"], [37, 1, 1, "", "row_gap_space"]], "nodriver.cdp.overlay.FlexItemHighlightConfig": [[37, 1, 1, "", "base_size_border"], [37, 1, 1, "", "base_size_box"], [37, 1, 1, "", "flexibility_arrow"]], "nodriver.cdp.overlay.FlexNodeHighlightConfig": [[37, 1, 1, "", "flex_container_highlight_config"], [37, 1, 1, "", "node_id"]], "nodriver.cdp.overlay.GridHighlightConfig": [[37, 1, 1, "", "area_border_color"], [37, 1, 1, "", "cell_border_color"], [37, 1, 1, "", "cell_border_dash"], [37, 1, 1, "", "column_gap_color"], [37, 1, 1, "", "column_hatch_color"], [37, 1, 1, "", "column_line_color"], [37, 1, 1, "", "column_line_dash"], [37, 1, 1, "", "grid_background_color"], [37, 1, 1, "", "grid_border_color"], [37, 1, 1, "", "grid_border_dash"], [37, 1, 1, "", "row_gap_color"], [37, 1, 1, "", "row_hatch_color"], [37, 1, 1, "", "row_line_color"], [37, 1, 1, "", "row_line_dash"], [37, 1, 1, "", "show_area_names"], [37, 1, 1, "", "show_grid_extension_lines"], [37, 1, 1, "", "show_line_names"], [37, 1, 1, "", "show_negative_line_numbers"], [37, 1, 1, "", "show_positive_line_numbers"], [37, 1, 1, "", "show_track_sizes"]], "nodriver.cdp.overlay.GridNodeHighlightConfig": [[37, 1, 1, "", "grid_highlight_config"], [37, 1, 1, "", "node_id"]], "nodriver.cdp.overlay.HighlightConfig": [[37, 1, 1, "", "border_color"], [37, 1, 1, "", "color_format"], [37, 1, 1, "", "container_query_container_highlight_config"], [37, 1, 1, "", "content_color"], [37, 1, 1, "", "contrast_algorithm"], [37, 1, 1, "", "css_grid_color"], [37, 1, 1, "", "event_target_color"], [37, 1, 1, "", "flex_container_highlight_config"], [37, 1, 1, "", "flex_item_highlight_config"], [37, 1, 1, "", "grid_highlight_config"], [37, 1, 1, "", "margin_color"], [37, 1, 1, "", "padding_color"], [37, 1, 1, "", "shape_color"], [37, 1, 1, "", "shape_margin_color"], [37, 1, 1, "", "show_accessibility_info"], [37, 1, 1, "", "show_extension_lines"], [37, 1, 1, "", "show_info"], [37, 1, 1, "", "show_rulers"], [37, 1, 1, "", "show_styles"]], "nodriver.cdp.overlay.HingeConfig": [[37, 1, 1, "", "content_color"], [37, 1, 1, "", "outline_color"], [37, 1, 1, "", "rect"]], "nodriver.cdp.overlay.InspectMode": [[37, 1, 1, "", "CAPTURE_AREA_SCREENSHOT"], [37, 1, 1, "", "NONE"], [37, 1, 1, "", "SEARCH_FOR_NODE"], [37, 1, 1, "", "SEARCH_FOR_UA_SHADOW_DOM"], [37, 1, 1, "", "SHOW_DISTANCES"]], "nodriver.cdp.overlay.InspectNodeRequested": [[37, 1, 1, "", "backend_node_id"]], "nodriver.cdp.overlay.IsolatedElementHighlightConfig": [[37, 1, 1, "", "isolation_mode_highlight_config"], [37, 1, 1, "", "node_id"]], "nodriver.cdp.overlay.IsolationModeHighlightConfig": [[37, 1, 1, "", "mask_color"], [37, 1, 1, "", "resizer_color"], [37, 1, 1, "", "resizer_handle_color"]], "nodriver.cdp.overlay.LineStyle": [[37, 1, 1, "", "color"], [37, 1, 1, "", "pattern"]], "nodriver.cdp.overlay.NodeHighlightRequested": [[37, 1, 1, "", "node_id"]], "nodriver.cdp.overlay.ScreenshotRequested": [[37, 1, 1, "", "viewport"]], "nodriver.cdp.overlay.ScrollSnapContainerHighlightConfig": [[37, 1, 1, "", "scroll_margin_color"], [37, 1, 1, "", "scroll_padding_color"], [37, 1, 1, "", "snap_area_border"], [37, 1, 1, "", "snapport_border"]], "nodriver.cdp.overlay.ScrollSnapHighlightConfig": [[37, 1, 1, "", "node_id"], [37, 1, 1, "", "scroll_snap_container_highlight_config"]], "nodriver.cdp.overlay.SourceOrderConfig": [[37, 1, 1, "", "child_outline_color"], [37, 1, 1, "", "parent_outline_color"]], "nodriver.cdp.overlay.WindowControlsOverlayConfig": [[37, 1, 1, "", "selected_platform"], [37, 1, 1, "", "show_css"], [37, 1, 1, "", "theme_color"]], "nodriver.cdp.page": [[38, 0, 1, "", "AdFrameExplanation"], [38, 0, 1, "", "AdFrameStatus"], [38, 0, 1, "", "AdFrameType"], [38, 0, 1, "", "AdScriptId"], [38, 0, 1, "", "AppManifestError"], [38, 0, 1, "", "AppManifestParsedProperties"], [38, 0, 1, "", "AutoResponseMode"], [38, 0, 1, "", "BackForwardCacheBlockingDetails"], [38, 0, 1, "", "BackForwardCacheNotRestoredExplanation"], [38, 0, 1, "", "BackForwardCacheNotRestoredExplanationTree"], [38, 0, 1, "", "BackForwardCacheNotRestoredReason"], [38, 0, 1, "", "BackForwardCacheNotRestoredReasonType"], [38, 0, 1, "", "BackForwardCacheNotUsed"], [38, 0, 1, "", "ClientNavigationDisposition"], [38, 0, 1, "", "ClientNavigationReason"], [38, 0, 1, "", "CompilationCacheParams"], [38, 0, 1, "", "CompilationCacheProduced"], [38, 0, 1, "", "CrossOriginIsolatedContextType"], [38, 0, 1, "", "DialogType"], [38, 0, 1, "", "DocumentOpened"], [38, 0, 1, "", "DomContentEventFired"], [38, 0, 1, "", "DownloadProgress"], [38, 0, 1, "", "DownloadWillBegin"], [38, 0, 1, "", "FileChooserOpened"], [38, 0, 1, "", "FileFilter"], [38, 0, 1, "", "FileHandler"], [38, 0, 1, "", "FontFamilies"], [38, 0, 1, "", "FontSizes"], [38, 0, 1, "", "Frame"], [38, 0, 1, "", "FrameAttached"], [38, 0, 1, "", "FrameClearedScheduledNavigation"], [38, 0, 1, "", "FrameDetached"], [38, 0, 1, "", "FrameId"], [38, 0, 1, "", "FrameNavigated"], [38, 0, 1, "", "FrameRequestedNavigation"], [38, 0, 1, "", "FrameResized"], [38, 0, 1, "", "FrameResource"], [38, 0, 1, "", "FrameResourceTree"], [38, 0, 1, "", "FrameScheduledNavigation"], [38, 0, 1, "", "FrameStartedLoading"], [38, 0, 1, "", "FrameStartedNavigating"], [38, 0, 1, "", "FrameStoppedLoading"], [38, 0, 1, "", "FrameSubtreeWillBeDetached"], [38, 0, 1, "", "FrameTree"], [38, 0, 1, "", "GatedAPIFeatures"], [38, 0, 1, "", "ImageResource"], [38, 0, 1, "", "InstallabilityError"], [38, 0, 1, "", "InstallabilityErrorArgument"], [38, 0, 1, "", "InterstitialHidden"], [38, 0, 1, "", "InterstitialShown"], [38, 0, 1, "", "JavascriptDialogClosed"], [38, 0, 1, "", "JavascriptDialogOpening"], [38, 0, 1, "", "LaunchHandler"], [38, 0, 1, "", "LayoutViewport"], [38, 0, 1, "", "LifecycleEvent"], [38, 0, 1, "", "LoadEventFired"], [38, 0, 1, "", "NavigatedWithinDocument"], [38, 0, 1, "", "NavigationEntry"], [38, 0, 1, "", "NavigationType"], [38, 0, 1, "", "OriginTrial"], [38, 0, 1, "", "OriginTrialStatus"], [38, 0, 1, "", "OriginTrialToken"], [38, 0, 1, "", "OriginTrialTokenStatus"], [38, 0, 1, "", "OriginTrialTokenWithStatus"], [38, 0, 1, "", "OriginTrialUsageRestriction"], [38, 0, 1, "", "PermissionsPolicyBlockLocator"], [38, 0, 1, "", "PermissionsPolicyBlockReason"], [38, 0, 1, "", "PermissionsPolicyFeature"], [38, 0, 1, "", "PermissionsPolicyFeatureState"], [38, 0, 1, "", "ProtocolHandler"], [38, 0, 1, "", "ReferrerPolicy"], [38, 0, 1, "", "RelatedApplication"], [38, 0, 1, "", "ScopeExtension"], [38, 0, 1, "", "ScreencastFrame"], [38, 0, 1, "", "ScreencastFrameMetadata"], [38, 0, 1, "", "ScreencastVisibilityChanged"], [38, 0, 1, "", "Screenshot"], [38, 0, 1, "", "ScriptFontFamilies"], [38, 0, 1, "", "ScriptIdentifier"], [38, 0, 1, "", "SecureContextType"], [38, 0, 1, "", "SecurityOriginDetails"], [38, 0, 1, "", "ShareTarget"], [38, 0, 1, "", "Shortcut"], [38, 0, 1, "", "TransitionType"], [38, 0, 1, "", "Viewport"], [38, 0, 1, "", "VisualViewport"], [38, 0, 1, "", "WebAppManifest"], [38, 0, 1, "", "WindowOpen"], [38, 5, 1, "", "add_compilation_cache"], [38, 5, 1, "", "add_script_to_evaluate_on_load"], [38, 5, 1, "", "add_script_to_evaluate_on_new_document"], [38, 5, 1, "", "bring_to_front"], [38, 5, 1, "", "capture_screenshot"], [38, 5, 1, "", "capture_snapshot"], [38, 5, 1, "", "clear_compilation_cache"], [38, 5, 1, "", "clear_device_metrics_override"], [38, 5, 1, "", "clear_device_orientation_override"], [38, 5, 1, "", "clear_geolocation_override"], [38, 5, 1, "", "close"], [38, 5, 1, "", "crash"], [38, 5, 1, "", "create_isolated_world"], [38, 5, 1, "", "delete_cookie"], [38, 5, 1, "", "disable"], [38, 5, 1, "", "enable"], [38, 5, 1, "", "generate_test_report"], [38, 5, 1, "", "get_ad_script_id"], [38, 5, 1, "", "get_app_id"], [38, 5, 1, "", "get_app_manifest"], [38, 5, 1, "", "get_frame_tree"], [38, 5, 1, "", "get_installability_errors"], [38, 5, 1, "", "get_layout_metrics"], [38, 5, 1, "", "get_manifest_icons"], [38, 5, 1, "", "get_navigation_history"], [38, 5, 1, "", "get_origin_trials"], [38, 5, 1, "", "get_permissions_policy_state"], [38, 5, 1, "", "get_resource_content"], [38, 5, 1, "", "get_resource_tree"], [38, 5, 1, "", "handle_java_script_dialog"], [38, 5, 1, "", "navigate"], [38, 5, 1, "", "navigate_to_history_entry"], [38, 5, 1, "", "print_to_pdf"], [38, 5, 1, "", "produce_compilation_cache"], [38, 5, 1, "", "reload"], [38, 5, 1, "", "remove_script_to_evaluate_on_load"], [38, 5, 1, "", "remove_script_to_evaluate_on_new_document"], [38, 5, 1, "", "reset_navigation_history"], [38, 5, 1, "", "screencast_frame_ack"], [38, 5, 1, "", "search_in_resource"], [38, 5, 1, "", "set_ad_blocking_enabled"], [38, 5, 1, "", "set_bypass_csp"], [38, 5, 1, "", "set_device_metrics_override"], [38, 5, 1, "", "set_device_orientation_override"], [38, 5, 1, "", "set_document_content"], [38, 5, 1, "", "set_download_behavior"], [38, 5, 1, "", "set_font_families"], [38, 5, 1, "", "set_font_sizes"], [38, 5, 1, "", "set_geolocation_override"], [38, 5, 1, "", "set_intercept_file_chooser_dialog"], [38, 5, 1, "", "set_lifecycle_events_enabled"], [38, 5, 1, "", "set_prerendering_allowed"], [38, 5, 1, "", "set_rph_registration_mode"], [38, 5, 1, "", "set_spc_transaction_mode"], [38, 5, 1, "", "set_touch_emulation_enabled"], [38, 5, 1, "", "set_web_lifecycle_state"], [38, 5, 1, "", "start_screencast"], [38, 5, 1, "", "stop_loading"], [38, 5, 1, "", "stop_screencast"], [38, 5, 1, "", "wait_for_debugger"]], "nodriver.cdp.page.AdFrameExplanation": [[38, 1, 1, "", "CREATED_BY_AD_SCRIPT"], [38, 1, 1, "", "MATCHED_BLOCKING_RULE"], [38, 1, 1, "", "PARENT_IS_AD"]], "nodriver.cdp.page.AdFrameStatus": [[38, 1, 1, "", "ad_frame_type"], [38, 1, 1, "", "explanations"]], "nodriver.cdp.page.AdFrameType": [[38, 1, 1, "", "CHILD"], [38, 1, 1, "", "NONE"], [38, 1, 1, "", "ROOT"]], "nodriver.cdp.page.AdScriptId": [[38, 1, 1, "", "debugger_id"], [38, 1, 1, "", "script_id"]], "nodriver.cdp.page.AppManifestError": [[38, 1, 1, "", "column"], [38, 1, 1, "", "critical"], [38, 1, 1, "", "line"], [38, 1, 1, "", "message"]], "nodriver.cdp.page.AppManifestParsedProperties": [[38, 1, 1, "", "scope"]], "nodriver.cdp.page.AutoResponseMode": [[38, 1, 1, "", "AUTO_ACCEPT"], [38, 1, 1, "", "AUTO_OPT_OUT"], [38, 1, 1, "", "AUTO_REJECT"], [38, 1, 1, "", "NONE"]], "nodriver.cdp.page.BackForwardCacheBlockingDetails": [[38, 1, 1, "", "column_number"], [38, 1, 1, "", "function"], [38, 1, 1, "", "line_number"], [38, 1, 1, "", "url"]], "nodriver.cdp.page.BackForwardCacheNotRestoredExplanation": [[38, 1, 1, "", "context"], [38, 1, 1, "", "details"], [38, 1, 1, "", "reason"], [38, 1, 1, "", "type_"]], "nodriver.cdp.page.BackForwardCacheNotRestoredExplanationTree": [[38, 1, 1, "", "children"], [38, 1, 1, "", "explanations"], [38, 1, 1, "", "url"]], "nodriver.cdp.page.BackForwardCacheNotRestoredReason": [[38, 1, 1, "", "ACTIVATION_NAVIGATIONS_DISALLOWED_FOR_BUG1234857"], [38, 1, 1, "", "APP_BANNER"], [38, 1, 1, "", "BACK_FORWARD_CACHE_DISABLED"], [38, 1, 1, "", "BACK_FORWARD_CACHE_DISABLED_BY_COMMAND_LINE"], [38, 1, 1, "", "BACK_FORWARD_CACHE_DISABLED_BY_LOW_MEMORY"], [38, 1, 1, "", "BACK_FORWARD_CACHE_DISABLED_FOR_DELEGATE"], [38, 1, 1, "", "BACK_FORWARD_CACHE_DISABLED_FOR_PRERENDER"], [38, 1, 1, "", "BROADCAST_CHANNEL"], [38, 1, 1, "", "BROADCAST_CHANNEL_ON_MESSAGE"], [38, 1, 1, "", "BROWSING_INSTANCE_NOT_SWAPPED"], [38, 1, 1, "", "CACHE_CONTROL_NO_STORE"], [38, 1, 1, "", "CACHE_CONTROL_NO_STORE_COOKIE_MODIFIED"], [38, 1, 1, "", "CACHE_CONTROL_NO_STORE_DEVICE_BOUND_SESSION_TERMINATED"], [38, 1, 1, "", "CACHE_CONTROL_NO_STORE_HTTP_ONLY_COOKIE_MODIFIED"], [38, 1, 1, "", "CACHE_FLUSHED"], [38, 1, 1, "", "CACHE_LIMIT"], [38, 1, 1, "", "CACHE_LIMIT_PRUNED"], [38, 1, 1, "", "CONFLICTING_BROWSING_INSTANCE"], [38, 1, 1, "", "CONTAINS_PLUGINS"], [38, 1, 1, "", "CONTENT_DISCARDED"], [38, 1, 1, "", "CONTENT_FILE_CHOOSER"], [38, 1, 1, "", "CONTENT_FILE_SYSTEM_ACCESS"], [38, 1, 1, "", "CONTENT_MEDIA_DEVICES_DISPATCHER_HOST"], [38, 1, 1, "", "CONTENT_MEDIA_SESSION_SERVICE"], [38, 1, 1, "", "CONTENT_SCREEN_READER"], [38, 1, 1, "", "CONTENT_SECURITY_HANDLER"], [38, 1, 1, "", "CONTENT_SERIAL"], [38, 1, 1, "", "CONTENT_WEB_AUTHENTICATION_API"], [38, 1, 1, "", "CONTENT_WEB_BLUETOOTH"], [38, 1, 1, "", "CONTENT_WEB_USB"], [38, 1, 1, "", "COOKIE_DISABLED"], [38, 1, 1, "", "COOKIE_FLUSHED"], [38, 1, 1, "", "DISABLE_FOR_RENDER_FRAME_HOST_CALLED"], [38, 1, 1, "", "DOCUMENT_LOADED"], [38, 1, 1, "", "DOMAIN_NOT_ALLOWED"], [38, 1, 1, "", "DUMMY"], [38, 1, 1, "", "EMBEDDER_APP_BANNER_MANAGER"], [38, 1, 1, "", "EMBEDDER_CHROME_PASSWORD_MANAGER_CLIENT_BIND_CREDENTIAL_MANAGER"], [38, 1, 1, "", "EMBEDDER_DOM_DISTILLER_SELF_DELETING_REQUEST_DELEGATE"], [38, 1, 1, "", "EMBEDDER_DOM_DISTILLER_VIEWER_SOURCE"], [38, 1, 1, "", "EMBEDDER_EXTENSIONS"], [38, 1, 1, "", "EMBEDDER_EXTENSION_MESSAGING"], [38, 1, 1, "", "EMBEDDER_EXTENSION_MESSAGING_FOR_OPEN_PORT"], [38, 1, 1, "", "EMBEDDER_EXTENSION_SENT_MESSAGE_TO_CACHED_FRAME"], [38, 1, 1, "", "EMBEDDER_MODAL_DIALOG"], [38, 1, 1, "", "EMBEDDER_OFFLINE_PAGE"], [38, 1, 1, "", "EMBEDDER_OOM_INTERVENTION_TAB_HELPER"], [38, 1, 1, "", "EMBEDDER_PERMISSION_REQUEST_MANAGER"], [38, 1, 1, "", "EMBEDDER_POPUP_BLOCKER_TAB_HELPER"], [38, 1, 1, "", "EMBEDDER_SAFE_BROWSING_THREAT_DETAILS"], [38, 1, 1, "", "EMBEDDER_SAFE_BROWSING_TRIGGERED_POPUP_BLOCKER"], [38, 1, 1, "", "ENTERED_BACK_FORWARD_CACHE_BEFORE_SERVICE_WORKER_HOST_ADDED"], [38, 1, 1, "", "ERROR_DOCUMENT"], [38, 1, 1, "", "FENCED_FRAMES_EMBEDDER"], [38, 1, 1, "", "FOREGROUND_CACHE_LIMIT"], [38, 1, 1, "", "HAVE_INNER_CONTENTS"], [38, 1, 1, "", "HTTP_AUTH_REQUIRED"], [38, 1, 1, "", "HTTP_METHOD_NOT_GET"], [38, 1, 1, "", "HTTP_STATUS_NOT_OK"], [38, 1, 1, "", "IDLE_MANAGER"], [38, 1, 1, "", "IGNORE_EVENT_AND_EVICT"], [38, 1, 1, "", "INDEXED_DB_EVENT"], [38, 1, 1, "", "INJECTED_JAVASCRIPT"], [38, 1, 1, "", "INJECTED_STYLE_SHEET"], [38, 1, 1, "", "JAVA_SCRIPT_EXECUTION"], [38, 1, 1, "", "JS_NETWORK_REQUEST_RECEIVED_CACHE_CONTROL_NO_STORE_RESOURCE"], [38, 1, 1, "", "KEEPALIVE_REQUEST"], [38, 1, 1, "", "KEYBOARD_LOCK"], [38, 1, 1, "", "LIVE_MEDIA_STREAM_TRACK"], [38, 1, 1, "", "LOADING"], [38, 1, 1, "", "MAIN_RESOURCE_HAS_CACHE_CONTROL_NO_CACHE"], [38, 1, 1, "", "MAIN_RESOURCE_HAS_CACHE_CONTROL_NO_STORE"], [38, 1, 1, "", "NAVIGATION_CANCELLED_WHILE_RESTORING"], [38, 1, 1, "", "NETWORK_EXCEEDS_BUFFER_LIMIT"], [38, 1, 1, "", "NETWORK_REQUEST_DATAPIPE_DRAINED_AS_BYTES_CONSUMER"], [38, 1, 1, "", "NETWORK_REQUEST_REDIRECTED"], [38, 1, 1, "", "NETWORK_REQUEST_TIMEOUT"], [38, 1, 1, "", "NOT_MOST_RECENT_NAVIGATION_ENTRY"], [38, 1, 1, "", "NOT_PRIMARY_MAIN_FRAME"], [38, 1, 1, "", "NO_RESPONSE_HEAD"], [38, 1, 1, "", "OUTSTANDING_NETWORK_REQUEST_DIRECT_SOCKET"], [38, 1, 1, "", "OUTSTANDING_NETWORK_REQUEST_FETCH"], [38, 1, 1, "", "OUTSTANDING_NETWORK_REQUEST_OTHERS"], [38, 1, 1, "", "OUTSTANDING_NETWORK_REQUEST_XHR"], [38, 1, 1, "", "PARSER_ABORTED"], [38, 1, 1, "", "PAYMENT_MANAGER"], [38, 1, 1, "", "PICTURE_IN_PICTURE"], [38, 1, 1, "", "POST_MESSAGE_BY_WEB_VIEW_CLIENT"], [38, 1, 1, "", "PRINTING"], [38, 1, 1, "", "RELATED_ACTIVE_CONTENTS_EXIST"], [38, 1, 1, "", "RENDERER_PROCESS_CRASHED"], [38, 1, 1, "", "RENDERER_PROCESS_KILLED"], [38, 1, 1, "", "RENDER_FRAME_HOST_REUSED_CROSS_SITE"], [38, 1, 1, "", "RENDER_FRAME_HOST_REUSED_SAME_SITE"], [38, 1, 1, "", "REQUESTED_AUDIO_CAPTURE_PERMISSION"], [38, 1, 1, "", "REQUESTED_BACKGROUND_WORK_PERMISSION"], [38, 1, 1, "", "REQUESTED_BACK_FORWARD_CACHE_BLOCKED_SENSORS"], [38, 1, 1, "", "REQUESTED_BY_WEB_VIEW_CLIENT"], [38, 1, 1, "", "REQUESTED_MIDI_PERMISSION"], [38, 1, 1, "", "REQUESTED_STORAGE_ACCESS_GRANT"], [38, 1, 1, "", "REQUESTED_VIDEO_CAPTURE_PERMISSION"], [38, 1, 1, "", "SCHEDULER_TRACKED_FEATURE_USED"], [38, 1, 1, "", "SCHEME_NOT_HTTP_OR_HTTPS"], [38, 1, 1, "", "SERVICE_WORKER_CLAIM"], [38, 1, 1, "", "SERVICE_WORKER_POST_MESSAGE"], [38, 1, 1, "", "SERVICE_WORKER_UNREGISTRATION"], [38, 1, 1, "", "SERVICE_WORKER_VERSION_ACTIVATION"], [38, 1, 1, "", "SESSION_RESTORED"], [38, 1, 1, "", "SHARED_WORKER"], [38, 1, 1, "", "SMART_CARD"], [38, 1, 1, "", "SPEECH_RECOGNIZER"], [38, 1, 1, "", "SPEECH_SYNTHESIS"], [38, 1, 1, "", "SUBFRAME_IS_NAVIGATING"], [38, 1, 1, "", "SUBRESOURCE_HAS_CACHE_CONTROL_NO_CACHE"], [38, 1, 1, "", "SUBRESOURCE_HAS_CACHE_CONTROL_NO_STORE"], [38, 1, 1, "", "TIMEOUT"], [38, 1, 1, "", "TIMEOUT_PUTTING_IN_CACHE"], [38, 1, 1, "", "UNKNOWN"], [38, 1, 1, "", "UNLOAD_HANDLER"], [38, 1, 1, "", "UNLOAD_HANDLER_EXISTS_IN_MAIN_FRAME"], [38, 1, 1, "", "UNLOAD_HANDLER_EXISTS_IN_SUB_FRAME"], [38, 1, 1, "", "USER_AGENT_OVERRIDE_DIFFERS"], [38, 1, 1, "", "WAS_GRANTED_MEDIA_ACCESS"], [38, 1, 1, "", "WEB_DATABASE"], [38, 1, 1, "", "WEB_HID"], [38, 1, 1, "", "WEB_LOCKS"], [38, 1, 1, "", "WEB_NFC"], [38, 1, 1, "", "WEB_OTP_SERVICE"], [38, 1, 1, "", "WEB_RTC"], [38, 1, 1, "", "WEB_RTC_STICKY"], [38, 1, 1, "", "WEB_SHARE"], [38, 1, 1, "", "WEB_SOCKET"], [38, 1, 1, "", "WEB_SOCKET_STICKY"], [38, 1, 1, "", "WEB_TRANSPORT"], [38, 1, 1, "", "WEB_TRANSPORT_STICKY"], [38, 1, 1, "", "WEB_VIEW_DOCUMENT_START_JAVASCRIPT_CHANGED"], [38, 1, 1, "", "WEB_VIEW_JAVA_SCRIPT_OBJECT_CHANGED"], [38, 1, 1, "", "WEB_VIEW_MESSAGE_LISTENER_INJECTED"], [38, 1, 1, "", "WEB_VIEW_SAFE_BROWSING_ALLOWLIST_CHANGED"], [38, 1, 1, "", "WEB_VIEW_SETTINGS_CHANGED"], [38, 1, 1, "", "WEB_XR"]], "nodriver.cdp.page.BackForwardCacheNotRestoredReasonType": [[38, 1, 1, "", "CIRCUMSTANTIAL"], [38, 1, 1, "", "PAGE_SUPPORT_NEEDED"], [38, 1, 1, "", "SUPPORT_PENDING"]], "nodriver.cdp.page.BackForwardCacheNotUsed": [[38, 1, 1, "", "frame_id"], [38, 1, 1, "", "loader_id"], [38, 1, 1, "", "not_restored_explanations"], [38, 1, 1, "", "not_restored_explanations_tree"]], "nodriver.cdp.page.ClientNavigationDisposition": [[38, 1, 1, "", "CURRENT_TAB"], [38, 1, 1, "", "DOWNLOAD"], [38, 1, 1, "", "NEW_TAB"], [38, 1, 1, "", "NEW_WINDOW"]], "nodriver.cdp.page.ClientNavigationReason": [[38, 1, 1, "", "ANCHOR_CLICK"], [38, 1, 1, "", "FORM_SUBMISSION_GET"], [38, 1, 1, "", "FORM_SUBMISSION_POST"], [38, 1, 1, "", "HTTP_HEADER_REFRESH"], [38, 1, 1, "", "INITIAL_FRAME_NAVIGATION"], [38, 1, 1, "", "META_TAG_REFRESH"], [38, 1, 1, "", "OTHER"], [38, 1, 1, "", "PAGE_BLOCK_INTERSTITIAL"], [38, 1, 1, "", "RELOAD"], [38, 1, 1, "", "SCRIPT_INITIATED"]], "nodriver.cdp.page.CompilationCacheParams": [[38, 1, 1, "", "eager"], [38, 1, 1, "", "url"]], "nodriver.cdp.page.CompilationCacheProduced": [[38, 1, 1, "", "data"], [38, 1, 1, "", "url"]], "nodriver.cdp.page.CrossOriginIsolatedContextType": [[38, 1, 1, "", "ISOLATED"], [38, 1, 1, "", "NOT_ISOLATED"], [38, 1, 1, "", "NOT_ISOLATED_FEATURE_DISABLED"]], "nodriver.cdp.page.DialogType": [[38, 1, 1, "", "ALERT"], [38, 1, 1, "", "BEFOREUNLOAD"], [38, 1, 1, "", "CONFIRM"], [38, 1, 1, "", "PROMPT"]], "nodriver.cdp.page.DocumentOpened": [[38, 1, 1, "", "frame"]], "nodriver.cdp.page.DomContentEventFired": [[38, 1, 1, "", "timestamp"]], "nodriver.cdp.page.DownloadProgress": [[38, 1, 1, "", "guid"], [38, 1, 1, "", "received_bytes"], [38, 1, 1, "", "state"], [38, 1, 1, "", "total_bytes"]], "nodriver.cdp.page.DownloadWillBegin": [[38, 1, 1, "", "frame_id"], [38, 1, 1, "", "guid"], [38, 1, 1, "", "suggested_filename"], [38, 1, 1, "", "url"]], "nodriver.cdp.page.FileChooserOpened": [[38, 1, 1, "", "backend_node_id"], [38, 1, 1, "", "frame_id"], [38, 1, 1, "", "mode"]], "nodriver.cdp.page.FileFilter": [[38, 1, 1, "", "accepts"], [38, 1, 1, "", "name"]], "nodriver.cdp.page.FileHandler": [[38, 1, 1, "", "accepts"], [38, 1, 1, "", "action"], [38, 1, 1, "", "icons"], [38, 1, 1, "", "launch_type"], [38, 1, 1, "", "name"]], "nodriver.cdp.page.FontFamilies": [[38, 1, 1, "", "cursive"], [38, 1, 1, "", "fantasy"], [38, 1, 1, "", "fixed"], [38, 1, 1, "", "math"], [38, 1, 1, "", "sans_serif"], [38, 1, 1, "", "serif"], [38, 1, 1, "", "standard"]], "nodriver.cdp.page.FontSizes": [[38, 1, 1, "", "fixed"], [38, 1, 1, "", "standard"]], "nodriver.cdp.page.Frame": [[38, 1, 1, "", "ad_frame_status"], [38, 1, 1, "", "cross_origin_isolated_context_type"], [38, 1, 1, "", "domain_and_registry"], [38, 1, 1, "", "gated_api_features"], [38, 1, 1, "", "id_"], [38, 1, 1, "", "loader_id"], [38, 1, 1, "", "mime_type"], [38, 1, 1, "", "name"], [38, 1, 1, "", "parent_id"], [38, 1, 1, "", "secure_context_type"], [38, 1, 1, "", "security_origin"], [38, 1, 1, "", "security_origin_details"], [38, 1, 1, "", "unreachable_url"], [38, 1, 1, "", "url"], [38, 1, 1, "", "url_fragment"]], "nodriver.cdp.page.FrameAttached": [[38, 1, 1, "", "frame_id"], [38, 1, 1, "", "parent_frame_id"], [38, 1, 1, "", "stack"]], "nodriver.cdp.page.FrameClearedScheduledNavigation": [[38, 1, 1, "", "frame_id"]], "nodriver.cdp.page.FrameDetached": [[38, 1, 1, "", "frame_id"], [38, 1, 1, "", "reason"]], "nodriver.cdp.page.FrameNavigated": [[38, 1, 1, "", "frame"], [38, 1, 1, "", "type_"]], "nodriver.cdp.page.FrameRequestedNavigation": [[38, 1, 1, "", "disposition"], [38, 1, 1, "", "frame_id"], [38, 1, 1, "", "reason"], [38, 1, 1, "", "url"]], "nodriver.cdp.page.FrameResource": [[38, 1, 1, "", "canceled"], [38, 1, 1, "", "content_size"], [38, 1, 1, "", "failed"], [38, 1, 1, "", "last_modified"], [38, 1, 1, "", "mime_type"], [38, 1, 1, "", "type_"], [38, 1, 1, "", "url"]], "nodriver.cdp.page.FrameResourceTree": [[38, 1, 1, "", "child_frames"], [38, 1, 1, "", "frame"], [38, 1, 1, "", "resources"]], "nodriver.cdp.page.FrameScheduledNavigation": [[38, 1, 1, "", "delay"], [38, 1, 1, "", "frame_id"], [38, 1, 1, "", "reason"], [38, 1, 1, "", "url"]], "nodriver.cdp.page.FrameStartedLoading": [[38, 1, 1, "", "frame_id"]], "nodriver.cdp.page.FrameStartedNavigating": [[38, 1, 1, "", "frame_id"], [38, 1, 1, "", "loader_id"], [38, 1, 1, "", "navigation_type"], [38, 1, 1, "", "url"]], "nodriver.cdp.page.FrameStoppedLoading": [[38, 1, 1, "", "frame_id"]], "nodriver.cdp.page.FrameSubtreeWillBeDetached": [[38, 1, 1, "", "frame_id"]], "nodriver.cdp.page.FrameTree": [[38, 1, 1, "", "child_frames"], [38, 1, 1, "", "frame"]], "nodriver.cdp.page.GatedAPIFeatures": [[38, 1, 1, "", "PERFORMANCE_MEASURE_MEMORY"], [38, 1, 1, "", "PERFORMANCE_PROFILE"], [38, 1, 1, "", "SHARED_ARRAY_BUFFERS"], [38, 1, 1, "", "SHARED_ARRAY_BUFFERS_TRANSFER_ALLOWED"]], "nodriver.cdp.page.ImageResource": [[38, 1, 1, "", "sizes"], [38, 1, 1, "", "type_"], [38, 1, 1, "", "url"]], "nodriver.cdp.page.InstallabilityError": [[38, 1, 1, "", "error_arguments"], [38, 1, 1, "", "error_id"]], "nodriver.cdp.page.InstallabilityErrorArgument": [[38, 1, 1, "", "name"], [38, 1, 1, "", "value"]], "nodriver.cdp.page.JavascriptDialogClosed": [[38, 1, 1, "", "result"], [38, 1, 1, "", "user_input"]], "nodriver.cdp.page.JavascriptDialogOpening": [[38, 1, 1, "", "default_prompt"], [38, 1, 1, "", "has_browser_handler"], [38, 1, 1, "", "message"], [38, 1, 1, "", "type_"], [38, 1, 1, "", "url"]], "nodriver.cdp.page.LaunchHandler": [[38, 1, 1, "", "client_mode"]], "nodriver.cdp.page.LayoutViewport": [[38, 1, 1, "", "client_height"], [38, 1, 1, "", "client_width"], [38, 1, 1, "", "page_x"], [38, 1, 1, "", "page_y"]], "nodriver.cdp.page.LifecycleEvent": [[38, 1, 1, "", "frame_id"], [38, 1, 1, "", "loader_id"], [38, 1, 1, "", "name"], [38, 1, 1, "", "timestamp"]], "nodriver.cdp.page.LoadEventFired": [[38, 1, 1, "", "timestamp"]], "nodriver.cdp.page.NavigatedWithinDocument": [[38, 1, 1, "", "frame_id"], [38, 1, 1, "", "navigation_type"], [38, 1, 1, "", "url"]], "nodriver.cdp.page.NavigationEntry": [[38, 1, 1, "", "id_"], [38, 1, 1, "", "title"], [38, 1, 1, "", "transition_type"], [38, 1, 1, "", "url"], [38, 1, 1, "", "user_typed_url"]], "nodriver.cdp.page.NavigationType": [[38, 1, 1, "", "BACK_FORWARD_CACHE_RESTORE"], [38, 1, 1, "", "NAVIGATION"]], "nodriver.cdp.page.OriginTrial": [[38, 1, 1, "", "status"], [38, 1, 1, "", "tokens_with_status"], [38, 1, 1, "", "trial_name"]], "nodriver.cdp.page.OriginTrialStatus": [[38, 1, 1, "", "ENABLED"], [38, 1, 1, "", "OS_NOT_SUPPORTED"], [38, 1, 1, "", "TRIAL_NOT_ALLOWED"], [38, 1, 1, "", "VALID_TOKEN_NOT_PROVIDED"]], "nodriver.cdp.page.OriginTrialToken": [[38, 1, 1, "", "expiry_time"], [38, 1, 1, "", "is_third_party"], [38, 1, 1, "", "match_sub_domains"], [38, 1, 1, "", "origin"], [38, 1, 1, "", "trial_name"], [38, 1, 1, "", "usage_restriction"]], "nodriver.cdp.page.OriginTrialTokenStatus": [[38, 1, 1, "", "EXPIRED"], [38, 1, 1, "", "FEATURE_DISABLED"], [38, 1, 1, "", "FEATURE_DISABLED_FOR_USER"], [38, 1, 1, "", "INSECURE"], [38, 1, 1, "", "INVALID_SIGNATURE"], [38, 1, 1, "", "MALFORMED"], [38, 1, 1, "", "NOT_SUPPORTED"], [38, 1, 1, "", "SUCCESS"], [38, 1, 1, "", "TOKEN_DISABLED"], [38, 1, 1, "", "UNKNOWN_TRIAL"], [38, 1, 1, "", "WRONG_ORIGIN"], [38, 1, 1, "", "WRONG_VERSION"]], "nodriver.cdp.page.OriginTrialTokenWithStatus": [[38, 1, 1, "", "parsed_token"], [38, 1, 1, "", "raw_token_text"], [38, 1, 1, "", "status"]], "nodriver.cdp.page.OriginTrialUsageRestriction": [[38, 1, 1, "", "NONE"], [38, 1, 1, "", "SUBSET"]], "nodriver.cdp.page.PermissionsPolicyBlockLocator": [[38, 1, 1, "", "block_reason"], [38, 1, 1, "", "frame_id"]], "nodriver.cdp.page.PermissionsPolicyBlockReason": [[38, 1, 1, "", "HEADER"], [38, 1, 1, "", "IFRAME_ATTRIBUTE"], [38, 1, 1, "", "IN_FENCED_FRAME_TREE"], [38, 1, 1, "", "IN_ISOLATED_APP"]], "nodriver.cdp.page.PermissionsPolicyFeature": [[38, 1, 1, "", "ACCELEROMETER"], [38, 1, 1, "", "ALL_SCREENS_CAPTURE"], [38, 1, 1, "", "AMBIENT_LIGHT_SENSOR"], [38, 1, 1, "", "ATTRIBUTION_REPORTING"], [38, 1, 1, "", "AUTOPLAY"], [38, 1, 1, "", "BLUETOOTH"], [38, 1, 1, "", "BROWSING_TOPICS"], [38, 1, 1, "", "CAMERA"], [38, 1, 1, "", "CAPTURED_SURFACE_CONTROL"], [38, 1, 1, "", "CH_DEVICE_MEMORY"], [38, 1, 1, "", "CH_DOWNLINK"], [38, 1, 1, "", "CH_DPR"], [38, 1, 1, "", "CH_ECT"], [38, 1, 1, "", "CH_PREFERS_COLOR_SCHEME"], [38, 1, 1, "", "CH_PREFERS_REDUCED_MOTION"], [38, 1, 1, "", "CH_PREFERS_REDUCED_TRANSPARENCY"], [38, 1, 1, "", "CH_RTT"], [38, 1, 1, "", "CH_SAVE_DATA"], [38, 1, 1, "", "CH_UA"], [38, 1, 1, "", "CH_UA_ARCH"], [38, 1, 1, "", "CH_UA_BITNESS"], [38, 1, 1, "", "CH_UA_FORM_FACTORS"], [38, 1, 1, "", "CH_UA_FULL_VERSION"], [38, 1, 1, "", "CH_UA_FULL_VERSION_LIST"], [38, 1, 1, "", "CH_UA_HIGH_ENTROPY_VALUES"], [38, 1, 1, "", "CH_UA_MOBILE"], [38, 1, 1, "", "CH_UA_MODEL"], [38, 1, 1, "", "CH_UA_PLATFORM"], [38, 1, 1, "", "CH_UA_PLATFORM_VERSION"], [38, 1, 1, "", "CH_UA_WOW64"], [38, 1, 1, "", "CH_VIEWPORT_HEIGHT"], [38, 1, 1, "", "CH_VIEWPORT_WIDTH"], [38, 1, 1, "", "CH_WIDTH"], [38, 1, 1, "", "CLIPBOARD_READ"], [38, 1, 1, "", "CLIPBOARD_WRITE"], [38, 1, 1, "", "COMPUTE_PRESSURE"], [38, 1, 1, "", "CONTROLLED_FRAME"], [38, 1, 1, "", "CROSS_ORIGIN_ISOLATED"], [38, 1, 1, "", "DEFERRED_FETCH"], [38, 1, 1, "", "DEFERRED_FETCH_MINIMAL"], [38, 1, 1, "", "DIGITAL_CREDENTIALS_GET"], [38, 1, 1, "", "DIRECT_SOCKETS"], [38, 1, 1, "", "DIRECT_SOCKETS_PRIVATE"], [38, 1, 1, "", "DISPLAY_CAPTURE"], [38, 1, 1, "", "DOCUMENT_DOMAIN"], [38, 1, 1, "", "ENCRYPTED_MEDIA"], [38, 1, 1, "", "EXECUTION_WHILE_NOT_RENDERED"], [38, 1, 1, "", "EXECUTION_WHILE_OUT_OF_VIEWPORT"], [38, 1, 1, "", "FENCED_UNPARTITIONED_STORAGE_READ"], [38, 1, 1, "", "FOCUS_WITHOUT_USER_ACTIVATION"], [38, 1, 1, "", "FROBULATE"], [38, 1, 1, "", "FULLSCREEN"], [38, 1, 1, "", "GAMEPAD"], [38, 1, 1, "", "GEOLOCATION"], [38, 1, 1, "", "GYROSCOPE"], [38, 1, 1, "", "HID"], [38, 1, 1, "", "IDENTITY_CREDENTIALS_GET"], [38, 1, 1, "", "IDLE_DETECTION"], [38, 1, 1, "", "INTEREST_COHORT"], [38, 1, 1, "", "JOIN_AD_INTEREST_GROUP"], [38, 1, 1, "", "KEYBOARD_MAP"], [38, 1, 1, "", "LANGUAGE_DETECTOR"], [38, 1, 1, "", "LOCAL_FONTS"], [38, 1, 1, "", "MAGNETOMETER"], [38, 1, 1, "", "MEDIA_PLAYBACK_WHILE_NOT_VISIBLE"], [38, 1, 1, "", "MICROPHONE"], [38, 1, 1, "", "MIDI"], [38, 1, 1, "", "OTP_CREDENTIALS"], [38, 1, 1, "", "PAYMENT"], [38, 1, 1, "", "PICTURE_IN_PICTURE"], [38, 1, 1, "", "POPINS"], [38, 1, 1, "", "PRIVATE_AGGREGATION"], [38, 1, 1, "", "PRIVATE_STATE_TOKEN_ISSUANCE"], [38, 1, 1, "", "PRIVATE_STATE_TOKEN_REDEMPTION"], [38, 1, 1, "", "PUBLICKEY_CREDENTIALS_CREATE"], [38, 1, 1, "", "PUBLICKEY_CREDENTIALS_GET"], [38, 1, 1, "", "REWRITER"], [38, 1, 1, "", "RUN_AD_AUCTION"], [38, 1, 1, "", "SCREEN_WAKE_LOCK"], [38, 1, 1, "", "SERIAL"], [38, 1, 1, "", "SHARED_AUTOFILL"], [38, 1, 1, "", "SHARED_STORAGE"], [38, 1, 1, "", "SHARED_STORAGE_SELECT_URL"], [38, 1, 1, "", "SMART_CARD"], [38, 1, 1, "", "SPEAKER_SELECTION"], [38, 1, 1, "", "STORAGE_ACCESS"], [38, 1, 1, "", "SUB_APPS"], [38, 1, 1, "", "SUMMARIZER"], [38, 1, 1, "", "SYNC_XHR"], [38, 1, 1, "", "TRANSLATOR"], [38, 1, 1, "", "UNLOAD"], [38, 1, 1, "", "USB"], [38, 1, 1, "", "USB_UNRESTRICTED"], [38, 1, 1, "", "VERTICAL_SCROLL"], [38, 1, 1, "", "WEB_APP_INSTALLATION"], [38, 1, 1, "", "WEB_PRINTING"], [38, 1, 1, "", "WEB_SHARE"], [38, 1, 1, "", "WINDOW_MANAGEMENT"], [38, 1, 1, "", "WRITER"], [38, 1, 1, "", "XR_SPATIAL_TRACKING"]], "nodriver.cdp.page.PermissionsPolicyFeatureState": [[38, 1, 1, "", "allowed"], [38, 1, 1, "", "feature"], [38, 1, 1, "", "locator"]], "nodriver.cdp.page.ProtocolHandler": [[38, 1, 1, "", "protocol"], [38, 1, 1, "", "url"]], "nodriver.cdp.page.ReferrerPolicy": [[38, 1, 1, "", "NO_REFERRER"], [38, 1, 1, "", "NO_REFERRER_WHEN_DOWNGRADE"], [38, 1, 1, "", "ORIGIN"], [38, 1, 1, "", "ORIGIN_WHEN_CROSS_ORIGIN"], [38, 1, 1, "", "SAME_ORIGIN"], [38, 1, 1, "", "STRICT_ORIGIN"], [38, 1, 1, "", "STRICT_ORIGIN_WHEN_CROSS_ORIGIN"], [38, 1, 1, "", "UNSAFE_URL"]], "nodriver.cdp.page.RelatedApplication": [[38, 1, 1, "", "id_"], [38, 1, 1, "", "url"]], "nodriver.cdp.page.ScopeExtension": [[38, 1, 1, "", "has_origin_wildcard"], [38, 1, 1, "", "origin"]], "nodriver.cdp.page.ScreencastFrame": [[38, 1, 1, "", "data"], [38, 1, 1, "", "metadata"], [38, 1, 1, "", "session_id"]], "nodriver.cdp.page.ScreencastFrameMetadata": [[38, 1, 1, "", "device_height"], [38, 1, 1, "", "device_width"], [38, 1, 1, "", "offset_top"], [38, 1, 1, "", "page_scale_factor"], [38, 1, 1, "", "scroll_offset_x"], [38, 1, 1, "", "scroll_offset_y"], [38, 1, 1, "", "timestamp"]], "nodriver.cdp.page.ScreencastVisibilityChanged": [[38, 1, 1, "", "visible"]], "nodriver.cdp.page.Screenshot": [[38, 1, 1, "", "form_factor"], [38, 1, 1, "", "image"], [38, 1, 1, "", "label"]], "nodriver.cdp.page.ScriptFontFamilies": [[38, 1, 1, "", "font_families"], [38, 1, 1, "", "script"]], "nodriver.cdp.page.SecureContextType": [[38, 1, 1, "", "INSECURE_ANCESTOR"], [38, 1, 1, "", "INSECURE_SCHEME"], [38, 1, 1, "", "SECURE"], [38, 1, 1, "", "SECURE_LOCALHOST"]], "nodriver.cdp.page.SecurityOriginDetails": [[38, 1, 1, "", "is_localhost"]], "nodriver.cdp.page.ShareTarget": [[38, 1, 1, "", "action"], [38, 1, 1, "", "enctype"], [38, 1, 1, "", "files"], [38, 1, 1, "", "method"], [38, 1, 1, "", "text"], [38, 1, 1, "", "title"], [38, 1, 1, "", "url"]], "nodriver.cdp.page.Shortcut": [[38, 1, 1, "", "name"], [38, 1, 1, "", "url"]], "nodriver.cdp.page.TransitionType": [[38, 1, 1, "", "ADDRESS_BAR"], [38, 1, 1, "", "AUTO_BOOKMARK"], [38, 1, 1, "", "AUTO_SUBFRAME"], [38, 1, 1, "", "AUTO_TOPLEVEL"], [38, 1, 1, "", "FORM_SUBMIT"], [38, 1, 1, "", "GENERATED"], [38, 1, 1, "", "KEYWORD"], [38, 1, 1, "", "KEYWORD_GENERATED"], [38, 1, 1, "", "LINK"], [38, 1, 1, "", "MANUAL_SUBFRAME"], [38, 1, 1, "", "OTHER"], [38, 1, 1, "", "RELOAD"], [38, 1, 1, "", "TYPED"]], "nodriver.cdp.page.Viewport": [[38, 1, 1, "", "height"], [38, 1, 1, "", "scale"], [38, 1, 1, "", "width"], [38, 1, 1, "", "x"], [38, 1, 1, "", "y"]], "nodriver.cdp.page.VisualViewport": [[38, 1, 1, "", "client_height"], [38, 1, 1, "", "client_width"], [38, 1, 1, "", "offset_x"], [38, 1, 1, "", "offset_y"], [38, 1, 1, "", "page_x"], [38, 1, 1, "", "page_y"], [38, 1, 1, "", "scale"], [38, 1, 1, "", "zoom"]], "nodriver.cdp.page.WebAppManifest": [[38, 1, 1, "", "background_color"], [38, 1, 1, "", "description"], [38, 1, 1, "", "dir_"], [38, 1, 1, "", "display"], [38, 1, 1, "", "display_overrides"], [38, 1, 1, "", "file_handlers"], [38, 1, 1, "", "icons"], [38, 1, 1, "", "id_"], [38, 1, 1, "", "lang"], [38, 1, 1, "", "launch_handler"], [38, 1, 1, "", "name"], [38, 1, 1, "", "orientation"], [38, 1, 1, "", "prefer_related_applications"], [38, 1, 1, "", "protocol_handlers"], [38, 1, 1, "", "related_applications"], [38, 1, 1, "", "scope"], [38, 1, 1, "", "scope_extensions"], [38, 1, 1, "", "screenshots"], [38, 1, 1, "", "share_target"], [38, 1, 1, "", "short_name"], [38, 1, 1, "", "shortcuts"], [38, 1, 1, "", "start_url"], [38, 1, 1, "", "theme_color"]], "nodriver.cdp.page.WindowOpen": [[38, 1, 1, "", "url"], [38, 1, 1, "", "user_gesture"], [38, 1, 1, "", "window_features"], [38, 1, 1, "", "window_name"]], "nodriver.cdp.performance": [[39, 0, 1, "", "Metric"], [39, 0, 1, "", "Metrics"], [39, 5, 1, "", "disable"], [39, 5, 1, "", "enable"], [39, 5, 1, "", "get_metrics"], [39, 5, 1, "", "set_time_domain"]], "nodriver.cdp.performance.Metric": [[39, 1, 1, "", "name"], [39, 1, 1, "", "value"]], "nodriver.cdp.performance.Metrics": [[39, 1, 1, "", "metrics"], [39, 1, 1, "", "title"]], "nodriver.cdp.performance_timeline": [[40, 0, 1, "", "LargestContentfulPaint"], [40, 0, 1, "", "LayoutShift"], [40, 0, 1, "", "LayoutShiftAttribution"], [40, 0, 1, "", "TimelineEvent"], [40, 0, 1, "", "TimelineEventAdded"], [40, 5, 1, "", "enable"]], "nodriver.cdp.performance_timeline.LargestContentfulPaint": [[40, 1, 1, "", "element_id"], [40, 1, 1, "", "load_time"], [40, 1, 1, "", "node_id"], [40, 1, 1, "", "render_time"], [40, 1, 1, "", "size"], [40, 1, 1, "", "url"]], "nodriver.cdp.performance_timeline.LayoutShift": [[40, 1, 1, "", "had_recent_input"], [40, 1, 1, "", "last_input_time"], [40, 1, 1, "", "sources"], [40, 1, 1, "", "value"]], "nodriver.cdp.performance_timeline.LayoutShiftAttribution": [[40, 1, 1, "", "current_rect"], [40, 1, 1, "", "node_id"], [40, 1, 1, "", "previous_rect"]], "nodriver.cdp.performance_timeline.TimelineEvent": [[40, 1, 1, "", "duration"], [40, 1, 1, "", "frame_id"], [40, 1, 1, "", "layout_shift_details"], [40, 1, 1, "", "lcp_details"], [40, 1, 1, "", "name"], [40, 1, 1, "", "time"], [40, 1, 1, "", "type_"]], "nodriver.cdp.performance_timeline.TimelineEventAdded": [[40, 1, 1, "", "event"]], "nodriver.cdp.preload": [[41, 0, 1, "", "PrefetchStatus"], [41, 0, 1, "", "PrefetchStatusUpdated"], [41, 0, 1, "", "PreloadEnabledStateUpdated"], [41, 0, 1, "", "PreloadPipelineId"], [41, 0, 1, "", "PreloadingAttemptKey"], [41, 0, 1, "", "PreloadingAttemptSource"], [41, 0, 1, "", "PreloadingAttemptSourcesUpdated"], [41, 0, 1, "", "PreloadingStatus"], [41, 0, 1, "", "PrerenderFinalStatus"], [41, 0, 1, "", "PrerenderMismatchedHeaders"], [41, 0, 1, "", "PrerenderStatusUpdated"], [41, 0, 1, "", "RuleSet"], [41, 0, 1, "", "RuleSetErrorType"], [41, 0, 1, "", "RuleSetId"], [41, 0, 1, "", "RuleSetRemoved"], [41, 0, 1, "", "RuleSetUpdated"], [41, 0, 1, "", "SpeculationAction"], [41, 0, 1, "", "SpeculationTargetHint"], [41, 5, 1, "", "disable"], [41, 5, 1, "", "enable"]], "nodriver.cdp.preload.PrefetchStatus": [[41, 1, 1, "", "PREFETCH_ALLOWED"], [41, 1, 1, "", "PREFETCH_EVICTED_AFTER_BROWSING_DATA_REMOVED"], [41, 1, 1, "", "PREFETCH_EVICTED_AFTER_CANDIDATE_REMOVED"], [41, 1, 1, "", "PREFETCH_EVICTED_FOR_NEWER_PREFETCH"], [41, 1, 1, "", "PREFETCH_FAILED_INELIGIBLE_REDIRECT"], [41, 1, 1, "", "PREFETCH_FAILED_INVALID_REDIRECT"], [41, 1, 1, "", "PREFETCH_FAILED_MIME_NOT_SUPPORTED"], [41, 1, 1, "", "PREFETCH_FAILED_NET_ERROR"], [41, 1, 1, "", "PREFETCH_FAILED_NON2_XX"], [41, 1, 1, "", "PREFETCH_HELDBACK"], [41, 1, 1, "", "PREFETCH_INELIGIBLE_RETRY_AFTER"], [41, 1, 1, "", "PREFETCH_IS_PRIVACY_DECOY"], [41, 1, 1, "", "PREFETCH_IS_STALE"], [41, 1, 1, "", "PREFETCH_NOT_ELIGIBLE_BATTERY_SAVER_ENABLED"], [41, 1, 1, "", "PREFETCH_NOT_ELIGIBLE_BROWSER_CONTEXT_OFF_THE_RECORD"], [41, 1, 1, "", "PREFETCH_NOT_ELIGIBLE_DATA_SAVER_ENABLED"], [41, 1, 1, "", "PREFETCH_NOT_ELIGIBLE_EXISTING_PROXY"], [41, 1, 1, "", "PREFETCH_NOT_ELIGIBLE_HOST_IS_NON_UNIQUE"], [41, 1, 1, "", "PREFETCH_NOT_ELIGIBLE_NON_DEFAULT_STORAGE_PARTITION"], [41, 1, 1, "", "PREFETCH_NOT_ELIGIBLE_PRELOADING_DISABLED"], [41, 1, 1, "", "PREFETCH_NOT_ELIGIBLE_REDIRECT_FROM_SERVICE_WORKER"], [41, 1, 1, "", "PREFETCH_NOT_ELIGIBLE_REDIRECT_TO_SERVICE_WORKER"], [41, 1, 1, "", "PREFETCH_NOT_ELIGIBLE_SAME_SITE_CROSS_ORIGIN_PREFETCH_REQUIRED_PROXY"], [41, 1, 1, "", "PREFETCH_NOT_ELIGIBLE_SCHEME_IS_NOT_HTTPS"], [41, 1, 1, "", "PREFETCH_NOT_ELIGIBLE_USER_HAS_COOKIES"], [41, 1, 1, "", "PREFETCH_NOT_ELIGIBLE_USER_HAS_SERVICE_WORKER"], [41, 1, 1, "", "PREFETCH_NOT_ELIGIBLE_USER_HAS_SERVICE_WORKER_NO_FETCH_HANDLER"], [41, 1, 1, "", "PREFETCH_NOT_FINISHED_IN_TIME"], [41, 1, 1, "", "PREFETCH_NOT_STARTED"], [41, 1, 1, "", "PREFETCH_NOT_USED_COOKIES_CHANGED"], [41, 1, 1, "", "PREFETCH_NOT_USED_PROBE_FAILED"], [41, 1, 1, "", "PREFETCH_PROXY_NOT_AVAILABLE"], [41, 1, 1, "", "PREFETCH_RESPONSE_USED"], [41, 1, 1, "", "PREFETCH_SUCCESSFUL_BUT_NOT_USED"]], "nodriver.cdp.preload.PrefetchStatusUpdated": [[41, 1, 1, "", "initiating_frame_id"], [41, 1, 1, "", "key"], [41, 1, 1, "", "pipeline_id"], [41, 1, 1, "", "prefetch_status"], [41, 1, 1, "", "prefetch_url"], [41, 1, 1, "", "request_id"], [41, 1, 1, "", "status"]], "nodriver.cdp.preload.PreloadEnabledStateUpdated": [[41, 1, 1, "", "disabled_by_battery_saver"], [41, 1, 1, "", "disabled_by_data_saver"], [41, 1, 1, "", "disabled_by_holdback_prefetch_speculation_rules"], [41, 1, 1, "", "disabled_by_holdback_prerender_speculation_rules"], [41, 1, 1, "", "disabled_by_preference"]], "nodriver.cdp.preload.PreloadingAttemptKey": [[41, 1, 1, "", "action"], [41, 1, 1, "", "loader_id"], [41, 1, 1, "", "target_hint"], [41, 1, 1, "", "url"]], "nodriver.cdp.preload.PreloadingAttemptSource": [[41, 1, 1, "", "key"], [41, 1, 1, "", "node_ids"], [41, 1, 1, "", "rule_set_ids"]], "nodriver.cdp.preload.PreloadingAttemptSourcesUpdated": [[41, 1, 1, "", "loader_id"], [41, 1, 1, "", "preloading_attempt_sources"]], "nodriver.cdp.preload.PreloadingStatus": [[41, 1, 1, "", "FAILURE"], [41, 1, 1, "", "NOT_SUPPORTED"], [41, 1, 1, "", "PENDING"], [41, 1, 1, "", "READY"], [41, 1, 1, "", "RUNNING"], [41, 1, 1, "", "SUCCESS"]], "nodriver.cdp.preload.PrerenderFinalStatus": [[41, 1, 1, "", "ACTIVATED"], [41, 1, 1, "", "ACTIVATED_BEFORE_STARTED"], [41, 1, 1, "", "ACTIVATED_DURING_MAIN_FRAME_NAVIGATION"], [41, 1, 1, "", "ACTIVATED_IN_BACKGROUND"], [41, 1, 1, "", "ACTIVATED_WITH_AUXILIARY_BROWSING_CONTEXTS"], [41, 1, 1, "", "ACTIVATION_FRAME_POLICY_NOT_COMPATIBLE"], [41, 1, 1, "", "ACTIVATION_NAVIGATION_DESTROYED_BEFORE_SUCCESS"], [41, 1, 1, "", "ACTIVATION_NAVIGATION_PARAMETER_MISMATCH"], [41, 1, 1, "", "ACTIVATION_URL_HAS_EFFECTIVE_URL"], [41, 1, 1, "", "ALL_PRERENDERING_CANCELED"], [41, 1, 1, "", "AUDIO_OUTPUT_DEVICE_REQUESTED"], [41, 1, 1, "", "BATTERY_SAVER_ENABLED"], [41, 1, 1, "", "BLOCKED_BY_CLIENT"], [41, 1, 1, "", "BROWSING_DATA_REMOVED"], [41, 1, 1, "", "CANCEL_ALL_HOSTS_FOR_TESTING"], [41, 1, 1, "", "CLIENT_CERT_REQUESTED"], [41, 1, 1, "", "CROSS_SITE_NAVIGATION_IN_INITIAL_NAVIGATION"], [41, 1, 1, "", "CROSS_SITE_NAVIGATION_IN_MAIN_FRAME_NAVIGATION"], [41, 1, 1, "", "CROSS_SITE_REDIRECT_IN_INITIAL_NAVIGATION"], [41, 1, 1, "", "CROSS_SITE_REDIRECT_IN_MAIN_FRAME_NAVIGATION"], [41, 1, 1, "", "DATA_SAVER_ENABLED"], [41, 1, 1, "", "DESTROYED"], [41, 1, 1, "", "DID_FAIL_LOAD"], [41, 1, 1, "", "DOWNLOAD"], [41, 1, 1, "", "EMBEDDER_HOST_DISALLOWED"], [41, 1, 1, "", "INACTIVE_PAGE_RESTRICTION"], [41, 1, 1, "", "INVALID_SCHEME_NAVIGATION"], [41, 1, 1, "", "INVALID_SCHEME_REDIRECT"], [41, 1, 1, "", "JAVA_SCRIPT_INTERFACE_ADDED"], [41, 1, 1, "", "JAVA_SCRIPT_INTERFACE_REMOVED"], [41, 1, 1, "", "LOGIN_AUTH_REQUESTED"], [41, 1, 1, "", "LOW_END_DEVICE"], [41, 1, 1, "", "MAIN_FRAME_NAVIGATION"], [41, 1, 1, "", "MAX_NUM_OF_RUNNING_EAGER_PRERENDERS_EXCEEDED"], [41, 1, 1, "", "MAX_NUM_OF_RUNNING_EMBEDDER_PRERENDERS_EXCEEDED"], [41, 1, 1, "", "MAX_NUM_OF_RUNNING_NON_EAGER_PRERENDERS_EXCEEDED"], [41, 1, 1, "", "MEMORY_LIMIT_EXCEEDED"], [41, 1, 1, "", "MEMORY_PRESSURE_AFTER_TRIGGERED"], [41, 1, 1, "", "MEMORY_PRESSURE_ON_TRIGGER"], [41, 1, 1, "", "MIXED_CONTENT"], [41, 1, 1, "", "MOJO_BINDER_POLICY"], [41, 1, 1, "", "NAVIGATION_BAD_HTTP_STATUS"], [41, 1, 1, "", "NAVIGATION_NOT_COMMITTED"], [41, 1, 1, "", "NAVIGATION_REQUEST_BLOCKED_BY_CSP"], [41, 1, 1, "", "NAVIGATION_REQUEST_NETWORK_ERROR"], [41, 1, 1, "", "OTHER_PRERENDERED_PAGE_ACTIVATED"], [41, 1, 1, "", "PRELOADING_DISABLED"], [41, 1, 1, "", "PRELOADING_UNSUPPORTED_BY_WEB_CONTENTS"], [41, 1, 1, "", "PRERENDERING_DISABLED_BY_DEV_TOOLS"], [41, 1, 1, "", "PRERENDERING_URL_HAS_EFFECTIVE_URL"], [41, 1, 1, "", "PRERENDER_FAILED_DURING_PREFETCH"], [41, 1, 1, "", "PRIMARY_MAIN_FRAME_RENDERER_PROCESS_CRASHED"], [41, 1, 1, "", "PRIMARY_MAIN_FRAME_RENDERER_PROCESS_KILLED"], [41, 1, 1, "", "REDIRECTED_PRERENDERING_URL_HAS_EFFECTIVE_URL"], [41, 1, 1, "", "RENDERER_PROCESS_CRASHED"], [41, 1, 1, "", "RENDERER_PROCESS_KILLED"], [41, 1, 1, "", "SAME_SITE_CROSS_ORIGIN_NAVIGATION_NOT_OPT_IN_IN_INITIAL_NAVIGATION"], [41, 1, 1, "", "SAME_SITE_CROSS_ORIGIN_NAVIGATION_NOT_OPT_IN_IN_MAIN_FRAME_NAVIGATION"], [41, 1, 1, "", "SAME_SITE_CROSS_ORIGIN_REDIRECT_NOT_OPT_IN_IN_INITIAL_NAVIGATION"], [41, 1, 1, "", "SAME_SITE_CROSS_ORIGIN_REDIRECT_NOT_OPT_IN_IN_MAIN_FRAME_NAVIGATION"], [41, 1, 1, "", "SLOW_NETWORK"], [41, 1, 1, "", "SPECULATION_RULE_REMOVED"], [41, 1, 1, "", "SSL_CERTIFICATE_ERROR"], [41, 1, 1, "", "START_FAILED"], [41, 1, 1, "", "STOP"], [41, 1, 1, "", "TAB_CLOSED_BY_USER_GESTURE"], [41, 1, 1, "", "TAB_CLOSED_WITHOUT_USER_GESTURE"], [41, 1, 1, "", "TIMEOUT_BACKGROUNDED"], [41, 1, 1, "", "TRIGGER_BACKGROUNDED"], [41, 1, 1, "", "TRIGGER_DESTROYED"], [41, 1, 1, "", "TRIGGER_URL_HAS_EFFECTIVE_URL"], [41, 1, 1, "", "UA_CHANGE_REQUIRES_RELOAD"], [41, 1, 1, "", "V8_OPTIMIZER_DISABLED"], [41, 1, 1, "", "WINDOW_CLOSED"]], "nodriver.cdp.preload.PrerenderMismatchedHeaders": [[41, 1, 1, "", "activation_value"], [41, 1, 1, "", "header_name"], [41, 1, 1, "", "initial_value"]], "nodriver.cdp.preload.PrerenderStatusUpdated": [[41, 1, 1, "", "disallowed_mojo_interface"], [41, 1, 1, "", "key"], [41, 1, 1, "", "mismatched_headers"], [41, 1, 1, "", "pipeline_id"], [41, 1, 1, "", "prerender_status"], [41, 1, 1, "", "status"]], "nodriver.cdp.preload.RuleSet": [[41, 1, 1, "", "backend_node_id"], [41, 1, 1, "", "error_message"], [41, 1, 1, "", "error_type"], [41, 1, 1, "", "id_"], [41, 1, 1, "", "loader_id"], [41, 1, 1, "", "request_id"], [41, 1, 1, "", "source_text"], [41, 1, 1, "", "url"]], "nodriver.cdp.preload.RuleSetErrorType": [[41, 1, 1, "", "INVALID_RULES_SKIPPED"], [41, 1, 1, "", "SOURCE_IS_NOT_JSON_OBJECT"]], "nodriver.cdp.preload.RuleSetRemoved": [[41, 1, 1, "", "id_"]], "nodriver.cdp.preload.RuleSetUpdated": [[41, 1, 1, "", "rule_set"]], "nodriver.cdp.preload.SpeculationAction": [[41, 1, 1, "", "PREFETCH"], [41, 1, 1, "", "PRERENDER"]], "nodriver.cdp.preload.SpeculationTargetHint": [[41, 1, 1, "", "BLANK"], [41, 1, 1, "", "SELF"]], "nodriver.cdp.profiler": [[42, 0, 1, "", "ConsoleProfileFinished"], [42, 0, 1, "", "ConsoleProfileStarted"], [42, 0, 1, "", "CoverageRange"], [42, 0, 1, "", "FunctionCoverage"], [42, 0, 1, "", "PositionTickInfo"], [42, 0, 1, "", "PreciseCoverageDeltaUpdate"], [42, 0, 1, "", "Profile"], [42, 0, 1, "", "ProfileNode"], [42, 0, 1, "", "ScriptCoverage"], [42, 5, 1, "", "disable"], [42, 5, 1, "", "enable"], [42, 5, 1, "", "get_best_effort_coverage"], [42, 5, 1, "", "set_sampling_interval"], [42, 5, 1, "", "start"], [42, 5, 1, "", "start_precise_coverage"], [42, 5, 1, "", "stop"], [42, 5, 1, "", "stop_precise_coverage"], [42, 5, 1, "", "take_precise_coverage"]], "nodriver.cdp.profiler.ConsoleProfileFinished": [[42, 1, 1, "", "id_"], [42, 1, 1, "", "location"], [42, 1, 1, "", "profile"], [42, 1, 1, "", "title"]], "nodriver.cdp.profiler.ConsoleProfileStarted": [[42, 1, 1, "", "id_"], [42, 1, 1, "", "location"], [42, 1, 1, "", "title"]], "nodriver.cdp.profiler.CoverageRange": [[42, 1, 1, "", "count"], [42, 1, 1, "", "end_offset"], [42, 1, 1, "", "start_offset"]], "nodriver.cdp.profiler.FunctionCoverage": [[42, 1, 1, "", "function_name"], [42, 1, 1, "", "is_block_coverage"], [42, 1, 1, "", "ranges"]], "nodriver.cdp.profiler.PositionTickInfo": [[42, 1, 1, "", "line"], [42, 1, 1, "", "ticks"]], "nodriver.cdp.profiler.PreciseCoverageDeltaUpdate": [[42, 1, 1, "", "occasion"], [42, 1, 1, "", "result"], [42, 1, 1, "", "timestamp"]], "nodriver.cdp.profiler.Profile": [[42, 1, 1, "", "end_time"], [42, 1, 1, "", "nodes"], [42, 1, 1, "", "samples"], [42, 1, 1, "", "start_time"], [42, 1, 1, "", "time_deltas"]], "nodriver.cdp.profiler.ProfileNode": [[42, 1, 1, "", "call_frame"], [42, 1, 1, "", "children"], [42, 1, 1, "", "deopt_reason"], [42, 1, 1, "", "hit_count"], [42, 1, 1, "", "id_"], [42, 1, 1, "", "position_ticks"]], "nodriver.cdp.profiler.ScriptCoverage": [[42, 1, 1, "", "functions"], [42, 1, 1, "", "script_id"], [42, 1, 1, "", "url"]], "nodriver.cdp.pwa": [[43, 0, 1, "", "DisplayMode"], [43, 0, 1, "", "FileHandler"], [43, 0, 1, "", "FileHandlerAccept"], [43, 5, 1, "", "change_app_user_settings"], [43, 5, 1, "", "get_os_app_state"], [43, 5, 1, "", "install"], [43, 5, 1, "", "launch"], [43, 5, 1, "", "launch_files_in_app"], [43, 5, 1, "", "open_current_page_in_app"], [43, 5, 1, "", "uninstall"]], "nodriver.cdp.pwa.DisplayMode": [[43, 1, 1, "", "BROWSER"], [43, 1, 1, "", "STANDALONE"]], "nodriver.cdp.pwa.FileHandler": [[43, 1, 1, "", "accepts"], [43, 1, 1, "", "action"], [43, 1, 1, "", "display_name"]], "nodriver.cdp.pwa.FileHandlerAccept": [[43, 1, 1, "", "file_extensions"], [43, 1, 1, "", "media_type"]], "nodriver.cdp.runtime": [[44, 0, 1, "", "BindingCalled"], [44, 0, 1, "", "CallArgument"], [44, 0, 1, "", "CallFrame"], [44, 0, 1, "", "ConsoleAPICalled"], [44, 0, 1, "", "CustomPreview"], [44, 0, 1, "", "DeepSerializedValue"], [44, 0, 1, "", "EntryPreview"], [44, 0, 1, "", "ExceptionDetails"], [44, 0, 1, "", "ExceptionRevoked"], [44, 0, 1, "", "ExceptionThrown"], [44, 0, 1, "", "ExecutionContextCreated"], [44, 0, 1, "", "ExecutionContextDescription"], [44, 0, 1, "", "ExecutionContextDestroyed"], [44, 0, 1, "", "ExecutionContextId"], [44, 0, 1, "", "ExecutionContextsCleared"], [44, 0, 1, "", "InspectRequested"], [44, 0, 1, "", "InternalPropertyDescriptor"], [44, 0, 1, "", "ObjectPreview"], [44, 0, 1, "", "PrivatePropertyDescriptor"], [44, 0, 1, "", "PropertyDescriptor"], [44, 0, 1, "", "PropertyPreview"], [44, 0, 1, "", "RemoteObject"], [44, 0, 1, "", "RemoteObjectId"], [44, 0, 1, "", "ScriptId"], [44, 0, 1, "", "SerializationOptions"], [44, 0, 1, "", "StackTrace"], [44, 0, 1, "", "StackTraceId"], [44, 0, 1, "", "TimeDelta"], [44, 0, 1, "", "Timestamp"], [44, 0, 1, "", "UniqueDebuggerId"], [44, 0, 1, "", "UnserializableValue"], [44, 5, 1, "", "add_binding"], [44, 5, 1, "", "await_promise"], [44, 5, 1, "", "call_function_on"], [44, 5, 1, "", "compile_script"], [44, 5, 1, "", "disable"], [44, 5, 1, "", "discard_console_entries"], [44, 5, 1, "", "enable"], [44, 5, 1, "", "evaluate"], [44, 5, 1, "", "get_exception_details"], [44, 5, 1, "", "get_heap_usage"], [44, 5, 1, "", "get_isolate_id"], [44, 5, 1, "", "get_properties"], [44, 5, 1, "", "global_lexical_scope_names"], [44, 5, 1, "", "query_objects"], [44, 5, 1, "", "release_object"], [44, 5, 1, "", "release_object_group"], [44, 5, 1, "", "remove_binding"], [44, 5, 1, "", "run_if_waiting_for_debugger"], [44, 5, 1, "", "run_script"], [44, 5, 1, "", "set_async_call_stack_depth"], [44, 5, 1, "", "set_custom_object_formatter_enabled"], [44, 5, 1, "", "set_max_call_stack_size_to_capture"], [44, 5, 1, "", "terminate_execution"]], "nodriver.cdp.runtime.BindingCalled": [[44, 1, 1, "", "execution_context_id"], [44, 1, 1, "", "name"], [44, 1, 1, "", "payload"]], "nodriver.cdp.runtime.CallArgument": [[44, 1, 1, "", "object_id"], [44, 1, 1, "", "unserializable_value"], [44, 1, 1, "", "value"]], "nodriver.cdp.runtime.CallFrame": [[44, 1, 1, "", "column_number"], [44, 1, 1, "", "function_name"], [44, 1, 1, "", "line_number"], [44, 1, 1, "", "script_id"], [44, 1, 1, "", "url"]], "nodriver.cdp.runtime.ConsoleAPICalled": [[44, 1, 1, "", "args"], [44, 1, 1, "", "context"], [44, 1, 1, "", "execution_context_id"], [44, 1, 1, "", "stack_trace"], [44, 1, 1, "", "timestamp"], [44, 1, 1, "", "type_"]], "nodriver.cdp.runtime.CustomPreview": [[44, 1, 1, "", "body_getter_id"], [44, 1, 1, "", "header"]], "nodriver.cdp.runtime.DeepSerializedValue": [[44, 1, 1, "", "object_id"], [44, 1, 1, "", "type_"], [44, 1, 1, "", "value"], [44, 1, 1, "", "weak_local_object_reference"]], "nodriver.cdp.runtime.EntryPreview": [[44, 1, 1, "", "key"], [44, 1, 1, "", "value"]], "nodriver.cdp.runtime.ExceptionDetails": [[44, 1, 1, "", "column_number"], [44, 1, 1, "", "exception"], [44, 1, 1, "", "exception_id"], [44, 1, 1, "", "exception_meta_data"], [44, 1, 1, "", "execution_context_id"], [44, 1, 1, "", "line_number"], [44, 1, 1, "", "script_id"], [44, 1, 1, "", "stack_trace"], [44, 1, 1, "", "text"], [44, 1, 1, "", "url"]], "nodriver.cdp.runtime.ExceptionRevoked": [[44, 1, 1, "", "exception_id"], [44, 1, 1, "", "reason"]], "nodriver.cdp.runtime.ExceptionThrown": [[44, 1, 1, "", "exception_details"], [44, 1, 1, "", "timestamp"]], "nodriver.cdp.runtime.ExecutionContextCreated": [[44, 1, 1, "", "context"]], "nodriver.cdp.runtime.ExecutionContextDescription": [[44, 1, 1, "", "aux_data"], [44, 1, 1, "", "id_"], [44, 1, 1, "", "name"], [44, 1, 1, "", "origin"], [44, 1, 1, "", "unique_id"]], "nodriver.cdp.runtime.ExecutionContextDestroyed": [[44, 1, 1, "", "execution_context_id"], [44, 1, 1, "", "execution_context_unique_id"]], "nodriver.cdp.runtime.InspectRequested": [[44, 1, 1, "", "execution_context_id"], [44, 1, 1, "", "hints"], [44, 1, 1, "", "object_"]], "nodriver.cdp.runtime.InternalPropertyDescriptor": [[44, 1, 1, "", "name"], [44, 1, 1, "", "value"]], "nodriver.cdp.runtime.ObjectPreview": [[44, 1, 1, "", "description"], [44, 1, 1, "", "entries"], [44, 1, 1, "", "overflow"], [44, 1, 1, "", "properties"], [44, 1, 1, "", "subtype"], [44, 1, 1, "", "type_"]], "nodriver.cdp.runtime.PrivatePropertyDescriptor": [[44, 1, 1, "", "get"], [44, 1, 1, "", "name"], [44, 1, 1, "", "set_"], [44, 1, 1, "", "value"]], "nodriver.cdp.runtime.PropertyDescriptor": [[44, 1, 1, "", "configurable"], [44, 1, 1, "", "enumerable"], [44, 1, 1, "", "get"], [44, 1, 1, "", "is_own"], [44, 1, 1, "", "name"], [44, 1, 1, "", "set_"], [44, 1, 1, "", "symbol"], [44, 1, 1, "", "value"], [44, 1, 1, "", "was_thrown"], [44, 1, 1, "", "writable"]], "nodriver.cdp.runtime.PropertyPreview": [[44, 1, 1, "", "name"], [44, 1, 1, "", "subtype"], [44, 1, 1, "", "type_"], [44, 1, 1, "", "value"], [44, 1, 1, "", "value_preview"]], "nodriver.cdp.runtime.RemoteObject": [[44, 1, 1, "", "class_name"], [44, 1, 1, "", "custom_preview"], [44, 1, 1, "", "deep_serialized_value"], [44, 1, 1, "", "description"], [44, 1, 1, "", "object_id"], [44, 1, 1, "", "preview"], [44, 1, 1, "", "subtype"], [44, 1, 1, "", "type_"], [44, 1, 1, "", "unserializable_value"], [44, 1, 1, "", "value"]], "nodriver.cdp.runtime.SerializationOptions": [[44, 1, 1, "", "additional_parameters"], [44, 1, 1, "", "max_depth"], [44, 1, 1, "", "serialization"]], "nodriver.cdp.runtime.StackTrace": [[44, 1, 1, "", "call_frames"], [44, 1, 1, "", "description"], [44, 1, 1, "", "parent"], [44, 1, 1, "", "parent_id"]], "nodriver.cdp.runtime.StackTraceId": [[44, 1, 1, "", "debugger_id"], [44, 1, 1, "", "id_"]], "nodriver.cdp.schema": [[45, 0, 1, "", "Domain"], [45, 5, 1, "", "get_domains"]], "nodriver.cdp.schema.Domain": [[45, 1, 1, "", "name"], [45, 1, 1, "", "version"]], "nodriver.cdp.security": [[46, 0, 1, "", "CertificateError"], [46, 0, 1, "", "CertificateErrorAction"], [46, 0, 1, "", "CertificateId"], [46, 0, 1, "", "CertificateSecurityState"], [46, 0, 1, "", "InsecureContentStatus"], [46, 0, 1, "", "MixedContentType"], [46, 0, 1, "", "SafetyTipInfo"], [46, 0, 1, "", "SafetyTipStatus"], [46, 0, 1, "", "SecurityState"], [46, 0, 1, "", "SecurityStateChanged"], [46, 0, 1, "", "SecurityStateExplanation"], [46, 0, 1, "", "VisibleSecurityState"], [46, 0, 1, "", "VisibleSecurityStateChanged"], [46, 5, 1, "", "disable"], [46, 5, 1, "", "enable"], [46, 5, 1, "", "handle_certificate_error"], [46, 5, 1, "", "set_ignore_certificate_errors"], [46, 5, 1, "", "set_override_certificate_errors"]], "nodriver.cdp.security.CertificateError": [[46, 1, 1, "", "error_type"], [46, 1, 1, "", "event_id"], [46, 1, 1, "", "request_url"]], "nodriver.cdp.security.CertificateErrorAction": [[46, 1, 1, "", "CANCEL"], [46, 1, 1, "", "CONTINUE"]], "nodriver.cdp.security.CertificateSecurityState": [[46, 1, 1, "", "certificate"], [46, 1, 1, "", "certificate_has_sha1_signature"], [46, 1, 1, "", "certificate_has_weak_signature"], [46, 1, 1, "", "certificate_network_error"], [46, 1, 1, "", "cipher"], [46, 1, 1, "", "issuer"], [46, 1, 1, "", "key_exchange"], [46, 1, 1, "", "key_exchange_group"], [46, 1, 1, "", "mac"], [46, 1, 1, "", "modern_ssl"], [46, 1, 1, "", "obsolete_ssl_cipher"], [46, 1, 1, "", "obsolete_ssl_key_exchange"], [46, 1, 1, "", "obsolete_ssl_protocol"], [46, 1, 1, "", "obsolete_ssl_signature"], [46, 1, 1, "", "protocol"], [46, 1, 1, "", "subject_name"], [46, 1, 1, "", "valid_from"], [46, 1, 1, "", "valid_to"]], "nodriver.cdp.security.InsecureContentStatus": [[46, 1, 1, "", "contained_mixed_form"], [46, 1, 1, "", "displayed_content_with_cert_errors"], [46, 1, 1, "", "displayed_insecure_content_style"], [46, 1, 1, "", "displayed_mixed_content"], [46, 1, 1, "", "ran_content_with_cert_errors"], [46, 1, 1, "", "ran_insecure_content_style"], [46, 1, 1, "", "ran_mixed_content"]], "nodriver.cdp.security.MixedContentType": [[46, 1, 1, "", "BLOCKABLE"], [46, 1, 1, "", "NONE"], [46, 1, 1, "", "OPTIONALLY_BLOCKABLE"]], "nodriver.cdp.security.SafetyTipInfo": [[46, 1, 1, "", "safe_url"], [46, 1, 1, "", "safety_tip_status"]], "nodriver.cdp.security.SafetyTipStatus": [[46, 1, 1, "", "BAD_REPUTATION"], [46, 1, 1, "", "LOOKALIKE"]], "nodriver.cdp.security.SecurityState": [[46, 1, 1, "", "INFO"], [46, 1, 1, "", "INSECURE"], [46, 1, 1, "", "INSECURE_BROKEN"], [46, 1, 1, "", "NEUTRAL"], [46, 1, 1, "", "SECURE"], [46, 1, 1, "", "UNKNOWN"]], "nodriver.cdp.security.SecurityStateChanged": [[46, 1, 1, "", "explanations"], [46, 1, 1, "", "insecure_content_status"], [46, 1, 1, "", "scheme_is_cryptographic"], [46, 1, 1, "", "security_state"], [46, 1, 1, "", "summary"]], "nodriver.cdp.security.SecurityStateExplanation": [[46, 1, 1, "", "certificate"], [46, 1, 1, "", "description"], [46, 1, 1, "", "mixed_content_type"], [46, 1, 1, "", "recommendations"], [46, 1, 1, "", "security_state"], [46, 1, 1, "", "summary"], [46, 1, 1, "", "title"]], "nodriver.cdp.security.VisibleSecurityState": [[46, 1, 1, "", "certificate_security_state"], [46, 1, 1, "", "safety_tip_info"], [46, 1, 1, "", "security_state"], [46, 1, 1, "", "security_state_issue_ids"]], "nodriver.cdp.security.VisibleSecurityStateChanged": [[46, 1, 1, "", "visible_security_state"]], "nodriver.cdp.service_worker": [[47, 0, 1, "", "RegistrationID"], [47, 0, 1, "", "ServiceWorkerErrorMessage"], [47, 0, 1, "", "ServiceWorkerRegistration"], [47, 0, 1, "", "ServiceWorkerVersion"], [47, 0, 1, "", "ServiceWorkerVersionRunningStatus"], [47, 0, 1, "", "ServiceWorkerVersionStatus"], [47, 0, 1, "", "WorkerErrorReported"], [47, 0, 1, "", "WorkerRegistrationUpdated"], [47, 0, 1, "", "WorkerVersionUpdated"], [47, 5, 1, "", "deliver_push_message"], [47, 5, 1, "", "disable"], [47, 5, 1, "", "dispatch_periodic_sync_event"], [47, 5, 1, "", "dispatch_sync_event"], [47, 5, 1, "", "enable"], [47, 5, 1, "", "inspect_worker"], [47, 5, 1, "", "set_force_update_on_page_load"], [47, 5, 1, "", "skip_waiting"], [47, 5, 1, "", "start_worker"], [47, 5, 1, "", "stop_all_workers"], [47, 5, 1, "", "stop_worker"], [47, 5, 1, "", "unregister"], [47, 5, 1, "", "update_registration"]], "nodriver.cdp.service_worker.ServiceWorkerErrorMessage": [[47, 1, 1, "", "column_number"], [47, 1, 1, "", "error_message"], [47, 1, 1, "", "line_number"], [47, 1, 1, "", "registration_id"], [47, 1, 1, "", "source_url"], [47, 1, 1, "", "version_id"]], "nodriver.cdp.service_worker.ServiceWorkerRegistration": [[47, 1, 1, "", "is_deleted"], [47, 1, 1, "", "registration_id"], [47, 1, 1, "", "scope_url"]], "nodriver.cdp.service_worker.ServiceWorkerVersion": [[47, 1, 1, "", "controlled_clients"], [47, 1, 1, "", "registration_id"], [47, 1, 1, "", "router_rules"], [47, 1, 1, "", "running_status"], [47, 1, 1, "", "script_last_modified"], [47, 1, 1, "", "script_response_time"], [47, 1, 1, "", "script_url"], [47, 1, 1, "", "status"], [47, 1, 1, "", "target_id"], [47, 1, 1, "", "version_id"]], "nodriver.cdp.service_worker.ServiceWorkerVersionRunningStatus": [[47, 1, 1, "", "RUNNING"], [47, 1, 1, "", "STARTING"], [47, 1, 1, "", "STOPPED"], [47, 1, 1, "", "STOPPING"]], "nodriver.cdp.service_worker.ServiceWorkerVersionStatus": [[47, 1, 1, "", "ACTIVATED"], [47, 1, 1, "", "ACTIVATING"], [47, 1, 1, "", "INSTALLED"], [47, 1, 1, "", "INSTALLING"], [47, 1, 1, "", "NEW"], [47, 1, 1, "", "REDUNDANT"]], "nodriver.cdp.service_worker.WorkerErrorReported": [[47, 1, 1, "", "error_message"]], "nodriver.cdp.service_worker.WorkerRegistrationUpdated": [[47, 1, 1, "", "registrations"]], "nodriver.cdp.service_worker.WorkerVersionUpdated": [[47, 1, 1, "", "versions"]], "nodriver.cdp.storage": [[48, 0, 1, "", "AttributionReportingAggregatableDebugReportingConfig"], [48, 0, 1, "", "AttributionReportingAggregatableDebugReportingData"], [48, 0, 1, "", "AttributionReportingAggregatableDedupKey"], [48, 0, 1, "", "AttributionReportingAggregatableResult"], [48, 0, 1, "", "AttributionReportingAggregatableTriggerData"], [48, 0, 1, "", "AttributionReportingAggregatableValueDictEntry"], [48, 0, 1, "", "AttributionReportingAggregatableValueEntry"], [48, 0, 1, "", "AttributionReportingAggregationKeysEntry"], [48, 0, 1, "", "AttributionReportingEventLevelResult"], [48, 0, 1, "", "AttributionReportingEventReportWindows"], [48, 0, 1, "", "AttributionReportingEventTriggerData"], [48, 0, 1, "", "AttributionReportingFilterConfig"], [48, 0, 1, "", "AttributionReportingFilterDataEntry"], [48, 0, 1, "", "AttributionReportingFilterPair"], [48, 0, 1, "", "AttributionReportingSourceRegistered"], [48, 0, 1, "", "AttributionReportingSourceRegistration"], [48, 0, 1, "", "AttributionReportingSourceRegistrationResult"], [48, 0, 1, "", "AttributionReportingSourceRegistrationTimeConfig"], [48, 0, 1, "", "AttributionReportingSourceType"], [48, 0, 1, "", "AttributionReportingTriggerDataMatching"], [48, 0, 1, "", "AttributionReportingTriggerRegistered"], [48, 0, 1, "", "AttributionReportingTriggerRegistration"], [48, 0, 1, "", "AttributionReportingTriggerSpec"], [48, 0, 1, "", "AttributionScopesData"], [48, 0, 1, "", "CacheStorageContentUpdated"], [48, 0, 1, "", "CacheStorageListUpdated"], [48, 0, 1, "", "IndexedDBContentUpdated"], [48, 0, 1, "", "IndexedDBListUpdated"], [48, 0, 1, "", "InterestGroupAccessType"], [48, 0, 1, "", "InterestGroupAccessed"], [48, 0, 1, "", "InterestGroupAuctionEventOccurred"], [48, 0, 1, "", "InterestGroupAuctionEventType"], [48, 0, 1, "", "InterestGroupAuctionFetchType"], [48, 0, 1, "", "InterestGroupAuctionId"], [48, 0, 1, "", "InterestGroupAuctionNetworkRequestCreated"], [48, 0, 1, "", "RelatedWebsiteSet"], [48, 0, 1, "", "SerializedStorageKey"], [48, 0, 1, "", "SharedStorageAccessParams"], [48, 0, 1, "", "SharedStorageAccessType"], [48, 0, 1, "", "SharedStorageAccessed"], [48, 0, 1, "", "SharedStorageEntry"], [48, 0, 1, "", "SharedStorageMetadata"], [48, 0, 1, "", "SharedStorageReportingMetadata"], [48, 0, 1, "", "SharedStorageUrlWithMetadata"], [48, 0, 1, "", "SignedInt64AsBase10"], [48, 0, 1, "", "StorageBucket"], [48, 0, 1, "", "StorageBucketCreatedOrUpdated"], [48, 0, 1, "", "StorageBucketDeleted"], [48, 0, 1, "", "StorageBucketInfo"], [48, 0, 1, "", "StorageBucketsDurability"], [48, 0, 1, "", "StorageType"], [48, 0, 1, "", "TrustTokens"], [48, 0, 1, "", "UnsignedInt128AsBase16"], [48, 0, 1, "", "UnsignedInt64AsBase10"], [48, 0, 1, "", "UsageForType"], [48, 5, 1, "", "clear_cookies"], [48, 5, 1, "", "clear_data_for_origin"], [48, 5, 1, "", "clear_data_for_storage_key"], [48, 5, 1, "", "clear_shared_storage_entries"], [48, 5, 1, "", "clear_trust_tokens"], [48, 5, 1, "", "delete_shared_storage_entry"], [48, 5, 1, "", "delete_storage_bucket"], [48, 5, 1, "", "get_affected_urls_for_third_party_cookie_metadata"], [48, 5, 1, "", "get_cookies"], [48, 5, 1, "", "get_interest_group_details"], [48, 5, 1, "", "get_related_website_sets"], [48, 5, 1, "", "get_shared_storage_entries"], [48, 5, 1, "", "get_shared_storage_metadata"], [48, 5, 1, "", "get_storage_key_for_frame"], [48, 5, 1, "", "get_trust_tokens"], [48, 5, 1, "", "get_usage_and_quota"], [48, 5, 1, "", "override_quota_for_origin"], [48, 5, 1, "", "reset_shared_storage_budget"], [48, 5, 1, "", "run_bounce_tracking_mitigations"], [48, 5, 1, "", "send_pending_attribution_reports"], [48, 5, 1, "", "set_attribution_reporting_local_testing_mode"], [48, 5, 1, "", "set_attribution_reporting_tracking"], [48, 5, 1, "", "set_cookies"], [48, 5, 1, "", "set_interest_group_auction_tracking"], [48, 5, 1, "", "set_interest_group_tracking"], [48, 5, 1, "", "set_shared_storage_entry"], [48, 5, 1, "", "set_shared_storage_tracking"], [48, 5, 1, "", "set_storage_bucket_tracking"], [48, 5, 1, "", "track_cache_storage_for_origin"], [48, 5, 1, "", "track_cache_storage_for_storage_key"], [48, 5, 1, "", "track_indexed_db_for_origin"], [48, 5, 1, "", "track_indexed_db_for_storage_key"], [48, 5, 1, "", "untrack_cache_storage_for_origin"], [48, 5, 1, "", "untrack_cache_storage_for_storage_key"], [48, 5, 1, "", "untrack_indexed_db_for_origin"], [48, 5, 1, "", "untrack_indexed_db_for_storage_key"]], "nodriver.cdp.storage.AttributionReportingAggregatableDebugReportingConfig": [[48, 1, 1, "", "aggregation_coordinator_origin"], [48, 1, 1, "", "budget"], [48, 1, 1, "", "debug_data"], [48, 1, 1, "", "key_piece"]], "nodriver.cdp.storage.AttributionReportingAggregatableDebugReportingData": [[48, 1, 1, "", "key_piece"], [48, 1, 1, "", "types"], [48, 1, 1, "", "value"]], "nodriver.cdp.storage.AttributionReportingAggregatableDedupKey": [[48, 1, 1, "", "dedup_key"], [48, 1, 1, "", "filters"]], "nodriver.cdp.storage.AttributionReportingAggregatableResult": [[48, 1, 1, "", "DEDUPLICATED"], [48, 1, 1, "", "EXCESSIVE_ATTRIBUTIONS"], [48, 1, 1, "", "EXCESSIVE_REPORTING_ORIGINS"], [48, 1, 1, "", "EXCESSIVE_REPORTS"], [48, 1, 1, "", "INSUFFICIENT_BUDGET"], [48, 1, 1, "", "INSUFFICIENT_NAMED_BUDGET"], [48, 1, 1, "", "INTERNAL_ERROR"], [48, 1, 1, "", "NOT_REGISTERED"], [48, 1, 1, "", "NO_CAPACITY_FOR_ATTRIBUTION_DESTINATION"], [48, 1, 1, "", "NO_HISTOGRAMS"], [48, 1, 1, "", "NO_MATCHING_SOURCES"], [48, 1, 1, "", "NO_MATCHING_SOURCE_FILTER_DATA"], [48, 1, 1, "", "PROHIBITED_BY_BROWSER_POLICY"], [48, 1, 1, "", "REPORT_WINDOW_PASSED"], [48, 1, 1, "", "SUCCESS"]], "nodriver.cdp.storage.AttributionReportingAggregatableTriggerData": [[48, 1, 1, "", "filters"], [48, 1, 1, "", "key_piece"], [48, 1, 1, "", "source_keys"]], "nodriver.cdp.storage.AttributionReportingAggregatableValueDictEntry": [[48, 1, 1, "", "filtering_id"], [48, 1, 1, "", "key"], [48, 1, 1, "", "value"]], "nodriver.cdp.storage.AttributionReportingAggregatableValueEntry": [[48, 1, 1, "", "filters"], [48, 1, 1, "", "values"]], "nodriver.cdp.storage.AttributionReportingAggregationKeysEntry": [[48, 1, 1, "", "key"], [48, 1, 1, "", "value"]], "nodriver.cdp.storage.AttributionReportingEventLevelResult": [[48, 1, 1, "", "DEDUPLICATED"], [48, 1, 1, "", "EXCESSIVE_ATTRIBUTIONS"], [48, 1, 1, "", "EXCESSIVE_REPORTING_ORIGINS"], [48, 1, 1, "", "EXCESSIVE_REPORTS"], [48, 1, 1, "", "FALSELY_ATTRIBUTED_SOURCE"], [48, 1, 1, "", "INTERNAL_ERROR"], [48, 1, 1, "", "NEVER_ATTRIBUTED_SOURCE"], [48, 1, 1, "", "NOT_REGISTERED"], [48, 1, 1, "", "NO_CAPACITY_FOR_ATTRIBUTION_DESTINATION"], [48, 1, 1, "", "NO_MATCHING_CONFIGURATIONS"], [48, 1, 1, "", "NO_MATCHING_SOURCES"], [48, 1, 1, "", "NO_MATCHING_SOURCE_FILTER_DATA"], [48, 1, 1, "", "NO_MATCHING_TRIGGER_DATA"], [48, 1, 1, "", "PRIORITY_TOO_LOW"], [48, 1, 1, "", "PROHIBITED_BY_BROWSER_POLICY"], [48, 1, 1, "", "REPORT_WINDOW_NOT_STARTED"], [48, 1, 1, "", "REPORT_WINDOW_PASSED"], [48, 1, 1, "", "SUCCESS"], [48, 1, 1, "", "SUCCESS_DROPPED_LOWER_PRIORITY"]], "nodriver.cdp.storage.AttributionReportingEventReportWindows": [[48, 1, 1, "", "ends"], [48, 1, 1, "", "start"]], "nodriver.cdp.storage.AttributionReportingEventTriggerData": [[48, 1, 1, "", "data"], [48, 1, 1, "", "dedup_key"], [48, 1, 1, "", "filters"], [48, 1, 1, "", "priority"]], "nodriver.cdp.storage.AttributionReportingFilterConfig": [[48, 1, 1, "", "filter_values"], [48, 1, 1, "", "lookback_window"]], "nodriver.cdp.storage.AttributionReportingFilterDataEntry": [[48, 1, 1, "", "key"], [48, 1, 1, "", "values"]], "nodriver.cdp.storage.AttributionReportingFilterPair": [[48, 1, 1, "", "filters"], [48, 1, 1, "", "not_filters"]], "nodriver.cdp.storage.AttributionReportingSourceRegistered": [[48, 1, 1, "", "registration"], [48, 1, 1, "", "result"]], "nodriver.cdp.storage.AttributionReportingSourceRegistration": [[48, 1, 1, "", "aggregatable_debug_reporting_config"], [48, 1, 1, "", "aggregatable_report_window"], [48, 1, 1, "", "aggregation_keys"], [48, 1, 1, "", "debug_key"], [48, 1, 1, "", "destination_limit_priority"], [48, 1, 1, "", "destination_sites"], [48, 1, 1, "", "event_id"], [48, 1, 1, "", "expiry"], [48, 1, 1, "", "filter_data"], [48, 1, 1, "", "max_event_level_reports"], [48, 1, 1, "", "priority"], [48, 1, 1, "", "reporting_origin"], [48, 1, 1, "", "scopes_data"], [48, 1, 1, "", "source_origin"], [48, 1, 1, "", "time"], [48, 1, 1, "", "trigger_data_matching"], [48, 1, 1, "", "trigger_specs"], [48, 1, 1, "", "type_"]], "nodriver.cdp.storage.AttributionReportingSourceRegistrationResult": [[48, 1, 1, "", "DESTINATION_BOTH_LIMITS_REACHED"], [48, 1, 1, "", "DESTINATION_GLOBAL_LIMIT_REACHED"], [48, 1, 1, "", "DESTINATION_PER_DAY_REPORTING_LIMIT_REACHED"], [48, 1, 1, "", "DESTINATION_REPORTING_LIMIT_REACHED"], [48, 1, 1, "", "EXCEEDS_MAX_CHANNEL_CAPACITY"], [48, 1, 1, "", "EXCEEDS_MAX_EVENT_STATES_LIMIT"], [48, 1, 1, "", "EXCEEDS_MAX_SCOPES_CHANNEL_CAPACITY"], [48, 1, 1, "", "EXCEEDS_MAX_TRIGGER_STATE_CARDINALITY"], [48, 1, 1, "", "EXCESSIVE_REPORTING_ORIGINS"], [48, 1, 1, "", "INSUFFICIENT_SOURCE_CAPACITY"], [48, 1, 1, "", "INSUFFICIENT_UNIQUE_DESTINATION_CAPACITY"], [48, 1, 1, "", "INTERNAL_ERROR"], [48, 1, 1, "", "PROHIBITED_BY_BROWSER_POLICY"], [48, 1, 1, "", "REPORTING_ORIGINS_PER_SITE_LIMIT_REACHED"], [48, 1, 1, "", "SUCCESS"], [48, 1, 1, "", "SUCCESS_NOISED"]], "nodriver.cdp.storage.AttributionReportingSourceRegistrationTimeConfig": [[48, 1, 1, "", "EXCLUDE"], [48, 1, 1, "", "INCLUDE"]], "nodriver.cdp.storage.AttributionReportingSourceType": [[48, 1, 1, "", "EVENT"], [48, 1, 1, "", "NAVIGATION"]], "nodriver.cdp.storage.AttributionReportingTriggerDataMatching": [[48, 1, 1, "", "EXACT"], [48, 1, 1, "", "MODULUS"]], "nodriver.cdp.storage.AttributionReportingTriggerRegistered": [[48, 1, 1, "", "aggregatable"], [48, 1, 1, "", "event_level"], [48, 1, 1, "", "registration"]], "nodriver.cdp.storage.AttributionReportingTriggerRegistration": [[48, 1, 1, "", "aggregatable_debug_reporting_config"], [48, 1, 1, "", "aggregatable_dedup_keys"], [48, 1, 1, "", "aggregatable_filtering_id_max_bytes"], [48, 1, 1, "", "aggregatable_trigger_data"], [48, 1, 1, "", "aggregatable_values"], [48, 1, 1, "", "aggregation_coordinator_origin"], [48, 1, 1, "", "debug_key"], [48, 1, 1, "", "debug_reporting"], [48, 1, 1, "", "event_trigger_data"], [48, 1, 1, "", "filters"], [48, 1, 1, "", "scopes"], [48, 1, 1, "", "source_registration_time_config"], [48, 1, 1, "", "trigger_context_id"]], "nodriver.cdp.storage.AttributionReportingTriggerSpec": [[48, 1, 1, "", "event_report_windows"], [48, 1, 1, "", "trigger_data"]], "nodriver.cdp.storage.AttributionScopesData": [[48, 1, 1, "", "limit"], [48, 1, 1, "", "max_event_states"], [48, 1, 1, "", "values"]], "nodriver.cdp.storage.CacheStorageContentUpdated": [[48, 1, 1, "", "bucket_id"], [48, 1, 1, "", "cache_name"], [48, 1, 1, "", "origin"], [48, 1, 1, "", "storage_key"]], "nodriver.cdp.storage.CacheStorageListUpdated": [[48, 1, 1, "", "bucket_id"], [48, 1, 1, "", "origin"], [48, 1, 1, "", "storage_key"]], "nodriver.cdp.storage.IndexedDBContentUpdated": [[48, 1, 1, "", "bucket_id"], [48, 1, 1, "", "database_name"], [48, 1, 1, "", "object_store_name"], [48, 1, 1, "", "origin"], [48, 1, 1, "", "storage_key"]], "nodriver.cdp.storage.IndexedDBListUpdated": [[48, 1, 1, "", "bucket_id"], [48, 1, 1, "", "origin"], [48, 1, 1, "", "storage_key"]], "nodriver.cdp.storage.InterestGroupAccessType": [[48, 1, 1, "", "ADDITIONAL_BID"], [48, 1, 1, "", "ADDITIONAL_BID_WIN"], [48, 1, 1, "", "BID"], [48, 1, 1, "", "CLEAR"], [48, 1, 1, "", "JOIN"], [48, 1, 1, "", "LEAVE"], [48, 1, 1, "", "LOADED"], [48, 1, 1, "", "TOP_LEVEL_ADDITIONAL_BID"], [48, 1, 1, "", "TOP_LEVEL_BID"], [48, 1, 1, "", "UPDATE"], [48, 1, 1, "", "WIN"]], "nodriver.cdp.storage.InterestGroupAccessed": [[48, 1, 1, "", "access_time"], [48, 1, 1, "", "bid"], [48, 1, 1, "", "bid_currency"], [48, 1, 1, "", "component_seller_origin"], [48, 1, 1, "", "name"], [48, 1, 1, "", "owner_origin"], [48, 1, 1, "", "type_"], [48, 1, 1, "", "unique_auction_id"]], "nodriver.cdp.storage.InterestGroupAuctionEventOccurred": [[48, 1, 1, "", "auction_config"], [48, 1, 1, "", "event_time"], [48, 1, 1, "", "parent_auction_id"], [48, 1, 1, "", "type_"], [48, 1, 1, "", "unique_auction_id"]], "nodriver.cdp.storage.InterestGroupAuctionEventType": [[48, 1, 1, "", "CONFIG_RESOLVED"], [48, 1, 1, "", "STARTED"]], "nodriver.cdp.storage.InterestGroupAuctionFetchType": [[48, 1, 1, "", "BIDDER_JS"], [48, 1, 1, "", "BIDDER_TRUSTED_SIGNALS"], [48, 1, 1, "", "BIDDER_WASM"], [48, 1, 1, "", "SELLER_JS"], [48, 1, 1, "", "SELLER_TRUSTED_SIGNALS"]], "nodriver.cdp.storage.InterestGroupAuctionNetworkRequestCreated": [[48, 1, 1, "", "auctions"], [48, 1, 1, "", "request_id"], [48, 1, 1, "", "type_"]], "nodriver.cdp.storage.RelatedWebsiteSet": [[48, 1, 1, "", "associated_sites"], [48, 1, 1, "", "primary_sites"], [48, 1, 1, "", "service_sites"]], "nodriver.cdp.storage.SharedStorageAccessParams": [[48, 1, 1, "", "ignore_if_present"], [48, 1, 1, "", "key"], [48, 1, 1, "", "operation_name"], [48, 1, 1, "", "script_source_url"], [48, 1, 1, "", "serialized_data"], [48, 1, 1, "", "urls_with_metadata"], [48, 1, 1, "", "value"]], "nodriver.cdp.storage.SharedStorageAccessType": [[48, 1, 1, "", "DOCUMENT_ADD_MODULE"], [48, 1, 1, "", "DOCUMENT_APPEND"], [48, 1, 1, "", "DOCUMENT_CLEAR"], [48, 1, 1, "", "DOCUMENT_DELETE"], [48, 1, 1, "", "DOCUMENT_GET"], [48, 1, 1, "", "DOCUMENT_RUN"], [48, 1, 1, "", "DOCUMENT_SELECT_URL"], [48, 1, 1, "", "DOCUMENT_SET"], [48, 1, 1, "", "HEADER_APPEND"], [48, 1, 1, "", "HEADER_CLEAR"], [48, 1, 1, "", "HEADER_DELETE"], [48, 1, 1, "", "HEADER_SET"], [48, 1, 1, "", "WORKLET_APPEND"], [48, 1, 1, "", "WORKLET_CLEAR"], [48, 1, 1, "", "WORKLET_DELETE"], [48, 1, 1, "", "WORKLET_ENTRIES"], [48, 1, 1, "", "WORKLET_GET"], [48, 1, 1, "", "WORKLET_KEYS"], [48, 1, 1, "", "WORKLET_LENGTH"], [48, 1, 1, "", "WORKLET_REMAINING_BUDGET"], [48, 1, 1, "", "WORKLET_SET"]], "nodriver.cdp.storage.SharedStorageAccessed": [[48, 1, 1, "", "access_time"], [48, 1, 1, "", "main_frame_id"], [48, 1, 1, "", "owner_origin"], [48, 1, 1, "", "params"], [48, 1, 1, "", "type_"]], "nodriver.cdp.storage.SharedStorageEntry": [[48, 1, 1, "", "key"], [48, 1, 1, "", "value"]], "nodriver.cdp.storage.SharedStorageMetadata": [[48, 1, 1, "", "bytes_used"], [48, 1, 1, "", "creation_time"], [48, 1, 1, "", "length"], [48, 1, 1, "", "remaining_budget"]], "nodriver.cdp.storage.SharedStorageReportingMetadata": [[48, 1, 1, "", "event_type"], [48, 1, 1, "", "reporting_url"]], "nodriver.cdp.storage.SharedStorageUrlWithMetadata": [[48, 1, 1, "", "reporting_metadata"], [48, 1, 1, "", "url"]], "nodriver.cdp.storage.StorageBucket": [[48, 1, 1, "", "name"], [48, 1, 1, "", "storage_key"]], "nodriver.cdp.storage.StorageBucketCreatedOrUpdated": [[48, 1, 1, "", "bucket_info"]], "nodriver.cdp.storage.StorageBucketDeleted": [[48, 1, 1, "", "bucket_id"]], "nodriver.cdp.storage.StorageBucketInfo": [[48, 1, 1, "", "bucket"], [48, 1, 1, "", "durability"], [48, 1, 1, "", "expiration"], [48, 1, 1, "", "id_"], [48, 1, 1, "", "persistent"], [48, 1, 1, "", "quota"]], "nodriver.cdp.storage.StorageBucketsDurability": [[48, 1, 1, "", "RELAXED"], [48, 1, 1, "", "STRICT"]], "nodriver.cdp.storage.StorageType": [[48, 1, 1, "", "ALL_"], [48, 1, 1, "", "CACHE_STORAGE"], [48, 1, 1, "", "COOKIES"], [48, 1, 1, "", "FILE_SYSTEMS"], [48, 1, 1, "", "INDEXEDDB"], [48, 1, 1, "", "INTEREST_GROUPS"], [48, 1, 1, "", "LOCAL_STORAGE"], [48, 1, 1, "", "OTHER"], [48, 1, 1, "", "SERVICE_WORKERS"], [48, 1, 1, "", "SHADER_CACHE"], [48, 1, 1, "", "SHARED_STORAGE"], [48, 1, 1, "", "STORAGE_BUCKETS"], [48, 1, 1, "", "WEBSQL"]], "nodriver.cdp.storage.TrustTokens": [[48, 1, 1, "", "count"], [48, 1, 1, "", "issuer_origin"]], "nodriver.cdp.storage.UsageForType": [[48, 1, 1, "", "storage_type"], [48, 1, 1, "", "usage"]], "nodriver.cdp.system_info": [[49, 0, 1, "", "GPUDevice"], [49, 0, 1, "", "GPUInfo"], [49, 0, 1, "", "ImageDecodeAcceleratorCapability"], [49, 0, 1, "", "ImageType"], [49, 0, 1, "", "ProcessInfo"], [49, 0, 1, "", "Size"], [49, 0, 1, "", "SubsamplingFormat"], [49, 0, 1, "", "VideoDecodeAcceleratorCapability"], [49, 0, 1, "", "VideoEncodeAcceleratorCapability"], [49, 5, 1, "", "get_feature_state"], [49, 5, 1, "", "get_info"], [49, 5, 1, "", "get_process_info"]], "nodriver.cdp.system_info.GPUDevice": [[49, 1, 1, "", "device_id"], [49, 1, 1, "", "device_string"], [49, 1, 1, "", "driver_vendor"], [49, 1, 1, "", "driver_version"], [49, 1, 1, "", "revision"], [49, 1, 1, "", "sub_sys_id"], [49, 1, 1, "", "vendor_id"], [49, 1, 1, "", "vendor_string"]], "nodriver.cdp.system_info.GPUInfo": [[49, 1, 1, "", "aux_attributes"], [49, 1, 1, "", "devices"], [49, 1, 1, "", "driver_bug_workarounds"], [49, 1, 1, "", "feature_status"], [49, 1, 1, "", "image_decoding"], [49, 1, 1, "", "video_decoding"], [49, 1, 1, "", "video_encoding"]], "nodriver.cdp.system_info.ImageDecodeAcceleratorCapability": [[49, 1, 1, "", "image_type"], [49, 1, 1, "", "max_dimensions"], [49, 1, 1, "", "min_dimensions"], [49, 1, 1, "", "subsamplings"]], "nodriver.cdp.system_info.ImageType": [[49, 1, 1, "", "JPEG"], [49, 1, 1, "", "UNKNOWN"], [49, 1, 1, "", "WEBP"]], "nodriver.cdp.system_info.ProcessInfo": [[49, 1, 1, "", "cpu_time"], [49, 1, 1, "", "id_"], [49, 1, 1, "", "type_"]], "nodriver.cdp.system_info.Size": [[49, 1, 1, "", "height"], [49, 1, 1, "", "width"]], "nodriver.cdp.system_info.SubsamplingFormat": [[49, 1, 1, "", "YUV420"], [49, 1, 1, "", "YUV422"], [49, 1, 1, "", "YUV444"]], "nodriver.cdp.system_info.VideoDecodeAcceleratorCapability": [[49, 1, 1, "", "max_resolution"], [49, 1, 1, "", "min_resolution"], [49, 1, 1, "", "profile"]], "nodriver.cdp.system_info.VideoEncodeAcceleratorCapability": [[49, 1, 1, "", "max_framerate_denominator"], [49, 1, 1, "", "max_framerate_numerator"], [49, 1, 1, "", "max_resolution"], [49, 1, 1, "", "profile"]], "nodriver.cdp.target": [[50, 0, 1, "", "AttachedToTarget"], [50, 0, 1, "", "DetachedFromTarget"], [50, 0, 1, "", "FilterEntry"], [50, 0, 1, "", "ReceivedMessageFromTarget"], [50, 0, 1, "", "RemoteLocation"], [50, 0, 1, "", "SessionID"], [50, 0, 1, "", "TargetCrashed"], [50, 0, 1, "", "TargetCreated"], [50, 0, 1, "", "TargetDestroyed"], [50, 0, 1, "", "TargetFilter"], [50, 0, 1, "", "TargetID"], [50, 0, 1, "", "TargetInfo"], [50, 0, 1, "", "TargetInfoChanged"], [50, 0, 1, "", "WindowState"], [50, 5, 1, "", "activate_target"], [50, 5, 1, "", "attach_to_browser_target"], [50, 5, 1, "", "attach_to_target"], [50, 5, 1, "", "auto_attach_related"], [50, 5, 1, "", "close_target"], [50, 5, 1, "", "create_browser_context"], [50, 5, 1, "", "create_target"], [50, 5, 1, "", "detach_from_target"], [50, 5, 1, "", "dispose_browser_context"], [50, 5, 1, "", "expose_dev_tools_protocol"], [50, 5, 1, "", "get_browser_contexts"], [50, 5, 1, "", "get_target_info"], [50, 5, 1, "", "get_targets"], [50, 5, 1, "", "send_message_to_target"], [50, 5, 1, "", "set_auto_attach"], [50, 5, 1, "", "set_discover_targets"], [50, 5, 1, "", "set_remote_locations"]], "nodriver.cdp.target.AttachedToTarget": [[50, 1, 1, "", "session_id"], [50, 1, 1, "", "target_info"], [50, 1, 1, "", "waiting_for_debugger"]], "nodriver.cdp.target.DetachedFromTarget": [[50, 1, 1, "", "session_id"], [50, 1, 1, "", "target_id"]], "nodriver.cdp.target.FilterEntry": [[50, 1, 1, "", "exclude"], [50, 1, 1, "", "type_"]], "nodriver.cdp.target.ReceivedMessageFromTarget": [[50, 1, 1, "", "message"], [50, 1, 1, "", "session_id"], [50, 1, 1, "", "target_id"]], "nodriver.cdp.target.RemoteLocation": [[50, 1, 1, "", "host"], [50, 1, 1, "", "port"]], "nodriver.cdp.target.TargetCrashed": [[50, 1, 1, "", "error_code"], [50, 1, 1, "", "status"], [50, 1, 1, "", "target_id"]], "nodriver.cdp.target.TargetCreated": [[50, 1, 1, "", "target_info"]], "nodriver.cdp.target.TargetDestroyed": [[50, 1, 1, "", "target_id"]], "nodriver.cdp.target.TargetInfo": [[50, 1, 1, "", "attached"], [50, 1, 1, "", "browser_context_id"], [50, 1, 1, "", "can_access_opener"], [50, 1, 1, "", "opener_frame_id"], [50, 1, 1, "", "opener_id"], [50, 1, 1, "", "subtype"], [50, 1, 1, "", "target_id"], [50, 1, 1, "", "title"], [50, 1, 1, "", "type_"], [50, 1, 1, "", "url"]], "nodriver.cdp.target.TargetInfoChanged": [[50, 1, 1, "", "target_info"]], "nodriver.cdp.target.WindowState": [[50, 1, 1, "", "FULLSCREEN"], [50, 1, 1, "", "MAXIMIZED"], [50, 1, 1, "", "MINIMIZED"], [50, 1, 1, "", "NORMAL"]], "nodriver.cdp.tethering": [[51, 0, 1, "", "Accepted"], [51, 5, 1, "", "bind"], [51, 5, 1, "", "unbind"]], "nodriver.cdp.tethering.Accepted": [[51, 1, 1, "", "connection_id"], [51, 1, 1, "", "port"]], "nodriver.cdp.tracing": [[52, 0, 1, "", "BufferUsage"], [52, 0, 1, "", "DataCollected"], [52, 0, 1, "", "MemoryDumpConfig"], [52, 0, 1, "", "MemoryDumpLevelOfDetail"], [52, 0, 1, "", "StreamCompression"], [52, 0, 1, "", "StreamFormat"], [52, 0, 1, "", "TraceConfig"], [52, 0, 1, "", "TracingBackend"], [52, 0, 1, "", "TracingComplete"], [52, 5, 1, "", "end"], [52, 5, 1, "", "get_categories"], [52, 5, 1, "", "record_clock_sync_marker"], [52, 5, 1, "", "request_memory_dump"], [52, 5, 1, "", "start"]], "nodriver.cdp.tracing.BufferUsage": [[52, 1, 1, "", "event_count"], [52, 1, 1, "", "percent_full"], [52, 1, 1, "", "value"]], "nodriver.cdp.tracing.DataCollected": [[52, 1, 1, "", "value"]], "nodriver.cdp.tracing.MemoryDumpLevelOfDetail": [[52, 1, 1, "", "BACKGROUND"], [52, 1, 1, "", "DETAILED"], [52, 1, 1, "", "LIGHT"]], "nodriver.cdp.tracing.StreamCompression": [[52, 1, 1, "", "GZIP"], [52, 1, 1, "", "NONE"]], "nodriver.cdp.tracing.StreamFormat": [[52, 1, 1, "", "JSON"], [52, 1, 1, "", "PROTO"]], "nodriver.cdp.tracing.TraceConfig": [[52, 1, 1, "", "enable_argument_filter"], [52, 1, 1, "", "enable_sampling"], [52, 1, 1, "", "enable_systrace"], [52, 1, 1, "", "excluded_categories"], [52, 1, 1, "", "included_categories"], [52, 1, 1, "", "memory_dump_config"], [52, 1, 1, "", "record_mode"], [52, 1, 1, "", "synthetic_delays"], [52, 1, 1, "", "trace_buffer_size_in_kb"]], "nodriver.cdp.tracing.TracingBackend": [[52, 1, 1, "", "AUTO"], [52, 1, 1, "", "CHROME"], [52, 1, 1, "", "SYSTEM"]], "nodriver.cdp.tracing.TracingComplete": [[52, 1, 1, "", "data_loss_occurred"], [52, 1, 1, "", "stream"], [52, 1, 1, "", "stream_compression"], [52, 1, 1, "", "trace_format"]], "nodriver.cdp.web_audio": [[53, 0, 1, "", "AudioListener"], [53, 0, 1, "", "AudioListenerCreated"], [53, 0, 1, "", "AudioListenerWillBeDestroyed"], [53, 0, 1, "", "AudioNode"], [53, 0, 1, "", "AudioNodeCreated"], [53, 0, 1, "", "AudioNodeWillBeDestroyed"], [53, 0, 1, "", "AudioParam"], [53, 0, 1, "", "AudioParamCreated"], [53, 0, 1, "", "AudioParamWillBeDestroyed"], [53, 0, 1, "", "AutomationRate"], [53, 0, 1, "", "BaseAudioContext"], [53, 0, 1, "", "ChannelCountMode"], [53, 0, 1, "", "ChannelInterpretation"], [53, 0, 1, "", "ContextChanged"], [53, 0, 1, "", "ContextCreated"], [53, 0, 1, "", "ContextRealtimeData"], [53, 0, 1, "", "ContextState"], [53, 0, 1, "", "ContextType"], [53, 0, 1, "", "ContextWillBeDestroyed"], [53, 0, 1, "", "GraphObjectId"], [53, 0, 1, "", "NodeParamConnected"], [53, 0, 1, "", "NodeParamDisconnected"], [53, 0, 1, "", "NodeType"], [53, 0, 1, "", "NodesConnected"], [53, 0, 1, "", "NodesDisconnected"], [53, 0, 1, "", "ParamType"], [53, 5, 1, "", "disable"], [53, 5, 1, "", "enable"], [53, 5, 1, "", "get_realtime_data"]], "nodriver.cdp.web_audio.AudioListener": [[53, 1, 1, "", "context_id"], [53, 1, 1, "", "listener_id"]], "nodriver.cdp.web_audio.AudioListenerCreated": [[53, 1, 1, "", "listener"]], "nodriver.cdp.web_audio.AudioListenerWillBeDestroyed": [[53, 1, 1, "", "context_id"], [53, 1, 1, "", "listener_id"]], "nodriver.cdp.web_audio.AudioNode": [[53, 1, 1, "", "channel_count"], [53, 1, 1, "", "channel_count_mode"], [53, 1, 1, "", "channel_interpretation"], [53, 1, 1, "", "context_id"], [53, 1, 1, "", "node_id"], [53, 1, 1, "", "node_type"], [53, 1, 1, "", "number_of_inputs"], [53, 1, 1, "", "number_of_outputs"]], "nodriver.cdp.web_audio.AudioNodeCreated": [[53, 1, 1, "", "node"]], "nodriver.cdp.web_audio.AudioNodeWillBeDestroyed": [[53, 1, 1, "", "context_id"], [53, 1, 1, "", "node_id"]], "nodriver.cdp.web_audio.AudioParam": [[53, 1, 1, "", "context_id"], [53, 1, 1, "", "default_value"], [53, 1, 1, "", "max_value"], [53, 1, 1, "", "min_value"], [53, 1, 1, "", "node_id"], [53, 1, 1, "", "param_id"], [53, 1, 1, "", "param_type"], [53, 1, 1, "", "rate"]], "nodriver.cdp.web_audio.AudioParamCreated": [[53, 1, 1, "", "param"]], "nodriver.cdp.web_audio.AudioParamWillBeDestroyed": [[53, 1, 1, "", "context_id"], [53, 1, 1, "", "node_id"], [53, 1, 1, "", "param_id"]], "nodriver.cdp.web_audio.AutomationRate": [[53, 1, 1, "", "A_RATE"], [53, 1, 1, "", "K_RATE"]], "nodriver.cdp.web_audio.BaseAudioContext": [[53, 1, 1, "", "callback_buffer_size"], [53, 1, 1, "", "context_id"], [53, 1, 1, "", "context_state"], [53, 1, 1, "", "context_type"], [53, 1, 1, "", "max_output_channel_count"], [53, 1, 1, "", "realtime_data"], [53, 1, 1, "", "sample_rate"]], "nodriver.cdp.web_audio.ChannelCountMode": [[53, 1, 1, "", "CLAMPED_MAX"], [53, 1, 1, "", "EXPLICIT"], [53, 1, 1, "", "MAX_"]], "nodriver.cdp.web_audio.ChannelInterpretation": [[53, 1, 1, "", "DISCRETE"], [53, 1, 1, "", "SPEAKERS"]], "nodriver.cdp.web_audio.ContextChanged": [[53, 1, 1, "", "context"]], "nodriver.cdp.web_audio.ContextCreated": [[53, 1, 1, "", "context"]], "nodriver.cdp.web_audio.ContextRealtimeData": [[53, 1, 1, "", "callback_interval_mean"], [53, 1, 1, "", "callback_interval_variance"], [53, 1, 1, "", "current_time"], [53, 1, 1, "", "render_capacity"]], "nodriver.cdp.web_audio.ContextState": [[53, 1, 1, "", "CLOSED"], [53, 1, 1, "", "INTERRUPTED"], [53, 1, 1, "", "RUNNING"], [53, 1, 1, "", "SUSPENDED"]], "nodriver.cdp.web_audio.ContextType": [[53, 1, 1, "", "OFFLINE"], [53, 1, 1, "", "REALTIME"]], "nodriver.cdp.web_audio.ContextWillBeDestroyed": [[53, 1, 1, "", "context_id"]], "nodriver.cdp.web_audio.NodeParamConnected": [[53, 1, 1, "", "context_id"], [53, 1, 1, "", "destination_id"], [53, 1, 1, "", "source_id"], [53, 1, 1, "", "source_output_index"]], "nodriver.cdp.web_audio.NodeParamDisconnected": [[53, 1, 1, "", "context_id"], [53, 1, 1, "", "destination_id"], [53, 1, 1, "", "source_id"], [53, 1, 1, "", "source_output_index"]], "nodriver.cdp.web_audio.NodesConnected": [[53, 1, 1, "", "context_id"], [53, 1, 1, "", "destination_id"], [53, 1, 1, "", "destination_input_index"], [53, 1, 1, "", "source_id"], [53, 1, 1, "", "source_output_index"]], "nodriver.cdp.web_audio.NodesDisconnected": [[53, 1, 1, "", "context_id"], [53, 1, 1, "", "destination_id"], [53, 1, 1, "", "destination_input_index"], [53, 1, 1, "", "source_id"], [53, 1, 1, "", "source_output_index"]], "nodriver.cdp.web_authn": [[54, 0, 1, "", "AuthenticatorId"], [54, 0, 1, "", "AuthenticatorProtocol"], [54, 0, 1, "", "AuthenticatorTransport"], [54, 0, 1, "", "Credential"], [54, 0, 1, "", "CredentialAdded"], [54, 0, 1, "", "CredentialAsserted"], [54, 0, 1, "", "CredentialDeleted"], [54, 0, 1, "", "CredentialUpdated"], [54, 0, 1, "", "Ctap2Version"], [54, 0, 1, "", "VirtualAuthenticatorOptions"], [54, 5, 1, "", "add_credential"], [54, 5, 1, "", "add_virtual_authenticator"], [54, 5, 1, "", "clear_credentials"], [54, 5, 1, "", "disable"], [54, 5, 1, "", "enable"], [54, 5, 1, "", "get_credential"], [54, 5, 1, "", "get_credentials"], [54, 5, 1, "", "remove_credential"], [54, 5, 1, "", "remove_virtual_authenticator"], [54, 5, 1, "", "set_automatic_presence_simulation"], [54, 5, 1, "", "set_credential_properties"], [54, 5, 1, "", "set_response_override_bits"], [54, 5, 1, "", "set_user_verified"]], "nodriver.cdp.web_authn.AuthenticatorProtocol": [[54, 1, 1, "", "CTAP2"], [54, 1, 1, "", "U2F"]], "nodriver.cdp.web_authn.AuthenticatorTransport": [[54, 1, 1, "", "BLE"], [54, 1, 1, "", "CABLE"], [54, 1, 1, "", "INTERNAL"], [54, 1, 1, "", "NFC"], [54, 1, 1, "", "USB"]], "nodriver.cdp.web_authn.Credential": [[54, 1, 1, "", "backup_eligibility"], [54, 1, 1, "", "backup_state"], [54, 1, 1, "", "credential_id"], [54, 1, 1, "", "is_resident_credential"], [54, 1, 1, "", "large_blob"], [54, 1, 1, "", "private_key"], [54, 1, 1, "", "rp_id"], [54, 1, 1, "", "sign_count"], [54, 1, 1, "", "user_display_name"], [54, 1, 1, "", "user_handle"], [54, 1, 1, "", "user_name"]], "nodriver.cdp.web_authn.CredentialAdded": [[54, 1, 1, "", "authenticator_id"], [54, 1, 1, "", "credential"]], "nodriver.cdp.web_authn.CredentialAsserted": [[54, 1, 1, "", "authenticator_id"], [54, 1, 1, "", "credential"]], "nodriver.cdp.web_authn.CredentialDeleted": [[54, 1, 1, "", "authenticator_id"], [54, 1, 1, "", "credential_id"]], "nodriver.cdp.web_authn.CredentialUpdated": [[54, 1, 1, "", "authenticator_id"], [54, 1, 1, "", "credential"]], "nodriver.cdp.web_authn.Ctap2Version": [[54, 1, 1, "", "CTAP2_0"], [54, 1, 1, "", "CTAP2_1"]], "nodriver.cdp.web_authn.VirtualAuthenticatorOptions": [[54, 1, 1, "", "automatic_presence_simulation"], [54, 1, 1, "", "ctap2_version"], [54, 1, 1, "", "default_backup_eligibility"], [54, 1, 1, "", "default_backup_state"], [54, 1, 1, "", "has_cred_blob"], [54, 1, 1, "", "has_large_blob"], [54, 1, 1, "", "has_min_pin_length"], [54, 1, 1, "", "has_prf"], [54, 1, 1, "", "has_resident_key"], [54, 1, 1, "", "has_user_verification"], [54, 1, 1, "", "is_user_verified"], [54, 1, 1, "", "protocol"], [54, 1, 1, "", "transport"]], "nodriver.core": [[57, 4, 0, "-", "_contradict"]], "nodriver.core._contradict": [[57, 0, 1, "id0", "ContraDict"], [57, 5, 1, "", "cdict"]], "nodriver.core._contradict.ContraDict": [[57, 3, 1, "id1", "clear"], [57, 3, 1, "id2", "copy"], [57, 3, 1, "id3", "fromkeys"], [57, 3, 1, "id4", "get"], [57, 3, 1, "id5", "items"], [57, 3, 1, "id6", "keys"], [57, 3, 1, "id7", "pop"], [57, 3, 1, "id8", "popitem"], [57, 3, 1, "id9", "setdefault"], [57, 3, 1, "id10", "update"], [57, 3, 1, "id11", "values"]]}, "objtypes": {"0": "py:class", "1": "py:attribute", "2": "py:property", "3": "py:method", "4": "py:module", "5": "py:function"}, "objnames": {"0": ["py", "class", "Python class"], "1": ["py", "attribute", "Python attribute"], "2": ["py", "property", "Python property"], "3": ["py", "method", "Python method"], "4": ["py", "module", "Python module"], "5": ["py", "function", "Python function"]}, "titleterms": {"nodriv": [0, 60], "click": [0, 60], "here": [0, 60], "FOR": 0, "doc": [0, 60], "some": [0, 58, 60], "featur": [0, 60], "quick": 0, "start": [0, 59], "main": 0, "object": [0, 1], "cdp": [0, 1, 58], "access": 2, "type": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], "command": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 58], "event": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], "anim": 3, "audit": 4, "autofil": 5, "backgroundservic": 6, "bluetoothemul": 7, "browser": [8, 55], "cachestorag": 9, "cast": 10, "consol": 11, "css": 12, "debugg": 13, "deviceaccess": 14, "deviceorient": 15, "dom": 16, "domdebugg": 17, "domsnapshot": 18, "domstorag": 19, "emul": 20, "eventbreakpoint": 21, "extens": 22, "fedcm": 23, "fetch": 24, "filesystem": 25, "headlessexperiment": 26, "heapprofil": 27, "indexeddb": 28, "input": 29, "inspector": 30, "io": 31, "layertre": 32, "log": 33, "media": 34, "memori": 35, "network": 36, "overlai": 37, "page": 38, "perform": 39, "performancetimelin": 40, "preload": 41, "profil": 42, "pwa": 43, "runtim": 44, "schema": 45, "secur": 46, "servicework": 47, "storag": 48, "systeminfo": 49, "target": 50, "tether": 51, "trace": 52, "webaudio": 53, "webauthn": 54, "class": [55, 56, 57, 58], "cooki": 55, "element": 56, "other": [57, 58], "helper": 57, "config": 57, "contradict": 57, "function": 57, "tab": 58, "custom": [58, 59], "us": 58, "often": 58, "need": 58, "simpli": 58, "requir": 58, "method": 58, "find": 58, "text": 58, "best_match": 58, "true": 58, "select": 58, "selector": 58, "select_al": 58, "await": 58, "template_loc": 58, "verify_cf": 58, "exampl": [58, 59, 60], "111x71": 58, "send": 58, "quickstart": 59, "guid": 59, "instal": [59, 60], "usag": [59, 60], "more": 59, "complet": 59, "option": 59, "altern": 59, "proxi": 59, "socks5": 59, "authent": 59, "too": 59, "concret": 59, "what": 60, "i": 60, "new": 60, "api": 60, "look": 60, "like": 60, "titl": 61, "section": 61, "subsect": 61, "paragraph": 61, "tabl": 61}, "envversion": {"sphinx.domains.c": 3, "sphinx.domains.changeset": 1, "sphinx.domains.citation": 1, "sphinx.domains.cpp": 9, "sphinx.domains.index": 1, "sphinx.domains.javascript": 3, "sphinx.domains.math": 2, "sphinx.domains.python": 4, "sphinx.domains.rst": 2, "sphinx.domains.std": 2, "sphinx.ext.viewcode": 1, "sphinx.ext.intersphinx": 1, "sphinx": 60}, "alltitles": {"NODRIVER": [[0, "nodriver"], [60, "nodriver"]], "CLICK HERE FOR DOCS": [[0, "id1"]], "Some features": [[0, "some-features"], [60, "some-features"]], "Quick start": [[0, "quick-start"]], "Main objects": [[0, "main-objects"]], "CDP object": [[0, "cdp-object"], [1, "cdp-object"]], "Accessibility": [[2, "accessibility"]], "Types": [[2, "types"], [3, "types"], [4, "types"], [5, "types"], [6, "types"], [7, "types"], [8, "types"], [9, "types"], [10, "types"], [11, "types"], [12, "types"], [13, "types"], [14, "types"], [15, "types"], [16, "types"], [17, "types"], [18, "types"], [19, "types"], [20, "types"], [21, "types"], [22, "types"], [23, "types"], [24, "types"], [25, "types"], [26, "types"], [27, "types"], [28, "types"], [29, "types"], [30, "types"], [31, "types"], [32, "types"], [33, "types"], [34, "types"], [35, "types"], [36, "types"], [37, "types"], [38, "types"], [39, "types"], [40, "types"], [41, "types"], [42, "types"], [43, "types"], [44, "types"], [45, "types"], [46, "types"], [47, "types"], [48, "types"], [49, "types"], [50, "types"], [51, "types"], [52, "types"], [53, "types"], [54, "types"]], "Commands": [[2, "commands"], [3, "commands"], [4, "commands"], [5, "commands"], [6, "commands"], [7, "commands"], [8, "commands"], [9, "commands"], [10, "commands"], [11, "commands"], [12, "commands"], [13, "commands"], [14, "commands"], [15, "commands"], [16, "commands"], [17, "commands"], [18, "commands"], [19, "commands"], [20, "commands"], [21, "commands"], [22, "commands"], [23, "commands"], [24, "commands"], [25, "commands"], [26, "commands"], [27, "commands"], [28, "commands"], [29, "commands"], [30, "commands"], [31, "commands"], [32, "commands"], [33, "commands"], [34, "commands"], [35, "commands"], [36, "commands"], [37, "commands"], [38, "commands"], [39, "commands"], [40, "commands"], [41, "commands"], [42, "commands"], [43, "commands"], [44, "commands"], [45, "commands"], [46, "commands"], [47, "commands"], [48, "commands"], [49, "commands"], [50, "commands"], [51, "commands"], [52, "commands"], [53, "commands"], [54, "commands"]], "Events": [[2, "events"], [3, "events"], [4, "events"], [5, "events"], [6, "events"], [7, "events"], [8, "events"], [9, "events"], [10, "events"], [11, "events"], [12, "events"], [13, "events"], [14, "events"], [15, "events"], [16, "events"], [17, "events"], [18, "events"], [19, "events"], [20, "events"], [21, "events"], [22, "events"], [23, "events"], [24, "events"], [25, "events"], [26, "events"], [27, "events"], [28, "events"], [29, "events"], [30, "events"], [31, "events"], [32, "events"], [33, "events"], [34, "events"], [35, "events"], [36, "events"], [37, "events"], [38, "events"], [39, "events"], [40, "events"], [41, "events"], [42, "events"], [43, "events"], [44, "events"], [45, "events"], [46, "events"], [47, "events"], [48, "events"], [49, "events"], [50, "events"], [51, "events"], [52, "events"], [53, "events"], [54, "events"]], "Animation": [[3, "animation"]], "Audits": [[4, "audits"]], "Autofill": [[5, "autofill"]], "BackgroundService": [[6, "backgroundservice"]], "BluetoothEmulation": [[7, "bluetoothemulation"]], "Browser": [[8, "browser"]], "CacheStorage": [[9, "cachestorage"]], "Cast": [[10, "cast"]], "Console": [[11, "console"]], "CSS": [[12, "css"]], "Debugger": [[13, "debugger"]], "DeviceAccess": [[14, "deviceaccess"]], "DeviceOrientation": [[15, "deviceorientation"]], "DOM": [[16, "dom"]], "DOMDebugger": [[17, "domdebugger"]], "DOMSnapshot": [[18, "domsnapshot"]], "DOMStorage": [[19, "domstorage"]], "Emulation": [[20, "emulation"]], "EventBreakpoints": [[21, "eventbreakpoints"]], "Extensions": [[22, "extensions"]], "FedCm": [[23, "fedcm"]], "Fetch": [[24, "fetch"]], "FileSystem": [[25, "filesystem"]], "HeadlessExperimental": [[26, "headlessexperimental"]], "HeapProfiler": [[27, "heapprofiler"]], "IndexedDB": [[28, "indexeddb"]], "Input": [[29, "module-nodriver.cdp.input_"]], "Inspector": [[30, "inspector"]], "IO": [[31, "io"]], "LayerTree": [[32, "layertree"]], "Log": [[33, "log"]], "Media": [[34, "media"]], "Memory": [[35, "memory"]], "Network": [[36, "network"]], "Overlay": [[37, "overlay"]], "Page": [[38, "page"]], "Performance": [[39, "module-nodriver.cdp.performance"]], "PerformanceTimeline": [[40, "performancetimeline"]], "Preload": [[41, "preload"]], "Profiler": [[42, "module-nodriver.cdp.profiler"]], "PWA": [[43, "pwa"]], "Runtime": [[44, "runtime"]], "Schema": [[45, "schema"]], "Security": [[46, "security"]], "ServiceWorker": [[47, "serviceworker"]], "Storage": [[48, "storage"]], "SystemInfo": [[49, "systeminfo"]], "Target": [[50, "target"]], "Tethering": [[51, "tethering"]], "Tracing": [[52, "module-nodriver.cdp.tracing"]], "WebAudio": [[53, "webaudio"]], "WebAuthn": [[54, "webauthn"]], "Quickstart guide": [[59, "quickstart-guide"]], "Installation": [[59, "installation"], [60, "installation"]], "usage example": [[59, "usage-example"], [60, "usage-example"]], "More complete example": [[59, "more-complete-example"]], "Custom starting options": [[59, "custom-starting-options"]], "Alternative custom options": [[59, "alternative-custom-options"]], "Proxies (socks5, authenticated too)": [[59, "proxies-socks5-authenticated-too"]], "Concrete example": [[59, "concrete-example"]], "TITLE": [[61, "title"]], "SECTION": [[61, "section"]], "SUBSECTION": [[61, "subsection"]], "PARAGRAPH": [[61, "paragraph"]], "TABLES": [[61, "tables"]], "Browser class": [[55, "browser-class"], [55, "id1"]], "cookies": [[55, "cookies"]], "Element class": [[56, "element-class"]], "Other classes and Helper classes": [[57, "other-classes-and-helper-classes"]], "Config class": [[57, "config-class"]], "ContraDict class": [[57, "contradict-class"]], "Helper functions": [[57, "module-nodriver.core._contradict"]], "Tab class": [[58, "tab-class"]], "Custom CDP commands": [[58, "custom-cdp-commands"]], "some useful, often needed and simply required methods": [[58, "some-useful-often-needed-and-simply-required-methods"]], "find()  |  find(text)": [[58, "find-find-text"]], "find() |  find(text, best_match=True) or find(text, True)": [[58, "find-find-text-best-match-true-or-find-text-true"]], "select() | select(selector)": [[58, "select-select-selector"]], "select_all() | select_all(selector)": [[58, "select-all-select-all-selector"]], "await Tab": [[58, "await-tab"]], "await Tab.template_location (and await Tab.verify_cf)": [[58, "await-tab-template-location-and-await-tab-verify-cf"]], "example (111x71)": [[58, "example-111x71"], [58, "id1"], [58, "id2"]], "Using other and custom CDP commands": [[58, "using-other-and-custom-cdp-commands"]], "send()": [[58, "send"]], "for docs click here": [[60, "for-docs-click-here"]], "what is new": [[60, "what-is-new"]], "Some examples of what the api looks like": [[60, "some-examples-of-what-the-api-looks-like"]]}, "indexentries": {"browser (class in nodriver)": [[55, "nodriver.Browser"]], "config (browser attribute)": [[55, "nodriver.Browser.config"]], "connection (browser attribute)": [[55, "nodriver.Browser.connection"]], "cookies (browser property)": [[55, "nodriver.Browser.cookies"]], "create() (browser class method)": [[55, "nodriver.Browser.create"]], "create_context() (browser method)": [[55, "nodriver.Browser.create_context"]], "get() (browser method)": [[55, "nodriver.Browser.get"]], "grant_all_permissions() (browser method)": [[55, "nodriver.Browser.grant_all_permissions"]], "main_tab (browser property)": [[55, "nodriver.Browser.main_tab"]], "sleep() (browser method)": [[55, "nodriver.Browser.sleep"]], "start() (browser method)": [[55, "nodriver.Browser.start"]], "stop() (browser method)": [[55, "nodriver.Browser.stop"]], "stopped (browser property)": [[55, "nodriver.Browser.stopped"]], "tabs (browser property)": [[55, "nodriver.Browser.tabs"]], "targets (browser attribute)": [[55, "nodriver.Browser.targets"]], "tile_windows() (browser method)": [[55, "nodriver.Browser.tile_windows"]], "update_targets() (browser method)": [[55, "nodriver.Browser.update_targets"]], "wait() (browser method)": [[55, "nodriver.Browser.wait"]], "websocket_url (browser property)": [[55, "nodriver.Browser.websocket_url"]], "element (class in nodriver)": [[56, "nodriver.Element"]], "apply() (element method)": [[56, "nodriver.Element.apply"]], "assigned_slot (element property)": [[56, "nodriver.Element.assigned_slot"]], "attributes (element property)": [[56, "nodriver.Element.attributes"]], "attrs (element property)": [[56, "nodriver.Element.attrs"]], "backend_node_id (element property)": [[56, "nodriver.Element.backend_node_id"]], "base_url (element property)": [[56, "nodriver.Element.base_url"]], "child_node_count (element property)": [[56, "nodriver.Element.child_node_count"]], "children (element property)": [[56, "nodriver.Element.children"]], "clear_input() (element method)": [[56, "nodriver.Element.clear_input"]], "click() (element method)": [[56, "nodriver.Element.click"]], "click_mouse() (element method)": [[56, "nodriver.Element.click_mouse"]], "compatibility_mode (element property)": [[56, "nodriver.Element.compatibility_mode"]], "content_document (element property)": [[56, "nodriver.Element.content_document"]], "distributed_nodes (element property)": [[56, "nodriver.Element.distributed_nodes"]], "document_url (element property)": [[56, "nodriver.Element.document_url"]], "flash() (element method)": [[56, "nodriver.Element.flash"]], "focus() (element method)": [[56, "nodriver.Element.focus"]], "frame_id (element property)": [[56, "nodriver.Element.frame_id"]], "get_html() (element method)": [[56, "nodriver.Element.get_html"]], "get_js_attributes() (element method)": [[56, "nodriver.Element.get_js_attributes"]], "get_position() (element method)": [[56, "nodriver.Element.get_position"]], "highlight_overlay() (element method)": [[56, "nodriver.Element.highlight_overlay"]], "imported_document (element property)": [[56, "nodriver.Element.imported_document"]], "internal_subset (element property)": [[56, "nodriver.Element.internal_subset"]], "is_recording() (element method)": [[56, "nodriver.Element.is_recording"]], "is_svg (element property)": [[56, "nodriver.Element.is_svg"]], "local_name (element property)": [[56, "nodriver.Element.local_name"]], "mouse_click() (element method)": [[56, "nodriver.Element.mouse_click"]], "mouse_drag() (element method)": [[56, "nodriver.Element.mouse_drag"]], "mouse_move() (element method)": [[56, "nodriver.Element.mouse_move"]], "node (element property)": [[56, "nodriver.Element.node"]], "node_id (element property)": [[56, "nodriver.Element.node_id"]], "node_name (element property)": [[56, "nodriver.Element.node_name"]], "node_type (element property)": [[56, "nodriver.Element.node_type"]], "node_value (element property)": [[56, "nodriver.Element.node_value"]], "object_id (element property)": [[56, "nodriver.Element.object_id"]], "parent (element property)": [[56, "nodriver.Element.parent"]], "parent_id (element property)": [[56, "nodriver.Element.parent_id"]], "pseudo_elements (element property)": [[56, "nodriver.Element.pseudo_elements"]], "pseudo_identifier (element property)": [[56, "nodriver.Element.pseudo_identifier"]], "pseudo_type (element property)": [[56, "nodriver.Element.pseudo_type"]], "public_id (element property)": [[56, "nodriver.Element.public_id"]], "query_selector() (element method)": [[56, "nodriver.Element.query_selector"]], "query_selector_all() (element method)": [[56, "nodriver.Element.query_selector_all"]], "record_video() (element method)": [[56, "nodriver.Element.record_video"]], "remote_object (element property)": [[56, "nodriver.Element.remote_object"]], "remove_from_dom() (element method)": [[56, "nodriver.Element.remove_from_dom"]], "save_screenshot() (element method)": [[56, "nodriver.Element.save_screenshot"]], "save_to_dom() (element method)": [[56, "nodriver.Element.save_to_dom"]], "scroll_into_view() (element method)": [[56, "nodriver.Element.scroll_into_view"]], "select_option() (element method)": [[56, "nodriver.Element.select_option"]], "send_file() (element method)": [[56, "nodriver.Element.send_file"]], "send_keys() (element method)": [[56, "nodriver.Element.send_keys"]], "set_text() (element method)": [[56, "nodriver.Element.set_text"]], "set_value() (element method)": [[56, "nodriver.Element.set_value"]], "shadow_children (element property)": [[56, "nodriver.Element.shadow_children"]], "shadow_root_type (element property)": [[56, "nodriver.Element.shadow_root_type"]], "shadow_roots (element property)": [[56, "nodriver.Element.shadow_roots"]], "system_id (element property)": [[56, "nodriver.Element.system_id"]], "tab (element property)": [[56, "nodriver.Element.tab"]], "tag (element property)": [[56, "nodriver.Element.tag"]], "tag_name (element property)": [[56, "nodriver.Element.tag_name"]], "template_content (element property)": [[56, "nodriver.Element.template_content"]], "text (element property)": [[56, "nodriver.Element.text"]], "text_all (element property)": [[56, "nodriver.Element.text_all"]], "tree (element property)": [[56, "nodriver.Element.tree"]], "update() (element method)": [[56, "nodriver.Element.update"]], "value (element property)": [[56, "nodriver.Element.value"]], "xml_version (element property)": [[56, "nodriver.Element.xml_version"]], "config (class in nodriver)": [[57, "nodriver.Config"]], "contradict (class in nodriver.core._contradict)": [[57, "id0"], [57, "nodriver.core._contradict.ContraDict"]], "add_argument() (config method)": [[57, "nodriver.Config.add_argument"]], "add_extension() (config method)": [[57, "nodriver.Config.add_extension"]], "browser_args (config property)": [[57, "nodriver.Config.browser_args"]], "cdict() (in module nodriver.core._contradict)": [[57, "nodriver.core._contradict.cdict"]], "clear() (contradict method)": [[57, "id1"], [57, "nodriver.core._contradict.ContraDict.clear"]], "copy() (contradict method)": [[57, "id2"], [57, "nodriver.core._contradict.ContraDict.copy"]], "fromkeys() (contradict method)": [[57, "id3"], [57, "nodriver.core._contradict.ContraDict.fromkeys"]], "get() (contradict method)": [[57, "id4"], [57, "nodriver.core._contradict.ContraDict.get"]], "items() (contradict method)": [[57, "id5"], [57, "nodriver.core._contradict.ContraDict.items"]], "keys() (contradict method)": [[57, "id6"], [57, "nodriver.core._contradict.ContraDict.keys"]], "module": [[57, "module-nodriver.core._contradict"]], "nodriver.core._contradict": [[57, "module-nodriver.core._contradict"]], "pop() (contradict method)": [[57, "id7"], [57, "nodriver.core._contradict.ContraDict.pop"]], "popitem() (contradict method)": [[57, "id8"], [57, "nodriver.core._contradict.ContraDict.popitem"]], "setdefault() (contradict method)": [[57, "id9"], [57, "nodriver.core._contradict.ContraDict.setdefault"]], "update() (contradict method)": [[57, "id10"], [57, "nodriver.core._contradict.ContraDict.update"]], "user_data_dir (config property)": [[57, "nodriver.Config.user_data_dir"]], "uses_custom_data_dir (config property)": [[57, "nodriver.Config.uses_custom_data_dir"]], "values() (contradict method)": [[57, "id11"], [57, "nodriver.core._contradict.ContraDict.values"]], "tab (class in nodriver)": [[58, "nodriver.Tab"]], "activate() (tab method)": [[58, "nodriver.Tab.activate"]], "add_handler() (tab method)": [[58, "nodriver.Tab.add_handler"]], "attached (tab attribute)": [[58, "nodriver.Tab.attached"]], "back() (tab method)": [[58, "nodriver.Tab.back"]], "bring_to_front() (tab method)": [[58, "nodriver.Tab.bring_to_front"]], "browser (tab property)": [[58, "nodriver.Tab.browser"]], "bypass_insecure_connection_warning() (tab method)": [[58, "nodriver.Tab.bypass_insecure_connection_warning"]], "close() (tab method)": [[58, "nodriver.Tab.close"]], "closed (tab property)": [[58, "nodriver.Tab.closed"]], "connect() (tab method)": [[58, "nodriver.Tab.connect"]], "disconnect() (tab method)": [[58, "nodriver.Tab.disconnect"]], "download_file() (tab method)": [[58, "nodriver.Tab.download_file"]], "evaluate() (tab method)": [[58, "nodriver.Tab.evaluate"]], "feed_cdp() (tab method)": [[58, "nodriver.Tab.feed_cdp"]], "find() (tab method)": [[58, "nodriver.Tab.find"]], "find_all() (tab method)": [[58, "nodriver.Tab.find_all"]], "find_element_by_text() (tab method)": [[58, "nodriver.Tab.find_element_by_text"]], "find_elements_by_text() (tab method)": [[58, "nodriver.Tab.find_elements_by_text"]], "flash_point() (tab method)": [[58, "nodriver.Tab.flash_point"]], "forward() (tab method)": [[58, "nodriver.Tab.forward"]], "fullscreen() (tab method)": [[58, "nodriver.Tab.fullscreen"]], "get() (tab method)": [[58, "nodriver.Tab.get"]], "get_all_linked_sources() (tab method)": [[58, "nodriver.Tab.get_all_linked_sources"]], "get_all_urls() (tab method)": [[58, "nodriver.Tab.get_all_urls"]], "get_content() (tab method)": [[58, "nodriver.Tab.get_content"]], "get_frame_resource_tree() (tab method)": [[58, "nodriver.Tab.get_frame_resource_tree"]], "get_frame_resource_urls() (tab method)": [[58, "nodriver.Tab.get_frame_resource_urls"]], "get_frame_tree() (tab method)": [[58, "nodriver.Tab.get_frame_tree"]], "get_local_storage() (tab method)": [[58, "nodriver.Tab.get_local_storage"]], "get_window() (tab method)": [[58, "nodriver.Tab.get_window"]], "inspector_open() (tab method)": [[58, "nodriver.Tab.inspector_open"]], "inspector_url (tab property)": [[58, "nodriver.Tab.inspector_url"]], "js_dumps() (tab method)": [[58, "nodriver.Tab.js_dumps"]], "maximize() (tab method)": [[58, "nodriver.Tab.maximize"]], "medimize() (tab method)": [[58, "nodriver.Tab.medimize"]], "minimize() (tab method)": [[58, "nodriver.Tab.minimize"]], "mouse_click() (tab method)": [[58, "nodriver.Tab.mouse_click"]], "mouse_drag() (tab method)": [[58, "nodriver.Tab.mouse_drag"]], "mouse_move() (tab method)": [[58, "nodriver.Tab.mouse_move"]], "open_external_inspector() (tab method)": [[58, "nodriver.Tab.open_external_inspector"]], "query_selector() (tab method)": [[58, "nodriver.Tab.query_selector"]], "query_selector_all() (tab method)": [[58, "nodriver.Tab.query_selector_all"]], "reload() (tab method)": [[58, "nodriver.Tab.reload"]], "remove_handler() (tab method)": [[58, "nodriver.Tab.remove_handler"]], "save_screenshot() (tab method)": [[58, "nodriver.Tab.save_screenshot"]], "scroll_bottom_reached() (tab method)": [[58, "nodriver.Tab.scroll_bottom_reached"]], "scroll_down() (tab method)": [[58, "nodriver.Tab.scroll_down"]], "scroll_up() (tab method)": [[58, "nodriver.Tab.scroll_up"]], "search_frame_resources() (tab method)": [[58, "nodriver.Tab.search_frame_resources"]], "select() (tab method)": [[58, "nodriver.Tab.select"]], "select_all() (tab method)": [[58, "nodriver.Tab.select_all"]], "send() (tab method)": [[58, "nodriver.Tab.send"]], "set_download_path() (tab method)": [[58, "nodriver.Tab.set_download_path"]], "set_local_storage() (tab method)": [[58, "nodriver.Tab.set_local_storage"]], "set_window_size() (tab method)": [[58, "nodriver.Tab.set_window_size"]], "set_window_state() (tab method)": [[58, "nodriver.Tab.set_window_state"]], "sleep() (tab method)": [[58, "nodriver.Tab.sleep"]], "target (tab property)": [[58, "nodriver.Tab.target"]], "template_location() (tab method)": [[58, "nodriver.Tab.template_location"]], "verify_cf() (tab method)": [[58, "nodriver.Tab.verify_cf"]], "wait() (tab method)": [[58, "nodriver.Tab.wait"]], "wait_for() (tab method)": [[58, "nodriver.Tab.wait_for"]], "websocket (tab property)": [[58, "nodriver.Tab.websocket"]], "xpath() (tab method)": [[58, "nodriver.Tab.xpath"]]}})