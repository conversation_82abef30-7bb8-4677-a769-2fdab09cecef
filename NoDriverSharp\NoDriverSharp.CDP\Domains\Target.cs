using System.Text.Json.Serialization;
using NoDriverSharp.CDP.Common;

namespace NoDriverSharp.CDP.Domains;

/// <summary>
/// Supports additional targets discovery and allows to attach to them.
/// </summary>
public static class Target
{
    /// <summary>
    /// Creates a new page.
    /// </summary>
    public class CreateTarget : CDPCommand<CreateTarget.Parameters>
    {
        public override string Method => "Target.createTarget";

        public class Parameters
        {
            /// <summary>
            /// The initial URL the page will be navigated to. An empty string indicates about:blank.
            /// </summary>
            [JsonPropertyName("url")]
            public string Url { get; set; } = string.Empty;

            /// <summary>
            /// Frame width in DIP (headless chrome only).
            /// </summary>
            [JsonPropertyName("width")]
            public int? Width { get; set; }

            /// <summary>
            /// Frame height in DIP (headless chrome only).
            /// </summary>
            [JsonPropertyName("height")]
            public int? Height { get; set; }

            /// <summary>
            /// The browser context to create the page in.
            /// </summary>
            [JsonPropertyName("browserContextId")]
            public string? BrowserContextId { get; set; }

            /// <summary>
            /// Whether to create a new window or tab (chrome-only, false by default).
            /// </summary>
            [JsonPropertyName("newWindow")]
            public bool? NewWindow { get; set; }

            /// <summary>
            /// Whether to create the target in background or foreground (chrome-only, false by default).
            /// </summary>
            [JsonPropertyName("background")]
            public bool? Background { get; set; }
        }

        public class Result
        {
            /// <summary>
            /// The id of the page opened.
            /// </summary>
            [JsonPropertyName("targetId")]
            public required TargetId TargetId { get; set; }
        }

        public CreateTarget(string url, int? width = null, int? height = null, string? browserContextId = null, bool? newWindow = null, bool? background = null)
            : base(new Parameters 
            { 
                Url = url, 
                Width = width, 
                Height = height, 
                BrowserContextId = browserContextId, 
                NewWindow = newWindow, 
                Background = background 
            })
        {
        }
    }

    /// <summary>
    /// Closes the target. If the target is a page that gets closed too.
    /// </summary>
    public class CloseTarget : CDPCommand<CloseTarget.Parameters>
    {
        public override string Method => "Target.closeTarget";

        public class Parameters
        {
            /// <summary>
            /// Target id.
            /// </summary>
            [JsonPropertyName("targetId")]
            public required TargetId TargetId { get; set; }
        }

        public class Result
        {
            /// <summary>
            /// Always set to true. If an error occurs, the response indicates protocol error.
            /// </summary>
            [JsonPropertyName("success")]
            public bool Success { get; set; }
        }

        public CloseTarget(TargetId targetId)
            : base(new Parameters { TargetId = targetId })
        {
        }
    }

    /// <summary>
    /// Retrieves a list of available targets.
    /// </summary>
    public class GetTargets : CDPCommand
    {
        public override string Method => "Target.getTargets";

        public class Result
        {
            /// <summary>
            /// The list of targets.
            /// </summary>
            [JsonPropertyName("targetInfos")]
            public TargetInfo[] TargetInfos { get; set; } = Array.Empty<TargetInfo>();
        }
    }

    /// <summary>
    /// Activates (focuses) the target.
    /// </summary>
    public class ActivateTarget : CDPCommand<ActivateTarget.Parameters>
    {
        public override string Method => "Target.activateTarget";

        public class Parameters
        {
            /// <summary>
            /// Target id.
            /// </summary>
            [JsonPropertyName("targetId")]
            public required TargetId TargetId { get; set; }
        }

        public ActivateTarget(TargetId targetId)
            : base(new Parameters { TargetId = targetId })
        {
        }
    }

    /// <summary>
    /// Information about a target.
    /// </summary>
    public class TargetInfo
    {
        /// <summary>
        /// Target id.
        /// </summary>
        [JsonPropertyName("targetId")]
        public required TargetId TargetId { get; set; }

        /// <summary>
        /// Target type.
        /// </summary>
        [JsonPropertyName("type")]
        public string Type { get; set; } = string.Empty;

        /// <summary>
        /// Target title.
        /// </summary>
        [JsonPropertyName("title")]
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// Target URL.
        /// </summary>
        [JsonPropertyName("url")]
        public string Url { get; set; } = string.Empty;

        /// <summary>
        /// Whether the target has an attached client.
        /// </summary>
        [JsonPropertyName("attached")]
        public bool Attached { get; set; }

        /// <summary>
        /// Opener target Id
        /// </summary>
        [JsonPropertyName("openerId")]
        public TargetId? OpenerId { get; set; }

        /// <summary>
        /// Whether the opened window is secure context and thus its document is a secure context.
        /// </summary>
        [JsonPropertyName("canAccessOpener")]
        public bool CanAccessOpener { get; set; }

        /// <summary>
        /// Frame id of originating window (is only set if target has an opener).
        /// </summary>
        [JsonPropertyName("openerFrameId")]
        public FrameId? OpenerFrameId { get; set; }

        /// <summary>
        /// Browser context id.
        /// </summary>
        [JsonPropertyName("browserContextId")]
        public string? BrowserContextId { get; set; }

        /// <summary>
        /// Provides additional details for specific target types. For example, for
        /// the type of "page", this may be set to "portal" or "prerender".
        /// </summary>
        [JsonPropertyName("subtype")]
        public string? Subtype { get; set; }
    }
}
