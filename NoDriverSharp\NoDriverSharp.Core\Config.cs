using System.Runtime.InteropServices;

namespace NoDriverSharp.Core;

/// <summary>
/// Configuration options for browser startup and behavior
/// </summary>
public class Config
{
    /// <summary>
    /// Path to the browser executable. If null, will auto-detect Chrome/Chromium.
    /// </summary>
    public string? BrowserExecutablePath { get; set; }

    /// <summary>
    /// Directory for user data. If null, a temporary directory will be created.
    /// </summary>
    public string? UserDataDir { get; set; }

    /// <summary>
    /// Whether to run browser in headless mode
    /// </summary>
    public bool Headless { get; set; } = false;

    /// <summary>
    /// Whether to enable sandbox mode
    /// </summary>
    public bool Sandbox { get; set; } = true;

    /// <summary>
    /// Host for the debugging port
    /// </summary>
    public string Host { get; set; } = "127.0.0.1";

    /// <summary>
    /// Port for the debugging interface. If 0, will auto-assign.
    /// </summary>
    public int Port { get; set; } = 0;

    /// <summary>
    /// Language setting for the browser
    /// </summary>
    public string Language { get; set; } = "en-US";

    /// <summary>
    /// Additional browser arguments
    /// </summary>
    public List<string> BrowserArgs { get; set; } = new();

    /// <summary>
    /// Whether to automatically discover targets
    /// </summary>
    public bool AutodiscoverTargets { get; set; } = true;

    /// <summary>
    /// Expert mode - disables web security and enables debugging features
    /// </summary>
    public bool Expert { get; set; } = false;

    /// <summary>
    /// Timeout for browser startup
    /// </summary>
    public TimeSpan StartupTimeout { get; set; } = TimeSpan.FromSeconds(30);

    /// <summary>
    /// Timeout for page navigation
    /// </summary>
    public TimeSpan NavigationTimeout { get; set; } = TimeSpan.FromSeconds(30);

    /// <summary>
    /// Timeout for element operations
    /// </summary>
    public TimeSpan ElementTimeout { get; set; } = TimeSpan.FromSeconds(10);

    /// <summary>
    /// Whether to keep the user data directory after browser shutdown
    /// </summary>
    public bool KeepUserDataDir { get; set; } = false;

    /// <summary>
    /// Window size for the browser
    /// </summary>
    public (int Width, int Height)? WindowSize { get; set; }

    /// <summary>
    /// Window position for the browser
    /// </summary>
    public (int X, int Y)? WindowPosition { get; set; }

    /// <summary>
    /// Whether to maximize the browser window
    /// </summary>
    public bool Maximized { get; set; } = false;

    /// <summary>
    /// Creates a new Config instance with default values
    /// </summary>
    public Config()
    {
        // Set default browser args for anti-detection
        BrowserArgs.AddRange(GetDefaultBrowserArgs());
    }

    /// <summary>
    /// Gets the default browser arguments for anti-detection
    /// </summary>
    private static List<string> GetDefaultBrowserArgs()
    {
        return new List<string>
        {
            "--remote-allow-origins=*",
            "--no-first-run",
            "--no-service-autorun",
            "--no-default-browser-check",
            "--homepage=about:blank",
            "--no-pings",
            "--password-store=basic",
            "--disable-infobars",
            "--disable-breakpad",
            "--disable-dev-shm-usage",
            "--disable-session-crashed-bubble",
            "--disable-search-engine-choice-screen",
            "--disable-blink-features=AutomationControlled",
            "--disable-features=VizDisplayCompositor"
        };
    }

    /// <summary>
    /// Gets all browser arguments including defaults and custom ones
    /// </summary>
    public List<string> GetAllBrowserArgs()
    {
        var args = new List<string>(BrowserArgs);

        // Add debugging port
        args.Add($"--remote-debugging-port={Port}");
        args.Add($"--remote-debugging-host={Host}");

        // Add headless if specified
        if (Headless)
        {
            args.Add("--headless=new");
        }

        // Add sandbox settings
        if (!Sandbox)
        {
            args.Add("--no-sandbox");
            args.Add("--disable-setuid-sandbox");
        }

        // Add user data directory
        if (!string.IsNullOrEmpty(UserDataDir))
        {
            args.Add($"--user-data-dir={UserDataDir}");
        }

        // Add language
        args.Add($"--lang={Language}");

        // Add expert mode settings
        if (Expert)
        {
            args.AddRange(new[]
            {
                "--disable-web-security",
                "--disable-site-isolation-trials",
                "--disable-features=VizDisplayCompositor"
            });
        }

        // Add window size
        if (WindowSize.HasValue)
        {
            args.Add($"--window-size={WindowSize.Value.Width},{WindowSize.Value.Height}");
        }

        // Add window position
        if (WindowPosition.HasValue)
        {
            args.Add($"--window-position={WindowPosition.Value.X},{WindowPosition.Value.Y}");
        }

        // Add maximized
        if (Maximized)
        {
            args.Add("--start-maximized");
        }

        return args.Distinct().ToList();
    }

    /// <summary>
    /// Auto-detects the Chrome/Chromium executable path
    /// </summary>
    public static string? FindBrowserExecutable()
    {
        var possiblePaths = new List<string>();

        if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
        {
            possiblePaths.AddRange(new[]
            {
                @"C:\Program Files\Google\Chrome\Application\chrome.exe",
                @"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
                @"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe",
                @"C:\Program Files\Microsoft\Edge\Application\msedge.exe",
                @"C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe",
                @"C:\Program Files\BraveSoftware\Brave-Browser\Application\brave.exe",
                @"C:\Program Files (x86)\BraveSoftware\Brave-Browser\Application\brave.exe"
            });
        }
        else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
        {
            possiblePaths.AddRange(new[]
            {
                "/usr/bin/google-chrome",
                "/usr/bin/google-chrome-stable",
                "/usr/bin/chromium",
                "/usr/bin/chromium-browser",
                "/snap/bin/chromium",
                "/usr/bin/brave-browser"
            });
        }
        else if (RuntimeInformation.IsOSPlatform(OSPlatform.OSX))
        {
            possiblePaths.AddRange(new[]
            {
                "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
                "/Applications/Chromium.app/Contents/MacOS/Chromium",
                "/Applications/Brave Browser.app/Contents/MacOS/Brave Browser",
                "/Applications/Microsoft Edge.app/Contents/MacOS/Microsoft Edge"
            });
        }

        return possiblePaths.FirstOrDefault(File.Exists);
    }

    /// <summary>
    /// Creates a temporary user data directory
    /// </summary>
    public static string CreateTempUserDataDir()
    {
        var tempDir = Path.Combine(Path.GetTempPath(), "NoDriverSharp_" + Guid.NewGuid().ToString("N")[..8]);
        Directory.CreateDirectory(tempDir);
        return tempDir;
    }
}
