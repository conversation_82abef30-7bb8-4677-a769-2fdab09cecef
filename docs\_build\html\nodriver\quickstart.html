<!doctype html>
<html class="no-js" lang="en" data-content_root="../">
  <head><meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width,initial-scale=1"/>
    <meta name="color-scheme" content="light dark"><meta name="viewport" content="width=device-width, initial-scale=1" />
<link rel="index" title="Index" href="../genindex.html" /><link rel="search" title="Search" href="../search.html" /><link rel="next" title="Browser class" href="classes/browser.html" /><link rel="prev" title="NODRIVER" href="../index.html" />

    <!-- Generated with Sphinx 7.2.6 and Furo 2023.09.10 -->
        <title>Quickstart guide - nodriver documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=fa44fd50" />
    <link rel="stylesheet" type="text/css" href="../_static/styles/furo.css?v=135e06be" />
    <link rel="stylesheet" type="text/css" href="../_static/styles/furo-extensions.css?v=36a5483c" />
    <link rel="stylesheet" type="text/css" href="../_static/./custom.css?v=3934642d" />
    
    


<style>
  body {
    --color-code-background: #eeffcc;
  --color-code-foreground: black;
  
  }
  @media not print {
    body[data-theme="dark"] {
      --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8eefba;
  --color-background-primary: #111112;
  --color-problematic: #e7aeae;
  
    }
    @media (prefers-color-scheme: dark) {
      body:not([data-theme="light"]) {
        --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8eefba;
  --color-background-primary: #111112;
  --color-problematic: #e7aeae;
  
      }
    }
  }
</style></head>
  <body>
    
    <script>
      document.body.dataset.theme = localStorage.getItem("theme") || "auto";
    </script>
    

<svg xmlns="http://www.w3.org/2000/svg" style="display: none;">
  <symbol id="svg-toc" viewBox="0 0 24 24">
    <title>Contents</title>
    <svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 1024 1024">
      <path d="M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM115.4 518.9L271.7 642c5.8 4.6 14.4.5 14.4-6.9V388.9c0-7.4-8.5-11.5-14.4-6.9L115.4 505.1a8.74 8.74 0 0 0 0 13.8z"/>
    </svg>
  </symbol>
  <symbol id="svg-menu" viewBox="0 0 24 24">
    <title>Menu</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-menu">
      <line x1="3" y1="12" x2="21" y2="12"></line>
      <line x1="3" y1="6" x2="21" y2="6"></line>
      <line x1="3" y1="18" x2="21" y2="18"></line>
    </svg>
  </symbol>
  <symbol id="svg-arrow-right" viewBox="0 0 24 24">
    <title>Expand</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-chevron-right">
      <polyline points="9 18 15 12 9 6"></polyline>
    </svg>
  </symbol>
  <symbol id="svg-sun" viewBox="0 0 24 24">
    <title>Light mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="feather-sun">
      <circle cx="12" cy="12" r="5"></circle>
      <line x1="12" y1="1" x2="12" y2="3"></line>
      <line x1="12" y1="21" x2="12" y2="23"></line>
      <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
      <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
      <line x1="1" y1="12" x2="3" y2="12"></line>
      <line x1="21" y1="12" x2="23" y2="12"></line>
      <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
      <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
    </svg>
  </symbol>
  <symbol id="svg-moon" viewBox="0 0 24 24">
    <title>Dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-moon">
      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
      <path d="M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z" />
    </svg>
  </symbol>
  <symbol id="svg-sun-half" viewBox="0 0 24 24">
    <title>Auto light/dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-shadow">
      <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
      <circle cx="12" cy="12" r="9" />
      <path d="M13 12h5" />
      <path d="M13 15h4" />
      <path d="M13 18h1" />
      <path d="M13 9h4" />
      <path d="M13 6h1" />
    </svg>
  </symbol>
</svg>

<input type="checkbox" class="sidebar-toggle" name="__navigation" id="__navigation">
<input type="checkbox" class="sidebar-toggle" name="__toc" id="__toc">
<label class="overlay sidebar-overlay" for="__navigation">
  <div class="visually-hidden">Hide navigation sidebar</div>
</label>
<label class="overlay toc-overlay" for="__toc">
  <div class="visually-hidden">Hide table of contents sidebar</div>
</label>



<div class="page">
  <header class="mobile-header">
    <div class="header-left">
      <label class="nav-overlay-icon" for="__navigation">
        <div class="visually-hidden">Toggle site navigation sidebar</div>
        <i class="icon"><svg><use href="#svg-menu"></use></svg></i>
      </label>
    </div>
    <div class="header-center">
      <a href="../index.html"><div class="brand">nodriver  documentation</div></a>
    </div>
    <div class="header-right">
      <div class="theme-toggle-container theme-toggle-header">
        <button class="theme-toggle">
          <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
          <svg class="theme-icon-when-auto"><use href="#svg-sun-half"></use></svg>
          <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
          <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
        </button>
      </div>
      <label class="toc-overlay-icon toc-header-icon" for="__toc">
        <div class="visually-hidden">Toggle table of contents sidebar</div>
        <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
      </label>
    </div>
  </header>
  <aside class="sidebar-drawer">
    <div class="sidebar-container">
      
      <div class="sidebar-sticky"><div class="sidebar-scroll"><a class="sidebar-brand" href="../index.html">
  
  
  <span class="sidebar-brand-text">nodriver  documentation</span>
  
</a><form class="sidebar-search-container" method="get" action="../search.html" role="search">
  <input class="sidebar-search" placeholder="Search" name="q" aria-label="Search">
  <input type="hidden" name="check_keywords" value="yes">
  <input type="hidden" name="area" value="default">
</form>
<div id="searchbox"></div><div class="sidebar-tree">
  <ul class="current">
<li class="toctree-l1 current current-page"><a class="current reference internal" href="#">Quickstart guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="#usage-example">usage example</a></li>
<li class="toctree-l1"><a class="reference internal" href="#more-complete-example">More complete example</a></li>
<li class="toctree-l1"><a class="reference internal" href="#custom-starting-options">Custom starting options</a></li>
<li class="toctree-l1"><a class="reference internal" href="#alternative-custom-options">Alternative custom options</a></li>
<li class="toctree-l1"><a class="reference internal" href="#proxies-socks5-authenticated-too">Proxies (socks5, authenticated too)</a></li>
<li class="toctree-l1"><a class="reference internal" href="#concrete-example">Concrete example</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="classes/browser.html">Browser class</a></li>
<li class="toctree-l1"><a class="reference internal" href="classes/tab.html">Tab class</a></li>
<li class="toctree-l1"><a class="reference internal" href="classes/element.html">Element class</a></li>
<li class="toctree-l1"><a class="reference internal" href="classes/others_and_helpers.html">Other classes and Helper classes</a></li>
</ul>
<ul>
<li class="toctree-l1 has-children"><a class="reference internal" href="cdp.html">CDP object</a><input class="toctree-checkbox" id="toctree-checkbox-1" name="toctree-checkbox-1" role="switch" type="checkbox"/><label for="toctree-checkbox-1"><div class="visually-hidden">Toggle navigation of CDP object</div><i class="icon"><svg><use href="#svg-arrow-right"></use></svg></i></label><ul>
<li class="toctree-l2"><a class="reference internal" href="cdp/accessibility.html">Accessibility</a></li>
<li class="toctree-l2"><a class="reference internal" href="cdp/animation.html">Animation</a></li>
<li class="toctree-l2"><a class="reference internal" href="cdp/audits.html">Audits</a></li>
<li class="toctree-l2"><a class="reference internal" href="cdp/autofill.html">Autofill</a></li>
<li class="toctree-l2"><a class="reference internal" href="cdp/background_service.html">BackgroundService</a></li>
<li class="toctree-l2"><a class="reference internal" href="cdp/bluetooth_emulation.html">BluetoothEmulation</a></li>
<li class="toctree-l2"><a class="reference internal" href="cdp/browser.html">Browser</a></li>
<li class="toctree-l2"><a class="reference internal" href="cdp/cache_storage.html">CacheStorage</a></li>
<li class="toctree-l2"><a class="reference internal" href="cdp/cast.html">Cast</a></li>
<li class="toctree-l2"><a class="reference internal" href="cdp/console.html">Console</a></li>
<li class="toctree-l2"><a class="reference internal" href="cdp/css.html">CSS</a></li>
<li class="toctree-l2"><a class="reference internal" href="cdp/debugger.html">Debugger</a></li>
<li class="toctree-l2"><a class="reference internal" href="cdp/device_access.html">DeviceAccess</a></li>
<li class="toctree-l2"><a class="reference internal" href="cdp/device_orientation.html">DeviceOrientation</a></li>
<li class="toctree-l2"><a class="reference internal" href="cdp/dom.html">DOM</a></li>
<li class="toctree-l2"><a class="reference internal" href="cdp/dom_debugger.html">DOMDebugger</a></li>
<li class="toctree-l2"><a class="reference internal" href="cdp/dom_snapshot.html">DOMSnapshot</a></li>
<li class="toctree-l2"><a class="reference internal" href="cdp/dom_storage.html">DOMStorage</a></li>
<li class="toctree-l2"><a class="reference internal" href="cdp/emulation.html">Emulation</a></li>
<li class="toctree-l2"><a class="reference internal" href="cdp/event_breakpoints.html">EventBreakpoints</a></li>
<li class="toctree-l2"><a class="reference internal" href="cdp/extensions.html">Extensions</a></li>
<li class="toctree-l2"><a class="reference internal" href="cdp/fed_cm.html">FedCm</a></li>
<li class="toctree-l2"><a class="reference internal" href="cdp/fetch.html">Fetch</a></li>
<li class="toctree-l2"><a class="reference internal" href="cdp/file_system.html">FileSystem</a></li>
<li class="toctree-l2"><a class="reference internal" href="cdp/headless_experimental.html">HeadlessExperimental</a></li>
<li class="toctree-l2"><a class="reference internal" href="cdp/heap_profiler.html">HeapProfiler</a></li>
<li class="toctree-l2"><a class="reference internal" href="cdp/indexed_db.html">IndexedDB</a></li>
<li class="toctree-l2"><a class="reference internal" href="cdp/input_.html">Input</a></li>
<li class="toctree-l2"><a class="reference internal" href="cdp/inspector.html">Inspector</a></li>
<li class="toctree-l2"><a class="reference internal" href="cdp/io.html">IO</a></li>
<li class="toctree-l2"><a class="reference internal" href="cdp/layer_tree.html">LayerTree</a></li>
<li class="toctree-l2"><a class="reference internal" href="cdp/log.html">Log</a></li>
<li class="toctree-l2"><a class="reference internal" href="cdp/media.html">Media</a></li>
<li class="toctree-l2"><a class="reference internal" href="cdp/memory.html">Memory</a></li>
<li class="toctree-l2"><a class="reference internal" href="cdp/network.html">Network</a></li>
<li class="toctree-l2"><a class="reference internal" href="cdp/overlay.html">Overlay</a></li>
<li class="toctree-l2"><a class="reference internal" href="cdp/page.html">Page</a></li>
<li class="toctree-l2"><a class="reference internal" href="cdp/performance.html">Performance</a></li>
<li class="toctree-l2"><a class="reference internal" href="cdp/performance_timeline.html">PerformanceTimeline</a></li>
<li class="toctree-l2"><a class="reference internal" href="cdp/preload.html">Preload</a></li>
<li class="toctree-l2"><a class="reference internal" href="cdp/profiler.html">Profiler</a></li>
<li class="toctree-l2"><a class="reference internal" href="cdp/pwa.html">PWA</a></li>
<li class="toctree-l2"><a class="reference internal" href="cdp/runtime.html">Runtime</a></li>
<li class="toctree-l2"><a class="reference internal" href="cdp/schema.html">Schema</a></li>
<li class="toctree-l2"><a class="reference internal" href="cdp/security.html">Security</a></li>
<li class="toctree-l2"><a class="reference internal" href="cdp/service_worker.html">ServiceWorker</a></li>
<li class="toctree-l2"><a class="reference internal" href="cdp/storage.html">Storage</a></li>
<li class="toctree-l2"><a class="reference internal" href="cdp/system_info.html">SystemInfo</a></li>
<li class="toctree-l2"><a class="reference internal" href="cdp/target.html">Target</a></li>
<li class="toctree-l2"><a class="reference internal" href="cdp/tethering.html">Tethering</a></li>
<li class="toctree-l2"><a class="reference internal" href="cdp/tracing.html">Tracing</a></li>
<li class="toctree-l2"><a class="reference internal" href="cdp/web_audio.html">WebAudio</a></li>
<li class="toctree-l2"><a class="reference internal" href="cdp/web_authn.html">WebAuthn</a></li>
</ul>
</li>
</ul>

</div>
</div>
      </div>
      
    </div>
  </aside>
  <div class="main">
    <div class="content">
      <div class="article-container">
        <a href="#" class="back-to-top muted-link">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
            <path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8v12z"></path>
          </svg>
          <span>Back to top</span>
        </a>
        <div class="content-icon-container">
          
<div class="theme-toggle-container theme-toggle-content">
            <button class="theme-toggle">
              <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
              <svg class="theme-icon-when-auto"><use href="#svg-sun-half"></use></svg>
              <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
              <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
            </button>
          </div>
          <label class="toc-overlay-icon toc-content-icon" for="__toc">
            <div class="visually-hidden">Toggle table of contents sidebar</div>
            <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
          </label>
        </div>
        <article role="main">
          <section id="quickstart-guide">
<h1>Quickstart guide<a class="headerlink" href="#quickstart-guide" title="Link to this heading">#</a></h1>
<section id="installation">
<h2>Installation<a class="headerlink" href="#installation" title="Link to this heading">#</a></h2>
<p>Since it’s a part of undetected-chromedriver, installation goes via</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="c1"># todo. use pip install nodriver instead</span>
<span class="n">pip</span> <span class="n">install</span> <span class="n">undetected</span><span class="o">-</span><span class="n">chromedriver</span>
</pre></div>
</div>
<hr class="docutils" />
<p>Or as a seperate package via:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="n">pip</span> <span class="n">install</span> <span class="n">nodriver</span>
</pre></div>
</div>
</section>
</section>
<section id="usage-example">
<span id="getting-started-commands"></span><h1>usage example<a class="headerlink" href="#usage-example" title="Link to this heading">#</a></h1>
<p>The aim of this project (just like undetected-chromedriver, somewhere long ago)
is to keep it short and simple, so you can quickly open an editor or interactive session,
type or paste a few lines and off you go.</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">nodriver</span> <span class="k">as</span> <span class="nn">uc</span>

<span class="k">async</span> <span class="k">def</span> <span class="nf">main</span><span class="p">():</span>

    <span class="n">browser</span> <span class="o">=</span> <span class="k">await</span> <span class="n">uc</span><span class="o">.</span><span class="n">start</span><span class="p">()</span>
    <span class="n">page</span> <span class="o">=</span> <span class="k">await</span> <span class="n">browser</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s1">&#39;https://www.nowsecure.nl&#39;</span><span class="p">)</span>

    <span class="o">...</span> <span class="n">further</span> <span class="n">code</span> <span class="o">...</span>

<span class="k">if</span> <span class="vm">__name__</span> <span class="o">==</span> <span class="s1">&#39;__main__&#39;</span><span class="p">:</span>
    <span class="c1"># since asyncio.run never worked (for me)</span>
    <span class="n">uc</span><span class="o">.</span><span class="n">loop</span><span class="p">()</span><span class="o">.</span><span class="n">run_until_complete</span><span class="p">(</span><span class="n">main</span><span class="p">())</span>
</pre></div>
</div>
</section>
<section id="more-complete-example">
<h1>More complete example<a class="headerlink" href="#more-complete-example" title="Link to this heading">#</a></h1>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">nodriver</span>

<span class="k">async</span> <span class="k">def</span> <span class="nf">main</span><span class="p">():</span>

    <span class="n">browser</span> <span class="o">=</span> <span class="k">await</span> <span class="n">nodriver</span><span class="o">.</span><span class="n">start</span><span class="p">()</span>
    <span class="n">page</span> <span class="o">=</span> <span class="k">await</span> <span class="n">browser</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s1">&#39;https://www.nowsecure.nl&#39;</span><span class="p">)</span>

    <span class="k">await</span> <span class="n">page</span><span class="o">.</span><span class="n">save_screenshot</span><span class="p">()</span>
    <span class="k">await</span> <span class="n">page</span><span class="o">.</span><span class="n">get_content</span><span class="p">()</span>
    <span class="k">await</span> <span class="n">page</span><span class="o">.</span><span class="n">scroll_down</span><span class="p">(</span><span class="mi">150</span><span class="p">)</span>
    <span class="n">elems</span> <span class="o">=</span> <span class="k">await</span> <span class="n">page</span><span class="o">.</span><span class="n">select_all</span><span class="p">(</span><span class="s1">&#39;*[src]&#39;</span><span class="p">)</span>

    <span class="k">for</span> <span class="n">elem</span> <span class="ow">in</span> <span class="n">elems</span><span class="p">:</span>
        <span class="k">await</span> <span class="n">elem</span><span class="o">.</span><span class="n">flash</span><span class="p">()</span>

    <span class="n">page2</span> <span class="o">=</span> <span class="k">await</span> <span class="n">browser</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s1">&#39;https://twitter.com&#39;</span><span class="p">,</span> <span class="n">new_tab</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
    <span class="n">page3</span> <span class="o">=</span> <span class="k">await</span> <span class="n">browser</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s1">&#39;https://github.com/ultrafunkamsterdam/nodriver&#39;</span><span class="p">,</span> <span class="n">new_window</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>

    <span class="k">for</span> <span class="n">p</span> <span class="ow">in</span> <span class="p">(</span><span class="n">page</span><span class="p">,</span> <span class="n">page2</span><span class="p">,</span> <span class="n">page3</span><span class="p">):</span>
       <span class="k">await</span> <span class="n">p</span><span class="o">.</span><span class="n">bring_to_front</span><span class="p">()</span>
       <span class="k">await</span> <span class="n">p</span><span class="o">.</span><span class="n">scroll_down</span><span class="p">(</span><span class="mi">200</span><span class="p">)</span>
       <span class="k">await</span> <span class="n">p</span>   <span class="c1"># wait for events to be processed</span>
       <span class="k">await</span> <span class="n">p</span><span class="o">.</span><span class="n">reload</span><span class="p">()</span>
       <span class="k">if</span> <span class="n">p</span> <span class="o">!=</span> <span class="n">page3</span><span class="p">:</span>
           <span class="k">await</span> <span class="n">p</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>

<span class="k">if</span> <span class="vm">__name__</span> <span class="o">==</span> <span class="s1">&#39;__main__&#39;</span><span class="p">:</span>

    <span class="c1"># since asyncio.run never worked (for me)</span>
    <span class="n">uc</span><span class="o">.</span><span class="n">loop</span><span class="p">()</span><span class="o">.</span><span class="n">run_until_complete</span><span class="p">(</span><span class="n">main</span><span class="p">())</span>
</pre></div>
</div>
</section>
<section id="custom-starting-options">
<h1>Custom starting options<a class="headerlink" href="#custom-starting-options" title="Link to this heading">#</a></h1>
<p>I’ll leave out the async boilerplate here</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">nodriver</span> <span class="kn">import</span> <span class="o">*</span>

<span class="n">browser</span> <span class="o">=</span> <span class="k">await</span> <span class="n">start</span><span class="p">(</span>
    <span class="n">headless</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span>
    <span class="n">user_data_dir</span><span class="o">=</span><span class="s2">&quot;/path/to/existing/profile&quot;</span><span class="p">,</span>  <span class="c1"># by specifying it, it won&#39;t be automatically cleaned up when finished</span>
    <span class="n">browser_executable_path</span><span class="o">=</span><span class="s2">&quot;/path/to/some/other/browser&quot;</span><span class="p">,</span>
    <span class="n">browser_args</span><span class="o">=</span><span class="p">[</span><span class="s1">&#39;--some-browser-arg=true&#39;</span><span class="p">,</span> <span class="s1">&#39;--some-other-option&#39;</span><span class="p">],</span>
    <span class="n">lang</span><span class="o">=</span><span class="s2">&quot;en-US&quot;</span>   <span class="c1"># this could set iso-language-code in navigator, not recommended to change</span>
<span class="p">)</span>
<span class="n">tab</span> <span class="o">=</span> <span class="k">await</span> <span class="n">browser</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s1">&#39;https://somewebsite.com&#39;</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="alternative-custom-options">
<h1>Alternative custom options<a class="headerlink" href="#alternative-custom-options" title="Link to this heading">#</a></h1>
<p>I’ll leave out the async boilerplate here</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">nodriver</span> <span class="kn">import</span> <span class="o">*</span>

<span class="n">config</span> <span class="o">=</span> <span class="n">Config</span><span class="p">()</span>
<span class="n">config</span><span class="o">.</span><span class="n">headless</span> <span class="o">=</span> <span class="kc">False</span>
<span class="n">config</span><span class="o">.</span><span class="n">user_data_dir</span><span class="o">=</span><span class="s2">&quot;/path/to/existing/profile&quot;</span><span class="p">,</span>  <span class="c1"># by specifying it, it won&#39;t be automatically cleaned up when finished</span>
<span class="n">config</span><span class="o">.</span><span class="n">browser_executable_path</span><span class="o">=</span><span class="s2">&quot;/path/to/some/other/browser&quot;</span><span class="p">,</span>
<span class="n">config</span><span class="o">.</span><span class="n">browser_args</span><span class="o">=</span><span class="p">[</span><span class="s1">&#39;--some-browser-arg=true&#39;</span><span class="p">,</span> <span class="s1">&#39;--some-other-option&#39;</span><span class="p">],</span>
<span class="n">config</span><span class="o">.</span><span class="n">lang</span><span class="o">=</span><span class="s2">&quot;en-US&quot;</span>   <span class="c1"># this could set iso-language-code in navigator, not recommended to change</span>
<span class="p">)</span>
</pre></div>
</div>
</section>
<section id="proxies-socks5-authenticated-too">
<h1>Proxies (socks5, authenticated too)<a class="headerlink" href="#proxies-socks5-authenticated-too" title="Link to this heading">#</a></h1>
<p>You can create as many browser contexts, with each a
different proxy, while normally you can only use 1 proxy
for all your browser windows.</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">nodriver</span> <span class="kn">import</span> <span class="o">*</span>
<span class="n">browser</span> <span class="o">=</span> <span class="k">await</span> <span class="n">start</span><span class="p">()</span>
<span class="n">proxied_tab</span> <span class="o">=</span> <span class="k">await</span> <span class="n">browser</span><span class="o">.</span><span class="n">create_context</span><span class="p">(</span>
    <span class="n">url</span><span class="p">:</span> <span class="o">...</span>
    <span class="n">proxy_server</span> <span class="o">=</span> <span class="s2">&quot;socks5://myuser:<EMAIL>&quot;</span>
    <span class="n">proxy_bypass_list</span> <span class="o">=</span> <span class="p">[</span><span class="s2">&quot;localhost&quot;</span><span class="p">]</span>
<span class="p">)</span>
<span class="k">await</span> <span class="n">proxied_tab</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s1">&#39;https://whatismyip.com&#39;</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="concrete-example">
<h1>Concrete example<a class="headerlink" href="#concrete-example" title="Link to this heading">#</a></h1>
<p>A more concrete example, which can be found in the ./example/ folder,
shows a script to create a twitter account</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">asyncio</span>
<span class="kn">import</span> <span class="nn">random</span>
<span class="kn">import</span> <span class="nn">string</span>
<span class="kn">import</span> <span class="nn">logging</span>

<span class="n">logging</span><span class="o">.</span><span class="n">basicConfig</span><span class="p">(</span><span class="n">level</span><span class="o">=</span><span class="mi">30</span><span class="p">)</span>

<span class="kn">import</span> <span class="nn">nodriver</span> <span class="k">as</span> <span class="nn">uc</span>

<span class="n">months</span> <span class="o">=</span> <span class="p">[</span>
    <span class="s2">&quot;january&quot;</span><span class="p">,</span>
    <span class="s2">&quot;february&quot;</span><span class="p">,</span>
    <span class="s2">&quot;march&quot;</span><span class="p">,</span>
    <span class="s2">&quot;april&quot;</span><span class="p">,</span>
    <span class="s2">&quot;may&quot;</span><span class="p">,</span>
    <span class="s2">&quot;june&quot;</span><span class="p">,</span>
    <span class="s2">&quot;july&quot;</span><span class="p">,</span>
    <span class="s2">&quot;august&quot;</span><span class="p">,</span>
    <span class="s2">&quot;september&quot;</span><span class="p">,</span>
    <span class="s2">&quot;october&quot;</span><span class="p">,</span>
    <span class="s2">&quot;november&quot;</span><span class="p">,</span>
    <span class="s2">&quot;december&quot;</span><span class="p">,</span>
<span class="p">]</span>


<span class="k">async</span> <span class="k">def</span> <span class="nf">main</span><span class="p">():</span>
    <span class="n">driver</span> <span class="o">=</span> <span class="k">await</span> <span class="n">uc</span><span class="o">.</span><span class="n">start</span><span class="p">()</span>

    <span class="n">tab</span> <span class="o">=</span> <span class="k">await</span> <span class="n">driver</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;https://twitter.com&quot;</span><span class="p">)</span>

    <span class="c1"># wait for text to appear instead of a static number of seconds to wait</span>
    <span class="c1"># this does not always work as expected, due to speed.</span>
    <span class="nb">print</span><span class="p">(</span><span class="s1">&#39;finding the &quot;create account&quot; button&#39;</span><span class="p">)</span>
    <span class="n">create_account</span> <span class="o">=</span> <span class="k">await</span> <span class="n">tab</span><span class="o">.</span><span class="n">find</span><span class="p">(</span><span class="s2">&quot;create account&quot;</span><span class="p">,</span> <span class="n">best_match</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>

    <span class="nb">print</span><span class="p">(</span><span class="s1">&#39;&quot;create account&quot; =&gt; click&#39;</span><span class="p">)</span>
    <span class="k">await</span> <span class="n">create_account</span><span class="o">.</span><span class="n">click</span><span class="p">()</span>

    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;finding the email input field&quot;</span><span class="p">)</span>
    <span class="n">email</span> <span class="o">=</span> <span class="k">await</span> <span class="n">tab</span><span class="o">.</span><span class="n">select</span><span class="p">(</span><span class="s2">&quot;input[type=email]&quot;</span><span class="p">)</span>

    <span class="c1"># sometimes, email field is not shown, because phone is being asked instead</span>
    <span class="c1"># when this occurs, find the small text which says &quot;use email instead&quot;</span>
    <span class="k">if</span> <span class="ow">not</span> <span class="n">email</span><span class="p">:</span>
        <span class="n">use_mail_instead</span> <span class="o">=</span> <span class="k">await</span> <span class="n">tab</span><span class="o">.</span><span class="n">find</span><span class="p">(</span><span class="s2">&quot;use email instead&quot;</span><span class="p">)</span>
        <span class="c1"># and click it</span>
        <span class="k">await</span> <span class="n">use_mail_instead</span><span class="o">.</span><span class="n">click</span><span class="p">()</span>

        <span class="c1"># now find the email field again</span>
        <span class="n">email</span> <span class="o">=</span> <span class="k">await</span> <span class="n">tab</span><span class="o">.</span><span class="n">select</span><span class="p">(</span><span class="s2">&quot;input[type=email]&quot;</span><span class="p">)</span>

    <span class="n">randstr</span> <span class="o">=</span> <span class="k">lambda</span> <span class="n">k</span><span class="p">:</span> <span class="s2">&quot;&quot;</span><span class="o">.</span><span class="n">join</span><span class="p">(</span><span class="n">random</span><span class="o">.</span><span class="n">choices</span><span class="p">(</span><span class="n">string</span><span class="o">.</span><span class="n">ascii_letters</span><span class="p">,</span> <span class="n">k</span><span class="o">=</span><span class="n">k</span><span class="p">))</span>

    <span class="c1"># send keys to email field</span>
    <span class="nb">print</span><span class="p">(</span><span class="s1">&#39;filling in the &quot;email&quot; input field&#39;</span><span class="p">)</span>
    <span class="k">await</span> <span class="n">email</span><span class="o">.</span><span class="n">send_keys</span><span class="p">(</span><span class="s2">&quot;&quot;</span><span class="o">.</span><span class="n">join</span><span class="p">([</span><span class="n">randstr</span><span class="p">(</span><span class="mi">8</span><span class="p">),</span> <span class="s2">&quot;@&quot;</span><span class="p">,</span> <span class="n">randstr</span><span class="p">(</span><span class="mi">8</span><span class="p">),</span> <span class="s2">&quot;.com&quot;</span><span class="p">]))</span>

    <span class="c1"># find the name input field</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;finding the name input field&quot;</span><span class="p">)</span>
    <span class="n">name</span> <span class="o">=</span> <span class="k">await</span> <span class="n">tab</span><span class="o">.</span><span class="n">select</span><span class="p">(</span><span class="s2">&quot;input[type=text]&quot;</span><span class="p">)</span>

    <span class="c1"># again, send random text</span>
    <span class="nb">print</span><span class="p">(</span><span class="s1">&#39;filling in the &quot;name&quot; input field&#39;</span><span class="p">)</span>
    <span class="k">await</span> <span class="n">name</span><span class="o">.</span><span class="n">send_keys</span><span class="p">(</span><span class="n">randstr</span><span class="p">(</span><span class="mi">8</span><span class="p">))</span>

    <span class="c1"># since there are 3 select fields on the tab, we can use unpacking</span>
    <span class="c1"># to assign each field</span>
    <span class="nb">print</span><span class="p">(</span><span class="s1">&#39;finding the &quot;month&quot; , &quot;day&quot; and &quot;year&quot; fields in 1 go&#39;</span><span class="p">)</span>
    <span class="n">sel_month</span><span class="p">,</span> <span class="n">sel_day</span><span class="p">,</span> <span class="n">sel_year</span> <span class="o">=</span> <span class="k">await</span> <span class="n">tab</span><span class="o">.</span><span class="n">select_all</span><span class="p">(</span><span class="s2">&quot;select&quot;</span><span class="p">)</span>

    <span class="c1"># await sel_month.focus()</span>
    <span class="nb">print</span><span class="p">(</span><span class="s1">&#39;filling in the &quot;month&quot; input field&#39;</span><span class="p">)</span>
    <span class="k">await</span> <span class="n">sel_month</span><span class="o">.</span><span class="n">send_keys</span><span class="p">(</span><span class="n">months</span><span class="p">[</span><span class="n">random</span><span class="o">.</span><span class="n">randint</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="mi">11</span><span class="p">)]</span><span class="o">.</span><span class="n">title</span><span class="p">())</span>

    <span class="c1"># await sel_day.focus()</span>
    <span class="c1"># i don&#39;t want to bother with month-lengths and leap years</span>
    <span class="nb">print</span><span class="p">(</span><span class="s1">&#39;filling in the &quot;day&quot; input field&#39;</span><span class="p">)</span>
    <span class="k">await</span> <span class="n">sel_day</span><span class="o">.</span><span class="n">send_keys</span><span class="p">(</span><span class="nb">str</span><span class="p">(</span><span class="n">random</span><span class="o">.</span><span class="n">randint</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="mi">28</span><span class="p">)))</span>

    <span class="c1"># await sel_year.focus()</span>
    <span class="c1"># i don&#39;t want to bother with age restrictions</span>
    <span class="nb">print</span><span class="p">(</span><span class="s1">&#39;filling in the &quot;year&quot; input field&#39;</span><span class="p">)</span>
    <span class="k">await</span> <span class="n">sel_year</span><span class="o">.</span><span class="n">send_keys</span><span class="p">(</span><span class="nb">str</span><span class="p">(</span><span class="n">random</span><span class="o">.</span><span class="n">randint</span><span class="p">(</span><span class="mi">1980</span><span class="p">,</span> <span class="mi">2005</span><span class="p">)))</span>

    <span class="k">await</span> <span class="n">tab</span>

    <span class="c1"># let&#39;s handle the cookie nag as well</span>
    <span class="n">cookie_bar_accept</span> <span class="o">=</span> <span class="k">await</span> <span class="n">tab</span><span class="o">.</span><span class="n">find</span><span class="p">(</span><span class="s2">&quot;accept all&quot;</span><span class="p">,</span> <span class="n">best_match</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
    <span class="k">if</span> <span class="n">cookie_bar_accept</span><span class="p">:</span>
        <span class="k">await</span> <span class="n">cookie_bar_accept</span><span class="o">.</span><span class="n">click</span><span class="p">()</span>

    <span class="k">await</span> <span class="n">tab</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span>

    <span class="n">next_btn</span> <span class="o">=</span> <span class="k">await</span> <span class="n">tab</span><span class="o">.</span><span class="n">find</span><span class="p">(</span><span class="n">text</span><span class="o">=</span><span class="s2">&quot;next&quot;</span><span class="p">,</span> <span class="n">best_match</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
    <span class="c1"># for btn in reversed(next_btns):</span>
    <span class="k">await</span> <span class="n">next_btn</span><span class="o">.</span><span class="n">mouse_click</span><span class="p">()</span>

    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;sleeping 2 seconds&quot;</span><span class="p">)</span>
    <span class="k">await</span> <span class="n">tab</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mi">2</span><span class="p">)</span>  <span class="c1"># visually see what part we&#39;re actually in</span>

    <span class="nb">print</span><span class="p">(</span><span class="s1">&#39;finding &quot;next&quot; button&#39;</span><span class="p">)</span>
    <span class="n">next_btn</span> <span class="o">=</span> <span class="k">await</span> <span class="n">tab</span><span class="o">.</span><span class="n">find</span><span class="p">(</span><span class="n">text</span><span class="o">=</span><span class="s2">&quot;next&quot;</span><span class="p">,</span> <span class="n">best_match</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="s1">&#39;clicking &quot;next&quot; button&#39;</span><span class="p">)</span>
    <span class="k">await</span> <span class="n">next_btn</span><span class="o">.</span><span class="n">mouse_click</span><span class="p">()</span>

    <span class="c1"># just wait for some button, before we continue</span>
    <span class="k">await</span> <span class="n">tab</span><span class="o">.</span><span class="n">select</span><span class="p">(</span><span class="s2">&quot;[role=button]&quot;</span><span class="p">)</span>

    <span class="nb">print</span><span class="p">(</span><span class="s1">&#39;finding &quot;sign up&quot;  button&#39;</span><span class="p">)</span>
    <span class="n">sign_up_btn</span> <span class="o">=</span> <span class="k">await</span> <span class="n">tab</span><span class="o">.</span><span class="n">find</span><span class="p">(</span><span class="s2">&quot;Sign up&quot;</span><span class="p">,</span> <span class="n">best_match</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
    <span class="c1"># we need the second one</span>
    <span class="nb">print</span><span class="p">(</span><span class="s1">&#39;clicking &quot;sign up&quot;  button&#39;</span><span class="p">)</span>
    <span class="k">await</span> <span class="n">sign_up_btn</span><span class="o">.</span><span class="n">click</span><span class="p">()</span>

    <span class="nb">print</span><span class="p">(</span><span class="s1">&#39;the rest of the &quot;implementation&quot; is out of scope&#39;</span><span class="p">)</span>
    <span class="c1"># further implementation outside of scope</span>
    <span class="k">await</span> <span class="n">tab</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mi">10</span><span class="p">)</span>
    <span class="n">driver</span><span class="o">.</span><span class="n">stop</span><span class="p">()</span>

    <span class="c1"># verification code per mail</span>


<span class="k">if</span> <span class="vm">__name__</span> <span class="o">==</span> <span class="s2">&quot;__main__&quot;</span><span class="p">:</span>
    <span class="c1"># since asyncio.run never worked (for me)</span>
    <span class="c1"># i use</span>
    <span class="n">uc</span><span class="o">.</span><span class="n">loop</span><span class="p">()</span><span class="o">.</span><span class="n">run_until_complete</span><span class="p">(</span><span class="n">main</span><span class="p">())</span>
</pre></div>
</div>
</section>

        </article>
      </div>
      <footer>
        
        <div class="related-pages">
          <a class="next-page" href="classes/browser.html">
              <div class="page-info">
                <div class="context">
                  <span>Next</span>
                </div>
                <div class="title">Browser class</div>
              </div>
              <svg class="furo-related-icon"><use href="#svg-arrow-right"></use></svg>
            </a>
          <a class="prev-page" href="../index.html">
              <svg class="furo-related-icon"><use href="#svg-arrow-right"></use></svg>
              <div class="page-info">
                <div class="context">
                  <span>Previous</span>
                </div>
                
                <div class="title">Home</div>
                
              </div>
            </a>
        </div>
        <div class="bottom-of-page">
          <div class="left-details">
            <div class="copyright">
                Copyright &#169; 2023, Author
            </div>
            Made with <a href="https://www.sphinx-doc.org/">Sphinx</a> and <a class="muted-link" href="https://pradyunsg.me">@pradyunsg</a>'s
            
            <a href="https://github.com/pradyunsg/furo">Furo</a>
            
          </div>
          <div class="right-details">
            
          </div>
        </div>
        
      </footer>
    </div>
    <aside class="toc-drawer">
      
      
      <div class="toc-sticky toc-scroll">
        <div class="toc-title-container">
          <span class="toc-title">
            On this page
          </span>
        </div>
        <div class="toc-tree-container">
          <div class="toc-tree">
            <ul>
<li><a class="reference internal" href="#">Quickstart guide</a><ul>
<li><a class="reference internal" href="#installation">Installation</a></li>
</ul>
</li>
<li><a class="reference internal" href="#usage-example">usage example</a></li>
<li><a class="reference internal" href="#more-complete-example">More complete example</a></li>
<li><a class="reference internal" href="#custom-starting-options">Custom starting options</a></li>
<li><a class="reference internal" href="#alternative-custom-options">Alternative custom options</a></li>
<li><a class="reference internal" href="#proxies-socks5-authenticated-too">Proxies (socks5, authenticated too)</a></li>
<li><a class="reference internal" href="#concrete-example">Concrete example</a></li>
</ul>

          </div>
        </div>
      </div>
      
      
    </aside>
  </div>
</div><script src="../_static/documentation_options.js?v=5929fcd5"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../_static/scripts/furo.js?v=32e29ea5"></script>
    </body>
</html>