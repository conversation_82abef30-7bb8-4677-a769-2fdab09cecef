using System.Text.Json.Serialization;
using NoDriverSharp.CDP.Common;

namespace NoDriverSharp.CDP.Domains;

/// <summary>
/// Actions and events related to the inspected page belong to the page domain.
/// </summary>
public static class Page
{
    /// <summary>
    /// Navigates current page to the given URL.
    /// </summary>
    public class Navigate : CDPCommand<Navigate.Parameters>
    {
        public override string Method => "Page.navigate";

        public class Parameters
        {
            /// <summary>
            /// URL to navigate the page to.
            /// </summary>
            [JsonPropertyName("url")]
            public string Url { get; set; } = string.Empty;

            /// <summary>
            /// Referrer URL.
            /// </summary>
            [JsonPropertyName("referrer")]
            public string? Referrer { get; set; }

            /// <summary>
            /// Intended transition type.
            /// </summary>
            [JsonPropertyName("transitionType")]
            public TransitionType? TransitionType { get; set; }

            /// <summary>
            /// Frame id to navigate, if not specified navigates the top frame.
            /// </summary>
            [JsonPropertyName("frameId")]
            public FrameId? FrameId { get; set; }
        }

        public class Result
        {
            /// <summary>
            /// Frame id that has navigated (or failed to navigate)
            /// </summary>
            [JsonPropertyName("frameId")]
            public required FrameId FrameId { get; set; }

            /// <summary>
            /// Loader identifier.
            /// </summary>
            [JsonPropertyName("loaderId")]
            public LoaderId? LoaderId { get; set; }

            /// <summary>
            /// User friendly error message, present if and only if navigation has failed.
            /// </summary>
            [JsonPropertyName("errorText")]
            public string? ErrorText { get; set; }
        }

        public Navigate(string url, string? referrer = null, TransitionType? transitionType = null, FrameId? frameId = null)
            : base(new Parameters { Url = url, Referrer = referrer, TransitionType = transitionType, FrameId = frameId })
        {
        }
    }

    /// <summary>
    /// Reloads given page optionally ignoring the cache.
    /// </summary>
    public class Reload : CDPCommand<Reload.Parameters>
    {
        public override string Method => "Page.reload";

        public class Parameters
        {
            /// <summary>
            /// If true, browser cache is ignored (as if the user pressed Shift+refresh).
            /// </summary>
            [JsonPropertyName("ignoreCache")]
            public bool? IgnoreCache { get; set; }

            /// <summary>
            /// If set, the script will be injected into all frames of the inspected page after reload.
            /// </summary>
            [JsonPropertyName("scriptToEvaluateOnLoad")]
            public string? ScriptToEvaluateOnLoad { get; set; }
        }

        public Reload(bool? ignoreCache = null, string? scriptToEvaluateOnLoad = null)
            : base(new Parameters { IgnoreCache = ignoreCache, ScriptToEvaluateOnLoad = scriptToEvaluateOnLoad })
        {
        }
    }

    /// <summary>
    /// Enables page domain notifications.
    /// </summary>
    public class Enable : CDPCommand
    {
        public override string Method => "Page.enable";
    }

    /// <summary>
    /// Disables page domain notifications.
    /// </summary>
    public class Disable : CDPCommand
    {
        public override string Method => "Page.disable";
    }

    /// <summary>
    /// Capture page screenshot.
    /// </summary>
    public class CaptureScreenshot : CDPCommand<CaptureScreenshot.Parameters>
    {
        public override string Method => "Page.captureScreenshot";

        public class Parameters
        {
            /// <summary>
            /// Image compression format (defaults to png).
            /// </summary>
            [JsonPropertyName("format")]
            public string? Format { get; set; }

            /// <summary>
            /// Compression quality from range [0..100] (jpeg only).
            /// </summary>
            [JsonPropertyName("quality")]
            public int? Quality { get; set; }

            /// <summary>
            /// Capture the screenshot of a given region only.
            /// </summary>
            [JsonPropertyName("clip")]
            public Viewport? Clip { get; set; }

            /// <summary>
            /// Capture the screenshot from the surface, rather than the view.
            /// </summary>
            [JsonPropertyName("fromSurface")]
            public bool? FromSurface { get; set; }

            /// <summary>
            /// Capture the screenshot beyond the viewport.
            /// </summary>
            [JsonPropertyName("captureBeyondViewport")]
            public bool? CaptureBeyondViewport { get; set; }
        }

        public class Result
        {
            /// <summary>
            /// Base64-encoded image data.
            /// </summary>
            [JsonPropertyName("data")]
            public string Data { get; set; } = string.Empty;
        }

        public CaptureScreenshot(string? format = null, int? quality = null, Viewport? clip = null, bool? fromSurface = null, bool? captureBeyondViewport = null)
            : base(new Parameters { Format = format, Quality = quality, Clip = clip, FromSurface = fromSurface, CaptureBeyondViewport = captureBeyondViewport })
        {
        }
    }

    /// <summary>
    /// Transition type.
    /// </summary>
    public enum TransitionType
    {
        Link,
        Typed,
        AddressBar,
        AutoBookmark,
        AutoSubframe,
        ManualSubframe,
        Generated,
        AutoToplevel,
        FormSubmit,
        Reload,
        Keyword,
        KeywordGenerated,
        Other
    }

    /// <summary>
    /// Fired when the page is about to be unloaded.
    /// </summary>
    public class FrameNavigated : CDPEvent<FrameNavigated.Parameters>
    {
        public override string Method => "Page.frameNavigated";

        public class Parameters
        {
            /// <summary>
            /// Frame object.
            /// </summary>
            [JsonPropertyName("frame")]
            public required Frame Frame { get; set; }
        }
    }

    /// <summary>
    /// Information about the Frame on the page.
    /// </summary>
    public class Frame
    {
        /// <summary>
        /// Frame unique identifier.
        /// </summary>
        [JsonPropertyName("id")]
        public required FrameId Id { get; set; }

        /// <summary>
        /// Parent frame identifier.
        /// </summary>
        [JsonPropertyName("parentId")]
        public FrameId? ParentId { get; set; }

        /// <summary>
        /// Identifier of the loader associated with this frame.
        /// </summary>
        [JsonPropertyName("loaderId")]
        public required LoaderId LoaderId { get; set; }

        /// <summary>
        /// Frame's name as specified in the tag.
        /// </summary>
        [JsonPropertyName("name")]
        public string? Name { get; set; }

        /// <summary>
        /// Frame document's URL without fragment.
        /// </summary>
        [JsonPropertyName("url")]
        public string Url { get; set; } = string.Empty;

        /// <summary>
        /// Frame document's URL fragment including the '#'.
        /// </summary>
        [JsonPropertyName("urlFragment")]
        public string? UrlFragment { get; set; }

        /// <summary>
        /// Frame document's security origin.
        /// </summary>
        [JsonPropertyName("securityOrigin")]
        public string SecurityOrigin { get; set; } = string.Empty;

        /// <summary>
        /// Frame document's mimeType as determined by the browser.
        /// </summary>
        [JsonPropertyName("mimeType")]
        public string MimeType { get; set; } = string.Empty;
    }
}
