﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2012/06/nuspec.xsd">
  <metadata>
    <id>NoDriverSharp.CDP</id>
    <version>1.0.0</version>
    <authors>NoDriverSharp Contributors</authors>
    <license type="expression">MIT</license>
    <licenseUrl>https://licenses.nuget.org/MIT</licenseUrl>
    <description>Chrome DevTools Protocol classes for NoDriverSharp</description>
    <tags>cdp chrome devtools protocol</tags>
    <repository type="git" url="https://github.com/NoDriverSharp/NoDriverSharp" commit="256761f5b67bbcfbfda299dff3b909d3cca4dc3a" />
    <dependencies>
      <group targetFramework="net9.0">
        <dependency id="System.Text.Json" version="8.0.5" exclude="Build,Analyzers" />
      </group>
    </dependencies>
  </metadata>
  <files>
    <file src="C:\Users\<USER>\source\repos\nodriver\NoDriverSharp\NoDriverSharp.CDP\bin\Debug\net9.0\NoDriverSharp.CDP.dll" target="lib\net9.0\NoDriverSharp.CDP.dll" />
  </files>
</package>