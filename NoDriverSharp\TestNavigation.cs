using NoDriverSharp.Core;
using Microsoft.Extensions.Logging;

Console.WriteLine("Testing NoDriverSharp Navigation to fingerprint.com");
Console.WriteLine("==================================================");

// Configure logging
using var loggerFactory = LoggerFactory.Create(builder =>
    builder.AddConsole().SetMinimumLevel(LogLevel.Information));

var logger = loggerFactory.CreateLogger<Browser>();

try
{
    Console.WriteLine("Creating stealth configuration...");
    var config = NoDriver.CreateStealthConfig();
    config.Headless = false;
    config.WindowSize = (1920, 1080);
    config.NavigationTimeout = TimeSpan.FromSeconds(30);

    Console.WriteLine("Starting browser...");
    await using var browser = await NoDriver.StartAsync(config, logger);
    Console.WriteLine($"Browser started! Debug URL: {browser.DebuggerUrl}");

    // Test basic navigation first
    Console.WriteLine("Testing basic navigation...");
    var testTab = await browser.GetAsync("https://httpbin.org/status/200");
    Console.WriteLine($"✓ Basic test successful: {testTab.Url}");
    await Task.Delay(2000);

    // Test fingerprint.com
    Console.WriteLine("Testing fingerprint.com navigation...");
    var tab = await browser.GetAsync("https://fingerprint.com/products/bot-detection/", newTab: true);
    Console.WriteLine($"✓ Navigation successful!");
    Console.WriteLine($"Final URL: {tab.Url}");
    Console.WriteLine($"Page Title: {tab.Title}");

    // Wait for page to load
    Console.WriteLine("Waiting for page analysis...");
    await Task.Delay(10000);

    // Take screenshot
    await tab.SaveScreenshotAsync("fingerprint_test.png");
    Console.WriteLine("Screenshot saved: fingerprint_test.png");

    Console.WriteLine("\nTest completed successfully!");
    Console.WriteLine("Press any key to close...");
    Console.ReadKey();
}
catch (Exception ex)
{
    Console.WriteLine($"Test failed: {ex.Message}");
    Console.WriteLine($"Exception type: {ex.GetType().Name}");
    if (ex.InnerException != null)
    {
        Console.WriteLine($"Inner exception: {ex.InnerException.Message}");
    }
    Console.WriteLine($"Stack trace: {ex.StackTrace}");
}
