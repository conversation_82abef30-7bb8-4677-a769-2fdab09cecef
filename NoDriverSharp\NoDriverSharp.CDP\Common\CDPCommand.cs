using System.Text.Json;
using System.Text.Json.Serialization;

namespace NoDriverSharp.CDP.Common;

/// <summary>
/// Base class for all Chrome DevTools Protocol commands
/// </summary>
public abstract class CDPCommand
{
    /// <summary>
    /// The method name for this CDP command
    /// </summary>
    [JsonPropertyName("method")]
    public abstract string Method { get; }

    /// <summary>
    /// Parameters for this command
    /// </summary>
    [JsonPropertyName("params")]
    public virtual object? Params { get; set; }

    /// <summary>
    /// Unique identifier for this command
    /// </summary>
    [JsonPropertyName("id")]
    public int Id { get; set; }

    /// <summary>
    /// Serialize this command to JSON
    /// </summary>
    public string ToJson()
    {
        return JsonSerializer.Serialize(this, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
        });
    }
}

/// <summary>
/// Generic CDP command with typed parameters
/// </summary>
/// <typeparam name="TParams">Type of the parameters</typeparam>
public abstract class CDPCommand<TParams> : CDPCommand where TParams : class
{
    /// <summary>
    /// Typed parameters for this command
    /// </summary>
    [JsonPropertyName("params")]
    public new TParams? TypedParams { get; set; }

    /// <summary>
    /// Base parameters property (for serialization)
    /// </summary>
    [JsonIgnore]
    public override object? Params
    {
        get => TypedParams;
        set => TypedParams = (TParams?)value;
    }

    protected CDPCommand(TParams? parameters = null)
    {
        TypedParams = parameters;
    }
}
