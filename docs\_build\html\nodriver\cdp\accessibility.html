<!doctype html>
<html class="no-js" lang="en" data-content_root="../../">
  <head><meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width,initial-scale=1"/>
    <meta name="color-scheme" content="light dark"><meta name="viewport" content="width=device-width, initial-scale=1" />
<link rel="index" title="Index" href="../../genindex.html" /><link rel="search" title="Search" href="../../search.html" /><link rel="next" title="Animation" href="animation.html" /><link rel="prev" title="CDP object" href="../cdp.html" />

    <!-- Generated with Sphinx 7.2.6 and Furo 2023.09.10 -->
        <title>Accessibility - nodriver documentation</title>
      <link rel="stylesheet" type="text/css" href="../../_static/pygments.css?v=fa44fd50" />
    <link rel="stylesheet" type="text/css" href="../../_static/styles/furo.css?v=135e06be" />
    <link rel="stylesheet" type="text/css" href="../../_static/styles/furo-extensions.css?v=36a5483c" />
    <link rel="stylesheet" type="text/css" href="../../_static/./custom.css?v=3934642d" />
    
    


<style>
  body {
    --color-code-background: #eeffcc;
  --color-code-foreground: black;
  
  }
  @media not print {
    body[data-theme="dark"] {
      --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8eefba;
  --color-background-primary: #111112;
  --color-problematic: #e7aeae;
  
    }
    @media (prefers-color-scheme: dark) {
      body:not([data-theme="light"]) {
        --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8eefba;
  --color-background-primary: #111112;
  --color-problematic: #e7aeae;
  
      }
    }
  }
</style></head>
  <body>
    
    <script>
      document.body.dataset.theme = localStorage.getItem("theme") || "auto";
    </script>
    

<svg xmlns="http://www.w3.org/2000/svg" style="display: none;">
  <symbol id="svg-toc" viewBox="0 0 24 24">
    <title>Contents</title>
    <svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 1024 1024">
      <path d="M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM115.4 518.9L271.7 642c5.8 4.6 14.4.5 14.4-6.9V388.9c0-7.4-8.5-11.5-14.4-6.9L115.4 505.1a8.74 8.74 0 0 0 0 13.8z"/>
    </svg>
  </symbol>
  <symbol id="svg-menu" viewBox="0 0 24 24">
    <title>Menu</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-menu">
      <line x1="3" y1="12" x2="21" y2="12"></line>
      <line x1="3" y1="6" x2="21" y2="6"></line>
      <line x1="3" y1="18" x2="21" y2="18"></line>
    </svg>
  </symbol>
  <symbol id="svg-arrow-right" viewBox="0 0 24 24">
    <title>Expand</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-chevron-right">
      <polyline points="9 18 15 12 9 6"></polyline>
    </svg>
  </symbol>
  <symbol id="svg-sun" viewBox="0 0 24 24">
    <title>Light mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="feather-sun">
      <circle cx="12" cy="12" r="5"></circle>
      <line x1="12" y1="1" x2="12" y2="3"></line>
      <line x1="12" y1="21" x2="12" y2="23"></line>
      <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
      <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
      <line x1="1" y1="12" x2="3" y2="12"></line>
      <line x1="21" y1="12" x2="23" y2="12"></line>
      <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
      <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
    </svg>
  </symbol>
  <symbol id="svg-moon" viewBox="0 0 24 24">
    <title>Dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-moon">
      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
      <path d="M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z" />
    </svg>
  </symbol>
  <symbol id="svg-sun-half" viewBox="0 0 24 24">
    <title>Auto light/dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-shadow">
      <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
      <circle cx="12" cy="12" r="9" />
      <path d="M13 12h5" />
      <path d="M13 15h4" />
      <path d="M13 18h1" />
      <path d="M13 9h4" />
      <path d="M13 6h1" />
    </svg>
  </symbol>
</svg>

<input type="checkbox" class="sidebar-toggle" name="__navigation" id="__navigation">
<input type="checkbox" class="sidebar-toggle" name="__toc" id="__toc">
<label class="overlay sidebar-overlay" for="__navigation">
  <div class="visually-hidden">Hide navigation sidebar</div>
</label>
<label class="overlay toc-overlay" for="__toc">
  <div class="visually-hidden">Hide table of contents sidebar</div>
</label>



<div class="page">
  <header class="mobile-header">
    <div class="header-left">
      <label class="nav-overlay-icon" for="__navigation">
        <div class="visually-hidden">Toggle site navigation sidebar</div>
        <i class="icon"><svg><use href="#svg-menu"></use></svg></i>
      </label>
    </div>
    <div class="header-center">
      <a href="../../index.html"><div class="brand">nodriver  documentation</div></a>
    </div>
    <div class="header-right">
      <div class="theme-toggle-container theme-toggle-header">
        <button class="theme-toggle">
          <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
          <svg class="theme-icon-when-auto"><use href="#svg-sun-half"></use></svg>
          <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
          <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
        </button>
      </div>
      <label class="toc-overlay-icon toc-header-icon" for="__toc">
        <div class="visually-hidden">Toggle table of contents sidebar</div>
        <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
      </label>
    </div>
  </header>
  <aside class="sidebar-drawer">
    <div class="sidebar-container">
      
      <div class="sidebar-sticky"><div class="sidebar-scroll"><a class="sidebar-brand" href="../../index.html">
  
  
  <span class="sidebar-brand-text">nodriver  documentation</span>
  
</a><form class="sidebar-search-container" method="get" action="../../search.html" role="search">
  <input class="sidebar-search" placeholder="Search" name="q" aria-label="Search">
  <input type="hidden" name="check_keywords" value="yes">
  <input type="hidden" name="area" value="default">
</form>
<div id="searchbox"></div><div class="sidebar-tree">
  <ul>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html">Quickstart guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#usage-example">usage example</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#more-complete-example">More complete example</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#custom-starting-options">Custom starting options</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#alternative-custom-options">Alternative custom options</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#proxies-socks5-authenticated-too">Proxies (socks5, authenticated too)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#concrete-example">Concrete example</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../classes/browser.html">Browser class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../classes/tab.html">Tab class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../classes/element.html">Element class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../classes/others_and_helpers.html">Other classes and Helper classes</a></li>
</ul>
<ul class="current">
<li class="toctree-l1 current has-children"><a class="reference internal" href="../cdp.html">CDP object</a><input checked="" class="toctree-checkbox" id="toctree-checkbox-1" name="toctree-checkbox-1" role="switch" type="checkbox"/><label for="toctree-checkbox-1"><div class="visually-hidden">Toggle navigation of CDP object</div><i class="icon"><svg><use href="#svg-arrow-right"></use></svg></i></label><ul class="current">
<li class="toctree-l2 current current-page"><a class="current reference internal" href="#">Accessibility</a></li>
<li class="toctree-l2"><a class="reference internal" href="animation.html">Animation</a></li>
<li class="toctree-l2"><a class="reference internal" href="audits.html">Audits</a></li>
<li class="toctree-l2"><a class="reference internal" href="autofill.html">Autofill</a></li>
<li class="toctree-l2"><a class="reference internal" href="background_service.html">BackgroundService</a></li>
<li class="toctree-l2"><a class="reference internal" href="bluetooth_emulation.html">BluetoothEmulation</a></li>
<li class="toctree-l2"><a class="reference internal" href="browser.html">Browser</a></li>
<li class="toctree-l2"><a class="reference internal" href="cache_storage.html">CacheStorage</a></li>
<li class="toctree-l2"><a class="reference internal" href="cast.html">Cast</a></li>
<li class="toctree-l2"><a class="reference internal" href="console.html">Console</a></li>
<li class="toctree-l2"><a class="reference internal" href="css.html">CSS</a></li>
<li class="toctree-l2"><a class="reference internal" href="debugger.html">Debugger</a></li>
<li class="toctree-l2"><a class="reference internal" href="device_access.html">DeviceAccess</a></li>
<li class="toctree-l2"><a class="reference internal" href="device_orientation.html">DeviceOrientation</a></li>
<li class="toctree-l2"><a class="reference internal" href="dom.html">DOM</a></li>
<li class="toctree-l2"><a class="reference internal" href="dom_debugger.html">DOMDebugger</a></li>
<li class="toctree-l2"><a class="reference internal" href="dom_snapshot.html">DOMSnapshot</a></li>
<li class="toctree-l2"><a class="reference internal" href="dom_storage.html">DOMStorage</a></li>
<li class="toctree-l2"><a class="reference internal" href="emulation.html">Emulation</a></li>
<li class="toctree-l2"><a class="reference internal" href="event_breakpoints.html">EventBreakpoints</a></li>
<li class="toctree-l2"><a class="reference internal" href="extensions.html">Extensions</a></li>
<li class="toctree-l2"><a class="reference internal" href="fed_cm.html">FedCm</a></li>
<li class="toctree-l2"><a class="reference internal" href="fetch.html">Fetch</a></li>
<li class="toctree-l2"><a class="reference internal" href="file_system.html">FileSystem</a></li>
<li class="toctree-l2"><a class="reference internal" href="headless_experimental.html">HeadlessExperimental</a></li>
<li class="toctree-l2"><a class="reference internal" href="heap_profiler.html">HeapProfiler</a></li>
<li class="toctree-l2"><a class="reference internal" href="indexed_db.html">IndexedDB</a></li>
<li class="toctree-l2"><a class="reference internal" href="input_.html">Input</a></li>
<li class="toctree-l2"><a class="reference internal" href="inspector.html">Inspector</a></li>
<li class="toctree-l2"><a class="reference internal" href="io.html">IO</a></li>
<li class="toctree-l2"><a class="reference internal" href="layer_tree.html">LayerTree</a></li>
<li class="toctree-l2"><a class="reference internal" href="log.html">Log</a></li>
<li class="toctree-l2"><a class="reference internal" href="media.html">Media</a></li>
<li class="toctree-l2"><a class="reference internal" href="memory.html">Memory</a></li>
<li class="toctree-l2"><a class="reference internal" href="network.html">Network</a></li>
<li class="toctree-l2"><a class="reference internal" href="overlay.html">Overlay</a></li>
<li class="toctree-l2"><a class="reference internal" href="page.html">Page</a></li>
<li class="toctree-l2"><a class="reference internal" href="performance.html">Performance</a></li>
<li class="toctree-l2"><a class="reference internal" href="performance_timeline.html">PerformanceTimeline</a></li>
<li class="toctree-l2"><a class="reference internal" href="preload.html">Preload</a></li>
<li class="toctree-l2"><a class="reference internal" href="profiler.html">Profiler</a></li>
<li class="toctree-l2"><a class="reference internal" href="pwa.html">PWA</a></li>
<li class="toctree-l2"><a class="reference internal" href="runtime.html">Runtime</a></li>
<li class="toctree-l2"><a class="reference internal" href="schema.html">Schema</a></li>
<li class="toctree-l2"><a class="reference internal" href="security.html">Security</a></li>
<li class="toctree-l2"><a class="reference internal" href="service_worker.html">ServiceWorker</a></li>
<li class="toctree-l2"><a class="reference internal" href="storage.html">Storage</a></li>
<li class="toctree-l2"><a class="reference internal" href="system_info.html">SystemInfo</a></li>
<li class="toctree-l2"><a class="reference internal" href="target.html">Target</a></li>
<li class="toctree-l2"><a class="reference internal" href="tethering.html">Tethering</a></li>
<li class="toctree-l2"><a class="reference internal" href="tracing.html">Tracing</a></li>
<li class="toctree-l2"><a class="reference internal" href="web_audio.html">WebAudio</a></li>
<li class="toctree-l2"><a class="reference internal" href="web_authn.html">WebAuthn</a></li>
</ul>
</li>
</ul>

</div>
</div>
      </div>
      
    </div>
  </aside>
  <div class="main">
    <div class="content">
      <div class="article-container">
        <a href="#" class="back-to-top muted-link">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
            <path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8v12z"></path>
          </svg>
          <span>Back to top</span>
        </a>
        <div class="content-icon-container">
          
<div class="theme-toggle-container theme-toggle-content">
            <button class="theme-toggle">
              <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
              <svg class="theme-icon-when-auto"><use href="#svg-sun-half"></use></svg>
              <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
              <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
            </button>
          </div>
          <label class="toc-overlay-icon toc-content-icon" for="__toc">
            <div class="visually-hidden">Toggle table of contents sidebar</div>
            <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
          </label>
        </div>
        <article role="main">
          <section id="accessibility">
<h1>Accessibility<a class="headerlink" href="#accessibility" title="Link to this heading">#</a></h1>
<p><em>This CDP domain is experimental.</em></p>
<ul class="simple" id="module-nodriver.cdp.accessibility">
<li><p><a class="reference internal" href="#types">Types</a></p></li>
<li><p><a class="reference internal" href="#commands">Commands</a></p></li>
<li><p><a class="reference internal" href="#events">Events</a></p></li>
</ul>
<section id="types">
<h2>Types<a class="headerlink" href="#types" title="Link to this heading">#</a></h2>
<p>Generally, you do not need to instantiate CDP types
yourself. Instead, the API creates objects for you as return
values from commands, and then you can use those objects as
arguments to other commands.</p>
<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXNodeId">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">AXNodeId</span></span><a class="reference internal" href="../../_modules/nodriver/cdp/accessibility.html#AXNodeId"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.accessibility.AXNodeId" title="Link to this definition">#</a></dt>
<dd><p>Unique accessibility node identifier.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXValueType">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">AXValueType</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">names</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">module</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">qualname</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">boundary</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/accessibility.html#AXValueType"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.accessibility.AXValueType" title="Link to this definition">#</a></dt>
<dd><p>Enum of possible property types.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXValueType.BOOLEAN">
<span class="sig-name descname"><span class="pre">BOOLEAN</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'boolean'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXValueType.BOOLEAN" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXValueType.TRISTATE">
<span class="sig-name descname"><span class="pre">TRISTATE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'tristate'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXValueType.TRISTATE" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXValueType.BOOLEAN_OR_UNDEFINED">
<span class="sig-name descname"><span class="pre">BOOLEAN_OR_UNDEFINED</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'booleanOrUndefined'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXValueType.BOOLEAN_OR_UNDEFINED" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXValueType.IDREF">
<span class="sig-name descname"><span class="pre">IDREF</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'idref'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXValueType.IDREF" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXValueType.IDREF_LIST">
<span class="sig-name descname"><span class="pre">IDREF_LIST</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'idrefList'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXValueType.IDREF_LIST" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXValueType.INTEGER">
<span class="sig-name descname"><span class="pre">INTEGER</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'integer'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXValueType.INTEGER" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXValueType.NODE">
<span class="sig-name descname"><span class="pre">NODE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'node'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXValueType.NODE" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXValueType.NODE_LIST">
<span class="sig-name descname"><span class="pre">NODE_LIST</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'nodeList'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXValueType.NODE_LIST" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXValueType.NUMBER">
<span class="sig-name descname"><span class="pre">NUMBER</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'number'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXValueType.NUMBER" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXValueType.STRING">
<span class="sig-name descname"><span class="pre">STRING</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'string'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXValueType.STRING" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXValueType.COMPUTED_STRING">
<span class="sig-name descname"><span class="pre">COMPUTED_STRING</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'computedString'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXValueType.COMPUTED_STRING" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXValueType.TOKEN">
<span class="sig-name descname"><span class="pre">TOKEN</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'token'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXValueType.TOKEN" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXValueType.TOKEN_LIST">
<span class="sig-name descname"><span class="pre">TOKEN_LIST</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'tokenList'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXValueType.TOKEN_LIST" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXValueType.DOM_RELATION">
<span class="sig-name descname"><span class="pre">DOM_RELATION</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'domRelation'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXValueType.DOM_RELATION" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXValueType.ROLE">
<span class="sig-name descname"><span class="pre">ROLE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'role'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXValueType.ROLE" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXValueType.INTERNAL_ROLE">
<span class="sig-name descname"><span class="pre">INTERNAL_ROLE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'internalRole'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXValueType.INTERNAL_ROLE" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXValueType.VALUE_UNDEFINED">
<span class="sig-name descname"><span class="pre">VALUE_UNDEFINED</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'valueUndefined'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXValueType.VALUE_UNDEFINED" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXValueSourceType">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">AXValueSourceType</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">names</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">module</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">qualname</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">boundary</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/accessibility.html#AXValueSourceType"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.accessibility.AXValueSourceType" title="Link to this definition">#</a></dt>
<dd><p>Enum of possible property sources.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXValueSourceType.ATTRIBUTE">
<span class="sig-name descname"><span class="pre">ATTRIBUTE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'attribute'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXValueSourceType.ATTRIBUTE" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXValueSourceType.IMPLICIT">
<span class="sig-name descname"><span class="pre">IMPLICIT</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'implicit'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXValueSourceType.IMPLICIT" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXValueSourceType.STYLE">
<span class="sig-name descname"><span class="pre">STYLE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'style'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXValueSourceType.STYLE" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXValueSourceType.CONTENTS">
<span class="sig-name descname"><span class="pre">CONTENTS</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'contents'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXValueSourceType.CONTENTS" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXValueSourceType.PLACEHOLDER">
<span class="sig-name descname"><span class="pre">PLACEHOLDER</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'placeholder'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXValueSourceType.PLACEHOLDER" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXValueSourceType.RELATED_ELEMENT">
<span class="sig-name descname"><span class="pre">RELATED_ELEMENT</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'relatedElement'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXValueSourceType.RELATED_ELEMENT" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXValueNativeSourceType">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">AXValueNativeSourceType</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">names</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">module</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">qualname</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">boundary</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/accessibility.html#AXValueNativeSourceType"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.accessibility.AXValueNativeSourceType" title="Link to this definition">#</a></dt>
<dd><p>Enum of possible native property sources (as a subtype of a particular AXValueSourceType).</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXValueNativeSourceType.DESCRIPTION">
<span class="sig-name descname"><span class="pre">DESCRIPTION</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'description'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXValueNativeSourceType.DESCRIPTION" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXValueNativeSourceType.FIGCAPTION">
<span class="sig-name descname"><span class="pre">FIGCAPTION</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'figcaption'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXValueNativeSourceType.FIGCAPTION" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXValueNativeSourceType.LABEL">
<span class="sig-name descname"><span class="pre">LABEL</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'label'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXValueNativeSourceType.LABEL" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXValueNativeSourceType.LABELFOR">
<span class="sig-name descname"><span class="pre">LABELFOR</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'labelfor'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXValueNativeSourceType.LABELFOR" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXValueNativeSourceType.LABELWRAPPED">
<span class="sig-name descname"><span class="pre">LABELWRAPPED</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'labelwrapped'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXValueNativeSourceType.LABELWRAPPED" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXValueNativeSourceType.LEGEND">
<span class="sig-name descname"><span class="pre">LEGEND</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'legend'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXValueNativeSourceType.LEGEND" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXValueNativeSourceType.RUBYANNOTATION">
<span class="sig-name descname"><span class="pre">RUBYANNOTATION</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'rubyannotation'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXValueNativeSourceType.RUBYANNOTATION" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXValueNativeSourceType.TABLECAPTION">
<span class="sig-name descname"><span class="pre">TABLECAPTION</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'tablecaption'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXValueNativeSourceType.TABLECAPTION" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXValueNativeSourceType.TITLE">
<span class="sig-name descname"><span class="pre">TITLE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'title'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXValueNativeSourceType.TITLE" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXValueNativeSourceType.OTHER">
<span class="sig-name descname"><span class="pre">OTHER</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'other'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXValueNativeSourceType.OTHER" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXValueSource">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">AXValueSource</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">type_</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">attribute</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">attribute_value</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">superseded</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">native_source</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">native_source_value</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">invalid</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">invalid_reason</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/accessibility.html#AXValueSource"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.accessibility.AXValueSource" title="Link to this definition">#</a></dt>
<dd><p>A single source for a computed AX property.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXValueSource.type_">
<span class="sig-name descname"><span class="pre">type_</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.accessibility.AXValueSourceType" title="nodriver.cdp.accessibility.AXValueSourceType"><code class="xref py py-class docutils literal notranslate"><span class="pre">AXValueSourceType</span></code></a></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXValueSource.type_" title="Link to this definition">#</a></dt>
<dd><p>What type of source this is.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXValueSource.value">
<span class="sig-name descname"><span class="pre">value</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.accessibility.AXValue" title="nodriver.cdp.accessibility.AXValue"><code class="xref py py-class docutils literal notranslate"><span class="pre">AXValue</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXValueSource.value" title="Link to this definition">#</a></dt>
<dd><p>The value of this property source.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXValueSource.attribute">
<span class="sig-name descname"><span class="pre">attribute</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXValueSource.attribute" title="Link to this definition">#</a></dt>
<dd><p>The name of the relevant attribute, if any.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXValueSource.attribute_value">
<span class="sig-name descname"><span class="pre">attribute_value</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.accessibility.AXValue" title="nodriver.cdp.accessibility.AXValue"><code class="xref py py-class docutils literal notranslate"><span class="pre">AXValue</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXValueSource.attribute_value" title="Link to this definition">#</a></dt>
<dd><p>The value of the relevant attribute, if any.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXValueSource.superseded">
<span class="sig-name descname"><span class="pre">superseded</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXValueSource.superseded" title="Link to this definition">#</a></dt>
<dd><p>Whether this source is superseded by a higher priority source.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXValueSource.native_source">
<span class="sig-name descname"><span class="pre">native_source</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.accessibility.AXValueNativeSourceType" title="nodriver.cdp.accessibility.AXValueNativeSourceType"><code class="xref py py-class docutils literal notranslate"><span class="pre">AXValueNativeSourceType</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXValueSource.native_source" title="Link to this definition">#</a></dt>
<dd><p>The native markup source for this value, e.g. a <code class="docutils literal notranslate"><span class="pre">&lt;label&gt;</span></code> element.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXValueSource.native_source_value">
<span class="sig-name descname"><span class="pre">native_source_value</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.accessibility.AXValue" title="nodriver.cdp.accessibility.AXValue"><code class="xref py py-class docutils literal notranslate"><span class="pre">AXValue</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXValueSource.native_source_value" title="Link to this definition">#</a></dt>
<dd><p>The value, such as a node or node list, of the native source.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXValueSource.invalid">
<span class="sig-name descname"><span class="pre">invalid</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXValueSource.invalid" title="Link to this definition">#</a></dt>
<dd><p>Whether the value for this property is invalid.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXValueSource.invalid_reason">
<span class="sig-name descname"><span class="pre">invalid_reason</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXValueSource.invalid_reason" title="Link to this definition">#</a></dt>
<dd><p>Reason for the value being invalid, if it is.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXRelatedNode">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">AXRelatedNode</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">backend_dom_node_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">idref</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">text</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/accessibility.html#AXRelatedNode"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.accessibility.AXRelatedNode" title="Link to this definition">#</a></dt>
<dd><dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXRelatedNode.backend_dom_node_id">
<span class="sig-name descname"><span class="pre">backend_dom_node_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="dom.html#nodriver.cdp.dom.BackendNodeId" title="nodriver.cdp.dom.BackendNodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">BackendNodeId</span></code></a></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXRelatedNode.backend_dom_node_id" title="Link to this definition">#</a></dt>
<dd><p>The BackendNodeId of the related DOM node.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXRelatedNode.idref">
<span class="sig-name descname"><span class="pre">idref</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXRelatedNode.idref" title="Link to this definition">#</a></dt>
<dd><p>The IDRef value provided, if any.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXRelatedNode.text">
<span class="sig-name descname"><span class="pre">text</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXRelatedNode.text" title="Link to this definition">#</a></dt>
<dd><p>The text alternative of this node in the current context.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXProperty">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">AXProperty</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/accessibility.html#AXProperty"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.accessibility.AXProperty" title="Link to this definition">#</a></dt>
<dd><dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXProperty.name">
<span class="sig-name descname"><span class="pre">name</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.accessibility.AXPropertyName" title="nodriver.cdp.accessibility.AXPropertyName"><code class="xref py py-class docutils literal notranslate"><span class="pre">AXPropertyName</span></code></a></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXProperty.name" title="Link to this definition">#</a></dt>
<dd><p>The name of this property.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXProperty.value">
<span class="sig-name descname"><span class="pre">value</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.accessibility.AXValue" title="nodriver.cdp.accessibility.AXValue"><code class="xref py py-class docutils literal notranslate"><span class="pre">AXValue</span></code></a></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXProperty.value" title="Link to this definition">#</a></dt>
<dd><p>The value of this property.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXValue">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">AXValue</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">type_</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">related_nodes</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">sources</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/accessibility.html#AXValue"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.accessibility.AXValue" title="Link to this definition">#</a></dt>
<dd><p>A single computed AX property.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXValue.type_">
<span class="sig-name descname"><span class="pre">type_</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.accessibility.AXValueType" title="nodriver.cdp.accessibility.AXValueType"><code class="xref py py-class docutils literal notranslate"><span class="pre">AXValueType</span></code></a></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXValue.type_" title="Link to this definition">#</a></dt>
<dd><p>The type of this value.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXValue.value">
<span class="sig-name descname"><span class="pre">value</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXValue.value" title="Link to this definition">#</a></dt>
<dd><p>The computed value of this property.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXValue.related_nodes">
<span class="sig-name descname"><span class="pre">related_nodes</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.accessibility.AXRelatedNode" title="nodriver.cdp.accessibility.AXRelatedNode"><code class="xref py py-class docutils literal notranslate"><span class="pre">AXRelatedNode</span></code></a><span class="pre">]]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXValue.related_nodes" title="Link to this definition">#</a></dt>
<dd><p>One or more related nodes, if applicable.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXValue.sources">
<span class="sig-name descname"><span class="pre">sources</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.accessibility.AXValueSource" title="nodriver.cdp.accessibility.AXValueSource"><code class="xref py py-class docutils literal notranslate"><span class="pre">AXValueSource</span></code></a><span class="pre">]]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXValue.sources" title="Link to this definition">#</a></dt>
<dd><p>The sources which contributed to the computation of this property.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXPropertyName">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">AXPropertyName</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">names</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">module</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">qualname</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">boundary</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/accessibility.html#AXPropertyName"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.accessibility.AXPropertyName" title="Link to this definition">#</a></dt>
<dd><p>Values of AXProperty name:
- from ‘busy’ to ‘roledescription’: states which apply to every AX node
- from ‘live’ to ‘root’: attributes which apply to nodes in live regions
- from ‘autocomplete’ to ‘valuetext’: attributes which apply to widgets
- from ‘checked’ to ‘selected’: states which apply to widgets
- from ‘activedescendant’ to ‘owns’ - relationships between elements other than parent/child/sibling.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXPropertyName.ACTIONS">
<span class="sig-name descname"><span class="pre">ACTIONS</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'actions'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXPropertyName.ACTIONS" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXPropertyName.BUSY">
<span class="sig-name descname"><span class="pre">BUSY</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'busy'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXPropertyName.BUSY" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXPropertyName.DISABLED">
<span class="sig-name descname"><span class="pre">DISABLED</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'disabled'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXPropertyName.DISABLED" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXPropertyName.EDITABLE">
<span class="sig-name descname"><span class="pre">EDITABLE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'editable'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXPropertyName.EDITABLE" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXPropertyName.FOCUSABLE">
<span class="sig-name descname"><span class="pre">FOCUSABLE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'focusable'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXPropertyName.FOCUSABLE" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXPropertyName.FOCUSED">
<span class="sig-name descname"><span class="pre">FOCUSED</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'focused'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXPropertyName.FOCUSED" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXPropertyName.HIDDEN">
<span class="sig-name descname"><span class="pre">HIDDEN</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'hidden'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXPropertyName.HIDDEN" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXPropertyName.HIDDEN_ROOT">
<span class="sig-name descname"><span class="pre">HIDDEN_ROOT</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'hiddenRoot'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXPropertyName.HIDDEN_ROOT" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXPropertyName.INVALID">
<span class="sig-name descname"><span class="pre">INVALID</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'invalid'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXPropertyName.INVALID" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXPropertyName.KEYSHORTCUTS">
<span class="sig-name descname"><span class="pre">KEYSHORTCUTS</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'keyshortcuts'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXPropertyName.KEYSHORTCUTS" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXPropertyName.SETTABLE">
<span class="sig-name descname"><span class="pre">SETTABLE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'settable'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXPropertyName.SETTABLE" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXPropertyName.ROLEDESCRIPTION">
<span class="sig-name descname"><span class="pre">ROLEDESCRIPTION</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'roledescription'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXPropertyName.ROLEDESCRIPTION" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXPropertyName.LIVE">
<span class="sig-name descname"><span class="pre">LIVE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'live'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXPropertyName.LIVE" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXPropertyName.ATOMIC">
<span class="sig-name descname"><span class="pre">ATOMIC</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'atomic'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXPropertyName.ATOMIC" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXPropertyName.RELEVANT">
<span class="sig-name descname"><span class="pre">RELEVANT</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'relevant'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXPropertyName.RELEVANT" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXPropertyName.ROOT">
<span class="sig-name descname"><span class="pre">ROOT</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'root'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXPropertyName.ROOT" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXPropertyName.AUTOCOMPLETE">
<span class="sig-name descname"><span class="pre">AUTOCOMPLETE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'autocomplete'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXPropertyName.AUTOCOMPLETE" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXPropertyName.HAS_POPUP">
<span class="sig-name descname"><span class="pre">HAS_POPUP</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'hasPopup'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXPropertyName.HAS_POPUP" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXPropertyName.LEVEL">
<span class="sig-name descname"><span class="pre">LEVEL</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'level'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXPropertyName.LEVEL" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXPropertyName.MULTISELECTABLE">
<span class="sig-name descname"><span class="pre">MULTISELECTABLE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'multiselectable'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXPropertyName.MULTISELECTABLE" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXPropertyName.ORIENTATION">
<span class="sig-name descname"><span class="pre">ORIENTATION</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'orientation'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXPropertyName.ORIENTATION" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXPropertyName.MULTILINE">
<span class="sig-name descname"><span class="pre">MULTILINE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'multiline'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXPropertyName.MULTILINE" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXPropertyName.READONLY">
<span class="sig-name descname"><span class="pre">READONLY</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'readonly'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXPropertyName.READONLY" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXPropertyName.REQUIRED">
<span class="sig-name descname"><span class="pre">REQUIRED</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'required'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXPropertyName.REQUIRED" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXPropertyName.VALUEMIN">
<span class="sig-name descname"><span class="pre">VALUEMIN</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'valuemin'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXPropertyName.VALUEMIN" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXPropertyName.VALUEMAX">
<span class="sig-name descname"><span class="pre">VALUEMAX</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'valuemax'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXPropertyName.VALUEMAX" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXPropertyName.VALUETEXT">
<span class="sig-name descname"><span class="pre">VALUETEXT</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'valuetext'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXPropertyName.VALUETEXT" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXPropertyName.CHECKED">
<span class="sig-name descname"><span class="pre">CHECKED</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'checked'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXPropertyName.CHECKED" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXPropertyName.EXPANDED">
<span class="sig-name descname"><span class="pre">EXPANDED</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'expanded'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXPropertyName.EXPANDED" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXPropertyName.MODAL">
<span class="sig-name descname"><span class="pre">MODAL</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'modal'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXPropertyName.MODAL" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXPropertyName.PRESSED">
<span class="sig-name descname"><span class="pre">PRESSED</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'pressed'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXPropertyName.PRESSED" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXPropertyName.SELECTED">
<span class="sig-name descname"><span class="pre">SELECTED</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'selected'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXPropertyName.SELECTED" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXPropertyName.ACTIVEDESCENDANT">
<span class="sig-name descname"><span class="pre">ACTIVEDESCENDANT</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'activedescendant'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXPropertyName.ACTIVEDESCENDANT" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXPropertyName.CONTROLS">
<span class="sig-name descname"><span class="pre">CONTROLS</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'controls'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXPropertyName.CONTROLS" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXPropertyName.DESCRIBEDBY">
<span class="sig-name descname"><span class="pre">DESCRIBEDBY</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'describedby'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXPropertyName.DESCRIBEDBY" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXPropertyName.DETAILS">
<span class="sig-name descname"><span class="pre">DETAILS</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'details'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXPropertyName.DETAILS" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXPropertyName.ERRORMESSAGE">
<span class="sig-name descname"><span class="pre">ERRORMESSAGE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'errormessage'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXPropertyName.ERRORMESSAGE" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXPropertyName.FLOWTO">
<span class="sig-name descname"><span class="pre">FLOWTO</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'flowto'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXPropertyName.FLOWTO" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXPropertyName.LABELLEDBY">
<span class="sig-name descname"><span class="pre">LABELLEDBY</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'labelledby'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXPropertyName.LABELLEDBY" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXPropertyName.OWNS">
<span class="sig-name descname"><span class="pre">OWNS</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'owns'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXPropertyName.OWNS" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXPropertyName.URL">
<span class="sig-name descname"><span class="pre">URL</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'url'</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXPropertyName.URL" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXNode">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">AXNode</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">node_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">ignored</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">ignored_reasons</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">role</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">chrome_role</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">name</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">description</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">properties</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">parent_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">child_ids</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">backend_dom_node_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">frame_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/accessibility.html#AXNode"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.accessibility.AXNode" title="Link to this definition">#</a></dt>
<dd><p>A node in the accessibility tree.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXNode.node_id">
<span class="sig-name descname"><span class="pre">node_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.accessibility.AXNodeId" title="nodriver.cdp.accessibility.AXNodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">AXNodeId</span></code></a></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXNode.node_id" title="Link to this definition">#</a></dt>
<dd><p>Unique identifier for this node.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXNode.ignored">
<span class="sig-name descname"><span class="pre">ignored</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXNode.ignored" title="Link to this definition">#</a></dt>
<dd><p>Whether this node is ignored for accessibility</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXNode.ignored_reasons">
<span class="sig-name descname"><span class="pre">ignored_reasons</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.accessibility.AXProperty" title="nodriver.cdp.accessibility.AXProperty"><code class="xref py py-class docutils literal notranslate"><span class="pre">AXProperty</span></code></a><span class="pre">]]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXNode.ignored_reasons" title="Link to this definition">#</a></dt>
<dd><p>Collection of reasons why this node is hidden.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXNode.role">
<span class="sig-name descname"><span class="pre">role</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.accessibility.AXValue" title="nodriver.cdp.accessibility.AXValue"><code class="xref py py-class docutils literal notranslate"><span class="pre">AXValue</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXNode.role" title="Link to this definition">#</a></dt>
<dd><p>This <code class="docutils literal notranslate"><span class="pre">Node</span></code>’s role, whether explicit or implicit.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXNode.chrome_role">
<span class="sig-name descname"><span class="pre">chrome_role</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.accessibility.AXValue" title="nodriver.cdp.accessibility.AXValue"><code class="xref py py-class docutils literal notranslate"><span class="pre">AXValue</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXNode.chrome_role" title="Link to this definition">#</a></dt>
<dd><p>This <code class="docutils literal notranslate"><span class="pre">Node</span></code>’s Chrome raw role.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXNode.name">
<span class="sig-name descname"><span class="pre">name</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.accessibility.AXValue" title="nodriver.cdp.accessibility.AXValue"><code class="xref py py-class docutils literal notranslate"><span class="pre">AXValue</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXNode.name" title="Link to this definition">#</a></dt>
<dd><p>The accessible name for this <code class="docutils literal notranslate"><span class="pre">Node</span></code>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXNode.description">
<span class="sig-name descname"><span class="pre">description</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.accessibility.AXValue" title="nodriver.cdp.accessibility.AXValue"><code class="xref py py-class docutils literal notranslate"><span class="pre">AXValue</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXNode.description" title="Link to this definition">#</a></dt>
<dd><p>The accessible description for this <code class="docutils literal notranslate"><span class="pre">Node</span></code>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXNode.value">
<span class="sig-name descname"><span class="pre">value</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.accessibility.AXValue" title="nodriver.cdp.accessibility.AXValue"><code class="xref py py-class docutils literal notranslate"><span class="pre">AXValue</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXNode.value" title="Link to this definition">#</a></dt>
<dd><p>The value for this <code class="docutils literal notranslate"><span class="pre">Node</span></code>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXNode.properties">
<span class="sig-name descname"><span class="pre">properties</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.accessibility.AXProperty" title="nodriver.cdp.accessibility.AXProperty"><code class="xref py py-class docutils literal notranslate"><span class="pre">AXProperty</span></code></a><span class="pre">]]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXNode.properties" title="Link to this definition">#</a></dt>
<dd><p>All other properties</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXNode.parent_id">
<span class="sig-name descname"><span class="pre">parent_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.accessibility.AXNodeId" title="nodriver.cdp.accessibility.AXNodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">AXNodeId</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXNode.parent_id" title="Link to this definition">#</a></dt>
<dd><p>ID for this node’s parent.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXNode.child_ids">
<span class="sig-name descname"><span class="pre">child_ids</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.accessibility.AXNodeId" title="nodriver.cdp.accessibility.AXNodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">AXNodeId</span></code></a><span class="pre">]]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXNode.child_ids" title="Link to this definition">#</a></dt>
<dd><p>IDs for each of this node’s child nodes.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXNode.backend_dom_node_id">
<span class="sig-name descname"><span class="pre">backend_dom_node_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="dom.html#nodriver.cdp.dom.BackendNodeId" title="nodriver.cdp.dom.BackendNodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">BackendNodeId</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXNode.backend_dom_node_id" title="Link to this definition">#</a></dt>
<dd><p>The backend ID for the associated DOM node, if any.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.AXNode.frame_id">
<span class="sig-name descname"><span class="pre">frame_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="page.html#nodriver.cdp.page.FrameId" title="nodriver.cdp.page.FrameId"><code class="xref py py-class docutils literal notranslate"><span class="pre">FrameId</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.AXNode.frame_id" title="Link to this definition">#</a></dt>
<dd><p>The frame ID for the frame associated with this nodes document.</p>
</dd></dl>

</dd></dl>

</section>
<section id="commands">
<h2>Commands<a class="headerlink" href="#commands" title="Link to this heading">#</a></h2>
<p>Each command is a generator function. The return
type <code class="docutils literal notranslate"><span class="pre">Generator[x,</span> <span class="pre">y,</span> <span class="pre">z]</span></code> indicates that the generator
<em>yields</em> arguments of type <code class="docutils literal notranslate"><span class="pre">x</span></code>, it must be resumed with
an argument of type <code class="docutils literal notranslate"><span class="pre">y</span></code>, and it returns type <code class="docutils literal notranslate"><span class="pre">z</span></code>. In
this library, types <code class="docutils literal notranslate"><span class="pre">x</span></code> and <code class="docutils literal notranslate"><span class="pre">y</span></code> are the same for all
commands, and <code class="docutils literal notranslate"><span class="pre">z</span></code> is the return type you should pay attention
to. For more information, see
<a class="reference internal" href="../../readme.html#getting-started-commands"><span class="std std-ref">Getting Started: Commands</span></a>.</p>
<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.disable">
<span class="sig-name descname"><span class="pre">disable</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/accessibility.html#disable"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.accessibility.disable" title="Link to this definition">#</a></dt>
<dd><p>Disables the accessibility domain.</p>
<dl class="field-list simple">
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.enable">
<span class="sig-name descname"><span class="pre">enable</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/accessibility.html#enable"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.accessibility.enable" title="Link to this definition">#</a></dt>
<dd><p>Enables the accessibility domain which causes <code class="docutils literal notranslate"><span class="pre">AXNodeId</span></code>’s to remain consistent between method calls.
This turns on accessibility for the page, which can impact performance until accessibility is disabled.</p>
<dl class="field-list simple">
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.get_ax_node_and_ancestors">
<span class="sig-name descname"><span class="pre">get_ax_node_and_ancestors</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">node_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">backend_node_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">object_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/accessibility.html#get_ax_node_and_ancestors"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.accessibility.get_ax_node_and_ancestors" title="Link to this definition">#</a></dt>
<dd><p>Fetches a node and all ancestors up to and including the root.
Requires <code class="docutils literal notranslate"><span class="pre">enable()</span></code> to have been called previously.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="dom.html#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a>]</span>) – <em>(Optional)</em> Identifier of the node to get.</p></li>
<li><p><strong>backend_node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="dom.html#nodriver.cdp.dom.BackendNodeId" title="nodriver.cdp.dom.BackendNodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">BackendNodeId</span></code></a>]</span>) – <em>(Optional)</em> Identifier of the backend node to get.</p></li>
<li><p><strong>object_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="runtime.html#nodriver.cdp.runtime.RemoteObjectId" title="nodriver.cdp.runtime.RemoteObjectId"><code class="xref py py-class docutils literal notranslate"><span class="pre">RemoteObjectId</span></code></a>]</span>) – <em>(Optional)</em> JavaScript object id of the node wrapper to get.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.accessibility.AXNode" title="nodriver.cdp.accessibility.AXNode"><code class="xref py py-class docutils literal notranslate"><span class="pre">AXNode</span></code></a>]]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.get_child_ax_nodes">
<span class="sig-name descname"><span class="pre">get_child_ax_nodes</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">id_</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">frame_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/accessibility.html#get_child_ax_nodes"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.accessibility.get_child_ax_nodes" title="Link to this definition">#</a></dt>
<dd><p>Fetches a particular accessibility node by AXNodeId.
Requires <code class="docutils literal notranslate"><span class="pre">enable()</span></code> to have been called previously.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>id</strong> – </p></li>
<li><p><strong>frame_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="page.html#nodriver.cdp.page.FrameId" title="nodriver.cdp.page.FrameId"><code class="xref py py-class docutils literal notranslate"><span class="pre">FrameId</span></code></a>]</span>) – <em>(Optional)</em> The frame in whose document the node resides. If omitted, the root frame is used.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.accessibility.AXNode" title="nodriver.cdp.accessibility.AXNode"><code class="xref py py-class docutils literal notranslate"><span class="pre">AXNode</span></code></a>]]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.get_full_ax_tree">
<span class="sig-name descname"><span class="pre">get_full_ax_tree</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">depth</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">frame_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/accessibility.html#get_full_ax_tree"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.accessibility.get_full_ax_tree" title="Link to this definition">#</a></dt>
<dd><p>Fetches the entire accessibility tree for the root Document</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>depth</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a>]</span>) – <em>(Optional)</em> The maximum depth at which descendants of the root node should be retrieved. If omitted, the full tree is returned.</p></li>
<li><p><strong>frame_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="page.html#nodriver.cdp.page.FrameId" title="nodriver.cdp.page.FrameId"><code class="xref py py-class docutils literal notranslate"><span class="pre">FrameId</span></code></a>]</span>) – <em>(Optional)</em> The frame for whose document the AX tree should be retrieved. If omitted, the root frame is used.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.accessibility.AXNode" title="nodriver.cdp.accessibility.AXNode"><code class="xref py py-class docutils literal notranslate"><span class="pre">AXNode</span></code></a>]]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.get_partial_ax_tree">
<span class="sig-name descname"><span class="pre">get_partial_ax_tree</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">node_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">backend_node_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">object_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">fetch_relatives</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/accessibility.html#get_partial_ax_tree"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.accessibility.get_partial_ax_tree" title="Link to this definition">#</a></dt>
<dd><p>Fetches the accessibility node and partial accessibility tree for this DOM node, if it exists.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="dom.html#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a>]</span>) – <em>(Optional)</em> Identifier of the node to get the partial accessibility tree for.</p></li>
<li><p><strong>backend_node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="dom.html#nodriver.cdp.dom.BackendNodeId" title="nodriver.cdp.dom.BackendNodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">BackendNodeId</span></code></a>]</span>) – <em>(Optional)</em> Identifier of the backend node to get the partial accessibility tree for.</p></li>
<li><p><strong>object_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="runtime.html#nodriver.cdp.runtime.RemoteObjectId" title="nodriver.cdp.runtime.RemoteObjectId"><code class="xref py py-class docutils literal notranslate"><span class="pre">RemoteObjectId</span></code></a>]</span>) – <em>(Optional)</em> JavaScript object id of the node wrapper to get the partial accessibility tree for.</p></li>
<li><p><strong>fetch_relatives</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <em>(Optional)</em> Whether to fetch this node’s ancestors, siblings and children. Defaults to true.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.accessibility.AXNode" title="nodriver.cdp.accessibility.AXNode"><code class="xref py py-class docutils literal notranslate"><span class="pre">AXNode</span></code></a>]]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>The <code class="docutils literal notranslate"><span class="pre">Accessibility.AXNode</span></code> for this DOM node, if it exists, plus its ancestors, siblings and children, if requested.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.get_root_ax_node">
<span class="sig-name descname"><span class="pre">get_root_ax_node</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">frame_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/accessibility.html#get_root_ax_node"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.accessibility.get_root_ax_node" title="Link to this definition">#</a></dt>
<dd><p>Fetches the root node.
Requires <code class="docutils literal notranslate"><span class="pre">enable()</span></code> to have been called previously.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>frame_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="page.html#nodriver.cdp.page.FrameId" title="nodriver.cdp.page.FrameId"><code class="xref py py-class docutils literal notranslate"><span class="pre">FrameId</span></code></a>]</span>) – <em>(Optional)</em> The frame in whose document the node resides. If omitted, the root frame is used.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference internal" href="#nodriver.cdp.accessibility.AXNode" title="nodriver.cdp.accessibility.AXNode"><code class="xref py py-class docutils literal notranslate"><span class="pre">AXNode</span></code></a>]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.query_ax_tree">
<span class="sig-name descname"><span class="pre">query_ax_tree</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">node_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">backend_node_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">object_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">accessible_name</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">role</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/accessibility.html#query_ax_tree"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.accessibility.query_ax_tree" title="Link to this definition">#</a></dt>
<dd><p>Query a DOM node’s accessibility subtree for accessible name and role.
This command computes the name and role for all nodes in the subtree, including those that are
ignored for accessibility, and returns those that match the specified name and role. If no DOM
node is specified, or the DOM node does not exist, the command returns an error. If neither
<code class="docutils literal notranslate"><span class="pre">accessibleName</span></code> or <code class="docutils literal notranslate"><span class="pre">role</span></code> is specified, it returns all the accessibility nodes in the subtree.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="dom.html#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a>]</span>) – <em>(Optional)</em> Identifier of the node for the root to query.</p></li>
<li><p><strong>backend_node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="dom.html#nodriver.cdp.dom.BackendNodeId" title="nodriver.cdp.dom.BackendNodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">BackendNodeId</span></code></a>]</span>) – <em>(Optional)</em> Identifier of the backend node for the root to query.</p></li>
<li><p><strong>object_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="runtime.html#nodriver.cdp.runtime.RemoteObjectId" title="nodriver.cdp.runtime.RemoteObjectId"><code class="xref py py-class docutils literal notranslate"><span class="pre">RemoteObjectId</span></code></a>]</span>) – <em>(Optional)</em> JavaScript object id of the node wrapper for the root to query.</p></li>
<li><p><strong>accessible_name</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]</span>) – <em>(Optional)</em> Find nodes with this computed name.</p></li>
<li><p><strong>role</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]</span>) – <em>(Optional)</em> Find nodes with this computed role.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.accessibility.AXNode" title="nodriver.cdp.accessibility.AXNode"><code class="xref py py-class docutils literal notranslate"><span class="pre">AXNode</span></code></a>]]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>A list of <code class="docutils literal notranslate"><span class="pre">Accessibility.AXNode</span></code> matching the specified attributes, including nodes that are ignored for accessibility.</p>
</dd>
</dl>
</dd></dl>

</section>
<section id="events">
<h2>Events<a class="headerlink" href="#events" title="Link to this heading">#</a></h2>
<p>Generally, you do not need to instantiate CDP events
yourself. Instead, the API creates events for you and then
you use the event’s attributes.</p>
<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.LoadComplete">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">LoadComplete</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">root</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/accessibility.html#LoadComplete"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.accessibility.LoadComplete" title="Link to this definition">#</a></dt>
<dd><p><strong>EXPERIMENTAL</strong></p>
<p>The loadComplete event mirrors the load complete event sent by the browser to assistive
technology when the web page has finished loading.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.LoadComplete.root">
<span class="sig-name descname"><span class="pre">root</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.accessibility.AXNode" title="nodriver.cdp.accessibility.AXNode"><code class="xref py py-class docutils literal notranslate"><span class="pre">AXNode</span></code></a></em><a class="headerlink" href="#nodriver.cdp.accessibility.LoadComplete.root" title="Link to this definition">#</a></dt>
<dd><p>New document root node.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.NodesUpdated">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">NodesUpdated</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">nodes</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/accessibility.html#NodesUpdated"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.accessibility.NodesUpdated" title="Link to this definition">#</a></dt>
<dd><p><strong>EXPERIMENTAL</strong></p>
<p>The nodesUpdated event is sent every time a previously requested node has changed the in tree.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.accessibility.NodesUpdated.nodes">
<span class="sig-name descname"><span class="pre">nodes</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.accessibility.AXNode" title="nodriver.cdp.accessibility.AXNode"><code class="xref py py-class docutils literal notranslate"><span class="pre">AXNode</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.accessibility.NodesUpdated.nodes" title="Link to this definition">#</a></dt>
<dd><p>Updated node data.</p>
</dd></dl>

</dd></dl>

</section>
</section>

        </article>
      </div>
      <footer>
        
        <div class="related-pages">
          <a class="next-page" href="animation.html">
              <div class="page-info">
                <div class="context">
                  <span>Next</span>
                </div>
                <div class="title">Animation</div>
              </div>
              <svg class="furo-related-icon"><use href="#svg-arrow-right"></use></svg>
            </a>
          <a class="prev-page" href="../cdp.html">
              <svg class="furo-related-icon"><use href="#svg-arrow-right"></use></svg>
              <div class="page-info">
                <div class="context">
                  <span>Previous</span>
                </div>
                
                <div class="title">CDP object</div>
                
              </div>
            </a>
        </div>
        <div class="bottom-of-page">
          <div class="left-details">
            <div class="copyright">
                Copyright &#169; 2023, Author
            </div>
            Made with <a href="https://www.sphinx-doc.org/">Sphinx</a> and <a class="muted-link" href="https://pradyunsg.me">@pradyunsg</a>'s
            
            <a href="https://github.com/pradyunsg/furo">Furo</a>
            
          </div>
          <div class="right-details">
            
          </div>
        </div>
        
      </footer>
    </div>
    <aside class="toc-drawer">
      
      
      <div class="toc-sticky toc-scroll">
        <div class="toc-title-container">
          <span class="toc-title">
            On this page
          </span>
        </div>
        <div class="toc-tree-container">
          <div class="toc-tree">
            <ul>
<li><a class="reference internal" href="#">Accessibility</a><ul>
<li><a class="reference internal" href="#types">Types</a><ul>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXNodeId"><code class="docutils literal notranslate"><span class="pre">AXNodeId</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXValueType"><code class="docutils literal notranslate"><span class="pre">AXValueType</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXValueType.BOOLEAN"><code class="docutils literal notranslate"><span class="pre">AXValueType.BOOLEAN</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXValueType.TRISTATE"><code class="docutils literal notranslate"><span class="pre">AXValueType.TRISTATE</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXValueType.BOOLEAN_OR_UNDEFINED"><code class="docutils literal notranslate"><span class="pre">AXValueType.BOOLEAN_OR_UNDEFINED</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXValueType.IDREF"><code class="docutils literal notranslate"><span class="pre">AXValueType.IDREF</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXValueType.IDREF_LIST"><code class="docutils literal notranslate"><span class="pre">AXValueType.IDREF_LIST</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXValueType.INTEGER"><code class="docutils literal notranslate"><span class="pre">AXValueType.INTEGER</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXValueType.NODE"><code class="docutils literal notranslate"><span class="pre">AXValueType.NODE</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXValueType.NODE_LIST"><code class="docutils literal notranslate"><span class="pre">AXValueType.NODE_LIST</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXValueType.NUMBER"><code class="docutils literal notranslate"><span class="pre">AXValueType.NUMBER</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXValueType.STRING"><code class="docutils literal notranslate"><span class="pre">AXValueType.STRING</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXValueType.COMPUTED_STRING"><code class="docutils literal notranslate"><span class="pre">AXValueType.COMPUTED_STRING</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXValueType.TOKEN"><code class="docutils literal notranslate"><span class="pre">AXValueType.TOKEN</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXValueType.TOKEN_LIST"><code class="docutils literal notranslate"><span class="pre">AXValueType.TOKEN_LIST</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXValueType.DOM_RELATION"><code class="docutils literal notranslate"><span class="pre">AXValueType.DOM_RELATION</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXValueType.ROLE"><code class="docutils literal notranslate"><span class="pre">AXValueType.ROLE</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXValueType.INTERNAL_ROLE"><code class="docutils literal notranslate"><span class="pre">AXValueType.INTERNAL_ROLE</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXValueType.VALUE_UNDEFINED"><code class="docutils literal notranslate"><span class="pre">AXValueType.VALUE_UNDEFINED</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXValueSourceType"><code class="docutils literal notranslate"><span class="pre">AXValueSourceType</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXValueSourceType.ATTRIBUTE"><code class="docutils literal notranslate"><span class="pre">AXValueSourceType.ATTRIBUTE</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXValueSourceType.IMPLICIT"><code class="docutils literal notranslate"><span class="pre">AXValueSourceType.IMPLICIT</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXValueSourceType.STYLE"><code class="docutils literal notranslate"><span class="pre">AXValueSourceType.STYLE</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXValueSourceType.CONTENTS"><code class="docutils literal notranslate"><span class="pre">AXValueSourceType.CONTENTS</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXValueSourceType.PLACEHOLDER"><code class="docutils literal notranslate"><span class="pre">AXValueSourceType.PLACEHOLDER</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXValueSourceType.RELATED_ELEMENT"><code class="docutils literal notranslate"><span class="pre">AXValueSourceType.RELATED_ELEMENT</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXValueNativeSourceType"><code class="docutils literal notranslate"><span class="pre">AXValueNativeSourceType</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXValueNativeSourceType.DESCRIPTION"><code class="docutils literal notranslate"><span class="pre">AXValueNativeSourceType.DESCRIPTION</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXValueNativeSourceType.FIGCAPTION"><code class="docutils literal notranslate"><span class="pre">AXValueNativeSourceType.FIGCAPTION</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXValueNativeSourceType.LABEL"><code class="docutils literal notranslate"><span class="pre">AXValueNativeSourceType.LABEL</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXValueNativeSourceType.LABELFOR"><code class="docutils literal notranslate"><span class="pre">AXValueNativeSourceType.LABELFOR</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXValueNativeSourceType.LABELWRAPPED"><code class="docutils literal notranslate"><span class="pre">AXValueNativeSourceType.LABELWRAPPED</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXValueNativeSourceType.LEGEND"><code class="docutils literal notranslate"><span class="pre">AXValueNativeSourceType.LEGEND</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXValueNativeSourceType.RUBYANNOTATION"><code class="docutils literal notranslate"><span class="pre">AXValueNativeSourceType.RUBYANNOTATION</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXValueNativeSourceType.TABLECAPTION"><code class="docutils literal notranslate"><span class="pre">AXValueNativeSourceType.TABLECAPTION</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXValueNativeSourceType.TITLE"><code class="docutils literal notranslate"><span class="pre">AXValueNativeSourceType.TITLE</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXValueNativeSourceType.OTHER"><code class="docutils literal notranslate"><span class="pre">AXValueNativeSourceType.OTHER</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXValueSource"><code class="docutils literal notranslate"><span class="pre">AXValueSource</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXValueSource.type_"><code class="docutils literal notranslate"><span class="pre">AXValueSource.type_</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXValueSource.value"><code class="docutils literal notranslate"><span class="pre">AXValueSource.value</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXValueSource.attribute"><code class="docutils literal notranslate"><span class="pre">AXValueSource.attribute</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXValueSource.attribute_value"><code class="docutils literal notranslate"><span class="pre">AXValueSource.attribute_value</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXValueSource.superseded"><code class="docutils literal notranslate"><span class="pre">AXValueSource.superseded</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXValueSource.native_source"><code class="docutils literal notranslate"><span class="pre">AXValueSource.native_source</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXValueSource.native_source_value"><code class="docutils literal notranslate"><span class="pre">AXValueSource.native_source_value</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXValueSource.invalid"><code class="docutils literal notranslate"><span class="pre">AXValueSource.invalid</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXValueSource.invalid_reason"><code class="docutils literal notranslate"><span class="pre">AXValueSource.invalid_reason</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXRelatedNode"><code class="docutils literal notranslate"><span class="pre">AXRelatedNode</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXRelatedNode.backend_dom_node_id"><code class="docutils literal notranslate"><span class="pre">AXRelatedNode.backend_dom_node_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXRelatedNode.idref"><code class="docutils literal notranslate"><span class="pre">AXRelatedNode.idref</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXRelatedNode.text"><code class="docutils literal notranslate"><span class="pre">AXRelatedNode.text</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXProperty"><code class="docutils literal notranslate"><span class="pre">AXProperty</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXProperty.name"><code class="docutils literal notranslate"><span class="pre">AXProperty.name</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXProperty.value"><code class="docutils literal notranslate"><span class="pre">AXProperty.value</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXValue"><code class="docutils literal notranslate"><span class="pre">AXValue</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXValue.type_"><code class="docutils literal notranslate"><span class="pre">AXValue.type_</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXValue.value"><code class="docutils literal notranslate"><span class="pre">AXValue.value</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXValue.related_nodes"><code class="docutils literal notranslate"><span class="pre">AXValue.related_nodes</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXValue.sources"><code class="docutils literal notranslate"><span class="pre">AXValue.sources</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXPropertyName"><code class="docutils literal notranslate"><span class="pre">AXPropertyName</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXPropertyName.ACTIONS"><code class="docutils literal notranslate"><span class="pre">AXPropertyName.ACTIONS</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXPropertyName.BUSY"><code class="docutils literal notranslate"><span class="pre">AXPropertyName.BUSY</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXPropertyName.DISABLED"><code class="docutils literal notranslate"><span class="pre">AXPropertyName.DISABLED</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXPropertyName.EDITABLE"><code class="docutils literal notranslate"><span class="pre">AXPropertyName.EDITABLE</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXPropertyName.FOCUSABLE"><code class="docutils literal notranslate"><span class="pre">AXPropertyName.FOCUSABLE</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXPropertyName.FOCUSED"><code class="docutils literal notranslate"><span class="pre">AXPropertyName.FOCUSED</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXPropertyName.HIDDEN"><code class="docutils literal notranslate"><span class="pre">AXPropertyName.HIDDEN</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXPropertyName.HIDDEN_ROOT"><code class="docutils literal notranslate"><span class="pre">AXPropertyName.HIDDEN_ROOT</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXPropertyName.INVALID"><code class="docutils literal notranslate"><span class="pre">AXPropertyName.INVALID</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXPropertyName.KEYSHORTCUTS"><code class="docutils literal notranslate"><span class="pre">AXPropertyName.KEYSHORTCUTS</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXPropertyName.SETTABLE"><code class="docutils literal notranslate"><span class="pre">AXPropertyName.SETTABLE</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXPropertyName.ROLEDESCRIPTION"><code class="docutils literal notranslate"><span class="pre">AXPropertyName.ROLEDESCRIPTION</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXPropertyName.LIVE"><code class="docutils literal notranslate"><span class="pre">AXPropertyName.LIVE</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXPropertyName.ATOMIC"><code class="docutils literal notranslate"><span class="pre">AXPropertyName.ATOMIC</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXPropertyName.RELEVANT"><code class="docutils literal notranslate"><span class="pre">AXPropertyName.RELEVANT</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXPropertyName.ROOT"><code class="docutils literal notranslate"><span class="pre">AXPropertyName.ROOT</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXPropertyName.AUTOCOMPLETE"><code class="docutils literal notranslate"><span class="pre">AXPropertyName.AUTOCOMPLETE</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXPropertyName.HAS_POPUP"><code class="docutils literal notranslate"><span class="pre">AXPropertyName.HAS_POPUP</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXPropertyName.LEVEL"><code class="docutils literal notranslate"><span class="pre">AXPropertyName.LEVEL</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXPropertyName.MULTISELECTABLE"><code class="docutils literal notranslate"><span class="pre">AXPropertyName.MULTISELECTABLE</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXPropertyName.ORIENTATION"><code class="docutils literal notranslate"><span class="pre">AXPropertyName.ORIENTATION</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXPropertyName.MULTILINE"><code class="docutils literal notranslate"><span class="pre">AXPropertyName.MULTILINE</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXPropertyName.READONLY"><code class="docutils literal notranslate"><span class="pre">AXPropertyName.READONLY</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXPropertyName.REQUIRED"><code class="docutils literal notranslate"><span class="pre">AXPropertyName.REQUIRED</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXPropertyName.VALUEMIN"><code class="docutils literal notranslate"><span class="pre">AXPropertyName.VALUEMIN</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXPropertyName.VALUEMAX"><code class="docutils literal notranslate"><span class="pre">AXPropertyName.VALUEMAX</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXPropertyName.VALUETEXT"><code class="docutils literal notranslate"><span class="pre">AXPropertyName.VALUETEXT</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXPropertyName.CHECKED"><code class="docutils literal notranslate"><span class="pre">AXPropertyName.CHECKED</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXPropertyName.EXPANDED"><code class="docutils literal notranslate"><span class="pre">AXPropertyName.EXPANDED</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXPropertyName.MODAL"><code class="docutils literal notranslate"><span class="pre">AXPropertyName.MODAL</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXPropertyName.PRESSED"><code class="docutils literal notranslate"><span class="pre">AXPropertyName.PRESSED</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXPropertyName.SELECTED"><code class="docutils literal notranslate"><span class="pre">AXPropertyName.SELECTED</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXPropertyName.ACTIVEDESCENDANT"><code class="docutils literal notranslate"><span class="pre">AXPropertyName.ACTIVEDESCENDANT</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXPropertyName.CONTROLS"><code class="docutils literal notranslate"><span class="pre">AXPropertyName.CONTROLS</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXPropertyName.DESCRIBEDBY"><code class="docutils literal notranslate"><span class="pre">AXPropertyName.DESCRIBEDBY</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXPropertyName.DETAILS"><code class="docutils literal notranslate"><span class="pre">AXPropertyName.DETAILS</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXPropertyName.ERRORMESSAGE"><code class="docutils literal notranslate"><span class="pre">AXPropertyName.ERRORMESSAGE</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXPropertyName.FLOWTO"><code class="docutils literal notranslate"><span class="pre">AXPropertyName.FLOWTO</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXPropertyName.LABELLEDBY"><code class="docutils literal notranslate"><span class="pre">AXPropertyName.LABELLEDBY</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXPropertyName.OWNS"><code class="docutils literal notranslate"><span class="pre">AXPropertyName.OWNS</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXPropertyName.URL"><code class="docutils literal notranslate"><span class="pre">AXPropertyName.URL</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXNode"><code class="docutils literal notranslate"><span class="pre">AXNode</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXNode.node_id"><code class="docutils literal notranslate"><span class="pre">AXNode.node_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXNode.ignored"><code class="docutils literal notranslate"><span class="pre">AXNode.ignored</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXNode.ignored_reasons"><code class="docutils literal notranslate"><span class="pre">AXNode.ignored_reasons</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXNode.role"><code class="docutils literal notranslate"><span class="pre">AXNode.role</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXNode.chrome_role"><code class="docutils literal notranslate"><span class="pre">AXNode.chrome_role</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXNode.name"><code class="docutils literal notranslate"><span class="pre">AXNode.name</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXNode.description"><code class="docutils literal notranslate"><span class="pre">AXNode.description</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXNode.value"><code class="docutils literal notranslate"><span class="pre">AXNode.value</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXNode.properties"><code class="docutils literal notranslate"><span class="pre">AXNode.properties</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXNode.parent_id"><code class="docutils literal notranslate"><span class="pre">AXNode.parent_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXNode.child_ids"><code class="docutils literal notranslate"><span class="pre">AXNode.child_ids</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXNode.backend_dom_node_id"><code class="docutils literal notranslate"><span class="pre">AXNode.backend_dom_node_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.AXNode.frame_id"><code class="docutils literal notranslate"><span class="pre">AXNode.frame_id</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#commands">Commands</a><ul>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.disable"><code class="docutils literal notranslate"><span class="pre">disable()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.enable"><code class="docutils literal notranslate"><span class="pre">enable()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.get_ax_node_and_ancestors"><code class="docutils literal notranslate"><span class="pre">get_ax_node_and_ancestors()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.get_child_ax_nodes"><code class="docutils literal notranslate"><span class="pre">get_child_ax_nodes()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.get_full_ax_tree"><code class="docutils literal notranslate"><span class="pre">get_full_ax_tree()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.get_partial_ax_tree"><code class="docutils literal notranslate"><span class="pre">get_partial_ax_tree()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.get_root_ax_node"><code class="docutils literal notranslate"><span class="pre">get_root_ax_node()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.query_ax_tree"><code class="docutils literal notranslate"><span class="pre">query_ax_tree()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#events">Events</a><ul>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.LoadComplete"><code class="docutils literal notranslate"><span class="pre">LoadComplete</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.LoadComplete.root"><code class="docutils literal notranslate"><span class="pre">LoadComplete.root</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.NodesUpdated"><code class="docutils literal notranslate"><span class="pre">NodesUpdated</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.accessibility.NodesUpdated.nodes"><code class="docutils literal notranslate"><span class="pre">NodesUpdated.nodes</span></code></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>

          </div>
        </div>
      </div>
      
      
    </aside>
  </div>
</div><script src="../../_static/documentation_options.js?v=5929fcd5"></script>
    <script src="../../_static/doctools.js?v=888ff710"></script>
    <script src="../../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../../_static/scripts/furo.js?v=32e29ea5"></script>
    </body>
</html>