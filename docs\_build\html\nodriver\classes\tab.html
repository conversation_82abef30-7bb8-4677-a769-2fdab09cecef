<!doctype html>
<html class="no-js" lang="en" data-content_root="../../">
  <head><meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width,initial-scale=1"/>
    <meta name="color-scheme" content="light dark"><meta name="viewport" content="width=device-width, initial-scale=1" />
<link rel="index" title="Index" href="../../genindex.html" /><link rel="search" title="Search" href="../../search.html" /><link rel="next" title="Element class" href="element.html" /><link rel="prev" title="Browser class" href="browser.html" />

    <!-- Generated with Sphinx 7.2.6 and Furo 2023.09.10 -->
        <title>Tab class - nodriver documentation</title>
      <link rel="stylesheet" type="text/css" href="../../_static/pygments.css?v=fa44fd50" />
    <link rel="stylesheet" type="text/css" href="../../_static/styles/furo.css?v=135e06be" />
    <link rel="stylesheet" type="text/css" href="../../_static/styles/furo-extensions.css?v=36a5483c" />
    <link rel="stylesheet" type="text/css" href="../../_static/./custom.css?v=3934642d" />
    
    


<style>
  body {
    --color-code-background: #eeffcc;
  --color-code-foreground: black;
  
  }
  @media not print {
    body[data-theme="dark"] {
      --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8eefba;
  --color-background-primary: #111112;
  --color-problematic: #e7aeae;
  
    }
    @media (prefers-color-scheme: dark) {
      body:not([data-theme="light"]) {
        --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8eefba;
  --color-background-primary: #111112;
  --color-problematic: #e7aeae;
  
      }
    }
  }
</style></head>
  <body>
    
    <script>
      document.body.dataset.theme = localStorage.getItem("theme") || "auto";
    </script>
    

<svg xmlns="http://www.w3.org/2000/svg" style="display: none;">
  <symbol id="svg-toc" viewBox="0 0 24 24">
    <title>Contents</title>
    <svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 1024 1024">
      <path d="M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM115.4 518.9L271.7 642c5.8 4.6 14.4.5 14.4-6.9V388.9c0-7.4-8.5-11.5-14.4-6.9L115.4 505.1a8.74 8.74 0 0 0 0 13.8z"/>
    </svg>
  </symbol>
  <symbol id="svg-menu" viewBox="0 0 24 24">
    <title>Menu</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-menu">
      <line x1="3" y1="12" x2="21" y2="12"></line>
      <line x1="3" y1="6" x2="21" y2="6"></line>
      <line x1="3" y1="18" x2="21" y2="18"></line>
    </svg>
  </symbol>
  <symbol id="svg-arrow-right" viewBox="0 0 24 24">
    <title>Expand</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-chevron-right">
      <polyline points="9 18 15 12 9 6"></polyline>
    </svg>
  </symbol>
  <symbol id="svg-sun" viewBox="0 0 24 24">
    <title>Light mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="feather-sun">
      <circle cx="12" cy="12" r="5"></circle>
      <line x1="12" y1="1" x2="12" y2="3"></line>
      <line x1="12" y1="21" x2="12" y2="23"></line>
      <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
      <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
      <line x1="1" y1="12" x2="3" y2="12"></line>
      <line x1="21" y1="12" x2="23" y2="12"></line>
      <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
      <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
    </svg>
  </symbol>
  <symbol id="svg-moon" viewBox="0 0 24 24">
    <title>Dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-moon">
      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
      <path d="M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z" />
    </svg>
  </symbol>
  <symbol id="svg-sun-half" viewBox="0 0 24 24">
    <title>Auto light/dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-shadow">
      <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
      <circle cx="12" cy="12" r="9" />
      <path d="M13 12h5" />
      <path d="M13 15h4" />
      <path d="M13 18h1" />
      <path d="M13 9h4" />
      <path d="M13 6h1" />
    </svg>
  </symbol>
</svg>

<input type="checkbox" class="sidebar-toggle" name="__navigation" id="__navigation">
<input type="checkbox" class="sidebar-toggle" name="__toc" id="__toc">
<label class="overlay sidebar-overlay" for="__navigation">
  <div class="visually-hidden">Hide navigation sidebar</div>
</label>
<label class="overlay toc-overlay" for="__toc">
  <div class="visually-hidden">Hide table of contents sidebar</div>
</label>



<div class="page">
  <header class="mobile-header">
    <div class="header-left">
      <label class="nav-overlay-icon" for="__navigation">
        <div class="visually-hidden">Toggle site navigation sidebar</div>
        <i class="icon"><svg><use href="#svg-menu"></use></svg></i>
      </label>
    </div>
    <div class="header-center">
      <a href="../../index.html"><div class="brand">nodriver  documentation</div></a>
    </div>
    <div class="header-right">
      <div class="theme-toggle-container theme-toggle-header">
        <button class="theme-toggle">
          <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
          <svg class="theme-icon-when-auto"><use href="#svg-sun-half"></use></svg>
          <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
          <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
        </button>
      </div>
      <label class="toc-overlay-icon toc-header-icon" for="__toc">
        <div class="visually-hidden">Toggle table of contents sidebar</div>
        <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
      </label>
    </div>
  </header>
  <aside class="sidebar-drawer">
    <div class="sidebar-container">
      
      <div class="sidebar-sticky"><div class="sidebar-scroll"><a class="sidebar-brand" href="../../index.html">
  
  
  <span class="sidebar-brand-text">nodriver  documentation</span>
  
</a><form class="sidebar-search-container" method="get" action="../../search.html" role="search">
  <input class="sidebar-search" placeholder="Search" name="q" aria-label="Search">
  <input type="hidden" name="check_keywords" value="yes">
  <input type="hidden" name="area" value="default">
</form>
<div id="searchbox"></div><div class="sidebar-tree">
  <ul>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html">Quickstart guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#usage-example">usage example</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#more-complete-example">More complete example</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#custom-starting-options">Custom starting options</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#alternative-custom-options">Alternative custom options</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#proxies-socks5-authenticated-too">Proxies (socks5, authenticated too)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#concrete-example">Concrete example</a></li>
</ul>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="browser.html">Browser class</a></li>
<li class="toctree-l1 current current-page"><a class="current reference internal" href="#">Tab class</a></li>
<li class="toctree-l1"><a class="reference internal" href="element.html">Element class</a></li>
<li class="toctree-l1"><a class="reference internal" href="others_and_helpers.html">Other classes and Helper classes</a></li>
</ul>
<ul>
<li class="toctree-l1 has-children"><a class="reference internal" href="../cdp.html">CDP object</a><input class="toctree-checkbox" id="toctree-checkbox-1" name="toctree-checkbox-1" role="switch" type="checkbox"/><label for="toctree-checkbox-1"><div class="visually-hidden">Toggle navigation of CDP object</div><i class="icon"><svg><use href="#svg-arrow-right"></use></svg></i></label><ul>
<li class="toctree-l2"><a class="reference internal" href="../cdp/accessibility.html">Accessibility</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/animation.html">Animation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/audits.html">Audits</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/autofill.html">Autofill</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/background_service.html">BackgroundService</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/bluetooth_emulation.html">BluetoothEmulation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/browser.html">Browser</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/cache_storage.html">CacheStorage</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/cast.html">Cast</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/console.html">Console</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/css.html">CSS</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/debugger.html">Debugger</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/device_access.html">DeviceAccess</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/device_orientation.html">DeviceOrientation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/dom.html">DOM</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/dom_debugger.html">DOMDebugger</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/dom_snapshot.html">DOMSnapshot</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/dom_storage.html">DOMStorage</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/emulation.html">Emulation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/event_breakpoints.html">EventBreakpoints</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/extensions.html">Extensions</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/fed_cm.html">FedCm</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/fetch.html">Fetch</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/file_system.html">FileSystem</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/headless_experimental.html">HeadlessExperimental</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/heap_profiler.html">HeapProfiler</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/indexed_db.html">IndexedDB</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/input_.html">Input</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/inspector.html">Inspector</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/io.html">IO</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/layer_tree.html">LayerTree</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/log.html">Log</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/media.html">Media</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/memory.html">Memory</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/network.html">Network</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/overlay.html">Overlay</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/page.html">Page</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/performance.html">Performance</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/performance_timeline.html">PerformanceTimeline</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/preload.html">Preload</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/profiler.html">Profiler</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/pwa.html">PWA</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/runtime.html">Runtime</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/schema.html">Schema</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/security.html">Security</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/service_worker.html">ServiceWorker</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/storage.html">Storage</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/system_info.html">SystemInfo</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/target.html">Target</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/tethering.html">Tethering</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/tracing.html">Tracing</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/web_audio.html">WebAudio</a></li>
<li class="toctree-l2"><a class="reference internal" href="../cdp/web_authn.html">WebAuthn</a></li>
</ul>
</li>
</ul>

</div>
</div>
      </div>
      
    </div>
  </aside>
  <div class="main">
    <div class="content">
      <div class="article-container">
        <a href="#" class="back-to-top muted-link">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
            <path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8v12z"></path>
          </svg>
          <span>Back to top</span>
        </a>
        <div class="content-icon-container">
          
<div class="theme-toggle-container theme-toggle-content">
            <button class="theme-toggle">
              <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
              <svg class="theme-icon-when-auto"><use href="#svg-sun-half"></use></svg>
              <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
              <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
            </button>
          </div>
          <label class="toc-overlay-icon toc-content-icon" for="__toc">
            <div class="visually-hidden">Toggle table of contents sidebar</div>
            <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
          </label>
        </div>
        <article role="main">
          <section id="tab-class">
<span id="tab"></span><h1>Tab class<a class="headerlink" href="#tab-class" title="Link to this heading">#</a></h1>
<dl class="py class">
<dt class="sig sig-object py" id="nodriver.Tab">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">Tab</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">websocket_url</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">target</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">browser</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwargs</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/tab.html#Tab"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Tab" title="Link to this definition">#</a></dt>
<dd><p><a class="reference internal" href="#tab"><span class="std std-ref">Tab class</span></a> is the controlling mechanism/connection to a ‘target’,
for most of us ‘target’ can be read as ‘tab’. however it could also
be an iframe, serviceworker or background script for example,
although there isn’t much to control for those.</p>
<p>if you open a new window by using <code class="xref py py-meth docutils literal notranslate"><span class="pre">browser.get(...,</span> <span class="pre">new_window=True)()</span></code>
your url will open a new window. this window is a ‘tab’.
When you browse to another page, the tab will be the same (it is an browser view).</p>
<p>So it’s important to keep some reference to tab objects, in case you’re
done interacting with elements and want to operate on the page level again.</p>
<section id="custom-cdp-commands">
<h2>Custom CDP commands<a class="headerlink" href="#custom-cdp-commands" title="Link to this heading">#</a></h2>
<p>Tab object provide many useful and often-used methods. It is also
possible to utilize the included cdp classes to to something totally custom.</p>
<p>the cdp package is a set of so-called “domains” with each having methods, events and types.
to send a cdp method, for example <a class="reference internal" href="../cdp/page.html#nodriver.cdp.page.navigate" title="nodriver.cdp.page.navigate"><code class="xref py py-obj docutils literal notranslate"><span class="pre">cdp.page.navigate</span></code></a>, you’ll have to check
whether the method accepts any parameters and whether they are required or not.</p>
<p>you can use</p>
<p><code class="docutils literal notranslate"><span class="pre">`python</span>
<span class="pre">await</span> <span class="pre">tab.send(cdp.page.navigate(url='https://yoururlhere'))</span>
<span class="pre">`</span></code></p>
<p>so tab.send() accepts a generator object, which is created by calling a cdp method.
this way you can build very detailed and customized commands.
(note: finding correct command combo’s can be a time consuming task, luckily i added a whole bunch
of useful methods, preferably having the same api’s or lookalikes, as in selenium)</p>
<section id="some-useful-often-needed-and-simply-required-methods">
<h3>some useful, often needed and simply required methods<a class="headerlink" href="#some-useful-often-needed-and-simply-required-methods" title="Link to this heading">#</a></h3>
</section>
</section>
<section id="find-find-text">
<h2><a class="reference internal" href="#nodriver.Tab.find" title="nodriver.Tab.find"><code class="xref py py-meth docutils literal notranslate"><span class="pre">find()</span></code></a>  |  find(text)<a class="headerlink" href="#find-find-text" title="Link to this heading">#</a></h2>
<p>find and returns a single element by text match. by default returns the first element found.
much more powerful is the best_match flag, although also much more expensive.
when no match is found, it will retry for &lt;timeout&gt; seconds (default: 10), so
this is also suitable to use as wait condition.</p>
</section>
<section id="find-find-text-best-match-true-or-find-text-true">
<h2><a class="reference internal" href="#nodriver.Tab.find" title="nodriver.Tab.find"><code class="xref py py-meth docutils literal notranslate"><span class="pre">find()</span></code></a> |  find(text, best_match=True) or find(text, True)<a class="headerlink" href="#find-find-text-best-match-true-or-find-text-true" title="Link to this heading">#</a></h2>
<p>Much more powerful (and expensive!!) than the above, is the use of the <cite>find(text, best_match=True)</cite> flag.
It will still return 1 element, but when multiple matches are found, picks the one having the
most similar text length.
How would that help?
For example, you search for “login”, you’d probably want the “login” button element,
and not thousands of scripts,meta,headings which happens to contain a string of “login”.</p>
<p>when no match is found, it will retry for &lt;timeout&gt; seconds (default: 10), so
this is also suitable to use as wait condition.</p>
</section>
<section id="select-select-selector">
<h2><a class="reference internal" href="#nodriver.Tab.select" title="nodriver.Tab.select"><code class="xref py py-meth docutils literal notranslate"><span class="pre">select()</span></code></a> | select(selector)<a class="headerlink" href="#select-select-selector" title="Link to this heading">#</a></h2>
<p>find and returns a single element by css selector match.
when no match is found, it will retry for &lt;timeout&gt; seconds (default: 10), so
this is also suitable to use as wait condition.</p>
</section>
<section id="select-all-select-all-selector">
<h2><a class="reference internal" href="#nodriver.Tab.select_all" title="nodriver.Tab.select_all"><code class="xref py py-meth docutils literal notranslate"><span class="pre">select_all()</span></code></a> | select_all(selector)<a class="headerlink" href="#select-all-select-all-selector" title="Link to this heading">#</a></h2>
<p>find and returns all elements by css selector match.
when no match is found, it will retry for &lt;timeout&gt; seconds (default: 10), so
this is also suitable to use as wait condition.</p>
</section>
<section id="await-tab">
<h2>await <a class="reference internal" href="#nodriver.Tab" title="nodriver.Tab"><code class="xref py py-obj docutils literal notranslate"><span class="pre">Tab</span></code></a><a class="headerlink" href="#await-tab" title="Link to this heading">#</a></h2>
<p>calling <cite>await tab</cite> will do a lot of stuff under the hood, and ensures all references
are up to date. also it allows for the script to “breathe”, as it is oftentime faster than your browser or
webpage. So whenever you get stuck and things crashes or element could not be found, you should probably let
it “breathe”  by calling <cite>await page</cite>  and/or <cite>await page.sleep()</cite></p>
<p>also, it’s ensuring <code class="xref py py-obj docutils literal notranslate"><span class="pre">url</span></code> will be updated to the most recent one, which is quite important in some
other methods.</p>
<p>attempts to find the location of given template image in the current viewport
the only real use case for this is bot-detection systems.
you can find for example the location of a ‘verify’-checkbox,
which are hidden from dom using shadow-root’s or workers.</p>
</section>
<section id="await-tab-template-location-and-await-tab-verify-cf">
<h2>await <a class="reference internal" href="#nodriver.Tab.template_location" title="nodriver.Tab.template_location"><code class="xref py py-obj docutils literal notranslate"><span class="pre">Tab.template_location</span></code></a> (and await <a class="reference internal" href="#nodriver.Tab.verify_cf" title="nodriver.Tab.verify_cf"><code class="xref py py-obj docutils literal notranslate"><span class="pre">Tab.verify_cf</span></code></a>)<a class="headerlink" href="#await-tab-template-location-and-await-tab-verify-cf" title="Link to this heading">#</a></h2>
<p>attempts to find the location of given template image in the current viewport.
the only real use case for this is bot-detection systems.
you can find, for example the location of a ‘verify’-checkbox, which are hidden from dom
using shadow-root’s or/or workers and cannot be controlled by normal methods.</p>
<p>template_image can be custom (for example your language, included is english only),
but you need to create the template image yourself, which is just a cropped
image of the area, see example image, where the target is exactly in the center.
template_image can be custom (for example your language), but you need to
create the template image yourself, where the target is exactly in the center.</p>
</section>
<section id="example-111x71">
<h2>example (111x71)<a class="headerlink" href="#example-111x71" title="Link to this heading">#</a></h2>
<p>this includes the white space on the left, to make the box center</p>
<a class="reference internal image-reference" href="../../_images/template_example.png"><img alt="example template image" src="../../_images/template_example.png" style="width: 111px;" /></a>
<section id="using-other-and-custom-cdp-commands">
<h3>Using other and custom CDP commands<a class="headerlink" href="#using-other-and-custom-cdp-commands" title="Link to this heading">#</a></h3>
<p>using the included cdp module, you can easily craft commands, which will always return an generator object.
this generator object can be easily sent to the <a class="reference internal" href="#nodriver.Tab.send" title="nodriver.Tab.send"><code class="xref py py-meth docutils literal notranslate"><span class="pre">send()</span></code></a>  method.</p>
</section>
</section>
<section id="send">
<h2><a class="reference internal" href="#nodriver.Tab.send" title="nodriver.Tab.send"><code class="xref py py-meth docutils literal notranslate"><span class="pre">send()</span></code></a><a class="headerlink" href="#send" title="Link to this heading">#</a></h2>
<p>this is probably THE most important method, although you won’t ever call it, unless you want to
go really custom. the send method accepts a <code class="xref py py-obj docutils literal notranslate"><span class="pre">cdp</span></code> command. Each of which can be found in the
cdp section.</p>
<p>when you import * from this package, cdp will be in your namespace, and contains all domains/actions/events
you can act upon.</p>
<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Tab.activate">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">activate</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/tab.html#Tab.activate"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Tab.activate" title="Link to this definition">#</a></dt>
<dd><p>active this target (ie: tab,window,page)</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Tab.add_handler">
<span class="sig-name descname"><span class="pre">add_handler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">event_type_or_domain</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">handler</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#nodriver.Tab.add_handler" title="Link to this definition">#</a></dt>
<dd><p>add a handler for given event</p>
<p>if event_type_or_domain is a module instead of a type, it will find all available events and add
the handler.</p>
<p>if you want to receive event updates (network traffic are also ‘events’) you can add handlers for those events.
handlers can be regular callback functions or async coroutine functions (and also just lamba’s).
for example, you want to check the network traffic:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="n">page</span><span class="o">.</span><span class="n">add_handler</span><span class="p">(</span><span class="n">cdp</span><span class="o">.</span><span class="n">network</span><span class="o">.</span><span class="n">RequestWillBeSent</span><span class="p">,</span> <span class="k">lambda</span> <span class="n">event</span><span class="p">:</span> <span class="nb">print</span><span class="p">(</span><span class="s1">&#39;network event =&gt; </span><span class="si">%s</span><span class="s1">&#39;</span> <span class="o">%</span> <span class="n">event</span><span class="o">.</span><span class="n">request</span><span class="p">))</span>
</pre></div>
</div>
<p>the next time you make network traffic you will see your console print like crazy.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>event_type_or_domain</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Union" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Union</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#type" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">type</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/types.html#types.ModuleType" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">ModuleType</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#type" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">type</span></code></a>]]</span>) – </p></li>
<li><p><strong>handler</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Union" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Union</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Callable" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Callable</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Awaitable" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Awaitable</span></code></a>]</span>) – </p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p></p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p></p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.Tab.attached">
<span class="sig-name descname"><span class="pre">attached</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.Tab.attached" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Tab.back">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">back</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/tab.html#Tab.back"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Tab.back" title="Link to this definition">#</a></dt>
<dd><p>history back</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Tab.bring_to_front">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">bring_to_front</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/tab.html#Tab.bring_to_front"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Tab.bring_to_front" title="Link to this definition">#</a></dt>
<dd><p>alias to self.activate</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="nodriver.Tab.browser">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">browser</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="browser.html#nodriver.Browser" title="nodriver.core.browser.Browser"><span class="pre">Browser</span></a></em><a class="headerlink" href="#nodriver.Tab.browser" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Tab.bypass_insecure_connection_warning">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">bypass_insecure_connection_warning</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/tab.html#Tab.bypass_insecure_connection_warning"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Tab.bypass_insecure_connection_warning" title="Link to this definition">#</a></dt>
<dd><p>when you enter a site where the certificate is invalid
you get a warning. call this function to “proceed”
:return:
:rtype:</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Tab.close">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">close</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/tab.html#Tab.close"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Tab.close" title="Link to this definition">#</a></dt>
<dd><p>close the current target (ie: tab,window,page)
:return:
:rtype:</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="nodriver.Tab.closed">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">closed</span></span><a class="headerlink" href="#nodriver.Tab.closed" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Tab.connect">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">connect</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kw</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#nodriver.Tab.connect" title="Link to this definition">#</a></dt>
<dd><p>opens the websocket connection. should not be called manually by users
:type kw: 
:param kw:
:return:</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Tab.disconnect">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">disconnect</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#nodriver.Tab.disconnect" title="Link to this definition">#</a></dt>
<dd><p>closes the websocket connection. should not be called manually by users.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Tab.download_file">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">download_file</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">url</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">filename</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/tab.html#Tab.download_file"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Tab.download_file" title="Link to this definition">#</a></dt>
<dd><p>downloads file by given url.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>url</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – url of the file</p></li>
<li><p><strong>filename</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.TypeVar" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">TypeVar</span></code></a>(<code class="docutils literal notranslate"><span class="pre">PathLike</span></code>, bound= <a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a> | <a class="reference external" href="https://docs.python.org/3/library/pathlib.html#pathlib.Path" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Path</span></code></a>)]</span>) – the name for the file. if not specified the name is composed from the url file name</p></li>
</ul>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Tab.evaluate">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">evaluate</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">expression</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">await_promise</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">return_by_value</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/tab.html#Tab.evaluate"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Tab.evaluate" title="Link to this definition">#</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Union" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Union</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Tuple" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Tuple</span></code></a>[<a class="reference internal" href="../cdp/runtime.html#nodriver.cdp.runtime.RemoteObject" title="nodriver.cdp.runtime.RemoteObject"><code class="xref py py-class docutils literal notranslate"><span class="pre">RemoteObject</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="../cdp/runtime.html#nodriver.cdp.runtime.ExceptionDetails" title="nodriver.cdp.runtime.ExceptionDetails"><code class="xref py py-class docutils literal notranslate"><span class="pre">ExceptionDetails</span></code></a>]]]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Tab.feed_cdp">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">feed_cdp</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cmd</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/tab.html#Tab.feed_cdp"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Tab.feed_cdp" title="Link to this definition">#</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><span class="sphinx_autodoc_typehints-type"><code class="xref py py-class docutils literal notranslate"><span class="pre">Future</span></code></span></p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Tab.find">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">find</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">text</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">best_match</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">return_enclosing_element</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">timeout</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">10</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/tab.html#Tab.find"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Tab.find" title="Link to this definition">#</a></dt>
<dd><p>find single element by text
can also be used to wait for such element to appear.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>text</strong> (<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><em>str</em></a>) – text to search for. note: script contents are also considered text</p></li>
<li><p><strong>best_match</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a></span>) – <dl class="field-list simple">
<dt class="field-odd">param best_match<span class="colon">:</span></dt>
<dd class="field-odd"><p>when True (default), it will return the element which has the most
comparable string length. this could help tremendously, when for example
you search for “login”, you’d probably want the login button element,
and not thousands of scripts,meta,headings containing a string of “login”.
When False, it will return naively just the first match (but is way faster).</p>
</dd>
<dt class="field-even">type best_match<span class="colon">:</span></dt>
<dd class="field-even"><p>bool</p>
</dd>
</dl>
</p></li>
<li><p><strong>timeout</strong> (<a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><em>float</em></a><em>,</em><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><em>int</em></a>) – raise timeout exception when after this many seconds nothing is found.</p></li>
</ul>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Tab.find_all">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">find_all</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">text</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">timeout</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">10</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/tab.html#Tab.find_all"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Tab.find_all" title="Link to this definition">#</a></dt>
<dd><p>find multiple elements by text
can also be used to wait for such element to appear.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>text</strong> (<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><em>str</em></a>) – text to search for. note: script contents are also considered text</p></li>
<li><p><strong>timeout</strong> (<a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><em>float</em></a><em>,</em><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><em>int</em></a>) – raise timeout exception when after this many seconds nothing is found.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="element.html#nodriver.Element" title="nodriver.core.element.Element"><code class="xref py py-class docutils literal notranslate"><span class="pre">Element</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Tab.find_element_by_text">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">find_element_by_text</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">text</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">best_match</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">return_enclosing_element</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/tab.html#Tab.find_element_by_text"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Tab.find_element_by_text" title="Link to this definition">#</a></dt>
<dd><p>finds and returns the first element containing &lt;text&gt;, or best match</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>text</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – </p></li>
<li><p><strong>best_match</strong> (<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><em>bool</em></a>) – when True, which is MUCH more expensive (thus much slower),
will find the closest match based on length.
this could help tremendously, when for example you search for “login”, you’d probably want the login button element,
and not thousands of scripts,meta,headings containing a string of “login”.</p></li>
<li><p><strong>return_enclosing_element</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – </p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p></p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p></p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Tab.find_elements_by_text">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">find_elements_by_text</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">text</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">tag_hint</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/tab.html#Tab.find_elements_by_text"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Tab.find_elements_by_text" title="Link to this definition">#</a></dt>
<dd><p>returns element which match the given text.
returns element which match the given text.
please note: this may (or will) also return any other element (like inline scripts),
which happen to contain that text.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>text</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – </p></li>
<li><p><strong>tag_hint</strong> (<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><em>str</em></a>) – when provided, narrows down search to only elements which match given tag eg: a, div, script, span</p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p></p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p></p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Tab.flash_point">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">flash_point</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">y</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">duration</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0.5</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">size</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">10</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/tab.html#Tab.flash_point"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Tab.flash_point" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Tab.forward">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">forward</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/tab.html#Tab.forward"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Tab.forward" title="Link to this definition">#</a></dt>
<dd><p>history forward</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Tab.fullscreen">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">fullscreen</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/tab.html#Tab.fullscreen"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Tab.fullscreen" title="Link to this definition">#</a></dt>
<dd><p>minimize page/tab/window</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Tab.get">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">get</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">url</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'chrome://welcome'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">new_tab</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">new_window</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/tab.html#Tab.get"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Tab.get" title="Link to this definition">#</a></dt>
<dd><p>top level get. utilizes the first tab to retrieve given url.</p>
<p>convenience function known from selenium.
this function handles waits/sleeps and detects when DOM events fired, so it’s the safest
way of navigating.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>url</strong> – the url to navigate to</p></li>
<li><p><strong>new_tab</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a></span>) – open new tab</p></li>
<li><p><strong>new_window</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a></span>) – open new window</p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>Page</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Tab.get_all_linked_sources">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">get_all_linked_sources</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/tab.html#Tab.get_all_linked_sources"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Tab.get_all_linked_sources" title="Link to this definition">#</a></dt>
<dd><p>get all elements of tag: link, a, img, scripts meta, video, audio</p>
<dl class="field-list simple">
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="element.html#nodriver.Element" title="nodriver.core.element.Element"><code class="xref py py-class docutils literal notranslate"><span class="pre">Element</span></code></a>]</span></p>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p></p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Tab.get_all_urls">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">get_all_urls</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">absolute</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/tab.html#Tab.get_all_urls"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Tab.get_all_urls" title="Link to this definition">#</a></dt>
<dd><p>convenience function, which returns all links (a,link,img,script,meta)</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>absolute</strong> – try to build all the links in absolute form instead of “as is”, often relative</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>list of urls</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Tab.get_content">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">get_content</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/tab.html#Tab.get_content"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Tab.get_content" title="Link to this definition">#</a></dt>
<dd><p>gets the current page source content (html)
:return:
:rtype:</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Tab.get_frame_resource_tree">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">get_frame_resource_tree</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/tab.html#Tab.get_frame_resource_tree"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Tab.get_frame_resource_tree" title="Link to this definition">#</a></dt>
<dd><p>retrieves the frame resource tree for current tab.
There seems no real difference between <span class="xref std std-ref">Tab.get_frame_tree()</span>
but still it returns a different object
:return:
:rtype:</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Tab.get_frame_resource_urls">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">get_frame_resource_urls</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/tab.html#Tab.get_frame_resource_urls"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Tab.get_frame_resource_urls" title="Link to this definition">#</a></dt>
<dd><p>gets the urls of resources
:return:
:rtype:</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Tab.get_frame_tree">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">get_frame_tree</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/tab.html#Tab.get_frame_tree"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Tab.get_frame_tree" title="Link to this definition">#</a></dt>
<dd><p>retrieves the frame tree for current tab
There seems no real difference between <span class="xref std std-ref">Tab.get_frame_resource_tree()</span>
:return:
:rtype:</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Tab.get_local_storage">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">get_local_storage</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/tab.html#Tab.get_local_storage"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Tab.get_local_storage" title="Link to this definition">#</a></dt>
<dd><p>get local storage items as dict of strings (careful!, proper deserialization needs to be done if needed)</p>
<dl class="field-list simple">
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p></p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p></p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Tab.get_window">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">get_window</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/tab.html#Tab.get_window"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Tab.get_window" title="Link to this definition">#</a></dt>
<dd><p>get the window Bounds
:return:
:rtype:</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Tab.inspector_open">
<span class="sig-name descname"><span class="pre">inspector_open</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/tab.html#Tab.inspector_open"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Tab.inspector_open" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="nodriver.Tab.inspector_url">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">inspector_url</span></span><a class="headerlink" href="#nodriver.Tab.inspector_url" title="Link to this definition">#</a></dt>
<dd><p>get the inspector url. this url can be used in another browser to show you the devtools interface for
current tab. useful for debugging (and headless)
:return:
:rtype:</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Tab.js_dumps">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">js_dumps</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">obj_name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">return_by_value</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/tab.html#Tab.js_dumps"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Tab.js_dumps" title="Link to this definition">#</a></dt>
<dd><p>dump given js object with its properties and values as a dict</p>
<p>note: complex objects might not be serializable, therefore this method is not a “source of thruth”</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>obj_name</strong> (<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><em>str</em></a>) – the js object to dump</p></li>
<li><p><strong>return_by_value</strong> (<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><em>bool</em></a>) – if you want an tuple of cdp objects (returnvalue, errors), set this to False</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Union" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Union</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Tuple" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Tuple</span></code></a>[<a class="reference internal" href="../cdp/runtime.html#nodriver.cdp.runtime.RemoteObject" title="nodriver.cdp.runtime.RemoteObject"><code class="xref py py-class docutils literal notranslate"><span class="pre">RemoteObject</span></code></a>, <a class="reference internal" href="../cdp/runtime.html#nodriver.cdp.runtime.ExceptionDetails" title="nodriver.cdp.runtime.ExceptionDetails"><code class="xref py py-class docutils literal notranslate"><span class="pre">ExceptionDetails</span></code></a>]]</span></p>
</dd>
</dl>
<p class="rubric">Example</p>
<p>x = await self.js_dumps(‘window’)
print(x)</p>
<blockquote>
<div><p>‘…{
‘pageYOffset’: 0,
‘visualViewport’: {},
‘screenX’: 10,
‘screenY’: 10,
‘outerWidth’: 1050,
‘outerHeight’: 832,
‘devicePixelRatio’: 1,
‘screenLeft’: 10,
‘screenTop’: 10,
‘styleMedia’: {},
‘onsearch’: None,
‘isSecureContext’: True,
‘trustedTypes’: {},
‘performance’: {‘timeOrigin’: 1707823094767.9,
‘timing’: {‘connectStart’: 0,
‘navigationStart’: 1707823094768,
]…
‘</p>
</div></blockquote>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Tab.maximize">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">maximize</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/tab.html#Tab.maximize"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Tab.maximize" title="Link to this definition">#</a></dt>
<dd><p>maximize page/tab/window</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Tab.medimize">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">medimize</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/tab.html#Tab.medimize"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Tab.medimize" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Tab.minimize">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">minimize</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/tab.html#Tab.minimize"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Tab.minimize" title="Link to this definition">#</a></dt>
<dd><p>minimize page/tab/window</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Tab.mouse_click">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">mouse_click</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">y</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">button</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'left'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">buttons</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">modifiers</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">_until_event</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/tab.html#Tab.mouse_click"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Tab.mouse_click" title="Link to this definition">#</a></dt>
<dd><p>native click on position x,y
:type y: <span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a></span>
:param y:
:type y:
:type x: <span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a></span>
:param x:
:type x:
:type button: <span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>
:param button: str (default = “left”)
:type buttons: <span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a>]</span>
:param buttons: which button (default 1 = left)
:type modifiers: <span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a>]</span>
:param modifiers: <em>(Optional)</em> Bit field representing pressed modifier keys.</p>
<blockquote>
<div><p>Alt=1, Ctrl=2, Meta/Command=4, Shift=8 (default: 0).</p>
</div></blockquote>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>_until_event</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#type" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">type</span></code></a>]</span>) – internal. event to wait for before returning</p>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p></p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Tab.mouse_drag">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">mouse_drag</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">source_point</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">dest_point</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">relative</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">steps</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/tab.html#Tab.mouse_drag"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Tab.mouse_drag" title="Link to this definition">#</a></dt>
<dd><p>drag mouse from one point to another. holding button pressed
you are probably looking for <code class="xref py py-meth docutils literal notranslate"><span class="pre">element.Element.mouse_drag()</span></code> method. where you
can drag on the element</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>dest_point</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#tuple" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">tuple</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a>]</span>) – </p></li>
<li><p><strong>source_point</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#tuple" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">tuple</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a>]</span>) – </p></li>
<li><p><strong>relative</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a></span>) – when True, treats point as relative. for example (-100, 200) will move left 100px and down 200px</p></li>
<li><p><strong>steps</strong> (<a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><em>int</em></a>) – move in &lt;steps&gt; points, this could make it look more “natural” (default 1),
but also a lot slower.
for very smooth action use 50-100</p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p></p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p></p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Tab.mouse_move">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">mouse_move</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">y</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">steps</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">10</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">flash</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/tab.html#Tab.mouse_move"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Tab.mouse_move" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Tab.open_external_inspector">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">open_external_inspector</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/tab.html#Tab.open_external_inspector"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Tab.open_external_inspector" title="Link to this definition">#</a></dt>
<dd><p>opens the system’s browser containing the devtools inspector page
for this tab. could be handy, especially to debug in headless mode.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Tab.query_selector">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">query_selector</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">selector</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">_node</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/tab.html#Tab.query_selector"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Tab.query_selector" title="Link to this definition">#</a></dt>
<dd><p>find single element based on css selector string</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>selector</strong> (<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><em>str</em></a>) – css selector(s)</p>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p></p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p></p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Tab.query_selector_all">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">query_selector_all</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">selector</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">_node</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/tab.html#Tab.query_selector_all"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Tab.query_selector_all" title="Link to this definition">#</a></dt>
<dd><p>equivalent of javascripts document.querySelectorAll.
this is considered one of the main methods to use in this package.</p>
<p>it returns all matching <a class="reference internal" href="element.html#nodriver.Element" title="nodriver.Element"><code class="xref py py-obj docutils literal notranslate"><span class="pre">nodriver.Element</span></code></a> objects.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>selector</strong> (<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><em>str</em></a>) – css selector. (first time? =&gt; <a class="reference external" href="https://www.w3schools.com/cssref/css_selectors.php">https://www.w3schools.com/cssref/css_selectors.php</a> )</p></li>
<li><p><strong>_node</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Union" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Union</span></code></a>[<a class="reference internal" href="../cdp/dom.html#nodriver.cdp.dom.Node" title="nodriver.cdp.dom.Node"><code class="xref py py-class docutils literal notranslate"><span class="pre">Node</span></code></a>, <a class="reference internal" href="element.html#nodriver.Element" title="nodriver.core.element.Element"><code class="xref py py-class docutils literal notranslate"><span class="pre">Element</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span>) – internal use</p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p></p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p></p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Tab.reload">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">reload</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">ignore_cache</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">script_to_evaluate_on_load</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/tab.html#Tab.reload"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Tab.reload" title="Link to this definition">#</a></dt>
<dd><p>Reloads the page</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>ignore_cache</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – when set to True (default), it ignores cache, and re-downloads the items</p></li>
<li><p><strong>script_to_evaluate_on_load</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]</span>) – script to run on load. I actually haven’t experimented with this one, so no guarantees.</p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p></p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p></p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Tab.remove_handler">
<span class="sig-name descname"><span class="pre">remove_handler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">event_type_or_domain</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">handler</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#nodriver.Tab.remove_handler" title="Link to this definition">#</a></dt>
<dd><p>remove a handler for given event
:type event_type_or_domain: <span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Union" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Union</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#type" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">type</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/types.html#types.ModuleType" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">ModuleType</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#type" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">type</span></code></a>]]</span>
:param event_type_or_domain:
:type event_type_or_domain:
:type handler: <span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Union" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Union</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Callable" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Callable</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Awaitable" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Awaitable</span></code></a>]</span>
:param handler:
:type handler:</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Tab.save_screenshot">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">save_screenshot</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">filename</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'auto'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">format</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'jpeg'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">full_page</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/tab.html#Tab.save_screenshot"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Tab.save_screenshot" title="Link to this definition">#</a></dt>
<dd><p>Saves a screenshot of the page.
This is not the same as <a class="reference internal" href="element.html#nodriver.Element.save_screenshot" title="nodriver.Element.save_screenshot"><code class="xref py py-obj docutils literal notranslate"><span class="pre">Element.save_screenshot</span></code></a>, which saves a screenshot of a single element only</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>filename</strong> (<em>PathLike</em>) – uses this as the save path</p></li>
<li><p><strong>format</strong> (<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><em>str</em></a>) – jpeg or png (defaults to jpeg)</p></li>
<li><p><strong>full_page</strong> (<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><em>bool</em></a>) – when False (default) it captures the current viewport. when True, it captures the entire page</p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>the path/filename of saved screenshot</p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)">str</a></p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Tab.scroll_bottom_reached">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">scroll_bottom_reached</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/tab.html#Tab.scroll_bottom_reached"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Tab.scroll_bottom_reached" title="Link to this definition">#</a></dt>
<dd><p>returns True if scroll is at the bottom of the page
handy when you need to scroll over paginated pages of different lengths
:return:
:rtype:</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Tab.scroll_down">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">scroll_down</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">amount</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">25</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/tab.html#Tab.scroll_down"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Tab.scroll_down" title="Link to this definition">#</a></dt>
<dd><p>scrolls down maybe</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>amount</strong> (<a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><em>int</em></a>) – number in percentage. 25 is a quarter of page, 50 half, and 1000 is 10x the page</p>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p></p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p></p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Tab.scroll_up">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">scroll_up</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">amount</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">25</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/tab.html#Tab.scroll_up"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Tab.scroll_up" title="Link to this definition">#</a></dt>
<dd><p>scrolls up maybe</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>amount</strong> (<a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><em>int</em></a>) – number in percentage. 25 is a quarter of page, 50 half, and 1000 is 10x the page</p>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p></p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p></p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Tab.search_frame_resources">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">search_frame_resources</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">query</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/tab.html#Tab.search_frame_resources"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Tab.search_frame_resources" title="Link to this definition">#</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="../cdp/debugger.html#nodriver.cdp.debugger.SearchMatch" title="nodriver.cdp.debugger.SearchMatch"><code class="xref py py-class docutils literal notranslate"><span class="pre">SearchMatch</span></code></a>]]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Tab.select">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">select</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">selector</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">timeout</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">10</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/tab.html#Tab.select"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Tab.select" title="Link to this definition">#</a></dt>
<dd><p>find single element by css selector.
can also be used to wait for such element to appear.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>selector</strong> (<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><em>str</em></a>) – css selector, eg a[href], button[class*=close], a &gt; img[src]</p></li>
<li><p><strong>timeout</strong> (<a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><em>float</em></a><em>,</em><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><em>int</em></a>) – raise timeout exception when after this many seconds nothing is found.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="element.html#nodriver.Element" title="nodriver.core.element.Element"><code class="xref py py-class docutils literal notranslate"><span class="pre">Element</span></code></a></span></p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Tab.select_all">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">select_all</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">selector</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">timeout</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">10</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">include_frames</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/tab.html#Tab.select_all"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Tab.select_all" title="Link to this definition">#</a></dt>
<dd><p>find multiple elements by css selector.
can also be used to wait for such element to appear.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>selector</strong> (<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><em>str</em></a>) – css selector, eg a[href], button[class*=close], a &gt; img[src]</p></li>
<li><p><strong>timeout</strong> (<a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><em>float</em></a><em>,</em><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><em>int</em></a>) – raise timeout exception when after this many seconds nothing is found.</p></li>
<li><p><strong>include_frames</strong> (<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><em>bool</em></a>) – whether to include results in iframes.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="element.html#nodriver.Element" title="nodriver.core.element.Element"><code class="xref py py-class docutils literal notranslate"><span class="pre">Element</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Tab.send">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">send</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cdp_obj</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">_is_update</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#nodriver.Tab.send" title="Link to this definition">#</a></dt>
<dd><p>send a protocol command. the commands are made using any of the cdp.&lt;domain&gt;.&lt;method&gt;()’s
and is used to send custom cdp commands as well.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>cdp_obj</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>]</span>) – the generator object created by a cdp method</p></li>
<li><p><strong>_is_update</strong> – internal flag
prevents infinite loop by skipping the registeration of handlers
when multiple calls to connection.send() are made</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a></span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p></p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Tab.set_download_path">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">set_download_path</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/tab.html#Tab.set_download_path"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Tab.set_download_path" title="Link to this definition">#</a></dt>
<dd><p>sets the download path and allows downloads
this is required for any download function to work (well not entirely, since when unset we set a default folder)</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>path</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Union" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Union</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.TypeVar" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">TypeVar</span></code></a>(<code class="docutils literal notranslate"><span class="pre">PathLike</span></code>, bound= <a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a> | <a class="reference external" href="https://docs.python.org/3/library/pathlib.html#pathlib.Path" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Path</span></code></a>)]</span>) – </p>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p></p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p></p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Tab.set_local_storage">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">set_local_storage</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">items</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/tab.html#Tab.set_local_storage"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Tab.set_local_storage" title="Link to this definition">#</a></dt>
<dd><p>set local storage.
dict items must be strings. simple types will be converted to strings automatically.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>items</strong> (<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#dict" title="(in Python v3.13)"><em>dict</em></a><em>[</em><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><em>str</em></a><em>,</em><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><em>str</em></a><em>]</em>) – dict containing {key:str, value:str}</p>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p></p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p></p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Tab.set_window_size">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">set_window_size</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">left</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">top</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">width</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1280</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">height</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1024</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/tab.html#Tab.set_window_size"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Tab.set_window_size" title="Link to this definition">#</a></dt>
<dd><p>set window size and position</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>left</strong> – pixels from the left of the screen to the window top-left corner</p></li>
<li><p><strong>top</strong> – pixels from the top of the screen to the window top-left corner</p></li>
<li><p><strong>width</strong> – width of the window in pixels</p></li>
<li><p><strong>height</strong> – height of the window in pixels</p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p></p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p></p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Tab.set_window_state">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">set_window_state</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">left</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">top</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">width</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1280</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">height</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">720</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">state</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'normal'</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/tab.html#Tab.set_window_state"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Tab.set_window_state" title="Link to this definition">#</a></dt>
<dd><p>sets the window size or state.</p>
<p>for state you can provide the full name like minimized, maximized, normal, fullscreen, or
something which leads to either of those, like min, mini, mi,  max, ma, maxi, full, fu, no, nor
in case state is set other than “normal”, the left, top, width, and height are ignored.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>left</strong> (<a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><em>int</em></a>) – desired offset from left, in pixels</p></li>
<li><p><strong>top</strong> (<a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><em>int</em></a>) – desired offset from the top, in pixels</p></li>
<li><p><strong>width</strong> (<a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><em>int</em></a>) – desired width in pixels</p></li>
<li><p><strong>height</strong> (<a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><em>int</em></a>) – desired height in pixels</p></li>
<li><p><strong>state</strong> (<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><em>str</em></a>) – <dl class="simple">
<dt>can be one of the following strings:</dt><dd><ul>
<li><p>normal</p></li>
<li><p>fullscreen</p></li>
<li><p>maximized</p></li>
<li><p>minimized</p></li>
</ul>
</dd>
</dl>
</p></li>
</ul>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Tab.sleep">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">sleep</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">t</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/tab.html#Tab.sleep"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Tab.sleep" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="nodriver.Tab.target">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">target</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="../cdp/target.html#nodriver.cdp.target.TargetInfo" title="nodriver.cdp.target.TargetInfo"><span class="pre">TargetInfo</span></a></em><a class="headerlink" href="#nodriver.Tab.target" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Tab.template_location">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">template_location</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">template_image</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/tab.html#Tab.template_location"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Tab.template_location" title="Link to this definition">#</a></dt>
<dd><p>attempts to find the location of given template image in the current viewport
the only real use case for this is bot-detection systems.
you can find for example the location of a ‘verify’-checkbox,
which are hidden from dom using shadow-root’s or workers.</p>
<p>template_image can be custom (for example your language, included is english only),
but you need to create the template image yourself, which is just a cropped
image of the area, see example image, where the target is exactly in the center.
template_image can be custom (for example your language), but you need to
create the template image yourself, where the target is exactly in the center.</p>
<section id="id1">
<h3>example (111x71)<a class="headerlink" href="#id1" title="Link to this heading">#</a></h3>
<p>this includes the white space on the left, to make the box center</p>
<a class="reference internal image-reference" href="../../_images/template_example.png"><img alt="example template image" src="../../_images/template_example.png" style="width: 111px;" /></a>
<dl class="field-list simple">
<dt class="field-odd">type template_image<span class="colon">:</span></dt>
<dd class="field-odd"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.TypeVar" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">TypeVar</span></code></a>(<code class="docutils literal notranslate"><span class="pre">PathLike</span></code>, bound= <a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a> | <a class="reference external" href="https://docs.python.org/3/library/pathlib.html#pathlib.Path" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Path</span></code></a>)</span></p>
</dd>
<dt class="field-even">param template_image<span class="colon">:</span></dt>
<dd class="field-even"><p></p></dd>
<dt class="field-odd">type template_image<span class="colon">:</span></dt>
<dd class="field-odd"><p></p></dd>
<dt class="field-even">return<span class="colon">:</span></dt>
<dd class="field-even"><p></p></dd>
<dt class="field-odd">rtype<span class="colon">:</span></dt>
<dd class="field-odd"><p></p></dd>
</dl>
</section>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Tab.verify_cf">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">verify_cf</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">template_image</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">flash</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/tab.html#Tab.verify_cf"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Tab.verify_cf" title="Link to this definition">#</a></dt>
<dd><p>convenience function to verify cf checkbox</p>
<p>template_image can be custom (for example your language, included is english only),
but you need to create the template image yourself, which is just a cropped
image of the area, see example image, where the target is exactly in the center.</p>
<section id="id2">
<h3>example (111x71)<a class="headerlink" href="#id2" title="Link to this heading">#</a></h3>
<p>this includes the white space on the left, to make the box center</p>
<a class="reference internal image-reference" href="../../_images/template_example.png"><img alt="example template image" src="../../_images/template_example.png" style="width: 111px;" /></a>
<dl class="field-list simple">
<dt class="field-odd">type template_image<span class="colon">:</span></dt>
<dd class="field-odd"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span></p>
</dd>
<dt class="field-even">param template_image<span class="colon">:</span></dt>
<dd class="field-even"><p>template_image can be custom (for example your language, included is english only),
but you need to create the template image yourself, which is just a cropped
image of the area, where the target is exactly in the center. see example on
(<a class="reference external" href="https://ultrafunkamsterdam.github.io/nodriver/nodriver/classes/tab.html#example-111x71">https://ultrafunkamsterdam.github.io/nodriver/nodriver/classes/tab.html#example-111x71</a>),</p>
</dd>
<dt class="field-odd">type template_image<span class="colon">:</span></dt>
<dd class="field-odd"><p></p></dd>
<dt class="field-even">type flash<span class="colon">:</span></dt>
<dd class="field-even"><p></p></dd>
<dt class="field-odd">param flash<span class="colon">:</span></dt>
<dd class="field-odd"><p>whether to show an indicator where the mouse is clicking.</p>
</dd>
<dt class="field-even">type flash<span class="colon">:</span></dt>
<dd class="field-even"><p></p></dd>
<dt class="field-odd">return<span class="colon">:</span></dt>
<dd class="field-odd"><p></p></dd>
<dt class="field-even">rtype<span class="colon">:</span></dt>
<dd class="field-even"><p></p></dd>
</dl>
</section>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Tab.wait">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">wait</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">t</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/tab.html#Tab.wait"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Tab.wait" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Tab.wait_for">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">wait_for</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">selector</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">text</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">timeout</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">10</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/tab.html#Tab.wait_for"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Tab.wait_for" title="Link to this definition">#</a></dt>
<dd><p>variant on query_selector_all and find_elements_by_text
this variant takes either selector or text, and will block until
the requested element(s) are found.</p>
<p>it will block for a maximum of &lt;timeout&gt; seconds, after which
an TimeoutError will be raised</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>selector</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]</span>) – css selector</p></li>
<li><p><strong>text</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]</span>) – text</p></li>
<li><p><strong>timeout</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Union" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Union</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span>) – </p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p></p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference internal" href="element.html#nodriver.Element" title="nodriver.Element">Element</a></p>
</dd>
<dt class="field-even">Raises<span class="colon">:</span></dt>
<dd class="field-even"><p>asyncio.TimeoutError</p>
</dd>
</dl>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="nodriver.Tab.websocket">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">websocket</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">ClientConnection</span></em><a class="headerlink" href="#nodriver.Tab.websocket" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="nodriver.Tab.xpath">
<em class="property"><span class="pre">async</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">xpath</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">xpath</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">timeout</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">2.5</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/core/tab.html#Tab.xpath"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.Tab.xpath" title="Link to this definition">#</a></dt>
<dd><p>find elements by xpath string.
if not immediately found, retries are attempted until <span class="xref std std-ref">timeout</span> is reached (default 2.5 seconds).
in case nothing is found, it returns an empty list. It will not raise.
this timeout mechanism helps when relying on some element to appear before continuing your script.</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># find all the inline scripts (script elements without src attribute )</span>
<span class="k">await</span> <span class="n">tab</span><span class="o">.</span><span class="n">xpath</span><span class="p">(</span><span class="s1">&#39;//script[not(@src)]&#39;</span><span class="p">)</span>

<span class="c1"># or here, more complex, but my personal favorite to case-insensitive text search</span>

<span class="k">await</span> <span class="n">tab</span><span class="o">.</span><span class="n">xpath</span><span class="p">(</span><span class="s1">&#39;//text()[ contains( translate(., &quot;ABCDEFGHIJKLMNOPQRSTUVWXYZ&quot;, &quot;abcdefghijklmnopqrstuvwxyz&quot;),&quot;test&quot;)]&#39;</span><span class="p">)</span>
</pre></div>
</div>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>xpath</strong> (<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><em>str</em></a>) – </p></li>
<li><p><strong>timeout</strong> (<a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><em>float</em></a>) – 2.5</p></li>
</ul>
</dd>
</dl>
<p>:return:List[nodriver.Element] or []
:rtype:</p>
</dd></dl>

</section>
</dd></dl>

</section>

        </article>
      </div>
      <footer>
        
        <div class="related-pages">
          <a class="next-page" href="element.html">
              <div class="page-info">
                <div class="context">
                  <span>Next</span>
                </div>
                <div class="title">Element class</div>
              </div>
              <svg class="furo-related-icon"><use href="#svg-arrow-right"></use></svg>
            </a>
          <a class="prev-page" href="browser.html">
              <svg class="furo-related-icon"><use href="#svg-arrow-right"></use></svg>
              <div class="page-info">
                <div class="context">
                  <span>Previous</span>
                </div>
                
                <div class="title">Browser class</div>
                
              </div>
            </a>
        </div>
        <div class="bottom-of-page">
          <div class="left-details">
            <div class="copyright">
                Copyright &#169; 2023, Author
            </div>
            Made with <a href="https://www.sphinx-doc.org/">Sphinx</a> and <a class="muted-link" href="https://pradyunsg.me">@pradyunsg</a>'s
            
            <a href="https://github.com/pradyunsg/furo">Furo</a>
            
          </div>
          <div class="right-details">
            
          </div>
        </div>
        
      </footer>
    </div>
    <aside class="toc-drawer">
      
      
      <div class="toc-sticky toc-scroll">
        <div class="toc-title-container">
          <span class="toc-title">
            On this page
          </span>
        </div>
        <div class="toc-tree-container">
          <div class="toc-tree">
            <ul>
<li><a class="reference internal" href="#">Tab class</a><ul>
<li><a class="reference internal" href="#nodriver.Tab"><code class="docutils literal notranslate"><span class="pre">Tab</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.Tab.activate"><code class="docutils literal notranslate"><span class="pre">Tab.activate()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.add_handler"><code class="docutils literal notranslate"><span class="pre">Tab.add_handler()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.attached"><code class="docutils literal notranslate"><span class="pre">Tab.attached</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.back"><code class="docutils literal notranslate"><span class="pre">Tab.back()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.bring_to_front"><code class="docutils literal notranslate"><span class="pre">Tab.bring_to_front()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.browser"><code class="docutils literal notranslate"><span class="pre">Tab.browser</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.bypass_insecure_connection_warning"><code class="docutils literal notranslate"><span class="pre">Tab.bypass_insecure_connection_warning()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.close"><code class="docutils literal notranslate"><span class="pre">Tab.close()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.closed"><code class="docutils literal notranslate"><span class="pre">Tab.closed</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.connect"><code class="docutils literal notranslate"><span class="pre">Tab.connect()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.disconnect"><code class="docutils literal notranslate"><span class="pre">Tab.disconnect()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.download_file"><code class="docutils literal notranslate"><span class="pre">Tab.download_file()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.evaluate"><code class="docutils literal notranslate"><span class="pre">Tab.evaluate()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.feed_cdp"><code class="docutils literal notranslate"><span class="pre">Tab.feed_cdp()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.find"><code class="docutils literal notranslate"><span class="pre">Tab.find()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.find_all"><code class="docutils literal notranslate"><span class="pre">Tab.find_all()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.find_element_by_text"><code class="docutils literal notranslate"><span class="pre">Tab.find_element_by_text()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.find_elements_by_text"><code class="docutils literal notranslate"><span class="pre">Tab.find_elements_by_text()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.flash_point"><code class="docutils literal notranslate"><span class="pre">Tab.flash_point()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.forward"><code class="docutils literal notranslate"><span class="pre">Tab.forward()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.fullscreen"><code class="docutils literal notranslate"><span class="pre">Tab.fullscreen()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.get"><code class="docutils literal notranslate"><span class="pre">Tab.get()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.get_all_linked_sources"><code class="docutils literal notranslate"><span class="pre">Tab.get_all_linked_sources()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.get_all_urls"><code class="docutils literal notranslate"><span class="pre">Tab.get_all_urls()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.get_content"><code class="docutils literal notranslate"><span class="pre">Tab.get_content()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.get_frame_resource_tree"><code class="docutils literal notranslate"><span class="pre">Tab.get_frame_resource_tree()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.get_frame_resource_urls"><code class="docutils literal notranslate"><span class="pre">Tab.get_frame_resource_urls()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.get_frame_tree"><code class="docutils literal notranslate"><span class="pre">Tab.get_frame_tree()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.get_local_storage"><code class="docutils literal notranslate"><span class="pre">Tab.get_local_storage()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.get_window"><code class="docutils literal notranslate"><span class="pre">Tab.get_window()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.inspector_open"><code class="docutils literal notranslate"><span class="pre">Tab.inspector_open()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.inspector_url"><code class="docutils literal notranslate"><span class="pre">Tab.inspector_url</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.js_dumps"><code class="docutils literal notranslate"><span class="pre">Tab.js_dumps()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.maximize"><code class="docutils literal notranslate"><span class="pre">Tab.maximize()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.medimize"><code class="docutils literal notranslate"><span class="pre">Tab.medimize()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.minimize"><code class="docutils literal notranslate"><span class="pre">Tab.minimize()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.mouse_click"><code class="docutils literal notranslate"><span class="pre">Tab.mouse_click()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.mouse_drag"><code class="docutils literal notranslate"><span class="pre">Tab.mouse_drag()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.mouse_move"><code class="docutils literal notranslate"><span class="pre">Tab.mouse_move()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.open_external_inspector"><code class="docutils literal notranslate"><span class="pre">Tab.open_external_inspector()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.query_selector"><code class="docutils literal notranslate"><span class="pre">Tab.query_selector()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.query_selector_all"><code class="docutils literal notranslate"><span class="pre">Tab.query_selector_all()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.reload"><code class="docutils literal notranslate"><span class="pre">Tab.reload()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.remove_handler"><code class="docutils literal notranslate"><span class="pre">Tab.remove_handler()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.save_screenshot"><code class="docutils literal notranslate"><span class="pre">Tab.save_screenshot()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.scroll_bottom_reached"><code class="docutils literal notranslate"><span class="pre">Tab.scroll_bottom_reached()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.scroll_down"><code class="docutils literal notranslate"><span class="pre">Tab.scroll_down()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.scroll_up"><code class="docutils literal notranslate"><span class="pre">Tab.scroll_up()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.search_frame_resources"><code class="docutils literal notranslate"><span class="pre">Tab.search_frame_resources()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.select"><code class="docutils literal notranslate"><span class="pre">Tab.select()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.select_all"><code class="docutils literal notranslate"><span class="pre">Tab.select_all()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.send"><code class="docutils literal notranslate"><span class="pre">Tab.send()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.set_download_path"><code class="docutils literal notranslate"><span class="pre">Tab.set_download_path()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.set_local_storage"><code class="docutils literal notranslate"><span class="pre">Tab.set_local_storage()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.set_window_size"><code class="docutils literal notranslate"><span class="pre">Tab.set_window_size()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.set_window_state"><code class="docutils literal notranslate"><span class="pre">Tab.set_window_state()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.sleep"><code class="docutils literal notranslate"><span class="pre">Tab.sleep()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.target"><code class="docutils literal notranslate"><span class="pre">Tab.target</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.template_location"><code class="docutils literal notranslate"><span class="pre">Tab.template_location()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.verify_cf"><code class="docutils literal notranslate"><span class="pre">Tab.verify_cf()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.wait"><code class="docutils literal notranslate"><span class="pre">Tab.wait()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.wait_for"><code class="docutils literal notranslate"><span class="pre">Tab.wait_for()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.websocket"><code class="docutils literal notranslate"><span class="pre">Tab.websocket</span></code></a></li>
<li><a class="reference internal" href="#nodriver.Tab.xpath"><code class="docutils literal notranslate"><span class="pre">Tab.xpath()</span></code></a></li>
</ul>
</li>
</ul>
</li>
</ul>

          </div>
        </div>
      </div>
      
      
    </aside>
  </div>
</div><script src="../../_static/documentation_options.js?v=5929fcd5"></script>
    <script src="../../_static/doctools.js?v=888ff710"></script>
    <script src="../../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../../_static/scripts/furo.js?v=32e29ea5"></script>
    </body>
</html>