using System.Text.Json.Serialization;
using NoDriverSharp.CDP.Common;

namespace NoDriverSharp.CDP.Domains;

/// <summary>
/// This domain exposes DOM read/write operations. Each DOM Node is represented with its mirror object
/// that has an `id`. This `id` can be used to get additional information on the Node, resolve it into
/// the JavaScript object wrapper, etc. It is important that client receives DOM events only for the
/// nodes that are known to the client. Backend keeps track of the nodes that were sent to the client
/// and never sends the same node twice. It is client's responsibility to collect information about
/// the nodes that were sent to the client.
/// </summary>
public static class DOM
{
    /// <summary>
    /// Enables DOM agent for the given page.
    /// </summary>
    public class Enable : CDPCommand
    {
        public override string Method => "DOM.enable";
    }

    /// <summary>
    /// Disables DOM agent for the given page.
    /// </summary>
    public class Disable : CDPCommand
    {
        public override string Method => "DOM.disable";
    }

    /// <summary>
    /// Returns the root DOM node (and optionally the subtree) to the caller.
    /// </summary>
    public class GetDocument : CDPCommand<GetDocument.Parameters>
    {
        public override string Method => "DOM.getDocument";

        public class Parameters
        {
            /// <summary>
            /// The maximum depth at which children should be retrieved, defaults to 1.
            /// </summary>
            [JsonPropertyName("depth")]
            public int? Depth { get; set; }

            /// <summary>
            /// Whether or not iframes and shadow roots should be traversed when returning the subtree.
            /// </summary>
            [JsonPropertyName("pierce")]
            public bool? Pierce { get; set; }
        }

        public class Result
        {
            /// <summary>
            /// Resulting node.
            /// </summary>
            [JsonPropertyName("root")]
            public required Node Root { get; set; }
        }

        public GetDocument(int? depth = null, bool? pierce = null)
            : base(new Parameters { Depth = depth, Pierce = pierce })
        {
        }
    }

    /// <summary>
    /// Searches for a given string in the DOM tree. Use `getSearchResults` to access search results or
    /// `cancelSearch` to end this search session.
    /// </summary>
    public class PerformSearch : CDPCommand<PerformSearch.Parameters>
    {
        public override string Method => "DOM.performSearch";

        public class Parameters
        {
            /// <summary>
            /// Plain text or query selector or XPath search query.
            /// </summary>
            [JsonPropertyName("query")]
            public string Query { get; set; } = string.Empty;

            /// <summary>
            /// True to search in user agent shadow DOM.
            /// </summary>
            [JsonPropertyName("includeUserAgentShadowDOM")]
            public bool? IncludeUserAgentShadowDOM { get; set; }
        }

        public class Result
        {
            /// <summary>
            /// Unique search session identifier.
            /// </summary>
            [JsonPropertyName("searchId")]
            public string SearchId { get; set; } = string.Empty;

            /// <summary>
            /// Number of search results.
            /// </summary>
            [JsonPropertyName("resultCount")]
            public int ResultCount { get; set; }
        }

        public PerformSearch(string query, bool? includeUserAgentShadowDOM = null)
            : base(new Parameters { Query = query, IncludeUserAgentShadowDOM = includeUserAgentShadowDOM })
        {
        }
    }

    /// <summary>
    /// Returns search results from given `fromIndex` to given `toIndex` from the search with the given identifier.
    /// </summary>
    public class GetSearchResults : CDPCommand<GetSearchResults.Parameters>
    {
        public override string Method => "DOM.getSearchResults";

        public class Parameters
        {
            /// <summary>
            /// Unique search session identifier.
            /// </summary>
            [JsonPropertyName("searchId")]
            public string SearchId { get; set; } = string.Empty;

            /// <summary>
            /// Start index of the search result to be returned.
            /// </summary>
            [JsonPropertyName("fromIndex")]
            public int FromIndex { get; set; }

            /// <summary>
            /// End index of the search result to be returned.
            /// </summary>
            [JsonPropertyName("toIndex")]
            public int ToIndex { get; set; }
        }

        public class Result
        {
            /// <summary>
            /// Ids of the search result nodes.
            /// </summary>
            [JsonPropertyName("nodeIds")]
            public NodeId[] NodeIds { get; set; } = Array.Empty<NodeId>();
        }

        public GetSearchResults(string searchId, int fromIndex, int toIndex)
            : base(new Parameters { SearchId = searchId, FromIndex = fromIndex, ToIndex = toIndex })
        {
        }
    }

    /// <summary>
    /// Resolves the JavaScript node object for a given NodeId or BackendNodeId.
    /// </summary>
    public class ResolveNode : CDPCommand<ResolveNode.Parameters>
    {
        public override string Method => "DOM.resolveNode";

        public class Parameters
        {
            /// <summary>
            /// Id of the node to resolve.
            /// </summary>
            [JsonPropertyName("nodeId")]
            public NodeId? NodeId { get; set; }

            /// <summary>
            /// Backend identifier of the node to resolve.
            /// </summary>
            [JsonPropertyName("backendNodeId")]
            public BackendNodeId? BackendNodeId { get; set; }

            /// <summary>
            /// Symbolic group name that can be used to release multiple objects.
            /// </summary>
            [JsonPropertyName("objectGroup")]
            public string? ObjectGroup { get; set; }

            /// <summary>
            /// Execution context in which to resolve the node.
            /// </summary>
            [JsonPropertyName("executionContextId")]
            public ExecutionContextId? ExecutionContextId { get; set; }
        }

        public class Result
        {
            /// <summary>
            /// JavaScript object wrapper for given node.
            /// </summary>
            [JsonPropertyName("object")]
            public required RemoteObject Object { get; set; }
        }

        public ResolveNode(NodeId? nodeId = null, BackendNodeId? backendNodeId = null, string? objectGroup = null, ExecutionContextId? executionContextId = null)
            : base(new Parameters { NodeId = nodeId, BackendNodeId = backendNodeId, ObjectGroup = objectGroup, ExecutionContextId = executionContextId })
        {
        }
    }

    /// <summary>
    /// DOM interaction is implemented in terms of mirror objects that represent the actual DOM nodes.
    /// DOMNode is a base node mirror type.
    /// </summary>
    public class Node
    {
        /// <summary>
        /// Node identifier that is passed into the rest of the DOM messages as the `nodeId`.
        /// </summary>
        [JsonPropertyName("nodeId")]
        public required NodeId NodeId { get; set; }

        /// <summary>
        /// The id of the parent node if any.
        /// </summary>
        [JsonPropertyName("parentId")]
        public NodeId? ParentId { get; set; }

        /// <summary>
        /// The BackendNodeId for this node.
        /// </summary>
        [JsonPropertyName("backendNodeId")]
        public required BackendNodeId BackendNodeId { get; set; }

        /// <summary>
        /// `Node`'s nodeType.
        /// </summary>
        [JsonPropertyName("nodeType")]
        public int NodeType { get; set; }

        /// <summary>
        /// `Node`'s nodeName.
        /// </summary>
        [JsonPropertyName("nodeName")]
        public string NodeName { get; set; } = string.Empty;

        /// <summary>
        /// `Node`'s localName.
        /// </summary>
        [JsonPropertyName("localName")]
        public string LocalName { get; set; } = string.Empty;

        /// <summary>
        /// `Node`'s nodeValue.
        /// </summary>
        [JsonPropertyName("nodeValue")]
        public string NodeValue { get; set; } = string.Empty;

        /// <summary>
        /// Child count for `Container` nodes.
        /// </summary>
        [JsonPropertyName("childNodeCount")]
        public int? ChildNodeCount { get; set; }

        /// <summary>
        /// Child nodes of this node when requested with children.
        /// </summary>
        [JsonPropertyName("children")]
        public Node[]? Children { get; set; }

        /// <summary>
        /// Attributes of the `Element` node in the form of flat array `[name1, value1, name2, value2]`.
        /// </summary>
        [JsonPropertyName("attributes")]
        public string[]? Attributes { get; set; }

        /// <summary>
        /// Document URL that `Document` or `FrameOwner` node points to.
        /// </summary>
        [JsonPropertyName("documentURL")]
        public string? DocumentURL { get; set; }

        /// <summary>
        /// Base URL that `Document` or `FrameOwner` node uses for URL completion.
        /// </summary>
        [JsonPropertyName("baseURL")]
        public string? BaseURL { get; set; }

        /// <summary>
        /// `DocumentType`'s publicId.
        /// </summary>
        [JsonPropertyName("publicId")]
        public string? PublicId { get; set; }

        /// <summary>
        /// `DocumentType`'s systemId.
        /// </summary>
        [JsonPropertyName("systemId")]
        public string? SystemId { get; set; }

        /// <summary>
        /// `DocumentType`'s internalSubset.
        /// </summary>
        [JsonPropertyName("internalSubset")]
        public string? InternalSubset { get; set; }

        /// <summary>
        /// `Document`'s XML version in case of XML documents.
        /// </summary>
        [JsonPropertyName("xmlVersion")]
        public string? XmlVersion { get; set; }

        /// <summary>
        /// `Attr`'s name.
        /// </summary>
        [JsonPropertyName("name")]
        public string? Name { get; set; }

        /// <summary>
        /// `Attr`'s value.
        /// </summary>
        [JsonPropertyName("value")]
        public string? Value { get; set; }

        /// <summary>
        /// Frame ID for frame owner elements.
        /// </summary>
        [JsonPropertyName("frameId")]
        public FrameId? FrameId { get; set; }

        /// <summary>
        /// Content document for frame owner elements.
        /// </summary>
        [JsonPropertyName("contentDocument")]
        public Node? ContentDocument { get; set; }

        /// <summary>
        /// Shadow root list for given element host.
        /// </summary>
        [JsonPropertyName("shadowRoots")]
        public Node[]? ShadowRoots { get; set; }

        /// <summary>
        /// Content document fragment for template elements.
        /// </summary>
        [JsonPropertyName("templateContent")]
        public Node? TemplateContent { get; set; }

        /// <summary>
        /// Pseudo elements associated with this node.
        /// </summary>
        [JsonPropertyName("pseudoElements")]
        public Node[]? PseudoElements { get; set; }

        /// <summary>
        /// Import document for the HTMLImport links.
        /// </summary>
        [JsonPropertyName("importedDocument")]
        public Node? ImportedDocument { get; set; }

        /// <summary>
        /// Distributed nodes for given insertion point.
        /// </summary>
        [JsonPropertyName("distributedNodes")]
        public BackendNode[]? DistributedNodes { get; set; }

        /// <summary>
        /// Whether the node is SVG.
        /// </summary>
        [JsonPropertyName("isSVG")]
        public bool? IsSVG { get; set; }
    }

    /// <summary>
    /// Backend node with a friendly name.
    /// </summary>
    public class BackendNode
    {
        /// <summary>
        /// `Node`'s nodeType.
        /// </summary>
        [JsonPropertyName("nodeType")]
        public int NodeType { get; set; }

        /// <summary>
        /// `Node`'s nodeName.
        /// </summary>
        [JsonPropertyName("nodeName")]
        public string NodeName { get; set; } = string.Empty;

        /// <summary>
        /// The BackendNodeId for this node.
        /// </summary>
        [JsonPropertyName("backendNodeId")]
        public required BackendNodeId BackendNodeId { get; set; }
    }

    /// <summary>
    /// Mirror object referencing original JavaScript object.
    /// </summary>
    public class RemoteObject
    {
        /// <summary>
        /// Object type.
        /// </summary>
        [JsonPropertyName("type")]
        public string Type { get; set; } = string.Empty;

        /// <summary>
        /// Object subtype hint. Specified for `object` type values only.
        /// </summary>
        [JsonPropertyName("subtype")]
        public string? Subtype { get; set; }

        /// <summary>
        /// Object class (constructor) name. Specified for `object` type values only.
        /// </summary>
        [JsonPropertyName("className")]
        public string? ClassName { get; set; }

        /// <summary>
        /// Remote object value in case of primitive values or JSON values (if it was requested).
        /// </summary>
        [JsonPropertyName("value")]
        public object? Value { get; set; }

        /// <summary>
        /// Primitive value which can not be JSON-stringified does not have `value`, but gets this property.
        /// </summary>
        [JsonPropertyName("unserializableValue")]
        public string? UnserializableValue { get; set; }

        /// <summary>
        /// String representation of the object.
        /// </summary>
        [JsonPropertyName("description")]
        public string? Description { get; set; }

        /// <summary>
        /// Unique object identifier (for non-primitive values).
        /// </summary>
        [JsonPropertyName("objectId")]
        public RemoteObjectId? ObjectId { get; set; }

        /// <summary>
        /// Preview containing abbreviated property values. Specified for `object` type values only.
        /// </summary>
        [JsonPropertyName("preview")]
        public ObjectPreview? Preview { get; set; }

        /// <summary>
        /// Custom preview for the object. Specified for `object` type values only.
        /// </summary>
        [JsonPropertyName("customPreview")]
        public CustomPreview? CustomPreview { get; set; }
    }

    /// <summary>
    /// Object containing abbreviated remote object value.
    /// </summary>
    public class ObjectPreview
    {
        /// <summary>
        /// Object type.
        /// </summary>
        [JsonPropertyName("type")]
        public string Type { get; set; } = string.Empty;

        /// <summary>
        /// Object subtype hint. Specified for `object` type values only.
        /// </summary>
        [JsonPropertyName("subtype")]
        public string? Subtype { get; set; }

        /// <summary>
        /// String representation of the object.
        /// </summary>
        [JsonPropertyName("description")]
        public string? Description { get; set; }

        /// <summary>
        /// True iff some of the properties or entries of the original object did not fit.
        /// </summary>
        [JsonPropertyName("overflow")]
        public bool Overflow { get; set; }

        /// <summary>
        /// List of the properties.
        /// </summary>
        [JsonPropertyName("properties")]
        public PropertyPreview[] Properties { get; set; } = Array.Empty<PropertyPreview>();

        /// <summary>
        /// List of the entries. Specified for `map` and `set` subtype values only.
        /// </summary>
        [JsonPropertyName("entries")]
        public EntryPreview[]? Entries { get; set; }
    }

    /// <summary>
    /// Property preview.
    /// </summary>
    public class PropertyPreview
    {
        /// <summary>
        /// Property name.
        /// </summary>
        [JsonPropertyName("name")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Object type. Accessor means that the property is an accessor property.
        /// </summary>
        [JsonPropertyName("type")]
        public string Type { get; set; } = string.Empty;

        /// <summary>
        /// User-friendly property value string.
        /// </summary>
        [JsonPropertyName("value")]
        public string? Value { get; set; }

        /// <summary>
        /// Nested value preview.
        /// </summary>
        [JsonPropertyName("valuePreview")]
        public ObjectPreview? ValuePreview { get; set; }

        /// <summary>
        /// Object subtype hint. Specified for `object` type values only.
        /// </summary>
        [JsonPropertyName("subtype")]
        public string? Subtype { get; set; }
    }

    /// <summary>
    /// Entry preview.
    /// </summary>
    public class EntryPreview
    {
        /// <summary>
        /// Preview of the key. Specified for map-like collection entries.
        /// </summary>
        [JsonPropertyName("key")]
        public ObjectPreview? Key { get; set; }

        /// <summary>
        /// Preview of the value.
        /// </summary>
        [JsonPropertyName("value")]
        public ObjectPreview Value { get; set; } = new();
    }

    /// <summary>
    /// Custom preview.
    /// </summary>
    public class CustomPreview
    {
        /// <summary>
        /// The JSON-stringified result of formatter.header(object, config) call.
        /// </summary>
        [JsonPropertyName("header")]
        public string Header { get; set; } = string.Empty;

        /// <summary>
        /// If formatter returns true as a result of formatter.hasBody call then bodyGetterId will
        /// contain RemoteObjectId for the function that returns result of formatter.body(object, config) call.
        /// </summary>
        [JsonPropertyName("bodyGetterId")]
        public RemoteObjectId? BodyGetterId { get; set; }
    }
}
