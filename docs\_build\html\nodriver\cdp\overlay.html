<!doctype html>
<html class="no-js" lang="en" data-content_root="../../">
  <head><meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width,initial-scale=1"/>
    <meta name="color-scheme" content="light dark"><meta name="viewport" content="width=device-width, initial-scale=1" />
<link rel="index" title="Index" href="../../genindex.html" /><link rel="search" title="Search" href="../../search.html" /><link rel="next" title="Page" href="page.html" /><link rel="prev" title="Network" href="network.html" />

    <!-- Generated with Sphinx 7.2.6 and Furo 2023.09.10 -->
        <title>Overlay - nodriver documentation</title>
      <link rel="stylesheet" type="text/css" href="../../_static/pygments.css?v=fa44fd50" />
    <link rel="stylesheet" type="text/css" href="../../_static/styles/furo.css?v=135e06be" />
    <link rel="stylesheet" type="text/css" href="../../_static/styles/furo-extensions.css?v=36a5483c" />
    <link rel="stylesheet" type="text/css" href="../../_static/./custom.css?v=3934642d" />
    
    


<style>
  body {
    --color-code-background: #eeffcc;
  --color-code-foreground: black;
  
  }
  @media not print {
    body[data-theme="dark"] {
      --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8eefba;
  --color-background-primary: #111112;
  --color-problematic: #e7aeae;
  
    }
    @media (prefers-color-scheme: dark) {
      body:not([data-theme="light"]) {
        --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8eefba;
  --color-background-primary: #111112;
  --color-problematic: #e7aeae;
  
      }
    }
  }
</style></head>
  <body>
    
    <script>
      document.body.dataset.theme = localStorage.getItem("theme") || "auto";
    </script>
    

<svg xmlns="http://www.w3.org/2000/svg" style="display: none;">
  <symbol id="svg-toc" viewBox="0 0 24 24">
    <title>Contents</title>
    <svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 1024 1024">
      <path d="M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM115.4 518.9L271.7 642c5.8 4.6 14.4.5 14.4-6.9V388.9c0-7.4-8.5-11.5-14.4-6.9L115.4 505.1a8.74 8.74 0 0 0 0 13.8z"/>
    </svg>
  </symbol>
  <symbol id="svg-menu" viewBox="0 0 24 24">
    <title>Menu</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-menu">
      <line x1="3" y1="12" x2="21" y2="12"></line>
      <line x1="3" y1="6" x2="21" y2="6"></line>
      <line x1="3" y1="18" x2="21" y2="18"></line>
    </svg>
  </symbol>
  <symbol id="svg-arrow-right" viewBox="0 0 24 24">
    <title>Expand</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-chevron-right">
      <polyline points="9 18 15 12 9 6"></polyline>
    </svg>
  </symbol>
  <symbol id="svg-sun" viewBox="0 0 24 24">
    <title>Light mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="feather-sun">
      <circle cx="12" cy="12" r="5"></circle>
      <line x1="12" y1="1" x2="12" y2="3"></line>
      <line x1="12" y1="21" x2="12" y2="23"></line>
      <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
      <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
      <line x1="1" y1="12" x2="3" y2="12"></line>
      <line x1="21" y1="12" x2="23" y2="12"></line>
      <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
      <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
    </svg>
  </symbol>
  <symbol id="svg-moon" viewBox="0 0 24 24">
    <title>Dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-moon">
      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
      <path d="M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z" />
    </svg>
  </symbol>
  <symbol id="svg-sun-half" viewBox="0 0 24 24">
    <title>Auto light/dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-shadow">
      <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
      <circle cx="12" cy="12" r="9" />
      <path d="M13 12h5" />
      <path d="M13 15h4" />
      <path d="M13 18h1" />
      <path d="M13 9h4" />
      <path d="M13 6h1" />
    </svg>
  </symbol>
</svg>

<input type="checkbox" class="sidebar-toggle" name="__navigation" id="__navigation">
<input type="checkbox" class="sidebar-toggle" name="__toc" id="__toc">
<label class="overlay sidebar-overlay" for="__navigation">
  <div class="visually-hidden">Hide navigation sidebar</div>
</label>
<label class="overlay toc-overlay" for="__toc">
  <div class="visually-hidden">Hide table of contents sidebar</div>
</label>



<div class="page">
  <header class="mobile-header">
    <div class="header-left">
      <label class="nav-overlay-icon" for="__navigation">
        <div class="visually-hidden">Toggle site navigation sidebar</div>
        <i class="icon"><svg><use href="#svg-menu"></use></svg></i>
      </label>
    </div>
    <div class="header-center">
      <a href="../../index.html"><div class="brand">nodriver  documentation</div></a>
    </div>
    <div class="header-right">
      <div class="theme-toggle-container theme-toggle-header">
        <button class="theme-toggle">
          <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
          <svg class="theme-icon-when-auto"><use href="#svg-sun-half"></use></svg>
          <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
          <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
        </button>
      </div>
      <label class="toc-overlay-icon toc-header-icon" for="__toc">
        <div class="visually-hidden">Toggle table of contents sidebar</div>
        <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
      </label>
    </div>
  </header>
  <aside class="sidebar-drawer">
    <div class="sidebar-container">
      
      <div class="sidebar-sticky"><div class="sidebar-scroll"><a class="sidebar-brand" href="../../index.html">
  
  
  <span class="sidebar-brand-text">nodriver  documentation</span>
  
</a><form class="sidebar-search-container" method="get" action="../../search.html" role="search">
  <input class="sidebar-search" placeholder="Search" name="q" aria-label="Search">
  <input type="hidden" name="check_keywords" value="yes">
  <input type="hidden" name="area" value="default">
</form>
<div id="searchbox"></div><div class="sidebar-tree">
  <ul>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html">Quickstart guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#usage-example">usage example</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#more-complete-example">More complete example</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#custom-starting-options">Custom starting options</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#alternative-custom-options">Alternative custom options</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#proxies-socks5-authenticated-too">Proxies (socks5, authenticated too)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#concrete-example">Concrete example</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../classes/browser.html">Browser class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../classes/tab.html">Tab class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../classes/element.html">Element class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../classes/others_and_helpers.html">Other classes and Helper classes</a></li>
</ul>
<ul class="current">
<li class="toctree-l1 current has-children"><a class="reference internal" href="../cdp.html">CDP object</a><input checked="" class="toctree-checkbox" id="toctree-checkbox-1" name="toctree-checkbox-1" role="switch" type="checkbox"/><label for="toctree-checkbox-1"><div class="visually-hidden">Toggle navigation of CDP object</div><i class="icon"><svg><use href="#svg-arrow-right"></use></svg></i></label><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="accessibility.html">Accessibility</a></li>
<li class="toctree-l2"><a class="reference internal" href="animation.html">Animation</a></li>
<li class="toctree-l2"><a class="reference internal" href="audits.html">Audits</a></li>
<li class="toctree-l2"><a class="reference internal" href="autofill.html">Autofill</a></li>
<li class="toctree-l2"><a class="reference internal" href="background_service.html">BackgroundService</a></li>
<li class="toctree-l2"><a class="reference internal" href="bluetooth_emulation.html">BluetoothEmulation</a></li>
<li class="toctree-l2"><a class="reference internal" href="browser.html">Browser</a></li>
<li class="toctree-l2"><a class="reference internal" href="cache_storage.html">CacheStorage</a></li>
<li class="toctree-l2"><a class="reference internal" href="cast.html">Cast</a></li>
<li class="toctree-l2"><a class="reference internal" href="console.html">Console</a></li>
<li class="toctree-l2"><a class="reference internal" href="css.html">CSS</a></li>
<li class="toctree-l2"><a class="reference internal" href="debugger.html">Debugger</a></li>
<li class="toctree-l2"><a class="reference internal" href="device_access.html">DeviceAccess</a></li>
<li class="toctree-l2"><a class="reference internal" href="device_orientation.html">DeviceOrientation</a></li>
<li class="toctree-l2"><a class="reference internal" href="dom.html">DOM</a></li>
<li class="toctree-l2"><a class="reference internal" href="dom_debugger.html">DOMDebugger</a></li>
<li class="toctree-l2"><a class="reference internal" href="dom_snapshot.html">DOMSnapshot</a></li>
<li class="toctree-l2"><a class="reference internal" href="dom_storage.html">DOMStorage</a></li>
<li class="toctree-l2"><a class="reference internal" href="emulation.html">Emulation</a></li>
<li class="toctree-l2"><a class="reference internal" href="event_breakpoints.html">EventBreakpoints</a></li>
<li class="toctree-l2"><a class="reference internal" href="extensions.html">Extensions</a></li>
<li class="toctree-l2"><a class="reference internal" href="fed_cm.html">FedCm</a></li>
<li class="toctree-l2"><a class="reference internal" href="fetch.html">Fetch</a></li>
<li class="toctree-l2"><a class="reference internal" href="file_system.html">FileSystem</a></li>
<li class="toctree-l2"><a class="reference internal" href="headless_experimental.html">HeadlessExperimental</a></li>
<li class="toctree-l2"><a class="reference internal" href="heap_profiler.html">HeapProfiler</a></li>
<li class="toctree-l2"><a class="reference internal" href="indexed_db.html">IndexedDB</a></li>
<li class="toctree-l2"><a class="reference internal" href="input_.html">Input</a></li>
<li class="toctree-l2"><a class="reference internal" href="inspector.html">Inspector</a></li>
<li class="toctree-l2"><a class="reference internal" href="io.html">IO</a></li>
<li class="toctree-l2"><a class="reference internal" href="layer_tree.html">LayerTree</a></li>
<li class="toctree-l2"><a class="reference internal" href="log.html">Log</a></li>
<li class="toctree-l2"><a class="reference internal" href="media.html">Media</a></li>
<li class="toctree-l2"><a class="reference internal" href="memory.html">Memory</a></li>
<li class="toctree-l2"><a class="reference internal" href="network.html">Network</a></li>
<li class="toctree-l2 current current-page"><a class="current reference internal" href="#">Overlay</a></li>
<li class="toctree-l2"><a class="reference internal" href="page.html">Page</a></li>
<li class="toctree-l2"><a class="reference internal" href="performance.html">Performance</a></li>
<li class="toctree-l2"><a class="reference internal" href="performance_timeline.html">PerformanceTimeline</a></li>
<li class="toctree-l2"><a class="reference internal" href="preload.html">Preload</a></li>
<li class="toctree-l2"><a class="reference internal" href="profiler.html">Profiler</a></li>
<li class="toctree-l2"><a class="reference internal" href="pwa.html">PWA</a></li>
<li class="toctree-l2"><a class="reference internal" href="runtime.html">Runtime</a></li>
<li class="toctree-l2"><a class="reference internal" href="schema.html">Schema</a></li>
<li class="toctree-l2"><a class="reference internal" href="security.html">Security</a></li>
<li class="toctree-l2"><a class="reference internal" href="service_worker.html">ServiceWorker</a></li>
<li class="toctree-l2"><a class="reference internal" href="storage.html">Storage</a></li>
<li class="toctree-l2"><a class="reference internal" href="system_info.html">SystemInfo</a></li>
<li class="toctree-l2"><a class="reference internal" href="target.html">Target</a></li>
<li class="toctree-l2"><a class="reference internal" href="tethering.html">Tethering</a></li>
<li class="toctree-l2"><a class="reference internal" href="tracing.html">Tracing</a></li>
<li class="toctree-l2"><a class="reference internal" href="web_audio.html">WebAudio</a></li>
<li class="toctree-l2"><a class="reference internal" href="web_authn.html">WebAuthn</a></li>
</ul>
</li>
</ul>

</div>
</div>
      </div>
      
    </div>
  </aside>
  <div class="main">
    <div class="content">
      <div class="article-container">
        <a href="#" class="back-to-top muted-link">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
            <path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8v12z"></path>
          </svg>
          <span>Back to top</span>
        </a>
        <div class="content-icon-container">
          
<div class="theme-toggle-container theme-toggle-content">
            <button class="theme-toggle">
              <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
              <svg class="theme-icon-when-auto"><use href="#svg-sun-half"></use></svg>
              <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
              <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
            </button>
          </div>
          <label class="toc-overlay-icon toc-content-icon" for="__toc">
            <div class="visually-hidden">Toggle table of contents sidebar</div>
            <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
          </label>
        </div>
        <article role="main">
          <section id="overlay">
<h1>Overlay<a class="headerlink" href="#overlay" title="Link to this heading">#</a></h1>
<p>This domain provides various functionality related to drawing atop the inspected page.</p>
<p><em>This CDP domain is experimental.</em></p>
<ul class="simple" id="module-nodriver.cdp.overlay">
<li><p><a class="reference internal" href="#types">Types</a></p></li>
<li><p><a class="reference internal" href="#commands">Commands</a></p></li>
<li><p><a class="reference internal" href="#events">Events</a></p></li>
</ul>
<section id="types">
<h2>Types<a class="headerlink" href="#types" title="Link to this heading">#</a></h2>
<p>Generally, you do not need to instantiate CDP types
yourself. Instead, the API creates objects for you as return
values from commands, and then you can use those objects as
arguments to other commands.</p>
<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.SourceOrderConfig">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">SourceOrderConfig</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">parent_outline_color</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">child_outline_color</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/overlay.html#SourceOrderConfig"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.overlay.SourceOrderConfig" title="Link to this definition">#</a></dt>
<dd><p>Configuration data for drawing the source order of an elements children.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.SourceOrderConfig.parent_outline_color">
<span class="sig-name descname"><span class="pre">parent_outline_color</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="dom.html#nodriver.cdp.dom.RGBA" title="nodriver.cdp.dom.RGBA"><code class="xref py py-class docutils literal notranslate"><span class="pre">RGBA</span></code></a></em><a class="headerlink" href="#nodriver.cdp.overlay.SourceOrderConfig.parent_outline_color" title="Link to this definition">#</a></dt>
<dd><p>the color to outline the given element in.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.SourceOrderConfig.child_outline_color">
<span class="sig-name descname"><span class="pre">child_outline_color</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="dom.html#nodriver.cdp.dom.RGBA" title="nodriver.cdp.dom.RGBA"><code class="xref py py-class docutils literal notranslate"><span class="pre">RGBA</span></code></a></em><a class="headerlink" href="#nodriver.cdp.overlay.SourceOrderConfig.child_outline_color" title="Link to this definition">#</a></dt>
<dd><p>the color to outline the child elements in.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.GridHighlightConfig">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">GridHighlightConfig</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">show_grid_extension_lines</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">show_positive_line_numbers</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">show_negative_line_numbers</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">show_area_names</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">show_line_names</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">show_track_sizes</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">grid_border_color</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">cell_border_color</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">row_line_color</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">column_line_color</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">grid_border_dash</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">cell_border_dash</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">row_line_dash</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">column_line_dash</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">row_gap_color</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">row_hatch_color</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">column_gap_color</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">column_hatch_color</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">area_border_color</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">grid_background_color</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/overlay.html#GridHighlightConfig"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.overlay.GridHighlightConfig" title="Link to this definition">#</a></dt>
<dd><p>Configuration data for the highlighting of Grid elements.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.GridHighlightConfig.show_grid_extension_lines">
<span class="sig-name descname"><span class="pre">show_grid_extension_lines</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.GridHighlightConfig.show_grid_extension_lines" title="Link to this definition">#</a></dt>
<dd><p>false).</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>Whether the extension lines from grid cells to the rulers should be shown (default</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.GridHighlightConfig.show_positive_line_numbers">
<span class="sig-name descname"><span class="pre">show_positive_line_numbers</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.GridHighlightConfig.show_positive_line_numbers" title="Link to this definition">#</a></dt>
<dd><p>false).</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>Show Positive line number labels (default</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.GridHighlightConfig.show_negative_line_numbers">
<span class="sig-name descname"><span class="pre">show_negative_line_numbers</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.GridHighlightConfig.show_negative_line_numbers" title="Link to this definition">#</a></dt>
<dd><p>false).</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>Show Negative line number labels (default</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.GridHighlightConfig.show_area_names">
<span class="sig-name descname"><span class="pre">show_area_names</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.GridHighlightConfig.show_area_names" title="Link to this definition">#</a></dt>
<dd><p>false).</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>Show area name labels (default</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.GridHighlightConfig.show_line_names">
<span class="sig-name descname"><span class="pre">show_line_names</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.GridHighlightConfig.show_line_names" title="Link to this definition">#</a></dt>
<dd><p>false).</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>Show line name labels (default</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.GridHighlightConfig.show_track_sizes">
<span class="sig-name descname"><span class="pre">show_track_sizes</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.GridHighlightConfig.show_track_sizes" title="Link to this definition">#</a></dt>
<dd><p>false).</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>Show track size labels (default</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.GridHighlightConfig.grid_border_color">
<span class="sig-name descname"><span class="pre">grid_border_color</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="dom.html#nodriver.cdp.dom.RGBA" title="nodriver.cdp.dom.RGBA"><code class="xref py py-class docutils literal notranslate"><span class="pre">RGBA</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.GridHighlightConfig.grid_border_color" title="Link to this definition">#</a></dt>
<dd><p>transparent).</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>The grid container border highlight color (default</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.GridHighlightConfig.cell_border_color">
<span class="sig-name descname"><span class="pre">cell_border_color</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="dom.html#nodriver.cdp.dom.RGBA" title="nodriver.cdp.dom.RGBA"><code class="xref py py-class docutils literal notranslate"><span class="pre">RGBA</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.GridHighlightConfig.cell_border_color" title="Link to this definition">#</a></dt>
<dd><p>transparent). Deprecated, please use rowLineColor and columnLineColor instead.</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>The cell border color (default</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.GridHighlightConfig.row_line_color">
<span class="sig-name descname"><span class="pre">row_line_color</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="dom.html#nodriver.cdp.dom.RGBA" title="nodriver.cdp.dom.RGBA"><code class="xref py py-class docutils literal notranslate"><span class="pre">RGBA</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.GridHighlightConfig.row_line_color" title="Link to this definition">#</a></dt>
<dd><p>transparent).</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>The row line color (default</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.GridHighlightConfig.column_line_color">
<span class="sig-name descname"><span class="pre">column_line_color</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="dom.html#nodriver.cdp.dom.RGBA" title="nodriver.cdp.dom.RGBA"><code class="xref py py-class docutils literal notranslate"><span class="pre">RGBA</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.GridHighlightConfig.column_line_color" title="Link to this definition">#</a></dt>
<dd><p>transparent).</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>The column line color (default</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.GridHighlightConfig.grid_border_dash">
<span class="sig-name descname"><span class="pre">grid_border_dash</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.GridHighlightConfig.grid_border_dash" title="Link to this definition">#</a></dt>
<dd><p>false).</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>Whether the grid border is dashed (default</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.GridHighlightConfig.cell_border_dash">
<span class="sig-name descname"><span class="pre">cell_border_dash</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.GridHighlightConfig.cell_border_dash" title="Link to this definition">#</a></dt>
<dd><p>false). Deprecated, please us rowLineDash and columnLineDash instead.</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>Whether the cell border is dashed (default</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.GridHighlightConfig.row_line_dash">
<span class="sig-name descname"><span class="pre">row_line_dash</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.GridHighlightConfig.row_line_dash" title="Link to this definition">#</a></dt>
<dd><p>false).</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>Whether row lines are dashed (default</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.GridHighlightConfig.column_line_dash">
<span class="sig-name descname"><span class="pre">column_line_dash</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.GridHighlightConfig.column_line_dash" title="Link to this definition">#</a></dt>
<dd><p>false).</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>Whether column lines are dashed (default</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.GridHighlightConfig.row_gap_color">
<span class="sig-name descname"><span class="pre">row_gap_color</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="dom.html#nodriver.cdp.dom.RGBA" title="nodriver.cdp.dom.RGBA"><code class="xref py py-class docutils literal notranslate"><span class="pre">RGBA</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.GridHighlightConfig.row_gap_color" title="Link to this definition">#</a></dt>
<dd><p>transparent).</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>The row gap highlight fill color (default</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.GridHighlightConfig.row_hatch_color">
<span class="sig-name descname"><span class="pre">row_hatch_color</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="dom.html#nodriver.cdp.dom.RGBA" title="nodriver.cdp.dom.RGBA"><code class="xref py py-class docutils literal notranslate"><span class="pre">RGBA</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.GridHighlightConfig.row_hatch_color" title="Link to this definition">#</a></dt>
<dd><p>transparent).</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>The row gap hatching fill color (default</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.GridHighlightConfig.column_gap_color">
<span class="sig-name descname"><span class="pre">column_gap_color</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="dom.html#nodriver.cdp.dom.RGBA" title="nodriver.cdp.dom.RGBA"><code class="xref py py-class docutils literal notranslate"><span class="pre">RGBA</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.GridHighlightConfig.column_gap_color" title="Link to this definition">#</a></dt>
<dd><p>transparent).</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>The column gap highlight fill color (default</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.GridHighlightConfig.column_hatch_color">
<span class="sig-name descname"><span class="pre">column_hatch_color</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="dom.html#nodriver.cdp.dom.RGBA" title="nodriver.cdp.dom.RGBA"><code class="xref py py-class docutils literal notranslate"><span class="pre">RGBA</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.GridHighlightConfig.column_hatch_color" title="Link to this definition">#</a></dt>
<dd><p>transparent).</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>The column gap hatching fill color (default</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.GridHighlightConfig.area_border_color">
<span class="sig-name descname"><span class="pre">area_border_color</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="dom.html#nodriver.cdp.dom.RGBA" title="nodriver.cdp.dom.RGBA"><code class="xref py py-class docutils literal notranslate"><span class="pre">RGBA</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.GridHighlightConfig.area_border_color" title="Link to this definition">#</a></dt>
<dd><p>transparent).</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>The named grid areas border color (Default</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.GridHighlightConfig.grid_background_color">
<span class="sig-name descname"><span class="pre">grid_background_color</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="dom.html#nodriver.cdp.dom.RGBA" title="nodriver.cdp.dom.RGBA"><code class="xref py py-class docutils literal notranslate"><span class="pre">RGBA</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.GridHighlightConfig.grid_background_color" title="Link to this definition">#</a></dt>
<dd><p>transparent).</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>The grid container background color (Default</p>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.FlexContainerHighlightConfig">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">FlexContainerHighlightConfig</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">container_border</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">line_separator</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">item_separator</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">main_distributed_space</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">cross_distributed_space</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">row_gap_space</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">column_gap_space</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">cross_alignment</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/overlay.html#FlexContainerHighlightConfig"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.overlay.FlexContainerHighlightConfig" title="Link to this definition">#</a></dt>
<dd><p>Configuration data for the highlighting of Flex container elements.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.FlexContainerHighlightConfig.container_border">
<span class="sig-name descname"><span class="pre">container_border</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.overlay.LineStyle" title="nodriver.cdp.overlay.LineStyle"><code class="xref py py-class docutils literal notranslate"><span class="pre">LineStyle</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.FlexContainerHighlightConfig.container_border" title="Link to this definition">#</a></dt>
<dd><p>The style of the container border</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.FlexContainerHighlightConfig.line_separator">
<span class="sig-name descname"><span class="pre">line_separator</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.overlay.LineStyle" title="nodriver.cdp.overlay.LineStyle"><code class="xref py py-class docutils literal notranslate"><span class="pre">LineStyle</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.FlexContainerHighlightConfig.line_separator" title="Link to this definition">#</a></dt>
<dd><p>The style of the separator between lines</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.FlexContainerHighlightConfig.item_separator">
<span class="sig-name descname"><span class="pre">item_separator</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.overlay.LineStyle" title="nodriver.cdp.overlay.LineStyle"><code class="xref py py-class docutils literal notranslate"><span class="pre">LineStyle</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.FlexContainerHighlightConfig.item_separator" title="Link to this definition">#</a></dt>
<dd><p>The style of the separator between items</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.FlexContainerHighlightConfig.main_distributed_space">
<span class="sig-name descname"><span class="pre">main_distributed_space</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.overlay.BoxStyle" title="nodriver.cdp.overlay.BoxStyle"><code class="xref py py-class docutils literal notranslate"><span class="pre">BoxStyle</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.FlexContainerHighlightConfig.main_distributed_space" title="Link to this definition">#</a></dt>
<dd><p>Style of content-distribution space on the main axis (justify-content).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.FlexContainerHighlightConfig.cross_distributed_space">
<span class="sig-name descname"><span class="pre">cross_distributed_space</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.overlay.BoxStyle" title="nodriver.cdp.overlay.BoxStyle"><code class="xref py py-class docutils literal notranslate"><span class="pre">BoxStyle</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.FlexContainerHighlightConfig.cross_distributed_space" title="Link to this definition">#</a></dt>
<dd><p>Style of content-distribution space on the cross axis (align-content).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.FlexContainerHighlightConfig.row_gap_space">
<span class="sig-name descname"><span class="pre">row_gap_space</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.overlay.BoxStyle" title="nodriver.cdp.overlay.BoxStyle"><code class="xref py py-class docutils literal notranslate"><span class="pre">BoxStyle</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.FlexContainerHighlightConfig.row_gap_space" title="Link to this definition">#</a></dt>
<dd><p>Style of empty space caused by row gaps (gap/row-gap).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.FlexContainerHighlightConfig.column_gap_space">
<span class="sig-name descname"><span class="pre">column_gap_space</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.overlay.BoxStyle" title="nodriver.cdp.overlay.BoxStyle"><code class="xref py py-class docutils literal notranslate"><span class="pre">BoxStyle</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.FlexContainerHighlightConfig.column_gap_space" title="Link to this definition">#</a></dt>
<dd><p>Style of empty space caused by columns gaps (gap/column-gap).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.FlexContainerHighlightConfig.cross_alignment">
<span class="sig-name descname"><span class="pre">cross_alignment</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.overlay.LineStyle" title="nodriver.cdp.overlay.LineStyle"><code class="xref py py-class docutils literal notranslate"><span class="pre">LineStyle</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.FlexContainerHighlightConfig.cross_alignment" title="Link to this definition">#</a></dt>
<dd><p>Style of the self-alignment line (align-items).</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.FlexItemHighlightConfig">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">FlexItemHighlightConfig</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">base_size_box</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">base_size_border</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">flexibility_arrow</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/overlay.html#FlexItemHighlightConfig"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.overlay.FlexItemHighlightConfig" title="Link to this definition">#</a></dt>
<dd><p>Configuration data for the highlighting of Flex item elements.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.FlexItemHighlightConfig.base_size_box">
<span class="sig-name descname"><span class="pre">base_size_box</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.overlay.BoxStyle" title="nodriver.cdp.overlay.BoxStyle"><code class="xref py py-class docutils literal notranslate"><span class="pre">BoxStyle</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.FlexItemHighlightConfig.base_size_box" title="Link to this definition">#</a></dt>
<dd><p>Style of the box representing the item’s base size</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.FlexItemHighlightConfig.base_size_border">
<span class="sig-name descname"><span class="pre">base_size_border</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.overlay.LineStyle" title="nodriver.cdp.overlay.LineStyle"><code class="xref py py-class docutils literal notranslate"><span class="pre">LineStyle</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.FlexItemHighlightConfig.base_size_border" title="Link to this definition">#</a></dt>
<dd><p>Style of the border around the box representing the item’s base size</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.FlexItemHighlightConfig.flexibility_arrow">
<span class="sig-name descname"><span class="pre">flexibility_arrow</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.overlay.LineStyle" title="nodriver.cdp.overlay.LineStyle"><code class="xref py py-class docutils literal notranslate"><span class="pre">LineStyle</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.FlexItemHighlightConfig.flexibility_arrow" title="Link to this definition">#</a></dt>
<dd><p>Style of the arrow representing if the item grew or shrank</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.LineStyle">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">LineStyle</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">color</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pattern</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/overlay.html#LineStyle"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.overlay.LineStyle" title="Link to this definition">#</a></dt>
<dd><p>Style information for drawing a line.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.LineStyle.color">
<span class="sig-name descname"><span class="pre">color</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="dom.html#nodriver.cdp.dom.RGBA" title="nodriver.cdp.dom.RGBA"><code class="xref py py-class docutils literal notranslate"><span class="pre">RGBA</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.LineStyle.color" title="Link to this definition">#</a></dt>
<dd><p>transparent)</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>The color of the line (default</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.LineStyle.pattern">
<span class="sig-name descname"><span class="pre">pattern</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.LineStyle.pattern" title="Link to this definition">#</a></dt>
<dd><p>solid)</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>The line pattern (default</p>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.BoxStyle">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">BoxStyle</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fill_color</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">hatch_color</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/overlay.html#BoxStyle"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.overlay.BoxStyle" title="Link to this definition">#</a></dt>
<dd><p>Style information for drawing a box.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.BoxStyle.fill_color">
<span class="sig-name descname"><span class="pre">fill_color</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="dom.html#nodriver.cdp.dom.RGBA" title="nodriver.cdp.dom.RGBA"><code class="xref py py-class docutils literal notranslate"><span class="pre">RGBA</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.BoxStyle.fill_color" title="Link to this definition">#</a></dt>
<dd><p>transparent)</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>The background color for the box (default</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.BoxStyle.hatch_color">
<span class="sig-name descname"><span class="pre">hatch_color</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="dom.html#nodriver.cdp.dom.RGBA" title="nodriver.cdp.dom.RGBA"><code class="xref py py-class docutils literal notranslate"><span class="pre">RGBA</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.BoxStyle.hatch_color" title="Link to this definition">#</a></dt>
<dd><p>transparent)</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>The hatching color for the box (default</p>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.ContrastAlgorithm">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">ContrastAlgorithm</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">names</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">module</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">qualname</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">boundary</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/overlay.html#ContrastAlgorithm"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.overlay.ContrastAlgorithm" title="Link to this definition">#</a></dt>
<dd><dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.ContrastAlgorithm.AA">
<span class="sig-name descname"><span class="pre">AA</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'aa'</span></em><a class="headerlink" href="#nodriver.cdp.overlay.ContrastAlgorithm.AA" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.ContrastAlgorithm.AAA">
<span class="sig-name descname"><span class="pre">AAA</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'aaa'</span></em><a class="headerlink" href="#nodriver.cdp.overlay.ContrastAlgorithm.AAA" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.ContrastAlgorithm.APCA">
<span class="sig-name descname"><span class="pre">APCA</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'apca'</span></em><a class="headerlink" href="#nodriver.cdp.overlay.ContrastAlgorithm.APCA" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.HighlightConfig">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">HighlightConfig</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">show_info</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">show_styles</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">show_rulers</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">show_accessibility_info</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">show_extension_lines</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">content_color</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">padding_color</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">border_color</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">margin_color</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">event_target_color</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">shape_color</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">shape_margin_color</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">css_grid_color</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">color_format</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">grid_highlight_config</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">flex_container_highlight_config</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">flex_item_highlight_config</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">contrast_algorithm</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">container_query_container_highlight_config</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/overlay.html#HighlightConfig"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.overlay.HighlightConfig" title="Link to this definition">#</a></dt>
<dd><p>Configuration data for the highlighting of page elements.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.HighlightConfig.show_info">
<span class="sig-name descname"><span class="pre">show_info</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.HighlightConfig.show_info" title="Link to this definition">#</a></dt>
<dd><p>false).</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>Whether the node info tooltip should be shown (default</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.HighlightConfig.show_styles">
<span class="sig-name descname"><span class="pre">show_styles</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.HighlightConfig.show_styles" title="Link to this definition">#</a></dt>
<dd><p>false).</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>Whether the node styles in the tooltip (default</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.HighlightConfig.show_rulers">
<span class="sig-name descname"><span class="pre">show_rulers</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.HighlightConfig.show_rulers" title="Link to this definition">#</a></dt>
<dd><p>false).</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>Whether the rulers should be shown (default</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.HighlightConfig.show_accessibility_info">
<span class="sig-name descname"><span class="pre">show_accessibility_info</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.HighlightConfig.show_accessibility_info" title="Link to this definition">#</a></dt>
<dd><p>true).</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>Whether the a11y info should be shown (default</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.HighlightConfig.show_extension_lines">
<span class="sig-name descname"><span class="pre">show_extension_lines</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.HighlightConfig.show_extension_lines" title="Link to this definition">#</a></dt>
<dd><p>false).</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>Whether the extension lines from node to the rulers should be shown (default</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.HighlightConfig.content_color">
<span class="sig-name descname"><span class="pre">content_color</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="dom.html#nodriver.cdp.dom.RGBA" title="nodriver.cdp.dom.RGBA"><code class="xref py py-class docutils literal notranslate"><span class="pre">RGBA</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.HighlightConfig.content_color" title="Link to this definition">#</a></dt>
<dd><p>transparent).</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>The content box highlight fill color (default</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.HighlightConfig.padding_color">
<span class="sig-name descname"><span class="pre">padding_color</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="dom.html#nodriver.cdp.dom.RGBA" title="nodriver.cdp.dom.RGBA"><code class="xref py py-class docutils literal notranslate"><span class="pre">RGBA</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.HighlightConfig.padding_color" title="Link to this definition">#</a></dt>
<dd><p>transparent).</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>The padding highlight fill color (default</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.HighlightConfig.border_color">
<span class="sig-name descname"><span class="pre">border_color</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="dom.html#nodriver.cdp.dom.RGBA" title="nodriver.cdp.dom.RGBA"><code class="xref py py-class docutils literal notranslate"><span class="pre">RGBA</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.HighlightConfig.border_color" title="Link to this definition">#</a></dt>
<dd><p>transparent).</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>The border highlight fill color (default</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.HighlightConfig.margin_color">
<span class="sig-name descname"><span class="pre">margin_color</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="dom.html#nodriver.cdp.dom.RGBA" title="nodriver.cdp.dom.RGBA"><code class="xref py py-class docutils literal notranslate"><span class="pre">RGBA</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.HighlightConfig.margin_color" title="Link to this definition">#</a></dt>
<dd><p>transparent).</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>The margin highlight fill color (default</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.HighlightConfig.event_target_color">
<span class="sig-name descname"><span class="pre">event_target_color</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="dom.html#nodriver.cdp.dom.RGBA" title="nodriver.cdp.dom.RGBA"><code class="xref py py-class docutils literal notranslate"><span class="pre">RGBA</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.HighlightConfig.event_target_color" title="Link to this definition">#</a></dt>
<dd><p>transparent).</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>The event target element highlight fill color (default</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.HighlightConfig.shape_color">
<span class="sig-name descname"><span class="pre">shape_color</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="dom.html#nodriver.cdp.dom.RGBA" title="nodriver.cdp.dom.RGBA"><code class="xref py py-class docutils literal notranslate"><span class="pre">RGBA</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.HighlightConfig.shape_color" title="Link to this definition">#</a></dt>
<dd><p>transparent).</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>The shape outside fill color (default</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.HighlightConfig.shape_margin_color">
<span class="sig-name descname"><span class="pre">shape_margin_color</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="dom.html#nodriver.cdp.dom.RGBA" title="nodriver.cdp.dom.RGBA"><code class="xref py py-class docutils literal notranslate"><span class="pre">RGBA</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.HighlightConfig.shape_margin_color" title="Link to this definition">#</a></dt>
<dd><p>transparent).</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>The shape margin fill color (default</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.HighlightConfig.css_grid_color">
<span class="sig-name descname"><span class="pre">css_grid_color</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="dom.html#nodriver.cdp.dom.RGBA" title="nodriver.cdp.dom.RGBA"><code class="xref py py-class docutils literal notranslate"><span class="pre">RGBA</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.HighlightConfig.css_grid_color" title="Link to this definition">#</a></dt>
<dd><p>transparent).</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>The grid layout color (default</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.HighlightConfig.color_format">
<span class="sig-name descname"><span class="pre">color_format</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.overlay.ColorFormat" title="nodriver.cdp.overlay.ColorFormat"><code class="xref py py-class docutils literal notranslate"><span class="pre">ColorFormat</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.HighlightConfig.color_format" title="Link to this definition">#</a></dt>
<dd><p>hex).</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>The color format used to format color styles (default</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.HighlightConfig.grid_highlight_config">
<span class="sig-name descname"><span class="pre">grid_highlight_config</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.overlay.GridHighlightConfig" title="nodriver.cdp.overlay.GridHighlightConfig"><code class="xref py py-class docutils literal notranslate"><span class="pre">GridHighlightConfig</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.HighlightConfig.grid_highlight_config" title="Link to this definition">#</a></dt>
<dd><p>all transparent).</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>The grid layout highlight configuration (default</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.HighlightConfig.flex_container_highlight_config">
<span class="sig-name descname"><span class="pre">flex_container_highlight_config</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.overlay.FlexContainerHighlightConfig" title="nodriver.cdp.overlay.FlexContainerHighlightConfig"><code class="xref py py-class docutils literal notranslate"><span class="pre">FlexContainerHighlightConfig</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.HighlightConfig.flex_container_highlight_config" title="Link to this definition">#</a></dt>
<dd><p>all transparent).</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>The flex container highlight configuration (default</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.HighlightConfig.flex_item_highlight_config">
<span class="sig-name descname"><span class="pre">flex_item_highlight_config</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.overlay.FlexItemHighlightConfig" title="nodriver.cdp.overlay.FlexItemHighlightConfig"><code class="xref py py-class docutils literal notranslate"><span class="pre">FlexItemHighlightConfig</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.HighlightConfig.flex_item_highlight_config" title="Link to this definition">#</a></dt>
<dd><p>all transparent).</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>The flex item highlight configuration (default</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.HighlightConfig.contrast_algorithm">
<span class="sig-name descname"><span class="pre">contrast_algorithm</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.overlay.ContrastAlgorithm" title="nodriver.cdp.overlay.ContrastAlgorithm"><code class="xref py py-class docutils literal notranslate"><span class="pre">ContrastAlgorithm</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.HighlightConfig.contrast_algorithm" title="Link to this definition">#</a></dt>
<dd><p>aa).</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>The contrast algorithm to use for the contrast ratio (default</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.HighlightConfig.container_query_container_highlight_config">
<span class="sig-name descname"><span class="pre">container_query_container_highlight_config</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.overlay.ContainerQueryContainerHighlightConfig" title="nodriver.cdp.overlay.ContainerQueryContainerHighlightConfig"><code class="xref py py-class docutils literal notranslate"><span class="pre">ContainerQueryContainerHighlightConfig</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.HighlightConfig.container_query_container_highlight_config" title="Link to this definition">#</a></dt>
<dd><p>all transparent).</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>The container query container highlight configuration (default</p>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.ColorFormat">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">ColorFormat</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">names</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">module</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">qualname</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">boundary</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/overlay.html#ColorFormat"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.overlay.ColorFormat" title="Link to this definition">#</a></dt>
<dd><dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.ColorFormat.RGB">
<span class="sig-name descname"><span class="pre">RGB</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'rgb'</span></em><a class="headerlink" href="#nodriver.cdp.overlay.ColorFormat.RGB" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.ColorFormat.HSL">
<span class="sig-name descname"><span class="pre">HSL</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'hsl'</span></em><a class="headerlink" href="#nodriver.cdp.overlay.ColorFormat.HSL" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.ColorFormat.HWB">
<span class="sig-name descname"><span class="pre">HWB</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'hwb'</span></em><a class="headerlink" href="#nodriver.cdp.overlay.ColorFormat.HWB" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.ColorFormat.HEX_">
<span class="sig-name descname"><span class="pre">HEX_</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'hex'</span></em><a class="headerlink" href="#nodriver.cdp.overlay.ColorFormat.HEX_" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.GridNodeHighlightConfig">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">GridNodeHighlightConfig</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">grid_highlight_config</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">node_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/overlay.html#GridNodeHighlightConfig"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.overlay.GridNodeHighlightConfig" title="Link to this definition">#</a></dt>
<dd><p>Configurations for Persistent Grid Highlight</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.GridNodeHighlightConfig.grid_highlight_config">
<span class="sig-name descname"><span class="pre">grid_highlight_config</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.overlay.GridHighlightConfig" title="nodriver.cdp.overlay.GridHighlightConfig"><code class="xref py py-class docutils literal notranslate"><span class="pre">GridHighlightConfig</span></code></a></em><a class="headerlink" href="#nodriver.cdp.overlay.GridNodeHighlightConfig.grid_highlight_config" title="Link to this definition">#</a></dt>
<dd><p>A descriptor for the highlight appearance.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.GridNodeHighlightConfig.node_id">
<span class="sig-name descname"><span class="pre">node_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="dom.html#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a></em><a class="headerlink" href="#nodriver.cdp.overlay.GridNodeHighlightConfig.node_id" title="Link to this definition">#</a></dt>
<dd><p>Identifier of the node to highlight.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.FlexNodeHighlightConfig">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">FlexNodeHighlightConfig</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">flex_container_highlight_config</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">node_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/overlay.html#FlexNodeHighlightConfig"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.overlay.FlexNodeHighlightConfig" title="Link to this definition">#</a></dt>
<dd><dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.FlexNodeHighlightConfig.flex_container_highlight_config">
<span class="sig-name descname"><span class="pre">flex_container_highlight_config</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.overlay.FlexContainerHighlightConfig" title="nodriver.cdp.overlay.FlexContainerHighlightConfig"><code class="xref py py-class docutils literal notranslate"><span class="pre">FlexContainerHighlightConfig</span></code></a></em><a class="headerlink" href="#nodriver.cdp.overlay.FlexNodeHighlightConfig.flex_container_highlight_config" title="Link to this definition">#</a></dt>
<dd><p>A descriptor for the highlight appearance of flex containers.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.FlexNodeHighlightConfig.node_id">
<span class="sig-name descname"><span class="pre">node_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="dom.html#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a></em><a class="headerlink" href="#nodriver.cdp.overlay.FlexNodeHighlightConfig.node_id" title="Link to this definition">#</a></dt>
<dd><p>Identifier of the node to highlight.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.ScrollSnapContainerHighlightConfig">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">ScrollSnapContainerHighlightConfig</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">snapport_border</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">snap_area_border</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">scroll_margin_color</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">scroll_padding_color</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/overlay.html#ScrollSnapContainerHighlightConfig"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.overlay.ScrollSnapContainerHighlightConfig" title="Link to this definition">#</a></dt>
<dd><dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.ScrollSnapContainerHighlightConfig.snapport_border">
<span class="sig-name descname"><span class="pre">snapport_border</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.overlay.LineStyle" title="nodriver.cdp.overlay.LineStyle"><code class="xref py py-class docutils literal notranslate"><span class="pre">LineStyle</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.ScrollSnapContainerHighlightConfig.snapport_border" title="Link to this definition">#</a></dt>
<dd><p>transparent)</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>The style of the snapport border (default</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.ScrollSnapContainerHighlightConfig.snap_area_border">
<span class="sig-name descname"><span class="pre">snap_area_border</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.overlay.LineStyle" title="nodriver.cdp.overlay.LineStyle"><code class="xref py py-class docutils literal notranslate"><span class="pre">LineStyle</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.ScrollSnapContainerHighlightConfig.snap_area_border" title="Link to this definition">#</a></dt>
<dd><p>transparent)</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>The style of the snap area border (default</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.ScrollSnapContainerHighlightConfig.scroll_margin_color">
<span class="sig-name descname"><span class="pre">scroll_margin_color</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="dom.html#nodriver.cdp.dom.RGBA" title="nodriver.cdp.dom.RGBA"><code class="xref py py-class docutils literal notranslate"><span class="pre">RGBA</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.ScrollSnapContainerHighlightConfig.scroll_margin_color" title="Link to this definition">#</a></dt>
<dd><p>transparent).</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>The margin highlight fill color (default</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.ScrollSnapContainerHighlightConfig.scroll_padding_color">
<span class="sig-name descname"><span class="pre">scroll_padding_color</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="dom.html#nodriver.cdp.dom.RGBA" title="nodriver.cdp.dom.RGBA"><code class="xref py py-class docutils literal notranslate"><span class="pre">RGBA</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.ScrollSnapContainerHighlightConfig.scroll_padding_color" title="Link to this definition">#</a></dt>
<dd><p>transparent).</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>The padding highlight fill color (default</p>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.ScrollSnapHighlightConfig">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">ScrollSnapHighlightConfig</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">scroll_snap_container_highlight_config</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">node_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/overlay.html#ScrollSnapHighlightConfig"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.overlay.ScrollSnapHighlightConfig" title="Link to this definition">#</a></dt>
<dd><dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.ScrollSnapHighlightConfig.scroll_snap_container_highlight_config">
<span class="sig-name descname"><span class="pre">scroll_snap_container_highlight_config</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.overlay.ScrollSnapContainerHighlightConfig" title="nodriver.cdp.overlay.ScrollSnapContainerHighlightConfig"><code class="xref py py-class docutils literal notranslate"><span class="pre">ScrollSnapContainerHighlightConfig</span></code></a></em><a class="headerlink" href="#nodriver.cdp.overlay.ScrollSnapHighlightConfig.scroll_snap_container_highlight_config" title="Link to this definition">#</a></dt>
<dd><p>A descriptor for the highlight appearance of scroll snap containers.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.ScrollSnapHighlightConfig.node_id">
<span class="sig-name descname"><span class="pre">node_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="dom.html#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a></em><a class="headerlink" href="#nodriver.cdp.overlay.ScrollSnapHighlightConfig.node_id" title="Link to this definition">#</a></dt>
<dd><p>Identifier of the node to highlight.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.HingeConfig">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">HingeConfig</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">rect</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">content_color</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">outline_color</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/overlay.html#HingeConfig"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.overlay.HingeConfig" title="Link to this definition">#</a></dt>
<dd><p>Configuration for dual screen hinge</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.HingeConfig.rect">
<span class="sig-name descname"><span class="pre">rect</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="dom.html#nodriver.cdp.dom.Rect" title="nodriver.cdp.dom.Rect"><code class="xref py py-class docutils literal notranslate"><span class="pre">Rect</span></code></a></em><a class="headerlink" href="#nodriver.cdp.overlay.HingeConfig.rect" title="Link to this definition">#</a></dt>
<dd><p>A rectangle represent hinge</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.HingeConfig.content_color">
<span class="sig-name descname"><span class="pre">content_color</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="dom.html#nodriver.cdp.dom.RGBA" title="nodriver.cdp.dom.RGBA"><code class="xref py py-class docutils literal notranslate"><span class="pre">RGBA</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.HingeConfig.content_color" title="Link to this definition">#</a></dt>
<dd><p>a dark color).</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>The content box highlight fill color (default</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.HingeConfig.outline_color">
<span class="sig-name descname"><span class="pre">outline_color</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="dom.html#nodriver.cdp.dom.RGBA" title="nodriver.cdp.dom.RGBA"><code class="xref py py-class docutils literal notranslate"><span class="pre">RGBA</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.HingeConfig.outline_color" title="Link to this definition">#</a></dt>
<dd><p>transparent).</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>The content box highlight outline color (default</p>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.WindowControlsOverlayConfig">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">WindowControlsOverlayConfig</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">show_css</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">selected_platform</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">theme_color</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/overlay.html#WindowControlsOverlayConfig"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.overlay.WindowControlsOverlayConfig" title="Link to this definition">#</a></dt>
<dd><p>Configuration for Window Controls Overlay</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.WindowControlsOverlayConfig.show_css">
<span class="sig-name descname"><span class="pre">show_css</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a></em><a class="headerlink" href="#nodriver.cdp.overlay.WindowControlsOverlayConfig.show_css" title="Link to this definition">#</a></dt>
<dd><p>Whether the title bar CSS should be shown when emulating the Window Controls Overlay.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.WindowControlsOverlayConfig.selected_platform">
<span class="sig-name descname"><span class="pre">selected_platform</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.overlay.WindowControlsOverlayConfig.selected_platform" title="Link to this definition">#</a></dt>
<dd><p>Selected platforms to show the overlay.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.WindowControlsOverlayConfig.theme_color">
<span class="sig-name descname"><span class="pre">theme_color</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.overlay.WindowControlsOverlayConfig.theme_color" title="Link to this definition">#</a></dt>
<dd><p>The theme color defined in app manifest.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.ContainerQueryHighlightConfig">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">ContainerQueryHighlightConfig</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">container_query_container_highlight_config</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">node_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/overlay.html#ContainerQueryHighlightConfig"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.overlay.ContainerQueryHighlightConfig" title="Link to this definition">#</a></dt>
<dd><dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.ContainerQueryHighlightConfig.container_query_container_highlight_config">
<span class="sig-name descname"><span class="pre">container_query_container_highlight_config</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.overlay.ContainerQueryContainerHighlightConfig" title="nodriver.cdp.overlay.ContainerQueryContainerHighlightConfig"><code class="xref py py-class docutils literal notranslate"><span class="pre">ContainerQueryContainerHighlightConfig</span></code></a></em><a class="headerlink" href="#nodriver.cdp.overlay.ContainerQueryHighlightConfig.container_query_container_highlight_config" title="Link to this definition">#</a></dt>
<dd><p>A descriptor for the highlight appearance of container query containers.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.ContainerQueryHighlightConfig.node_id">
<span class="sig-name descname"><span class="pre">node_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="dom.html#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a></em><a class="headerlink" href="#nodriver.cdp.overlay.ContainerQueryHighlightConfig.node_id" title="Link to this definition">#</a></dt>
<dd><p>Identifier of the container node to highlight.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.ContainerQueryContainerHighlightConfig">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">ContainerQueryContainerHighlightConfig</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">container_border</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">descendant_border</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/overlay.html#ContainerQueryContainerHighlightConfig"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.overlay.ContainerQueryContainerHighlightConfig" title="Link to this definition">#</a></dt>
<dd><dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.ContainerQueryContainerHighlightConfig.container_border">
<span class="sig-name descname"><span class="pre">container_border</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.overlay.LineStyle" title="nodriver.cdp.overlay.LineStyle"><code class="xref py py-class docutils literal notranslate"><span class="pre">LineStyle</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.ContainerQueryContainerHighlightConfig.container_border" title="Link to this definition">#</a></dt>
<dd><p>The style of the container border.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.ContainerQueryContainerHighlightConfig.descendant_border">
<span class="sig-name descname"><span class="pre">descendant_border</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.overlay.LineStyle" title="nodriver.cdp.overlay.LineStyle"><code class="xref py py-class docutils literal notranslate"><span class="pre">LineStyle</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.ContainerQueryContainerHighlightConfig.descendant_border" title="Link to this definition">#</a></dt>
<dd><p>The style of the descendants’ borders.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.IsolatedElementHighlightConfig">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">IsolatedElementHighlightConfig</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">isolation_mode_highlight_config</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">node_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/overlay.html#IsolatedElementHighlightConfig"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.overlay.IsolatedElementHighlightConfig" title="Link to this definition">#</a></dt>
<dd><dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.IsolatedElementHighlightConfig.isolation_mode_highlight_config">
<span class="sig-name descname"><span class="pre">isolation_mode_highlight_config</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.overlay.IsolationModeHighlightConfig" title="nodriver.cdp.overlay.IsolationModeHighlightConfig"><code class="xref py py-class docutils literal notranslate"><span class="pre">IsolationModeHighlightConfig</span></code></a></em><a class="headerlink" href="#nodriver.cdp.overlay.IsolatedElementHighlightConfig.isolation_mode_highlight_config" title="Link to this definition">#</a></dt>
<dd><p>A descriptor for the highlight appearance of an element in isolation mode.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.IsolatedElementHighlightConfig.node_id">
<span class="sig-name descname"><span class="pre">node_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="dom.html#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a></em><a class="headerlink" href="#nodriver.cdp.overlay.IsolatedElementHighlightConfig.node_id" title="Link to this definition">#</a></dt>
<dd><p>Identifier of the isolated element to highlight.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.IsolationModeHighlightConfig">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">IsolationModeHighlightConfig</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">resizer_color</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">resizer_handle_color</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">mask_color</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/overlay.html#IsolationModeHighlightConfig"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.overlay.IsolationModeHighlightConfig" title="Link to this definition">#</a></dt>
<dd><dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.IsolationModeHighlightConfig.resizer_color">
<span class="sig-name descname"><span class="pre">resizer_color</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="dom.html#nodriver.cdp.dom.RGBA" title="nodriver.cdp.dom.RGBA"><code class="xref py py-class docutils literal notranslate"><span class="pre">RGBA</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.IsolationModeHighlightConfig.resizer_color" title="Link to this definition">#</a></dt>
<dd><p>transparent).</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>The fill color of the resizers (default</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.IsolationModeHighlightConfig.resizer_handle_color">
<span class="sig-name descname"><span class="pre">resizer_handle_color</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="dom.html#nodriver.cdp.dom.RGBA" title="nodriver.cdp.dom.RGBA"><code class="xref py py-class docutils literal notranslate"><span class="pre">RGBA</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.IsolationModeHighlightConfig.resizer_handle_color" title="Link to this definition">#</a></dt>
<dd><p>transparent).</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>The fill color for resizer handles (default</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.IsolationModeHighlightConfig.mask_color">
<span class="sig-name descname"><span class="pre">mask_color</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="dom.html#nodriver.cdp.dom.RGBA" title="nodriver.cdp.dom.RGBA"><code class="xref py py-class docutils literal notranslate"><span class="pre">RGBA</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.overlay.IsolationModeHighlightConfig.mask_color" title="Link to this definition">#</a></dt>
<dd><p>transparent).</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>The fill color for the mask covering non-isolated elements (default</p>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.InspectMode">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">InspectMode</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">names</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">module</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">qualname</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">boundary</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/overlay.html#InspectMode"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.overlay.InspectMode" title="Link to this definition">#</a></dt>
<dd><dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.InspectMode.SEARCH_FOR_NODE">
<span class="sig-name descname"><span class="pre">SEARCH_FOR_NODE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'searchForNode'</span></em><a class="headerlink" href="#nodriver.cdp.overlay.InspectMode.SEARCH_FOR_NODE" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.InspectMode.SEARCH_FOR_UA_SHADOW_DOM">
<span class="sig-name descname"><span class="pre">SEARCH_FOR_UA_SHADOW_DOM</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'searchForUAShadowDOM'</span></em><a class="headerlink" href="#nodriver.cdp.overlay.InspectMode.SEARCH_FOR_UA_SHADOW_DOM" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.InspectMode.CAPTURE_AREA_SCREENSHOT">
<span class="sig-name descname"><span class="pre">CAPTURE_AREA_SCREENSHOT</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'captureAreaScreenshot'</span></em><a class="headerlink" href="#nodriver.cdp.overlay.InspectMode.CAPTURE_AREA_SCREENSHOT" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.InspectMode.SHOW_DISTANCES">
<span class="sig-name descname"><span class="pre">SHOW_DISTANCES</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'showDistances'</span></em><a class="headerlink" href="#nodriver.cdp.overlay.InspectMode.SHOW_DISTANCES" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.InspectMode.NONE">
<span class="sig-name descname"><span class="pre">NONE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'none'</span></em><a class="headerlink" href="#nodriver.cdp.overlay.InspectMode.NONE" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

</section>
<section id="commands">
<h2>Commands<a class="headerlink" href="#commands" title="Link to this heading">#</a></h2>
<p>Each command is a generator function. The return
type <code class="docutils literal notranslate"><span class="pre">Generator[x,</span> <span class="pre">y,</span> <span class="pre">z]</span></code> indicates that the generator
<em>yields</em> arguments of type <code class="docutils literal notranslate"><span class="pre">x</span></code>, it must be resumed with
an argument of type <code class="docutils literal notranslate"><span class="pre">y</span></code>, and it returns type <code class="docutils literal notranslate"><span class="pre">z</span></code>. In
this library, types <code class="docutils literal notranslate"><span class="pre">x</span></code> and <code class="docutils literal notranslate"><span class="pre">y</span></code> are the same for all
commands, and <code class="docutils literal notranslate"><span class="pre">z</span></code> is the return type you should pay attention
to. For more information, see
<a class="reference internal" href="../../readme.html#getting-started-commands"><span class="std std-ref">Getting Started: Commands</span></a>.</p>
<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.disable">
<span class="sig-name descname"><span class="pre">disable</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/overlay.html#disable"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.overlay.disable" title="Link to this definition">#</a></dt>
<dd><p>Disables domain notifications.</p>
<dl class="field-list simple">
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.enable">
<span class="sig-name descname"><span class="pre">enable</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/overlay.html#enable"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.overlay.enable" title="Link to this definition">#</a></dt>
<dd><p>Enables domain notifications.</p>
<dl class="field-list simple">
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.get_grid_highlight_objects_for_test">
<span class="sig-name descname"><span class="pre">get_grid_highlight_objects_for_test</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">node_ids</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/overlay.html#get_grid_highlight_objects_for_test"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.overlay.get_grid_highlight_objects_for_test" title="Link to this definition">#</a></dt>
<dd><p>For Persistent Grid testing.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>node_ids</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="dom.html#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a>]</span>) – Ids of the node to get highlight object for.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">dict</span></code></a>]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>Grid Highlight data for the node ids provided.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.get_highlight_object_for_test">
<span class="sig-name descname"><span class="pre">get_highlight_object_for_test</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">node_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">include_distance</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">include_style</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">color_format</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">show_accessibility_info</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/overlay.html#get_highlight_object_for_test"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.overlay.get_highlight_object_for_test" title="Link to this definition">#</a></dt>
<dd><p>For testing.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="dom.html#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a></span>) – Id of the node to get highlight object for.</p></li>
<li><p><strong>include_distance</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <em>(Optional)</em> Whether to include distance info.</p></li>
<li><p><strong>include_style</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <em>(Optional)</em> Whether to include style info.</p></li>
<li><p><strong>color_format</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="#nodriver.cdp.overlay.ColorFormat" title="nodriver.cdp.overlay.ColorFormat"><code class="xref py py-class docutils literal notranslate"><span class="pre">ColorFormat</span></code></a>]</span>) – <em>(Optional)</em> The color format to get config with (default: hex).</p></li>
<li><p><strong>show_accessibility_info</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <em>(Optional)</em> Whether to show accessibility info (default: true).</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">dict</span></code></a>]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>Highlight data for the node.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.get_source_order_highlight_object_for_test">
<span class="sig-name descname"><span class="pre">get_source_order_highlight_object_for_test</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">node_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/overlay.html#get_source_order_highlight_object_for_test"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.overlay.get_source_order_highlight_object_for_test" title="Link to this definition">#</a></dt>
<dd><p>For Source Order Viewer testing.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="dom.html#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a></span>) – Id of the node to highlight.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">dict</span></code></a>]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>Source order highlight data for the node id provided.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.hide_highlight">
<span class="sig-name descname"><span class="pre">hide_highlight</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/overlay.html#hide_highlight"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.overlay.hide_highlight" title="Link to this definition">#</a></dt>
<dd><p>Hides any highlight.</p>
<dl class="field-list simple">
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.highlight_frame">
<span class="sig-name descname"><span class="pre">highlight_frame</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">frame_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">content_color</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">content_outline_color</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/overlay.html#highlight_frame"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.overlay.highlight_frame" title="Link to this definition">#</a></dt>
<dd><p>Highlights owner element of the frame with given id.
Deprecated: Doesn’t work reliably and cannot be fixed due to process
separation (the owner node might be in a different process). Determine
the owner node in the client and use highlightNode.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 1.3.</span></p>
</div>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>frame_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="page.html#nodriver.cdp.page.FrameId" title="nodriver.cdp.page.FrameId"><code class="xref py py-class docutils literal notranslate"><span class="pre">FrameId</span></code></a></span>) – Identifier of the frame to highlight.</p></li>
<li><p><strong>content_color</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="dom.html#nodriver.cdp.dom.RGBA" title="nodriver.cdp.dom.RGBA"><code class="xref py py-class docutils literal notranslate"><span class="pre">RGBA</span></code></a>]</span>) – <em>(Optional)</em> The content box highlight fill color (default: transparent).</p></li>
<li><p><strong>content_outline_color</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="dom.html#nodriver.cdp.dom.RGBA" title="nodriver.cdp.dom.RGBA"><code class="xref py py-class docutils literal notranslate"><span class="pre">RGBA</span></code></a>]</span>) – <em>(Optional)</em> The content box highlight outline color (default: transparent).</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 1.3.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.highlight_node">
<span class="sig-name descname"><span class="pre">highlight_node</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">highlight_config</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">node_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">backend_node_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">object_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">selector</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/overlay.html#highlight_node"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.overlay.highlight_node" title="Link to this definition">#</a></dt>
<dd><p>Highlights DOM node with given id or with the given JavaScript object wrapper. Either nodeId or
objectId must be specified.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>highlight_config</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.overlay.HighlightConfig" title="nodriver.cdp.overlay.HighlightConfig"><code class="xref py py-class docutils literal notranslate"><span class="pre">HighlightConfig</span></code></a></span>) – A descriptor for the highlight appearance.</p></li>
<li><p><strong>node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="dom.html#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a>]</span>) – <em>(Optional)</em> Identifier of the node to highlight.</p></li>
<li><p><strong>backend_node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="dom.html#nodriver.cdp.dom.BackendNodeId" title="nodriver.cdp.dom.BackendNodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">BackendNodeId</span></code></a>]</span>) – <em>(Optional)</em> Identifier of the backend node to highlight.</p></li>
<li><p><strong>object_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="runtime.html#nodriver.cdp.runtime.RemoteObjectId" title="nodriver.cdp.runtime.RemoteObjectId"><code class="xref py py-class docutils literal notranslate"><span class="pre">RemoteObjectId</span></code></a>]</span>) – <em>(Optional)</em> JavaScript object id of the node to be highlighted.</p></li>
<li><p><strong>selector</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]</span>) – <em>(Optional)</em> Selectors to highlight relevant nodes.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.highlight_quad">
<span class="sig-name descname"><span class="pre">highlight_quad</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">quad</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">color</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">outline_color</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/overlay.html#highlight_quad"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.overlay.highlight_quad" title="Link to this definition">#</a></dt>
<dd><p>Highlights given quad. Coordinates are absolute with respect to the main frame viewport.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>quad</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="dom.html#nodriver.cdp.dom.Quad" title="nodriver.cdp.dom.Quad"><code class="xref py py-class docutils literal notranslate"><span class="pre">Quad</span></code></a></span>) – Quad to highlight</p></li>
<li><p><strong>color</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="dom.html#nodriver.cdp.dom.RGBA" title="nodriver.cdp.dom.RGBA"><code class="xref py py-class docutils literal notranslate"><span class="pre">RGBA</span></code></a>]</span>) – <em>(Optional)</em> The highlight fill color (default: transparent).</p></li>
<li><p><strong>outline_color</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="dom.html#nodriver.cdp.dom.RGBA" title="nodriver.cdp.dom.RGBA"><code class="xref py py-class docutils literal notranslate"><span class="pre">RGBA</span></code></a>]</span>) – <em>(Optional)</em> The highlight outline color (default: transparent).</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.highlight_rect">
<span class="sig-name descname"><span class="pre">highlight_rect</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">y</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">width</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">height</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">color</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">outline_color</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/overlay.html#highlight_rect"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.overlay.highlight_rect" title="Link to this definition">#</a></dt>
<dd><p>Highlights given rectangle. Coordinates are absolute with respect to the main frame viewport.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>x</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></span>) – X coordinate</p></li>
<li><p><strong>y</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></span>) – Y coordinate</p></li>
<li><p><strong>width</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></span>) – Rectangle width</p></li>
<li><p><strong>height</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></span>) – Rectangle height</p></li>
<li><p><strong>color</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="dom.html#nodriver.cdp.dom.RGBA" title="nodriver.cdp.dom.RGBA"><code class="xref py py-class docutils literal notranslate"><span class="pre">RGBA</span></code></a>]</span>) – <em>(Optional)</em> The highlight fill color (default: transparent).</p></li>
<li><p><strong>outline_color</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="dom.html#nodriver.cdp.dom.RGBA" title="nodriver.cdp.dom.RGBA"><code class="xref py py-class docutils literal notranslate"><span class="pre">RGBA</span></code></a>]</span>) – <em>(Optional)</em> The highlight outline color (default: transparent).</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.highlight_source_order">
<span class="sig-name descname"><span class="pre">highlight_source_order</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">source_order_config</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">node_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">backend_node_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">object_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/overlay.html#highlight_source_order"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.overlay.highlight_source_order" title="Link to this definition">#</a></dt>
<dd><p>Highlights the source order of the children of the DOM node with given id or with the given
JavaScript object wrapper. Either nodeId or objectId must be specified.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>source_order_config</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.overlay.SourceOrderConfig" title="nodriver.cdp.overlay.SourceOrderConfig"><code class="xref py py-class docutils literal notranslate"><span class="pre">SourceOrderConfig</span></code></a></span>) – A descriptor for the appearance of the overlay drawing.</p></li>
<li><p><strong>node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="dom.html#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a>]</span>) – <em>(Optional)</em> Identifier of the node to highlight.</p></li>
<li><p><strong>backend_node_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="dom.html#nodriver.cdp.dom.BackendNodeId" title="nodriver.cdp.dom.BackendNodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">BackendNodeId</span></code></a>]</span>) – <em>(Optional)</em> Identifier of the backend node to highlight.</p></li>
<li><p><strong>object_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="runtime.html#nodriver.cdp.runtime.RemoteObjectId" title="nodriver.cdp.runtime.RemoteObjectId"><code class="xref py py-class docutils literal notranslate"><span class="pre">RemoteObjectId</span></code></a>]</span>) – <em>(Optional)</em> JavaScript object id of the node to be highlighted.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.set_inspect_mode">
<span class="sig-name descname"><span class="pre">set_inspect_mode</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">mode</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">highlight_config</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/overlay.html#set_inspect_mode"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.overlay.set_inspect_mode" title="Link to this definition">#</a></dt>
<dd><p>Enters the ‘inspect’ mode. In this mode, elements that user is hovering over are highlighted.
Backend then generates ‘inspectNodeRequested’ event upon element selection.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>mode</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.overlay.InspectMode" title="nodriver.cdp.overlay.InspectMode"><code class="xref py py-class docutils literal notranslate"><span class="pre">InspectMode</span></code></a></span>) – Set an inspection mode.</p></li>
<li><p><strong>highlight_config</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="#nodriver.cdp.overlay.HighlightConfig" title="nodriver.cdp.overlay.HighlightConfig"><code class="xref py py-class docutils literal notranslate"><span class="pre">HighlightConfig</span></code></a>]</span>) – <em>(Optional)</em> A descriptor for the highlight appearance of hovered-over nodes. May be omitted if <code class="docutils literal notranslate"><span class="pre">`enabled</span> <span class="pre">==</span> <span class="pre">false`</span></code>.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.set_paused_in_debugger_message">
<span class="sig-name descname"><span class="pre">set_paused_in_debugger_message</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">message</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/overlay.html#set_paused_in_debugger_message"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.overlay.set_paused_in_debugger_message" title="Link to this definition">#</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>message</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]</span>) – <em>(Optional)</em> The message to display, also triggers resume and step over controls.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.set_show_ad_highlights">
<span class="sig-name descname"><span class="pre">set_show_ad_highlights</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">show</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/overlay.html#set_show_ad_highlights"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.overlay.set_show_ad_highlights" title="Link to this definition">#</a></dt>
<dd><p>Highlights owner element of all frames detected to be ads.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>show</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a></span>) – True for showing ad highlights</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.set_show_container_query_overlays">
<span class="sig-name descname"><span class="pre">set_show_container_query_overlays</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">container_query_highlight_configs</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/overlay.html#set_show_container_query_overlays"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.overlay.set_show_container_query_overlays" title="Link to this definition">#</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>container_query_highlight_configs</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.overlay.ContainerQueryHighlightConfig" title="nodriver.cdp.overlay.ContainerQueryHighlightConfig"><code class="xref py py-class docutils literal notranslate"><span class="pre">ContainerQueryHighlightConfig</span></code></a>]</span>) – An array of node identifiers and descriptors for the highlight appearance.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.set_show_debug_borders">
<span class="sig-name descname"><span class="pre">set_show_debug_borders</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">show</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/overlay.html#set_show_debug_borders"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.overlay.set_show_debug_borders" title="Link to this definition">#</a></dt>
<dd><p>Requests that backend shows debug borders on layers</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>show</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a></span>) – True for showing debug borders</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.set_show_flex_overlays">
<span class="sig-name descname"><span class="pre">set_show_flex_overlays</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">flex_node_highlight_configs</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/overlay.html#set_show_flex_overlays"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.overlay.set_show_flex_overlays" title="Link to this definition">#</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>flex_node_highlight_configs</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.overlay.FlexNodeHighlightConfig" title="nodriver.cdp.overlay.FlexNodeHighlightConfig"><code class="xref py py-class docutils literal notranslate"><span class="pre">FlexNodeHighlightConfig</span></code></a>]</span>) – An array of node identifiers and descriptors for the highlight appearance.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.set_show_fps_counter">
<span class="sig-name descname"><span class="pre">set_show_fps_counter</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">show</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/overlay.html#set_show_fps_counter"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.overlay.set_show_fps_counter" title="Link to this definition">#</a></dt>
<dd><p>Requests that backend shows the FPS counter</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>show</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a></span>) – True for showing the FPS counter</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.set_show_grid_overlays">
<span class="sig-name descname"><span class="pre">set_show_grid_overlays</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">grid_node_highlight_configs</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/overlay.html#set_show_grid_overlays"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.overlay.set_show_grid_overlays" title="Link to this definition">#</a></dt>
<dd><p>Highlight multiple elements with the CSS Grid overlay.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>grid_node_highlight_configs</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.overlay.GridNodeHighlightConfig" title="nodriver.cdp.overlay.GridNodeHighlightConfig"><code class="xref py py-class docutils literal notranslate"><span class="pre">GridNodeHighlightConfig</span></code></a>]</span>) – An array of node identifiers and descriptors for the highlight appearance.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.set_show_hinge">
<span class="sig-name descname"><span class="pre">set_show_hinge</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">hinge_config</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/overlay.html#set_show_hinge"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.overlay.set_show_hinge" title="Link to this definition">#</a></dt>
<dd><p>Add a dual screen device hinge</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>hinge_config</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="#nodriver.cdp.overlay.HingeConfig" title="nodriver.cdp.overlay.HingeConfig"><code class="xref py py-class docutils literal notranslate"><span class="pre">HingeConfig</span></code></a>]</span>) – <em>(Optional)</em> hinge data, null means hideHinge</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.set_show_hit_test_borders">
<span class="sig-name descname"><span class="pre">set_show_hit_test_borders</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">show</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/overlay.html#set_show_hit_test_borders"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.overlay.set_show_hit_test_borders" title="Link to this definition">#</a></dt>
<dd><p>Deprecated, no longer has any effect.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 1.3.</span></p>
</div>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>show</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a></span>) – True for showing hit-test borders</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 1.3.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.set_show_isolated_elements">
<span class="sig-name descname"><span class="pre">set_show_isolated_elements</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">isolated_element_highlight_configs</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/overlay.html#set_show_isolated_elements"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.overlay.set_show_isolated_elements" title="Link to this definition">#</a></dt>
<dd><p>Show elements in isolation mode with overlays.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>isolated_element_highlight_configs</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.overlay.IsolatedElementHighlightConfig" title="nodriver.cdp.overlay.IsolatedElementHighlightConfig"><code class="xref py py-class docutils literal notranslate"><span class="pre">IsolatedElementHighlightConfig</span></code></a>]</span>) – An array of node identifiers and descriptors for the highlight appearance.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.set_show_layout_shift_regions">
<span class="sig-name descname"><span class="pre">set_show_layout_shift_regions</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">result</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/overlay.html#set_show_layout_shift_regions"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.overlay.set_show_layout_shift_regions" title="Link to this definition">#</a></dt>
<dd><p>Requests that backend shows layout shift regions</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>result</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a></span>) – True for showing layout shift regions</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.set_show_paint_rects">
<span class="sig-name descname"><span class="pre">set_show_paint_rects</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">result</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/overlay.html#set_show_paint_rects"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.overlay.set_show_paint_rects" title="Link to this definition">#</a></dt>
<dd><p>Requests that backend shows paint rectangles</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>result</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a></span>) – True for showing paint rectangles</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.set_show_scroll_bottleneck_rects">
<span class="sig-name descname"><span class="pre">set_show_scroll_bottleneck_rects</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">show</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/overlay.html#set_show_scroll_bottleneck_rects"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.overlay.set_show_scroll_bottleneck_rects" title="Link to this definition">#</a></dt>
<dd><p>Requests that backend shows scroll bottleneck rects</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>show</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a></span>) – True for showing scroll bottleneck rects</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.set_show_scroll_snap_overlays">
<span class="sig-name descname"><span class="pre">set_show_scroll_snap_overlays</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">scroll_snap_highlight_configs</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/overlay.html#set_show_scroll_snap_overlays"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.overlay.set_show_scroll_snap_overlays" title="Link to this definition">#</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>scroll_snap_highlight_configs</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.overlay.ScrollSnapHighlightConfig" title="nodriver.cdp.overlay.ScrollSnapHighlightConfig"><code class="xref py py-class docutils literal notranslate"><span class="pre">ScrollSnapHighlightConfig</span></code></a>]</span>) – An array of node identifiers and descriptors for the highlight appearance.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.set_show_viewport_size_on_resize">
<span class="sig-name descname"><span class="pre">set_show_viewport_size_on_resize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">show</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/overlay.html#set_show_viewport_size_on_resize"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.overlay.set_show_viewport_size_on_resize" title="Link to this definition">#</a></dt>
<dd><p>Paints viewport size upon main frame resize.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>show</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a></span>) – Whether to paint size or not.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.set_show_web_vitals">
<span class="sig-name descname"><span class="pre">set_show_web_vitals</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">show</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/overlay.html#set_show_web_vitals"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.overlay.set_show_web_vitals" title="Link to this definition">#</a></dt>
<dd><p>Deprecated, no longer has any effect.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 1.3.</span></p>
</div>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>show</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a></span>) – </p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 1.3.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.set_show_window_controls_overlay">
<span class="sig-name descname"><span class="pre">set_show_window_controls_overlay</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">window_controls_overlay_config</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/overlay.html#set_show_window_controls_overlay"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.overlay.set_show_window_controls_overlay" title="Link to this definition">#</a></dt>
<dd><p>Show Window Controls Overlay for PWA</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>window_controls_overlay_config</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="#nodriver.cdp.overlay.WindowControlsOverlayConfig" title="nodriver.cdp.overlay.WindowControlsOverlayConfig"><code class="xref py py-class docutils literal notranslate"><span class="pre">WindowControlsOverlayConfig</span></code></a>]</span>) – <em>(Optional)</em> Window Controls Overlay data, null means hide Window Controls Overlay</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

</section>
<section id="events">
<h2>Events<a class="headerlink" href="#events" title="Link to this heading">#</a></h2>
<p>Generally, you do not need to instantiate CDP events
yourself. Instead, the API creates events for you and then
you use the event’s attributes.</p>
<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.InspectNodeRequested">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">InspectNodeRequested</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">backend_node_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/overlay.html#InspectNodeRequested"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.overlay.InspectNodeRequested" title="Link to this definition">#</a></dt>
<dd><p>Fired when the node should be inspected. This happens after call to <code class="docutils literal notranslate"><span class="pre">setInspectMode</span></code> or when
user manually inspects an element.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.InspectNodeRequested.backend_node_id">
<span class="sig-name descname"><span class="pre">backend_node_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="dom.html#nodriver.cdp.dom.BackendNodeId" title="nodriver.cdp.dom.BackendNodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">BackendNodeId</span></code></a></em><a class="headerlink" href="#nodriver.cdp.overlay.InspectNodeRequested.backend_node_id" title="Link to this definition">#</a></dt>
<dd><p>Id of the node to inspect.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.NodeHighlightRequested">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">NodeHighlightRequested</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">node_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/overlay.html#NodeHighlightRequested"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.overlay.NodeHighlightRequested" title="Link to this definition">#</a></dt>
<dd><p>Fired when the node should be highlighted. This happens after call to <code class="docutils literal notranslate"><span class="pre">setInspectMode</span></code>.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.NodeHighlightRequested.node_id">
<span class="sig-name descname"><span class="pre">node_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="dom.html#nodriver.cdp.dom.NodeId" title="nodriver.cdp.dom.NodeId"><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeId</span></code></a></em><a class="headerlink" href="#nodriver.cdp.overlay.NodeHighlightRequested.node_id" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.ScreenshotRequested">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">ScreenshotRequested</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">viewport</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/overlay.html#ScreenshotRequested"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.overlay.ScreenshotRequested" title="Link to this definition">#</a></dt>
<dd><p>Fired when user asks to capture screenshot of some area on the page.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.ScreenshotRequested.viewport">
<span class="sig-name descname"><span class="pre">viewport</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="page.html#nodriver.cdp.page.Viewport" title="nodriver.cdp.page.Viewport"><code class="xref py py-class docutils literal notranslate"><span class="pre">Viewport</span></code></a></em><a class="headerlink" href="#nodriver.cdp.overlay.ScreenshotRequested.viewport" title="Link to this definition">#</a></dt>
<dd><p>Viewport to capture, in device independent pixels (dip).</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.overlay.InspectModeCanceled">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">InspectModeCanceled</span></span><a class="reference internal" href="../../_modules/nodriver/cdp/overlay.html#InspectModeCanceled"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.overlay.InspectModeCanceled" title="Link to this definition">#</a></dt>
<dd><p>Fired when user cancels the inspect mode.</p>
</dd></dl>

</section>
</section>

        </article>
      </div>
      <footer>
        
        <div class="related-pages">
          <a class="next-page" href="page.html">
              <div class="page-info">
                <div class="context">
                  <span>Next</span>
                </div>
                <div class="title">Page</div>
              </div>
              <svg class="furo-related-icon"><use href="#svg-arrow-right"></use></svg>
            </a>
          <a class="prev-page" href="network.html">
              <svg class="furo-related-icon"><use href="#svg-arrow-right"></use></svg>
              <div class="page-info">
                <div class="context">
                  <span>Previous</span>
                </div>
                
                <div class="title">Network</div>
                
              </div>
            </a>
        </div>
        <div class="bottom-of-page">
          <div class="left-details">
            <div class="copyright">
                Copyright &#169; 2023, Author
            </div>
            Made with <a href="https://www.sphinx-doc.org/">Sphinx</a> and <a class="muted-link" href="https://pradyunsg.me">@pradyunsg</a>'s
            
            <a href="https://github.com/pradyunsg/furo">Furo</a>
            
          </div>
          <div class="right-details">
            
          </div>
        </div>
        
      </footer>
    </div>
    <aside class="toc-drawer">
      
      
      <div class="toc-sticky toc-scroll">
        <div class="toc-title-container">
          <span class="toc-title">
            On this page
          </span>
        </div>
        <div class="toc-tree-container">
          <div class="toc-tree">
            <ul>
<li><a class="reference internal" href="#">Overlay</a><ul>
<li><a class="reference internal" href="#types">Types</a><ul>
<li><a class="reference internal" href="#nodriver.cdp.overlay.SourceOrderConfig"><code class="docutils literal notranslate"><span class="pre">SourceOrderConfig</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.overlay.SourceOrderConfig.parent_outline_color"><code class="docutils literal notranslate"><span class="pre">SourceOrderConfig.parent_outline_color</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.SourceOrderConfig.child_outline_color"><code class="docutils literal notranslate"><span class="pre">SourceOrderConfig.child_outline_color</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.GridHighlightConfig"><code class="docutils literal notranslate"><span class="pre">GridHighlightConfig</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.overlay.GridHighlightConfig.show_grid_extension_lines"><code class="docutils literal notranslate"><span class="pre">GridHighlightConfig.show_grid_extension_lines</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.GridHighlightConfig.show_positive_line_numbers"><code class="docutils literal notranslate"><span class="pre">GridHighlightConfig.show_positive_line_numbers</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.GridHighlightConfig.show_negative_line_numbers"><code class="docutils literal notranslate"><span class="pre">GridHighlightConfig.show_negative_line_numbers</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.GridHighlightConfig.show_area_names"><code class="docutils literal notranslate"><span class="pre">GridHighlightConfig.show_area_names</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.GridHighlightConfig.show_line_names"><code class="docutils literal notranslate"><span class="pre">GridHighlightConfig.show_line_names</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.GridHighlightConfig.show_track_sizes"><code class="docutils literal notranslate"><span class="pre">GridHighlightConfig.show_track_sizes</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.GridHighlightConfig.grid_border_color"><code class="docutils literal notranslate"><span class="pre">GridHighlightConfig.grid_border_color</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.GridHighlightConfig.cell_border_color"><code class="docutils literal notranslate"><span class="pre">GridHighlightConfig.cell_border_color</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.GridHighlightConfig.row_line_color"><code class="docutils literal notranslate"><span class="pre">GridHighlightConfig.row_line_color</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.GridHighlightConfig.column_line_color"><code class="docutils literal notranslate"><span class="pre">GridHighlightConfig.column_line_color</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.GridHighlightConfig.grid_border_dash"><code class="docutils literal notranslate"><span class="pre">GridHighlightConfig.grid_border_dash</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.GridHighlightConfig.cell_border_dash"><code class="docutils literal notranslate"><span class="pre">GridHighlightConfig.cell_border_dash</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.GridHighlightConfig.row_line_dash"><code class="docutils literal notranslate"><span class="pre">GridHighlightConfig.row_line_dash</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.GridHighlightConfig.column_line_dash"><code class="docutils literal notranslate"><span class="pre">GridHighlightConfig.column_line_dash</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.GridHighlightConfig.row_gap_color"><code class="docutils literal notranslate"><span class="pre">GridHighlightConfig.row_gap_color</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.GridHighlightConfig.row_hatch_color"><code class="docutils literal notranslate"><span class="pre">GridHighlightConfig.row_hatch_color</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.GridHighlightConfig.column_gap_color"><code class="docutils literal notranslate"><span class="pre">GridHighlightConfig.column_gap_color</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.GridHighlightConfig.column_hatch_color"><code class="docutils literal notranslate"><span class="pre">GridHighlightConfig.column_hatch_color</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.GridHighlightConfig.area_border_color"><code class="docutils literal notranslate"><span class="pre">GridHighlightConfig.area_border_color</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.GridHighlightConfig.grid_background_color"><code class="docutils literal notranslate"><span class="pre">GridHighlightConfig.grid_background_color</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.FlexContainerHighlightConfig"><code class="docutils literal notranslate"><span class="pre">FlexContainerHighlightConfig</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.overlay.FlexContainerHighlightConfig.container_border"><code class="docutils literal notranslate"><span class="pre">FlexContainerHighlightConfig.container_border</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.FlexContainerHighlightConfig.line_separator"><code class="docutils literal notranslate"><span class="pre">FlexContainerHighlightConfig.line_separator</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.FlexContainerHighlightConfig.item_separator"><code class="docutils literal notranslate"><span class="pre">FlexContainerHighlightConfig.item_separator</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.FlexContainerHighlightConfig.main_distributed_space"><code class="docutils literal notranslate"><span class="pre">FlexContainerHighlightConfig.main_distributed_space</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.FlexContainerHighlightConfig.cross_distributed_space"><code class="docutils literal notranslate"><span class="pre">FlexContainerHighlightConfig.cross_distributed_space</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.FlexContainerHighlightConfig.row_gap_space"><code class="docutils literal notranslate"><span class="pre">FlexContainerHighlightConfig.row_gap_space</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.FlexContainerHighlightConfig.column_gap_space"><code class="docutils literal notranslate"><span class="pre">FlexContainerHighlightConfig.column_gap_space</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.FlexContainerHighlightConfig.cross_alignment"><code class="docutils literal notranslate"><span class="pre">FlexContainerHighlightConfig.cross_alignment</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.FlexItemHighlightConfig"><code class="docutils literal notranslate"><span class="pre">FlexItemHighlightConfig</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.overlay.FlexItemHighlightConfig.base_size_box"><code class="docutils literal notranslate"><span class="pre">FlexItemHighlightConfig.base_size_box</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.FlexItemHighlightConfig.base_size_border"><code class="docutils literal notranslate"><span class="pre">FlexItemHighlightConfig.base_size_border</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.FlexItemHighlightConfig.flexibility_arrow"><code class="docutils literal notranslate"><span class="pre">FlexItemHighlightConfig.flexibility_arrow</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.LineStyle"><code class="docutils literal notranslate"><span class="pre">LineStyle</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.overlay.LineStyle.color"><code class="docutils literal notranslate"><span class="pre">LineStyle.color</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.LineStyle.pattern"><code class="docutils literal notranslate"><span class="pre">LineStyle.pattern</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.BoxStyle"><code class="docutils literal notranslate"><span class="pre">BoxStyle</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.overlay.BoxStyle.fill_color"><code class="docutils literal notranslate"><span class="pre">BoxStyle.fill_color</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.BoxStyle.hatch_color"><code class="docutils literal notranslate"><span class="pre">BoxStyle.hatch_color</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.ContrastAlgorithm"><code class="docutils literal notranslate"><span class="pre">ContrastAlgorithm</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.overlay.ContrastAlgorithm.AA"><code class="docutils literal notranslate"><span class="pre">ContrastAlgorithm.AA</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.ContrastAlgorithm.AAA"><code class="docutils literal notranslate"><span class="pre">ContrastAlgorithm.AAA</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.ContrastAlgorithm.APCA"><code class="docutils literal notranslate"><span class="pre">ContrastAlgorithm.APCA</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.HighlightConfig"><code class="docutils literal notranslate"><span class="pre">HighlightConfig</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.overlay.HighlightConfig.show_info"><code class="docutils literal notranslate"><span class="pre">HighlightConfig.show_info</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.HighlightConfig.show_styles"><code class="docutils literal notranslate"><span class="pre">HighlightConfig.show_styles</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.HighlightConfig.show_rulers"><code class="docutils literal notranslate"><span class="pre">HighlightConfig.show_rulers</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.HighlightConfig.show_accessibility_info"><code class="docutils literal notranslate"><span class="pre">HighlightConfig.show_accessibility_info</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.HighlightConfig.show_extension_lines"><code class="docutils literal notranslate"><span class="pre">HighlightConfig.show_extension_lines</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.HighlightConfig.content_color"><code class="docutils literal notranslate"><span class="pre">HighlightConfig.content_color</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.HighlightConfig.padding_color"><code class="docutils literal notranslate"><span class="pre">HighlightConfig.padding_color</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.HighlightConfig.border_color"><code class="docutils literal notranslate"><span class="pre">HighlightConfig.border_color</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.HighlightConfig.margin_color"><code class="docutils literal notranslate"><span class="pre">HighlightConfig.margin_color</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.HighlightConfig.event_target_color"><code class="docutils literal notranslate"><span class="pre">HighlightConfig.event_target_color</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.HighlightConfig.shape_color"><code class="docutils literal notranslate"><span class="pre">HighlightConfig.shape_color</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.HighlightConfig.shape_margin_color"><code class="docutils literal notranslate"><span class="pre">HighlightConfig.shape_margin_color</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.HighlightConfig.css_grid_color"><code class="docutils literal notranslate"><span class="pre">HighlightConfig.css_grid_color</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.HighlightConfig.color_format"><code class="docutils literal notranslate"><span class="pre">HighlightConfig.color_format</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.HighlightConfig.grid_highlight_config"><code class="docutils literal notranslate"><span class="pre">HighlightConfig.grid_highlight_config</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.HighlightConfig.flex_container_highlight_config"><code class="docutils literal notranslate"><span class="pre">HighlightConfig.flex_container_highlight_config</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.HighlightConfig.flex_item_highlight_config"><code class="docutils literal notranslate"><span class="pre">HighlightConfig.flex_item_highlight_config</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.HighlightConfig.contrast_algorithm"><code class="docutils literal notranslate"><span class="pre">HighlightConfig.contrast_algorithm</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.HighlightConfig.container_query_container_highlight_config"><code class="docutils literal notranslate"><span class="pre">HighlightConfig.container_query_container_highlight_config</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.ColorFormat"><code class="docutils literal notranslate"><span class="pre">ColorFormat</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.overlay.ColorFormat.RGB"><code class="docutils literal notranslate"><span class="pre">ColorFormat.RGB</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.ColorFormat.HSL"><code class="docutils literal notranslate"><span class="pre">ColorFormat.HSL</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.ColorFormat.HWB"><code class="docutils literal notranslate"><span class="pre">ColorFormat.HWB</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.ColorFormat.HEX_"><code class="docutils literal notranslate"><span class="pre">ColorFormat.HEX_</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.GridNodeHighlightConfig"><code class="docutils literal notranslate"><span class="pre">GridNodeHighlightConfig</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.overlay.GridNodeHighlightConfig.grid_highlight_config"><code class="docutils literal notranslate"><span class="pre">GridNodeHighlightConfig.grid_highlight_config</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.GridNodeHighlightConfig.node_id"><code class="docutils literal notranslate"><span class="pre">GridNodeHighlightConfig.node_id</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.FlexNodeHighlightConfig"><code class="docutils literal notranslate"><span class="pre">FlexNodeHighlightConfig</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.overlay.FlexNodeHighlightConfig.flex_container_highlight_config"><code class="docutils literal notranslate"><span class="pre">FlexNodeHighlightConfig.flex_container_highlight_config</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.FlexNodeHighlightConfig.node_id"><code class="docutils literal notranslate"><span class="pre">FlexNodeHighlightConfig.node_id</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.ScrollSnapContainerHighlightConfig"><code class="docutils literal notranslate"><span class="pre">ScrollSnapContainerHighlightConfig</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.overlay.ScrollSnapContainerHighlightConfig.snapport_border"><code class="docutils literal notranslate"><span class="pre">ScrollSnapContainerHighlightConfig.snapport_border</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.ScrollSnapContainerHighlightConfig.snap_area_border"><code class="docutils literal notranslate"><span class="pre">ScrollSnapContainerHighlightConfig.snap_area_border</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.ScrollSnapContainerHighlightConfig.scroll_margin_color"><code class="docutils literal notranslate"><span class="pre">ScrollSnapContainerHighlightConfig.scroll_margin_color</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.ScrollSnapContainerHighlightConfig.scroll_padding_color"><code class="docutils literal notranslate"><span class="pre">ScrollSnapContainerHighlightConfig.scroll_padding_color</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.ScrollSnapHighlightConfig"><code class="docutils literal notranslate"><span class="pre">ScrollSnapHighlightConfig</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.overlay.ScrollSnapHighlightConfig.scroll_snap_container_highlight_config"><code class="docutils literal notranslate"><span class="pre">ScrollSnapHighlightConfig.scroll_snap_container_highlight_config</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.ScrollSnapHighlightConfig.node_id"><code class="docutils literal notranslate"><span class="pre">ScrollSnapHighlightConfig.node_id</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.HingeConfig"><code class="docutils literal notranslate"><span class="pre">HingeConfig</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.overlay.HingeConfig.rect"><code class="docutils literal notranslate"><span class="pre">HingeConfig.rect</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.HingeConfig.content_color"><code class="docutils literal notranslate"><span class="pre">HingeConfig.content_color</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.HingeConfig.outline_color"><code class="docutils literal notranslate"><span class="pre">HingeConfig.outline_color</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.WindowControlsOverlayConfig"><code class="docutils literal notranslate"><span class="pre">WindowControlsOverlayConfig</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.overlay.WindowControlsOverlayConfig.show_css"><code class="docutils literal notranslate"><span class="pre">WindowControlsOverlayConfig.show_css</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.WindowControlsOverlayConfig.selected_platform"><code class="docutils literal notranslate"><span class="pre">WindowControlsOverlayConfig.selected_platform</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.WindowControlsOverlayConfig.theme_color"><code class="docutils literal notranslate"><span class="pre">WindowControlsOverlayConfig.theme_color</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.ContainerQueryHighlightConfig"><code class="docutils literal notranslate"><span class="pre">ContainerQueryHighlightConfig</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.overlay.ContainerQueryHighlightConfig.container_query_container_highlight_config"><code class="docutils literal notranslate"><span class="pre">ContainerQueryHighlightConfig.container_query_container_highlight_config</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.ContainerQueryHighlightConfig.node_id"><code class="docutils literal notranslate"><span class="pre">ContainerQueryHighlightConfig.node_id</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.ContainerQueryContainerHighlightConfig"><code class="docutils literal notranslate"><span class="pre">ContainerQueryContainerHighlightConfig</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.overlay.ContainerQueryContainerHighlightConfig.container_border"><code class="docutils literal notranslate"><span class="pre">ContainerQueryContainerHighlightConfig.container_border</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.ContainerQueryContainerHighlightConfig.descendant_border"><code class="docutils literal notranslate"><span class="pre">ContainerQueryContainerHighlightConfig.descendant_border</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.IsolatedElementHighlightConfig"><code class="docutils literal notranslate"><span class="pre">IsolatedElementHighlightConfig</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.overlay.IsolatedElementHighlightConfig.isolation_mode_highlight_config"><code class="docutils literal notranslate"><span class="pre">IsolatedElementHighlightConfig.isolation_mode_highlight_config</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.IsolatedElementHighlightConfig.node_id"><code class="docutils literal notranslate"><span class="pre">IsolatedElementHighlightConfig.node_id</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.IsolationModeHighlightConfig"><code class="docutils literal notranslate"><span class="pre">IsolationModeHighlightConfig</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.overlay.IsolationModeHighlightConfig.resizer_color"><code class="docutils literal notranslate"><span class="pre">IsolationModeHighlightConfig.resizer_color</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.IsolationModeHighlightConfig.resizer_handle_color"><code class="docutils literal notranslate"><span class="pre">IsolationModeHighlightConfig.resizer_handle_color</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.IsolationModeHighlightConfig.mask_color"><code class="docutils literal notranslate"><span class="pre">IsolationModeHighlightConfig.mask_color</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.InspectMode"><code class="docutils literal notranslate"><span class="pre">InspectMode</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.overlay.InspectMode.SEARCH_FOR_NODE"><code class="docutils literal notranslate"><span class="pre">InspectMode.SEARCH_FOR_NODE</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.InspectMode.SEARCH_FOR_UA_SHADOW_DOM"><code class="docutils literal notranslate"><span class="pre">InspectMode.SEARCH_FOR_UA_SHADOW_DOM</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.InspectMode.CAPTURE_AREA_SCREENSHOT"><code class="docutils literal notranslate"><span class="pre">InspectMode.CAPTURE_AREA_SCREENSHOT</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.InspectMode.SHOW_DISTANCES"><code class="docutils literal notranslate"><span class="pre">InspectMode.SHOW_DISTANCES</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.InspectMode.NONE"><code class="docutils literal notranslate"><span class="pre">InspectMode.NONE</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#commands">Commands</a><ul>
<li><a class="reference internal" href="#nodriver.cdp.overlay.disable"><code class="docutils literal notranslate"><span class="pre">disable()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.enable"><code class="docutils literal notranslate"><span class="pre">enable()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.get_grid_highlight_objects_for_test"><code class="docutils literal notranslate"><span class="pre">get_grid_highlight_objects_for_test()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.get_highlight_object_for_test"><code class="docutils literal notranslate"><span class="pre">get_highlight_object_for_test()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.get_source_order_highlight_object_for_test"><code class="docutils literal notranslate"><span class="pre">get_source_order_highlight_object_for_test()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.hide_highlight"><code class="docutils literal notranslate"><span class="pre">hide_highlight()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.highlight_frame"><code class="docutils literal notranslate"><span class="pre">highlight_frame()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.highlight_node"><code class="docutils literal notranslate"><span class="pre">highlight_node()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.highlight_quad"><code class="docutils literal notranslate"><span class="pre">highlight_quad()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.highlight_rect"><code class="docutils literal notranslate"><span class="pre">highlight_rect()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.highlight_source_order"><code class="docutils literal notranslate"><span class="pre">highlight_source_order()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.set_inspect_mode"><code class="docutils literal notranslate"><span class="pre">set_inspect_mode()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.set_paused_in_debugger_message"><code class="docutils literal notranslate"><span class="pre">set_paused_in_debugger_message()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.set_show_ad_highlights"><code class="docutils literal notranslate"><span class="pre">set_show_ad_highlights()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.set_show_container_query_overlays"><code class="docutils literal notranslate"><span class="pre">set_show_container_query_overlays()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.set_show_debug_borders"><code class="docutils literal notranslate"><span class="pre">set_show_debug_borders()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.set_show_flex_overlays"><code class="docutils literal notranslate"><span class="pre">set_show_flex_overlays()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.set_show_fps_counter"><code class="docutils literal notranslate"><span class="pre">set_show_fps_counter()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.set_show_grid_overlays"><code class="docutils literal notranslate"><span class="pre">set_show_grid_overlays()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.set_show_hinge"><code class="docutils literal notranslate"><span class="pre">set_show_hinge()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.set_show_hit_test_borders"><code class="docutils literal notranslate"><span class="pre">set_show_hit_test_borders()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.set_show_isolated_elements"><code class="docutils literal notranslate"><span class="pre">set_show_isolated_elements()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.set_show_layout_shift_regions"><code class="docutils literal notranslate"><span class="pre">set_show_layout_shift_regions()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.set_show_paint_rects"><code class="docutils literal notranslate"><span class="pre">set_show_paint_rects()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.set_show_scroll_bottleneck_rects"><code class="docutils literal notranslate"><span class="pre">set_show_scroll_bottleneck_rects()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.set_show_scroll_snap_overlays"><code class="docutils literal notranslate"><span class="pre">set_show_scroll_snap_overlays()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.set_show_viewport_size_on_resize"><code class="docutils literal notranslate"><span class="pre">set_show_viewport_size_on_resize()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.set_show_web_vitals"><code class="docutils literal notranslate"><span class="pre">set_show_web_vitals()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.set_show_window_controls_overlay"><code class="docutils literal notranslate"><span class="pre">set_show_window_controls_overlay()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#events">Events</a><ul>
<li><a class="reference internal" href="#nodriver.cdp.overlay.InspectNodeRequested"><code class="docutils literal notranslate"><span class="pre">InspectNodeRequested</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.overlay.InspectNodeRequested.backend_node_id"><code class="docutils literal notranslate"><span class="pre">InspectNodeRequested.backend_node_id</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.NodeHighlightRequested"><code class="docutils literal notranslate"><span class="pre">NodeHighlightRequested</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.overlay.NodeHighlightRequested.node_id"><code class="docutils literal notranslate"><span class="pre">NodeHighlightRequested.node_id</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.ScreenshotRequested"><code class="docutils literal notranslate"><span class="pre">ScreenshotRequested</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.overlay.ScreenshotRequested.viewport"><code class="docutils literal notranslate"><span class="pre">ScreenshotRequested.viewport</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.overlay.InspectModeCanceled"><code class="docutils literal notranslate"><span class="pre">InspectModeCanceled</span></code></a></li>
</ul>
</li>
</ul>
</li>
</ul>

          </div>
        </div>
      </div>
      
      
    </aside>
  </div>
</div><script src="../../_static/documentation_options.js?v=5929fcd5"></script>
    <script src="../../_static/doctools.js?v=888ff710"></script>
    <script src="../../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../../_static/scripts/furo.js?v=32e29ea5"></script>
    </body>
</html>