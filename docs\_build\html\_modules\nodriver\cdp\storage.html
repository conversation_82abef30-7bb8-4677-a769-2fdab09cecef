<!doctype html>
<html class="no-js" lang="en" data-content_root="../../../">
  <head><meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width,initial-scale=1"/>
    <meta name="color-scheme" content="light dark"><link rel="index" title="Index" href="../../../genindex.html" /><link rel="search" title="Search" href="../../../search.html" />

    <!-- Generated with Sphinx 7.2.6 and Furo 2023.09.10 -->
        <title>nodriver.cdp.storage - nodriver documentation</title>
      <link rel="stylesheet" type="text/css" href="../../../_static/pygments.css?v=fa44fd50" />
    <link rel="stylesheet" type="text/css" href="../../../_static/styles/furo.css?v=135e06be" />
    <link rel="stylesheet" type="text/css" href="../../../_static/styles/furo-extensions.css?v=36a5483c" />
    <link rel="stylesheet" type="text/css" href="../../../_static/./custom.css?v=3934642d" />
    
    


<style>
  body {
    --color-code-background: #eeffcc;
  --color-code-foreground: black;
  
  }
  @media not print {
    body[data-theme="dark"] {
      --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8eefba;
  --color-background-primary: #111112;
  --color-problematic: #e7aeae;
  
    }
    @media (prefers-color-scheme: dark) {
      body:not([data-theme="light"]) {
        --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8eefba;
  --color-background-primary: #111112;
  --color-problematic: #e7aeae;
  
      }
    }
  }
</style></head>
  <body>
    
    <script>
      document.body.dataset.theme = localStorage.getItem("theme") || "auto";
    </script>
    

<svg xmlns="http://www.w3.org/2000/svg" style="display: none;">
  <symbol id="svg-toc" viewBox="0 0 24 24">
    <title>Contents</title>
    <svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 1024 1024">
      <path d="M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM115.4 518.9L271.7 642c5.8 4.6 14.4.5 14.4-6.9V388.9c0-7.4-8.5-11.5-14.4-6.9L115.4 505.1a8.74 8.74 0 0 0 0 13.8z"/>
    </svg>
  </symbol>
  <symbol id="svg-menu" viewBox="0 0 24 24">
    <title>Menu</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-menu">
      <line x1="3" y1="12" x2="21" y2="12"></line>
      <line x1="3" y1="6" x2="21" y2="6"></line>
      <line x1="3" y1="18" x2="21" y2="18"></line>
    </svg>
  </symbol>
  <symbol id="svg-arrow-right" viewBox="0 0 24 24">
    <title>Expand</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-chevron-right">
      <polyline points="9 18 15 12 9 6"></polyline>
    </svg>
  </symbol>
  <symbol id="svg-sun" viewBox="0 0 24 24">
    <title>Light mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="feather-sun">
      <circle cx="12" cy="12" r="5"></circle>
      <line x1="12" y1="1" x2="12" y2="3"></line>
      <line x1="12" y1="21" x2="12" y2="23"></line>
      <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
      <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
      <line x1="1" y1="12" x2="3" y2="12"></line>
      <line x1="21" y1="12" x2="23" y2="12"></line>
      <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
      <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
    </svg>
  </symbol>
  <symbol id="svg-moon" viewBox="0 0 24 24">
    <title>Dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-moon">
      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
      <path d="M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z" />
    </svg>
  </symbol>
  <symbol id="svg-sun-half" viewBox="0 0 24 24">
    <title>Auto light/dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-shadow">
      <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
      <circle cx="12" cy="12" r="9" />
      <path d="M13 12h5" />
      <path d="M13 15h4" />
      <path d="M13 18h1" />
      <path d="M13 9h4" />
      <path d="M13 6h1" />
    </svg>
  </symbol>
</svg>

<input type="checkbox" class="sidebar-toggle" name="__navigation" id="__navigation">
<input type="checkbox" class="sidebar-toggle" name="__toc" id="__toc">
<label class="overlay sidebar-overlay" for="__navigation">
  <div class="visually-hidden">Hide navigation sidebar</div>
</label>
<label class="overlay toc-overlay" for="__toc">
  <div class="visually-hidden">Hide table of contents sidebar</div>
</label>



<div class="page">
  <header class="mobile-header">
    <div class="header-left">
      <label class="nav-overlay-icon" for="__navigation">
        <div class="visually-hidden">Toggle site navigation sidebar</div>
        <i class="icon"><svg><use href="#svg-menu"></use></svg></i>
      </label>
    </div>
    <div class="header-center">
      <a href="../../../index.html"><div class="brand">nodriver  documentation</div></a>
    </div>
    <div class="header-right">
      <div class="theme-toggle-container theme-toggle-header">
        <button class="theme-toggle">
          <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
          <svg class="theme-icon-when-auto"><use href="#svg-sun-half"></use></svg>
          <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
          <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
        </button>
      </div>
      <label class="toc-overlay-icon toc-header-icon no-toc" for="__toc">
        <div class="visually-hidden">Toggle table of contents sidebar</div>
        <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
      </label>
    </div>
  </header>
  <aside class="sidebar-drawer">
    <div class="sidebar-container">
      
      <div class="sidebar-sticky"><div class="sidebar-scroll"><a class="sidebar-brand" href="../../../index.html">
  
  
  <span class="sidebar-brand-text">nodriver  documentation</span>
  
</a><form class="sidebar-search-container" method="get" action="../../../search.html" role="search">
  <input class="sidebar-search" placeholder="Search" name="q" aria-label="Search">
  <input type="hidden" name="check_keywords" value="yes">
  <input type="hidden" name="area" value="default">
</form>
<div id="searchbox"></div><div class="sidebar-tree">
  <ul>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html">Quickstart guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#usage-example">usage example</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#more-complete-example">More complete example</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#custom-starting-options">Custom starting options</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#alternative-custom-options">Alternative custom options</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#proxies-socks5-authenticated-too">Proxies (socks5, authenticated too)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#concrete-example">Concrete example</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/classes/browser.html">Browser class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/classes/tab.html">Tab class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/classes/element.html">Element class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/classes/others_and_helpers.html">Other classes and Helper classes</a></li>
</ul>
<ul>
<li class="toctree-l1 has-children"><a class="reference internal" href="../../../nodriver/cdp.html">CDP object</a><input class="toctree-checkbox" id="toctree-checkbox-1" name="toctree-checkbox-1" role="switch" type="checkbox"/><label for="toctree-checkbox-1"><div class="visually-hidden">Toggle navigation of CDP object</div><i class="icon"><svg><use href="#svg-arrow-right"></use></svg></i></label><ul>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/accessibility.html">Accessibility</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/animation.html">Animation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/audits.html">Audits</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/autofill.html">Autofill</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/background_service.html">BackgroundService</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/bluetooth_emulation.html">BluetoothEmulation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/browser.html">Browser</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/cache_storage.html">CacheStorage</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/cast.html">Cast</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/console.html">Console</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/css.html">CSS</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/debugger.html">Debugger</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/device_access.html">DeviceAccess</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/device_orientation.html">DeviceOrientation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/dom.html">DOM</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/dom_debugger.html">DOMDebugger</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/dom_snapshot.html">DOMSnapshot</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/dom_storage.html">DOMStorage</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/emulation.html">Emulation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/event_breakpoints.html">EventBreakpoints</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/extensions.html">Extensions</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/fed_cm.html">FedCm</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/fetch.html">Fetch</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/file_system.html">FileSystem</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/headless_experimental.html">HeadlessExperimental</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/heap_profiler.html">HeapProfiler</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/indexed_db.html">IndexedDB</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/input_.html">Input</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/inspector.html">Inspector</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/io.html">IO</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/layer_tree.html">LayerTree</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/log.html">Log</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/media.html">Media</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/memory.html">Memory</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/network.html">Network</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/overlay.html">Overlay</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/page.html">Page</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/performance.html">Performance</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/performance_timeline.html">PerformanceTimeline</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/preload.html">Preload</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/profiler.html">Profiler</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/pwa.html">PWA</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/runtime.html">Runtime</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/schema.html">Schema</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/security.html">Security</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/service_worker.html">ServiceWorker</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/storage.html">Storage</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/system_info.html">SystemInfo</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/target.html">Target</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/tethering.html">Tethering</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/tracing.html">Tracing</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/web_audio.html">WebAudio</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/web_authn.html">WebAuthn</a></li>
</ul>
</li>
</ul>

</div>
</div>
      </div>
      
    </div>
  </aside>
  <div class="main">
    <div class="content">
      <div class="article-container">
        <a href="#" class="back-to-top muted-link">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
            <path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8v12z"></path>
          </svg>
          <span>Back to top</span>
        </a>
        <div class="content-icon-container">
          <div class="theme-toggle-container theme-toggle-content">
            <button class="theme-toggle">
              <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
              <svg class="theme-icon-when-auto"><use href="#svg-sun-half"></use></svg>
              <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
              <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
            </button>
          </div>
          <label class="toc-overlay-icon toc-content-icon no-toc" for="__toc">
            <div class="visually-hidden">Toggle table of contents sidebar</div>
            <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
          </label>
        </div>
        <article role="main">
          <h1>Source code for nodriver.cdp.storage</h1><div class="highlight"><pre>
<span></span><span class="c1"># DO NOT EDIT THIS FILE!</span>
<span class="c1">#</span>
<span class="c1"># This file is generated from the CDP specification. If you need to make</span>
<span class="c1"># changes, edit the generator and regenerate all of the modules.</span>
<span class="c1">#</span>
<span class="c1"># CDP domain: Storage (experimental)</span>

<span class="kn">from</span> <span class="nn">__future__</span> <span class="kn">import</span> <span class="n">annotations</span>

<span class="kn">import</span> <span class="nn">enum</span>
<span class="kn">import</span> <span class="nn">typing</span>
<span class="kn">from</span> <span class="nn">dataclasses</span> <span class="kn">import</span> <span class="n">dataclass</span>

<span class="kn">from</span> <span class="nn">.</span> <span class="kn">import</span> <span class="n">browser</span><span class="p">,</span> <span class="n">network</span><span class="p">,</span> <span class="n">page</span>
<span class="kn">from</span> <span class="nn">.util</span> <span class="kn">import</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">event_class</span>


<div class="viewcode-block" id="SerializedStorageKey">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.SerializedStorageKey">[docs]</a>
<span class="k">class</span> <span class="nc">SerializedStorageKey</span><span class="p">(</span><span class="nb">str</span><span class="p">):</span>
    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">SerializedStorageKey</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span>

    <span class="k">def</span> <span class="fm">__repr__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="s2">&quot;SerializedStorageKey(</span><span class="si">{}</span><span class="s2">)&quot;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="fm">__repr__</span><span class="p">())</span></div>



<div class="viewcode-block" id="StorageType">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.StorageType">[docs]</a>
<span class="k">class</span> <span class="nc">StorageType</span><span class="p">(</span><span class="n">enum</span><span class="o">.</span><span class="n">Enum</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Enum of possible storage types.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">COOKIES</span> <span class="o">=</span> <span class="s2">&quot;cookies&quot;</span>
    <span class="n">FILE_SYSTEMS</span> <span class="o">=</span> <span class="s2">&quot;file_systems&quot;</span>
    <span class="n">INDEXEDDB</span> <span class="o">=</span> <span class="s2">&quot;indexeddb&quot;</span>
    <span class="n">LOCAL_STORAGE</span> <span class="o">=</span> <span class="s2">&quot;local_storage&quot;</span>
    <span class="n">SHADER_CACHE</span> <span class="o">=</span> <span class="s2">&quot;shader_cache&quot;</span>
    <span class="n">WEBSQL</span> <span class="o">=</span> <span class="s2">&quot;websql&quot;</span>
    <span class="n">SERVICE_WORKERS</span> <span class="o">=</span> <span class="s2">&quot;service_workers&quot;</span>
    <span class="n">CACHE_STORAGE</span> <span class="o">=</span> <span class="s2">&quot;cache_storage&quot;</span>
    <span class="n">INTEREST_GROUPS</span> <span class="o">=</span> <span class="s2">&quot;interest_groups&quot;</span>
    <span class="n">SHARED_STORAGE</span> <span class="o">=</span> <span class="s2">&quot;shared_storage&quot;</span>
    <span class="n">STORAGE_BUCKETS</span> <span class="o">=</span> <span class="s2">&quot;storage_buckets&quot;</span>
    <span class="n">ALL_</span> <span class="o">=</span> <span class="s2">&quot;all&quot;</span>
    <span class="n">OTHER</span> <span class="o">=</span> <span class="s2">&quot;other&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">StorageType</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span></div>



<div class="viewcode-block" id="UsageForType">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.UsageForType">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">UsageForType</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Usage for a storage type.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: Name of storage type.</span>
    <span class="n">storage_type</span><span class="p">:</span> <span class="n">StorageType</span>

    <span class="c1">#: Storage usage (bytes).</span>
    <span class="n">usage</span><span class="p">:</span> <span class="nb">float</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;storageType&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">storage_type</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;usage&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">usage</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">UsageForType</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">storage_type</span><span class="o">=</span><span class="n">StorageType</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;storageType&quot;</span><span class="p">]),</span>
            <span class="n">usage</span><span class="o">=</span><span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;usage&quot;</span><span class="p">]),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="TrustTokens">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.TrustTokens">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">TrustTokens</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Pair of issuer origin and number of available (signed, but not used) Trust</span>
<span class="sd">    Tokens from that issuer.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">issuer_origin</span><span class="p">:</span> <span class="nb">str</span>

    <span class="n">count</span><span class="p">:</span> <span class="nb">float</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;issuerOrigin&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">issuer_origin</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;count&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">count</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">TrustTokens</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">issuer_origin</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;issuerOrigin&quot;</span><span class="p">]),</span>
            <span class="n">count</span><span class="o">=</span><span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;count&quot;</span><span class="p">]),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="InterestGroupAuctionId">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.InterestGroupAuctionId">[docs]</a>
<span class="k">class</span> <span class="nc">InterestGroupAuctionId</span><span class="p">(</span><span class="nb">str</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Protected audience interest group auction identifier.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">InterestGroupAuctionId</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span>

    <span class="k">def</span> <span class="fm">__repr__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="s2">&quot;InterestGroupAuctionId(</span><span class="si">{}</span><span class="s2">)&quot;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="fm">__repr__</span><span class="p">())</span></div>



<div class="viewcode-block" id="InterestGroupAccessType">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.InterestGroupAccessType">[docs]</a>
<span class="k">class</span> <span class="nc">InterestGroupAccessType</span><span class="p">(</span><span class="n">enum</span><span class="o">.</span><span class="n">Enum</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Enum of interest group access types.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">JOIN</span> <span class="o">=</span> <span class="s2">&quot;join&quot;</span>
    <span class="n">LEAVE</span> <span class="o">=</span> <span class="s2">&quot;leave&quot;</span>
    <span class="n">UPDATE</span> <span class="o">=</span> <span class="s2">&quot;update&quot;</span>
    <span class="n">LOADED</span> <span class="o">=</span> <span class="s2">&quot;loaded&quot;</span>
    <span class="n">BID</span> <span class="o">=</span> <span class="s2">&quot;bid&quot;</span>
    <span class="n">WIN</span> <span class="o">=</span> <span class="s2">&quot;win&quot;</span>
    <span class="n">ADDITIONAL_BID</span> <span class="o">=</span> <span class="s2">&quot;additionalBid&quot;</span>
    <span class="n">ADDITIONAL_BID_WIN</span> <span class="o">=</span> <span class="s2">&quot;additionalBidWin&quot;</span>
    <span class="n">TOP_LEVEL_BID</span> <span class="o">=</span> <span class="s2">&quot;topLevelBid&quot;</span>
    <span class="n">TOP_LEVEL_ADDITIONAL_BID</span> <span class="o">=</span> <span class="s2">&quot;topLevelAdditionalBid&quot;</span>
    <span class="n">CLEAR</span> <span class="o">=</span> <span class="s2">&quot;clear&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">InterestGroupAccessType</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span></div>



<div class="viewcode-block" id="InterestGroupAuctionEventType">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.InterestGroupAuctionEventType">[docs]</a>
<span class="k">class</span> <span class="nc">InterestGroupAuctionEventType</span><span class="p">(</span><span class="n">enum</span><span class="o">.</span><span class="n">Enum</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Enum of auction events.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">STARTED</span> <span class="o">=</span> <span class="s2">&quot;started&quot;</span>
    <span class="n">CONFIG_RESOLVED</span> <span class="o">=</span> <span class="s2">&quot;configResolved&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">InterestGroupAuctionEventType</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span></div>



<div class="viewcode-block" id="InterestGroupAuctionFetchType">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.InterestGroupAuctionFetchType">[docs]</a>
<span class="k">class</span> <span class="nc">InterestGroupAuctionFetchType</span><span class="p">(</span><span class="n">enum</span><span class="o">.</span><span class="n">Enum</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Enum of network fetches auctions can do.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">BIDDER_JS</span> <span class="o">=</span> <span class="s2">&quot;bidderJs&quot;</span>
    <span class="n">BIDDER_WASM</span> <span class="o">=</span> <span class="s2">&quot;bidderWasm&quot;</span>
    <span class="n">SELLER_JS</span> <span class="o">=</span> <span class="s2">&quot;sellerJs&quot;</span>
    <span class="n">BIDDER_TRUSTED_SIGNALS</span> <span class="o">=</span> <span class="s2">&quot;bidderTrustedSignals&quot;</span>
    <span class="n">SELLER_TRUSTED_SIGNALS</span> <span class="o">=</span> <span class="s2">&quot;sellerTrustedSignals&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">InterestGroupAuctionFetchType</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span></div>



<div class="viewcode-block" id="SharedStorageAccessType">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.SharedStorageAccessType">[docs]</a>
<span class="k">class</span> <span class="nc">SharedStorageAccessType</span><span class="p">(</span><span class="n">enum</span><span class="o">.</span><span class="n">Enum</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Enum of shared storage access types.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">DOCUMENT_ADD_MODULE</span> <span class="o">=</span> <span class="s2">&quot;documentAddModule&quot;</span>
    <span class="n">DOCUMENT_SELECT_URL</span> <span class="o">=</span> <span class="s2">&quot;documentSelectURL&quot;</span>
    <span class="n">DOCUMENT_RUN</span> <span class="o">=</span> <span class="s2">&quot;documentRun&quot;</span>
    <span class="n">DOCUMENT_SET</span> <span class="o">=</span> <span class="s2">&quot;documentSet&quot;</span>
    <span class="n">DOCUMENT_APPEND</span> <span class="o">=</span> <span class="s2">&quot;documentAppend&quot;</span>
    <span class="n">DOCUMENT_DELETE</span> <span class="o">=</span> <span class="s2">&quot;documentDelete&quot;</span>
    <span class="n">DOCUMENT_CLEAR</span> <span class="o">=</span> <span class="s2">&quot;documentClear&quot;</span>
    <span class="n">DOCUMENT_GET</span> <span class="o">=</span> <span class="s2">&quot;documentGet&quot;</span>
    <span class="n">WORKLET_SET</span> <span class="o">=</span> <span class="s2">&quot;workletSet&quot;</span>
    <span class="n">WORKLET_APPEND</span> <span class="o">=</span> <span class="s2">&quot;workletAppend&quot;</span>
    <span class="n">WORKLET_DELETE</span> <span class="o">=</span> <span class="s2">&quot;workletDelete&quot;</span>
    <span class="n">WORKLET_CLEAR</span> <span class="o">=</span> <span class="s2">&quot;workletClear&quot;</span>
    <span class="n">WORKLET_GET</span> <span class="o">=</span> <span class="s2">&quot;workletGet&quot;</span>
    <span class="n">WORKLET_KEYS</span> <span class="o">=</span> <span class="s2">&quot;workletKeys&quot;</span>
    <span class="n">WORKLET_ENTRIES</span> <span class="o">=</span> <span class="s2">&quot;workletEntries&quot;</span>
    <span class="n">WORKLET_LENGTH</span> <span class="o">=</span> <span class="s2">&quot;workletLength&quot;</span>
    <span class="n">WORKLET_REMAINING_BUDGET</span> <span class="o">=</span> <span class="s2">&quot;workletRemainingBudget&quot;</span>
    <span class="n">HEADER_SET</span> <span class="o">=</span> <span class="s2">&quot;headerSet&quot;</span>
    <span class="n">HEADER_APPEND</span> <span class="o">=</span> <span class="s2">&quot;headerAppend&quot;</span>
    <span class="n">HEADER_DELETE</span> <span class="o">=</span> <span class="s2">&quot;headerDelete&quot;</span>
    <span class="n">HEADER_CLEAR</span> <span class="o">=</span> <span class="s2">&quot;headerClear&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">SharedStorageAccessType</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span></div>



<div class="viewcode-block" id="SharedStorageEntry">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.SharedStorageEntry">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">SharedStorageEntry</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Struct for a single key-value pair in an origin&#39;s shared storage.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">key</span><span class="p">:</span> <span class="nb">str</span>

    <span class="n">value</span><span class="p">:</span> <span class="nb">str</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;key&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">key</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;value&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">SharedStorageEntry</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">key</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;key&quot;</span><span class="p">]),</span>
            <span class="n">value</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;value&quot;</span><span class="p">]),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="SharedStorageMetadata">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.SharedStorageMetadata">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">SharedStorageMetadata</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Details for an origin&#39;s shared storage.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: Time when the origin&#39;s shared storage was last created.</span>
    <span class="n">creation_time</span><span class="p">:</span> <span class="n">network</span><span class="o">.</span><span class="n">TimeSinceEpoch</span>

    <span class="c1">#: Number of key-value pairs stored in origin&#39;s shared storage.</span>
    <span class="n">length</span><span class="p">:</span> <span class="nb">int</span>

    <span class="c1">#: Current amount of bits of entropy remaining in the navigation budget.</span>
    <span class="n">remaining_budget</span><span class="p">:</span> <span class="nb">float</span>

    <span class="c1">#: Total number of bytes stored as key-value pairs in origin&#39;s shared</span>
    <span class="c1">#: storage.</span>
    <span class="n">bytes_used</span><span class="p">:</span> <span class="nb">int</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;creationTime&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">creation_time</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;length&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">length</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;remainingBudget&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">remaining_budget</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;bytesUsed&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">bytes_used</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">SharedStorageMetadata</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">creation_time</span><span class="o">=</span><span class="n">network</span><span class="o">.</span><span class="n">TimeSinceEpoch</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;creationTime&quot;</span><span class="p">]),</span>
            <span class="n">length</span><span class="o">=</span><span class="nb">int</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;length&quot;</span><span class="p">]),</span>
            <span class="n">remaining_budget</span><span class="o">=</span><span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;remainingBudget&quot;</span><span class="p">]),</span>
            <span class="n">bytes_used</span><span class="o">=</span><span class="nb">int</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;bytesUsed&quot;</span><span class="p">]),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="SharedStorageReportingMetadata">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.SharedStorageReportingMetadata">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">SharedStorageReportingMetadata</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Pair of reporting metadata details for a candidate URL for ``selectURL()``.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">event_type</span><span class="p">:</span> <span class="nb">str</span>

    <span class="n">reporting_url</span><span class="p">:</span> <span class="nb">str</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;eventType&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">event_type</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;reportingUrl&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">reporting_url</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">SharedStorageReportingMetadata</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">event_type</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;eventType&quot;</span><span class="p">]),</span>
            <span class="n">reporting_url</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;reportingUrl&quot;</span><span class="p">]),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="SharedStorageUrlWithMetadata">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.SharedStorageUrlWithMetadata">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">SharedStorageUrlWithMetadata</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Bundles a candidate URL with its reporting metadata.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: Spec of candidate URL.</span>
    <span class="n">url</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: Any associated reporting metadata.</span>
    <span class="n">reporting_metadata</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">SharedStorageReportingMetadata</span><span class="p">]</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;url&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">url</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;reportingMetadata&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">reporting_metadata</span><span class="p">]</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">SharedStorageUrlWithMetadata</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">url</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;url&quot;</span><span class="p">]),</span>
            <span class="n">reporting_metadata</span><span class="o">=</span><span class="p">[</span>
                <span class="n">SharedStorageReportingMetadata</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span>
                <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;reportingMetadata&quot;</span><span class="p">]</span>
            <span class="p">],</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="SharedStorageAccessParams">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.SharedStorageAccessParams">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">SharedStorageAccessParams</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Bundles the parameters for shared storage access events whose</span>
<span class="sd">    presence/absence can vary according to SharedStorageAccessType.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: Spec of the module script URL.</span>
    <span class="c1">#: Present only for SharedStorageAccessType.documentAddModule.</span>
    <span class="n">script_source_url</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: Name of the registered operation to be run.</span>
    <span class="c1">#: Present only for SharedStorageAccessType.documentRun and</span>
    <span class="c1">#: SharedStorageAccessType.documentSelectURL.</span>
    <span class="n">operation_name</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: The operation&#39;s serialized data in bytes (converted to a string).</span>
    <span class="c1">#: Present only for SharedStorageAccessType.documentRun and</span>
    <span class="c1">#: SharedStorageAccessType.documentSelectURL.</span>
    <span class="n">serialized_data</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: Array of candidate URLs&#39; specs, along with any associated metadata.</span>
    <span class="c1">#: Present only for SharedStorageAccessType.documentSelectURL.</span>
    <span class="n">urls_with_metadata</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">SharedStorageUrlWithMetadata</span><span class="p">]]</span> <span class="o">=</span> <span class="p">(</span>
        <span class="kc">None</span>
    <span class="p">)</span>

    <span class="c1">#: Key for a specific entry in an origin&#39;s shared storage.</span>
    <span class="c1">#: Present only for SharedStorageAccessType.documentSet,</span>
    <span class="c1">#: SharedStorageAccessType.documentAppend,</span>
    <span class="c1">#: SharedStorageAccessType.documentDelete,</span>
    <span class="c1">#: SharedStorageAccessType.workletSet,</span>
    <span class="c1">#: SharedStorageAccessType.workletAppend,</span>
    <span class="c1">#: SharedStorageAccessType.workletDelete,</span>
    <span class="c1">#: SharedStorageAccessType.workletGet,</span>
    <span class="c1">#: SharedStorageAccessType.headerSet,</span>
    <span class="c1">#: SharedStorageAccessType.headerAppend, and</span>
    <span class="c1">#: SharedStorageAccessType.headerDelete.</span>
    <span class="n">key</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: Value for a specific entry in an origin&#39;s shared storage.</span>
    <span class="c1">#: Present only for SharedStorageAccessType.documentSet,</span>
    <span class="c1">#: SharedStorageAccessType.documentAppend,</span>
    <span class="c1">#: SharedStorageAccessType.workletSet,</span>
    <span class="c1">#: SharedStorageAccessType.workletAppend,</span>
    <span class="c1">#: SharedStorageAccessType.headerSet, and</span>
    <span class="c1">#: SharedStorageAccessType.headerAppend.</span>
    <span class="n">value</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1">#: Whether or not to set an entry for a key if that key is already present.</span>
    <span class="c1">#: Present only for SharedStorageAccessType.documentSet,</span>
    <span class="c1">#: SharedStorageAccessType.workletSet, and</span>
    <span class="c1">#: SharedStorageAccessType.headerSet.</span>
    <span class="n">ignore_if_present</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">bool</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">script_source_url</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;scriptSourceUrl&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">script_source_url</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">operation_name</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;operationName&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">operation_name</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">serialized_data</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;serializedData&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">serialized_data</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">urls_with_metadata</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;urlsWithMetadata&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">urls_with_metadata</span><span class="p">]</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">key</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;key&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">key</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;value&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">ignore_if_present</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;ignoreIfPresent&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">ignore_if_present</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">SharedStorageAccessParams</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">script_source_url</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;scriptSourceUrl&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;scriptSourceUrl&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">operation_name</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;operationName&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;operationName&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">serialized_data</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;serializedData&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;serializedData&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">urls_with_metadata</span><span class="o">=</span><span class="p">(</span>
                <span class="p">[</span>
                    <span class="n">SharedStorageUrlWithMetadata</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span>
                    <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;urlsWithMetadata&quot;</span><span class="p">]</span>
                <span class="p">]</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;urlsWithMetadata&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">key</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;key&quot;</span><span class="p">])</span> <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;key&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span> <span class="k">else</span> <span class="kc">None</span><span class="p">,</span>
            <span class="n">value</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;value&quot;</span><span class="p">])</span> <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;value&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span> <span class="k">else</span> <span class="kc">None</span><span class="p">,</span>
            <span class="n">ignore_if_present</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">bool</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;ignoreIfPresent&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;ignoreIfPresent&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="StorageBucketsDurability">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.StorageBucketsDurability">[docs]</a>
<span class="k">class</span> <span class="nc">StorageBucketsDurability</span><span class="p">(</span><span class="n">enum</span><span class="o">.</span><span class="n">Enum</span><span class="p">):</span>
    <span class="n">RELAXED</span> <span class="o">=</span> <span class="s2">&quot;relaxed&quot;</span>
    <span class="n">STRICT</span> <span class="o">=</span> <span class="s2">&quot;strict&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">StorageBucketsDurability</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span></div>



<div class="viewcode-block" id="StorageBucket">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.StorageBucket">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">StorageBucket</span><span class="p">:</span>
    <span class="n">storage_key</span><span class="p">:</span> <span class="n">SerializedStorageKey</span>

    <span class="c1">#: If not specified, it is the default bucket of the storageKey.</span>
    <span class="n">name</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;storageKey&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">storage_key</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">name</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;name&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">name</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">StorageBucket</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">storage_key</span><span class="o">=</span><span class="n">SerializedStorageKey</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;storageKey&quot;</span><span class="p">]),</span>
            <span class="n">name</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;name&quot;</span><span class="p">])</span> <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;name&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span> <span class="k">else</span> <span class="kc">None</span><span class="p">,</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="StorageBucketInfo">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.StorageBucketInfo">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">StorageBucketInfo</span><span class="p">:</span>
    <span class="n">bucket</span><span class="p">:</span> <span class="n">StorageBucket</span>

    <span class="n">id_</span><span class="p">:</span> <span class="nb">str</span>

    <span class="n">expiration</span><span class="p">:</span> <span class="n">network</span><span class="o">.</span><span class="n">TimeSinceEpoch</span>

    <span class="c1">#: Storage quota (bytes).</span>
    <span class="n">quota</span><span class="p">:</span> <span class="nb">float</span>

    <span class="n">persistent</span><span class="p">:</span> <span class="nb">bool</span>

    <span class="n">durability</span><span class="p">:</span> <span class="n">StorageBucketsDurability</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;bucket&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">bucket</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;id&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">id_</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;expiration&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">expiration</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;quota&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">quota</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;persistent&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">persistent</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;durability&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">durability</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">StorageBucketInfo</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">bucket</span><span class="o">=</span><span class="n">StorageBucket</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;bucket&quot;</span><span class="p">]),</span>
            <span class="n">id_</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;id&quot;</span><span class="p">]),</span>
            <span class="n">expiration</span><span class="o">=</span><span class="n">network</span><span class="o">.</span><span class="n">TimeSinceEpoch</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;expiration&quot;</span><span class="p">]),</span>
            <span class="n">quota</span><span class="o">=</span><span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;quota&quot;</span><span class="p">]),</span>
            <span class="n">persistent</span><span class="o">=</span><span class="nb">bool</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;persistent&quot;</span><span class="p">]),</span>
            <span class="n">durability</span><span class="o">=</span><span class="n">StorageBucketsDurability</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;durability&quot;</span><span class="p">]),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="AttributionReportingSourceType">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.AttributionReportingSourceType">[docs]</a>
<span class="k">class</span> <span class="nc">AttributionReportingSourceType</span><span class="p">(</span><span class="n">enum</span><span class="o">.</span><span class="n">Enum</span><span class="p">):</span>
    <span class="n">NAVIGATION</span> <span class="o">=</span> <span class="s2">&quot;navigation&quot;</span>
    <span class="n">EVENT</span> <span class="o">=</span> <span class="s2">&quot;event&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">AttributionReportingSourceType</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span></div>



<div class="viewcode-block" id="UnsignedInt64AsBase10">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.UnsignedInt64AsBase10">[docs]</a>
<span class="k">class</span> <span class="nc">UnsignedInt64AsBase10</span><span class="p">(</span><span class="nb">str</span><span class="p">):</span>
    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">UnsignedInt64AsBase10</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span>

    <span class="k">def</span> <span class="fm">__repr__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="s2">&quot;UnsignedInt64AsBase10(</span><span class="si">{}</span><span class="s2">)&quot;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="fm">__repr__</span><span class="p">())</span></div>



<div class="viewcode-block" id="UnsignedInt128AsBase16">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.UnsignedInt128AsBase16">[docs]</a>
<span class="k">class</span> <span class="nc">UnsignedInt128AsBase16</span><span class="p">(</span><span class="nb">str</span><span class="p">):</span>
    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">UnsignedInt128AsBase16</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span>

    <span class="k">def</span> <span class="fm">__repr__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="s2">&quot;UnsignedInt128AsBase16(</span><span class="si">{}</span><span class="s2">)&quot;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="fm">__repr__</span><span class="p">())</span></div>



<div class="viewcode-block" id="SignedInt64AsBase10">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.SignedInt64AsBase10">[docs]</a>
<span class="k">class</span> <span class="nc">SignedInt64AsBase10</span><span class="p">(</span><span class="nb">str</span><span class="p">):</span>
    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">SignedInt64AsBase10</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span>

    <span class="k">def</span> <span class="fm">__repr__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="s2">&quot;SignedInt64AsBase10(</span><span class="si">{}</span><span class="s2">)&quot;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="fm">__repr__</span><span class="p">())</span></div>



<div class="viewcode-block" id="AttributionReportingFilterDataEntry">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.AttributionReportingFilterDataEntry">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">AttributionReportingFilterDataEntry</span><span class="p">:</span>
    <span class="n">key</span><span class="p">:</span> <span class="nb">str</span>

    <span class="n">values</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;key&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">key</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;values&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">values</span><span class="p">]</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">AttributionReportingFilterDataEntry</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">key</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;key&quot;</span><span class="p">]),</span>
            <span class="n">values</span><span class="o">=</span><span class="p">[</span><span class="nb">str</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;values&quot;</span><span class="p">]],</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="AttributionReportingFilterConfig">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.AttributionReportingFilterConfig">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">AttributionReportingFilterConfig</span><span class="p">:</span>
    <span class="n">filter_values</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">AttributionReportingFilterDataEntry</span><span class="p">]</span>

    <span class="c1">#: duration in seconds</span>
    <span class="n">lookback_window</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">int</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;filterValues&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">filter_values</span><span class="p">]</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">lookback_window</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;lookbackWindow&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">lookback_window</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">AttributionReportingFilterConfig</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">filter_values</span><span class="o">=</span><span class="p">[</span>
                <span class="n">AttributionReportingFilterDataEntry</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span>
                <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;filterValues&quot;</span><span class="p">]</span>
            <span class="p">],</span>
            <span class="n">lookback_window</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">int</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;lookbackWindow&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;lookbackWindow&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="AttributionReportingFilterPair">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.AttributionReportingFilterPair">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">AttributionReportingFilterPair</span><span class="p">:</span>
    <span class="n">filters</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">AttributionReportingFilterConfig</span><span class="p">]</span>

    <span class="n">not_filters</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">AttributionReportingFilterConfig</span><span class="p">]</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;filters&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">filters</span><span class="p">]</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;notFilters&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">not_filters</span><span class="p">]</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">AttributionReportingFilterPair</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">filters</span><span class="o">=</span><span class="p">[</span>
                <span class="n">AttributionReportingFilterConfig</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;filters&quot;</span><span class="p">]</span>
            <span class="p">],</span>
            <span class="n">not_filters</span><span class="o">=</span><span class="p">[</span>
                <span class="n">AttributionReportingFilterConfig</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span>
                <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;notFilters&quot;</span><span class="p">]</span>
            <span class="p">],</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="AttributionReportingAggregationKeysEntry">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.AttributionReportingAggregationKeysEntry">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">AttributionReportingAggregationKeysEntry</span><span class="p">:</span>
    <span class="n">key</span><span class="p">:</span> <span class="nb">str</span>

    <span class="n">value</span><span class="p">:</span> <span class="n">UnsignedInt128AsBase16</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;key&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">key</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;value&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">AttributionReportingAggregationKeysEntry</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">key</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;key&quot;</span><span class="p">]),</span>
            <span class="n">value</span><span class="o">=</span><span class="n">UnsignedInt128AsBase16</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;value&quot;</span><span class="p">]),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="AttributionReportingEventReportWindows">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.AttributionReportingEventReportWindows">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">AttributionReportingEventReportWindows</span><span class="p">:</span>
    <span class="c1">#: duration in seconds</span>
    <span class="n">start</span><span class="p">:</span> <span class="nb">int</span>

    <span class="c1">#: duration in seconds</span>
    <span class="n">ends</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="nb">int</span><span class="p">]</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;start&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">start</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;ends&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">ends</span><span class="p">]</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">AttributionReportingEventReportWindows</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">start</span><span class="o">=</span><span class="nb">int</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;start&quot;</span><span class="p">]),</span>
            <span class="n">ends</span><span class="o">=</span><span class="p">[</span><span class="nb">int</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;ends&quot;</span><span class="p">]],</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="AttributionReportingTriggerSpec">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.AttributionReportingTriggerSpec">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">AttributionReportingTriggerSpec</span><span class="p">:</span>
    <span class="c1">#: number instead of integer because not all uint32 can be represented by</span>
    <span class="c1">#: int</span>
    <span class="n">trigger_data</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="nb">float</span><span class="p">]</span>

    <span class="n">event_report_windows</span><span class="p">:</span> <span class="n">AttributionReportingEventReportWindows</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;triggerData&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">trigger_data</span><span class="p">]</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;eventReportWindows&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">event_report_windows</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">AttributionReportingTriggerSpec</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">trigger_data</span><span class="o">=</span><span class="p">[</span><span class="nb">float</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;triggerData&quot;</span><span class="p">]],</span>
            <span class="n">event_report_windows</span><span class="o">=</span><span class="n">AttributionReportingEventReportWindows</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span>
                <span class="n">json</span><span class="p">[</span><span class="s2">&quot;eventReportWindows&quot;</span><span class="p">]</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="AttributionReportingTriggerDataMatching">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.AttributionReportingTriggerDataMatching">[docs]</a>
<span class="k">class</span> <span class="nc">AttributionReportingTriggerDataMatching</span><span class="p">(</span><span class="n">enum</span><span class="o">.</span><span class="n">Enum</span><span class="p">):</span>
    <span class="n">EXACT</span> <span class="o">=</span> <span class="s2">&quot;exact&quot;</span>
    <span class="n">MODULUS</span> <span class="o">=</span> <span class="s2">&quot;modulus&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">AttributionReportingTriggerDataMatching</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span></div>



<div class="viewcode-block" id="AttributionReportingAggregatableDebugReportingData">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.AttributionReportingAggregatableDebugReportingData">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">AttributionReportingAggregatableDebugReportingData</span><span class="p">:</span>
    <span class="n">key_piece</span><span class="p">:</span> <span class="n">UnsignedInt128AsBase16</span>

    <span class="c1">#: number instead of integer because not all uint32 can be represented by</span>
    <span class="c1">#: int</span>
    <span class="n">value</span><span class="p">:</span> <span class="nb">float</span>

    <span class="n">types</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;keyPiece&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">key_piece</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;value&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;types&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">types</span><span class="p">]</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span>
            <span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span>
    <span class="p">)</span> <span class="o">-&gt;</span> <span class="n">AttributionReportingAggregatableDebugReportingData</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">key_piece</span><span class="o">=</span><span class="n">UnsignedInt128AsBase16</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;keyPiece&quot;</span><span class="p">]),</span>
            <span class="n">value</span><span class="o">=</span><span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;value&quot;</span><span class="p">]),</span>
            <span class="n">types</span><span class="o">=</span><span class="p">[</span><span class="nb">str</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;types&quot;</span><span class="p">]],</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="AttributionReportingAggregatableDebugReportingConfig">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.AttributionReportingAggregatableDebugReportingConfig">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">AttributionReportingAggregatableDebugReportingConfig</span><span class="p">:</span>
    <span class="n">key_piece</span><span class="p">:</span> <span class="n">UnsignedInt128AsBase16</span>

    <span class="n">debug_data</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">AttributionReportingAggregatableDebugReportingData</span><span class="p">]</span>

    <span class="c1">#: number instead of integer because not all uint32 can be represented by</span>
    <span class="c1">#: int, only present for source registrations</span>
    <span class="n">budget</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">float</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="n">aggregation_coordinator_origin</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;keyPiece&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">key_piece</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;debugData&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">debug_data</span><span class="p">]</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">budget</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;budget&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">budget</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">aggregation_coordinator_origin</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;aggregationCoordinatorOrigin&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">aggregation_coordinator_origin</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span>
            <span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span>
    <span class="p">)</span> <span class="o">-&gt;</span> <span class="n">AttributionReportingAggregatableDebugReportingConfig</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">key_piece</span><span class="o">=</span><span class="n">UnsignedInt128AsBase16</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;keyPiece&quot;</span><span class="p">]),</span>
            <span class="n">debug_data</span><span class="o">=</span><span class="p">[</span>
                <span class="n">AttributionReportingAggregatableDebugReportingData</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span>
                <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;debugData&quot;</span><span class="p">]</span>
            <span class="p">],</span>
            <span class="n">budget</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;budget&quot;</span><span class="p">])</span> <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;budget&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span> <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">aggregation_coordinator_origin</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;aggregationCoordinatorOrigin&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;aggregationCoordinatorOrigin&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="AttributionScopesData">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.AttributionScopesData">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">AttributionScopesData</span><span class="p">:</span>
    <span class="n">values</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span>

    <span class="c1">#: number instead of integer because not all uint32 can be represented by</span>
    <span class="c1">#: int</span>
    <span class="n">limit</span><span class="p">:</span> <span class="nb">float</span>

    <span class="n">max_event_states</span><span class="p">:</span> <span class="nb">float</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;values&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">values</span><span class="p">]</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;limit&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">limit</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;maxEventStates&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">max_event_states</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">AttributionScopesData</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">values</span><span class="o">=</span><span class="p">[</span><span class="nb">str</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;values&quot;</span><span class="p">]],</span>
            <span class="n">limit</span><span class="o">=</span><span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;limit&quot;</span><span class="p">]),</span>
            <span class="n">max_event_states</span><span class="o">=</span><span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;maxEventStates&quot;</span><span class="p">]),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="AttributionReportingSourceRegistration">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.AttributionReportingSourceRegistration">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">AttributionReportingSourceRegistration</span><span class="p">:</span>
    <span class="n">time</span><span class="p">:</span> <span class="n">network</span><span class="o">.</span><span class="n">TimeSinceEpoch</span>

    <span class="c1">#: duration in seconds</span>
    <span class="n">expiry</span><span class="p">:</span> <span class="nb">int</span>

    <span class="n">trigger_specs</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">AttributionReportingTriggerSpec</span><span class="p">]</span>

    <span class="c1">#: duration in seconds</span>
    <span class="n">aggregatable_report_window</span><span class="p">:</span> <span class="nb">int</span>

    <span class="n">type_</span><span class="p">:</span> <span class="n">AttributionReportingSourceType</span>

    <span class="n">source_origin</span><span class="p">:</span> <span class="nb">str</span>

    <span class="n">reporting_origin</span><span class="p">:</span> <span class="nb">str</span>

    <span class="n">destination_sites</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span>

    <span class="n">event_id</span><span class="p">:</span> <span class="n">UnsignedInt64AsBase10</span>

    <span class="n">priority</span><span class="p">:</span> <span class="n">SignedInt64AsBase10</span>

    <span class="n">filter_data</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">AttributionReportingFilterDataEntry</span><span class="p">]</span>

    <span class="n">aggregation_keys</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">AttributionReportingAggregationKeysEntry</span><span class="p">]</span>

    <span class="n">trigger_data_matching</span><span class="p">:</span> <span class="n">AttributionReportingTriggerDataMatching</span>

    <span class="n">destination_limit_priority</span><span class="p">:</span> <span class="n">SignedInt64AsBase10</span>

    <span class="n">aggregatable_debug_reporting_config</span><span class="p">:</span> <span class="p">(</span>
        <span class="n">AttributionReportingAggregatableDebugReportingConfig</span>
    <span class="p">)</span>

    <span class="n">max_event_level_reports</span><span class="p">:</span> <span class="nb">int</span>

    <span class="n">debug_key</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">UnsignedInt64AsBase10</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="n">scopes_data</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">AttributionScopesData</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;time&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">time</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;expiry&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">expiry</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;triggerSpecs&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">trigger_specs</span><span class="p">]</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;aggregatableReportWindow&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">aggregatable_report_window</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;type&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">type_</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;sourceOrigin&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">source_origin</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;reportingOrigin&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">reporting_origin</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;destinationSites&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">destination_sites</span><span class="p">]</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;eventId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">event_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;priority&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">priority</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;filterData&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">filter_data</span><span class="p">]</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;aggregationKeys&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">aggregation_keys</span><span class="p">]</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;triggerDataMatching&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">trigger_data_matching</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;destinationLimitPriority&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">destination_limit_priority</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;aggregatableDebugReportingConfig&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">(</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">aggregatable_debug_reporting_config</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="p">)</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;maxEventLevelReports&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">max_event_level_reports</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">debug_key</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;debugKey&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">debug_key</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">scopes_data</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;scopesData&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">scopes_data</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">AttributionReportingSourceRegistration</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">time</span><span class="o">=</span><span class="n">network</span><span class="o">.</span><span class="n">TimeSinceEpoch</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;time&quot;</span><span class="p">]),</span>
            <span class="n">expiry</span><span class="o">=</span><span class="nb">int</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;expiry&quot;</span><span class="p">]),</span>
            <span class="n">trigger_specs</span><span class="o">=</span><span class="p">[</span>
                <span class="n">AttributionReportingTriggerSpec</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span>
                <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;triggerSpecs&quot;</span><span class="p">]</span>
            <span class="p">],</span>
            <span class="n">aggregatable_report_window</span><span class="o">=</span><span class="nb">int</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;aggregatableReportWindow&quot;</span><span class="p">]),</span>
            <span class="n">type_</span><span class="o">=</span><span class="n">AttributionReportingSourceType</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;type&quot;</span><span class="p">]),</span>
            <span class="n">source_origin</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;sourceOrigin&quot;</span><span class="p">]),</span>
            <span class="n">reporting_origin</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;reportingOrigin&quot;</span><span class="p">]),</span>
            <span class="n">destination_sites</span><span class="o">=</span><span class="p">[</span><span class="nb">str</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;destinationSites&quot;</span><span class="p">]],</span>
            <span class="n">event_id</span><span class="o">=</span><span class="n">UnsignedInt64AsBase10</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;eventId&quot;</span><span class="p">]),</span>
            <span class="n">priority</span><span class="o">=</span><span class="n">SignedInt64AsBase10</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;priority&quot;</span><span class="p">]),</span>
            <span class="n">filter_data</span><span class="o">=</span><span class="p">[</span>
                <span class="n">AttributionReportingFilterDataEntry</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span>
                <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;filterData&quot;</span><span class="p">]</span>
            <span class="p">],</span>
            <span class="n">aggregation_keys</span><span class="o">=</span><span class="p">[</span>
                <span class="n">AttributionReportingAggregationKeysEntry</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span>
                <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;aggregationKeys&quot;</span><span class="p">]</span>
            <span class="p">],</span>
            <span class="n">trigger_data_matching</span><span class="o">=</span><span class="n">AttributionReportingTriggerDataMatching</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span>
                <span class="n">json</span><span class="p">[</span><span class="s2">&quot;triggerDataMatching&quot;</span><span class="p">]</span>
            <span class="p">),</span>
            <span class="n">destination_limit_priority</span><span class="o">=</span><span class="n">SignedInt64AsBase10</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span>
                <span class="n">json</span><span class="p">[</span><span class="s2">&quot;destinationLimitPriority&quot;</span><span class="p">]</span>
            <span class="p">),</span>
            <span class="n">aggregatable_debug_reporting_config</span><span class="o">=</span><span class="n">AttributionReportingAggregatableDebugReportingConfig</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span>
                <span class="n">json</span><span class="p">[</span><span class="s2">&quot;aggregatableDebugReportingConfig&quot;</span><span class="p">]</span>
            <span class="p">),</span>
            <span class="n">max_event_level_reports</span><span class="o">=</span><span class="nb">int</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;maxEventLevelReports&quot;</span><span class="p">]),</span>
            <span class="n">debug_key</span><span class="o">=</span><span class="p">(</span>
                <span class="n">UnsignedInt64AsBase10</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;debugKey&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;debugKey&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">scopes_data</span><span class="o">=</span><span class="p">(</span>
                <span class="n">AttributionScopesData</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;scopesData&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;scopesData&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="AttributionReportingSourceRegistrationResult">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.AttributionReportingSourceRegistrationResult">[docs]</a>
<span class="k">class</span> <span class="nc">AttributionReportingSourceRegistrationResult</span><span class="p">(</span><span class="n">enum</span><span class="o">.</span><span class="n">Enum</span><span class="p">):</span>
    <span class="n">SUCCESS</span> <span class="o">=</span> <span class="s2">&quot;success&quot;</span>
    <span class="n">INTERNAL_ERROR</span> <span class="o">=</span> <span class="s2">&quot;internalError&quot;</span>
    <span class="n">INSUFFICIENT_SOURCE_CAPACITY</span> <span class="o">=</span> <span class="s2">&quot;insufficientSourceCapacity&quot;</span>
    <span class="n">INSUFFICIENT_UNIQUE_DESTINATION_CAPACITY</span> <span class="o">=</span> <span class="s2">&quot;insufficientUniqueDestinationCapacity&quot;</span>
    <span class="n">EXCESSIVE_REPORTING_ORIGINS</span> <span class="o">=</span> <span class="s2">&quot;excessiveReportingOrigins&quot;</span>
    <span class="n">PROHIBITED_BY_BROWSER_POLICY</span> <span class="o">=</span> <span class="s2">&quot;prohibitedByBrowserPolicy&quot;</span>
    <span class="n">SUCCESS_NOISED</span> <span class="o">=</span> <span class="s2">&quot;successNoised&quot;</span>
    <span class="n">DESTINATION_REPORTING_LIMIT_REACHED</span> <span class="o">=</span> <span class="s2">&quot;destinationReportingLimitReached&quot;</span>
    <span class="n">DESTINATION_GLOBAL_LIMIT_REACHED</span> <span class="o">=</span> <span class="s2">&quot;destinationGlobalLimitReached&quot;</span>
    <span class="n">DESTINATION_BOTH_LIMITS_REACHED</span> <span class="o">=</span> <span class="s2">&quot;destinationBothLimitsReached&quot;</span>
    <span class="n">REPORTING_ORIGINS_PER_SITE_LIMIT_REACHED</span> <span class="o">=</span> <span class="s2">&quot;reportingOriginsPerSiteLimitReached&quot;</span>
    <span class="n">EXCEEDS_MAX_CHANNEL_CAPACITY</span> <span class="o">=</span> <span class="s2">&quot;exceedsMaxChannelCapacity&quot;</span>
    <span class="n">EXCEEDS_MAX_SCOPES_CHANNEL_CAPACITY</span> <span class="o">=</span> <span class="s2">&quot;exceedsMaxScopesChannelCapacity&quot;</span>
    <span class="n">EXCEEDS_MAX_TRIGGER_STATE_CARDINALITY</span> <span class="o">=</span> <span class="s2">&quot;exceedsMaxTriggerStateCardinality&quot;</span>
    <span class="n">EXCEEDS_MAX_EVENT_STATES_LIMIT</span> <span class="o">=</span> <span class="s2">&quot;exceedsMaxEventStatesLimit&quot;</span>
    <span class="n">DESTINATION_PER_DAY_REPORTING_LIMIT_REACHED</span> <span class="o">=</span> <span class="p">(</span>
        <span class="s2">&quot;destinationPerDayReportingLimitReached&quot;</span>
    <span class="p">)</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">AttributionReportingSourceRegistrationResult</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span></div>



<div class="viewcode-block" id="AttributionReportingSourceRegistrationTimeConfig">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.AttributionReportingSourceRegistrationTimeConfig">[docs]</a>
<span class="k">class</span> <span class="nc">AttributionReportingSourceRegistrationTimeConfig</span><span class="p">(</span><span class="n">enum</span><span class="o">.</span><span class="n">Enum</span><span class="p">):</span>
    <span class="n">INCLUDE</span> <span class="o">=</span> <span class="s2">&quot;include&quot;</span>
    <span class="n">EXCLUDE</span> <span class="o">=</span> <span class="s2">&quot;exclude&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">AttributionReportingSourceRegistrationTimeConfig</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span></div>



<div class="viewcode-block" id="AttributionReportingAggregatableValueDictEntry">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.AttributionReportingAggregatableValueDictEntry">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">AttributionReportingAggregatableValueDictEntry</span><span class="p">:</span>
    <span class="n">key</span><span class="p">:</span> <span class="nb">str</span>

    <span class="c1">#: number instead of integer because not all uint32 can be represented by</span>
    <span class="c1">#: int</span>
    <span class="n">value</span><span class="p">:</span> <span class="nb">float</span>

    <span class="n">filtering_id</span><span class="p">:</span> <span class="n">UnsignedInt64AsBase10</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;key&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">key</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;value&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;filteringId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">filtering_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span>
            <span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span>
    <span class="p">)</span> <span class="o">-&gt;</span> <span class="n">AttributionReportingAggregatableValueDictEntry</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">key</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;key&quot;</span><span class="p">]),</span>
            <span class="n">value</span><span class="o">=</span><span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;value&quot;</span><span class="p">]),</span>
            <span class="n">filtering_id</span><span class="o">=</span><span class="n">UnsignedInt64AsBase10</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;filteringId&quot;</span><span class="p">]),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="AttributionReportingAggregatableValueEntry">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.AttributionReportingAggregatableValueEntry">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">AttributionReportingAggregatableValueEntry</span><span class="p">:</span>
    <span class="n">values</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">AttributionReportingAggregatableValueDictEntry</span><span class="p">]</span>

    <span class="n">filters</span><span class="p">:</span> <span class="n">AttributionReportingFilterPair</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;values&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">values</span><span class="p">]</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;filters&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">filters</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">AttributionReportingAggregatableValueEntry</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">values</span><span class="o">=</span><span class="p">[</span>
                <span class="n">AttributionReportingAggregatableValueDictEntry</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span>
                <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;values&quot;</span><span class="p">]</span>
            <span class="p">],</span>
            <span class="n">filters</span><span class="o">=</span><span class="n">AttributionReportingFilterPair</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;filters&quot;</span><span class="p">]),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="AttributionReportingEventTriggerData">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.AttributionReportingEventTriggerData">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">AttributionReportingEventTriggerData</span><span class="p">:</span>
    <span class="n">data</span><span class="p">:</span> <span class="n">UnsignedInt64AsBase10</span>

    <span class="n">priority</span><span class="p">:</span> <span class="n">SignedInt64AsBase10</span>

    <span class="n">filters</span><span class="p">:</span> <span class="n">AttributionReportingFilterPair</span>

    <span class="n">dedup_key</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">UnsignedInt64AsBase10</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;data&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">data</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;priority&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">priority</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;filters&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">filters</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">dedup_key</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;dedupKey&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">dedup_key</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">AttributionReportingEventTriggerData</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">data</span><span class="o">=</span><span class="n">UnsignedInt64AsBase10</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;data&quot;</span><span class="p">]),</span>
            <span class="n">priority</span><span class="o">=</span><span class="n">SignedInt64AsBase10</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;priority&quot;</span><span class="p">]),</span>
            <span class="n">filters</span><span class="o">=</span><span class="n">AttributionReportingFilterPair</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;filters&quot;</span><span class="p">]),</span>
            <span class="n">dedup_key</span><span class="o">=</span><span class="p">(</span>
                <span class="n">UnsignedInt64AsBase10</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;dedupKey&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;dedupKey&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="AttributionReportingAggregatableTriggerData">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.AttributionReportingAggregatableTriggerData">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">AttributionReportingAggregatableTriggerData</span><span class="p">:</span>
    <span class="n">key_piece</span><span class="p">:</span> <span class="n">UnsignedInt128AsBase16</span>

    <span class="n">source_keys</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span>

    <span class="n">filters</span><span class="p">:</span> <span class="n">AttributionReportingFilterPair</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;keyPiece&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">key_piece</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;sourceKeys&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">source_keys</span><span class="p">]</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;filters&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">filters</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span>
            <span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span>
    <span class="p">)</span> <span class="o">-&gt;</span> <span class="n">AttributionReportingAggregatableTriggerData</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">key_piece</span><span class="o">=</span><span class="n">UnsignedInt128AsBase16</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;keyPiece&quot;</span><span class="p">]),</span>
            <span class="n">source_keys</span><span class="o">=</span><span class="p">[</span><span class="nb">str</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;sourceKeys&quot;</span><span class="p">]],</span>
            <span class="n">filters</span><span class="o">=</span><span class="n">AttributionReportingFilterPair</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;filters&quot;</span><span class="p">]),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="AttributionReportingAggregatableDedupKey">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.AttributionReportingAggregatableDedupKey">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">AttributionReportingAggregatableDedupKey</span><span class="p">:</span>
    <span class="n">filters</span><span class="p">:</span> <span class="n">AttributionReportingFilterPair</span>

    <span class="n">dedup_key</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">UnsignedInt64AsBase10</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;filters&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">filters</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">dedup_key</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;dedupKey&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">dedup_key</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">AttributionReportingAggregatableDedupKey</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">filters</span><span class="o">=</span><span class="n">AttributionReportingFilterPair</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;filters&quot;</span><span class="p">]),</span>
            <span class="n">dedup_key</span><span class="o">=</span><span class="p">(</span>
                <span class="n">UnsignedInt64AsBase10</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;dedupKey&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;dedupKey&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="AttributionReportingTriggerRegistration">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.AttributionReportingTriggerRegistration">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">AttributionReportingTriggerRegistration</span><span class="p">:</span>
    <span class="n">filters</span><span class="p">:</span> <span class="n">AttributionReportingFilterPair</span>

    <span class="n">aggregatable_dedup_keys</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">AttributionReportingAggregatableDedupKey</span><span class="p">]</span>

    <span class="n">event_trigger_data</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">AttributionReportingEventTriggerData</span><span class="p">]</span>

    <span class="n">aggregatable_trigger_data</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">AttributionReportingAggregatableTriggerData</span><span class="p">]</span>

    <span class="n">aggregatable_values</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">AttributionReportingAggregatableValueEntry</span><span class="p">]</span>

    <span class="n">aggregatable_filtering_id_max_bytes</span><span class="p">:</span> <span class="nb">int</span>

    <span class="n">debug_reporting</span><span class="p">:</span> <span class="nb">bool</span>

    <span class="n">source_registration_time_config</span><span class="p">:</span> <span class="n">AttributionReportingSourceRegistrationTimeConfig</span>

    <span class="n">aggregatable_debug_reporting_config</span><span class="p">:</span> <span class="p">(</span>
        <span class="n">AttributionReportingAggregatableDebugReportingConfig</span>
    <span class="p">)</span>

    <span class="n">scopes</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span>

    <span class="n">debug_key</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">UnsignedInt64AsBase10</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="n">aggregation_coordinator_origin</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="n">trigger_context_id</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;filters&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">filters</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;aggregatableDedupKeys&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span>
            <span class="n">i</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">aggregatable_dedup_keys</span>
        <span class="p">]</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;eventTriggerData&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">event_trigger_data</span><span class="p">]</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;aggregatableTriggerData&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span>
            <span class="n">i</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">aggregatable_trigger_data</span>
        <span class="p">]</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;aggregatableValues&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">aggregatable_values</span><span class="p">]</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;aggregatableFilteringIdMaxBytes&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">(</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">aggregatable_filtering_id_max_bytes</span>
        <span class="p">)</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;debugReporting&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">debug_reporting</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;sourceRegistrationTimeConfig&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">(</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">source_registration_time_config</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="p">)</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;aggregatableDebugReportingConfig&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">(</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">aggregatable_debug_reporting_config</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="p">)</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;scopes&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">scopes</span><span class="p">]</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">debug_key</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;debugKey&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">debug_key</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">aggregation_coordinator_origin</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;aggregationCoordinatorOrigin&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">aggregation_coordinator_origin</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">trigger_context_id</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;triggerContextId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">trigger_context_id</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">AttributionReportingTriggerRegistration</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">filters</span><span class="o">=</span><span class="n">AttributionReportingFilterPair</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;filters&quot;</span><span class="p">]),</span>
            <span class="n">aggregatable_dedup_keys</span><span class="o">=</span><span class="p">[</span>
                <span class="n">AttributionReportingAggregatableDedupKey</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span>
                <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;aggregatableDedupKeys&quot;</span><span class="p">]</span>
            <span class="p">],</span>
            <span class="n">event_trigger_data</span><span class="o">=</span><span class="p">[</span>
                <span class="n">AttributionReportingEventTriggerData</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span>
                <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;eventTriggerData&quot;</span><span class="p">]</span>
            <span class="p">],</span>
            <span class="n">aggregatable_trigger_data</span><span class="o">=</span><span class="p">[</span>
                <span class="n">AttributionReportingAggregatableTriggerData</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span>
                <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;aggregatableTriggerData&quot;</span><span class="p">]</span>
            <span class="p">],</span>
            <span class="n">aggregatable_values</span><span class="o">=</span><span class="p">[</span>
                <span class="n">AttributionReportingAggregatableValueEntry</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span>
                <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;aggregatableValues&quot;</span><span class="p">]</span>
            <span class="p">],</span>
            <span class="n">aggregatable_filtering_id_max_bytes</span><span class="o">=</span><span class="nb">int</span><span class="p">(</span>
                <span class="n">json</span><span class="p">[</span><span class="s2">&quot;aggregatableFilteringIdMaxBytes&quot;</span><span class="p">]</span>
            <span class="p">),</span>
            <span class="n">debug_reporting</span><span class="o">=</span><span class="nb">bool</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;debugReporting&quot;</span><span class="p">]),</span>
            <span class="n">source_registration_time_config</span><span class="o">=</span><span class="n">AttributionReportingSourceRegistrationTimeConfig</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span>
                <span class="n">json</span><span class="p">[</span><span class="s2">&quot;sourceRegistrationTimeConfig&quot;</span><span class="p">]</span>
            <span class="p">),</span>
            <span class="n">aggregatable_debug_reporting_config</span><span class="o">=</span><span class="n">AttributionReportingAggregatableDebugReportingConfig</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span>
                <span class="n">json</span><span class="p">[</span><span class="s2">&quot;aggregatableDebugReportingConfig&quot;</span><span class="p">]</span>
            <span class="p">),</span>
            <span class="n">scopes</span><span class="o">=</span><span class="p">[</span><span class="nb">str</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;scopes&quot;</span><span class="p">]],</span>
            <span class="n">debug_key</span><span class="o">=</span><span class="p">(</span>
                <span class="n">UnsignedInt64AsBase10</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;debugKey&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;debugKey&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">aggregation_coordinator_origin</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;aggregationCoordinatorOrigin&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;aggregationCoordinatorOrigin&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">trigger_context_id</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;triggerContextId&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;triggerContextId&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="AttributionReportingEventLevelResult">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.AttributionReportingEventLevelResult">[docs]</a>
<span class="k">class</span> <span class="nc">AttributionReportingEventLevelResult</span><span class="p">(</span><span class="n">enum</span><span class="o">.</span><span class="n">Enum</span><span class="p">):</span>
    <span class="n">SUCCESS</span> <span class="o">=</span> <span class="s2">&quot;success&quot;</span>
    <span class="n">SUCCESS_DROPPED_LOWER_PRIORITY</span> <span class="o">=</span> <span class="s2">&quot;successDroppedLowerPriority&quot;</span>
    <span class="n">INTERNAL_ERROR</span> <span class="o">=</span> <span class="s2">&quot;internalError&quot;</span>
    <span class="n">NO_CAPACITY_FOR_ATTRIBUTION_DESTINATION</span> <span class="o">=</span> <span class="s2">&quot;noCapacityForAttributionDestination&quot;</span>
    <span class="n">NO_MATCHING_SOURCES</span> <span class="o">=</span> <span class="s2">&quot;noMatchingSources&quot;</span>
    <span class="n">DEDUPLICATED</span> <span class="o">=</span> <span class="s2">&quot;deduplicated&quot;</span>
    <span class="n">EXCESSIVE_ATTRIBUTIONS</span> <span class="o">=</span> <span class="s2">&quot;excessiveAttributions&quot;</span>
    <span class="n">PRIORITY_TOO_LOW</span> <span class="o">=</span> <span class="s2">&quot;priorityTooLow&quot;</span>
    <span class="n">NEVER_ATTRIBUTED_SOURCE</span> <span class="o">=</span> <span class="s2">&quot;neverAttributedSource&quot;</span>
    <span class="n">EXCESSIVE_REPORTING_ORIGINS</span> <span class="o">=</span> <span class="s2">&quot;excessiveReportingOrigins&quot;</span>
    <span class="n">NO_MATCHING_SOURCE_FILTER_DATA</span> <span class="o">=</span> <span class="s2">&quot;noMatchingSourceFilterData&quot;</span>
    <span class="n">PROHIBITED_BY_BROWSER_POLICY</span> <span class="o">=</span> <span class="s2">&quot;prohibitedByBrowserPolicy&quot;</span>
    <span class="n">NO_MATCHING_CONFIGURATIONS</span> <span class="o">=</span> <span class="s2">&quot;noMatchingConfigurations&quot;</span>
    <span class="n">EXCESSIVE_REPORTS</span> <span class="o">=</span> <span class="s2">&quot;excessiveReports&quot;</span>
    <span class="n">FALSELY_ATTRIBUTED_SOURCE</span> <span class="o">=</span> <span class="s2">&quot;falselyAttributedSource&quot;</span>
    <span class="n">REPORT_WINDOW_PASSED</span> <span class="o">=</span> <span class="s2">&quot;reportWindowPassed&quot;</span>
    <span class="n">NOT_REGISTERED</span> <span class="o">=</span> <span class="s2">&quot;notRegistered&quot;</span>
    <span class="n">REPORT_WINDOW_NOT_STARTED</span> <span class="o">=</span> <span class="s2">&quot;reportWindowNotStarted&quot;</span>
    <span class="n">NO_MATCHING_TRIGGER_DATA</span> <span class="o">=</span> <span class="s2">&quot;noMatchingTriggerData&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">AttributionReportingEventLevelResult</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span></div>



<div class="viewcode-block" id="AttributionReportingAggregatableResult">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.AttributionReportingAggregatableResult">[docs]</a>
<span class="k">class</span> <span class="nc">AttributionReportingAggregatableResult</span><span class="p">(</span><span class="n">enum</span><span class="o">.</span><span class="n">Enum</span><span class="p">):</span>
    <span class="n">SUCCESS</span> <span class="o">=</span> <span class="s2">&quot;success&quot;</span>
    <span class="n">INTERNAL_ERROR</span> <span class="o">=</span> <span class="s2">&quot;internalError&quot;</span>
    <span class="n">NO_CAPACITY_FOR_ATTRIBUTION_DESTINATION</span> <span class="o">=</span> <span class="s2">&quot;noCapacityForAttributionDestination&quot;</span>
    <span class="n">NO_MATCHING_SOURCES</span> <span class="o">=</span> <span class="s2">&quot;noMatchingSources&quot;</span>
    <span class="n">EXCESSIVE_ATTRIBUTIONS</span> <span class="o">=</span> <span class="s2">&quot;excessiveAttributions&quot;</span>
    <span class="n">EXCESSIVE_REPORTING_ORIGINS</span> <span class="o">=</span> <span class="s2">&quot;excessiveReportingOrigins&quot;</span>
    <span class="n">NO_HISTOGRAMS</span> <span class="o">=</span> <span class="s2">&quot;noHistograms&quot;</span>
    <span class="n">INSUFFICIENT_BUDGET</span> <span class="o">=</span> <span class="s2">&quot;insufficientBudget&quot;</span>
    <span class="n">INSUFFICIENT_NAMED_BUDGET</span> <span class="o">=</span> <span class="s2">&quot;insufficientNamedBudget&quot;</span>
    <span class="n">NO_MATCHING_SOURCE_FILTER_DATA</span> <span class="o">=</span> <span class="s2">&quot;noMatchingSourceFilterData&quot;</span>
    <span class="n">NOT_REGISTERED</span> <span class="o">=</span> <span class="s2">&quot;notRegistered&quot;</span>
    <span class="n">PROHIBITED_BY_BROWSER_POLICY</span> <span class="o">=</span> <span class="s2">&quot;prohibitedByBrowserPolicy&quot;</span>
    <span class="n">DEDUPLICATED</span> <span class="o">=</span> <span class="s2">&quot;deduplicated&quot;</span>
    <span class="n">REPORT_WINDOW_PASSED</span> <span class="o">=</span> <span class="s2">&quot;reportWindowPassed&quot;</span>
    <span class="n">EXCESSIVE_REPORTS</span> <span class="o">=</span> <span class="s2">&quot;excessiveReports&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">AttributionReportingAggregatableResult</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span></div>



<div class="viewcode-block" id="RelatedWebsiteSet">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.RelatedWebsiteSet">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">RelatedWebsiteSet</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    A single Related Website Set object.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: The primary site of this set, along with the ccTLDs if there is any.</span>
    <span class="n">primary_sites</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span>

    <span class="c1">#: The associated sites of this set, along with the ccTLDs if there is any.</span>
    <span class="n">associated_sites</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span>

    <span class="c1">#: The service sites of this set, along with the ccTLDs if there is any.</span>
    <span class="n">service_sites</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;primarySites&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">primary_sites</span><span class="p">]</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;associatedSites&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">associated_sites</span><span class="p">]</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;serviceSites&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">service_sites</span><span class="p">]</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">RelatedWebsiteSet</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">primary_sites</span><span class="o">=</span><span class="p">[</span><span class="nb">str</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;primarySites&quot;</span><span class="p">]],</span>
            <span class="n">associated_sites</span><span class="o">=</span><span class="p">[</span><span class="nb">str</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;associatedSites&quot;</span><span class="p">]],</span>
            <span class="n">service_sites</span><span class="o">=</span><span class="p">[</span><span class="nb">str</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;serviceSites&quot;</span><span class="p">]],</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="get_storage_key_for_frame">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.get_storage_key_for_frame">[docs]</a>
<span class="k">def</span> <span class="nf">get_storage_key_for_frame</span><span class="p">(</span>
        <span class="n">frame_id</span><span class="p">:</span> <span class="n">page</span><span class="o">.</span><span class="n">FrameId</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">SerializedStorageKey</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Returns a storage key given a frame id.</span>

<span class="sd">    :param frame_id:</span>
<span class="sd">    :returns:</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;frameId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">frame_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Storage.getStorageKeyForFrame&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span>
    <span class="k">return</span> <span class="n">SerializedStorageKey</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;storageKey&quot;</span><span class="p">])</span></div>



<div class="viewcode-block" id="clear_data_for_origin">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.clear_data_for_origin">[docs]</a>
<span class="k">def</span> <span class="nf">clear_data_for_origin</span><span class="p">(</span>
        <span class="n">origin</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">storage_types</span><span class="p">:</span> <span class="nb">str</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Clears storage for origin.</span>

<span class="sd">    :param origin: Security origin.</span>
<span class="sd">    :param storage_types: Comma separated list of StorageType to clear.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;origin&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">origin</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;storageTypes&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">storage_types</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Storage.clearDataForOrigin&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span></div>



<div class="viewcode-block" id="clear_data_for_storage_key">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.clear_data_for_storage_key">[docs]</a>
<span class="k">def</span> <span class="nf">clear_data_for_storage_key</span><span class="p">(</span>
        <span class="n">storage_key</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">storage_types</span><span class="p">:</span> <span class="nb">str</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Clears storage for storage key.</span>

<span class="sd">    :param storage_key: Storage key.</span>
<span class="sd">    :param storage_types: Comma separated list of StorageType to clear.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;storageKey&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">storage_key</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;storageTypes&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">storage_types</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Storage.clearDataForStorageKey&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span></div>



<div class="viewcode-block" id="get_cookies">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.get_cookies">[docs]</a>
<span class="k">def</span> <span class="nf">get_cookies</span><span class="p">(</span>
        <span class="n">browser_context_id</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">browser</span><span class="o">.</span><span class="n">BrowserContextID</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">network</span><span class="o">.</span><span class="n">Cookie</span><span class="p">]]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Returns all browser cookies.</span>

<span class="sd">    :param browser_context_id: *(Optional)* Browser context to use when called on the browser endpoint.</span>
<span class="sd">    :returns: Array of cookie objects.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="k">if</span> <span class="n">browser_context_id</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;browserContextId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">browser_context_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Storage.getCookies&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span>
    <span class="k">return</span> <span class="p">[</span><span class="n">network</span><span class="o">.</span><span class="n">Cookie</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;cookies&quot;</span><span class="p">]]</span></div>



<div class="viewcode-block" id="set_cookies">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.set_cookies">[docs]</a>
<span class="k">def</span> <span class="nf">set_cookies</span><span class="p">(</span>
        <span class="n">cookies</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">network</span><span class="o">.</span><span class="n">CookieParam</span><span class="p">],</span>
        <span class="n">browser_context_id</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">browser</span><span class="o">.</span><span class="n">BrowserContextID</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Sets given cookies.</span>

<span class="sd">    :param cookies: Cookies to be set.</span>
<span class="sd">    :param browser_context_id: *(Optional)* Browser context to use when called on the browser endpoint.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;cookies&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">cookies</span><span class="p">]</span>
    <span class="k">if</span> <span class="n">browser_context_id</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;browserContextId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">browser_context_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Storage.setCookies&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span></div>



<div class="viewcode-block" id="clear_cookies">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.clear_cookies">[docs]</a>
<span class="k">def</span> <span class="nf">clear_cookies</span><span class="p">(</span>
        <span class="n">browser_context_id</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">browser</span><span class="o">.</span><span class="n">BrowserContextID</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Clears cookies.</span>

<span class="sd">    :param browser_context_id: *(Optional)* Browser context to use when called on the browser endpoint.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="k">if</span> <span class="n">browser_context_id</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;browserContextId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">browser_context_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Storage.clearCookies&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span></div>



<div class="viewcode-block" id="get_usage_and_quota">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.get_usage_and_quota">[docs]</a>
<span class="k">def</span> <span class="nf">get_usage_and_quota</span><span class="p">(</span>
        <span class="n">origin</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span>
    <span class="n">T_JSON_DICT</span><span class="p">,</span>
    <span class="n">T_JSON_DICT</span><span class="p">,</span>
    <span class="n">typing</span><span class="o">.</span><span class="n">Tuple</span><span class="p">[</span><span class="nb">float</span><span class="p">,</span> <span class="nb">float</span><span class="p">,</span> <span class="nb">bool</span><span class="p">,</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">UsageForType</span><span class="p">]],</span>
<span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Returns usage and quota in bytes.</span>

<span class="sd">    :param origin: Security origin.</span>
<span class="sd">    :returns: A tuple with the following items:</span>

<span class="sd">        0. **usage** - Storage usage (bytes).</span>
<span class="sd">        1. **quota** - Storage quota (bytes).</span>
<span class="sd">        2. **overrideActive** - Whether or not the origin has an active storage quota override</span>
<span class="sd">        3. **usageBreakdown** - Storage usage per type (bytes).</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;origin&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">origin</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Storage.getUsageAndQuota&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span>
    <span class="k">return</span> <span class="p">(</span>
        <span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;usage&quot;</span><span class="p">]),</span>
        <span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;quota&quot;</span><span class="p">]),</span>
        <span class="nb">bool</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;overrideActive&quot;</span><span class="p">]),</span>
        <span class="p">[</span><span class="n">UsageForType</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;usageBreakdown&quot;</span><span class="p">]],</span>
    <span class="p">)</span></div>



<div class="viewcode-block" id="override_quota_for_origin">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.override_quota_for_origin">[docs]</a>
<span class="k">def</span> <span class="nf">override_quota_for_origin</span><span class="p">(</span>
        <span class="n">origin</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">quota_size</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">float</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Override quota for the specified origin</span>

<span class="sd">    **EXPERIMENTAL**</span>

<span class="sd">    :param origin: Security origin.</span>
<span class="sd">    :param quota_size: *(Optional)* The quota size (in bytes) to override the original quota with. If this is called multiple times, the overridden quota will be equal to the quotaSize provided in the final call. If this is called without specifying a quotaSize, the quota will be reset to the default value for the specified origin. If this is called multiple times with different origins, the override will be maintained for each origin until it is disabled (called without a quotaSize).</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;origin&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">origin</span>
    <span class="k">if</span> <span class="n">quota_size</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;quotaSize&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">quota_size</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Storage.overrideQuotaForOrigin&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span></div>



<div class="viewcode-block" id="track_cache_storage_for_origin">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.track_cache_storage_for_origin">[docs]</a>
<span class="k">def</span> <span class="nf">track_cache_storage_for_origin</span><span class="p">(</span>
        <span class="n">origin</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Registers origin to be notified when an update occurs to its cache storage list.</span>

<span class="sd">    :param origin: Security origin.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;origin&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">origin</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Storage.trackCacheStorageForOrigin&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span></div>



<div class="viewcode-block" id="track_cache_storage_for_storage_key">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.track_cache_storage_for_storage_key">[docs]</a>
<span class="k">def</span> <span class="nf">track_cache_storage_for_storage_key</span><span class="p">(</span>
        <span class="n">storage_key</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Registers storage key to be notified when an update occurs to its cache storage list.</span>

<span class="sd">    :param storage_key: Storage key.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;storageKey&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">storage_key</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Storage.trackCacheStorageForStorageKey&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span></div>



<div class="viewcode-block" id="track_indexed_db_for_origin">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.track_indexed_db_for_origin">[docs]</a>
<span class="k">def</span> <span class="nf">track_indexed_db_for_origin</span><span class="p">(</span>
        <span class="n">origin</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Registers origin to be notified when an update occurs to its IndexedDB.</span>

<span class="sd">    :param origin: Security origin.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;origin&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">origin</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Storage.trackIndexedDBForOrigin&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span></div>



<div class="viewcode-block" id="track_indexed_db_for_storage_key">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.track_indexed_db_for_storage_key">[docs]</a>
<span class="k">def</span> <span class="nf">track_indexed_db_for_storage_key</span><span class="p">(</span>
        <span class="n">storage_key</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Registers storage key to be notified when an update occurs to its IndexedDB.</span>

<span class="sd">    :param storage_key: Storage key.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;storageKey&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">storage_key</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Storage.trackIndexedDBForStorageKey&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span></div>



<div class="viewcode-block" id="untrack_cache_storage_for_origin">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.untrack_cache_storage_for_origin">[docs]</a>
<span class="k">def</span> <span class="nf">untrack_cache_storage_for_origin</span><span class="p">(</span>
        <span class="n">origin</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Unregisters origin from receiving notifications for cache storage.</span>

<span class="sd">    :param origin: Security origin.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;origin&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">origin</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Storage.untrackCacheStorageForOrigin&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span></div>



<div class="viewcode-block" id="untrack_cache_storage_for_storage_key">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.untrack_cache_storage_for_storage_key">[docs]</a>
<span class="k">def</span> <span class="nf">untrack_cache_storage_for_storage_key</span><span class="p">(</span>
        <span class="n">storage_key</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Unregisters storage key from receiving notifications for cache storage.</span>

<span class="sd">    :param storage_key: Storage key.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;storageKey&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">storage_key</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Storage.untrackCacheStorageForStorageKey&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span></div>



<div class="viewcode-block" id="untrack_indexed_db_for_origin">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.untrack_indexed_db_for_origin">[docs]</a>
<span class="k">def</span> <span class="nf">untrack_indexed_db_for_origin</span><span class="p">(</span>
        <span class="n">origin</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Unregisters origin from receiving notifications for IndexedDB.</span>

<span class="sd">    :param origin: Security origin.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;origin&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">origin</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Storage.untrackIndexedDBForOrigin&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span></div>



<div class="viewcode-block" id="untrack_indexed_db_for_storage_key">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.untrack_indexed_db_for_storage_key">[docs]</a>
<span class="k">def</span> <span class="nf">untrack_indexed_db_for_storage_key</span><span class="p">(</span>
        <span class="n">storage_key</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Unregisters storage key from receiving notifications for IndexedDB.</span>

<span class="sd">    :param storage_key: Storage key.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;storageKey&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">storage_key</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Storage.untrackIndexedDBForStorageKey&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span></div>



<div class="viewcode-block" id="get_trust_tokens">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.get_trust_tokens">[docs]</a>
<span class="k">def</span> <span class="nf">get_trust_tokens</span><span class="p">()</span> <span class="o">-&gt;</span> <span class="p">(</span>
        <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">TrustTokens</span><span class="p">]]</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Returns the number of stored Trust Tokens per issuer for the</span>
<span class="sd">    current browsing context.</span>

<span class="sd">    **EXPERIMENTAL**</span>

<span class="sd">    :returns:</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Storage.getTrustTokens&quot;</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span>
    <span class="k">return</span> <span class="p">[</span><span class="n">TrustTokens</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;tokens&quot;</span><span class="p">]]</span></div>



<div class="viewcode-block" id="clear_trust_tokens">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.clear_trust_tokens">[docs]</a>
<span class="k">def</span> <span class="nf">clear_trust_tokens</span><span class="p">(</span>
        <span class="n">issuer_origin</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="nb">bool</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Removes all Trust Tokens issued by the provided issuerOrigin.</span>
<span class="sd">    Leaves other stored data, including the issuer&#39;s Redemption Records, intact.</span>

<span class="sd">    **EXPERIMENTAL**</span>

<span class="sd">    :param issuer_origin:</span>
<span class="sd">    :returns: True if any tokens were deleted, false otherwise.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;issuerOrigin&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">issuer_origin</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Storage.clearTrustTokens&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span>
    <span class="k">return</span> <span class="nb">bool</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;didDeleteTokens&quot;</span><span class="p">])</span></div>



<div class="viewcode-block" id="get_interest_group_details">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.get_interest_group_details">[docs]</a>
<span class="k">def</span> <span class="nf">get_interest_group_details</span><span class="p">(</span>
        <span class="n">owner_origin</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">name</span><span class="p">:</span> <span class="nb">str</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="nb">dict</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Gets details for a named interest group.</span>

<span class="sd">    **EXPERIMENTAL**</span>

<span class="sd">    :param owner_origin:</span>
<span class="sd">    :param name:</span>
<span class="sd">    :returns: This largely corresponds to: https://wicg.github.io/turtledove/#dictdef-generatebidinterestgroup but has absolute expirationTime instead of relative lifetimeMs and also adds joiningOrigin.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;ownerOrigin&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">owner_origin</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;name&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">name</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Storage.getInterestGroupDetails&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span>
    <span class="k">return</span> <span class="nb">dict</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;details&quot;</span><span class="p">])</span></div>



<div class="viewcode-block" id="set_interest_group_tracking">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.set_interest_group_tracking">[docs]</a>
<span class="k">def</span> <span class="nf">set_interest_group_tracking</span><span class="p">(</span>
        <span class="n">enable</span><span class="p">:</span> <span class="nb">bool</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Enables/Disables issuing of interestGroupAccessed events.</span>

<span class="sd">    **EXPERIMENTAL**</span>

<span class="sd">    :param enable:</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;enable&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">enable</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Storage.setInterestGroupTracking&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span></div>



<div class="viewcode-block" id="set_interest_group_auction_tracking">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.set_interest_group_auction_tracking">[docs]</a>
<span class="k">def</span> <span class="nf">set_interest_group_auction_tracking</span><span class="p">(</span>
        <span class="n">enable</span><span class="p">:</span> <span class="nb">bool</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Enables/Disables issuing of interestGroupAuctionEventOccurred and</span>
<span class="sd">    interestGroupAuctionNetworkRequestCreated.</span>

<span class="sd">    **EXPERIMENTAL**</span>

<span class="sd">    :param enable:</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;enable&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">enable</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Storage.setInterestGroupAuctionTracking&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span></div>



<div class="viewcode-block" id="get_shared_storage_metadata">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.get_shared_storage_metadata">[docs]</a>
<span class="k">def</span> <span class="nf">get_shared_storage_metadata</span><span class="p">(</span>
        <span class="n">owner_origin</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">SharedStorageMetadata</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Gets metadata for an origin&#39;s shared storage.</span>

<span class="sd">    **EXPERIMENTAL**</span>

<span class="sd">    :param owner_origin:</span>
<span class="sd">    :returns:</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;ownerOrigin&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">owner_origin</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Storage.getSharedStorageMetadata&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span>
    <span class="k">return</span> <span class="n">SharedStorageMetadata</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;metadata&quot;</span><span class="p">])</span></div>



<div class="viewcode-block" id="get_shared_storage_entries">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.get_shared_storage_entries">[docs]</a>
<span class="k">def</span> <span class="nf">get_shared_storage_entries</span><span class="p">(</span>
        <span class="n">owner_origin</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">SharedStorageEntry</span><span class="p">]]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Gets the entries in an given origin&#39;s shared storage.</span>

<span class="sd">    **EXPERIMENTAL**</span>

<span class="sd">    :param owner_origin:</span>
<span class="sd">    :returns:</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;ownerOrigin&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">owner_origin</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Storage.getSharedStorageEntries&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span>
    <span class="k">return</span> <span class="p">[</span><span class="n">SharedStorageEntry</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;entries&quot;</span><span class="p">]]</span></div>



<div class="viewcode-block" id="set_shared_storage_entry">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.set_shared_storage_entry">[docs]</a>
<span class="k">def</span> <span class="nf">set_shared_storage_entry</span><span class="p">(</span>
        <span class="n">owner_origin</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span>
        <span class="n">key</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span>
        <span class="n">value</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span>
        <span class="n">ignore_if_present</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">bool</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Sets entry with ``key`` and ``value`` for a given origin&#39;s shared storage.</span>

<span class="sd">    **EXPERIMENTAL**</span>

<span class="sd">    :param owner_origin:</span>
<span class="sd">    :param key:</span>
<span class="sd">    :param value:</span>
<span class="sd">    :param ignore_if_present: *(Optional)* If ```ignoreIfPresent```` is included and true, then only sets the entry if ````key``` doesn&#39;t already exist.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;ownerOrigin&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">owner_origin</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;key&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">key</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;value&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">value</span>
    <span class="k">if</span> <span class="n">ignore_if_present</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">params</span><span class="p">[</span><span class="s2">&quot;ignoreIfPresent&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">ignore_if_present</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Storage.setSharedStorageEntry&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span></div>



<div class="viewcode-block" id="delete_shared_storage_entry">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.delete_shared_storage_entry">[docs]</a>
<span class="k">def</span> <span class="nf">delete_shared_storage_entry</span><span class="p">(</span>
        <span class="n">owner_origin</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">key</span><span class="p">:</span> <span class="nb">str</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Deletes entry for ``key`` (if it exists) for a given origin&#39;s shared storage.</span>

<span class="sd">    **EXPERIMENTAL**</span>

<span class="sd">    :param owner_origin:</span>
<span class="sd">    :param key:</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;ownerOrigin&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">owner_origin</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;key&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">key</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Storage.deleteSharedStorageEntry&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span></div>



<div class="viewcode-block" id="clear_shared_storage_entries">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.clear_shared_storage_entries">[docs]</a>
<span class="k">def</span> <span class="nf">clear_shared_storage_entries</span><span class="p">(</span>
        <span class="n">owner_origin</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Clears all entries for a given origin&#39;s shared storage.</span>

<span class="sd">    **EXPERIMENTAL**</span>

<span class="sd">    :param owner_origin:</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;ownerOrigin&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">owner_origin</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Storage.clearSharedStorageEntries&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span></div>



<div class="viewcode-block" id="reset_shared_storage_budget">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.reset_shared_storage_budget">[docs]</a>
<span class="k">def</span> <span class="nf">reset_shared_storage_budget</span><span class="p">(</span>
        <span class="n">owner_origin</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Resets the budget for ``ownerOrigin`` by clearing all budget withdrawals.</span>

<span class="sd">    **EXPERIMENTAL**</span>

<span class="sd">    :param owner_origin:</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;ownerOrigin&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">owner_origin</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Storage.resetSharedStorageBudget&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span></div>



<div class="viewcode-block" id="set_shared_storage_tracking">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.set_shared_storage_tracking">[docs]</a>
<span class="k">def</span> <span class="nf">set_shared_storage_tracking</span><span class="p">(</span>
        <span class="n">enable</span><span class="p">:</span> <span class="nb">bool</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Enables/disables issuing of sharedStorageAccessed events.</span>

<span class="sd">    **EXPERIMENTAL**</span>

<span class="sd">    :param enable:</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;enable&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">enable</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Storage.setSharedStorageTracking&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span></div>



<div class="viewcode-block" id="set_storage_bucket_tracking">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.set_storage_bucket_tracking">[docs]</a>
<span class="k">def</span> <span class="nf">set_storage_bucket_tracking</span><span class="p">(</span>
        <span class="n">storage_key</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">enable</span><span class="p">:</span> <span class="nb">bool</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Set tracking for a storage key&#39;s buckets.</span>

<span class="sd">    **EXPERIMENTAL**</span>

<span class="sd">    :param storage_key:</span>
<span class="sd">    :param enable:</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;storageKey&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">storage_key</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;enable&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">enable</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Storage.setStorageBucketTracking&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span></div>



<div class="viewcode-block" id="delete_storage_bucket">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.delete_storage_bucket">[docs]</a>
<span class="k">def</span> <span class="nf">delete_storage_bucket</span><span class="p">(</span>
        <span class="n">bucket</span><span class="p">:</span> <span class="n">StorageBucket</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Deletes the Storage Bucket with the given storage key and bucket name.</span>

<span class="sd">    **EXPERIMENTAL**</span>

<span class="sd">    :param bucket:</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;bucket&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">bucket</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Storage.deleteStorageBucket&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span></div>



<div class="viewcode-block" id="run_bounce_tracking_mitigations">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.run_bounce_tracking_mitigations">[docs]</a>
<span class="k">def</span> <span class="nf">run_bounce_tracking_mitigations</span><span class="p">()</span> <span class="o">-&gt;</span> <span class="p">(</span>
        <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">]]</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Deletes state for sites identified as potential bounce trackers, immediately.</span>

<span class="sd">    **EXPERIMENTAL**</span>

<span class="sd">    :returns:</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Storage.runBounceTrackingMitigations&quot;</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span>
    <span class="k">return</span> <span class="p">[</span><span class="nb">str</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;deletedSites&quot;</span><span class="p">]]</span></div>



<div class="viewcode-block" id="set_attribution_reporting_local_testing_mode">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.set_attribution_reporting_local_testing_mode">[docs]</a>
<span class="k">def</span> <span class="nf">set_attribution_reporting_local_testing_mode</span><span class="p">(</span>
        <span class="n">enabled</span><span class="p">:</span> <span class="nb">bool</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    https://wicg.github.io/attribution-reporting-api/</span>

<span class="sd">    **EXPERIMENTAL**</span>

<span class="sd">    :param enabled: If enabled, noise is suppressed and reports are sent immediately.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;enabled&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">enabled</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Storage.setAttributionReportingLocalTestingMode&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span></div>



<div class="viewcode-block" id="set_attribution_reporting_tracking">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.set_attribution_reporting_tracking">[docs]</a>
<span class="k">def</span> <span class="nf">set_attribution_reporting_tracking</span><span class="p">(</span>
        <span class="n">enable</span><span class="p">:</span> <span class="nb">bool</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Enables/disables issuing of Attribution Reporting events.</span>

<span class="sd">    **EXPERIMENTAL**</span>

<span class="sd">    :param enable:</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;enable&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">enable</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Storage.setAttributionReportingTracking&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span></div>



<div class="viewcode-block" id="send_pending_attribution_reports">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.send_pending_attribution_reports">[docs]</a>
<span class="k">def</span> <span class="nf">send_pending_attribution_reports</span><span class="p">()</span> <span class="o">-&gt;</span> <span class="p">(</span>
        <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="nb">int</span><span class="p">]</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Sends all pending Attribution Reports immediately, regardless of their</span>
<span class="sd">    scheduled report time.</span>

<span class="sd">    **EXPERIMENTAL**</span>

<span class="sd">    :returns: The number of reports that were sent.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Storage.sendPendingAttributionReports&quot;</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span>
    <span class="k">return</span> <span class="nb">int</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;numSent&quot;</span><span class="p">])</span></div>



<div class="viewcode-block" id="get_related_website_sets">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.get_related_website_sets">[docs]</a>
<span class="k">def</span> <span class="nf">get_related_website_sets</span><span class="p">()</span> <span class="o">-&gt;</span> <span class="p">(</span>
        <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">RelatedWebsiteSet</span><span class="p">]]</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Returns the effective Related Website Sets in use by this profile for the browser</span>
<span class="sd">    session. The effective Related Website Sets will not change during a browser session.</span>

<span class="sd">    **EXPERIMENTAL**</span>

<span class="sd">    :returns:</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Storage.getRelatedWebsiteSets&quot;</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span>
    <span class="k">return</span> <span class="p">[</span><span class="n">RelatedWebsiteSet</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;sets&quot;</span><span class="p">]]</span></div>



<div class="viewcode-block" id="get_affected_urls_for_third_party_cookie_metadata">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.get_affected_urls_for_third_party_cookie_metadata">[docs]</a>
<span class="k">def</span> <span class="nf">get_affected_urls_for_third_party_cookie_metadata</span><span class="p">(</span>
        <span class="n">first_party_url</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">third_party_urls</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">]]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Returns the list of URLs from a page and its embedded resources that match</span>
<span class="sd">    existing grace period URL pattern rules.</span>
<span class="sd">    https://developers.google.com/privacy-sandbox/cookies/temporary-exceptions/grace-period</span>

<span class="sd">    **EXPERIMENTAL**</span>

<span class="sd">    :param first_party_url: The URL of the page currently being visited.</span>
<span class="sd">    :param third_party_urls: The list of embedded resource URLs from the page.</span>
<span class="sd">    :returns: Array of matching URLs. If there is a primary pattern match for the first- party URL, only the first-party URL is returned in the array.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;firstPartyUrl&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">first_party_url</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;thirdPartyUrls&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">third_party_urls</span><span class="p">]</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;Storage.getAffectedUrlsForThirdPartyCookieMetadata&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span>
    <span class="k">return</span> <span class="p">[</span><span class="nb">str</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;matchedUrls&quot;</span><span class="p">]]</span></div>



<div class="viewcode-block" id="CacheStorageContentUpdated">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.CacheStorageContentUpdated">[docs]</a>
<span class="nd">@event_class</span><span class="p">(</span><span class="s2">&quot;Storage.cacheStorageContentUpdated&quot;</span><span class="p">)</span>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">CacheStorageContentUpdated</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    A cache&#39;s contents have been modified.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: Origin to update.</span>
    <span class="n">origin</span><span class="p">:</span> <span class="nb">str</span>
    <span class="c1">#: Storage key to update.</span>
    <span class="n">storage_key</span><span class="p">:</span> <span class="nb">str</span>
    <span class="c1">#: Storage bucket to update.</span>
    <span class="n">bucket_id</span><span class="p">:</span> <span class="nb">str</span>
    <span class="c1">#: Name of cache in origin.</span>
    <span class="n">cache_name</span><span class="p">:</span> <span class="nb">str</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">CacheStorageContentUpdated</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">origin</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;origin&quot;</span><span class="p">]),</span>
            <span class="n">storage_key</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;storageKey&quot;</span><span class="p">]),</span>
            <span class="n">bucket_id</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;bucketId&quot;</span><span class="p">]),</span>
            <span class="n">cache_name</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;cacheName&quot;</span><span class="p">]),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="CacheStorageListUpdated">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.CacheStorageListUpdated">[docs]</a>
<span class="nd">@event_class</span><span class="p">(</span><span class="s2">&quot;Storage.cacheStorageListUpdated&quot;</span><span class="p">)</span>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">CacheStorageListUpdated</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    A cache has been added/deleted.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: Origin to update.</span>
    <span class="n">origin</span><span class="p">:</span> <span class="nb">str</span>
    <span class="c1">#: Storage key to update.</span>
    <span class="n">storage_key</span><span class="p">:</span> <span class="nb">str</span>
    <span class="c1">#: Storage bucket to update.</span>
    <span class="n">bucket_id</span><span class="p">:</span> <span class="nb">str</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">CacheStorageListUpdated</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">origin</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;origin&quot;</span><span class="p">]),</span>
            <span class="n">storage_key</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;storageKey&quot;</span><span class="p">]),</span>
            <span class="n">bucket_id</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;bucketId&quot;</span><span class="p">]),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="IndexedDBContentUpdated">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.IndexedDBContentUpdated">[docs]</a>
<span class="nd">@event_class</span><span class="p">(</span><span class="s2">&quot;Storage.indexedDBContentUpdated&quot;</span><span class="p">)</span>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">IndexedDBContentUpdated</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    The origin&#39;s IndexedDB object store has been modified.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: Origin to update.</span>
    <span class="n">origin</span><span class="p">:</span> <span class="nb">str</span>
    <span class="c1">#: Storage key to update.</span>
    <span class="n">storage_key</span><span class="p">:</span> <span class="nb">str</span>
    <span class="c1">#: Storage bucket to update.</span>
    <span class="n">bucket_id</span><span class="p">:</span> <span class="nb">str</span>
    <span class="c1">#: Database to update.</span>
    <span class="n">database_name</span><span class="p">:</span> <span class="nb">str</span>
    <span class="c1">#: ObjectStore to update.</span>
    <span class="n">object_store_name</span><span class="p">:</span> <span class="nb">str</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">IndexedDBContentUpdated</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">origin</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;origin&quot;</span><span class="p">]),</span>
            <span class="n">storage_key</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;storageKey&quot;</span><span class="p">]),</span>
            <span class="n">bucket_id</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;bucketId&quot;</span><span class="p">]),</span>
            <span class="n">database_name</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;databaseName&quot;</span><span class="p">]),</span>
            <span class="n">object_store_name</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;objectStoreName&quot;</span><span class="p">]),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="IndexedDBListUpdated">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.IndexedDBListUpdated">[docs]</a>
<span class="nd">@event_class</span><span class="p">(</span><span class="s2">&quot;Storage.indexedDBListUpdated&quot;</span><span class="p">)</span>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">IndexedDBListUpdated</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    The origin&#39;s IndexedDB database list has been modified.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: Origin to update.</span>
    <span class="n">origin</span><span class="p">:</span> <span class="nb">str</span>
    <span class="c1">#: Storage key to update.</span>
    <span class="n">storage_key</span><span class="p">:</span> <span class="nb">str</span>
    <span class="c1">#: Storage bucket to update.</span>
    <span class="n">bucket_id</span><span class="p">:</span> <span class="nb">str</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">IndexedDBListUpdated</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">origin</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;origin&quot;</span><span class="p">]),</span>
            <span class="n">storage_key</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;storageKey&quot;</span><span class="p">]),</span>
            <span class="n">bucket_id</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;bucketId&quot;</span><span class="p">]),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="InterestGroupAccessed">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.InterestGroupAccessed">[docs]</a>
<span class="nd">@event_class</span><span class="p">(</span><span class="s2">&quot;Storage.interestGroupAccessed&quot;</span><span class="p">)</span>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">InterestGroupAccessed</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    One of the interest groups was accessed. Note that these events are global</span>
<span class="sd">    to all targets sharing an interest group store.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">access_time</span><span class="p">:</span> <span class="n">network</span><span class="o">.</span><span class="n">TimeSinceEpoch</span>
    <span class="n">type_</span><span class="p">:</span> <span class="n">InterestGroupAccessType</span>
    <span class="n">owner_origin</span><span class="p">:</span> <span class="nb">str</span>
    <span class="n">name</span><span class="p">:</span> <span class="nb">str</span>
    <span class="c1">#: For topLevelBid/topLevelAdditionalBid, and when appropriate,</span>
    <span class="c1">#: win and additionalBidWin</span>
    <span class="n">component_seller_origin</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span>
    <span class="c1">#: For bid or somethingBid event, if done locally and not on a server.</span>
    <span class="n">bid</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">float</span><span class="p">]</span>
    <span class="n">bid_currency</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span>
    <span class="c1">#: For non-global events --- links to interestGroupAuctionEvent</span>
    <span class="n">unique_auction_id</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">InterestGroupAuctionId</span><span class="p">]</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">InterestGroupAccessed</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">access_time</span><span class="o">=</span><span class="n">network</span><span class="o">.</span><span class="n">TimeSinceEpoch</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;accessTime&quot;</span><span class="p">]),</span>
            <span class="n">type_</span><span class="o">=</span><span class="n">InterestGroupAccessType</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;type&quot;</span><span class="p">]),</span>
            <span class="n">owner_origin</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;ownerOrigin&quot;</span><span class="p">]),</span>
            <span class="n">name</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;name&quot;</span><span class="p">]),</span>
            <span class="n">component_seller_origin</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;componentSellerOrigin&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;componentSellerOrigin&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">bid</span><span class="o">=</span><span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;bid&quot;</span><span class="p">])</span> <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;bid&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span> <span class="k">else</span> <span class="kc">None</span><span class="p">,</span>
            <span class="n">bid_currency</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;bidCurrency&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;bidCurrency&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">unique_auction_id</span><span class="o">=</span><span class="p">(</span>
                <span class="n">InterestGroupAuctionId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;uniqueAuctionId&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;uniqueAuctionId&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="InterestGroupAuctionEventOccurred">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.InterestGroupAuctionEventOccurred">[docs]</a>
<span class="nd">@event_class</span><span class="p">(</span><span class="s2">&quot;Storage.interestGroupAuctionEventOccurred&quot;</span><span class="p">)</span>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">InterestGroupAuctionEventOccurred</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    An auction involving interest groups is taking place. These events are</span>
<span class="sd">    target-specific.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">event_time</span><span class="p">:</span> <span class="n">network</span><span class="o">.</span><span class="n">TimeSinceEpoch</span>
    <span class="n">type_</span><span class="p">:</span> <span class="n">InterestGroupAuctionEventType</span>
    <span class="n">unique_auction_id</span><span class="p">:</span> <span class="n">InterestGroupAuctionId</span>
    <span class="c1">#: Set for child auctions.</span>
    <span class="n">parent_auction_id</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">InterestGroupAuctionId</span><span class="p">]</span>
    <span class="c1">#: Set for started and configResolved</span>
    <span class="n">auction_config</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">dict</span><span class="p">]</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">InterestGroupAuctionEventOccurred</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">event_time</span><span class="o">=</span><span class="n">network</span><span class="o">.</span><span class="n">TimeSinceEpoch</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;eventTime&quot;</span><span class="p">]),</span>
            <span class="n">type_</span><span class="o">=</span><span class="n">InterestGroupAuctionEventType</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;type&quot;</span><span class="p">]),</span>
            <span class="n">unique_auction_id</span><span class="o">=</span><span class="n">InterestGroupAuctionId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;uniqueAuctionId&quot;</span><span class="p">]),</span>
            <span class="n">parent_auction_id</span><span class="o">=</span><span class="p">(</span>
                <span class="n">InterestGroupAuctionId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;parentAuctionId&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;parentAuctionId&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">auction_config</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">dict</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;auctionConfig&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;auctionConfig&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="InterestGroupAuctionNetworkRequestCreated">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.InterestGroupAuctionNetworkRequestCreated">[docs]</a>
<span class="nd">@event_class</span><span class="p">(</span><span class="s2">&quot;Storage.interestGroupAuctionNetworkRequestCreated&quot;</span><span class="p">)</span>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">InterestGroupAuctionNetworkRequestCreated</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Specifies which auctions a particular network fetch may be related to, and</span>
<span class="sd">    in what role. Note that it is not ordered with respect to</span>
<span class="sd">    Network.requestWillBeSent (but will happen before loadingFinished</span>
<span class="sd">    loadingFailed).</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">type_</span><span class="p">:</span> <span class="n">InterestGroupAuctionFetchType</span>
    <span class="n">request_id</span><span class="p">:</span> <span class="n">network</span><span class="o">.</span><span class="n">RequestId</span>
    <span class="c1">#: This is the set of the auctions using the worklet that issued this</span>
    <span class="c1">#: request.  In the case of trusted signals, it&#39;s possible that only some of</span>
    <span class="c1">#: them actually care about the keys being queried.</span>
    <span class="n">auctions</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">List</span><span class="p">[</span><span class="n">InterestGroupAuctionId</span><span class="p">]</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">InterestGroupAuctionNetworkRequestCreated</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">type_</span><span class="o">=</span><span class="n">InterestGroupAuctionFetchType</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;type&quot;</span><span class="p">]),</span>
            <span class="n">request_id</span><span class="o">=</span><span class="n">network</span><span class="o">.</span><span class="n">RequestId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;requestId&quot;</span><span class="p">]),</span>
            <span class="n">auctions</span><span class="o">=</span><span class="p">[</span><span class="n">InterestGroupAuctionId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">json</span><span class="p">[</span><span class="s2">&quot;auctions&quot;</span><span class="p">]],</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="SharedStorageAccessed">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.SharedStorageAccessed">[docs]</a>
<span class="nd">@event_class</span><span class="p">(</span><span class="s2">&quot;Storage.sharedStorageAccessed&quot;</span><span class="p">)</span>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">SharedStorageAccessed</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Shared storage was accessed by the associated page.</span>
<span class="sd">    The following parameters are included in all events.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: Time of the access.</span>
    <span class="n">access_time</span><span class="p">:</span> <span class="n">network</span><span class="o">.</span><span class="n">TimeSinceEpoch</span>
    <span class="c1">#: Enum value indicating the Shared Storage API method invoked.</span>
    <span class="n">type_</span><span class="p">:</span> <span class="n">SharedStorageAccessType</span>
    <span class="c1">#: DevTools Frame Token for the primary frame tree&#39;s root.</span>
    <span class="n">main_frame_id</span><span class="p">:</span> <span class="n">page</span><span class="o">.</span><span class="n">FrameId</span>
    <span class="c1">#: Serialized origin for the context that invoked the Shared Storage API.</span>
    <span class="n">owner_origin</span><span class="p">:</span> <span class="nb">str</span>
    <span class="c1">#: The sub-parameters wrapped by ``params`` are all optional and their</span>
    <span class="c1">#: presence/absence depends on ``type``.</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">SharedStorageAccessParams</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">SharedStorageAccessed</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">access_time</span><span class="o">=</span><span class="n">network</span><span class="o">.</span><span class="n">TimeSinceEpoch</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;accessTime&quot;</span><span class="p">]),</span>
            <span class="n">type_</span><span class="o">=</span><span class="n">SharedStorageAccessType</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;type&quot;</span><span class="p">]),</span>
            <span class="n">main_frame_id</span><span class="o">=</span><span class="n">page</span><span class="o">.</span><span class="n">FrameId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;mainFrameId&quot;</span><span class="p">]),</span>
            <span class="n">owner_origin</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;ownerOrigin&quot;</span><span class="p">]),</span>
            <span class="n">params</span><span class="o">=</span><span class="n">SharedStorageAccessParams</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;params&quot;</span><span class="p">]),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="StorageBucketCreatedOrUpdated">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.StorageBucketCreatedOrUpdated">[docs]</a>
<span class="nd">@event_class</span><span class="p">(</span><span class="s2">&quot;Storage.storageBucketCreatedOrUpdated&quot;</span><span class="p">)</span>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">StorageBucketCreatedOrUpdated</span><span class="p">:</span>
    <span class="n">bucket_info</span><span class="p">:</span> <span class="n">StorageBucketInfo</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">StorageBucketCreatedOrUpdated</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">bucket_info</span><span class="o">=</span><span class="n">StorageBucketInfo</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;bucketInfo&quot;</span><span class="p">]))</span></div>



<div class="viewcode-block" id="StorageBucketDeleted">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.StorageBucketDeleted">[docs]</a>
<span class="nd">@event_class</span><span class="p">(</span><span class="s2">&quot;Storage.storageBucketDeleted&quot;</span><span class="p">)</span>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">StorageBucketDeleted</span><span class="p">:</span>
    <span class="n">bucket_id</span><span class="p">:</span> <span class="nb">str</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">StorageBucketDeleted</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">bucket_id</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;bucketId&quot;</span><span class="p">]))</span></div>



<div class="viewcode-block" id="AttributionReportingSourceRegistered">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.AttributionReportingSourceRegistered">[docs]</a>
<span class="nd">@event_class</span><span class="p">(</span><span class="s2">&quot;Storage.attributionReportingSourceRegistered&quot;</span><span class="p">)</span>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">AttributionReportingSourceRegistered</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    **EXPERIMENTAL**</span>


<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">registration</span><span class="p">:</span> <span class="n">AttributionReportingSourceRegistration</span>
    <span class="n">result</span><span class="p">:</span> <span class="n">AttributionReportingSourceRegistrationResult</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">AttributionReportingSourceRegistered</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">registration</span><span class="o">=</span><span class="n">AttributionReportingSourceRegistration</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span>
                <span class="n">json</span><span class="p">[</span><span class="s2">&quot;registration&quot;</span><span class="p">]</span>
            <span class="p">),</span>
            <span class="n">result</span><span class="o">=</span><span class="n">AttributionReportingSourceRegistrationResult</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span>
                <span class="n">json</span><span class="p">[</span><span class="s2">&quot;result&quot;</span><span class="p">]</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="AttributionReportingTriggerRegistered">
<a class="viewcode-back" href="../../../nodriver/cdp/storage.html#nodriver.cdp.storage.AttributionReportingTriggerRegistered">[docs]</a>
<span class="nd">@event_class</span><span class="p">(</span><span class="s2">&quot;Storage.attributionReportingTriggerRegistered&quot;</span><span class="p">)</span>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">AttributionReportingTriggerRegistered</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    **EXPERIMENTAL**</span>


<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">registration</span><span class="p">:</span> <span class="n">AttributionReportingTriggerRegistration</span>
    <span class="n">event_level</span><span class="p">:</span> <span class="n">AttributionReportingEventLevelResult</span>
    <span class="n">aggregatable</span><span class="p">:</span> <span class="n">AttributionReportingAggregatableResult</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">AttributionReportingTriggerRegistered</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">registration</span><span class="o">=</span><span class="n">AttributionReportingTriggerRegistration</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span>
                <span class="n">json</span><span class="p">[</span><span class="s2">&quot;registration&quot;</span><span class="p">]</span>
            <span class="p">),</span>
            <span class="n">event_level</span><span class="o">=</span><span class="n">AttributionReportingEventLevelResult</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span>
                <span class="n">json</span><span class="p">[</span><span class="s2">&quot;eventLevel&quot;</span><span class="p">]</span>
            <span class="p">),</span>
            <span class="n">aggregatable</span><span class="o">=</span><span class="n">AttributionReportingAggregatableResult</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span>
                <span class="n">json</span><span class="p">[</span><span class="s2">&quot;aggregatable&quot;</span><span class="p">]</span>
            <span class="p">),</span>
        <span class="p">)</span></div>

</pre></div>
        </article>
      </div>
      <footer>
        
        <div class="related-pages">
          
          
        </div>
        <div class="bottom-of-page">
          <div class="left-details">
            <div class="copyright">
                Copyright &#169; 2023, Author
            </div>
            Made with <a href="https://www.sphinx-doc.org/">Sphinx</a> and <a class="muted-link" href="https://pradyunsg.me">@pradyunsg</a>'s
            
            <a href="https://github.com/pradyunsg/furo">Furo</a>
            
          </div>
          <div class="right-details">
            
          </div>
        </div>
        
      </footer>
    </div>
    <aside class="toc-drawer no-toc">
      
      
      
    </aside>
  </div>
</div><script src="../../../_static/documentation_options.js?v=5929fcd5"></script>
    <script src="../../../_static/doctools.js?v=888ff710"></script>
    <script src="../../../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../../../_static/scripts/furo.js?v=32e29ea5"></script>
    </body>
</html>