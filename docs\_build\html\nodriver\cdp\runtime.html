<!doctype html>
<html class="no-js" lang="en" data-content_root="../../">
  <head><meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width,initial-scale=1"/>
    <meta name="color-scheme" content="light dark"><meta name="viewport" content="width=device-width, initial-scale=1" />
<link rel="index" title="Index" href="../../genindex.html" /><link rel="search" title="Search" href="../../search.html" /><link rel="next" title="Schema" href="schema.html" /><link rel="prev" title="PWA" href="pwa.html" />

    <!-- Generated with Sphinx 7.2.6 and Furo 2023.09.10 -->
        <title>Runtime - nodriver documentation</title>
      <link rel="stylesheet" type="text/css" href="../../_static/pygments.css?v=fa44fd50" />
    <link rel="stylesheet" type="text/css" href="../../_static/styles/furo.css?v=135e06be" />
    <link rel="stylesheet" type="text/css" href="../../_static/styles/furo-extensions.css?v=36a5483c" />
    <link rel="stylesheet" type="text/css" href="../../_static/./custom.css?v=3934642d" />
    
    


<style>
  body {
    --color-code-background: #eeffcc;
  --color-code-foreground: black;
  
  }
  @media not print {
    body[data-theme="dark"] {
      --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8eefba;
  --color-background-primary: #111112;
  --color-problematic: #e7aeae;
  
    }
    @media (prefers-color-scheme: dark) {
      body:not([data-theme="light"]) {
        --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8eefba;
  --color-background-primary: #111112;
  --color-problematic: #e7aeae;
  
      }
    }
  }
</style></head>
  <body>
    
    <script>
      document.body.dataset.theme = localStorage.getItem("theme") || "auto";
    </script>
    

<svg xmlns="http://www.w3.org/2000/svg" style="display: none;">
  <symbol id="svg-toc" viewBox="0 0 24 24">
    <title>Contents</title>
    <svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 1024 1024">
      <path d="M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM115.4 518.9L271.7 642c5.8 4.6 14.4.5 14.4-6.9V388.9c0-7.4-8.5-11.5-14.4-6.9L115.4 505.1a8.74 8.74 0 0 0 0 13.8z"/>
    </svg>
  </symbol>
  <symbol id="svg-menu" viewBox="0 0 24 24">
    <title>Menu</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-menu">
      <line x1="3" y1="12" x2="21" y2="12"></line>
      <line x1="3" y1="6" x2="21" y2="6"></line>
      <line x1="3" y1="18" x2="21" y2="18"></line>
    </svg>
  </symbol>
  <symbol id="svg-arrow-right" viewBox="0 0 24 24">
    <title>Expand</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-chevron-right">
      <polyline points="9 18 15 12 9 6"></polyline>
    </svg>
  </symbol>
  <symbol id="svg-sun" viewBox="0 0 24 24">
    <title>Light mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="feather-sun">
      <circle cx="12" cy="12" r="5"></circle>
      <line x1="12" y1="1" x2="12" y2="3"></line>
      <line x1="12" y1="21" x2="12" y2="23"></line>
      <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
      <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
      <line x1="1" y1="12" x2="3" y2="12"></line>
      <line x1="21" y1="12" x2="23" y2="12"></line>
      <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
      <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
    </svg>
  </symbol>
  <symbol id="svg-moon" viewBox="0 0 24 24">
    <title>Dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-moon">
      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
      <path d="M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z" />
    </svg>
  </symbol>
  <symbol id="svg-sun-half" viewBox="0 0 24 24">
    <title>Auto light/dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-shadow">
      <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
      <circle cx="12" cy="12" r="9" />
      <path d="M13 12h5" />
      <path d="M13 15h4" />
      <path d="M13 18h1" />
      <path d="M13 9h4" />
      <path d="M13 6h1" />
    </svg>
  </symbol>
</svg>

<input type="checkbox" class="sidebar-toggle" name="__navigation" id="__navigation">
<input type="checkbox" class="sidebar-toggle" name="__toc" id="__toc">
<label class="overlay sidebar-overlay" for="__navigation">
  <div class="visually-hidden">Hide navigation sidebar</div>
</label>
<label class="overlay toc-overlay" for="__toc">
  <div class="visually-hidden">Hide table of contents sidebar</div>
</label>



<div class="page">
  <header class="mobile-header">
    <div class="header-left">
      <label class="nav-overlay-icon" for="__navigation">
        <div class="visually-hidden">Toggle site navigation sidebar</div>
        <i class="icon"><svg><use href="#svg-menu"></use></svg></i>
      </label>
    </div>
    <div class="header-center">
      <a href="../../index.html"><div class="brand">nodriver  documentation</div></a>
    </div>
    <div class="header-right">
      <div class="theme-toggle-container theme-toggle-header">
        <button class="theme-toggle">
          <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
          <svg class="theme-icon-when-auto"><use href="#svg-sun-half"></use></svg>
          <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
          <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
        </button>
      </div>
      <label class="toc-overlay-icon toc-header-icon" for="__toc">
        <div class="visually-hidden">Toggle table of contents sidebar</div>
        <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
      </label>
    </div>
  </header>
  <aside class="sidebar-drawer">
    <div class="sidebar-container">
      
      <div class="sidebar-sticky"><div class="sidebar-scroll"><a class="sidebar-brand" href="../../index.html">
  
  
  <span class="sidebar-brand-text">nodriver  documentation</span>
  
</a><form class="sidebar-search-container" method="get" action="../../search.html" role="search">
  <input class="sidebar-search" placeholder="Search" name="q" aria-label="Search">
  <input type="hidden" name="check_keywords" value="yes">
  <input type="hidden" name="area" value="default">
</form>
<div id="searchbox"></div><div class="sidebar-tree">
  <ul>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html">Quickstart guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#usage-example">usage example</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#more-complete-example">More complete example</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#custom-starting-options">Custom starting options</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#alternative-custom-options">Alternative custom options</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#proxies-socks5-authenticated-too">Proxies (socks5, authenticated too)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#concrete-example">Concrete example</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../classes/browser.html">Browser class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../classes/tab.html">Tab class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../classes/element.html">Element class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../classes/others_and_helpers.html">Other classes and Helper classes</a></li>
</ul>
<ul class="current">
<li class="toctree-l1 current has-children"><a class="reference internal" href="../cdp.html">CDP object</a><input checked="" class="toctree-checkbox" id="toctree-checkbox-1" name="toctree-checkbox-1" role="switch" type="checkbox"/><label for="toctree-checkbox-1"><div class="visually-hidden">Toggle navigation of CDP object</div><i class="icon"><svg><use href="#svg-arrow-right"></use></svg></i></label><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="accessibility.html">Accessibility</a></li>
<li class="toctree-l2"><a class="reference internal" href="animation.html">Animation</a></li>
<li class="toctree-l2"><a class="reference internal" href="audits.html">Audits</a></li>
<li class="toctree-l2"><a class="reference internal" href="autofill.html">Autofill</a></li>
<li class="toctree-l2"><a class="reference internal" href="background_service.html">BackgroundService</a></li>
<li class="toctree-l2"><a class="reference internal" href="bluetooth_emulation.html">BluetoothEmulation</a></li>
<li class="toctree-l2"><a class="reference internal" href="browser.html">Browser</a></li>
<li class="toctree-l2"><a class="reference internal" href="cache_storage.html">CacheStorage</a></li>
<li class="toctree-l2"><a class="reference internal" href="cast.html">Cast</a></li>
<li class="toctree-l2"><a class="reference internal" href="console.html">Console</a></li>
<li class="toctree-l2"><a class="reference internal" href="css.html">CSS</a></li>
<li class="toctree-l2"><a class="reference internal" href="debugger.html">Debugger</a></li>
<li class="toctree-l2"><a class="reference internal" href="device_access.html">DeviceAccess</a></li>
<li class="toctree-l2"><a class="reference internal" href="device_orientation.html">DeviceOrientation</a></li>
<li class="toctree-l2"><a class="reference internal" href="dom.html">DOM</a></li>
<li class="toctree-l2"><a class="reference internal" href="dom_debugger.html">DOMDebugger</a></li>
<li class="toctree-l2"><a class="reference internal" href="dom_snapshot.html">DOMSnapshot</a></li>
<li class="toctree-l2"><a class="reference internal" href="dom_storage.html">DOMStorage</a></li>
<li class="toctree-l2"><a class="reference internal" href="emulation.html">Emulation</a></li>
<li class="toctree-l2"><a class="reference internal" href="event_breakpoints.html">EventBreakpoints</a></li>
<li class="toctree-l2"><a class="reference internal" href="extensions.html">Extensions</a></li>
<li class="toctree-l2"><a class="reference internal" href="fed_cm.html">FedCm</a></li>
<li class="toctree-l2"><a class="reference internal" href="fetch.html">Fetch</a></li>
<li class="toctree-l2"><a class="reference internal" href="file_system.html">FileSystem</a></li>
<li class="toctree-l2"><a class="reference internal" href="headless_experimental.html">HeadlessExperimental</a></li>
<li class="toctree-l2"><a class="reference internal" href="heap_profiler.html">HeapProfiler</a></li>
<li class="toctree-l2"><a class="reference internal" href="indexed_db.html">IndexedDB</a></li>
<li class="toctree-l2"><a class="reference internal" href="input_.html">Input</a></li>
<li class="toctree-l2"><a class="reference internal" href="inspector.html">Inspector</a></li>
<li class="toctree-l2"><a class="reference internal" href="io.html">IO</a></li>
<li class="toctree-l2"><a class="reference internal" href="layer_tree.html">LayerTree</a></li>
<li class="toctree-l2"><a class="reference internal" href="log.html">Log</a></li>
<li class="toctree-l2"><a class="reference internal" href="media.html">Media</a></li>
<li class="toctree-l2"><a class="reference internal" href="memory.html">Memory</a></li>
<li class="toctree-l2"><a class="reference internal" href="network.html">Network</a></li>
<li class="toctree-l2"><a class="reference internal" href="overlay.html">Overlay</a></li>
<li class="toctree-l2"><a class="reference internal" href="page.html">Page</a></li>
<li class="toctree-l2"><a class="reference internal" href="performance.html">Performance</a></li>
<li class="toctree-l2"><a class="reference internal" href="performance_timeline.html">PerformanceTimeline</a></li>
<li class="toctree-l2"><a class="reference internal" href="preload.html">Preload</a></li>
<li class="toctree-l2"><a class="reference internal" href="profiler.html">Profiler</a></li>
<li class="toctree-l2"><a class="reference internal" href="pwa.html">PWA</a></li>
<li class="toctree-l2 current current-page"><a class="current reference internal" href="#">Runtime</a></li>
<li class="toctree-l2"><a class="reference internal" href="schema.html">Schema</a></li>
<li class="toctree-l2"><a class="reference internal" href="security.html">Security</a></li>
<li class="toctree-l2"><a class="reference internal" href="service_worker.html">ServiceWorker</a></li>
<li class="toctree-l2"><a class="reference internal" href="storage.html">Storage</a></li>
<li class="toctree-l2"><a class="reference internal" href="system_info.html">SystemInfo</a></li>
<li class="toctree-l2"><a class="reference internal" href="target.html">Target</a></li>
<li class="toctree-l2"><a class="reference internal" href="tethering.html">Tethering</a></li>
<li class="toctree-l2"><a class="reference internal" href="tracing.html">Tracing</a></li>
<li class="toctree-l2"><a class="reference internal" href="web_audio.html">WebAudio</a></li>
<li class="toctree-l2"><a class="reference internal" href="web_authn.html">WebAuthn</a></li>
</ul>
</li>
</ul>

</div>
</div>
      </div>
      
    </div>
  </aside>
  <div class="main">
    <div class="content">
      <div class="article-container">
        <a href="#" class="back-to-top muted-link">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
            <path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8v12z"></path>
          </svg>
          <span>Back to top</span>
        </a>
        <div class="content-icon-container">
          
<div class="theme-toggle-container theme-toggle-content">
            <button class="theme-toggle">
              <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
              <svg class="theme-icon-when-auto"><use href="#svg-sun-half"></use></svg>
              <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
              <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
            </button>
          </div>
          <label class="toc-overlay-icon toc-content-icon" for="__toc">
            <div class="visually-hidden">Toggle table of contents sidebar</div>
            <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
          </label>
        </div>
        <article role="main">
          <section id="runtime">
<h1>Runtime<a class="headerlink" href="#runtime" title="Link to this heading">#</a></h1>
<p>Runtime domain exposes JavaScript runtime by means of remote evaluation and mirror objects.
Evaluation results are returned as mirror object that expose object type, string representation
and unique identifier that can be used for further object reference. Original objects are
maintained in memory unless they are either explicitly released or are released along with the
other objects in their object group.</p>
<ul class="simple" id="module-nodriver.cdp.runtime">
<li><p><a class="reference internal" href="#types">Types</a></p></li>
<li><p><a class="reference internal" href="#commands">Commands</a></p></li>
<li><p><a class="reference internal" href="#events">Events</a></p></li>
</ul>
<section id="types">
<h2>Types<a class="headerlink" href="#types" title="Link to this heading">#</a></h2>
<p>Generally, you do not need to instantiate CDP types
yourself. Instead, the API creates objects for you as return
values from commands, and then you can use those objects as
arguments to other commands.</p>
<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.ScriptId">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">ScriptId</span></span><a class="reference internal" href="../../_modules/nodriver/cdp/runtime.html#ScriptId"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.runtime.ScriptId" title="Link to this definition">#</a></dt>
<dd><p>Unique script identifier.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.SerializationOptions">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">SerializationOptions</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">serialization</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">max_depth</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">additional_parameters</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/runtime.html#SerializationOptions"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.runtime.SerializationOptions" title="Link to this definition">#</a></dt>
<dd><p>Represents options for serialization. Overrides <code class="docutils literal notranslate"><span class="pre">generatePreview</span></code> and <code class="docutils literal notranslate"><span class="pre">returnByValue</span></code>.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.SerializationOptions.serialization">
<span class="sig-name descname"><span class="pre">serialization</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.runtime.SerializationOptions.serialization" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.SerializationOptions.max_depth">
<span class="sig-name descname"><span class="pre">max_depth</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.runtime.SerializationOptions.max_depth" title="Link to this definition">#</a></dt>
<dd><p>Deep serialization depth. Default is full depth. Respected only in <code class="docutils literal notranslate"><span class="pre">deep</span></code> serialization mode.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.SerializationOptions.additional_parameters">
<span class="sig-name descname"><span class="pre">additional_parameters</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">dict</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.runtime.SerializationOptions.additional_parameters" title="Link to this definition">#</a></dt>
<dd><p>Embedder-specific parameters. For example if connected to V8 in Chrome these control DOM
serialization via <code class="docutils literal notranslate"><span class="pre">maxNodeDepth:</span> <span class="pre">integer</span></code> and <code class="docutils literal notranslate"><span class="pre">includeShadowTree:</span> <span class="pre">&quot;none&quot;</span> <span class="pre">``</span> <span class="pre">&quot;open&quot;</span> <span class="pre">``</span> <span class="pre">&quot;all&quot;</span></code>.
Values can be only of type string or integer.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.DeepSerializedValue">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">DeepSerializedValue</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">type_</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">object_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">weak_local_object_reference</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/runtime.html#DeepSerializedValue"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.runtime.DeepSerializedValue" title="Link to this definition">#</a></dt>
<dd><p>Represents deep serialized value.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.DeepSerializedValue.type_">
<span class="sig-name descname"><span class="pre">type_</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.runtime.DeepSerializedValue.type_" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.DeepSerializedValue.value">
<span class="sig-name descname"><span class="pre">value</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.runtime.DeepSerializedValue.value" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.DeepSerializedValue.object_id">
<span class="sig-name descname"><span class="pre">object_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.runtime.DeepSerializedValue.object_id" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.DeepSerializedValue.weak_local_object_reference">
<span class="sig-name descname"><span class="pre">weak_local_object_reference</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.runtime.DeepSerializedValue.weak_local_object_reference" title="Link to this definition">#</a></dt>
<dd><p>Set if value reference met more then once during serialization. In such
case, value is provided only to one of the serialized values. Unique
per value in the scope of one CDP call.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.RemoteObjectId">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">RemoteObjectId</span></span><a class="reference internal" href="../../_modules/nodriver/cdp/runtime.html#RemoteObjectId"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.runtime.RemoteObjectId" title="Link to this definition">#</a></dt>
<dd><p>Unique object identifier.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.UnserializableValue">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">UnserializableValue</span></span><a class="reference internal" href="../../_modules/nodriver/cdp/runtime.html#UnserializableValue"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.runtime.UnserializableValue" title="Link to this definition">#</a></dt>
<dd><p>Primitive value which cannot be JSON-stringified. Includes values <code class="docutils literal notranslate"><span class="pre">-0</span></code>, <code class="docutils literal notranslate"><span class="pre">NaN</span></code>, <code class="docutils literal notranslate"><span class="pre">Infinity</span></code>,
<code class="docutils literal notranslate"><span class="pre">-Infinity</span></code>, and bigint literals.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.RemoteObject">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">RemoteObject</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">type_</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">subtype</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">class_name</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">unserializable_value</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">description</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">deep_serialized_value</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">object_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">preview</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">custom_preview</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/runtime.html#RemoteObject"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.runtime.RemoteObject" title="Link to this definition">#</a></dt>
<dd><p>Mirror object referencing original JavaScript object.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.RemoteObject.type_">
<span class="sig-name descname"><span class="pre">type_</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.runtime.RemoteObject.type_" title="Link to this definition">#</a></dt>
<dd><p>Object type.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.RemoteObject.subtype">
<span class="sig-name descname"><span class="pre">subtype</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.runtime.RemoteObject.subtype" title="Link to this definition">#</a></dt>
<dd><p>Object subtype hint. Specified for <code class="docutils literal notranslate"><span class="pre">object</span></code> type values only.
NOTE: If you change anything here, make sure to also update
<code class="docutils literal notranslate"><span class="pre">subtype</span></code> in <code class="docutils literal notranslate"><span class="pre">ObjectPreview</span></code> and <code class="docutils literal notranslate"><span class="pre">PropertyPreview</span></code> below.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.RemoteObject.class_name">
<span class="sig-name descname"><span class="pre">class_name</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.runtime.RemoteObject.class_name" title="Link to this definition">#</a></dt>
<dd><p>Object class (constructor) name. Specified for <code class="docutils literal notranslate"><span class="pre">object</span></code> type values only.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.RemoteObject.value">
<span class="sig-name descname"><span class="pre">value</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.runtime.RemoteObject.value" title="Link to this definition">#</a></dt>
<dd><p>Remote object value in case of primitive values or JSON values (if it was requested).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.RemoteObject.unserializable_value">
<span class="sig-name descname"><span class="pre">unserializable_value</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.runtime.UnserializableValue" title="nodriver.cdp.runtime.UnserializableValue"><code class="xref py py-class docutils literal notranslate"><span class="pre">UnserializableValue</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.runtime.RemoteObject.unserializable_value" title="Link to this definition">#</a></dt>
<dd><p>Primitive value which can not be JSON-stringified does not have <code class="docutils literal notranslate"><span class="pre">value</span></code>, but gets this
property.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.RemoteObject.description">
<span class="sig-name descname"><span class="pre">description</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.runtime.RemoteObject.description" title="Link to this definition">#</a></dt>
<dd><p>String representation of the object.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.RemoteObject.deep_serialized_value">
<span class="sig-name descname"><span class="pre">deep_serialized_value</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.runtime.DeepSerializedValue" title="nodriver.cdp.runtime.DeepSerializedValue"><code class="xref py py-class docutils literal notranslate"><span class="pre">DeepSerializedValue</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.runtime.RemoteObject.deep_serialized_value" title="Link to this definition">#</a></dt>
<dd><p>Deep serialized value.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.RemoteObject.object_id">
<span class="sig-name descname"><span class="pre">object_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.runtime.RemoteObjectId" title="nodriver.cdp.runtime.RemoteObjectId"><code class="xref py py-class docutils literal notranslate"><span class="pre">RemoteObjectId</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.runtime.RemoteObject.object_id" title="Link to this definition">#</a></dt>
<dd><p>Unique object identifier (for non-primitive values).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.RemoteObject.preview">
<span class="sig-name descname"><span class="pre">preview</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.runtime.ObjectPreview" title="nodriver.cdp.runtime.ObjectPreview"><code class="xref py py-class docutils literal notranslate"><span class="pre">ObjectPreview</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.runtime.RemoteObject.preview" title="Link to this definition">#</a></dt>
<dd><p>Preview containing abbreviated property values. Specified for <code class="docutils literal notranslate"><span class="pre">object</span></code> type values only.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.RemoteObject.custom_preview">
<span class="sig-name descname"><span class="pre">custom_preview</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.runtime.CustomPreview" title="nodriver.cdp.runtime.CustomPreview"><code class="xref py py-class docutils literal notranslate"><span class="pre">CustomPreview</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.runtime.RemoteObject.custom_preview" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.CustomPreview">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">CustomPreview</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">header</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">body_getter_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/runtime.html#CustomPreview"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.runtime.CustomPreview" title="Link to this definition">#</a></dt>
<dd><dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.CustomPreview.header">
<span class="sig-name descname"><span class="pre">header</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.runtime.CustomPreview.header" title="Link to this definition">#</a></dt>
<dd><p>The JSON-stringified result of formatter.header(object, config) call.
It contains json ML array that represents RemoteObject.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.CustomPreview.body_getter_id">
<span class="sig-name descname"><span class="pre">body_getter_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.runtime.RemoteObjectId" title="nodriver.cdp.runtime.RemoteObjectId"><code class="xref py py-class docutils literal notranslate"><span class="pre">RemoteObjectId</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.runtime.CustomPreview.body_getter_id" title="Link to this definition">#</a></dt>
<dd><p>If formatter returns true as a result of formatter.hasBody call then bodyGetterId will
contain RemoteObjectId for the function that returns result of formatter.body(object, config) call.
The result value is json ML array.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.ObjectPreview">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">ObjectPreview</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">type_</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">overflow</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">properties</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">subtype</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">description</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">entries</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/runtime.html#ObjectPreview"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.runtime.ObjectPreview" title="Link to this definition">#</a></dt>
<dd><p>Object containing abbreviated remote object value.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.ObjectPreview.type_">
<span class="sig-name descname"><span class="pre">type_</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.runtime.ObjectPreview.type_" title="Link to this definition">#</a></dt>
<dd><p>Object type.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.ObjectPreview.overflow">
<span class="sig-name descname"><span class="pre">overflow</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a></em><a class="headerlink" href="#nodriver.cdp.runtime.ObjectPreview.overflow" title="Link to this definition">#</a></dt>
<dd><p>True iff some of the properties or entries of the original object did not fit.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.ObjectPreview.properties">
<span class="sig-name descname"><span class="pre">properties</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.runtime.PropertyPreview" title="nodriver.cdp.runtime.PropertyPreview"><code class="xref py py-class docutils literal notranslate"><span class="pre">PropertyPreview</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.runtime.ObjectPreview.properties" title="Link to this definition">#</a></dt>
<dd><p>List of the properties.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.ObjectPreview.subtype">
<span class="sig-name descname"><span class="pre">subtype</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.runtime.ObjectPreview.subtype" title="Link to this definition">#</a></dt>
<dd><p>Object subtype hint. Specified for <code class="docutils literal notranslate"><span class="pre">object</span></code> type values only.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.ObjectPreview.description">
<span class="sig-name descname"><span class="pre">description</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.runtime.ObjectPreview.description" title="Link to this definition">#</a></dt>
<dd><p>String representation of the object.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.ObjectPreview.entries">
<span class="sig-name descname"><span class="pre">entries</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.runtime.EntryPreview" title="nodriver.cdp.runtime.EntryPreview"><code class="xref py py-class docutils literal notranslate"><span class="pre">EntryPreview</span></code></a><span class="pre">]]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.runtime.ObjectPreview.entries" title="Link to this definition">#</a></dt>
<dd><p>List of the entries. Specified for <code class="docutils literal notranslate"><span class="pre">map</span></code> and <code class="docutils literal notranslate"><span class="pre">set</span></code> subtype values only.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.PropertyPreview">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">PropertyPreview</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type_</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value_preview</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">subtype</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/runtime.html#PropertyPreview"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.runtime.PropertyPreview" title="Link to this definition">#</a></dt>
<dd><dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.PropertyPreview.name">
<span class="sig-name descname"><span class="pre">name</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.runtime.PropertyPreview.name" title="Link to this definition">#</a></dt>
<dd><p>Property name.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.PropertyPreview.type_">
<span class="sig-name descname"><span class="pre">type_</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.runtime.PropertyPreview.type_" title="Link to this definition">#</a></dt>
<dd><p>Object type. Accessor means that the property itself is an accessor property.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.PropertyPreview.value">
<span class="sig-name descname"><span class="pre">value</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.runtime.PropertyPreview.value" title="Link to this definition">#</a></dt>
<dd><p>User-friendly property value string.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.PropertyPreview.value_preview">
<span class="sig-name descname"><span class="pre">value_preview</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.runtime.ObjectPreview" title="nodriver.cdp.runtime.ObjectPreview"><code class="xref py py-class docutils literal notranslate"><span class="pre">ObjectPreview</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.runtime.PropertyPreview.value_preview" title="Link to this definition">#</a></dt>
<dd><p>Nested value preview.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.PropertyPreview.subtype">
<span class="sig-name descname"><span class="pre">subtype</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.runtime.PropertyPreview.subtype" title="Link to this definition">#</a></dt>
<dd><p>Object subtype hint. Specified for <code class="docutils literal notranslate"><span class="pre">object</span></code> type values only.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.EntryPreview">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">EntryPreview</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">key</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/runtime.html#EntryPreview"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.runtime.EntryPreview" title="Link to this definition">#</a></dt>
<dd><dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.EntryPreview.value">
<span class="sig-name descname"><span class="pre">value</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.runtime.ObjectPreview" title="nodriver.cdp.runtime.ObjectPreview"><code class="xref py py-class docutils literal notranslate"><span class="pre">ObjectPreview</span></code></a></em><a class="headerlink" href="#nodriver.cdp.runtime.EntryPreview.value" title="Link to this definition">#</a></dt>
<dd><p>Preview of the value.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.EntryPreview.key">
<span class="sig-name descname"><span class="pre">key</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.runtime.ObjectPreview" title="nodriver.cdp.runtime.ObjectPreview"><code class="xref py py-class docutils literal notranslate"><span class="pre">ObjectPreview</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.runtime.EntryPreview.key" title="Link to this definition">#</a></dt>
<dd><p>Preview of the key. Specified for map-like collection entries.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.PropertyDescriptor">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">PropertyDescriptor</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">configurable</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">enumerable</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">writable</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">get</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">set_</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">was_thrown</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">is_own</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">symbol</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/runtime.html#PropertyDescriptor"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.runtime.PropertyDescriptor" title="Link to this definition">#</a></dt>
<dd><p>Object property descriptor.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.PropertyDescriptor.name">
<span class="sig-name descname"><span class="pre">name</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.runtime.PropertyDescriptor.name" title="Link to this definition">#</a></dt>
<dd><p>Property name or symbol description.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.PropertyDescriptor.configurable">
<span class="sig-name descname"><span class="pre">configurable</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a></em><a class="headerlink" href="#nodriver.cdp.runtime.PropertyDescriptor.configurable" title="Link to this definition">#</a></dt>
<dd><p>True if the type of this property descriptor may be changed and if the property may be
deleted from the corresponding object.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.PropertyDescriptor.enumerable">
<span class="sig-name descname"><span class="pre">enumerable</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a></em><a class="headerlink" href="#nodriver.cdp.runtime.PropertyDescriptor.enumerable" title="Link to this definition">#</a></dt>
<dd><p>True if this property shows up during enumeration of the properties on the corresponding
object.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.PropertyDescriptor.value">
<span class="sig-name descname"><span class="pre">value</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.runtime.RemoteObject" title="nodriver.cdp.runtime.RemoteObject"><code class="xref py py-class docutils literal notranslate"><span class="pre">RemoteObject</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.runtime.PropertyDescriptor.value" title="Link to this definition">#</a></dt>
<dd><p>The value associated with the property.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.PropertyDescriptor.writable">
<span class="sig-name descname"><span class="pre">writable</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.runtime.PropertyDescriptor.writable" title="Link to this definition">#</a></dt>
<dd><p>True if the value associated with the property may be changed (data descriptors only).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.PropertyDescriptor.get">
<span class="sig-name descname"><span class="pre">get</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.runtime.RemoteObject" title="nodriver.cdp.runtime.RemoteObject"><code class="xref py py-class docutils literal notranslate"><span class="pre">RemoteObject</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.runtime.PropertyDescriptor.get" title="Link to this definition">#</a></dt>
<dd><p>A function which serves as a getter for the property, or <code class="docutils literal notranslate"><span class="pre">undefined</span></code> if there is no getter
(accessor descriptors only).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.PropertyDescriptor.set_">
<span class="sig-name descname"><span class="pre">set_</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.runtime.RemoteObject" title="nodriver.cdp.runtime.RemoteObject"><code class="xref py py-class docutils literal notranslate"><span class="pre">RemoteObject</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.runtime.PropertyDescriptor.set_" title="Link to this definition">#</a></dt>
<dd><p>A function which serves as a setter for the property, or <code class="docutils literal notranslate"><span class="pre">undefined</span></code> if there is no setter
(accessor descriptors only).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.PropertyDescriptor.was_thrown">
<span class="sig-name descname"><span class="pre">was_thrown</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.runtime.PropertyDescriptor.was_thrown" title="Link to this definition">#</a></dt>
<dd><p>True if the result was thrown during the evaluation.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.PropertyDescriptor.is_own">
<span class="sig-name descname"><span class="pre">is_own</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.runtime.PropertyDescriptor.is_own" title="Link to this definition">#</a></dt>
<dd><p>True if the property is owned for the object.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.PropertyDescriptor.symbol">
<span class="sig-name descname"><span class="pre">symbol</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.runtime.RemoteObject" title="nodriver.cdp.runtime.RemoteObject"><code class="xref py py-class docutils literal notranslate"><span class="pre">RemoteObject</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.runtime.PropertyDescriptor.symbol" title="Link to this definition">#</a></dt>
<dd><p>Property symbol object, if the property is of the <code class="docutils literal notranslate"><span class="pre">symbol</span></code> type.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.InternalPropertyDescriptor">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">InternalPropertyDescriptor</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/runtime.html#InternalPropertyDescriptor"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.runtime.InternalPropertyDescriptor" title="Link to this definition">#</a></dt>
<dd><p>Object internal property descriptor. This property isn’t normally visible in JavaScript code.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.InternalPropertyDescriptor.name">
<span class="sig-name descname"><span class="pre">name</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.runtime.InternalPropertyDescriptor.name" title="Link to this definition">#</a></dt>
<dd><p>Conventional property name.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.InternalPropertyDescriptor.value">
<span class="sig-name descname"><span class="pre">value</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.runtime.RemoteObject" title="nodriver.cdp.runtime.RemoteObject"><code class="xref py py-class docutils literal notranslate"><span class="pre">RemoteObject</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.runtime.InternalPropertyDescriptor.value" title="Link to this definition">#</a></dt>
<dd><p>The value associated with the property.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.PrivatePropertyDescriptor">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">PrivatePropertyDescriptor</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">get</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">set_</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/runtime.html#PrivatePropertyDescriptor"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.runtime.PrivatePropertyDescriptor" title="Link to this definition">#</a></dt>
<dd><p>Object private field descriptor.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.PrivatePropertyDescriptor.name">
<span class="sig-name descname"><span class="pre">name</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.runtime.PrivatePropertyDescriptor.name" title="Link to this definition">#</a></dt>
<dd><p>Private property name.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.PrivatePropertyDescriptor.value">
<span class="sig-name descname"><span class="pre">value</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.runtime.RemoteObject" title="nodriver.cdp.runtime.RemoteObject"><code class="xref py py-class docutils literal notranslate"><span class="pre">RemoteObject</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.runtime.PrivatePropertyDescriptor.value" title="Link to this definition">#</a></dt>
<dd><p>The value associated with the private property.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.PrivatePropertyDescriptor.get">
<span class="sig-name descname"><span class="pre">get</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.runtime.RemoteObject" title="nodriver.cdp.runtime.RemoteObject"><code class="xref py py-class docutils literal notranslate"><span class="pre">RemoteObject</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.runtime.PrivatePropertyDescriptor.get" title="Link to this definition">#</a></dt>
<dd><p>A function which serves as a getter for the private property,
or <code class="docutils literal notranslate"><span class="pre">undefined</span></code> if there is no getter (accessor descriptors only).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.PrivatePropertyDescriptor.set_">
<span class="sig-name descname"><span class="pre">set_</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.runtime.RemoteObject" title="nodriver.cdp.runtime.RemoteObject"><code class="xref py py-class docutils literal notranslate"><span class="pre">RemoteObject</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.runtime.PrivatePropertyDescriptor.set_" title="Link to this definition">#</a></dt>
<dd><p>A function which serves as a setter for the private property,
or <code class="docutils literal notranslate"><span class="pre">undefined</span></code> if there is no setter (accessor descriptors only).</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.CallArgument">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">CallArgument</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">unserializable_value</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">object_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/runtime.html#CallArgument"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.runtime.CallArgument" title="Link to this definition">#</a></dt>
<dd><p>Represents function call argument. Either remote object id <code class="docutils literal notranslate"><span class="pre">objectId</span></code>, primitive <code class="docutils literal notranslate"><span class="pre">value</span></code>,
unserializable primitive value or neither of (for undefined) them should be specified.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.CallArgument.value">
<span class="sig-name descname"><span class="pre">value</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.runtime.CallArgument.value" title="Link to this definition">#</a></dt>
<dd><p>Primitive value or serializable javascript object.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.CallArgument.unserializable_value">
<span class="sig-name descname"><span class="pre">unserializable_value</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.runtime.UnserializableValue" title="nodriver.cdp.runtime.UnserializableValue"><code class="xref py py-class docutils literal notranslate"><span class="pre">UnserializableValue</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.runtime.CallArgument.unserializable_value" title="Link to this definition">#</a></dt>
<dd><p>Primitive value which can not be JSON-stringified.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.CallArgument.object_id">
<span class="sig-name descname"><span class="pre">object_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.runtime.RemoteObjectId" title="nodriver.cdp.runtime.RemoteObjectId"><code class="xref py py-class docutils literal notranslate"><span class="pre">RemoteObjectId</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.runtime.CallArgument.object_id" title="Link to this definition">#</a></dt>
<dd><p>Remote object handle.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.ExecutionContextId">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">ExecutionContextId</span></span><a class="reference internal" href="../../_modules/nodriver/cdp/runtime.html#ExecutionContextId"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.runtime.ExecutionContextId" title="Link to this definition">#</a></dt>
<dd><p>Id of an execution context.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.ExecutionContextDescription">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">ExecutionContextDescription</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">id_</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">origin</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">unique_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">aux_data</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/runtime.html#ExecutionContextDescription"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.runtime.ExecutionContextDescription" title="Link to this definition">#</a></dt>
<dd><p>Description of an isolated world.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.ExecutionContextDescription.id_">
<span class="sig-name descname"><span class="pre">id_</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.runtime.ExecutionContextId" title="nodriver.cdp.runtime.ExecutionContextId"><code class="xref py py-class docutils literal notranslate"><span class="pre">ExecutionContextId</span></code></a></em><a class="headerlink" href="#nodriver.cdp.runtime.ExecutionContextDescription.id_" title="Link to this definition">#</a></dt>
<dd><p>Unique id of the execution context. It can be used to specify in which execution context
script evaluation should be performed.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.ExecutionContextDescription.origin">
<span class="sig-name descname"><span class="pre">origin</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.runtime.ExecutionContextDescription.origin" title="Link to this definition">#</a></dt>
<dd><p>Execution context origin.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.ExecutionContextDescription.name">
<span class="sig-name descname"><span class="pre">name</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.runtime.ExecutionContextDescription.name" title="Link to this definition">#</a></dt>
<dd><p>Human readable name describing given context.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.ExecutionContextDescription.unique_id">
<span class="sig-name descname"><span class="pre">unique_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.runtime.ExecutionContextDescription.unique_id" title="Link to this definition">#</a></dt>
<dd><p>A system-unique execution context identifier. Unlike the id, this is unique across
multiple processes, so can be reliably used to identify specific context while backend
performs a cross-process navigation.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.ExecutionContextDescription.aux_data">
<span class="sig-name descname"><span class="pre">aux_data</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">dict</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.runtime.ExecutionContextDescription.aux_data" title="Link to this definition">#</a></dt>
<dd><p>‘default’``’isolated’``’worker’, frameId: string}</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>Embedder-specific auxiliary data likely matching {isDefault</p>
</dd>
<dt class="field-even">Type<span class="colon">:</span></dt>
<dd class="field-even"><p>boolean, <a class="reference external" href="https://docs.python.org/3/library/functions.html#type" title="(in Python v3.13)">type</a></p>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.ExceptionDetails">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">ExceptionDetails</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">exception_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">text</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">line_number</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">column_number</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">script_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">url</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">stack_trace</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">exception</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">execution_context_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">exception_meta_data</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/runtime.html#ExceptionDetails"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.runtime.ExceptionDetails" title="Link to this definition">#</a></dt>
<dd><p>Detailed information about exception (or error) that was thrown during script compilation or
execution.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.ExceptionDetails.exception_id">
<span class="sig-name descname"><span class="pre">exception_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></em><a class="headerlink" href="#nodriver.cdp.runtime.ExceptionDetails.exception_id" title="Link to this definition">#</a></dt>
<dd><p>Exception id.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.ExceptionDetails.text">
<span class="sig-name descname"><span class="pre">text</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.runtime.ExceptionDetails.text" title="Link to this definition">#</a></dt>
<dd><p>Exception text, which should be used together with exception object when available.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.ExceptionDetails.line_number">
<span class="sig-name descname"><span class="pre">line_number</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></em><a class="headerlink" href="#nodriver.cdp.runtime.ExceptionDetails.line_number" title="Link to this definition">#</a></dt>
<dd><p>Line number of the exception location (0-based).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.ExceptionDetails.column_number">
<span class="sig-name descname"><span class="pre">column_number</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></em><a class="headerlink" href="#nodriver.cdp.runtime.ExceptionDetails.column_number" title="Link to this definition">#</a></dt>
<dd><p>Column number of the exception location (0-based).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.ExceptionDetails.script_id">
<span class="sig-name descname"><span class="pre">script_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.runtime.ScriptId" title="nodriver.cdp.runtime.ScriptId"><code class="xref py py-class docutils literal notranslate"><span class="pre">ScriptId</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.runtime.ExceptionDetails.script_id" title="Link to this definition">#</a></dt>
<dd><p>Script ID of the exception location.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.ExceptionDetails.url">
<span class="sig-name descname"><span class="pre">url</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.runtime.ExceptionDetails.url" title="Link to this definition">#</a></dt>
<dd><p>URL of the exception location, to be used when the script was not reported.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.ExceptionDetails.stack_trace">
<span class="sig-name descname"><span class="pre">stack_trace</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.runtime.StackTrace" title="nodriver.cdp.runtime.StackTrace"><code class="xref py py-class docutils literal notranslate"><span class="pre">StackTrace</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.runtime.ExceptionDetails.stack_trace" title="Link to this definition">#</a></dt>
<dd><p>JavaScript stack trace if available.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.ExceptionDetails.exception">
<span class="sig-name descname"><span class="pre">exception</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.runtime.RemoteObject" title="nodriver.cdp.runtime.RemoteObject"><code class="xref py py-class docutils literal notranslate"><span class="pre">RemoteObject</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.runtime.ExceptionDetails.exception" title="Link to this definition">#</a></dt>
<dd><p>Exception object if available.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.ExceptionDetails.execution_context_id">
<span class="sig-name descname"><span class="pre">execution_context_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.runtime.ExecutionContextId" title="nodriver.cdp.runtime.ExecutionContextId"><code class="xref py py-class docutils literal notranslate"><span class="pre">ExecutionContextId</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.runtime.ExceptionDetails.execution_context_id" title="Link to this definition">#</a></dt>
<dd><p>Identifier of the context where exception happened.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.ExceptionDetails.exception_meta_data">
<span class="sig-name descname"><span class="pre">exception_meta_data</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">dict</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.runtime.ExceptionDetails.exception_meta_data" title="Link to this definition">#</a></dt>
<dd><p>Dictionary with entries of meta data that the client associated
with this exception, such as information about associated network
requests, etc.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.Timestamp">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">Timestamp</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/runtime.html#Timestamp"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.runtime.Timestamp" title="Link to this definition">#</a></dt>
<dd><p>Number of milliseconds since epoch.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.TimeDelta">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">TimeDelta</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/runtime.html#TimeDelta"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.runtime.TimeDelta" title="Link to this definition">#</a></dt>
<dd><p>Number of milliseconds.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.CallFrame">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">CallFrame</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">function_name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">script_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">url</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">line_number</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">column_number</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/runtime.html#CallFrame"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.runtime.CallFrame" title="Link to this definition">#</a></dt>
<dd><p>Stack entry for runtime errors and assertions.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.CallFrame.function_name">
<span class="sig-name descname"><span class="pre">function_name</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.runtime.CallFrame.function_name" title="Link to this definition">#</a></dt>
<dd><p>JavaScript function name.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.CallFrame.script_id">
<span class="sig-name descname"><span class="pre">script_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.runtime.ScriptId" title="nodriver.cdp.runtime.ScriptId"><code class="xref py py-class docutils literal notranslate"><span class="pre">ScriptId</span></code></a></em><a class="headerlink" href="#nodriver.cdp.runtime.CallFrame.script_id" title="Link to this definition">#</a></dt>
<dd><p>JavaScript script id.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.CallFrame.url">
<span class="sig-name descname"><span class="pre">url</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.runtime.CallFrame.url" title="Link to this definition">#</a></dt>
<dd><p>JavaScript script name or url.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.CallFrame.line_number">
<span class="sig-name descname"><span class="pre">line_number</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></em><a class="headerlink" href="#nodriver.cdp.runtime.CallFrame.line_number" title="Link to this definition">#</a></dt>
<dd><p>JavaScript script line number (0-based).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.CallFrame.column_number">
<span class="sig-name descname"><span class="pre">column_number</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></em><a class="headerlink" href="#nodriver.cdp.runtime.CallFrame.column_number" title="Link to this definition">#</a></dt>
<dd><p>JavaScript script column number (0-based).</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.StackTrace">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">StackTrace</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">call_frames</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">description</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">parent</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">parent_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/runtime.html#StackTrace"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.runtime.StackTrace" title="Link to this definition">#</a></dt>
<dd><p>Call frames for assertions or error messages.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.StackTrace.call_frames">
<span class="sig-name descname"><span class="pre">call_frames</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.runtime.CallFrame" title="nodriver.cdp.runtime.CallFrame"><code class="xref py py-class docutils literal notranslate"><span class="pre">CallFrame</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.runtime.StackTrace.call_frames" title="Link to this definition">#</a></dt>
<dd><p>JavaScript function name.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.StackTrace.description">
<span class="sig-name descname"><span class="pre">description</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.runtime.StackTrace.description" title="Link to this definition">#</a></dt>
<dd><p>String label of this stack trace. For async traces this may be a name of the function that
initiated the async call.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.StackTrace.parent">
<span class="sig-name descname"><span class="pre">parent</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.runtime.StackTrace" title="nodriver.cdp.runtime.StackTrace"><code class="xref py py-class docutils literal notranslate"><span class="pre">StackTrace</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.runtime.StackTrace.parent" title="Link to this definition">#</a></dt>
<dd><p>Asynchronous JavaScript stack trace that preceded this stack, if available.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.StackTrace.parent_id">
<span class="sig-name descname"><span class="pre">parent_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.runtime.StackTraceId" title="nodriver.cdp.runtime.StackTraceId"><code class="xref py py-class docutils literal notranslate"><span class="pre">StackTraceId</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.runtime.StackTrace.parent_id" title="Link to this definition">#</a></dt>
<dd><p>Asynchronous JavaScript stack trace that preceded this stack, if available.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.UniqueDebuggerId">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">UniqueDebuggerId</span></span><a class="reference internal" href="../../_modules/nodriver/cdp/runtime.html#UniqueDebuggerId"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.runtime.UniqueDebuggerId" title="Link to this definition">#</a></dt>
<dd><p>Unique identifier of current debugger.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.StackTraceId">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">StackTraceId</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">id_</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">debugger_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/runtime.html#StackTraceId"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.runtime.StackTraceId" title="Link to this definition">#</a></dt>
<dd><p>If <code class="docutils literal notranslate"><span class="pre">debuggerId</span></code> is set stack trace comes from another debugger and can be resolved there. This
allows to track cross-debugger calls. See <code class="docutils literal notranslate"><span class="pre">Runtime.StackTrace</span></code> and <code class="docutils literal notranslate"><span class="pre">Debugger.paused</span></code> for usages.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.StackTraceId.id_">
<span class="sig-name descname"><span class="pre">id_</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.runtime.StackTraceId.id_" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.StackTraceId.debugger_id">
<span class="sig-name descname"><span class="pre">debugger_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.runtime.UniqueDebuggerId" title="nodriver.cdp.runtime.UniqueDebuggerId"><code class="xref py py-class docutils literal notranslate"><span class="pre">UniqueDebuggerId</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.runtime.StackTraceId.debugger_id" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

</section>
<section id="commands">
<h2>Commands<a class="headerlink" href="#commands" title="Link to this heading">#</a></h2>
<p>Each command is a generator function. The return
type <code class="docutils literal notranslate"><span class="pre">Generator[x,</span> <span class="pre">y,</span> <span class="pre">z]</span></code> indicates that the generator
<em>yields</em> arguments of type <code class="docutils literal notranslate"><span class="pre">x</span></code>, it must be resumed with
an argument of type <code class="docutils literal notranslate"><span class="pre">y</span></code>, and it returns type <code class="docutils literal notranslate"><span class="pre">z</span></code>. In
this library, types <code class="docutils literal notranslate"><span class="pre">x</span></code> and <code class="docutils literal notranslate"><span class="pre">y</span></code> are the same for all
commands, and <code class="docutils literal notranslate"><span class="pre">z</span></code> is the return type you should pay attention
to. For more information, see
<a class="reference internal" href="../../readme.html#getting-started-commands"><span class="std std-ref">Getting Started: Commands</span></a>.</p>
<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.add_binding">
<span class="sig-name descname"><span class="pre">add_binding</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">execution_context_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">execution_context_name</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/runtime.html#add_binding"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.runtime.add_binding" title="Link to this definition">#</a></dt>
<dd><p>If executionContextId is empty, adds binding with the given name on the
global objects of all inspected contexts, including those created later,
bindings survive reloads.
Binding function takes exactly one argument, this argument should be string,
in case of any other input, function throws an exception.
Each binding function call produces Runtime.bindingCalled notification.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>name</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – </p></li>
<li><p><strong>execution_context_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="#nodriver.cdp.runtime.ExecutionContextId" title="nodriver.cdp.runtime.ExecutionContextId"><code class="xref py py-class docutils literal notranslate"><span class="pre">ExecutionContextId</span></code></a>]</span>) – <strong>(DEPRECATED)</strong> <strong>(EXPERIMENTAL)</strong> <em>(Optional)</em> If specified, the binding would only be exposed to the specified execution context. If omitted and <code class="docutils literal notranslate"><span class="pre">`executionContextName``</span></code> is not set, the binding is exposed to all execution contexts of the target. This parameter is mutually exclusive with <code class="docutils literal notranslate"><span class="pre">``executionContextName``</span></code>. Deprecated in favor of <code class="docutils literal notranslate"><span class="pre">``executionContextName``</span></code> due to an unclear use case and bugs in implementation (crbug.com/1169639). <code class="docutils literal notranslate"><span class="pre">``executionContextId``</span></code> will be removed in the future.</p></li>
<li><p><strong>execution_context_name</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]</span>) – <em>(Optional)</em> If specified, the binding is exposed to the executionContext with matching name, even for contexts created after the binding is added. See also <code class="docutils literal notranslate"><span class="pre">``ExecutionContext.name``</span></code> and <code class="docutils literal notranslate"><span class="pre">``worldName``</span></code> parameter to <code class="docutils literal notranslate"><span class="pre">``Page.addScriptToEvaluateOnNewDocument``</span></code>. This parameter is mutually exclusive with <code class="docutils literal notranslate"><span class="pre">``executionContextId`</span></code>.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.await_promise">
<span class="sig-name descname"><span class="pre">await_promise</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">promise_object_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">return_by_value</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">generate_preview</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/runtime.html#await_promise"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.runtime.await_promise" title="Link to this definition">#</a></dt>
<dd><p>Add handler to promise with given promise object id.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>promise_object_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.runtime.RemoteObjectId" title="nodriver.cdp.runtime.RemoteObjectId"><code class="xref py py-class docutils literal notranslate"><span class="pre">RemoteObjectId</span></code></a></span>) – Identifier of the promise.</p></li>
<li><p><strong>return_by_value</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <em>(Optional)</em> Whether the result is expected to be a JSON object that should be sent by value.</p></li>
<li><p><strong>generate_preview</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <em>(Optional)</em> Whether preview should be generated for the result.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Tuple" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Tuple</span></code></a>[<a class="reference internal" href="#nodriver.cdp.runtime.RemoteObject" title="nodriver.cdp.runtime.RemoteObject"><code class="xref py py-class docutils literal notranslate"><span class="pre">RemoteObject</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="#nodriver.cdp.runtime.ExceptionDetails" title="nodriver.cdp.runtime.ExceptionDetails"><code class="xref py py-class docutils literal notranslate"><span class="pre">ExceptionDetails</span></code></a>]]]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p><p>A tuple with the following items:</p>
<ol class="arabic simple" start="0">
<li><p><strong>result</strong> - Promise result. Will contain rejected value if promise was rejected.</p></li>
<li><p><strong>exceptionDetails</strong> - <em>(Optional)</em> Exception details if stack strace is available.</p></li>
</ol>
</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.call_function_on">
<span class="sig-name descname"><span class="pre">call_function_on</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">function_declaration</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">object_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">arguments</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">silent</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">return_by_value</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">generate_preview</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">user_gesture</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">await_promise</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">execution_context_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">object_group</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">throw_on_side_effect</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">unique_context_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">serialization_options</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/runtime.html#call_function_on"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.runtime.call_function_on" title="Link to this definition">#</a></dt>
<dd><p>Calls function with given declaration on the given object. Object group of the result is
inherited from the target object.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>function_declaration</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – Declaration of the function to call.</p></li>
<li><p><strong>object_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="#nodriver.cdp.runtime.RemoteObjectId" title="nodriver.cdp.runtime.RemoteObjectId"><code class="xref py py-class docutils literal notranslate"><span class="pre">RemoteObjectId</span></code></a>]</span>) – <em>(Optional)</em> Identifier of the object to call function on. Either objectId or executionContextId should be specified.</p></li>
<li><p><strong>arguments</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.runtime.CallArgument" title="nodriver.cdp.runtime.CallArgument"><code class="xref py py-class docutils literal notranslate"><span class="pre">CallArgument</span></code></a>]]</span>) – <em>(Optional)</em> Call arguments. All call arguments must belong to the same JavaScript world as the target object.</p></li>
<li><p><strong>silent</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <em>(Optional)</em> In silent mode exceptions thrown during evaluation are not reported and do not pause execution. Overrides <code class="docutils literal notranslate"><span class="pre">`setPauseOnException``</span></code> state.</p></li>
<li><p><strong>return_by_value</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <em>(Optional)</em> Whether the result is expected to be a JSON object which should be sent by value. Can be overriden by <code class="docutils literal notranslate"><span class="pre">``serializationOptions``</span></code>.</p></li>
<li><p><strong>generate_preview</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <strong>(EXPERIMENTAL)</strong> <em>(Optional)</em> Whether preview should be generated for the result.</p></li>
<li><p><strong>user_gesture</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <em>(Optional)</em> Whether execution should be treated as initiated by user in the UI.</p></li>
<li><p><strong>await_promise</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <em>(Optional)</em> Whether execution should <code class="docutils literal notranslate"><span class="pre">``await``</span></code> for resulting value and return once awaited promise is resolved.</p></li>
<li><p><strong>execution_context_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="#nodriver.cdp.runtime.ExecutionContextId" title="nodriver.cdp.runtime.ExecutionContextId"><code class="xref py py-class docutils literal notranslate"><span class="pre">ExecutionContextId</span></code></a>]</span>) – <em>(Optional)</em> Specifies execution context which global object will be used to call function on. Either executionContextId or objectId should be specified.</p></li>
<li><p><strong>object_group</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]</span>) – <em>(Optional)</em> Symbolic group name that can be used to release multiple objects. If objectGroup is not specified and objectId is, objectGroup will be inherited from object.</p></li>
<li><p><strong>throw_on_side_effect</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <strong>(EXPERIMENTAL)</strong> <em>(Optional)</em> Whether to throw an exception if side effect cannot be ruled out during evaluation.</p></li>
<li><p><strong>unique_context_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]</span>) – <strong>(EXPERIMENTAL)</strong> <em>(Optional)</em> An alternative way to specify the execution context to call function on. Compared to contextId that may be reused across processes, this is guaranteed to be system-unique, so it can be used to prevent accidental function call in context different than intended (e.g. as a result of navigation across process boundaries). This is mutually exclusive with <code class="docutils literal notranslate"><span class="pre">``executionContextId``</span></code>.</p></li>
<li><p><strong>serialization_options</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="#nodriver.cdp.runtime.SerializationOptions" title="nodriver.cdp.runtime.SerializationOptions"><code class="xref py py-class docutils literal notranslate"><span class="pre">SerializationOptions</span></code></a>]</span>) – <strong>(EXPERIMENTAL)</strong> <em>(Optional)</em> Specifies the result serialization. If provided, overrides <code class="docutils literal notranslate"><span class="pre">``generatePreview``</span></code> and <code class="docutils literal notranslate"><span class="pre">``returnByValue`</span></code>.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Tuple" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Tuple</span></code></a>[<a class="reference internal" href="#nodriver.cdp.runtime.RemoteObject" title="nodriver.cdp.runtime.RemoteObject"><code class="xref py py-class docutils literal notranslate"><span class="pre">RemoteObject</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="#nodriver.cdp.runtime.ExceptionDetails" title="nodriver.cdp.runtime.ExceptionDetails"><code class="xref py py-class docutils literal notranslate"><span class="pre">ExceptionDetails</span></code></a>]]]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p><p>A tuple with the following items:</p>
<ol class="arabic simple" start="0">
<li><p><strong>result</strong> - Call result.</p></li>
<li><p><strong>exceptionDetails</strong> - <em>(Optional)</em> Exception details.</p></li>
</ol>
</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.compile_script">
<span class="sig-name descname"><span class="pre">compile_script</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">expression</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">source_url</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">persist_script</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">execution_context_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/runtime.html#compile_script"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.runtime.compile_script" title="Link to this definition">#</a></dt>
<dd><p>Compiles expression.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>expression</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – Expression to compile.</p></li>
<li><p><strong>source_url</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – Source url to be set for the script.</p></li>
<li><p><strong>persist_script</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a></span>) – Specifies whether the compiled script should be persisted.</p></li>
<li><p><strong>execution_context_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="#nodriver.cdp.runtime.ExecutionContextId" title="nodriver.cdp.runtime.ExecutionContextId"><code class="xref py py-class docutils literal notranslate"><span class="pre">ExecutionContextId</span></code></a>]</span>) – <em>(Optional)</em> Specifies in which execution context to perform script run. If the parameter is omitted the evaluation will be performed in the context of the inspected page.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Tuple" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Tuple</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="#nodriver.cdp.runtime.ScriptId" title="nodriver.cdp.runtime.ScriptId"><code class="xref py py-class docutils literal notranslate"><span class="pre">ScriptId</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="#nodriver.cdp.runtime.ExceptionDetails" title="nodriver.cdp.runtime.ExceptionDetails"><code class="xref py py-class docutils literal notranslate"><span class="pre">ExceptionDetails</span></code></a>]]]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p><p>A tuple with the following items:</p>
<ol class="arabic simple" start="0">
<li><p><strong>scriptId</strong> - <em>(Optional)</em> Id of the script.</p></li>
<li><p><strong>exceptionDetails</strong> - <em>(Optional)</em> Exception details.</p></li>
</ol>
</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.disable">
<span class="sig-name descname"><span class="pre">disable</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/runtime.html#disable"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.runtime.disable" title="Link to this definition">#</a></dt>
<dd><p>Disables reporting of execution contexts creation.</p>
<dl class="field-list simple">
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.discard_console_entries">
<span class="sig-name descname"><span class="pre">discard_console_entries</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/runtime.html#discard_console_entries"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.runtime.discard_console_entries" title="Link to this definition">#</a></dt>
<dd><p>Discards collected exceptions and console API calls.</p>
<dl class="field-list simple">
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.enable">
<span class="sig-name descname"><span class="pre">enable</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/runtime.html#enable"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.runtime.enable" title="Link to this definition">#</a></dt>
<dd><p>Enables reporting of execution contexts creation by means of <code class="docutils literal notranslate"><span class="pre">executionContextCreated</span></code> event.
When the reporting gets enabled the event will be sent immediately for each existing execution
context.</p>
<dl class="field-list simple">
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.evaluate">
<span class="sig-name descname"><span class="pre">evaluate</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">expression</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">object_group</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">include_command_line_api</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">silent</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">context_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">return_by_value</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">generate_preview</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">user_gesture</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">await_promise</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">throw_on_side_effect</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">timeout</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">disable_breaks</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">repl_mode</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">allow_unsafe_eval_blocked_by_csp</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">unique_context_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">serialization_options</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/runtime.html#evaluate"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.runtime.evaluate" title="Link to this definition">#</a></dt>
<dd><p>Evaluates expression on global object.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>expression</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – Expression to evaluate.</p></li>
<li><p><strong>object_group</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]</span>) – <em>(Optional)</em> Symbolic group name that can be used to release multiple objects.</p></li>
<li><p><strong>include_command_line_api</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <em>(Optional)</em> Determines whether Command Line API should be available during the evaluation.</p></li>
<li><p><strong>silent</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <em>(Optional)</em> In silent mode exceptions thrown during evaluation are not reported and do not pause execution. Overrides <code class="docutils literal notranslate"><span class="pre">`setPauseOnException``</span></code> state.</p></li>
<li><p><strong>context_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="#nodriver.cdp.runtime.ExecutionContextId" title="nodriver.cdp.runtime.ExecutionContextId"><code class="xref py py-class docutils literal notranslate"><span class="pre">ExecutionContextId</span></code></a>]</span>) – <em>(Optional)</em> Specifies in which execution context to perform evaluation. If the parameter is omitted the evaluation will be performed in the context of the inspected page. This is mutually exclusive with <code class="docutils literal notranslate"><span class="pre">``uniqueContextId``</span></code>, which offers an alternative way to identify the execution context that is more reliable in a multi-process environment.</p></li>
<li><p><strong>return_by_value</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <em>(Optional)</em> Whether the result is expected to be a JSON object that should be sent by value.</p></li>
<li><p><strong>generate_preview</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <strong>(EXPERIMENTAL)</strong> <em>(Optional)</em> Whether preview should be generated for the result.</p></li>
<li><p><strong>user_gesture</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <em>(Optional)</em> Whether execution should be treated as initiated by user in the UI.</p></li>
<li><p><strong>await_promise</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <em>(Optional)</em> Whether execution should <code class="docutils literal notranslate"><span class="pre">``await``</span></code> for resulting value and return once awaited promise is resolved.</p></li>
<li><p><strong>throw_on_side_effect</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <strong>(EXPERIMENTAL)</strong> <em>(Optional)</em> Whether to throw an exception if side effect cannot be ruled out during evaluation. This implies <code class="docutils literal notranslate"><span class="pre">``disableBreaks``</span></code> below.</p></li>
<li><p><strong>timeout</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="#nodriver.cdp.runtime.TimeDelta" title="nodriver.cdp.runtime.TimeDelta"><code class="xref py py-class docutils literal notranslate"><span class="pre">TimeDelta</span></code></a>]</span>) – <strong>(EXPERIMENTAL)</strong> <em>(Optional)</em> Terminate execution after timing out (number of milliseconds).</p></li>
<li><p><strong>disable_breaks</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <strong>(EXPERIMENTAL)</strong> <em>(Optional)</em> Disable breakpoints during execution.</p></li>
<li><p><strong>repl_mode</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <strong>(EXPERIMENTAL)</strong> <em>(Optional)</em> Setting this flag to true enables <code class="docutils literal notranslate"><span class="pre">``let``</span></code> re-declaration and top-level <code class="docutils literal notranslate"><span class="pre">``await``</span></code>. Note that <code class="docutils literal notranslate"><span class="pre">``let``</span></code> variables can only be re-declared if they originate from <code class="docutils literal notranslate"><span class="pre">``replMode``</span></code> themselves.</p></li>
<li><p><strong>allow_unsafe_eval_blocked_by_csp</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <strong>(EXPERIMENTAL)</strong> <em>(Optional)</em> The Content Security Policy (CSP) for the target might block ‘unsafe-eval’ which includes eval(), Function(), setTimeout() and setInterval() when called with non-callable arguments. This flag bypasses CSP for this evaluation and allows unsafe-eval. Defaults to true.</p></li>
<li><p><strong>unique_context_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]</span>) – <strong>(EXPERIMENTAL)</strong> <em>(Optional)</em> An alternative way to specify the execution context to evaluate in. Compared to contextId that may be reused across processes, this is guaranteed to be system-unique, so it can be used to prevent accidental evaluation of the expression in context different than intended (e.g. as a result of navigation across process boundaries). This is mutually exclusive with <code class="docutils literal notranslate"><span class="pre">``contextId``</span></code>.</p></li>
<li><p><strong>serialization_options</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="#nodriver.cdp.runtime.SerializationOptions" title="nodriver.cdp.runtime.SerializationOptions"><code class="xref py py-class docutils literal notranslate"><span class="pre">SerializationOptions</span></code></a>]</span>) – <strong>(EXPERIMENTAL)</strong> <em>(Optional)</em> Specifies the result serialization. If provided, overrides <code class="docutils literal notranslate"><span class="pre">``generatePreview``</span></code> and <code class="docutils literal notranslate"><span class="pre">``returnByValue`</span></code>.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Tuple" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Tuple</span></code></a>[<a class="reference internal" href="#nodriver.cdp.runtime.RemoteObject" title="nodriver.cdp.runtime.RemoteObject"><code class="xref py py-class docutils literal notranslate"><span class="pre">RemoteObject</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="#nodriver.cdp.runtime.ExceptionDetails" title="nodriver.cdp.runtime.ExceptionDetails"><code class="xref py py-class docutils literal notranslate"><span class="pre">ExceptionDetails</span></code></a>]]]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p><p>A tuple with the following items:</p>
<ol class="arabic simple" start="0">
<li><p><strong>result</strong> - Evaluation result.</p></li>
<li><p><strong>exceptionDetails</strong> - <em>(Optional)</em> Exception details.</p></li>
</ol>
</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.get_exception_details">
<span class="sig-name descname"><span class="pre">get_exception_details</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">error_object_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/runtime.html#get_exception_details"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.runtime.get_exception_details" title="Link to this definition">#</a></dt>
<dd><p>This method tries to lookup and populate exception details for a
JavaScript Error object.
Note that the stackTrace portion of the resulting exceptionDetails will
only be populated if the Runtime domain was enabled at the time when the
Error was thrown.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>error_object_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.runtime.RemoteObjectId" title="nodriver.cdp.runtime.RemoteObjectId"><code class="xref py py-class docutils literal notranslate"><span class="pre">RemoteObjectId</span></code></a></span>) – The error object for which to resolve the exception details.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="#nodriver.cdp.runtime.ExceptionDetails" title="nodriver.cdp.runtime.ExceptionDetails"><code class="xref py py-class docutils literal notranslate"><span class="pre">ExceptionDetails</span></code></a>]]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.get_heap_usage">
<span class="sig-name descname"><span class="pre">get_heap_usage</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/runtime.html#get_heap_usage"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.runtime.get_heap_usage" title="Link to this definition">#</a></dt>
<dd><p>Returns the JavaScript heap usage.
It is the total usage of the corresponding isolate not scoped to a particular Runtime.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Tuple" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Tuple</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a>]]</span></p>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p><p>A tuple with the following items:</p>
<ol class="arabic simple" start="0">
<li><p><strong>usedSize</strong> - Used JavaScript heap size in bytes.</p></li>
<li><p><strong>totalSize</strong> - Allocated JavaScript heap size in bytes.</p></li>
<li><p><strong>embedderHeapUsedSize</strong> - Used size in bytes in the embedder’s garbage-collected heap.</p></li>
<li><p><strong>backingStorageSize</strong> - Size in bytes of backing storage for array buffers and external strings.</p></li>
</ol>
</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.get_isolate_id">
<span class="sig-name descname"><span class="pre">get_isolate_id</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/runtime.html#get_isolate_id"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.runtime.get_isolate_id" title="Link to this definition">#</a></dt>
<dd><p>Returns the isolate id.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]</span></p>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>The isolate id.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.get_properties">
<span class="sig-name descname"><span class="pre">get_properties</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">own_properties</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">accessor_properties_only</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">generate_preview</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">non_indexed_properties_only</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/runtime.html#get_properties"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.runtime.get_properties" title="Link to this definition">#</a></dt>
<dd><p>Returns properties of a given object. Object group of the result is inherited from the target
object.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>object_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.runtime.RemoteObjectId" title="nodriver.cdp.runtime.RemoteObjectId"><code class="xref py py-class docutils literal notranslate"><span class="pre">RemoteObjectId</span></code></a></span>) – Identifier of the object to return properties for.</p></li>
<li><p><strong>own_properties</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <em>(Optional)</em> If true, returns properties belonging only to the element itself, not to its prototype chain.</p></li>
<li><p><strong>accessor_properties_only</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <strong>(EXPERIMENTAL)</strong> <em>(Optional)</em> If true, returns accessor properties (with getter/setter) only; internal properties are not returned either.</p></li>
<li><p><strong>generate_preview</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <strong>(EXPERIMENTAL)</strong> <em>(Optional)</em> Whether preview should be generated for the results.</p></li>
<li><p><strong>non_indexed_properties_only</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <strong>(EXPERIMENTAL)</strong> <em>(Optional)</em> If true, returns non-indexed properties only.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Tuple" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Tuple</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.runtime.PropertyDescriptor" title="nodriver.cdp.runtime.PropertyDescriptor"><code class="xref py py-class docutils literal notranslate"><span class="pre">PropertyDescriptor</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.runtime.InternalPropertyDescriptor" title="nodriver.cdp.runtime.InternalPropertyDescriptor"><code class="xref py py-class docutils literal notranslate"><span class="pre">InternalPropertyDescriptor</span></code></a>]], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.runtime.PrivatePropertyDescriptor" title="nodriver.cdp.runtime.PrivatePropertyDescriptor"><code class="xref py py-class docutils literal notranslate"><span class="pre">PrivatePropertyDescriptor</span></code></a>]], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="#nodriver.cdp.runtime.ExceptionDetails" title="nodriver.cdp.runtime.ExceptionDetails"><code class="xref py py-class docutils literal notranslate"><span class="pre">ExceptionDetails</span></code></a>]]]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p><p>A tuple with the following items:</p>
<ol class="arabic simple" start="0">
<li><p><strong>result</strong> - Object properties.</p></li>
<li><p><strong>internalProperties</strong> - <em>(Optional)</em> Internal object properties (only of the element itself).</p></li>
<li><p><strong>privateProperties</strong> - <em>(Optional)</em> Object private properties.</p></li>
<li><p><strong>exceptionDetails</strong> - <em>(Optional)</em> Exception details.</p></li>
</ol>
</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.global_lexical_scope_names">
<span class="sig-name descname"><span class="pre">global_lexical_scope_names</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">execution_context_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/runtime.html#global_lexical_scope_names"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.runtime.global_lexical_scope_names" title="Link to this definition">#</a></dt>
<dd><p>Returns all let, const and class variables from global scope.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>execution_context_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="#nodriver.cdp.runtime.ExecutionContextId" title="nodriver.cdp.runtime.ExecutionContextId"><code class="xref py py-class docutils literal notranslate"><span class="pre">ExecutionContextId</span></code></a>]</span>) – <em>(Optional)</em> Specifies in which execution context to lookup global scope variables.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.query_objects">
<span class="sig-name descname"><span class="pre">query_objects</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">prototype_object_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">object_group</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/runtime.html#query_objects"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.runtime.query_objects" title="Link to this definition">#</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>prototype_object_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.runtime.RemoteObjectId" title="nodriver.cdp.runtime.RemoteObjectId"><code class="xref py py-class docutils literal notranslate"><span class="pre">RemoteObjectId</span></code></a></span>) – Identifier of the prototype to return objects for.</p></li>
<li><p><strong>object_group</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]</span>) – <em>(Optional)</em> Symbolic group name that can be used to release the results.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference internal" href="#nodriver.cdp.runtime.RemoteObject" title="nodriver.cdp.runtime.RemoteObject"><code class="xref py py-class docutils literal notranslate"><span class="pre">RemoteObject</span></code></a>]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>Array with objects.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.release_object">
<span class="sig-name descname"><span class="pre">release_object</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/runtime.html#release_object"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.runtime.release_object" title="Link to this definition">#</a></dt>
<dd><p>Releases remote object with given id.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>object_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.runtime.RemoteObjectId" title="nodriver.cdp.runtime.RemoteObjectId"><code class="xref py py-class docutils literal notranslate"><span class="pre">RemoteObjectId</span></code></a></span>) – Identifier of the object to release.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.release_object_group">
<span class="sig-name descname"><span class="pre">release_object_group</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object_group</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/runtime.html#release_object_group"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.runtime.release_object_group" title="Link to this definition">#</a></dt>
<dd><p>Releases all remote objects that belong to a given group.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>object_group</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – Symbolic object group name.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.remove_binding">
<span class="sig-name descname"><span class="pre">remove_binding</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/runtime.html#remove_binding"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.runtime.remove_binding" title="Link to this definition">#</a></dt>
<dd><p>This method does not remove binding function from global object but
unsubscribes current runtime agent from Runtime.bindingCalled notifications.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>name</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – </p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.run_if_waiting_for_debugger">
<span class="sig-name descname"><span class="pre">run_if_waiting_for_debugger</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/runtime.html#run_if_waiting_for_debugger"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.runtime.run_if_waiting_for_debugger" title="Link to this definition">#</a></dt>
<dd><p>Tells inspected instance to run if it was waiting for debugger to attach.</p>
<dl class="field-list simple">
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.run_script">
<span class="sig-name descname"><span class="pre">run_script</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">script_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">execution_context_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">object_group</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">silent</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">include_command_line_api</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">return_by_value</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">generate_preview</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">await_promise</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/runtime.html#run_script"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.runtime.run_script" title="Link to this definition">#</a></dt>
<dd><p>Runs script with given id in a given context.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>script_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.runtime.ScriptId" title="nodriver.cdp.runtime.ScriptId"><code class="xref py py-class docutils literal notranslate"><span class="pre">ScriptId</span></code></a></span>) – Id of the script to run.</p></li>
<li><p><strong>execution_context_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="#nodriver.cdp.runtime.ExecutionContextId" title="nodriver.cdp.runtime.ExecutionContextId"><code class="xref py py-class docutils literal notranslate"><span class="pre">ExecutionContextId</span></code></a>]</span>) – <em>(Optional)</em> Specifies in which execution context to perform script run. If the parameter is omitted the evaluation will be performed in the context of the inspected page.</p></li>
<li><p><strong>object_group</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]</span>) – <em>(Optional)</em> Symbolic group name that can be used to release multiple objects.</p></li>
<li><p><strong>silent</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <em>(Optional)</em> In silent mode exceptions thrown during evaluation are not reported and do not pause execution. Overrides <code class="docutils literal notranslate"><span class="pre">`setPauseOnException``</span></code> state.</p></li>
<li><p><strong>include_command_line_api</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <em>(Optional)</em> Determines whether Command Line API should be available during the evaluation.</p></li>
<li><p><strong>return_by_value</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <em>(Optional)</em> Whether the result is expected to be a JSON object which should be sent by value.</p></li>
<li><p><strong>generate_preview</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <em>(Optional)</em> Whether preview should be generated for the result.</p></li>
<li><p><strong>await_promise</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <em>(Optional)</em> Whether execution should <code class="docutils literal notranslate"><span class="pre">``await`</span></code> for resulting value and return once awaited promise is resolved.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Tuple" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Tuple</span></code></a>[<a class="reference internal" href="#nodriver.cdp.runtime.RemoteObject" title="nodriver.cdp.runtime.RemoteObject"><code class="xref py py-class docutils literal notranslate"><span class="pre">RemoteObject</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="#nodriver.cdp.runtime.ExceptionDetails" title="nodriver.cdp.runtime.ExceptionDetails"><code class="xref py py-class docutils literal notranslate"><span class="pre">ExceptionDetails</span></code></a>]]]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p><p>A tuple with the following items:</p>
<ol class="arabic simple" start="0">
<li><p><strong>result</strong> - Run result.</p></li>
<li><p><strong>exceptionDetails</strong> - <em>(Optional)</em> Exception details.</p></li>
</ol>
</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.set_async_call_stack_depth">
<span class="sig-name descname"><span class="pre">set_async_call_stack_depth</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">max_depth</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/runtime.html#set_async_call_stack_depth"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.runtime.set_async_call_stack_depth" title="Link to this definition">#</a></dt>
<dd><p>Enables or disables async call stacks tracking.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>max_depth</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></span>) – Maximum depth of async call stacks. Setting to <code class="docutils literal notranslate"><span class="pre">`0`</span></code> will effectively disable collecting async call stacks (default).</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.set_custom_object_formatter_enabled">
<span class="sig-name descname"><span class="pre">set_custom_object_formatter_enabled</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">enabled</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/runtime.html#set_custom_object_formatter_enabled"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.runtime.set_custom_object_formatter_enabled" title="Link to this definition">#</a></dt>
<dd><p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>enabled</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a></span>) – </p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.set_max_call_stack_size_to_capture">
<span class="sig-name descname"><span class="pre">set_max_call_stack_size_to_capture</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">size</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/runtime.html#set_max_call_stack_size_to_capture"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.runtime.set_max_call_stack_size_to_capture" title="Link to this definition">#</a></dt>
<dd><p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>size</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></span>) – </p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.terminate_execution">
<span class="sig-name descname"><span class="pre">terminate_execution</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/runtime.html#terminate_execution"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.runtime.terminate_execution" title="Link to this definition">#</a></dt>
<dd><p>Terminate current or next JavaScript execution.
Will cancel the termination when the outer-most script execution ends.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

</section>
<section id="events">
<h2>Events<a class="headerlink" href="#events" title="Link to this heading">#</a></h2>
<p>Generally, you do not need to instantiate CDP events
yourself. Instead, the API creates events for you and then
you use the event’s attributes.</p>
<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.BindingCalled">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">BindingCalled</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">payload</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">execution_context_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/runtime.html#BindingCalled"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.runtime.BindingCalled" title="Link to this definition">#</a></dt>
<dd><p><strong>EXPERIMENTAL</strong></p>
<p>Notification is issued every time when binding is called.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.BindingCalled.name">
<span class="sig-name descname"><span class="pre">name</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.runtime.BindingCalled.name" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.BindingCalled.payload">
<span class="sig-name descname"><span class="pre">payload</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.runtime.BindingCalled.payload" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.BindingCalled.execution_context_id">
<span class="sig-name descname"><span class="pre">execution_context_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.runtime.ExecutionContextId" title="nodriver.cdp.runtime.ExecutionContextId"><code class="xref py py-class docutils literal notranslate"><span class="pre">ExecutionContextId</span></code></a></em><a class="headerlink" href="#nodriver.cdp.runtime.BindingCalled.execution_context_id" title="Link to this definition">#</a></dt>
<dd><p>Identifier of the context where the call was made.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.ConsoleAPICalled">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">ConsoleAPICalled</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">type_</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">args</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">execution_context_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">timestamp</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">stack_trace</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">context</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/runtime.html#ConsoleAPICalled"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.runtime.ConsoleAPICalled" title="Link to this definition">#</a></dt>
<dd><p>Issued when console API was called.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.ConsoleAPICalled.type_">
<span class="sig-name descname"><span class="pre">type_</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.runtime.ConsoleAPICalled.type_" title="Link to this definition">#</a></dt>
<dd><p>Type of the call.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.ConsoleAPICalled.args">
<span class="sig-name descname"><span class="pre">args</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.runtime.RemoteObject" title="nodriver.cdp.runtime.RemoteObject"><code class="xref py py-class docutils literal notranslate"><span class="pre">RemoteObject</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.runtime.ConsoleAPICalled.args" title="Link to this definition">#</a></dt>
<dd><p>Call arguments.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.ConsoleAPICalled.execution_context_id">
<span class="sig-name descname"><span class="pre">execution_context_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.runtime.ExecutionContextId" title="nodriver.cdp.runtime.ExecutionContextId"><code class="xref py py-class docutils literal notranslate"><span class="pre">ExecutionContextId</span></code></a></em><a class="headerlink" href="#nodriver.cdp.runtime.ConsoleAPICalled.execution_context_id" title="Link to this definition">#</a></dt>
<dd><p>Identifier of the context where the call was made.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.ConsoleAPICalled.timestamp">
<span class="sig-name descname"><span class="pre">timestamp</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.runtime.Timestamp" title="nodriver.cdp.runtime.Timestamp"><code class="xref py py-class docutils literal notranslate"><span class="pre">Timestamp</span></code></a></em><a class="headerlink" href="#nodriver.cdp.runtime.ConsoleAPICalled.timestamp" title="Link to this definition">#</a></dt>
<dd><p>Call timestamp.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.ConsoleAPICalled.stack_trace">
<span class="sig-name descname"><span class="pre">stack_trace</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.runtime.StackTrace" title="nodriver.cdp.runtime.StackTrace"><code class="xref py py-class docutils literal notranslate"><span class="pre">StackTrace</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.runtime.ConsoleAPICalled.stack_trace" title="Link to this definition">#</a></dt>
<dd><p>Stack trace captured when the call was made. The async stack chain is automatically reported for
the following call types: <code class="docutils literal notranslate"><span class="pre">assert</span></code>, <code class="docutils literal notranslate"><span class="pre">error</span></code>, <code class="docutils literal notranslate"><span class="pre">trace</span></code>, <code class="docutils literal notranslate"><span class="pre">warning</span></code>. For other types the async call
chain can be retrieved using <code class="docutils literal notranslate"><span class="pre">Debugger.getStackTrace</span></code> and <code class="docutils literal notranslate"><span class="pre">stackTrace.parentId</span></code> field.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.ConsoleAPICalled.context">
<span class="sig-name descname"><span class="pre">context</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.runtime.ConsoleAPICalled.context" title="Link to this definition">#</a></dt>
<dd><p>Console context descriptor for calls on non-default console context (not console.*):
‘anonymous#unique-logger-id’ for call on unnamed context, ‘name#unique-logger-id’ for call
on named context.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.ExceptionRevoked">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">ExceptionRevoked</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">reason</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">exception_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/runtime.html#ExceptionRevoked"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.runtime.ExceptionRevoked" title="Link to this definition">#</a></dt>
<dd><p>Issued when unhandled exception was revoked.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.ExceptionRevoked.reason">
<span class="sig-name descname"><span class="pre">reason</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.runtime.ExceptionRevoked.reason" title="Link to this definition">#</a></dt>
<dd><p>Reason describing why exception was revoked.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.ExceptionRevoked.exception_id">
<span class="sig-name descname"><span class="pre">exception_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></em><a class="headerlink" href="#nodriver.cdp.runtime.ExceptionRevoked.exception_id" title="Link to this definition">#</a></dt>
<dd><p>The id of revoked exception, as reported in <code class="docutils literal notranslate"><span class="pre">exceptionThrown</span></code>.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.ExceptionThrown">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">ExceptionThrown</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">timestamp</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">exception_details</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/runtime.html#ExceptionThrown"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.runtime.ExceptionThrown" title="Link to this definition">#</a></dt>
<dd><p>Issued when exception was thrown and unhandled.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.ExceptionThrown.timestamp">
<span class="sig-name descname"><span class="pre">timestamp</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.runtime.Timestamp" title="nodriver.cdp.runtime.Timestamp"><code class="xref py py-class docutils literal notranslate"><span class="pre">Timestamp</span></code></a></em><a class="headerlink" href="#nodriver.cdp.runtime.ExceptionThrown.timestamp" title="Link to this definition">#</a></dt>
<dd><p>Timestamp of the exception.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.ExceptionThrown.exception_details">
<span class="sig-name descname"><span class="pre">exception_details</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.runtime.ExceptionDetails" title="nodriver.cdp.runtime.ExceptionDetails"><code class="xref py py-class docutils literal notranslate"><span class="pre">ExceptionDetails</span></code></a></em><a class="headerlink" href="#nodriver.cdp.runtime.ExceptionThrown.exception_details" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.ExecutionContextCreated">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">ExecutionContextCreated</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">context</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/runtime.html#ExecutionContextCreated"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.runtime.ExecutionContextCreated" title="Link to this definition">#</a></dt>
<dd><p>Issued when new execution context is created.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.ExecutionContextCreated.context">
<span class="sig-name descname"><span class="pre">context</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.runtime.ExecutionContextDescription" title="nodriver.cdp.runtime.ExecutionContextDescription"><code class="xref py py-class docutils literal notranslate"><span class="pre">ExecutionContextDescription</span></code></a></em><a class="headerlink" href="#nodriver.cdp.runtime.ExecutionContextCreated.context" title="Link to this definition">#</a></dt>
<dd><p>A newly created execution context.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.ExecutionContextDestroyed">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">ExecutionContextDestroyed</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">execution_context_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">execution_context_unique_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/runtime.html#ExecutionContextDestroyed"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.runtime.ExecutionContextDestroyed" title="Link to this definition">#</a></dt>
<dd><p>Issued when execution context is destroyed.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.ExecutionContextDestroyed.execution_context_id">
<span class="sig-name descname"><span class="pre">execution_context_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.runtime.ExecutionContextId" title="nodriver.cdp.runtime.ExecutionContextId"><code class="xref py py-class docutils literal notranslate"><span class="pre">ExecutionContextId</span></code></a></em><a class="headerlink" href="#nodriver.cdp.runtime.ExecutionContextDestroyed.execution_context_id" title="Link to this definition">#</a></dt>
<dd><p>Id of the destroyed context</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.ExecutionContextDestroyed.execution_context_unique_id">
<span class="sig-name descname"><span class="pre">execution_context_unique_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.runtime.ExecutionContextDestroyed.execution_context_unique_id" title="Link to this definition">#</a></dt>
<dd><p>Unique Id of the destroyed context</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.ExecutionContextsCleared">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">ExecutionContextsCleared</span></span><a class="reference internal" href="../../_modules/nodriver/cdp/runtime.html#ExecutionContextsCleared"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.runtime.ExecutionContextsCleared" title="Link to this definition">#</a></dt>
<dd><p>Issued when all executionContexts were cleared in browser</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.InspectRequested">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">InspectRequested</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object_</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">hints</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">execution_context_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/runtime.html#InspectRequested"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.runtime.InspectRequested" title="Link to this definition">#</a></dt>
<dd><p>Issued when object should be inspected (for example, as a result of inspect() command line API
call).</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.InspectRequested.object_">
<span class="sig-name descname"><span class="pre">object_</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.runtime.RemoteObject" title="nodriver.cdp.runtime.RemoteObject"><code class="xref py py-class docutils literal notranslate"><span class="pre">RemoteObject</span></code></a></em><a class="headerlink" href="#nodriver.cdp.runtime.InspectRequested.object_" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.InspectRequested.hints">
<span class="sig-name descname"><span class="pre">hints</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">dict</span></code></a></em><a class="headerlink" href="#nodriver.cdp.runtime.InspectRequested.hints" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.runtime.InspectRequested.execution_context_id">
<span class="sig-name descname"><span class="pre">execution_context_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.runtime.ExecutionContextId" title="nodriver.cdp.runtime.ExecutionContextId"><code class="xref py py-class docutils literal notranslate"><span class="pre">ExecutionContextId</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.runtime.InspectRequested.execution_context_id" title="Link to this definition">#</a></dt>
<dd><p>Identifier of the context where the call was made.</p>
</dd></dl>

</dd></dl>

</section>
</section>

        </article>
      </div>
      <footer>
        
        <div class="related-pages">
          <a class="next-page" href="schema.html">
              <div class="page-info">
                <div class="context">
                  <span>Next</span>
                </div>
                <div class="title">Schema</div>
              </div>
              <svg class="furo-related-icon"><use href="#svg-arrow-right"></use></svg>
            </a>
          <a class="prev-page" href="pwa.html">
              <svg class="furo-related-icon"><use href="#svg-arrow-right"></use></svg>
              <div class="page-info">
                <div class="context">
                  <span>Previous</span>
                </div>
                
                <div class="title">PWA</div>
                
              </div>
            </a>
        </div>
        <div class="bottom-of-page">
          <div class="left-details">
            <div class="copyright">
                Copyright &#169; 2023, Author
            </div>
            Made with <a href="https://www.sphinx-doc.org/">Sphinx</a> and <a class="muted-link" href="https://pradyunsg.me">@pradyunsg</a>'s
            
            <a href="https://github.com/pradyunsg/furo">Furo</a>
            
          </div>
          <div class="right-details">
            
          </div>
        </div>
        
      </footer>
    </div>
    <aside class="toc-drawer">
      
      
      <div class="toc-sticky toc-scroll">
        <div class="toc-title-container">
          <span class="toc-title">
            On this page
          </span>
        </div>
        <div class="toc-tree-container">
          <div class="toc-tree">
            <ul>
<li><a class="reference internal" href="#">Runtime</a><ul>
<li><a class="reference internal" href="#types">Types</a><ul>
<li><a class="reference internal" href="#nodriver.cdp.runtime.ScriptId"><code class="docutils literal notranslate"><span class="pre">ScriptId</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.SerializationOptions"><code class="docutils literal notranslate"><span class="pre">SerializationOptions</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.runtime.SerializationOptions.serialization"><code class="docutils literal notranslate"><span class="pre">SerializationOptions.serialization</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.SerializationOptions.max_depth"><code class="docutils literal notranslate"><span class="pre">SerializationOptions.max_depth</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.SerializationOptions.additional_parameters"><code class="docutils literal notranslate"><span class="pre">SerializationOptions.additional_parameters</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.DeepSerializedValue"><code class="docutils literal notranslate"><span class="pre">DeepSerializedValue</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.runtime.DeepSerializedValue.type_"><code class="docutils literal notranslate"><span class="pre">DeepSerializedValue.type_</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.DeepSerializedValue.value"><code class="docutils literal notranslate"><span class="pre">DeepSerializedValue.value</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.DeepSerializedValue.object_id"><code class="docutils literal notranslate"><span class="pre">DeepSerializedValue.object_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.DeepSerializedValue.weak_local_object_reference"><code class="docutils literal notranslate"><span class="pre">DeepSerializedValue.weak_local_object_reference</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.RemoteObjectId"><code class="docutils literal notranslate"><span class="pre">RemoteObjectId</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.UnserializableValue"><code class="docutils literal notranslate"><span class="pre">UnserializableValue</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.RemoteObject"><code class="docutils literal notranslate"><span class="pre">RemoteObject</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.runtime.RemoteObject.type_"><code class="docutils literal notranslate"><span class="pre">RemoteObject.type_</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.RemoteObject.subtype"><code class="docutils literal notranslate"><span class="pre">RemoteObject.subtype</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.RemoteObject.class_name"><code class="docutils literal notranslate"><span class="pre">RemoteObject.class_name</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.RemoteObject.value"><code class="docutils literal notranslate"><span class="pre">RemoteObject.value</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.RemoteObject.unserializable_value"><code class="docutils literal notranslate"><span class="pre">RemoteObject.unserializable_value</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.RemoteObject.description"><code class="docutils literal notranslate"><span class="pre">RemoteObject.description</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.RemoteObject.deep_serialized_value"><code class="docutils literal notranslate"><span class="pre">RemoteObject.deep_serialized_value</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.RemoteObject.object_id"><code class="docutils literal notranslate"><span class="pre">RemoteObject.object_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.RemoteObject.preview"><code class="docutils literal notranslate"><span class="pre">RemoteObject.preview</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.RemoteObject.custom_preview"><code class="docutils literal notranslate"><span class="pre">RemoteObject.custom_preview</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.CustomPreview"><code class="docutils literal notranslate"><span class="pre">CustomPreview</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.runtime.CustomPreview.header"><code class="docutils literal notranslate"><span class="pre">CustomPreview.header</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.CustomPreview.body_getter_id"><code class="docutils literal notranslate"><span class="pre">CustomPreview.body_getter_id</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.ObjectPreview"><code class="docutils literal notranslate"><span class="pre">ObjectPreview</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.runtime.ObjectPreview.type_"><code class="docutils literal notranslate"><span class="pre">ObjectPreview.type_</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.ObjectPreview.overflow"><code class="docutils literal notranslate"><span class="pre">ObjectPreview.overflow</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.ObjectPreview.properties"><code class="docutils literal notranslate"><span class="pre">ObjectPreview.properties</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.ObjectPreview.subtype"><code class="docutils literal notranslate"><span class="pre">ObjectPreview.subtype</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.ObjectPreview.description"><code class="docutils literal notranslate"><span class="pre">ObjectPreview.description</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.ObjectPreview.entries"><code class="docutils literal notranslate"><span class="pre">ObjectPreview.entries</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.PropertyPreview"><code class="docutils literal notranslate"><span class="pre">PropertyPreview</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.runtime.PropertyPreview.name"><code class="docutils literal notranslate"><span class="pre">PropertyPreview.name</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.PropertyPreview.type_"><code class="docutils literal notranslate"><span class="pre">PropertyPreview.type_</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.PropertyPreview.value"><code class="docutils literal notranslate"><span class="pre">PropertyPreview.value</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.PropertyPreview.value_preview"><code class="docutils literal notranslate"><span class="pre">PropertyPreview.value_preview</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.PropertyPreview.subtype"><code class="docutils literal notranslate"><span class="pre">PropertyPreview.subtype</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.EntryPreview"><code class="docutils literal notranslate"><span class="pre">EntryPreview</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.runtime.EntryPreview.value"><code class="docutils literal notranslate"><span class="pre">EntryPreview.value</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.EntryPreview.key"><code class="docutils literal notranslate"><span class="pre">EntryPreview.key</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.PropertyDescriptor"><code class="docutils literal notranslate"><span class="pre">PropertyDescriptor</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.runtime.PropertyDescriptor.name"><code class="docutils literal notranslate"><span class="pre">PropertyDescriptor.name</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.PropertyDescriptor.configurable"><code class="docutils literal notranslate"><span class="pre">PropertyDescriptor.configurable</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.PropertyDescriptor.enumerable"><code class="docutils literal notranslate"><span class="pre">PropertyDescriptor.enumerable</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.PropertyDescriptor.value"><code class="docutils literal notranslate"><span class="pre">PropertyDescriptor.value</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.PropertyDescriptor.writable"><code class="docutils literal notranslate"><span class="pre">PropertyDescriptor.writable</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.PropertyDescriptor.get"><code class="docutils literal notranslate"><span class="pre">PropertyDescriptor.get</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.PropertyDescriptor.set_"><code class="docutils literal notranslate"><span class="pre">PropertyDescriptor.set_</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.PropertyDescriptor.was_thrown"><code class="docutils literal notranslate"><span class="pre">PropertyDescriptor.was_thrown</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.PropertyDescriptor.is_own"><code class="docutils literal notranslate"><span class="pre">PropertyDescriptor.is_own</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.PropertyDescriptor.symbol"><code class="docutils literal notranslate"><span class="pre">PropertyDescriptor.symbol</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.InternalPropertyDescriptor"><code class="docutils literal notranslate"><span class="pre">InternalPropertyDescriptor</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.runtime.InternalPropertyDescriptor.name"><code class="docutils literal notranslate"><span class="pre">InternalPropertyDescriptor.name</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.InternalPropertyDescriptor.value"><code class="docutils literal notranslate"><span class="pre">InternalPropertyDescriptor.value</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.PrivatePropertyDescriptor"><code class="docutils literal notranslate"><span class="pre">PrivatePropertyDescriptor</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.runtime.PrivatePropertyDescriptor.name"><code class="docutils literal notranslate"><span class="pre">PrivatePropertyDescriptor.name</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.PrivatePropertyDescriptor.value"><code class="docutils literal notranslate"><span class="pre">PrivatePropertyDescriptor.value</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.PrivatePropertyDescriptor.get"><code class="docutils literal notranslate"><span class="pre">PrivatePropertyDescriptor.get</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.PrivatePropertyDescriptor.set_"><code class="docutils literal notranslate"><span class="pre">PrivatePropertyDescriptor.set_</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.CallArgument"><code class="docutils literal notranslate"><span class="pre">CallArgument</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.runtime.CallArgument.value"><code class="docutils literal notranslate"><span class="pre">CallArgument.value</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.CallArgument.unserializable_value"><code class="docutils literal notranslate"><span class="pre">CallArgument.unserializable_value</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.CallArgument.object_id"><code class="docutils literal notranslate"><span class="pre">CallArgument.object_id</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.ExecutionContextId"><code class="docutils literal notranslate"><span class="pre">ExecutionContextId</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.ExecutionContextDescription"><code class="docutils literal notranslate"><span class="pre">ExecutionContextDescription</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.runtime.ExecutionContextDescription.id_"><code class="docutils literal notranslate"><span class="pre">ExecutionContextDescription.id_</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.ExecutionContextDescription.origin"><code class="docutils literal notranslate"><span class="pre">ExecutionContextDescription.origin</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.ExecutionContextDescription.name"><code class="docutils literal notranslate"><span class="pre">ExecutionContextDescription.name</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.ExecutionContextDescription.unique_id"><code class="docutils literal notranslate"><span class="pre">ExecutionContextDescription.unique_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.ExecutionContextDescription.aux_data"><code class="docutils literal notranslate"><span class="pre">ExecutionContextDescription.aux_data</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.ExceptionDetails"><code class="docutils literal notranslate"><span class="pre">ExceptionDetails</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.runtime.ExceptionDetails.exception_id"><code class="docutils literal notranslate"><span class="pre">ExceptionDetails.exception_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.ExceptionDetails.text"><code class="docutils literal notranslate"><span class="pre">ExceptionDetails.text</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.ExceptionDetails.line_number"><code class="docutils literal notranslate"><span class="pre">ExceptionDetails.line_number</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.ExceptionDetails.column_number"><code class="docutils literal notranslate"><span class="pre">ExceptionDetails.column_number</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.ExceptionDetails.script_id"><code class="docutils literal notranslate"><span class="pre">ExceptionDetails.script_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.ExceptionDetails.url"><code class="docutils literal notranslate"><span class="pre">ExceptionDetails.url</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.ExceptionDetails.stack_trace"><code class="docutils literal notranslate"><span class="pre">ExceptionDetails.stack_trace</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.ExceptionDetails.exception"><code class="docutils literal notranslate"><span class="pre">ExceptionDetails.exception</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.ExceptionDetails.execution_context_id"><code class="docutils literal notranslate"><span class="pre">ExceptionDetails.execution_context_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.ExceptionDetails.exception_meta_data"><code class="docutils literal notranslate"><span class="pre">ExceptionDetails.exception_meta_data</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.Timestamp"><code class="docutils literal notranslate"><span class="pre">Timestamp</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.TimeDelta"><code class="docutils literal notranslate"><span class="pre">TimeDelta</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.CallFrame"><code class="docutils literal notranslate"><span class="pre">CallFrame</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.runtime.CallFrame.function_name"><code class="docutils literal notranslate"><span class="pre">CallFrame.function_name</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.CallFrame.script_id"><code class="docutils literal notranslate"><span class="pre">CallFrame.script_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.CallFrame.url"><code class="docutils literal notranslate"><span class="pre">CallFrame.url</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.CallFrame.line_number"><code class="docutils literal notranslate"><span class="pre">CallFrame.line_number</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.CallFrame.column_number"><code class="docutils literal notranslate"><span class="pre">CallFrame.column_number</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.StackTrace"><code class="docutils literal notranslate"><span class="pre">StackTrace</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.runtime.StackTrace.call_frames"><code class="docutils literal notranslate"><span class="pre">StackTrace.call_frames</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.StackTrace.description"><code class="docutils literal notranslate"><span class="pre">StackTrace.description</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.StackTrace.parent"><code class="docutils literal notranslate"><span class="pre">StackTrace.parent</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.StackTrace.parent_id"><code class="docutils literal notranslate"><span class="pre">StackTrace.parent_id</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.UniqueDebuggerId"><code class="docutils literal notranslate"><span class="pre">UniqueDebuggerId</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.StackTraceId"><code class="docutils literal notranslate"><span class="pre">StackTraceId</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.runtime.StackTraceId.id_"><code class="docutils literal notranslate"><span class="pre">StackTraceId.id_</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.StackTraceId.debugger_id"><code class="docutils literal notranslate"><span class="pre">StackTraceId.debugger_id</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#commands">Commands</a><ul>
<li><a class="reference internal" href="#nodriver.cdp.runtime.add_binding"><code class="docutils literal notranslate"><span class="pre">add_binding()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.await_promise"><code class="docutils literal notranslate"><span class="pre">await_promise()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.call_function_on"><code class="docutils literal notranslate"><span class="pre">call_function_on()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.compile_script"><code class="docutils literal notranslate"><span class="pre">compile_script()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.disable"><code class="docutils literal notranslate"><span class="pre">disable()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.discard_console_entries"><code class="docutils literal notranslate"><span class="pre">discard_console_entries()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.enable"><code class="docutils literal notranslate"><span class="pre">enable()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.evaluate"><code class="docutils literal notranslate"><span class="pre">evaluate()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.get_exception_details"><code class="docutils literal notranslate"><span class="pre">get_exception_details()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.get_heap_usage"><code class="docutils literal notranslate"><span class="pre">get_heap_usage()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.get_isolate_id"><code class="docutils literal notranslate"><span class="pre">get_isolate_id()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.get_properties"><code class="docutils literal notranslate"><span class="pre">get_properties()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.global_lexical_scope_names"><code class="docutils literal notranslate"><span class="pre">global_lexical_scope_names()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.query_objects"><code class="docutils literal notranslate"><span class="pre">query_objects()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.release_object"><code class="docutils literal notranslate"><span class="pre">release_object()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.release_object_group"><code class="docutils literal notranslate"><span class="pre">release_object_group()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.remove_binding"><code class="docutils literal notranslate"><span class="pre">remove_binding()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.run_if_waiting_for_debugger"><code class="docutils literal notranslate"><span class="pre">run_if_waiting_for_debugger()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.run_script"><code class="docutils literal notranslate"><span class="pre">run_script()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.set_async_call_stack_depth"><code class="docutils literal notranslate"><span class="pre">set_async_call_stack_depth()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.set_custom_object_formatter_enabled"><code class="docutils literal notranslate"><span class="pre">set_custom_object_formatter_enabled()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.set_max_call_stack_size_to_capture"><code class="docutils literal notranslate"><span class="pre">set_max_call_stack_size_to_capture()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.terminate_execution"><code class="docutils literal notranslate"><span class="pre">terminate_execution()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#events">Events</a><ul>
<li><a class="reference internal" href="#nodriver.cdp.runtime.BindingCalled"><code class="docutils literal notranslate"><span class="pre">BindingCalled</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.runtime.BindingCalled.name"><code class="docutils literal notranslate"><span class="pre">BindingCalled.name</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.BindingCalled.payload"><code class="docutils literal notranslate"><span class="pre">BindingCalled.payload</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.BindingCalled.execution_context_id"><code class="docutils literal notranslate"><span class="pre">BindingCalled.execution_context_id</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.ConsoleAPICalled"><code class="docutils literal notranslate"><span class="pre">ConsoleAPICalled</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.runtime.ConsoleAPICalled.type_"><code class="docutils literal notranslate"><span class="pre">ConsoleAPICalled.type_</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.ConsoleAPICalled.args"><code class="docutils literal notranslate"><span class="pre">ConsoleAPICalled.args</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.ConsoleAPICalled.execution_context_id"><code class="docutils literal notranslate"><span class="pre">ConsoleAPICalled.execution_context_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.ConsoleAPICalled.timestamp"><code class="docutils literal notranslate"><span class="pre">ConsoleAPICalled.timestamp</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.ConsoleAPICalled.stack_trace"><code class="docutils literal notranslate"><span class="pre">ConsoleAPICalled.stack_trace</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.ConsoleAPICalled.context"><code class="docutils literal notranslate"><span class="pre">ConsoleAPICalled.context</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.ExceptionRevoked"><code class="docutils literal notranslate"><span class="pre">ExceptionRevoked</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.runtime.ExceptionRevoked.reason"><code class="docutils literal notranslate"><span class="pre">ExceptionRevoked.reason</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.ExceptionRevoked.exception_id"><code class="docutils literal notranslate"><span class="pre">ExceptionRevoked.exception_id</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.ExceptionThrown"><code class="docutils literal notranslate"><span class="pre">ExceptionThrown</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.runtime.ExceptionThrown.timestamp"><code class="docutils literal notranslate"><span class="pre">ExceptionThrown.timestamp</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.ExceptionThrown.exception_details"><code class="docutils literal notranslate"><span class="pre">ExceptionThrown.exception_details</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.ExecutionContextCreated"><code class="docutils literal notranslate"><span class="pre">ExecutionContextCreated</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.runtime.ExecutionContextCreated.context"><code class="docutils literal notranslate"><span class="pre">ExecutionContextCreated.context</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.ExecutionContextDestroyed"><code class="docutils literal notranslate"><span class="pre">ExecutionContextDestroyed</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.runtime.ExecutionContextDestroyed.execution_context_id"><code class="docutils literal notranslate"><span class="pre">ExecutionContextDestroyed.execution_context_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.ExecutionContextDestroyed.execution_context_unique_id"><code class="docutils literal notranslate"><span class="pre">ExecutionContextDestroyed.execution_context_unique_id</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.ExecutionContextsCleared"><code class="docutils literal notranslate"><span class="pre">ExecutionContextsCleared</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.InspectRequested"><code class="docutils literal notranslate"><span class="pre">InspectRequested</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.runtime.InspectRequested.object_"><code class="docutils literal notranslate"><span class="pre">InspectRequested.object_</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.InspectRequested.hints"><code class="docutils literal notranslate"><span class="pre">InspectRequested.hints</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.runtime.InspectRequested.execution_context_id"><code class="docutils literal notranslate"><span class="pre">InspectRequested.execution_context_id</span></code></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>

          </div>
        </div>
      </div>
      
      
    </aside>
  </div>
</div><script src="../../_static/documentation_options.js?v=5929fcd5"></script>
    <script src="../../_static/doctools.js?v=888ff710"></script>
    <script src="../../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../../_static/scripts/furo.js?v=32e29ea5"></script>
    </body>
</html>