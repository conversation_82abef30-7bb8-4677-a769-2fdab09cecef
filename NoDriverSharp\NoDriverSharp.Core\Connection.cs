using System.Collections.Concurrent;
using System.Net.WebSockets;
using System.Text;
using System.Text.Json;
using Microsoft.Extensions.Logging;
using NoDriverSharp.CDP.Common;

namespace NoDriverSharp.Core;

/// <summary>
/// Manages WebSocket connection to Chrome DevTools Protocol
/// </summary>
public class Connection : IDisposable
{
    private readonly ILogger _logger;
    private readonly string _websocketUrl;
    private ClientWebSocket? _webSocket;
    private CancellationTokenSource? _cancellationTokenSource;
    private Task? _messageListenerTask;
    private readonly ConcurrentDictionary<int, TaskCompletionSource<CDPResponse>> _pendingCommands = new();
    private readonly ConcurrentDictionary<string, List<Func<JsonElement, Task>>> _eventHandlers = new();
    private int _commandIdCounter = 0;
    private bool _disposed = false;

    /// <summary>
    /// Event fired when the connection is closed
    /// </summary>
    public event EventHandler<string>? ConnectionClosed;

    /// <summary>
    /// Whether the connection is currently open
    /// </summary>
    public bool IsConnected => _webSocket?.State == WebSocketState.Open;

    /// <summary>
    /// The WebSocket URL for this connection
    /// </summary>
    public string WebSocketUrl => _websocketUrl;

    public Connection(string websocketUrl, ILogger? logger = null)
    {
        _websocketUrl = websocketUrl ?? throw new ArgumentNullException(nameof(websocketUrl));
        _logger = logger ?? Microsoft.Extensions.Logging.Abstractions.NullLogger.Instance;
    }

    /// <summary>
    /// Connects to the WebSocket endpoint
    /// </summary>
    public async Task ConnectAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(Connection));

        if (IsConnected)
            return;

        try
        {
            _webSocket?.Dispose();
            _webSocket = new ClientWebSocket();
            _cancellationTokenSource = new CancellationTokenSource();

            _logger.LogDebug("Connecting to WebSocket: {Url}", _websocketUrl);
            await _webSocket.ConnectAsync(new Uri(_websocketUrl), cancellationToken);
            _logger.LogDebug("WebSocket connected successfully");

            // Start message listener
            _messageListenerTask = Task.Run(MessageListenerAsync, _cancellationTokenSource.Token);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to connect to WebSocket: {Url}", _websocketUrl);
            throw new ConnectionException($"Failed to connect to WebSocket: {_websocketUrl}", ex);
        }
    }

    /// <summary>
    /// Sends a CDP command and waits for the response
    /// </summary>
    public async Task<TResult> SendCommandAsync<TResult>(CDPCommand command, CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(Connection));

        if (!IsConnected)
            throw new ConnectionException("WebSocket is not connected");

        var commandId = Interlocked.Increment(ref _commandIdCounter);
        command.Id = commandId;

        var tcs = new TaskCompletionSource<CDPResponse>();
        _pendingCommands[commandId] = tcs;

        try
        {
            var json = command.ToJson();
            _logger.LogTrace("Sending command: {Json}", json);

            var bytes = Encoding.UTF8.GetBytes(json);
            await _webSocket.SendAsync(new ArraySegment<byte>(bytes), WebSocketMessageType.Text, true, cancellationToken);

            // Wait for response with timeout
            using var timeoutCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
            timeoutCts.CancelAfter(TimeSpan.FromSeconds(30));

            var response = await tcs.Task.WaitAsync(timeoutCts.Token);

            if (!response.IsSuccess)
            {
                throw new ProtocolException(
                    response.Error!.Code,
                    response.Error.Message,
                    response.Error.Data);
            }

            if (response.Result.HasValue)
            {
                return JsonSerializer.Deserialize<TResult>(response.Result.Value.GetRawText())!;
            }

            return default(TResult)!;
        }
        catch (OperationCanceledException) when (cancellationToken.IsCancellationRequested)
        {
            throw;
        }
        catch (OperationCanceledException)
        {
            throw new TimeoutException(TimeSpan.FromSeconds(30), $"Command {command.Method} timed out");
        }
        finally
        {
            _pendingCommands.TryRemove(commandId, out _);
        }
    }

    /// <summary>
    /// Sends a CDP command without waiting for a specific result
    /// </summary>
    public async Task SendCommandAsync(CDPCommand command, CancellationToken cancellationToken = default)
    {
        await SendCommandAsync<object>(command, cancellationToken);
    }

    /// <summary>
    /// Adds an event handler for a specific CDP event
    /// </summary>
    public void AddEventHandler(string eventName, Func<JsonElement, Task> handler)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(Connection));

        _eventHandlers.AddOrUpdate(eventName,
            new List<Func<JsonElement, Task>> { handler },
            (key, existing) =>
            {
                existing.Add(handler);
                return existing;
            });
    }

    /// <summary>
    /// Removes an event handler for a specific CDP event
    /// </summary>
    public void RemoveEventHandler(string eventName, Func<JsonElement, Task> handler)
    {
        if (_disposed)
            return;

        if (_eventHandlers.TryGetValue(eventName, out var handlers))
        {
            handlers.Remove(handler);
            if (handlers.Count == 0)
            {
                _eventHandlers.TryRemove(eventName, out _);
            }
        }
    }

    /// <summary>
    /// Closes the connection
    /// </summary>
    public async Task CloseAsync()
    {
        if (_disposed || !IsConnected)
            return;

        try
        {
            _cancellationTokenSource?.Cancel();
            
            if (_webSocket?.State == WebSocketState.Open)
            {
                await _webSocket.CloseAsync(WebSocketCloseStatus.NormalClosure, "Closing", CancellationToken.None);
            }

            if (_messageListenerTask != null)
            {
                await _messageListenerTask;
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error while closing WebSocket connection");
        }
    }

    /// <summary>
    /// Message listener task that processes incoming WebSocket messages
    /// </summary>
    private async Task MessageListenerAsync()
    {
        var buffer = new byte[8192];
        var messageBuffer = new List<byte>();

        try
        {
            while (!_cancellationTokenSource!.Token.IsCancellationRequested && IsConnected)
            {
                var result = await _webSocket!.ReceiveAsync(new ArraySegment<byte>(buffer), _cancellationTokenSource.Token);

                if (result.MessageType == WebSocketMessageType.Close)
                {
                    _logger.LogDebug("WebSocket closed by remote endpoint");
                    break;
                }

                messageBuffer.AddRange(buffer.Take(result.Count));

                if (result.EndOfMessage)
                {
                    var json = Encoding.UTF8.GetString(messageBuffer.ToArray());
                    messageBuffer.Clear();

                    _logger.LogTrace("Received message: {Json}", json);
                    await ProcessMessageAsync(json);
                }
            }
        }
        catch (OperationCanceledException) when (_cancellationTokenSource.Token.IsCancellationRequested)
        {
            _logger.LogDebug("Message listener cancelled");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in message listener");
        }
        finally
        {
            ConnectionClosed?.Invoke(this, "Message listener ended");
        }
    }

    /// <summary>
    /// Processes an incoming CDP message
    /// </summary>
    private async Task ProcessMessageAsync(string json)
    {
        try
        {
            using var document = JsonDocument.Parse(json);
            var root = document.RootElement;

            // Check if this is a command response
            if (root.TryGetProperty("id", out var idElement) && idElement.TryGetInt32(out var id))
            {
                if (_pendingCommands.TryRemove(id, out var tcs))
                {
                    var response = JsonSerializer.Deserialize<CDPResponse>(json)!;
                    tcs.SetResult(response);
                }
                return;
            }

            // Check if this is an event
            if (root.TryGetProperty("method", out var methodElement))
            {
                var method = methodElement.GetString()!;
                if (_eventHandlers.TryGetValue(method, out var handlers))
                {
                    var paramsElement = root.TryGetProperty("params", out var p) ? p : default;
                    
                    // Fire all handlers for this event
                    var tasks = handlers.Select(handler => 
                    {
                        try
                        {
                            return handler(paramsElement);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "Error in event handler for {Method}", method);
                            return Task.CompletedTask;
                        }
                    });

                    await Task.WhenAll(tasks);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing message: {Json}", json);
        }
    }

    public void Dispose()
    {
        if (_disposed)
            return;

        _disposed = true;

        try
        {
            _cancellationTokenSource?.Cancel();
            _webSocket?.Dispose();
            _cancellationTokenSource?.Dispose();
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error during disposal");
        }

        // Complete any pending commands with cancellation
        foreach (var tcs in _pendingCommands.Values)
        {
            tcs.TrySetCanceled();
        }
        _pendingCommands.Clear();
        _eventHandlers.Clear();
    }
}
