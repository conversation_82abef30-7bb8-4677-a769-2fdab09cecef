# DO NOT EDIT THIS FILE!
#
# This file is generated from the CDP specification. If you need to make
# changes, edit the generator and regenerate all of the modules.
#
# CDP domain: Overlay (experimental)

from __future__ import annotations

import enum
import typing
from dataclasses import dataclass

from deprecated.sphinx import deprecated  # type: ignore

from . import dom, page, runtime
from .util import T_JSON_DICT, event_class


@dataclass
class SourceOrderConfig:
    """
    Configuration data for drawing the source order of an elements children.
    """

    #: the color to outline the given element in.
    parent_outline_color: dom.RGBA

    #: the color to outline the child elements in.
    child_outline_color: dom.RGBA

    def to_json(self) -> T_JSON_DICT:
        json: T_JSON_DICT = dict()
        json["parentOutlineColor"] = self.parent_outline_color.to_json()
        json["childOutlineColor"] = self.child_outline_color.to_json()
        return json

    @classmethod
    def from_json(cls, json: T_JSON_DICT) -> SourceOrderConfig:
        return cls(
            parent_outline_color=dom.RGBA.from_json(json["parentOutlineColor"]),
            child_outline_color=dom.RGBA.from_json(json["childOutlineColor"]),
        )


@dataclass
class GridHighlightConfig:
    """
    Configuration data for the highlighting of Grid elements.
    """

    #: Whether the extension lines from grid cells to the rulers should be shown (default: false).
    show_grid_extension_lines: typing.Optional[bool] = None

    #: Show Positive line number labels (default: false).
    show_positive_line_numbers: typing.Optional[bool] = None

    #: Show Negative line number labels (default: false).
    show_negative_line_numbers: typing.Optional[bool] = None

    #: Show area name labels (default: false).
    show_area_names: typing.Optional[bool] = None

    #: Show line name labels (default: false).
    show_line_names: typing.Optional[bool] = None

    #: Show track size labels (default: false).
    show_track_sizes: typing.Optional[bool] = None

    #: The grid container border highlight color (default: transparent).
    grid_border_color: typing.Optional[dom.RGBA] = None

    #: The cell border color (default: transparent). Deprecated, please use rowLineColor and columnLineColor instead.
    cell_border_color: typing.Optional[dom.RGBA] = None

    #: The row line color (default: transparent).
    row_line_color: typing.Optional[dom.RGBA] = None

    #: The column line color (default: transparent).
    column_line_color: typing.Optional[dom.RGBA] = None

    #: Whether the grid border is dashed (default: false).
    grid_border_dash: typing.Optional[bool] = None

    #: Whether the cell border is dashed (default: false). Deprecated, please us rowLineDash and columnLineDash instead.
    cell_border_dash: typing.Optional[bool] = None

    #: Whether row lines are dashed (default: false).
    row_line_dash: typing.Optional[bool] = None

    #: Whether column lines are dashed (default: false).
    column_line_dash: typing.Optional[bool] = None

    #: The row gap highlight fill color (default: transparent).
    row_gap_color: typing.Optional[dom.RGBA] = None

    #: The row gap hatching fill color (default: transparent).
    row_hatch_color: typing.Optional[dom.RGBA] = None

    #: The column gap highlight fill color (default: transparent).
    column_gap_color: typing.Optional[dom.RGBA] = None

    #: The column gap hatching fill color (default: transparent).
    column_hatch_color: typing.Optional[dom.RGBA] = None

    #: The named grid areas border color (Default: transparent).
    area_border_color: typing.Optional[dom.RGBA] = None

    #: The grid container background color (Default: transparent).
    grid_background_color: typing.Optional[dom.RGBA] = None

    def to_json(self) -> T_JSON_DICT:
        json: T_JSON_DICT = dict()
        if self.show_grid_extension_lines is not None:
            json["showGridExtensionLines"] = self.show_grid_extension_lines
        if self.show_positive_line_numbers is not None:
            json["showPositiveLineNumbers"] = self.show_positive_line_numbers
        if self.show_negative_line_numbers is not None:
            json["showNegativeLineNumbers"] = self.show_negative_line_numbers
        if self.show_area_names is not None:
            json["showAreaNames"] = self.show_area_names
        if self.show_line_names is not None:
            json["showLineNames"] = self.show_line_names
        if self.show_track_sizes is not None:
            json["showTrackSizes"] = self.show_track_sizes
        if self.grid_border_color is not None:
            json["gridBorderColor"] = self.grid_border_color.to_json()
        if self.cell_border_color is not None:
            json["cellBorderColor"] = self.cell_border_color.to_json()
        if self.row_line_color is not None:
            json["rowLineColor"] = self.row_line_color.to_json()
        if self.column_line_color is not None:
            json["columnLineColor"] = self.column_line_color.to_json()
        if self.grid_border_dash is not None:
            json["gridBorderDash"] = self.grid_border_dash
        if self.cell_border_dash is not None:
            json["cellBorderDash"] = self.cell_border_dash
        if self.row_line_dash is not None:
            json["rowLineDash"] = self.row_line_dash
        if self.column_line_dash is not None:
            json["columnLineDash"] = self.column_line_dash
        if self.row_gap_color is not None:
            json["rowGapColor"] = self.row_gap_color.to_json()
        if self.row_hatch_color is not None:
            json["rowHatchColor"] = self.row_hatch_color.to_json()
        if self.column_gap_color is not None:
            json["columnGapColor"] = self.column_gap_color.to_json()
        if self.column_hatch_color is not None:
            json["columnHatchColor"] = self.column_hatch_color.to_json()
        if self.area_border_color is not None:
            json["areaBorderColor"] = self.area_border_color.to_json()
        if self.grid_background_color is not None:
            json["gridBackgroundColor"] = self.grid_background_color.to_json()
        return json

    @classmethod
    def from_json(cls, json: T_JSON_DICT) -> GridHighlightConfig:
        return cls(
            show_grid_extension_lines=(
                bool(json["showGridExtensionLines"])
                if json.get("showGridExtensionLines", None) is not None
                else None
            ),
            show_positive_line_numbers=(
                bool(json["showPositiveLineNumbers"])
                if json.get("showPositiveLineNumbers", None) is not None
                else None
            ),
            show_negative_line_numbers=(
                bool(json["showNegativeLineNumbers"])
                if json.get("showNegativeLineNumbers", None) is not None
                else None
            ),
            show_area_names=(
                bool(json["showAreaNames"])
                if json.get("showAreaNames", None) is not None
                else None
            ),
            show_line_names=(
                bool(json["showLineNames"])
                if json.get("showLineNames", None) is not None
                else None
            ),
            show_track_sizes=(
                bool(json["showTrackSizes"])
                if json.get("showTrackSizes", None) is not None
                else None
            ),
            grid_border_color=(
                dom.RGBA.from_json(json["gridBorderColor"])
                if json.get("gridBorderColor", None) is not None
                else None
            ),
            cell_border_color=(
                dom.RGBA.from_json(json["cellBorderColor"])
                if json.get("cellBorderColor", None) is not None
                else None
            ),
            row_line_color=(
                dom.RGBA.from_json(json["rowLineColor"])
                if json.get("rowLineColor", None) is not None
                else None
            ),
            column_line_color=(
                dom.RGBA.from_json(json["columnLineColor"])
                if json.get("columnLineColor", None) is not None
                else None
            ),
            grid_border_dash=(
                bool(json["gridBorderDash"])
                if json.get("gridBorderDash", None) is not None
                else None
            ),
            cell_border_dash=(
                bool(json["cellBorderDash"])
                if json.get("cellBorderDash", None) is not None
                else None
            ),
            row_line_dash=(
                bool(json["rowLineDash"])
                if json.get("rowLineDash", None) is not None
                else None
            ),
            column_line_dash=(
                bool(json["columnLineDash"])
                if json.get("columnLineDash", None) is not None
                else None
            ),
            row_gap_color=(
                dom.RGBA.from_json(json["rowGapColor"])
                if json.get("rowGapColor", None) is not None
                else None
            ),
            row_hatch_color=(
                dom.RGBA.from_json(json["rowHatchColor"])
                if json.get("rowHatchColor", None) is not None
                else None
            ),
            column_gap_color=(
                dom.RGBA.from_json(json["columnGapColor"])
                if json.get("columnGapColor", None) is not None
                else None
            ),
            column_hatch_color=(
                dom.RGBA.from_json(json["columnHatchColor"])
                if json.get("columnHatchColor", None) is not None
                else None
            ),
            area_border_color=(
                dom.RGBA.from_json(json["areaBorderColor"])
                if json.get("areaBorderColor", None) is not None
                else None
            ),
            grid_background_color=(
                dom.RGBA.from_json(json["gridBackgroundColor"])
                if json.get("gridBackgroundColor", None) is not None
                else None
            ),
        )


@dataclass
class FlexContainerHighlightConfig:
    """
    Configuration data for the highlighting of Flex container elements.
    """

    #: The style of the container border
    container_border: typing.Optional[LineStyle] = None

    #: The style of the separator between lines
    line_separator: typing.Optional[LineStyle] = None

    #: The style of the separator between items
    item_separator: typing.Optional[LineStyle] = None

    #: Style of content-distribution space on the main axis (justify-content).
    main_distributed_space: typing.Optional[BoxStyle] = None

    #: Style of content-distribution space on the cross axis (align-content).
    cross_distributed_space: typing.Optional[BoxStyle] = None

    #: Style of empty space caused by row gaps (gap/row-gap).
    row_gap_space: typing.Optional[BoxStyle] = None

    #: Style of empty space caused by columns gaps (gap/column-gap).
    column_gap_space: typing.Optional[BoxStyle] = None

    #: Style of the self-alignment line (align-items).
    cross_alignment: typing.Optional[LineStyle] = None

    def to_json(self) -> T_JSON_DICT:
        json: T_JSON_DICT = dict()
        if self.container_border is not None:
            json["containerBorder"] = self.container_border.to_json()
        if self.line_separator is not None:
            json["lineSeparator"] = self.line_separator.to_json()
        if self.item_separator is not None:
            json["itemSeparator"] = self.item_separator.to_json()
        if self.main_distributed_space is not None:
            json["mainDistributedSpace"] = self.main_distributed_space.to_json()
        if self.cross_distributed_space is not None:
            json["crossDistributedSpace"] = self.cross_distributed_space.to_json()
        if self.row_gap_space is not None:
            json["rowGapSpace"] = self.row_gap_space.to_json()
        if self.column_gap_space is not None:
            json["columnGapSpace"] = self.column_gap_space.to_json()
        if self.cross_alignment is not None:
            json["crossAlignment"] = self.cross_alignment.to_json()
        return json

    @classmethod
    def from_json(cls, json: T_JSON_DICT) -> FlexContainerHighlightConfig:
        return cls(
            container_border=(
                LineStyle.from_json(json["containerBorder"])
                if json.get("containerBorder", None) is not None
                else None
            ),
            line_separator=(
                LineStyle.from_json(json["lineSeparator"])
                if json.get("lineSeparator", None) is not None
                else None
            ),
            item_separator=(
                LineStyle.from_json(json["itemSeparator"])
                if json.get("itemSeparator", None) is not None
                else None
            ),
            main_distributed_space=(
                BoxStyle.from_json(json["mainDistributedSpace"])
                if json.get("mainDistributedSpace", None) is not None
                else None
            ),
            cross_distributed_space=(
                BoxStyle.from_json(json["crossDistributedSpace"])
                if json.get("crossDistributedSpace", None) is not None
                else None
            ),
            row_gap_space=(
                BoxStyle.from_json(json["rowGapSpace"])
                if json.get("rowGapSpace", None) is not None
                else None
            ),
            column_gap_space=(
                BoxStyle.from_json(json["columnGapSpace"])
                if json.get("columnGapSpace", None) is not None
                else None
            ),
            cross_alignment=(
                LineStyle.from_json(json["crossAlignment"])
                if json.get("crossAlignment", None) is not None
                else None
            ),
        )


@dataclass
class FlexItemHighlightConfig:
    """
    Configuration data for the highlighting of Flex item elements.
    """

    #: Style of the box representing the item's base size
    base_size_box: typing.Optional[BoxStyle] = None

    #: Style of the border around the box representing the item's base size
    base_size_border: typing.Optional[LineStyle] = None

    #: Style of the arrow representing if the item grew or shrank
    flexibility_arrow: typing.Optional[LineStyle] = None

    def to_json(self) -> T_JSON_DICT:
        json: T_JSON_DICT = dict()
        if self.base_size_box is not None:
            json["baseSizeBox"] = self.base_size_box.to_json()
        if self.base_size_border is not None:
            json["baseSizeBorder"] = self.base_size_border.to_json()
        if self.flexibility_arrow is not None:
            json["flexibilityArrow"] = self.flexibility_arrow.to_json()
        return json

    @classmethod
    def from_json(cls, json: T_JSON_DICT) -> FlexItemHighlightConfig:
        return cls(
            base_size_box=(
                BoxStyle.from_json(json["baseSizeBox"])
                if json.get("baseSizeBox", None) is not None
                else None
            ),
            base_size_border=(
                LineStyle.from_json(json["baseSizeBorder"])
                if json.get("baseSizeBorder", None) is not None
                else None
            ),
            flexibility_arrow=(
                LineStyle.from_json(json["flexibilityArrow"])
                if json.get("flexibilityArrow", None) is not None
                else None
            ),
        )


@dataclass
class LineStyle:
    """
    Style information for drawing a line.
    """

    #: The color of the line (default: transparent)
    color: typing.Optional[dom.RGBA] = None

    #: The line pattern (default: solid)
    pattern: typing.Optional[str] = None

    def to_json(self) -> T_JSON_DICT:
        json: T_JSON_DICT = dict()
        if self.color is not None:
            json["color"] = self.color.to_json()
        if self.pattern is not None:
            json["pattern"] = self.pattern
        return json

    @classmethod
    def from_json(cls, json: T_JSON_DICT) -> LineStyle:
        return cls(
            color=(
                dom.RGBA.from_json(json["color"])
                if json.get("color", None) is not None
                else None
            ),
            pattern=(
                str(json["pattern"]) if json.get("pattern", None) is not None else None
            ),
        )


@dataclass
class BoxStyle:
    """
    Style information for drawing a box.
    """

    #: The background color for the box (default: transparent)
    fill_color: typing.Optional[dom.RGBA] = None

    #: The hatching color for the box (default: transparent)
    hatch_color: typing.Optional[dom.RGBA] = None

    def to_json(self) -> T_JSON_DICT:
        json: T_JSON_DICT = dict()
        if self.fill_color is not None:
            json["fillColor"] = self.fill_color.to_json()
        if self.hatch_color is not None:
            json["hatchColor"] = self.hatch_color.to_json()
        return json

    @classmethod
    def from_json(cls, json: T_JSON_DICT) -> BoxStyle:
        return cls(
            fill_color=(
                dom.RGBA.from_json(json["fillColor"])
                if json.get("fillColor", None) is not None
                else None
            ),
            hatch_color=(
                dom.RGBA.from_json(json["hatchColor"])
                if json.get("hatchColor", None) is not None
                else None
            ),
        )


class ContrastAlgorithm(enum.Enum):
    AA = "aa"
    AAA = "aaa"
    APCA = "apca"

    def to_json(self) -> str:
        return self.value

    @classmethod
    def from_json(cls, json: str) -> ContrastAlgorithm:
        return cls(json)


@dataclass
class HighlightConfig:
    """
    Configuration data for the highlighting of page elements.
    """

    #: Whether the node info tooltip should be shown (default: false).
    show_info: typing.Optional[bool] = None

    #: Whether the node styles in the tooltip (default: false).
    show_styles: typing.Optional[bool] = None

    #: Whether the rulers should be shown (default: false).
    show_rulers: typing.Optional[bool] = None

    #: Whether the a11y info should be shown (default: true).
    show_accessibility_info: typing.Optional[bool] = None

    #: Whether the extension lines from node to the rulers should be shown (default: false).
    show_extension_lines: typing.Optional[bool] = None

    #: The content box highlight fill color (default: transparent).
    content_color: typing.Optional[dom.RGBA] = None

    #: The padding highlight fill color (default: transparent).
    padding_color: typing.Optional[dom.RGBA] = None

    #: The border highlight fill color (default: transparent).
    border_color: typing.Optional[dom.RGBA] = None

    #: The margin highlight fill color (default: transparent).
    margin_color: typing.Optional[dom.RGBA] = None

    #: The event target element highlight fill color (default: transparent).
    event_target_color: typing.Optional[dom.RGBA] = None

    #: The shape outside fill color (default: transparent).
    shape_color: typing.Optional[dom.RGBA] = None

    #: The shape margin fill color (default: transparent).
    shape_margin_color: typing.Optional[dom.RGBA] = None

    #: The grid layout color (default: transparent).
    css_grid_color: typing.Optional[dom.RGBA] = None

    #: The color format used to format color styles (default: hex).
    color_format: typing.Optional[ColorFormat] = None

    #: The grid layout highlight configuration (default: all transparent).
    grid_highlight_config: typing.Optional[GridHighlightConfig] = None

    #: The flex container highlight configuration (default: all transparent).
    flex_container_highlight_config: typing.Optional[FlexContainerHighlightConfig] = (
        None
    )

    #: The flex item highlight configuration (default: all transparent).
    flex_item_highlight_config: typing.Optional[FlexItemHighlightConfig] = None

    #: The contrast algorithm to use for the contrast ratio (default: aa).
    contrast_algorithm: typing.Optional[ContrastAlgorithm] = None

    #: The container query container highlight configuration (default: all transparent).
    container_query_container_highlight_config: typing.Optional[
        ContainerQueryContainerHighlightConfig
    ] = None

    def to_json(self) -> T_JSON_DICT:
        json: T_JSON_DICT = dict()
        if self.show_info is not None:
            json["showInfo"] = self.show_info
        if self.show_styles is not None:
            json["showStyles"] = self.show_styles
        if self.show_rulers is not None:
            json["showRulers"] = self.show_rulers
        if self.show_accessibility_info is not None:
            json["showAccessibilityInfo"] = self.show_accessibility_info
        if self.show_extension_lines is not None:
            json["showExtensionLines"] = self.show_extension_lines
        if self.content_color is not None:
            json["contentColor"] = self.content_color.to_json()
        if self.padding_color is not None:
            json["paddingColor"] = self.padding_color.to_json()
        if self.border_color is not None:
            json["borderColor"] = self.border_color.to_json()
        if self.margin_color is not None:
            json["marginColor"] = self.margin_color.to_json()
        if self.event_target_color is not None:
            json["eventTargetColor"] = self.event_target_color.to_json()
        if self.shape_color is not None:
            json["shapeColor"] = self.shape_color.to_json()
        if self.shape_margin_color is not None:
            json["shapeMarginColor"] = self.shape_margin_color.to_json()
        if self.css_grid_color is not None:
            json["cssGridColor"] = self.css_grid_color.to_json()
        if self.color_format is not None:
            json["colorFormat"] = self.color_format.to_json()
        if self.grid_highlight_config is not None:
            json["gridHighlightConfig"] = self.grid_highlight_config.to_json()
        if self.flex_container_highlight_config is not None:
            json["flexContainerHighlightConfig"] = (
                self.flex_container_highlight_config.to_json()
            )
        if self.flex_item_highlight_config is not None:
            json["flexItemHighlightConfig"] = self.flex_item_highlight_config.to_json()
        if self.contrast_algorithm is not None:
            json["contrastAlgorithm"] = self.contrast_algorithm.to_json()
        if self.container_query_container_highlight_config is not None:
            json["containerQueryContainerHighlightConfig"] = (
                self.container_query_container_highlight_config.to_json()
            )
        return json

    @classmethod
    def from_json(cls, json: T_JSON_DICT) -> HighlightConfig:
        return cls(
            show_info=(
                bool(json["showInfo"])
                if json.get("showInfo", None) is not None
                else None
            ),
            show_styles=(
                bool(json["showStyles"])
                if json.get("showStyles", None) is not None
                else None
            ),
            show_rulers=(
                bool(json["showRulers"])
                if json.get("showRulers", None) is not None
                else None
            ),
            show_accessibility_info=(
                bool(json["showAccessibilityInfo"])
                if json.get("showAccessibilityInfo", None) is not None
                else None
            ),
            show_extension_lines=(
                bool(json["showExtensionLines"])
                if json.get("showExtensionLines", None) is not None
                else None
            ),
            content_color=(
                dom.RGBA.from_json(json["contentColor"])
                if json.get("contentColor", None) is not None
                else None
            ),
            padding_color=(
                dom.RGBA.from_json(json["paddingColor"])
                if json.get("paddingColor", None) is not None
                else None
            ),
            border_color=(
                dom.RGBA.from_json(json["borderColor"])
                if json.get("borderColor", None) is not None
                else None
            ),
            margin_color=(
                dom.RGBA.from_json(json["marginColor"])
                if json.get("marginColor", None) is not None
                else None
            ),
            event_target_color=(
                dom.RGBA.from_json(json["eventTargetColor"])
                if json.get("eventTargetColor", None) is not None
                else None
            ),
            shape_color=(
                dom.RGBA.from_json(json["shapeColor"])
                if json.get("shapeColor", None) is not None
                else None
            ),
            shape_margin_color=(
                dom.RGBA.from_json(json["shapeMarginColor"])
                if json.get("shapeMarginColor", None) is not None
                else None
            ),
            css_grid_color=(
                dom.RGBA.from_json(json["cssGridColor"])
                if json.get("cssGridColor", None) is not None
                else None
            ),
            color_format=(
                ColorFormat.from_json(json["colorFormat"])
                if json.get("colorFormat", None) is not None
                else None
            ),
            grid_highlight_config=(
                GridHighlightConfig.from_json(json["gridHighlightConfig"])
                if json.get("gridHighlightConfig", None) is not None
                else None
            ),
            flex_container_highlight_config=(
                FlexContainerHighlightConfig.from_json(
                    json["flexContainerHighlightConfig"]
                )
                if json.get("flexContainerHighlightConfig", None) is not None
                else None
            ),
            flex_item_highlight_config=(
                FlexItemHighlightConfig.from_json(json["flexItemHighlightConfig"])
                if json.get("flexItemHighlightConfig", None) is not None
                else None
            ),
            contrast_algorithm=(
                ContrastAlgorithm.from_json(json["contrastAlgorithm"])
                if json.get("contrastAlgorithm", None) is not None
                else None
            ),
            container_query_container_highlight_config=(
                ContainerQueryContainerHighlightConfig.from_json(
                    json["containerQueryContainerHighlightConfig"]
                )
                if json.get("containerQueryContainerHighlightConfig", None) is not None
                else None
            ),
        )


class ColorFormat(enum.Enum):
    RGB = "rgb"
    HSL = "hsl"
    HWB = "hwb"
    HEX_ = "hex"

    def to_json(self) -> str:
        return self.value

    @classmethod
    def from_json(cls, json: str) -> ColorFormat:
        return cls(json)


@dataclass
class GridNodeHighlightConfig:
    """
    Configurations for Persistent Grid Highlight
    """

    #: A descriptor for the highlight appearance.
    grid_highlight_config: GridHighlightConfig

    #: Identifier of the node to highlight.
    node_id: dom.NodeId

    def to_json(self) -> T_JSON_DICT:
        json: T_JSON_DICT = dict()
        json["gridHighlightConfig"] = self.grid_highlight_config.to_json()
        json["nodeId"] = self.node_id.to_json()
        return json

    @classmethod
    def from_json(cls, json: T_JSON_DICT) -> GridNodeHighlightConfig:
        return cls(
            grid_highlight_config=GridHighlightConfig.from_json(
                json["gridHighlightConfig"]
            ),
            node_id=dom.NodeId.from_json(json["nodeId"]),
        )


@dataclass
class FlexNodeHighlightConfig:
    #: A descriptor for the highlight appearance of flex containers.
    flex_container_highlight_config: FlexContainerHighlightConfig

    #: Identifier of the node to highlight.
    node_id: dom.NodeId

    def to_json(self) -> T_JSON_DICT:
        json: T_JSON_DICT = dict()
        json["flexContainerHighlightConfig"] = (
            self.flex_container_highlight_config.to_json()
        )
        json["nodeId"] = self.node_id.to_json()
        return json

    @classmethod
    def from_json(cls, json: T_JSON_DICT) -> FlexNodeHighlightConfig:
        return cls(
            flex_container_highlight_config=FlexContainerHighlightConfig.from_json(
                json["flexContainerHighlightConfig"]
            ),
            node_id=dom.NodeId.from_json(json["nodeId"]),
        )


@dataclass
class ScrollSnapContainerHighlightConfig:
    #: The style of the snapport border (default: transparent)
    snapport_border: typing.Optional[LineStyle] = None

    #: The style of the snap area border (default: transparent)
    snap_area_border: typing.Optional[LineStyle] = None

    #: The margin highlight fill color (default: transparent).
    scroll_margin_color: typing.Optional[dom.RGBA] = None

    #: The padding highlight fill color (default: transparent).
    scroll_padding_color: typing.Optional[dom.RGBA] = None

    def to_json(self) -> T_JSON_DICT:
        json: T_JSON_DICT = dict()
        if self.snapport_border is not None:
            json["snapportBorder"] = self.snapport_border.to_json()
        if self.snap_area_border is not None:
            json["snapAreaBorder"] = self.snap_area_border.to_json()
        if self.scroll_margin_color is not None:
            json["scrollMarginColor"] = self.scroll_margin_color.to_json()
        if self.scroll_padding_color is not None:
            json["scrollPaddingColor"] = self.scroll_padding_color.to_json()
        return json

    @classmethod
    def from_json(cls, json: T_JSON_DICT) -> ScrollSnapContainerHighlightConfig:
        return cls(
            snapport_border=(
                LineStyle.from_json(json["snapportBorder"])
                if json.get("snapportBorder", None) is not None
                else None
            ),
            snap_area_border=(
                LineStyle.from_json(json["snapAreaBorder"])
                if json.get("snapAreaBorder", None) is not None
                else None
            ),
            scroll_margin_color=(
                dom.RGBA.from_json(json["scrollMarginColor"])
                if json.get("scrollMarginColor", None) is not None
                else None
            ),
            scroll_padding_color=(
                dom.RGBA.from_json(json["scrollPaddingColor"])
                if json.get("scrollPaddingColor", None) is not None
                else None
            ),
        )


@dataclass
class ScrollSnapHighlightConfig:
    #: A descriptor for the highlight appearance of scroll snap containers.
    scroll_snap_container_highlight_config: ScrollSnapContainerHighlightConfig

    #: Identifier of the node to highlight.
    node_id: dom.NodeId

    def to_json(self) -> T_JSON_DICT:
        json: T_JSON_DICT = dict()
        json["scrollSnapContainerHighlightConfig"] = (
            self.scroll_snap_container_highlight_config.to_json()
        )
        json["nodeId"] = self.node_id.to_json()
        return json

    @classmethod
    def from_json(cls, json: T_JSON_DICT) -> ScrollSnapHighlightConfig:
        return cls(
            scroll_snap_container_highlight_config=ScrollSnapContainerHighlightConfig.from_json(
                json["scrollSnapContainerHighlightConfig"]
            ),
            node_id=dom.NodeId.from_json(json["nodeId"]),
        )


@dataclass
class HingeConfig:
    """
    Configuration for dual screen hinge
    """

    #: A rectangle represent hinge
    rect: dom.Rect

    #: The content box highlight fill color (default: a dark color).
    content_color: typing.Optional[dom.RGBA] = None

    #: The content box highlight outline color (default: transparent).
    outline_color: typing.Optional[dom.RGBA] = None

    def to_json(self) -> T_JSON_DICT:
        json: T_JSON_DICT = dict()
        json["rect"] = self.rect.to_json()
        if self.content_color is not None:
            json["contentColor"] = self.content_color.to_json()
        if self.outline_color is not None:
            json["outlineColor"] = self.outline_color.to_json()
        return json

    @classmethod
    def from_json(cls, json: T_JSON_DICT) -> HingeConfig:
        return cls(
            rect=dom.Rect.from_json(json["rect"]),
            content_color=(
                dom.RGBA.from_json(json["contentColor"])
                if json.get("contentColor", None) is not None
                else None
            ),
            outline_color=(
                dom.RGBA.from_json(json["outlineColor"])
                if json.get("outlineColor", None) is not None
                else None
            ),
        )


@dataclass
class WindowControlsOverlayConfig:
    """
    Configuration for Window Controls Overlay
    """

    #: Whether the title bar CSS should be shown when emulating the Window Controls Overlay.
    show_css: bool

    #: Selected platforms to show the overlay.
    selected_platform: str

    #: The theme color defined in app manifest.
    theme_color: str

    def to_json(self) -> T_JSON_DICT:
        json: T_JSON_DICT = dict()
        json["showCSS"] = self.show_css
        json["selectedPlatform"] = self.selected_platform
        json["themeColor"] = self.theme_color
        return json

    @classmethod
    def from_json(cls, json: T_JSON_DICT) -> WindowControlsOverlayConfig:
        return cls(
            show_css=bool(json["showCSS"]),
            selected_platform=str(json["selectedPlatform"]),
            theme_color=str(json["themeColor"]),
        )


@dataclass
class ContainerQueryHighlightConfig:
    #: A descriptor for the highlight appearance of container query containers.
    container_query_container_highlight_config: ContainerQueryContainerHighlightConfig

    #: Identifier of the container node to highlight.
    node_id: dom.NodeId

    def to_json(self) -> T_JSON_DICT:
        json: T_JSON_DICT = dict()
        json["containerQueryContainerHighlightConfig"] = (
            self.container_query_container_highlight_config.to_json()
        )
        json["nodeId"] = self.node_id.to_json()
        return json

    @classmethod
    def from_json(cls, json: T_JSON_DICT) -> ContainerQueryHighlightConfig:
        return cls(
            container_query_container_highlight_config=ContainerQueryContainerHighlightConfig.from_json(
                json["containerQueryContainerHighlightConfig"]
            ),
            node_id=dom.NodeId.from_json(json["nodeId"]),
        )


@dataclass
class ContainerQueryContainerHighlightConfig:
    #: The style of the container border.
    container_border: typing.Optional[LineStyle] = None

    #: The style of the descendants' borders.
    descendant_border: typing.Optional[LineStyle] = None

    def to_json(self) -> T_JSON_DICT:
        json: T_JSON_DICT = dict()
        if self.container_border is not None:
            json["containerBorder"] = self.container_border.to_json()
        if self.descendant_border is not None:
            json["descendantBorder"] = self.descendant_border.to_json()
        return json

    @classmethod
    def from_json(cls, json: T_JSON_DICT) -> ContainerQueryContainerHighlightConfig:
        return cls(
            container_border=(
                LineStyle.from_json(json["containerBorder"])
                if json.get("containerBorder", None) is not None
                else None
            ),
            descendant_border=(
                LineStyle.from_json(json["descendantBorder"])
                if json.get("descendantBorder", None) is not None
                else None
            ),
        )


@dataclass
class IsolatedElementHighlightConfig:
    #: A descriptor for the highlight appearance of an element in isolation mode.
    isolation_mode_highlight_config: IsolationModeHighlightConfig

    #: Identifier of the isolated element to highlight.
    node_id: dom.NodeId

    def to_json(self) -> T_JSON_DICT:
        json: T_JSON_DICT = dict()
        json["isolationModeHighlightConfig"] = (
            self.isolation_mode_highlight_config.to_json()
        )
        json["nodeId"] = self.node_id.to_json()
        return json

    @classmethod
    def from_json(cls, json: T_JSON_DICT) -> IsolatedElementHighlightConfig:
        return cls(
            isolation_mode_highlight_config=IsolationModeHighlightConfig.from_json(
                json["isolationModeHighlightConfig"]
            ),
            node_id=dom.NodeId.from_json(json["nodeId"]),
        )


@dataclass
class IsolationModeHighlightConfig:
    #: The fill color of the resizers (default: transparent).
    resizer_color: typing.Optional[dom.RGBA] = None

    #: The fill color for resizer handles (default: transparent).
    resizer_handle_color: typing.Optional[dom.RGBA] = None

    #: The fill color for the mask covering non-isolated elements (default: transparent).
    mask_color: typing.Optional[dom.RGBA] = None

    def to_json(self) -> T_JSON_DICT:
        json: T_JSON_DICT = dict()
        if self.resizer_color is not None:
            json["resizerColor"] = self.resizer_color.to_json()
        if self.resizer_handle_color is not None:
            json["resizerHandleColor"] = self.resizer_handle_color.to_json()
        if self.mask_color is not None:
            json["maskColor"] = self.mask_color.to_json()
        return json

    @classmethod
    def from_json(cls, json: T_JSON_DICT) -> IsolationModeHighlightConfig:
        return cls(
            resizer_color=(
                dom.RGBA.from_json(json["resizerColor"])
                if json.get("resizerColor", None) is not None
                else None
            ),
            resizer_handle_color=(
                dom.RGBA.from_json(json["resizerHandleColor"])
                if json.get("resizerHandleColor", None) is not None
                else None
            ),
            mask_color=(
                dom.RGBA.from_json(json["maskColor"])
                if json.get("maskColor", None) is not None
                else None
            ),
        )


class InspectMode(enum.Enum):
    SEARCH_FOR_NODE = "searchForNode"
    SEARCH_FOR_UA_SHADOW_DOM = "searchForUAShadowDOM"
    CAPTURE_AREA_SCREENSHOT = "captureAreaScreenshot"
    SHOW_DISTANCES = "showDistances"
    NONE = "none"

    def to_json(self) -> str:
        return self.value

    @classmethod
    def from_json(cls, json: str) -> InspectMode:
        return cls(json)


def disable() -> typing.Generator[T_JSON_DICT, T_JSON_DICT, None]:
    """
    Disables domain notifications.
    """
    cmd_dict: T_JSON_DICT = {
        "method": "Overlay.disable",
    }
    json = yield cmd_dict


def enable() -> typing.Generator[T_JSON_DICT, T_JSON_DICT, None]:
    """
    Enables domain notifications.
    """
    cmd_dict: T_JSON_DICT = {
        "method": "Overlay.enable",
    }
    json = yield cmd_dict


def get_highlight_object_for_test(
        node_id: dom.NodeId,
        include_distance: typing.Optional[bool] = None,
        include_style: typing.Optional[bool] = None,
        color_format: typing.Optional[ColorFormat] = None,
        show_accessibility_info: typing.Optional[bool] = None,
) -> typing.Generator[T_JSON_DICT, T_JSON_DICT, dict]:
    """
    For testing.

    :param node_id: Id of the node to get highlight object for.
    :param include_distance: *(Optional)* Whether to include distance info.
    :param include_style: *(Optional)* Whether to include style info.
    :param color_format: *(Optional)* The color format to get config with (default: hex).
    :param show_accessibility_info: *(Optional)* Whether to show accessibility info (default: true).
    :returns: Highlight data for the node.
    """
    params: T_JSON_DICT = dict()
    params["nodeId"] = node_id.to_json()
    if include_distance is not None:
        params["includeDistance"] = include_distance
    if include_style is not None:
        params["includeStyle"] = include_style
    if color_format is not None:
        params["colorFormat"] = color_format.to_json()
    if show_accessibility_info is not None:
        params["showAccessibilityInfo"] = show_accessibility_info
    cmd_dict: T_JSON_DICT = {
        "method": "Overlay.getHighlightObjectForTest",
        "params": params,
    }
    json = yield cmd_dict
    return dict(json["highlight"])


def get_grid_highlight_objects_for_test(
        node_ids: typing.List[dom.NodeId],
) -> typing.Generator[T_JSON_DICT, T_JSON_DICT, dict]:
    """
    For Persistent Grid testing.

    :param node_ids: Ids of the node to get highlight object for.
    :returns: Grid Highlight data for the node ids provided.
    """
    params: T_JSON_DICT = dict()
    params["nodeIds"] = [i.to_json() for i in node_ids]
    cmd_dict: T_JSON_DICT = {
        "method": "Overlay.getGridHighlightObjectsForTest",
        "params": params,
    }
    json = yield cmd_dict
    return dict(json["highlights"])


def get_source_order_highlight_object_for_test(
        node_id: dom.NodeId,
) -> typing.Generator[T_JSON_DICT, T_JSON_DICT, dict]:
    """
    For Source Order Viewer testing.

    :param node_id: Id of the node to highlight.
    :returns: Source order highlight data for the node id provided.
    """
    params: T_JSON_DICT = dict()
    params["nodeId"] = node_id.to_json()
    cmd_dict: T_JSON_DICT = {
        "method": "Overlay.getSourceOrderHighlightObjectForTest",
        "params": params,
    }
    json = yield cmd_dict
    return dict(json["highlight"])


def hide_highlight() -> typing.Generator[T_JSON_DICT, T_JSON_DICT, None]:
    """
    Hides any highlight.
    """
    cmd_dict: T_JSON_DICT = {
        "method": "Overlay.hideHighlight",
    }
    json = yield cmd_dict


@deprecated(version="1.3")
def highlight_frame(
        frame_id: page.FrameId,
        content_color: typing.Optional[dom.RGBA] = None,
        content_outline_color: typing.Optional[dom.RGBA] = None,
) -> typing.Generator[T_JSON_DICT, T_JSON_DICT, None]:
    """
    Highlights owner element of the frame with given id.
    Deprecated: Doesn't work reliably and cannot be fixed due to process
    separation (the owner node might be in a different process). Determine
    the owner node in the client and use highlightNode.

    .. deprecated:: 1.3

    :param frame_id: Identifier of the frame to highlight.
    :param content_color: *(Optional)* The content box highlight fill color (default: transparent).
    :param content_outline_color: *(Optional)* The content box highlight outline color (default: transparent).
    """
    params: T_JSON_DICT = dict()
    params["frameId"] = frame_id.to_json()
    if content_color is not None:
        params["contentColor"] = content_color.to_json()
    if content_outline_color is not None:
        params["contentOutlineColor"] = content_outline_color.to_json()
    cmd_dict: T_JSON_DICT = {
        "method": "Overlay.highlightFrame",
        "params": params,
    }
    json = yield cmd_dict


def highlight_node(
        highlight_config: HighlightConfig,
        node_id: typing.Optional[dom.NodeId] = None,
        backend_node_id: typing.Optional[dom.BackendNodeId] = None,
        object_id: typing.Optional[runtime.RemoteObjectId] = None,
        selector: typing.Optional[str] = None,
) -> typing.Generator[T_JSON_DICT, T_JSON_DICT, None]:
    """
    Highlights DOM node with given id or with the given JavaScript object wrapper. Either nodeId or
    objectId must be specified.

    :param highlight_config: A descriptor for the highlight appearance.
    :param node_id: *(Optional)* Identifier of the node to highlight.
    :param backend_node_id: *(Optional)* Identifier of the backend node to highlight.
    :param object_id: *(Optional)* JavaScript object id of the node to be highlighted.
    :param selector: *(Optional)* Selectors to highlight relevant nodes.
    """
    params: T_JSON_DICT = dict()
    params["highlightConfig"] = highlight_config.to_json()
    if node_id is not None:
        params["nodeId"] = node_id.to_json()
    if backend_node_id is not None:
        params["backendNodeId"] = backend_node_id.to_json()
    if object_id is not None:
        params["objectId"] = object_id.to_json()
    if selector is not None:
        params["selector"] = selector
    cmd_dict: T_JSON_DICT = {
        "method": "Overlay.highlightNode",
        "params": params,
    }
    json = yield cmd_dict


def highlight_quad(
        quad: dom.Quad,
        color: typing.Optional[dom.RGBA] = None,
        outline_color: typing.Optional[dom.RGBA] = None,
) -> typing.Generator[T_JSON_DICT, T_JSON_DICT, None]:
    """
    Highlights given quad. Coordinates are absolute with respect to the main frame viewport.

    :param quad: Quad to highlight
    :param color: *(Optional)* The highlight fill color (default: transparent).
    :param outline_color: *(Optional)* The highlight outline color (default: transparent).
    """
    params: T_JSON_DICT = dict()
    params["quad"] = quad.to_json()
    if color is not None:
        params["color"] = color.to_json()
    if outline_color is not None:
        params["outlineColor"] = outline_color.to_json()
    cmd_dict: T_JSON_DICT = {
        "method": "Overlay.highlightQuad",
        "params": params,
    }
    json = yield cmd_dict


def highlight_rect(
        x: int,
        y: int,
        width: int,
        height: int,
        color: typing.Optional[dom.RGBA] = None,
        outline_color: typing.Optional[dom.RGBA] = None,
) -> typing.Generator[T_JSON_DICT, T_JSON_DICT, None]:
    """
    Highlights given rectangle. Coordinates are absolute with respect to the main frame viewport.

    :param x: X coordinate
    :param y: Y coordinate
    :param width: Rectangle width
    :param height: Rectangle height
    :param color: *(Optional)* The highlight fill color (default: transparent).
    :param outline_color: *(Optional)* The highlight outline color (default: transparent).
    """
    params: T_JSON_DICT = dict()
    params["x"] = x
    params["y"] = y
    params["width"] = width
    params["height"] = height
    if color is not None:
        params["color"] = color.to_json()
    if outline_color is not None:
        params["outlineColor"] = outline_color.to_json()
    cmd_dict: T_JSON_DICT = {
        "method": "Overlay.highlightRect",
        "params": params,
    }
    json = yield cmd_dict


def highlight_source_order(
        source_order_config: SourceOrderConfig,
        node_id: typing.Optional[dom.NodeId] = None,
        backend_node_id: typing.Optional[dom.BackendNodeId] = None,
        object_id: typing.Optional[runtime.RemoteObjectId] = None,
) -> typing.Generator[T_JSON_DICT, T_JSON_DICT, None]:
    """
    Highlights the source order of the children of the DOM node with given id or with the given
    JavaScript object wrapper. Either nodeId or objectId must be specified.

    :param source_order_config: A descriptor for the appearance of the overlay drawing.
    :param node_id: *(Optional)* Identifier of the node to highlight.
    :param backend_node_id: *(Optional)* Identifier of the backend node to highlight.
    :param object_id: *(Optional)* JavaScript object id of the node to be highlighted.
    """
    params: T_JSON_DICT = dict()
    params["sourceOrderConfig"] = source_order_config.to_json()
    if node_id is not None:
        params["nodeId"] = node_id.to_json()
    if backend_node_id is not None:
        params["backendNodeId"] = backend_node_id.to_json()
    if object_id is not None:
        params["objectId"] = object_id.to_json()
    cmd_dict: T_JSON_DICT = {
        "method": "Overlay.highlightSourceOrder",
        "params": params,
    }
    json = yield cmd_dict


def set_inspect_mode(
        mode: InspectMode, highlight_config: typing.Optional[HighlightConfig] = None
) -> typing.Generator[T_JSON_DICT, T_JSON_DICT, None]:
    """
    Enters the 'inspect' mode. In this mode, elements that user is hovering over are highlighted.
    Backend then generates 'inspectNodeRequested' event upon element selection.

    :param mode: Set an inspection mode.
    :param highlight_config: *(Optional)* A descriptor for the highlight appearance of hovered-over nodes. May be omitted if ```enabled == false```.
    """
    params: T_JSON_DICT = dict()
    params["mode"] = mode.to_json()
    if highlight_config is not None:
        params["highlightConfig"] = highlight_config.to_json()
    cmd_dict: T_JSON_DICT = {
        "method": "Overlay.setInspectMode",
        "params": params,
    }
    json = yield cmd_dict


def set_show_ad_highlights(
        show: bool,
) -> typing.Generator[T_JSON_DICT, T_JSON_DICT, None]:
    """
    Highlights owner element of all frames detected to be ads.

    :param show: True for showing ad highlights
    """
    params: T_JSON_DICT = dict()
    params["show"] = show
    cmd_dict: T_JSON_DICT = {
        "method": "Overlay.setShowAdHighlights",
        "params": params,
    }
    json = yield cmd_dict


def set_paused_in_debugger_message(
        message: typing.Optional[str] = None,
) -> typing.Generator[T_JSON_DICT, T_JSON_DICT, None]:
    """
    :param message: *(Optional)* The message to display, also triggers resume and step over controls.
    """
    params: T_JSON_DICT = dict()
    if message is not None:
        params["message"] = message
    cmd_dict: T_JSON_DICT = {
        "method": "Overlay.setPausedInDebuggerMessage",
        "params": params,
    }
    json = yield cmd_dict


def set_show_debug_borders(
        show: bool,
) -> typing.Generator[T_JSON_DICT, T_JSON_DICT, None]:
    """
    Requests that backend shows debug borders on layers

    :param show: True for showing debug borders
    """
    params: T_JSON_DICT = dict()
    params["show"] = show
    cmd_dict: T_JSON_DICT = {
        "method": "Overlay.setShowDebugBorders",
        "params": params,
    }
    json = yield cmd_dict


def set_show_fps_counter(
        show: bool,
) -> typing.Generator[T_JSON_DICT, T_JSON_DICT, None]:
    """
    Requests that backend shows the FPS counter

    :param show: True for showing the FPS counter
    """
    params: T_JSON_DICT = dict()
    params["show"] = show
    cmd_dict: T_JSON_DICT = {
        "method": "Overlay.setShowFPSCounter",
        "params": params,
    }
    json = yield cmd_dict


def set_show_grid_overlays(
        grid_node_highlight_configs: typing.List[GridNodeHighlightConfig],
) -> typing.Generator[T_JSON_DICT, T_JSON_DICT, None]:
    """
    Highlight multiple elements with the CSS Grid overlay.

    :param grid_node_highlight_configs: An array of node identifiers and descriptors for the highlight appearance.
    """
    params: T_JSON_DICT = dict()
    params["gridNodeHighlightConfigs"] = [
        i.to_json() for i in grid_node_highlight_configs
    ]
    cmd_dict: T_JSON_DICT = {
        "method": "Overlay.setShowGridOverlays",
        "params": params,
    }
    json = yield cmd_dict


def set_show_flex_overlays(
        flex_node_highlight_configs: typing.List[FlexNodeHighlightConfig],
) -> typing.Generator[T_JSON_DICT, T_JSON_DICT, None]:
    """
    :param flex_node_highlight_configs: An array of node identifiers and descriptors for the highlight appearance.
    """
    params: T_JSON_DICT = dict()
    params["flexNodeHighlightConfigs"] = [
        i.to_json() for i in flex_node_highlight_configs
    ]
    cmd_dict: T_JSON_DICT = {
        "method": "Overlay.setShowFlexOverlays",
        "params": params,
    }
    json = yield cmd_dict


def set_show_scroll_snap_overlays(
        scroll_snap_highlight_configs: typing.List[ScrollSnapHighlightConfig],
) -> typing.Generator[T_JSON_DICT, T_JSON_DICT, None]:
    """
    :param scroll_snap_highlight_configs: An array of node identifiers and descriptors for the highlight appearance.
    """
    params: T_JSON_DICT = dict()
    params["scrollSnapHighlightConfigs"] = [
        i.to_json() for i in scroll_snap_highlight_configs
    ]
    cmd_dict: T_JSON_DICT = {
        "method": "Overlay.setShowScrollSnapOverlays",
        "params": params,
    }
    json = yield cmd_dict


def set_show_container_query_overlays(
        container_query_highlight_configs: typing.List[ContainerQueryHighlightConfig],
) -> typing.Generator[T_JSON_DICT, T_JSON_DICT, None]:
    """
    :param container_query_highlight_configs: An array of node identifiers and descriptors for the highlight appearance.
    """
    params: T_JSON_DICT = dict()
    params["containerQueryHighlightConfigs"] = [
        i.to_json() for i in container_query_highlight_configs
    ]
    cmd_dict: T_JSON_DICT = {
        "method": "Overlay.setShowContainerQueryOverlays",
        "params": params,
    }
    json = yield cmd_dict


def set_show_paint_rects(
        result: bool,
) -> typing.Generator[T_JSON_DICT, T_JSON_DICT, None]:
    """
    Requests that backend shows paint rectangles

    :param result: True for showing paint rectangles
    """
    params: T_JSON_DICT = dict()
    params["result"] = result
    cmd_dict: T_JSON_DICT = {
        "method": "Overlay.setShowPaintRects",
        "params": params,
    }
    json = yield cmd_dict


def set_show_layout_shift_regions(
        result: bool,
) -> typing.Generator[T_JSON_DICT, T_JSON_DICT, None]:
    """
    Requests that backend shows layout shift regions

    :param result: True for showing layout shift regions
    """
    params: T_JSON_DICT = dict()
    params["result"] = result
    cmd_dict: T_JSON_DICT = {
        "method": "Overlay.setShowLayoutShiftRegions",
        "params": params,
    }
    json = yield cmd_dict


def set_show_scroll_bottleneck_rects(
        show: bool,
) -> typing.Generator[T_JSON_DICT, T_JSON_DICT, None]:
    """
    Requests that backend shows scroll bottleneck rects

    :param show: True for showing scroll bottleneck rects
    """
    params: T_JSON_DICT = dict()
    params["show"] = show
    cmd_dict: T_JSON_DICT = {
        "method": "Overlay.setShowScrollBottleneckRects",
        "params": params,
    }
    json = yield cmd_dict


@deprecated(version="1.3")
def set_show_hit_test_borders(
        show: bool,
) -> typing.Generator[T_JSON_DICT, T_JSON_DICT, None]:
    """
    Deprecated, no longer has any effect.

    .. deprecated:: 1.3

    :param show: True for showing hit-test borders
    """
    params: T_JSON_DICT = dict()
    params["show"] = show
    cmd_dict: T_JSON_DICT = {
        "method": "Overlay.setShowHitTestBorders",
        "params": params,
    }
    json = yield cmd_dict


@deprecated(version="1.3")
def set_show_web_vitals(show: bool) -> typing.Generator[T_JSON_DICT, T_JSON_DICT, None]:
    """
    Deprecated, no longer has any effect.

    .. deprecated:: 1.3

    :param show:
    """
    params: T_JSON_DICT = dict()
    params["show"] = show
    cmd_dict: T_JSON_DICT = {
        "method": "Overlay.setShowWebVitals",
        "params": params,
    }
    json = yield cmd_dict


def set_show_viewport_size_on_resize(
        show: bool,
) -> typing.Generator[T_JSON_DICT, T_JSON_DICT, None]:
    """
    Paints viewport size upon main frame resize.

    :param show: Whether to paint size or not.
    """
    params: T_JSON_DICT = dict()
    params["show"] = show
    cmd_dict: T_JSON_DICT = {
        "method": "Overlay.setShowViewportSizeOnResize",
        "params": params,
    }
    json = yield cmd_dict


def set_show_hinge(
        hinge_config: typing.Optional[HingeConfig] = None,
) -> typing.Generator[T_JSON_DICT, T_JSON_DICT, None]:
    """
    Add a dual screen device hinge

    :param hinge_config: *(Optional)* hinge data, null means hideHinge
    """
    params: T_JSON_DICT = dict()
    if hinge_config is not None:
        params["hingeConfig"] = hinge_config.to_json()
    cmd_dict: T_JSON_DICT = {
        "method": "Overlay.setShowHinge",
        "params": params,
    }
    json = yield cmd_dict


def set_show_isolated_elements(
        isolated_element_highlight_configs: typing.List[IsolatedElementHighlightConfig],
) -> typing.Generator[T_JSON_DICT, T_JSON_DICT, None]:
    """
    Show elements in isolation mode with overlays.

    :param isolated_element_highlight_configs: An array of node identifiers and descriptors for the highlight appearance.
    """
    params: T_JSON_DICT = dict()
    params["isolatedElementHighlightConfigs"] = [
        i.to_json() for i in isolated_element_highlight_configs
    ]
    cmd_dict: T_JSON_DICT = {
        "method": "Overlay.setShowIsolatedElements",
        "params": params,
    }
    json = yield cmd_dict


def set_show_window_controls_overlay(
        window_controls_overlay_config: typing.Optional[WindowControlsOverlayConfig] = None,
) -> typing.Generator[T_JSON_DICT, T_JSON_DICT, None]:
    """
    Show Window Controls Overlay for PWA

    :param window_controls_overlay_config: *(Optional)* Window Controls Overlay data, null means hide Window Controls Overlay
    """
    params: T_JSON_DICT = dict()
    if window_controls_overlay_config is not None:
        params["windowControlsOverlayConfig"] = window_controls_overlay_config.to_json()
    cmd_dict: T_JSON_DICT = {
        "method": "Overlay.setShowWindowControlsOverlay",
        "params": params,
    }
    json = yield cmd_dict


@event_class("Overlay.inspectNodeRequested")
@dataclass
class InspectNodeRequested:
    """
    Fired when the node should be inspected. This happens after call to ``setInspectMode`` or when
    user manually inspects an element.
    """

    #: Id of the node to inspect.
    backend_node_id: dom.BackendNodeId

    @classmethod
    def from_json(cls, json: T_JSON_DICT) -> InspectNodeRequested:
        return cls(backend_node_id=dom.BackendNodeId.from_json(json["backendNodeId"]))


@event_class("Overlay.nodeHighlightRequested")
@dataclass
class NodeHighlightRequested:
    """
    Fired when the node should be highlighted. This happens after call to ``setInspectMode``.
    """

    node_id: dom.NodeId

    @classmethod
    def from_json(cls, json: T_JSON_DICT) -> NodeHighlightRequested:
        return cls(node_id=dom.NodeId.from_json(json["nodeId"]))


@event_class("Overlay.screenshotRequested")
@dataclass
class ScreenshotRequested:
    """
    Fired when user asks to capture screenshot of some area on the page.
    """

    #: Viewport to capture, in device independent pixels (dip).
    viewport: page.Viewport

    @classmethod
    def from_json(cls, json: T_JSON_DICT) -> ScreenshotRequested:
        return cls(viewport=page.Viewport.from_json(json["viewport"]))


@event_class("Overlay.inspectModeCanceled")
@dataclass
class InspectModeCanceled:
    """
    Fired when user cancels the inspect mode.
    """

    @classmethod
    def from_json(cls, json: T_JSON_DICT) -> InspectModeCanceled:
        return cls()
