<!doctype html>
<html class="no-js" lang="en" data-content_root="../../">
  <head><meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width,initial-scale=1"/>
    <meta name="color-scheme" content="light dark"><meta name="viewport" content="width=device-width, initial-scale=1" />
<link rel="index" title="Index" href="../../genindex.html" /><link rel="search" title="Search" href="../../search.html" /><link rel="next" title="DeviceAccess" href="device_access.html" /><link rel="prev" title="CSS" href="css.html" />

    <!-- Generated with Sphinx 7.2.6 and Furo 2023.09.10 -->
        <title>Debugger - nodriver documentation</title>
      <link rel="stylesheet" type="text/css" href="../../_static/pygments.css?v=fa44fd50" />
    <link rel="stylesheet" type="text/css" href="../../_static/styles/furo.css?v=135e06be" />
    <link rel="stylesheet" type="text/css" href="../../_static/styles/furo-extensions.css?v=36a5483c" />
    <link rel="stylesheet" type="text/css" href="../../_static/./custom.css?v=3934642d" />
    
    


<style>
  body {
    --color-code-background: #eeffcc;
  --color-code-foreground: black;
  
  }
  @media not print {
    body[data-theme="dark"] {
      --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8eefba;
  --color-background-primary: #111112;
  --color-problematic: #e7aeae;
  
    }
    @media (prefers-color-scheme: dark) {
      body:not([data-theme="light"]) {
        --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8eefba;
  --color-background-primary: #111112;
  --color-problematic: #e7aeae;
  
      }
    }
  }
</style></head>
  <body>
    
    <script>
      document.body.dataset.theme = localStorage.getItem("theme") || "auto";
    </script>
    

<svg xmlns="http://www.w3.org/2000/svg" style="display: none;">
  <symbol id="svg-toc" viewBox="0 0 24 24">
    <title>Contents</title>
    <svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 1024 1024">
      <path d="M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM115.4 518.9L271.7 642c5.8 4.6 14.4.5 14.4-6.9V388.9c0-7.4-8.5-11.5-14.4-6.9L115.4 505.1a8.74 8.74 0 0 0 0 13.8z"/>
    </svg>
  </symbol>
  <symbol id="svg-menu" viewBox="0 0 24 24">
    <title>Menu</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-menu">
      <line x1="3" y1="12" x2="21" y2="12"></line>
      <line x1="3" y1="6" x2="21" y2="6"></line>
      <line x1="3" y1="18" x2="21" y2="18"></line>
    </svg>
  </symbol>
  <symbol id="svg-arrow-right" viewBox="0 0 24 24">
    <title>Expand</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-chevron-right">
      <polyline points="9 18 15 12 9 6"></polyline>
    </svg>
  </symbol>
  <symbol id="svg-sun" viewBox="0 0 24 24">
    <title>Light mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="feather-sun">
      <circle cx="12" cy="12" r="5"></circle>
      <line x1="12" y1="1" x2="12" y2="3"></line>
      <line x1="12" y1="21" x2="12" y2="23"></line>
      <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
      <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
      <line x1="1" y1="12" x2="3" y2="12"></line>
      <line x1="21" y1="12" x2="23" y2="12"></line>
      <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
      <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
    </svg>
  </symbol>
  <symbol id="svg-moon" viewBox="0 0 24 24">
    <title>Dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-moon">
      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
      <path d="M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z" />
    </svg>
  </symbol>
  <symbol id="svg-sun-half" viewBox="0 0 24 24">
    <title>Auto light/dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-shadow">
      <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
      <circle cx="12" cy="12" r="9" />
      <path d="M13 12h5" />
      <path d="M13 15h4" />
      <path d="M13 18h1" />
      <path d="M13 9h4" />
      <path d="M13 6h1" />
    </svg>
  </symbol>
</svg>

<input type="checkbox" class="sidebar-toggle" name="__navigation" id="__navigation">
<input type="checkbox" class="sidebar-toggle" name="__toc" id="__toc">
<label class="overlay sidebar-overlay" for="__navigation">
  <div class="visually-hidden">Hide navigation sidebar</div>
</label>
<label class="overlay toc-overlay" for="__toc">
  <div class="visually-hidden">Hide table of contents sidebar</div>
</label>



<div class="page">
  <header class="mobile-header">
    <div class="header-left">
      <label class="nav-overlay-icon" for="__navigation">
        <div class="visually-hidden">Toggle site navigation sidebar</div>
        <i class="icon"><svg><use href="#svg-menu"></use></svg></i>
      </label>
    </div>
    <div class="header-center">
      <a href="../../index.html"><div class="brand">nodriver  documentation</div></a>
    </div>
    <div class="header-right">
      <div class="theme-toggle-container theme-toggle-header">
        <button class="theme-toggle">
          <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
          <svg class="theme-icon-when-auto"><use href="#svg-sun-half"></use></svg>
          <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
          <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
        </button>
      </div>
      <label class="toc-overlay-icon toc-header-icon" for="__toc">
        <div class="visually-hidden">Toggle table of contents sidebar</div>
        <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
      </label>
    </div>
  </header>
  <aside class="sidebar-drawer">
    <div class="sidebar-container">
      
      <div class="sidebar-sticky"><div class="sidebar-scroll"><a class="sidebar-brand" href="../../index.html">
  
  
  <span class="sidebar-brand-text">nodriver  documentation</span>
  
</a><form class="sidebar-search-container" method="get" action="../../search.html" role="search">
  <input class="sidebar-search" placeholder="Search" name="q" aria-label="Search">
  <input type="hidden" name="check_keywords" value="yes">
  <input type="hidden" name="area" value="default">
</form>
<div id="searchbox"></div><div class="sidebar-tree">
  <ul>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html">Quickstart guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#usage-example">usage example</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#more-complete-example">More complete example</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#custom-starting-options">Custom starting options</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#alternative-custom-options">Alternative custom options</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#proxies-socks5-authenticated-too">Proxies (socks5, authenticated too)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html#concrete-example">Concrete example</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../classes/browser.html">Browser class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../classes/tab.html">Tab class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../classes/element.html">Element class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../classes/others_and_helpers.html">Other classes and Helper classes</a></li>
</ul>
<ul class="current">
<li class="toctree-l1 current has-children"><a class="reference internal" href="../cdp.html">CDP object</a><input checked="" class="toctree-checkbox" id="toctree-checkbox-1" name="toctree-checkbox-1" role="switch" type="checkbox"/><label for="toctree-checkbox-1"><div class="visually-hidden">Toggle navigation of CDP object</div><i class="icon"><svg><use href="#svg-arrow-right"></use></svg></i></label><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="accessibility.html">Accessibility</a></li>
<li class="toctree-l2"><a class="reference internal" href="animation.html">Animation</a></li>
<li class="toctree-l2"><a class="reference internal" href="audits.html">Audits</a></li>
<li class="toctree-l2"><a class="reference internal" href="autofill.html">Autofill</a></li>
<li class="toctree-l2"><a class="reference internal" href="background_service.html">BackgroundService</a></li>
<li class="toctree-l2"><a class="reference internal" href="bluetooth_emulation.html">BluetoothEmulation</a></li>
<li class="toctree-l2"><a class="reference internal" href="browser.html">Browser</a></li>
<li class="toctree-l2"><a class="reference internal" href="cache_storage.html">CacheStorage</a></li>
<li class="toctree-l2"><a class="reference internal" href="cast.html">Cast</a></li>
<li class="toctree-l2"><a class="reference internal" href="console.html">Console</a></li>
<li class="toctree-l2"><a class="reference internal" href="css.html">CSS</a></li>
<li class="toctree-l2 current current-page"><a class="current reference internal" href="#">Debugger</a></li>
<li class="toctree-l2"><a class="reference internal" href="device_access.html">DeviceAccess</a></li>
<li class="toctree-l2"><a class="reference internal" href="device_orientation.html">DeviceOrientation</a></li>
<li class="toctree-l2"><a class="reference internal" href="dom.html">DOM</a></li>
<li class="toctree-l2"><a class="reference internal" href="dom_debugger.html">DOMDebugger</a></li>
<li class="toctree-l2"><a class="reference internal" href="dom_snapshot.html">DOMSnapshot</a></li>
<li class="toctree-l2"><a class="reference internal" href="dom_storage.html">DOMStorage</a></li>
<li class="toctree-l2"><a class="reference internal" href="emulation.html">Emulation</a></li>
<li class="toctree-l2"><a class="reference internal" href="event_breakpoints.html">EventBreakpoints</a></li>
<li class="toctree-l2"><a class="reference internal" href="extensions.html">Extensions</a></li>
<li class="toctree-l2"><a class="reference internal" href="fed_cm.html">FedCm</a></li>
<li class="toctree-l2"><a class="reference internal" href="fetch.html">Fetch</a></li>
<li class="toctree-l2"><a class="reference internal" href="file_system.html">FileSystem</a></li>
<li class="toctree-l2"><a class="reference internal" href="headless_experimental.html">HeadlessExperimental</a></li>
<li class="toctree-l2"><a class="reference internal" href="heap_profiler.html">HeapProfiler</a></li>
<li class="toctree-l2"><a class="reference internal" href="indexed_db.html">IndexedDB</a></li>
<li class="toctree-l2"><a class="reference internal" href="input_.html">Input</a></li>
<li class="toctree-l2"><a class="reference internal" href="inspector.html">Inspector</a></li>
<li class="toctree-l2"><a class="reference internal" href="io.html">IO</a></li>
<li class="toctree-l2"><a class="reference internal" href="layer_tree.html">LayerTree</a></li>
<li class="toctree-l2"><a class="reference internal" href="log.html">Log</a></li>
<li class="toctree-l2"><a class="reference internal" href="media.html">Media</a></li>
<li class="toctree-l2"><a class="reference internal" href="memory.html">Memory</a></li>
<li class="toctree-l2"><a class="reference internal" href="network.html">Network</a></li>
<li class="toctree-l2"><a class="reference internal" href="overlay.html">Overlay</a></li>
<li class="toctree-l2"><a class="reference internal" href="page.html">Page</a></li>
<li class="toctree-l2"><a class="reference internal" href="performance.html">Performance</a></li>
<li class="toctree-l2"><a class="reference internal" href="performance_timeline.html">PerformanceTimeline</a></li>
<li class="toctree-l2"><a class="reference internal" href="preload.html">Preload</a></li>
<li class="toctree-l2"><a class="reference internal" href="profiler.html">Profiler</a></li>
<li class="toctree-l2"><a class="reference internal" href="pwa.html">PWA</a></li>
<li class="toctree-l2"><a class="reference internal" href="runtime.html">Runtime</a></li>
<li class="toctree-l2"><a class="reference internal" href="schema.html">Schema</a></li>
<li class="toctree-l2"><a class="reference internal" href="security.html">Security</a></li>
<li class="toctree-l2"><a class="reference internal" href="service_worker.html">ServiceWorker</a></li>
<li class="toctree-l2"><a class="reference internal" href="storage.html">Storage</a></li>
<li class="toctree-l2"><a class="reference internal" href="system_info.html">SystemInfo</a></li>
<li class="toctree-l2"><a class="reference internal" href="target.html">Target</a></li>
<li class="toctree-l2"><a class="reference internal" href="tethering.html">Tethering</a></li>
<li class="toctree-l2"><a class="reference internal" href="tracing.html">Tracing</a></li>
<li class="toctree-l2"><a class="reference internal" href="web_audio.html">WebAudio</a></li>
<li class="toctree-l2"><a class="reference internal" href="web_authn.html">WebAuthn</a></li>
</ul>
</li>
</ul>

</div>
</div>
      </div>
      
    </div>
  </aside>
  <div class="main">
    <div class="content">
      <div class="article-container">
        <a href="#" class="back-to-top muted-link">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
            <path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8v12z"></path>
          </svg>
          <span>Back to top</span>
        </a>
        <div class="content-icon-container">
          
<div class="theme-toggle-container theme-toggle-content">
            <button class="theme-toggle">
              <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
              <svg class="theme-icon-when-auto"><use href="#svg-sun-half"></use></svg>
              <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
              <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
            </button>
          </div>
          <label class="toc-overlay-icon toc-content-icon" for="__toc">
            <div class="visually-hidden">Toggle table of contents sidebar</div>
            <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
          </label>
        </div>
        <article role="main">
          <section id="debugger">
<h1>Debugger<a class="headerlink" href="#debugger" title="Link to this heading">#</a></h1>
<p>Debugger domain exposes JavaScript debugging capabilities. It allows setting and removing
breakpoints, stepping through execution, exploring stack traces, etc.</p>
<ul class="simple" id="module-nodriver.cdp.debugger">
<li><p><a class="reference internal" href="#types">Types</a></p></li>
<li><p><a class="reference internal" href="#commands">Commands</a></p></li>
<li><p><a class="reference internal" href="#events">Events</a></p></li>
</ul>
<section id="types">
<h2>Types<a class="headerlink" href="#types" title="Link to this heading">#</a></h2>
<p>Generally, you do not need to instantiate CDP types
yourself. Instead, the API creates objects for you as return
values from commands, and then you can use those objects as
arguments to other commands.</p>
<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.BreakpointId">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">BreakpointId</span></span><a class="reference internal" href="../../_modules/nodriver/cdp/debugger.html#BreakpointId"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.debugger.BreakpointId" title="Link to this definition">#</a></dt>
<dd><p>Breakpoint identifier.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.CallFrameId">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">CallFrameId</span></span><a class="reference internal" href="../../_modules/nodriver/cdp/debugger.html#CallFrameId"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.debugger.CallFrameId" title="Link to this definition">#</a></dt>
<dd><p>Call frame identifier.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.Location">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">Location</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">script_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">line_number</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">column_number</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/debugger.html#Location"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.debugger.Location" title="Link to this definition">#</a></dt>
<dd><p>Location in the source code.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.Location.script_id">
<span class="sig-name descname"><span class="pre">script_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="runtime.html#nodriver.cdp.runtime.ScriptId" title="nodriver.cdp.runtime.ScriptId"><code class="xref py py-class docutils literal notranslate"><span class="pre">ScriptId</span></code></a></em><a class="headerlink" href="#nodriver.cdp.debugger.Location.script_id" title="Link to this definition">#</a></dt>
<dd><p>Script identifier as reported in the <code class="docutils literal notranslate"><span class="pre">Debugger.scriptParsed</span></code>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.Location.line_number">
<span class="sig-name descname"><span class="pre">line_number</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></em><a class="headerlink" href="#nodriver.cdp.debugger.Location.line_number" title="Link to this definition">#</a></dt>
<dd><p>Line number in the script (0-based).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.Location.column_number">
<span class="sig-name descname"><span class="pre">column_number</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.debugger.Location.column_number" title="Link to this definition">#</a></dt>
<dd><p>Column number in the script (0-based).</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.ScriptPosition">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">ScriptPosition</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">line_number</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">column_number</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/debugger.html#ScriptPosition"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.debugger.ScriptPosition" title="Link to this definition">#</a></dt>
<dd><p>Location in the source code.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.ScriptPosition.line_number">
<span class="sig-name descname"><span class="pre">line_number</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></em><a class="headerlink" href="#nodriver.cdp.debugger.ScriptPosition.line_number" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.ScriptPosition.column_number">
<span class="sig-name descname"><span class="pre">column_number</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></em><a class="headerlink" href="#nodriver.cdp.debugger.ScriptPosition.column_number" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.LocationRange">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">LocationRange</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">script_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">end</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/debugger.html#LocationRange"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.debugger.LocationRange" title="Link to this definition">#</a></dt>
<dd><p>Location range within one script.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.LocationRange.script_id">
<span class="sig-name descname"><span class="pre">script_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="runtime.html#nodriver.cdp.runtime.ScriptId" title="nodriver.cdp.runtime.ScriptId"><code class="xref py py-class docutils literal notranslate"><span class="pre">ScriptId</span></code></a></em><a class="headerlink" href="#nodriver.cdp.debugger.LocationRange.script_id" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.LocationRange.start">
<span class="sig-name descname"><span class="pre">start</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.debugger.ScriptPosition" title="nodriver.cdp.debugger.ScriptPosition"><code class="xref py py-class docutils literal notranslate"><span class="pre">ScriptPosition</span></code></a></em><a class="headerlink" href="#nodriver.cdp.debugger.LocationRange.start" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.LocationRange.end">
<span class="sig-name descname"><span class="pre">end</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.debugger.ScriptPosition" title="nodriver.cdp.debugger.ScriptPosition"><code class="xref py py-class docutils literal notranslate"><span class="pre">ScriptPosition</span></code></a></em><a class="headerlink" href="#nodriver.cdp.debugger.LocationRange.end" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.CallFrame">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">CallFrame</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">call_frame_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">function_name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">location</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">url</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">scope_chain</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">this</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">function_location</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">return_value</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">can_be_restarted</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/debugger.html#CallFrame"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.debugger.CallFrame" title="Link to this definition">#</a></dt>
<dd><p>JavaScript call frame. Array of call frames form the call stack.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.CallFrame.call_frame_id">
<span class="sig-name descname"><span class="pre">call_frame_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.debugger.CallFrameId" title="nodriver.cdp.debugger.CallFrameId"><code class="xref py py-class docutils literal notranslate"><span class="pre">CallFrameId</span></code></a></em><a class="headerlink" href="#nodriver.cdp.debugger.CallFrame.call_frame_id" title="Link to this definition">#</a></dt>
<dd><p>Call frame identifier. This identifier is only valid while the virtual machine is paused.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.CallFrame.function_name">
<span class="sig-name descname"><span class="pre">function_name</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.debugger.CallFrame.function_name" title="Link to this definition">#</a></dt>
<dd><p>Name of the JavaScript function called on this call frame.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.CallFrame.location">
<span class="sig-name descname"><span class="pre">location</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.debugger.Location" title="nodriver.cdp.debugger.Location"><code class="xref py py-class docutils literal notranslate"><span class="pre">Location</span></code></a></em><a class="headerlink" href="#nodriver.cdp.debugger.CallFrame.location" title="Link to this definition">#</a></dt>
<dd><p>Location in the source code.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.CallFrame.url">
<span class="sig-name descname"><span class="pre">url</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.debugger.CallFrame.url" title="Link to this definition">#</a></dt>
<dd><p>JavaScript script name or url.
Deprecated in favor of using the <code class="docutils literal notranslate"><span class="pre">location.scriptId</span></code> to resolve the URL via a previously
sent <code class="docutils literal notranslate"><span class="pre">Debugger.scriptParsed</span></code> event.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.CallFrame.scope_chain">
<span class="sig-name descname"><span class="pre">scope_chain</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.debugger.Scope" title="nodriver.cdp.debugger.Scope"><code class="xref py py-class docutils literal notranslate"><span class="pre">Scope</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.debugger.CallFrame.scope_chain" title="Link to this definition">#</a></dt>
<dd><p>Scope chain for this call frame.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.CallFrame.this">
<span class="sig-name descname"><span class="pre">this</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="runtime.html#nodriver.cdp.runtime.RemoteObject" title="nodriver.cdp.runtime.RemoteObject"><code class="xref py py-class docutils literal notranslate"><span class="pre">RemoteObject</span></code></a></em><a class="headerlink" href="#nodriver.cdp.debugger.CallFrame.this" title="Link to this definition">#</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">this</span></code> object for this call frame.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.CallFrame.function_location">
<span class="sig-name descname"><span class="pre">function_location</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.debugger.Location" title="nodriver.cdp.debugger.Location"><code class="xref py py-class docutils literal notranslate"><span class="pre">Location</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.debugger.CallFrame.function_location" title="Link to this definition">#</a></dt>
<dd><p>Location in the source code.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.CallFrame.return_value">
<span class="sig-name descname"><span class="pre">return_value</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="runtime.html#nodriver.cdp.runtime.RemoteObject" title="nodriver.cdp.runtime.RemoteObject"><code class="xref py py-class docutils literal notranslate"><span class="pre">RemoteObject</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.debugger.CallFrame.return_value" title="Link to this definition">#</a></dt>
<dd><p>The value being returned, if the function is at return point.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.CallFrame.can_be_restarted">
<span class="sig-name descname"><span class="pre">can_be_restarted</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.debugger.CallFrame.can_be_restarted" title="Link to this definition">#</a></dt>
<dd><p>Valid only while the VM is paused and indicates whether this frame
can be restarted or not. Note that a <code class="docutils literal notranslate"><span class="pre">true</span></code> value here does not
guarantee that Debugger#restartFrame with this CallFrameId will be
successful, but it is very likely.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.Scope">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">Scope</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">type_</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">object_</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">name</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start_location</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">end_location</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/debugger.html#Scope"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.debugger.Scope" title="Link to this definition">#</a></dt>
<dd><p>Scope description.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.Scope.type_">
<span class="sig-name descname"><span class="pre">type_</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.debugger.Scope.type_" title="Link to this definition">#</a></dt>
<dd><p>Scope type.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.Scope.object_">
<span class="sig-name descname"><span class="pre">object_</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="runtime.html#nodriver.cdp.runtime.RemoteObject" title="nodriver.cdp.runtime.RemoteObject"><code class="xref py py-class docutils literal notranslate"><span class="pre">RemoteObject</span></code></a></em><a class="headerlink" href="#nodriver.cdp.debugger.Scope.object_" title="Link to this definition">#</a></dt>
<dd><p>Object representing the scope. For <code class="docutils literal notranslate"><span class="pre">global</span></code> and <code class="docutils literal notranslate"><span class="pre">with</span></code> scopes it represents the actual
object; for the rest of the scopes, it is artificial transient object enumerating scope
variables as its properties.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.Scope.name">
<span class="sig-name descname"><span class="pre">name</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.debugger.Scope.name" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.Scope.start_location">
<span class="sig-name descname"><span class="pre">start_location</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.debugger.Location" title="nodriver.cdp.debugger.Location"><code class="xref py py-class docutils literal notranslate"><span class="pre">Location</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.debugger.Scope.start_location" title="Link to this definition">#</a></dt>
<dd><p>Location in the source code where scope starts</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.Scope.end_location">
<span class="sig-name descname"><span class="pre">end_location</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.debugger.Location" title="nodriver.cdp.debugger.Location"><code class="xref py py-class docutils literal notranslate"><span class="pre">Location</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.debugger.Scope.end_location" title="Link to this definition">#</a></dt>
<dd><p>Location in the source code where scope ends</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.SearchMatch">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">SearchMatch</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">line_number</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">line_content</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/debugger.html#SearchMatch"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.debugger.SearchMatch" title="Link to this definition">#</a></dt>
<dd><p>Search match for resource.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.SearchMatch.line_number">
<span class="sig-name descname"><span class="pre">line_number</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a></em><a class="headerlink" href="#nodriver.cdp.debugger.SearchMatch.line_number" title="Link to this definition">#</a></dt>
<dd><p>Line number in resource content.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.SearchMatch.line_content">
<span class="sig-name descname"><span class="pre">line_content</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.debugger.SearchMatch.line_content" title="Link to this definition">#</a></dt>
<dd><p>Line with match content.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.BreakLocation">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">BreakLocation</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">script_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">line_number</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">column_number</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type_</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/debugger.html#BreakLocation"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.debugger.BreakLocation" title="Link to this definition">#</a></dt>
<dd><dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.BreakLocation.script_id">
<span class="sig-name descname"><span class="pre">script_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="runtime.html#nodriver.cdp.runtime.ScriptId" title="nodriver.cdp.runtime.ScriptId"><code class="xref py py-class docutils literal notranslate"><span class="pre">ScriptId</span></code></a></em><a class="headerlink" href="#nodriver.cdp.debugger.BreakLocation.script_id" title="Link to this definition">#</a></dt>
<dd><p>Script identifier as reported in the <code class="docutils literal notranslate"><span class="pre">Debugger.scriptParsed</span></code>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.BreakLocation.line_number">
<span class="sig-name descname"><span class="pre">line_number</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></em><a class="headerlink" href="#nodriver.cdp.debugger.BreakLocation.line_number" title="Link to this definition">#</a></dt>
<dd><p>Line number in the script (0-based).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.BreakLocation.column_number">
<span class="sig-name descname"><span class="pre">column_number</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.debugger.BreakLocation.column_number" title="Link to this definition">#</a></dt>
<dd><p>Column number in the script (0-based).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.BreakLocation.type_">
<span class="sig-name descname"><span class="pre">type_</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.debugger.BreakLocation.type_" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.WasmDisassemblyChunk">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">WasmDisassemblyChunk</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">lines</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">bytecode_offsets</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/debugger.html#WasmDisassemblyChunk"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.debugger.WasmDisassemblyChunk" title="Link to this definition">#</a></dt>
<dd><dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.WasmDisassemblyChunk.lines">
<span class="sig-name descname"><span class="pre">lines</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.debugger.WasmDisassemblyChunk.lines" title="Link to this definition">#</a></dt>
<dd><p>The next chunk of disassembled lines.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.WasmDisassemblyChunk.bytecode_offsets">
<span class="sig-name descname"><span class="pre">bytecode_offsets</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.debugger.WasmDisassemblyChunk.bytecode_offsets" title="Link to this definition">#</a></dt>
<dd><p>The bytecode offsets describing the start of each line.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.ScriptLanguage">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">ScriptLanguage</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">names</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">module</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">qualname</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">boundary</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/debugger.html#ScriptLanguage"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.debugger.ScriptLanguage" title="Link to this definition">#</a></dt>
<dd><p>Enum of possible script languages.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.ScriptLanguage.JAVA_SCRIPT">
<span class="sig-name descname"><span class="pre">JAVA_SCRIPT</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'JavaScript'</span></em><a class="headerlink" href="#nodriver.cdp.debugger.ScriptLanguage.JAVA_SCRIPT" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.ScriptLanguage.WEB_ASSEMBLY">
<span class="sig-name descname"><span class="pre">WEB_ASSEMBLY</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'WebAssembly'</span></em><a class="headerlink" href="#nodriver.cdp.debugger.ScriptLanguage.WEB_ASSEMBLY" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.DebugSymbols">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">DebugSymbols</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">type_</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">external_url</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/debugger.html#DebugSymbols"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.debugger.DebugSymbols" title="Link to this definition">#</a></dt>
<dd><p>Debug symbols available for a wasm script.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.DebugSymbols.type_">
<span class="sig-name descname"><span class="pre">type_</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.debugger.DebugSymbols.type_" title="Link to this definition">#</a></dt>
<dd><p>Type of the debug symbols.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.DebugSymbols.external_url">
<span class="sig-name descname"><span class="pre">external_url</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#nodriver.cdp.debugger.DebugSymbols.external_url" title="Link to this definition">#</a></dt>
<dd><p>URL of the external symbol source.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.ResolvedBreakpoint">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">ResolvedBreakpoint</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">breakpoint_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">location</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/debugger.html#ResolvedBreakpoint"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.debugger.ResolvedBreakpoint" title="Link to this definition">#</a></dt>
<dd><dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.ResolvedBreakpoint.breakpoint_id">
<span class="sig-name descname"><span class="pre">breakpoint_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.debugger.BreakpointId" title="nodriver.cdp.debugger.BreakpointId"><code class="xref py py-class docutils literal notranslate"><span class="pre">BreakpointId</span></code></a></em><a class="headerlink" href="#nodriver.cdp.debugger.ResolvedBreakpoint.breakpoint_id" title="Link to this definition">#</a></dt>
<dd><p>Breakpoint unique identifier.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.ResolvedBreakpoint.location">
<span class="sig-name descname"><span class="pre">location</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.debugger.Location" title="nodriver.cdp.debugger.Location"><code class="xref py py-class docutils literal notranslate"><span class="pre">Location</span></code></a></em><a class="headerlink" href="#nodriver.cdp.debugger.ResolvedBreakpoint.location" title="Link to this definition">#</a></dt>
<dd><p>Actual breakpoint location.</p>
</dd></dl>

</dd></dl>

</section>
<section id="commands">
<h2>Commands<a class="headerlink" href="#commands" title="Link to this heading">#</a></h2>
<p>Each command is a generator function. The return
type <code class="docutils literal notranslate"><span class="pre">Generator[x,</span> <span class="pre">y,</span> <span class="pre">z]</span></code> indicates that the generator
<em>yields</em> arguments of type <code class="docutils literal notranslate"><span class="pre">x</span></code>, it must be resumed with
an argument of type <code class="docutils literal notranslate"><span class="pre">y</span></code>, and it returns type <code class="docutils literal notranslate"><span class="pre">z</span></code>. In
this library, types <code class="docutils literal notranslate"><span class="pre">x</span></code> and <code class="docutils literal notranslate"><span class="pre">y</span></code> are the same for all
commands, and <code class="docutils literal notranslate"><span class="pre">z</span></code> is the return type you should pay attention
to. For more information, see
<a class="reference internal" href="../../readme.html#getting-started-commands"><span class="std std-ref">Getting Started: Commands</span></a>.</p>
<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.continue_to_location">
<span class="sig-name descname"><span class="pre">continue_to_location</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">location</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">target_call_frames</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/debugger.html#continue_to_location"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.debugger.continue_to_location" title="Link to this definition">#</a></dt>
<dd><p>Continues execution until specific location is reached.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>location</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.debugger.Location" title="nodriver.cdp.debugger.Location"><code class="xref py py-class docutils literal notranslate"><span class="pre">Location</span></code></a></span>) – Location to continue to.</p></li>
<li><p><strong>target_call_frames</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]</span>) – <em>(Optional)</em></p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.disable">
<span class="sig-name descname"><span class="pre">disable</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/debugger.html#disable"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.debugger.disable" title="Link to this definition">#</a></dt>
<dd><p>Disables debugger for given page.</p>
<dl class="field-list simple">
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.disassemble_wasm_module">
<span class="sig-name descname"><span class="pre">disassemble_wasm_module</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">script_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/debugger.html#disassemble_wasm_module"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.debugger.disassemble_wasm_module" title="Link to this definition">#</a></dt>
<dd><p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>script_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="runtime.html#nodriver.cdp.runtime.ScriptId" title="nodriver.cdp.runtime.ScriptId"><code class="xref py py-class docutils literal notranslate"><span class="pre">ScriptId</span></code></a></span>) – Id of the script to disassemble</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Tuple" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Tuple</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a>], <a class="reference internal" href="#nodriver.cdp.debugger.WasmDisassemblyChunk" title="nodriver.cdp.debugger.WasmDisassemblyChunk"><code class="xref py py-class docutils literal notranslate"><span class="pre">WasmDisassemblyChunk</span></code></a>]]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p><p>A tuple with the following items:</p>
<ol class="arabic simple" start="0">
<li><p><strong>streamId</strong> - <em>(Optional)</em> For large modules, return a stream from which additional chunks of disassembly can be read successively.</p></li>
<li><p><strong>totalNumberOfLines</strong> - The total number of lines in the disassembly text.</p></li>
<li><p><strong>functionBodyOffsets</strong> - The offsets of all function bodies, in the format [start1, end1, start2, end2, …] where all ends are exclusive.</p></li>
<li><p><strong>chunk</strong> - The first chunk of disassembly.</p></li>
</ol>
</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.enable">
<span class="sig-name descname"><span class="pre">enable</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">max_scripts_cache_size</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/debugger.html#enable"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.debugger.enable" title="Link to this definition">#</a></dt>
<dd><p>Enables debugger for the given page. Clients should not assume that the debugging has been
enabled until the result for this command is received.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>max_scripts_cache_size</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a>]</span>) – <strong>(EXPERIMENTAL)</strong> <em>(Optional)</em> The maximum size in bytes of collected scripts (not referenced by other heap objects) the debugger can hold. Puts no limit if parameter is omitted.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference internal" href="runtime.html#nodriver.cdp.runtime.UniqueDebuggerId" title="nodriver.cdp.runtime.UniqueDebuggerId"><code class="xref py py-class docutils literal notranslate"><span class="pre">UniqueDebuggerId</span></code></a>]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>Unique identifier of the debugger.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.evaluate_on_call_frame">
<span class="sig-name descname"><span class="pre">evaluate_on_call_frame</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">call_frame_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">expression</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">object_group</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">include_command_line_api</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">silent</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">return_by_value</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">generate_preview</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">throw_on_side_effect</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">timeout</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/debugger.html#evaluate_on_call_frame"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.debugger.evaluate_on_call_frame" title="Link to this definition">#</a></dt>
<dd><p>Evaluates expression on a given call frame.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>call_frame_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.debugger.CallFrameId" title="nodriver.cdp.debugger.CallFrameId"><code class="xref py py-class docutils literal notranslate"><span class="pre">CallFrameId</span></code></a></span>) – Call frame identifier to evaluate on.</p></li>
<li><p><strong>expression</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – Expression to evaluate.</p></li>
<li><p><strong>object_group</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]</span>) – <em>(Optional)</em> String object group name to put result into (allows rapid releasing resulting object handles using <code class="docutils literal notranslate"><span class="pre">`releaseObjectGroup``</span></code>).</p></li>
<li><p><strong>include_command_line_api</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <em>(Optional)</em> Specifies whether command line API should be available to the evaluated expression, defaults to false.</p></li>
<li><p><strong>silent</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <em>(Optional)</em> In silent mode exceptions thrown during evaluation are not reported and do not pause execution. Overrides <code class="docutils literal notranslate"><span class="pre">``setPauseOnException`</span></code> state.</p></li>
<li><p><strong>return_by_value</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <em>(Optional)</em> Whether the result is expected to be a JSON object that should be sent by value.</p></li>
<li><p><strong>generate_preview</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <strong>(EXPERIMENTAL)</strong> <em>(Optional)</em> Whether preview should be generated for the result.</p></li>
<li><p><strong>throw_on_side_effect</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <em>(Optional)</em> Whether to throw an exception if side effect cannot be ruled out during evaluation.</p></li>
<li><p><strong>timeout</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="runtime.html#nodriver.cdp.runtime.TimeDelta" title="nodriver.cdp.runtime.TimeDelta"><code class="xref py py-class docutils literal notranslate"><span class="pre">TimeDelta</span></code></a>]</span>) – <strong>(EXPERIMENTAL)</strong> <em>(Optional)</em> Terminate execution after timing out (number of milliseconds).</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Tuple" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Tuple</span></code></a>[<a class="reference internal" href="runtime.html#nodriver.cdp.runtime.RemoteObject" title="nodriver.cdp.runtime.RemoteObject"><code class="xref py py-class docutils literal notranslate"><span class="pre">RemoteObject</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="runtime.html#nodriver.cdp.runtime.ExceptionDetails" title="nodriver.cdp.runtime.ExceptionDetails"><code class="xref py py-class docutils literal notranslate"><span class="pre">ExceptionDetails</span></code></a>]]]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p><p>A tuple with the following items:</p>
<ol class="arabic simple" start="0">
<li><p><strong>result</strong> - Object wrapper for the evaluation result.</p></li>
<li><p><strong>exceptionDetails</strong> - <em>(Optional)</em> Exception details.</p></li>
</ol>
</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.get_possible_breakpoints">
<span class="sig-name descname"><span class="pre">get_possible_breakpoints</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">start</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">end</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">restrict_to_function</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/debugger.html#get_possible_breakpoints"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.debugger.get_possible_breakpoints" title="Link to this definition">#</a></dt>
<dd><p>Returns possible locations for breakpoint. scriptId in start and end range locations should be
the same.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>start</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.debugger.Location" title="nodriver.cdp.debugger.Location"><code class="xref py py-class docutils literal notranslate"><span class="pre">Location</span></code></a></span>) – Start of range to search possible breakpoint locations in.</p></li>
<li><p><strong>end</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="#nodriver.cdp.debugger.Location" title="nodriver.cdp.debugger.Location"><code class="xref py py-class docutils literal notranslate"><span class="pre">Location</span></code></a>]</span>) – <em>(Optional)</em> End of range to search possible breakpoint locations in (excluding). When not specified, end of scripts is used as end of range.</p></li>
<li><p><strong>restrict_to_function</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <em>(Optional)</em> Only consider locations which are in the same (non-nested) function as start.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.debugger.BreakLocation" title="nodriver.cdp.debugger.BreakLocation"><code class="xref py py-class docutils literal notranslate"><span class="pre">BreakLocation</span></code></a>]]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>List of the possible breakpoint locations.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.get_script_source">
<span class="sig-name descname"><span class="pre">get_script_source</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">script_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/debugger.html#get_script_source"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.debugger.get_script_source" title="Link to this definition">#</a></dt>
<dd><p>Returns source for the script with given id.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>script_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="runtime.html#nodriver.cdp.runtime.ScriptId" title="nodriver.cdp.runtime.ScriptId"><code class="xref py py-class docutils literal notranslate"><span class="pre">ScriptId</span></code></a></span>) – Id of the script to get source for.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Tuple" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Tuple</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]]]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p><p>A tuple with the following items:</p>
<ol class="arabic simple" start="0">
<li><p><strong>scriptSource</strong> - Script source (empty in case of Wasm bytecode).</p></li>
<li><p><strong>bytecode</strong> - <em>(Optional)</em> Wasm bytecode. (Encoded as a base64 string when passed over JSON)</p></li>
</ol>
</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.get_stack_trace">
<span class="sig-name descname"><span class="pre">get_stack_trace</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">stack_trace_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/debugger.html#get_stack_trace"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.debugger.get_stack_trace" title="Link to this definition">#</a></dt>
<dd><p>Returns stack trace with given <code class="docutils literal notranslate"><span class="pre">stackTraceId</span></code>.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>stack_trace_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="runtime.html#nodriver.cdp.runtime.StackTraceId" title="nodriver.cdp.runtime.StackTraceId"><code class="xref py py-class docutils literal notranslate"><span class="pre">StackTraceId</span></code></a></span>) – </p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference internal" href="runtime.html#nodriver.cdp.runtime.StackTrace" title="nodriver.cdp.runtime.StackTrace"><code class="xref py py-class docutils literal notranslate"><span class="pre">StackTrace</span></code></a>]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.get_wasm_bytecode">
<span class="sig-name descname"><span class="pre">get_wasm_bytecode</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">script_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/debugger.html#get_wasm_bytecode"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.debugger.get_wasm_bytecode" title="Link to this definition">#</a></dt>
<dd><p>This command is deprecated. Use getScriptSource instead.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 1.3.</span></p>
</div>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>script_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="runtime.html#nodriver.cdp.runtime.ScriptId" title="nodriver.cdp.runtime.ScriptId"><code class="xref py py-class docutils literal notranslate"><span class="pre">ScriptId</span></code></a></span>) – Id of the Wasm script to get source for.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>Script source. (Encoded as a base64 string when passed over JSON)</p>
</dd>
</dl>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 1.3.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.next_wasm_disassembly_chunk">
<span class="sig-name descname"><span class="pre">next_wasm_disassembly_chunk</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">stream_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/debugger.html#next_wasm_disassembly_chunk"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.debugger.next_wasm_disassembly_chunk" title="Link to this definition">#</a></dt>
<dd><p>Disassemble the next chunk of lines for the module corresponding to the
stream. If disassembly is complete, this API will invalidate the streamId
and return an empty chunk. Any subsequent calls for the now invalid stream
will return errors.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>stream_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – </p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference internal" href="#nodriver.cdp.debugger.WasmDisassemblyChunk" title="nodriver.cdp.debugger.WasmDisassemblyChunk"><code class="xref py py-class docutils literal notranslate"><span class="pre">WasmDisassemblyChunk</span></code></a>]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>The next chunk of disassembly.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.pause">
<span class="sig-name descname"><span class="pre">pause</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/debugger.html#pause"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.debugger.pause" title="Link to this definition">#</a></dt>
<dd><p>Stops on the next JavaScript statement.</p>
<dl class="field-list simple">
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.pause_on_async_call">
<span class="sig-name descname"><span class="pre">pause_on_async_call</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">parent_stack_trace_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/debugger.html#pause_on_async_call"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.debugger.pause_on_async_call" title="Link to this definition">#</a></dt>
<dd><div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 1.3.</span></p>
</div>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>parent_stack_trace_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="runtime.html#nodriver.cdp.runtime.StackTraceId" title="nodriver.cdp.runtime.StackTraceId"><code class="xref py py-class docutils literal notranslate"><span class="pre">StackTraceId</span></code></a></span>) – Debugger will pause when async call with given stack trace is started.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 1.3.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.remove_breakpoint">
<span class="sig-name descname"><span class="pre">remove_breakpoint</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">breakpoint_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/debugger.html#remove_breakpoint"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.debugger.remove_breakpoint" title="Link to this definition">#</a></dt>
<dd><p>Removes JavaScript breakpoint.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>breakpoint_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.debugger.BreakpointId" title="nodriver.cdp.debugger.BreakpointId"><code class="xref py py-class docutils literal notranslate"><span class="pre">BreakpointId</span></code></a></span>) – </p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.restart_frame">
<span class="sig-name descname"><span class="pre">restart_frame</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">call_frame_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">mode</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/debugger.html#restart_frame"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.debugger.restart_frame" title="Link to this definition">#</a></dt>
<dd><p>Restarts particular call frame from the beginning. The old, deprecated
behavior of <code class="docutils literal notranslate"><span class="pre">restartFrame</span></code> is to stay paused and allow further CDP commands
after a restart was scheduled. This can cause problems with restarting, so
we now continue execution immediatly after it has been scheduled until we
reach the beginning of the restarted frame.</p>
<p>To stay back-wards compatible, <code class="docutils literal notranslate"><span class="pre">restartFrame</span></code> now expects a <code class="docutils literal notranslate"><span class="pre">mode</span></code>
parameter to be present. If the <code class="docutils literal notranslate"><span class="pre">mode</span></code> parameter is missing, <code class="docutils literal notranslate"><span class="pre">restartFrame</span></code>
errors out.</p>
<p>The various return values are deprecated and <code class="docutils literal notranslate"><span class="pre">callFrames</span></code> is always empty.
Use the call frames from the <code class="docutils literal notranslate"><span class="pre">Debugger#paused</span></code> events instead, that fires
once V8 pauses at the beginning of the restarted function.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>call_frame_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.debugger.CallFrameId" title="nodriver.cdp.debugger.CallFrameId"><code class="xref py py-class docutils literal notranslate"><span class="pre">CallFrameId</span></code></a></span>) – Call frame identifier to evaluate on.</p></li>
<li><p><strong>mode</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]</span>) – <strong>(EXPERIMENTAL)</strong> <em>(Optional)</em> The <code class="docutils literal notranslate"><span class="pre">`mode``</span></code> parameter must be present and set to ‘StepInto’, otherwise <code class="docutils literal notranslate"><span class="pre">``restartFrame`</span></code> will error out.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Tuple" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Tuple</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.debugger.CallFrame" title="nodriver.cdp.debugger.CallFrame"><code class="xref py py-class docutils literal notranslate"><span class="pre">CallFrame</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="runtime.html#nodriver.cdp.runtime.StackTrace" title="nodriver.cdp.runtime.StackTrace"><code class="xref py py-class docutils literal notranslate"><span class="pre">StackTrace</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="runtime.html#nodriver.cdp.runtime.StackTraceId" title="nodriver.cdp.runtime.StackTraceId"><code class="xref py py-class docutils literal notranslate"><span class="pre">StackTraceId</span></code></a>]]]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p><p>A tuple with the following items:</p>
<ol class="arabic simple" start="0">
<li><p><strong>callFrames</strong> - New stack trace.</p></li>
<li><p><strong>asyncStackTrace</strong> - <em>(Optional)</em> Async stack trace, if any.</p></li>
<li><p><strong>asyncStackTraceId</strong> - <em>(Optional)</em> Async stack trace, if any.</p></li>
</ol>
</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.resume">
<span class="sig-name descname"><span class="pre">resume</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">terminate_on_resume</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/debugger.html#resume"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.debugger.resume" title="Link to this definition">#</a></dt>
<dd><p>Resumes JavaScript execution.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>terminate_on_resume</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <em>(Optional)</em> Set to true to terminate execution upon resuming execution. In contrast to Runtime.terminateExecution, this will allows to execute further JavaScript (i.e. via evaluation) until execution of the paused code is actually resumed, at which point termination is triggered. If execution is currently not paused, this parameter has no effect.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.search_in_content">
<span class="sig-name descname"><span class="pre">search_in_content</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">script_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">query</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">case_sensitive</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">is_regex</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/debugger.html#search_in_content"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.debugger.search_in_content" title="Link to this definition">#</a></dt>
<dd><p>Searches for given string in script content.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>script_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="runtime.html#nodriver.cdp.runtime.ScriptId" title="nodriver.cdp.runtime.ScriptId"><code class="xref py py-class docutils literal notranslate"><span class="pre">ScriptId</span></code></a></span>) – Id of the script to search in.</p></li>
<li><p><strong>query</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – String to search for.</p></li>
<li><p><strong>case_sensitive</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <em>(Optional)</em> If true, search is case sensitive.</p></li>
<li><p><strong>is_regex</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <em>(Optional)</em> If true, treats string parameter as regex.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.debugger.SearchMatch" title="nodriver.cdp.debugger.SearchMatch"><code class="xref py py-class docutils literal notranslate"><span class="pre">SearchMatch</span></code></a>]]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>List of search matches.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.set_async_call_stack_depth">
<span class="sig-name descname"><span class="pre">set_async_call_stack_depth</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">max_depth</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/debugger.html#set_async_call_stack_depth"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.debugger.set_async_call_stack_depth" title="Link to this definition">#</a></dt>
<dd><p>Enables or disables async call stacks tracking.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>max_depth</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></span>) – Maximum depth of async call stacks. Setting to <code class="docutils literal notranslate"><span class="pre">`0`</span></code> will effectively disable collecting async call stacks (default).</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.set_blackbox_execution_contexts">
<span class="sig-name descname"><span class="pre">set_blackbox_execution_contexts</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">unique_ids</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/debugger.html#set_blackbox_execution_contexts"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.debugger.set_blackbox_execution_contexts" title="Link to this definition">#</a></dt>
<dd><p>Replace previous blackbox execution contexts with passed ones. Forces backend to skip
stepping/pausing in scripts in these execution contexts. VM will try to leave blackboxed script by
performing ‘step in’ several times, finally resorting to ‘step out’ if unsuccessful.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>unique_ids</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]</span>) – Array of execution context unique ids for the debugger to ignore.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.set_blackbox_patterns">
<span class="sig-name descname"><span class="pre">set_blackbox_patterns</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">patterns</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">skip_anonymous</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/debugger.html#set_blackbox_patterns"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.debugger.set_blackbox_patterns" title="Link to this definition">#</a></dt>
<dd><p>Replace previous blackbox patterns with passed ones. Forces backend to skip stepping/pausing in
scripts with url matching one of the patterns. VM will try to leave blackboxed script by
performing ‘step in’ several times, finally resorting to ‘step out’ if unsuccessful.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>patterns</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]</span>) – Array of regexps that will be used to check script url for blackbox state.</p></li>
<li><p><strong>skip_anonymous</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <em>(Optional)</em> If true, also ignore scripts with no source url.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.set_blackboxed_ranges">
<span class="sig-name descname"><span class="pre">set_blackboxed_ranges</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">script_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">positions</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/debugger.html#set_blackboxed_ranges"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.debugger.set_blackboxed_ranges" title="Link to this definition">#</a></dt>
<dd><p>Makes backend skip steps in the script in blackboxed ranges. VM will try leave blacklisted
scripts by performing ‘step in’ several times, finally resorting to ‘step out’ if unsuccessful.
Positions array contains positions where blackbox state is changed. First interval isn’t
blackboxed. Array should be sorted.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>script_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="runtime.html#nodriver.cdp.runtime.ScriptId" title="nodriver.cdp.runtime.ScriptId"><code class="xref py py-class docutils literal notranslate"><span class="pre">ScriptId</span></code></a></span>) – Id of the script.</p></li>
<li><p><strong>positions</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.debugger.ScriptPosition" title="nodriver.cdp.debugger.ScriptPosition"><code class="xref py py-class docutils literal notranslate"><span class="pre">ScriptPosition</span></code></a>]</span>) – </p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.set_breakpoint">
<span class="sig-name descname"><span class="pre">set_breakpoint</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">location</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">condition</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/debugger.html#set_breakpoint"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.debugger.set_breakpoint" title="Link to this definition">#</a></dt>
<dd><p>Sets JavaScript breakpoint at a given location.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>location</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.debugger.Location" title="nodriver.cdp.debugger.Location"><code class="xref py py-class docutils literal notranslate"><span class="pre">Location</span></code></a></span>) – Location to set breakpoint in.</p></li>
<li><p><strong>condition</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]</span>) – <em>(Optional)</em> Expression to use as a breakpoint condition. When specified, debugger will only stop on the breakpoint if this expression evaluates to true.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Tuple" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Tuple</span></code></a>[<a class="reference internal" href="#nodriver.cdp.debugger.BreakpointId" title="nodriver.cdp.debugger.BreakpointId"><code class="xref py py-class docutils literal notranslate"><span class="pre">BreakpointId</span></code></a>, <a class="reference internal" href="#nodriver.cdp.debugger.Location" title="nodriver.cdp.debugger.Location"><code class="xref py py-class docutils literal notranslate"><span class="pre">Location</span></code></a>]]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p><p>A tuple with the following items:</p>
<ol class="arabic simple" start="0">
<li><p><strong>breakpointId</strong> - Id of the created breakpoint for further reference.</p></li>
<li><p><strong>actualLocation</strong> - Location this breakpoint resolved into.</p></li>
</ol>
</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.set_breakpoint_by_url">
<span class="sig-name descname"><span class="pre">set_breakpoint_by_url</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">line_number</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">url</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">url_regex</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">script_hash</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">column_number</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">condition</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/debugger.html#set_breakpoint_by_url"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.debugger.set_breakpoint_by_url" title="Link to this definition">#</a></dt>
<dd><p>Sets JavaScript breakpoint at given location specified either by URL or URL regex. Once this
command is issued, all existing parsed scripts will have breakpoints resolved and returned in
<code class="docutils literal notranslate"><span class="pre">locations</span></code> property. Further matching script parsing will result in subsequent
<code class="docutils literal notranslate"><span class="pre">breakpointResolved</span></code> events issued. This logical breakpoint will survive page reloads.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>line_number</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></span>) – Line number to set breakpoint at.</p></li>
<li><p><strong>url</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]</span>) – <em>(Optional)</em> URL of the resources to set breakpoint on.</p></li>
<li><p><strong>url_regex</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]</span>) – <em>(Optional)</em> Regex pattern for the URLs of the resources to set breakpoints on. Either <code class="docutils literal notranslate"><span class="pre">`url``</span></code> or <code class="docutils literal notranslate"><span class="pre">``urlRegex`</span></code> must be specified.</p></li>
<li><p><strong>script_hash</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]</span>) – <em>(Optional)</em> Script hash of the resources to set breakpoint on.</p></li>
<li><p><strong>column_number</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a>]</span>) – <em>(Optional)</em> Offset in the line to set breakpoint at.</p></li>
<li><p><strong>condition</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]</span>) – <em>(Optional)</em> Expression to use as a breakpoint condition. When specified, debugger will only stop on the breakpoint if this expression evaluates to true.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Tuple" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Tuple</span></code></a>[<a class="reference internal" href="#nodriver.cdp.debugger.BreakpointId" title="nodriver.cdp.debugger.BreakpointId"><code class="xref py py-class docutils literal notranslate"><span class="pre">BreakpointId</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.debugger.Location" title="nodriver.cdp.debugger.Location"><code class="xref py py-class docutils literal notranslate"><span class="pre">Location</span></code></a>]]]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p><p>A tuple with the following items:</p>
<ol class="arabic simple" start="0">
<li><p><strong>breakpointId</strong> - Id of the created breakpoint for further reference.</p></li>
<li><p><strong>locations</strong> - List of the locations this breakpoint resolved into upon addition.</p></li>
</ol>
</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.set_breakpoint_on_function_call">
<span class="sig-name descname"><span class="pre">set_breakpoint_on_function_call</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">condition</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/debugger.html#set_breakpoint_on_function_call"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.debugger.set_breakpoint_on_function_call" title="Link to this definition">#</a></dt>
<dd><p>Sets JavaScript breakpoint before each call to the given function.
If another function was created from the same source as a given one,
calling it will also trigger the breakpoint.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>object_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="runtime.html#nodriver.cdp.runtime.RemoteObjectId" title="nodriver.cdp.runtime.RemoteObjectId"><code class="xref py py-class docutils literal notranslate"><span class="pre">RemoteObjectId</span></code></a></span>) – Function object id.</p></li>
<li><p><strong>condition</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>]</span>) – <em>(Optional)</em> Expression to use as a breakpoint condition. When specified, debugger will stop on the breakpoint if this expression evaluates to true.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference internal" href="#nodriver.cdp.debugger.BreakpointId" title="nodriver.cdp.debugger.BreakpointId"><code class="xref py py-class docutils literal notranslate"><span class="pre">BreakpointId</span></code></a>]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>Id of the created breakpoint for further reference.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.set_breakpoints_active">
<span class="sig-name descname"><span class="pre">set_breakpoints_active</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">active</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/debugger.html#set_breakpoints_active"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.debugger.set_breakpoints_active" title="Link to this definition">#</a></dt>
<dd><p>Activates / deactivates all breakpoints on the page.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>active</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a></span>) – New value for breakpoints active state.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.set_instrumentation_breakpoint">
<span class="sig-name descname"><span class="pre">set_instrumentation_breakpoint</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">instrumentation</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/debugger.html#set_instrumentation_breakpoint"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.debugger.set_instrumentation_breakpoint" title="Link to this definition">#</a></dt>
<dd><p>Sets instrumentation breakpoint.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>instrumentation</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – Instrumentation name.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference internal" href="#nodriver.cdp.debugger.BreakpointId" title="nodriver.cdp.debugger.BreakpointId"><code class="xref py py-class docutils literal notranslate"><span class="pre">BreakpointId</span></code></a>]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>Id of the created breakpoint for further reference.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.set_pause_on_exceptions">
<span class="sig-name descname"><span class="pre">set_pause_on_exceptions</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">state</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/debugger.html#set_pause_on_exceptions"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.debugger.set_pause_on_exceptions" title="Link to this definition">#</a></dt>
<dd><p>Defines pause on exceptions state. Can be set to stop on all exceptions, uncaught exceptions,
or caught exceptions, no exceptions. Initial pause on exceptions state is <code class="docutils literal notranslate"><span class="pre">none</span></code>.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>state</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – Pause on exceptions mode.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.set_return_value">
<span class="sig-name descname"><span class="pre">set_return_value</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">new_value</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/debugger.html#set_return_value"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.debugger.set_return_value" title="Link to this definition">#</a></dt>
<dd><p>Changes return value in top frame. Available only at return break position.</p>
<p><strong>EXPERIMENTAL</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>new_value</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="runtime.html#nodriver.cdp.runtime.CallArgument" title="nodriver.cdp.runtime.CallArgument"><code class="xref py py-class docutils literal notranslate"><span class="pre">CallArgument</span></code></a></span>) – New return value.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.set_script_source">
<span class="sig-name descname"><span class="pre">set_script_source</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">script_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">script_source</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">dry_run</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">allow_top_frame_editing</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/debugger.html#set_script_source"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.debugger.set_script_source" title="Link to this definition">#</a></dt>
<dd><p>Edits JavaScript source live.</p>
<p>In general, functions that are currently on the stack can not be edited with
a single exception: If the edited function is the top-most stack frame and
that is the only activation of that function on the stack. In this case
the live edit will be successful and a <code class="docutils literal notranslate"><span class="pre">Debugger.restartFrame</span></code> for the
top-most function is automatically triggered.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>script_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="runtime.html#nodriver.cdp.runtime.ScriptId" title="nodriver.cdp.runtime.ScriptId"><code class="xref py py-class docutils literal notranslate"><span class="pre">ScriptId</span></code></a></span>) – Id of the script to edit.</p></li>
<li><p><strong>script_source</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – New content of the script.</p></li>
<li><p><strong>dry_run</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <em>(Optional)</em> If true the change will not actually be applied. Dry run may be used to get result description without actually modifying the code.</p></li>
<li><p><strong>allow_top_frame_editing</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <strong>(EXPERIMENTAL)</strong> <em>(Optional)</em> If true, then <code class="docutils literal notranslate"><span class="pre">`scriptSource``</span></code> is allowed to change the function on top of the stack as long as the top-most stack frame is the only activation of that function.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Tuple" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Tuple</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.debugger.CallFrame" title="nodriver.cdp.debugger.CallFrame"><code class="xref py py-class docutils literal notranslate"><span class="pre">CallFrame</span></code></a>]], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="runtime.html#nodriver.cdp.runtime.StackTrace" title="nodriver.cdp.runtime.StackTrace"><code class="xref py py-class docutils literal notranslate"><span class="pre">StackTrace</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="runtime.html#nodriver.cdp.runtime.StackTraceId" title="nodriver.cdp.runtime.StackTraceId"><code class="xref py py-class docutils literal notranslate"><span class="pre">StackTraceId</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference internal" href="runtime.html#nodriver.cdp.runtime.ExceptionDetails" title="nodriver.cdp.runtime.ExceptionDetails"><code class="xref py py-class docutils literal notranslate"><span class="pre">ExceptionDetails</span></code></a>]]]</span></p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p><p>A tuple with the following items:</p>
<ol class="arabic simple" start="0">
<li><p><strong>callFrames</strong> - <em>(Optional)</em> New stack trace in case editing has happened while VM was stopped.</p></li>
<li><p><strong>stackChanged</strong> - <em>(Optional)</em> Whether current call stack  was modified after applying the changes.</p></li>
<li><p><strong>asyncStackTrace</strong> - <em>(Optional)</em> Async stack trace, if any.</p></li>
<li><p><strong>asyncStackTraceId</strong> - <em>(Optional)</em> Async stack trace, if any.</p></li>
<li><p><strong>status</strong> - Whether the operation was successful or not. Only `` Ok`` denotes a successful live edit while the other enum variants denote why the live edit failed.</p></li>
<li><p><strong>exceptionDetails</strong> - <em>(Optional)</em> Exception details if any. Only present when `` status`` is `` CompileError`.</p></li>
</ol>
</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.set_skip_all_pauses">
<span class="sig-name descname"><span class="pre">set_skip_all_pauses</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">skip</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/debugger.html#set_skip_all_pauses"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.debugger.set_skip_all_pauses" title="Link to this definition">#</a></dt>
<dd><p>Makes page not interrupt on any pauses (breakpoint, exception, dom exception etc).</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>skip</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a></span>) – New value for skip pauses state.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.set_variable_value">
<span class="sig-name descname"><span class="pre">set_variable_value</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">scope_number</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">variable_name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">new_value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">call_frame_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/debugger.html#set_variable_value"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.debugger.set_variable_value" title="Link to this definition">#</a></dt>
<dd><p>Changes value of variable in a callframe. Object-based scopes are not supported and must be
mutated manually.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>scope_number</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></span>) – 0-based number of scope as was listed in scope chain. Only ‘local’, ‘closure’ and ‘catch’ scope types are allowed. Other scopes could be manipulated manually.</p></li>
<li><p><strong>variable_name</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></span>) – Variable name.</p></li>
<li><p><strong>new_value</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="runtime.html#nodriver.cdp.runtime.CallArgument" title="nodriver.cdp.runtime.CallArgument"><code class="xref py py-class docutils literal notranslate"><span class="pre">CallArgument</span></code></a></span>) – New variable value.</p></li>
<li><p><strong>call_frame_id</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference internal" href="#nodriver.cdp.debugger.CallFrameId" title="nodriver.cdp.debugger.CallFrameId"><code class="xref py py-class docutils literal notranslate"><span class="pre">CallFrameId</span></code></a></span>) – Id of callframe that holds variable.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.step_into">
<span class="sig-name descname"><span class="pre">step_into</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">break_on_async_call</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">skip_list</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/debugger.html#step_into"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.debugger.step_into" title="Link to this definition">#</a></dt>
<dd><p>Steps into the function call.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>break_on_async_call</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>]</span>) – <strong>(EXPERIMENTAL)</strong> <em>(Optional)</em> Debugger will pause on the execution of the first async task which was scheduled before next pause.</p></li>
<li><p><strong>skip_list</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.debugger.LocationRange" title="nodriver.cdp.debugger.LocationRange"><code class="xref py py-class docutils literal notranslate"><span class="pre">LocationRange</span></code></a>]]</span>) – <strong>(EXPERIMENTAL)</strong> <em>(Optional)</em> The skipList specifies location ranges that should be skipped on step into.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.step_out">
<span class="sig-name descname"><span class="pre">step_out</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/debugger.html#step_out"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.debugger.step_out" title="Link to this definition">#</a></dt>
<dd><p>Steps out of the function call.</p>
<dl class="field-list simple">
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.step_over">
<span class="sig-name descname"><span class="pre">step_over</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">skip_list</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/debugger.html#step_over"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.debugger.step_over" title="Link to this definition">#</a></dt>
<dd><p>Steps over the statement.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>skip_list</strong> (<span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a>[<a class="reference internal" href="#nodriver.cdp.debugger.LocationRange" title="nodriver.cdp.debugger.LocationRange"><code class="xref py py-class docutils literal notranslate"><span class="pre">LocationRange</span></code></a>]]</span>) – <strong>(EXPERIMENTAL)</strong> <em>(Optional)</em> The skipList specifies location ranges that should be skipped on step over.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="sphinx_autodoc_typehints-type"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Generator" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Generator</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Dict</span></code></a>[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Any</span></code></a>], <a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><code class="xref py py-obj docutils literal notranslate"><span class="pre">None</span></code></a>]</span></p>
</dd>
</dl>
</dd></dl>

</section>
<section id="events">
<h2>Events<a class="headerlink" href="#events" title="Link to this heading">#</a></h2>
<p>Generally, you do not need to instantiate CDP events
yourself. Instead, the API creates events for you and then
you use the event’s attributes.</p>
<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.BreakpointResolved">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">BreakpointResolved</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">breakpoint_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">location</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/debugger.html#BreakpointResolved"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.debugger.BreakpointResolved" title="Link to this definition">#</a></dt>
<dd><p>Fired when breakpoint is resolved to an actual script and location.
Deprecated in favor of <code class="docutils literal notranslate"><span class="pre">resolvedBreakpoints</span></code> in the <code class="docutils literal notranslate"><span class="pre">scriptParsed</span></code> event.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 1.3.</span></p>
</div>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.BreakpointResolved.breakpoint_id">
<span class="sig-name descname"><span class="pre">breakpoint_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.debugger.BreakpointId" title="nodriver.cdp.debugger.BreakpointId"><code class="xref py py-class docutils literal notranslate"><span class="pre">BreakpointId</span></code></a></em><a class="headerlink" href="#nodriver.cdp.debugger.BreakpointResolved.breakpoint_id" title="Link to this definition">#</a></dt>
<dd><p>Breakpoint unique identifier.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.BreakpointResolved.location">
<span class="sig-name descname"><span class="pre">location</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#nodriver.cdp.debugger.Location" title="nodriver.cdp.debugger.Location"><code class="xref py py-class docutils literal notranslate"><span class="pre">Location</span></code></a></em><a class="headerlink" href="#nodriver.cdp.debugger.BreakpointResolved.location" title="Link to this definition">#</a></dt>
<dd><p>Actual breakpoint location.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.Paused">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">Paused</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">call_frames</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">reason</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">data</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">hit_breakpoints</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">async_stack_trace</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">async_stack_trace_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">async_call_stack_trace_id</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/debugger.html#Paused"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.debugger.Paused" title="Link to this definition">#</a></dt>
<dd><p>Fired when the virtual machine stopped on breakpoint or exception or any other stop criteria.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.Paused.call_frames">
<span class="sig-name descname"><span class="pre">call_frames</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.debugger.CallFrame" title="nodriver.cdp.debugger.CallFrame"><code class="xref py py-class docutils literal notranslate"><span class="pre">CallFrame</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.debugger.Paused.call_frames" title="Link to this definition">#</a></dt>
<dd><p>Call stack the virtual machine stopped on.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.Paused.reason">
<span class="sig-name descname"><span class="pre">reason</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.debugger.Paused.reason" title="Link to this definition">#</a></dt>
<dd><p>Pause reason.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.Paused.data">
<span class="sig-name descname"><span class="pre">data</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">dict</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.debugger.Paused.data" title="Link to this definition">#</a></dt>
<dd><p>Object containing break-specific auxiliary properties.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.Paused.hit_breakpoints">
<span class="sig-name descname"><span class="pre">hit_breakpoints</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]]</span></em><a class="headerlink" href="#nodriver.cdp.debugger.Paused.hit_breakpoints" title="Link to this definition">#</a></dt>
<dd><p>Hit breakpoints IDs</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.Paused.async_stack_trace">
<span class="sig-name descname"><span class="pre">async_stack_trace</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="runtime.html#nodriver.cdp.runtime.StackTrace" title="nodriver.cdp.runtime.StackTrace"><code class="xref py py-class docutils literal notranslate"><span class="pre">StackTrace</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.debugger.Paused.async_stack_trace" title="Link to this definition">#</a></dt>
<dd><p>Async stack trace, if any.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.Paused.async_stack_trace_id">
<span class="sig-name descname"><span class="pre">async_stack_trace_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="runtime.html#nodriver.cdp.runtime.StackTraceId" title="nodriver.cdp.runtime.StackTraceId"><code class="xref py py-class docutils literal notranslate"><span class="pre">StackTraceId</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.debugger.Paused.async_stack_trace_id" title="Link to this definition">#</a></dt>
<dd><p>Async stack trace, if any.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.Paused.async_call_stack_trace_id">
<span class="sig-name descname"><span class="pre">async_call_stack_trace_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="runtime.html#nodriver.cdp.runtime.StackTraceId" title="nodriver.cdp.runtime.StackTraceId"><code class="xref py py-class docutils literal notranslate"><span class="pre">StackTraceId</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.debugger.Paused.async_call_stack_trace_id" title="Link to this definition">#</a></dt>
<dd><p>Never present, will be removed.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.Resumed">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">Resumed</span></span><a class="reference internal" href="../../_modules/nodriver/cdp/debugger.html#Resumed"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.debugger.Resumed" title="Link to this definition">#</a></dt>
<dd><p>Fired when the virtual machine resumed execution.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.ScriptFailedToParse">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">ScriptFailedToParse</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">script_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">url</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start_line</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start_column</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">end_line</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">end_column</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">execution_context_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">hash_</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">build_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">execution_context_aux_data</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">source_map_url</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">has_source_url</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">is_module</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">length</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">stack_trace</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">code_offset</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">script_language</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">embedder_name</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/debugger.html#ScriptFailedToParse"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.debugger.ScriptFailedToParse" title="Link to this definition">#</a></dt>
<dd><p>Fired when virtual machine fails to parse the script.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.ScriptFailedToParse.script_id">
<span class="sig-name descname"><span class="pre">script_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="runtime.html#nodriver.cdp.runtime.ScriptId" title="nodriver.cdp.runtime.ScriptId"><code class="xref py py-class docutils literal notranslate"><span class="pre">ScriptId</span></code></a></em><a class="headerlink" href="#nodriver.cdp.debugger.ScriptFailedToParse.script_id" title="Link to this definition">#</a></dt>
<dd><p>Identifier of the script parsed.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.ScriptFailedToParse.url">
<span class="sig-name descname"><span class="pre">url</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.debugger.ScriptFailedToParse.url" title="Link to this definition">#</a></dt>
<dd><p>URL or name of the script parsed (if any).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.ScriptFailedToParse.start_line">
<span class="sig-name descname"><span class="pre">start_line</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></em><a class="headerlink" href="#nodriver.cdp.debugger.ScriptFailedToParse.start_line" title="Link to this definition">#</a></dt>
<dd><p>Line offset of the script within the resource with given URL (for script tags).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.ScriptFailedToParse.start_column">
<span class="sig-name descname"><span class="pre">start_column</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></em><a class="headerlink" href="#nodriver.cdp.debugger.ScriptFailedToParse.start_column" title="Link to this definition">#</a></dt>
<dd><p>Column offset of the script within the resource with given URL.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.ScriptFailedToParse.end_line">
<span class="sig-name descname"><span class="pre">end_line</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></em><a class="headerlink" href="#nodriver.cdp.debugger.ScriptFailedToParse.end_line" title="Link to this definition">#</a></dt>
<dd><p>Last line of the script.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.ScriptFailedToParse.end_column">
<span class="sig-name descname"><span class="pre">end_column</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></em><a class="headerlink" href="#nodriver.cdp.debugger.ScriptFailedToParse.end_column" title="Link to this definition">#</a></dt>
<dd><p>Length of the last line of the script.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.ScriptFailedToParse.execution_context_id">
<span class="sig-name descname"><span class="pre">execution_context_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="runtime.html#nodriver.cdp.runtime.ExecutionContextId" title="nodriver.cdp.runtime.ExecutionContextId"><code class="xref py py-class docutils literal notranslate"><span class="pre">ExecutionContextId</span></code></a></em><a class="headerlink" href="#nodriver.cdp.debugger.ScriptFailedToParse.execution_context_id" title="Link to this definition">#</a></dt>
<dd><p>Specifies script creation context.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.ScriptFailedToParse.hash_">
<span class="sig-name descname"><span class="pre">hash_</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.debugger.ScriptFailedToParse.hash_" title="Link to this definition">#</a></dt>
<dd><p>Content hash of the script, SHA-256.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.ScriptFailedToParse.build_id">
<span class="sig-name descname"><span class="pre">build_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.debugger.ScriptFailedToParse.build_id" title="Link to this definition">#</a></dt>
<dd><p>For Wasm modules, the content of the <code class="docutils literal notranslate"><span class="pre">build_id</span></code> custom section.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.ScriptFailedToParse.execution_context_aux_data">
<span class="sig-name descname"><span class="pre">execution_context_aux_data</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">dict</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.debugger.ScriptFailedToParse.execution_context_aux_data" title="Link to this definition">#</a></dt>
<dd><p>‘default’``’isolated’``’worker’, frameId: string}</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>Embedder-specific auxiliary data likely matching {isDefault</p>
</dd>
<dt class="field-even">Type<span class="colon">:</span></dt>
<dd class="field-even"><p>boolean, <a class="reference external" href="https://docs.python.org/3/library/functions.html#type" title="(in Python v3.13)">type</a></p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.ScriptFailedToParse.source_map_url">
<span class="sig-name descname"><span class="pre">source_map_url</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.debugger.ScriptFailedToParse.source_map_url" title="Link to this definition">#</a></dt>
<dd><p>URL of source map associated with script (if any).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.ScriptFailedToParse.has_source_url">
<span class="sig-name descname"><span class="pre">has_source_url</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.debugger.ScriptFailedToParse.has_source_url" title="Link to this definition">#</a></dt>
<dd><p>True, if this script has sourceURL.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.ScriptFailedToParse.is_module">
<span class="sig-name descname"><span class="pre">is_module</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.debugger.ScriptFailedToParse.is_module" title="Link to this definition">#</a></dt>
<dd><p>True, if this script is ES6 module.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.ScriptFailedToParse.length">
<span class="sig-name descname"><span class="pre">length</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.debugger.ScriptFailedToParse.length" title="Link to this definition">#</a></dt>
<dd><p>This script length.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.ScriptFailedToParse.stack_trace">
<span class="sig-name descname"><span class="pre">stack_trace</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="runtime.html#nodriver.cdp.runtime.StackTrace" title="nodriver.cdp.runtime.StackTrace"><code class="xref py py-class docutils literal notranslate"><span class="pre">StackTrace</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.debugger.ScriptFailedToParse.stack_trace" title="Link to this definition">#</a></dt>
<dd><p>JavaScript top stack frame of where the script parsed event was triggered if available.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.ScriptFailedToParse.code_offset">
<span class="sig-name descname"><span class="pre">code_offset</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.debugger.ScriptFailedToParse.code_offset" title="Link to this definition">#</a></dt>
<dd><p>If the scriptLanguage is WebAssembly, the code section offset in the module.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.ScriptFailedToParse.script_language">
<span class="sig-name descname"><span class="pre">script_language</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.debugger.ScriptLanguage" title="nodriver.cdp.debugger.ScriptLanguage"><code class="xref py py-class docutils literal notranslate"><span class="pre">ScriptLanguage</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.debugger.ScriptFailedToParse.script_language" title="Link to this definition">#</a></dt>
<dd><p>The language of the script.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.ScriptFailedToParse.embedder_name">
<span class="sig-name descname"><span class="pre">embedder_name</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.debugger.ScriptFailedToParse.embedder_name" title="Link to this definition">#</a></dt>
<dd><p>The name the embedder supplied for this script.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.ScriptParsed">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">ScriptParsed</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">script_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">url</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start_line</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start_column</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">end_line</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">end_column</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">execution_context_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">hash_</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">build_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">execution_context_aux_data</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">is_live_edit</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">source_map_url</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">has_source_url</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">is_module</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">length</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">stack_trace</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">code_offset</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">script_language</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">debug_symbols</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">embedder_name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">resolved_breakpoints</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../../_modules/nodriver/cdp/debugger.html#ScriptParsed"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#nodriver.cdp.debugger.ScriptParsed" title="Link to this definition">#</a></dt>
<dd><p>Fired when virtual machine parses script. This event is also fired for all known and uncollected
scripts upon enabling debugger.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.ScriptParsed.script_id">
<span class="sig-name descname"><span class="pre">script_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="runtime.html#nodriver.cdp.runtime.ScriptId" title="nodriver.cdp.runtime.ScriptId"><code class="xref py py-class docutils literal notranslate"><span class="pre">ScriptId</span></code></a></em><a class="headerlink" href="#nodriver.cdp.debugger.ScriptParsed.script_id" title="Link to this definition">#</a></dt>
<dd><p>Identifier of the script parsed.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.ScriptParsed.url">
<span class="sig-name descname"><span class="pre">url</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.debugger.ScriptParsed.url" title="Link to this definition">#</a></dt>
<dd><p>URL or name of the script parsed (if any).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.ScriptParsed.start_line">
<span class="sig-name descname"><span class="pre">start_line</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></em><a class="headerlink" href="#nodriver.cdp.debugger.ScriptParsed.start_line" title="Link to this definition">#</a></dt>
<dd><p>Line offset of the script within the resource with given URL (for script tags).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.ScriptParsed.start_column">
<span class="sig-name descname"><span class="pre">start_column</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></em><a class="headerlink" href="#nodriver.cdp.debugger.ScriptParsed.start_column" title="Link to this definition">#</a></dt>
<dd><p>Column offset of the script within the resource with given URL.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.ScriptParsed.end_line">
<span class="sig-name descname"><span class="pre">end_line</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></em><a class="headerlink" href="#nodriver.cdp.debugger.ScriptParsed.end_line" title="Link to this definition">#</a></dt>
<dd><p>Last line of the script.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.ScriptParsed.end_column">
<span class="sig-name descname"><span class="pre">end_column</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></em><a class="headerlink" href="#nodriver.cdp.debugger.ScriptParsed.end_column" title="Link to this definition">#</a></dt>
<dd><p>Length of the last line of the script.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.ScriptParsed.execution_context_id">
<span class="sig-name descname"><span class="pre">execution_context_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="runtime.html#nodriver.cdp.runtime.ExecutionContextId" title="nodriver.cdp.runtime.ExecutionContextId"><code class="xref py py-class docutils literal notranslate"><span class="pre">ExecutionContextId</span></code></a></em><a class="headerlink" href="#nodriver.cdp.debugger.ScriptParsed.execution_context_id" title="Link to this definition">#</a></dt>
<dd><p>Specifies script creation context.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.ScriptParsed.hash_">
<span class="sig-name descname"><span class="pre">hash_</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.debugger.ScriptParsed.hash_" title="Link to this definition">#</a></dt>
<dd><p>Content hash of the script, SHA-256.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.ScriptParsed.build_id">
<span class="sig-name descname"><span class="pre">build_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></em><a class="headerlink" href="#nodriver.cdp.debugger.ScriptParsed.build_id" title="Link to this definition">#</a></dt>
<dd><p>For Wasm modules, the content of the <code class="docutils literal notranslate"><span class="pre">build_id</span></code> custom section.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.ScriptParsed.execution_context_aux_data">
<span class="sig-name descname"><span class="pre">execution_context_aux_data</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#dict" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">dict</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.debugger.ScriptParsed.execution_context_aux_data" title="Link to this definition">#</a></dt>
<dd><p>‘default’``’isolated’``’worker’, frameId: string}</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>Embedder-specific auxiliary data likely matching {isDefault</p>
</dd>
<dt class="field-even">Type<span class="colon">:</span></dt>
<dd class="field-even"><p>boolean, <a class="reference external" href="https://docs.python.org/3/library/functions.html#type" title="(in Python v3.13)">type</a></p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.ScriptParsed.is_live_edit">
<span class="sig-name descname"><span class="pre">is_live_edit</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.debugger.ScriptParsed.is_live_edit" title="Link to this definition">#</a></dt>
<dd><p>True, if this script is generated as a result of the live edit operation.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.ScriptParsed.source_map_url">
<span class="sig-name descname"><span class="pre">source_map_url</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.debugger.ScriptParsed.source_map_url" title="Link to this definition">#</a></dt>
<dd><p>URL of source map associated with script (if any).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.ScriptParsed.has_source_url">
<span class="sig-name descname"><span class="pre">has_source_url</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.debugger.ScriptParsed.has_source_url" title="Link to this definition">#</a></dt>
<dd><p>True, if this script has sourceURL.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.ScriptParsed.is_module">
<span class="sig-name descname"><span class="pre">is_module</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.debugger.ScriptParsed.is_module" title="Link to this definition">#</a></dt>
<dd><p>True, if this script is ES6 module.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.ScriptParsed.length">
<span class="sig-name descname"><span class="pre">length</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.debugger.ScriptParsed.length" title="Link to this definition">#</a></dt>
<dd><p>This script length.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.ScriptParsed.stack_trace">
<span class="sig-name descname"><span class="pre">stack_trace</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="runtime.html#nodriver.cdp.runtime.StackTrace" title="nodriver.cdp.runtime.StackTrace"><code class="xref py py-class docutils literal notranslate"><span class="pre">StackTrace</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.debugger.ScriptParsed.stack_trace" title="Link to this definition">#</a></dt>
<dd><p>JavaScript top stack frame of where the script parsed event was triggered if available.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.ScriptParsed.code_offset">
<span class="sig-name descname"><span class="pre">code_offset</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.debugger.ScriptParsed.code_offset" title="Link to this definition">#</a></dt>
<dd><p>If the scriptLanguage is WebAssembly, the code section offset in the module.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.ScriptParsed.script_language">
<span class="sig-name descname"><span class="pre">script_language</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.debugger.ScriptLanguage" title="nodriver.cdp.debugger.ScriptLanguage"><code class="xref py py-class docutils literal notranslate"><span class="pre">ScriptLanguage</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.debugger.ScriptParsed.script_language" title="Link to this definition">#</a></dt>
<dd><p>The language of the script.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.ScriptParsed.debug_symbols">
<span class="sig-name descname"><span class="pre">debug_symbols</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.debugger.DebugSymbols" title="nodriver.cdp.debugger.DebugSymbols"><code class="xref py py-class docutils literal notranslate"><span class="pre">DebugSymbols</span></code></a><span class="pre">]]</span></em><a class="headerlink" href="#nodriver.cdp.debugger.ScriptParsed.debug_symbols" title="Link to this definition">#</a></dt>
<dd><p>If the scriptLanguage is WebAssembly, the source of debug symbols for the module.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.ScriptParsed.embedder_name">
<span class="sig-name descname"><span class="pre">embedder_name</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a><span class="pre">]</span></em><a class="headerlink" href="#nodriver.cdp.debugger.ScriptParsed.embedder_name" title="Link to this definition">#</a></dt>
<dd><p>The name the embedder supplied for this script.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="nodriver.cdp.debugger.ScriptParsed.resolved_breakpoints">
<span class="sig-name descname"><span class="pre">resolved_breakpoints</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Optional" title="(in Python v3.13)"><code class="xref py py-data docutils literal notranslate"><span class="pre">Optional</span></code></a><span class="pre">[</span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">List</span></code></a><span class="pre">[</span><a class="reference internal" href="#nodriver.cdp.debugger.ResolvedBreakpoint" title="nodriver.cdp.debugger.ResolvedBreakpoint"><code class="xref py py-class docutils literal notranslate"><span class="pre">ResolvedBreakpoint</span></code></a><span class="pre">]]</span></em><a class="headerlink" href="#nodriver.cdp.debugger.ScriptParsed.resolved_breakpoints" title="Link to this definition">#</a></dt>
<dd><p>The list of set breakpoints in this script if calls to <code class="docutils literal notranslate"><span class="pre">setBreakpointByUrl</span></code>
matches this script’s URL or hash. Clients that use this list can ignore the
<code class="docutils literal notranslate"><span class="pre">breakpointResolved</span></code> event. They are equivalent.</p>
</dd></dl>

</dd></dl>

</section>
</section>

        </article>
      </div>
      <footer>
        
        <div class="related-pages">
          <a class="next-page" href="device_access.html">
              <div class="page-info">
                <div class="context">
                  <span>Next</span>
                </div>
                <div class="title">DeviceAccess</div>
              </div>
              <svg class="furo-related-icon"><use href="#svg-arrow-right"></use></svg>
            </a>
          <a class="prev-page" href="css.html">
              <svg class="furo-related-icon"><use href="#svg-arrow-right"></use></svg>
              <div class="page-info">
                <div class="context">
                  <span>Previous</span>
                </div>
                
                <div class="title">CSS</div>
                
              </div>
            </a>
        </div>
        <div class="bottom-of-page">
          <div class="left-details">
            <div class="copyright">
                Copyright &#169; 2023, Author
            </div>
            Made with <a href="https://www.sphinx-doc.org/">Sphinx</a> and <a class="muted-link" href="https://pradyunsg.me">@pradyunsg</a>'s
            
            <a href="https://github.com/pradyunsg/furo">Furo</a>
            
          </div>
          <div class="right-details">
            
          </div>
        </div>
        
      </footer>
    </div>
    <aside class="toc-drawer">
      
      
      <div class="toc-sticky toc-scroll">
        <div class="toc-title-container">
          <span class="toc-title">
            On this page
          </span>
        </div>
        <div class="toc-tree-container">
          <div class="toc-tree">
            <ul>
<li><a class="reference internal" href="#">Debugger</a><ul>
<li><a class="reference internal" href="#types">Types</a><ul>
<li><a class="reference internal" href="#nodriver.cdp.debugger.BreakpointId"><code class="docutils literal notranslate"><span class="pre">BreakpointId</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.CallFrameId"><code class="docutils literal notranslate"><span class="pre">CallFrameId</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.Location"><code class="docutils literal notranslate"><span class="pre">Location</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.debugger.Location.script_id"><code class="docutils literal notranslate"><span class="pre">Location.script_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.Location.line_number"><code class="docutils literal notranslate"><span class="pre">Location.line_number</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.Location.column_number"><code class="docutils literal notranslate"><span class="pre">Location.column_number</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.ScriptPosition"><code class="docutils literal notranslate"><span class="pre">ScriptPosition</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.debugger.ScriptPosition.line_number"><code class="docutils literal notranslate"><span class="pre">ScriptPosition.line_number</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.ScriptPosition.column_number"><code class="docutils literal notranslate"><span class="pre">ScriptPosition.column_number</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.LocationRange"><code class="docutils literal notranslate"><span class="pre">LocationRange</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.debugger.LocationRange.script_id"><code class="docutils literal notranslate"><span class="pre">LocationRange.script_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.LocationRange.start"><code class="docutils literal notranslate"><span class="pre">LocationRange.start</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.LocationRange.end"><code class="docutils literal notranslate"><span class="pre">LocationRange.end</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.CallFrame"><code class="docutils literal notranslate"><span class="pre">CallFrame</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.debugger.CallFrame.call_frame_id"><code class="docutils literal notranslate"><span class="pre">CallFrame.call_frame_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.CallFrame.function_name"><code class="docutils literal notranslate"><span class="pre">CallFrame.function_name</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.CallFrame.location"><code class="docutils literal notranslate"><span class="pre">CallFrame.location</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.CallFrame.url"><code class="docutils literal notranslate"><span class="pre">CallFrame.url</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.CallFrame.scope_chain"><code class="docutils literal notranslate"><span class="pre">CallFrame.scope_chain</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.CallFrame.this"><code class="docutils literal notranslate"><span class="pre">CallFrame.this</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.CallFrame.function_location"><code class="docutils literal notranslate"><span class="pre">CallFrame.function_location</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.CallFrame.return_value"><code class="docutils literal notranslate"><span class="pre">CallFrame.return_value</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.CallFrame.can_be_restarted"><code class="docutils literal notranslate"><span class="pre">CallFrame.can_be_restarted</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.Scope"><code class="docutils literal notranslate"><span class="pre">Scope</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.debugger.Scope.type_"><code class="docutils literal notranslate"><span class="pre">Scope.type_</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.Scope.object_"><code class="docutils literal notranslate"><span class="pre">Scope.object_</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.Scope.name"><code class="docutils literal notranslate"><span class="pre">Scope.name</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.Scope.start_location"><code class="docutils literal notranslate"><span class="pre">Scope.start_location</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.Scope.end_location"><code class="docutils literal notranslate"><span class="pre">Scope.end_location</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.SearchMatch"><code class="docutils literal notranslate"><span class="pre">SearchMatch</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.debugger.SearchMatch.line_number"><code class="docutils literal notranslate"><span class="pre">SearchMatch.line_number</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.SearchMatch.line_content"><code class="docutils literal notranslate"><span class="pre">SearchMatch.line_content</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.BreakLocation"><code class="docutils literal notranslate"><span class="pre">BreakLocation</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.debugger.BreakLocation.script_id"><code class="docutils literal notranslate"><span class="pre">BreakLocation.script_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.BreakLocation.line_number"><code class="docutils literal notranslate"><span class="pre">BreakLocation.line_number</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.BreakLocation.column_number"><code class="docutils literal notranslate"><span class="pre">BreakLocation.column_number</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.BreakLocation.type_"><code class="docutils literal notranslate"><span class="pre">BreakLocation.type_</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.WasmDisassemblyChunk"><code class="docutils literal notranslate"><span class="pre">WasmDisassemblyChunk</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.debugger.WasmDisassemblyChunk.lines"><code class="docutils literal notranslate"><span class="pre">WasmDisassemblyChunk.lines</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.WasmDisassemblyChunk.bytecode_offsets"><code class="docutils literal notranslate"><span class="pre">WasmDisassemblyChunk.bytecode_offsets</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.ScriptLanguage"><code class="docutils literal notranslate"><span class="pre">ScriptLanguage</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.debugger.ScriptLanguage.JAVA_SCRIPT"><code class="docutils literal notranslate"><span class="pre">ScriptLanguage.JAVA_SCRIPT</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.ScriptLanguage.WEB_ASSEMBLY"><code class="docutils literal notranslate"><span class="pre">ScriptLanguage.WEB_ASSEMBLY</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.DebugSymbols"><code class="docutils literal notranslate"><span class="pre">DebugSymbols</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.debugger.DebugSymbols.type_"><code class="docutils literal notranslate"><span class="pre">DebugSymbols.type_</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.DebugSymbols.external_url"><code class="docutils literal notranslate"><span class="pre">DebugSymbols.external_url</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.ResolvedBreakpoint"><code class="docutils literal notranslate"><span class="pre">ResolvedBreakpoint</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.debugger.ResolvedBreakpoint.breakpoint_id"><code class="docutils literal notranslate"><span class="pre">ResolvedBreakpoint.breakpoint_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.ResolvedBreakpoint.location"><code class="docutils literal notranslate"><span class="pre">ResolvedBreakpoint.location</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#commands">Commands</a><ul>
<li><a class="reference internal" href="#nodriver.cdp.debugger.continue_to_location"><code class="docutils literal notranslate"><span class="pre">continue_to_location()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.disable"><code class="docutils literal notranslate"><span class="pre">disable()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.disassemble_wasm_module"><code class="docutils literal notranslate"><span class="pre">disassemble_wasm_module()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.enable"><code class="docutils literal notranslate"><span class="pre">enable()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.evaluate_on_call_frame"><code class="docutils literal notranslate"><span class="pre">evaluate_on_call_frame()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.get_possible_breakpoints"><code class="docutils literal notranslate"><span class="pre">get_possible_breakpoints()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.get_script_source"><code class="docutils literal notranslate"><span class="pre">get_script_source()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.get_stack_trace"><code class="docutils literal notranslate"><span class="pre">get_stack_trace()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.get_wasm_bytecode"><code class="docutils literal notranslate"><span class="pre">get_wasm_bytecode()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.next_wasm_disassembly_chunk"><code class="docutils literal notranslate"><span class="pre">next_wasm_disassembly_chunk()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.pause"><code class="docutils literal notranslate"><span class="pre">pause()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.pause_on_async_call"><code class="docutils literal notranslate"><span class="pre">pause_on_async_call()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.remove_breakpoint"><code class="docutils literal notranslate"><span class="pre">remove_breakpoint()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.restart_frame"><code class="docutils literal notranslate"><span class="pre">restart_frame()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.resume"><code class="docutils literal notranslate"><span class="pre">resume()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.search_in_content"><code class="docutils literal notranslate"><span class="pre">search_in_content()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.set_async_call_stack_depth"><code class="docutils literal notranslate"><span class="pre">set_async_call_stack_depth()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.set_blackbox_execution_contexts"><code class="docutils literal notranslate"><span class="pre">set_blackbox_execution_contexts()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.set_blackbox_patterns"><code class="docutils literal notranslate"><span class="pre">set_blackbox_patterns()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.set_blackboxed_ranges"><code class="docutils literal notranslate"><span class="pre">set_blackboxed_ranges()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.set_breakpoint"><code class="docutils literal notranslate"><span class="pre">set_breakpoint()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.set_breakpoint_by_url"><code class="docutils literal notranslate"><span class="pre">set_breakpoint_by_url()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.set_breakpoint_on_function_call"><code class="docutils literal notranslate"><span class="pre">set_breakpoint_on_function_call()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.set_breakpoints_active"><code class="docutils literal notranslate"><span class="pre">set_breakpoints_active()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.set_instrumentation_breakpoint"><code class="docutils literal notranslate"><span class="pre">set_instrumentation_breakpoint()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.set_pause_on_exceptions"><code class="docutils literal notranslate"><span class="pre">set_pause_on_exceptions()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.set_return_value"><code class="docutils literal notranslate"><span class="pre">set_return_value()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.set_script_source"><code class="docutils literal notranslate"><span class="pre">set_script_source()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.set_skip_all_pauses"><code class="docutils literal notranslate"><span class="pre">set_skip_all_pauses()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.set_variable_value"><code class="docutils literal notranslate"><span class="pre">set_variable_value()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.step_into"><code class="docutils literal notranslate"><span class="pre">step_into()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.step_out"><code class="docutils literal notranslate"><span class="pre">step_out()</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.step_over"><code class="docutils literal notranslate"><span class="pre">step_over()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#events">Events</a><ul>
<li><a class="reference internal" href="#nodriver.cdp.debugger.BreakpointResolved"><code class="docutils literal notranslate"><span class="pre">BreakpointResolved</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.debugger.BreakpointResolved.breakpoint_id"><code class="docutils literal notranslate"><span class="pre">BreakpointResolved.breakpoint_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.BreakpointResolved.location"><code class="docutils literal notranslate"><span class="pre">BreakpointResolved.location</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.Paused"><code class="docutils literal notranslate"><span class="pre">Paused</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.debugger.Paused.call_frames"><code class="docutils literal notranslate"><span class="pre">Paused.call_frames</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.Paused.reason"><code class="docutils literal notranslate"><span class="pre">Paused.reason</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.Paused.data"><code class="docutils literal notranslate"><span class="pre">Paused.data</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.Paused.hit_breakpoints"><code class="docutils literal notranslate"><span class="pre">Paused.hit_breakpoints</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.Paused.async_stack_trace"><code class="docutils literal notranslate"><span class="pre">Paused.async_stack_trace</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.Paused.async_stack_trace_id"><code class="docutils literal notranslate"><span class="pre">Paused.async_stack_trace_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.Paused.async_call_stack_trace_id"><code class="docutils literal notranslate"><span class="pre">Paused.async_call_stack_trace_id</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.Resumed"><code class="docutils literal notranslate"><span class="pre">Resumed</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.ScriptFailedToParse"><code class="docutils literal notranslate"><span class="pre">ScriptFailedToParse</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.debugger.ScriptFailedToParse.script_id"><code class="docutils literal notranslate"><span class="pre">ScriptFailedToParse.script_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.ScriptFailedToParse.url"><code class="docutils literal notranslate"><span class="pre">ScriptFailedToParse.url</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.ScriptFailedToParse.start_line"><code class="docutils literal notranslate"><span class="pre">ScriptFailedToParse.start_line</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.ScriptFailedToParse.start_column"><code class="docutils literal notranslate"><span class="pre">ScriptFailedToParse.start_column</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.ScriptFailedToParse.end_line"><code class="docutils literal notranslate"><span class="pre">ScriptFailedToParse.end_line</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.ScriptFailedToParse.end_column"><code class="docutils literal notranslate"><span class="pre">ScriptFailedToParse.end_column</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.ScriptFailedToParse.execution_context_id"><code class="docutils literal notranslate"><span class="pre">ScriptFailedToParse.execution_context_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.ScriptFailedToParse.hash_"><code class="docutils literal notranslate"><span class="pre">ScriptFailedToParse.hash_</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.ScriptFailedToParse.build_id"><code class="docutils literal notranslate"><span class="pre">ScriptFailedToParse.build_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.ScriptFailedToParse.execution_context_aux_data"><code class="docutils literal notranslate"><span class="pre">ScriptFailedToParse.execution_context_aux_data</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.ScriptFailedToParse.source_map_url"><code class="docutils literal notranslate"><span class="pre">ScriptFailedToParse.source_map_url</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.ScriptFailedToParse.has_source_url"><code class="docutils literal notranslate"><span class="pre">ScriptFailedToParse.has_source_url</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.ScriptFailedToParse.is_module"><code class="docutils literal notranslate"><span class="pre">ScriptFailedToParse.is_module</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.ScriptFailedToParse.length"><code class="docutils literal notranslate"><span class="pre">ScriptFailedToParse.length</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.ScriptFailedToParse.stack_trace"><code class="docutils literal notranslate"><span class="pre">ScriptFailedToParse.stack_trace</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.ScriptFailedToParse.code_offset"><code class="docutils literal notranslate"><span class="pre">ScriptFailedToParse.code_offset</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.ScriptFailedToParse.script_language"><code class="docutils literal notranslate"><span class="pre">ScriptFailedToParse.script_language</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.ScriptFailedToParse.embedder_name"><code class="docutils literal notranslate"><span class="pre">ScriptFailedToParse.embedder_name</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.ScriptParsed"><code class="docutils literal notranslate"><span class="pre">ScriptParsed</span></code></a><ul>
<li><a class="reference internal" href="#nodriver.cdp.debugger.ScriptParsed.script_id"><code class="docutils literal notranslate"><span class="pre">ScriptParsed.script_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.ScriptParsed.url"><code class="docutils literal notranslate"><span class="pre">ScriptParsed.url</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.ScriptParsed.start_line"><code class="docutils literal notranslate"><span class="pre">ScriptParsed.start_line</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.ScriptParsed.start_column"><code class="docutils literal notranslate"><span class="pre">ScriptParsed.start_column</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.ScriptParsed.end_line"><code class="docutils literal notranslate"><span class="pre">ScriptParsed.end_line</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.ScriptParsed.end_column"><code class="docutils literal notranslate"><span class="pre">ScriptParsed.end_column</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.ScriptParsed.execution_context_id"><code class="docutils literal notranslate"><span class="pre">ScriptParsed.execution_context_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.ScriptParsed.hash_"><code class="docutils literal notranslate"><span class="pre">ScriptParsed.hash_</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.ScriptParsed.build_id"><code class="docutils literal notranslate"><span class="pre">ScriptParsed.build_id</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.ScriptParsed.execution_context_aux_data"><code class="docutils literal notranslate"><span class="pre">ScriptParsed.execution_context_aux_data</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.ScriptParsed.is_live_edit"><code class="docutils literal notranslate"><span class="pre">ScriptParsed.is_live_edit</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.ScriptParsed.source_map_url"><code class="docutils literal notranslate"><span class="pre">ScriptParsed.source_map_url</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.ScriptParsed.has_source_url"><code class="docutils literal notranslate"><span class="pre">ScriptParsed.has_source_url</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.ScriptParsed.is_module"><code class="docutils literal notranslate"><span class="pre">ScriptParsed.is_module</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.ScriptParsed.length"><code class="docutils literal notranslate"><span class="pre">ScriptParsed.length</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.ScriptParsed.stack_trace"><code class="docutils literal notranslate"><span class="pre">ScriptParsed.stack_trace</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.ScriptParsed.code_offset"><code class="docutils literal notranslate"><span class="pre">ScriptParsed.code_offset</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.ScriptParsed.script_language"><code class="docutils literal notranslate"><span class="pre">ScriptParsed.script_language</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.ScriptParsed.debug_symbols"><code class="docutils literal notranslate"><span class="pre">ScriptParsed.debug_symbols</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.ScriptParsed.embedder_name"><code class="docutils literal notranslate"><span class="pre">ScriptParsed.embedder_name</span></code></a></li>
<li><a class="reference internal" href="#nodriver.cdp.debugger.ScriptParsed.resolved_breakpoints"><code class="docutils literal notranslate"><span class="pre">ScriptParsed.resolved_breakpoints</span></code></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>

          </div>
        </div>
      </div>
      
      
    </aside>
  </div>
</div><script src="../../_static/documentation_options.js?v=5929fcd5"></script>
    <script src="../../_static/doctools.js?v=888ff710"></script>
    <script src="../../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../../_static/scripts/furo.js?v=32e29ea5"></script>
    </body>
</html>