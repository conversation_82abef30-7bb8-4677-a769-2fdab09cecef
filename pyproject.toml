[project]

name = "nodriver" # Required

version = "0.46.1"  # Required

description = """

    [Docs here](https://ultrafunkamsterdam.github.io/nodriver)

    * Official successor of Undetected Chromedriver
    * Can be made to work for for all chromium based browsers.
    * Dropped selenium and chromedriver binary requirements.
    * fully asynchronous == bizarre performance gains, and more granular control

    Part of undetected-chromedriver, or merely the successor of it, this library is a full rewrite, providing a
    fast framework for web automation, webscraping, bots and any other creative ideas which are normally
    hindered by annoying anti bot systems like Captcha / CloudFlare / Imperva / hCaptcha and other
    big corp "ai" money machines using your input to make even more $$ (http://tinyurl.com/bigcorp-ai-inputs)

    The webdriver/selenium requirement is dropped entirely, since this library communicates directly to the browser.
    Being fully asynchronous, this adds massive performance improvements and more detailed control possibilities.

    As usual ( like undetected chromedriver) all config details and best practices are built-in, which means
    up and running with just a line of code.

    This makes it simple to use for quick prototyping, and perfect for interactive interpreter use (eg: IPython).


    WARNING:
       - results may vary due to many factors. No guarantees are given whatsoever.
       - Running from bad IP or datacenter may still cause captcha's and/or other problems.
       - With great power comes ... etc etc etc
         no but SERIOUS: for your own benefit, make sure "they" have no reason for upscaling anti-bot measurements.
         there might be one day it would not be feasible anymore to work up against big corp, and provide upgrades
         and free libraries.

"""  # Optional
readme = {file = "README.md", content-type = "text/markdown"} # Optional
requires-python = ">=3.9"
license = {file = "LICENSE.txt"}
keywords = [
    "webdriver", "browser", "captcha", "scraping",
    "selenium", "navigator", "python3", "cloudflare",
    "chromedriver", "anti-bot", "bot-detection",
    "cloudflare-bypass", "distil", "anti-detection"]
authors = [
  {name = "UltrafunkAmsterdam", email = "<EMAIL>" } # Optional
]

maintainers = [
  {name = "UltrafunkAmsterdam", email = "<EMAIL>" } # Optional
]
classifiers = [  # Optional

    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "Topic :: Internet :: WWW/HTTP :: Browsers",
    "Programming Language :: Python :: 3.7",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",

]

dependencies = [
      "mss",
      "websockets>=14",
      "deprecated"
]

#
[project.urls]
"Homepage" = "https://github.com/UltrafunkAmsterdam/nodriver"
"Bug Reports" = "https://github.com/UltrafunkAmsterdam/nodriver/issues"
"Source" = "https://github.com/UltrafunkAmsterdam/nodriver"


[tool.setuptools]
include-package-data = true
packages = ["nodriver", "nodriver.core", "nodriver.cdp"]

[build-system]
requires = ["setuptools>=43.0.0", "wheel"]
build-backend = "setuptools.build_meta"

[project.optional-dependencies]
dev = [
    "black",
    "build",
    "isort",
    "sphinx",
    "sphinx_markdown_builder",
    "sphinx_autodoc_typehints",
    "furo",
    "pygments",
    "sphinxcontrib-video"
]
