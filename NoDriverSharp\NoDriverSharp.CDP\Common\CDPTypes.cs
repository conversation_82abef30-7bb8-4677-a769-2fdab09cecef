using System.Text.Json.Serialization;

namespace NoDriverSharp.CDP.Common;

/// <summary>
/// Common types used across CDP domains
/// </summary>

/// <summary>
/// Unique DOM node identifier
/// </summary>
public record NodeId(int Value)
{
    public static implicit operator int(NodeId nodeId) => nodeId.Value;
    public static implicit operator NodeId(int value) => new(value);
}

/// <summary>
/// Unique DOM node identifier used by Backend
/// </summary>
public record BackendNodeId(int Value)
{
    public static implicit operator int(BackendNodeId nodeId) => nodeId.Value;
    public static implicit operator BackendNodeId(int value) => new(value);
}

/// <summary>
/// Unique frame identifier
/// </summary>
public record FrameId(string Value)
{
    public static implicit operator string(FrameId frameId) => frameId.Value;
    public static implicit operator FrameId(string value) => new(value);
}

/// <summary>
/// Unique loader identifier
/// </summary>
public record LoaderId(string Value)
{
    public static implicit operator string(LoaderId loaderId) => loaderId.Value;
    public static implicit operator LoaderId(string value) => new(value);
}

/// <summary>
/// Unique execution context identifier
/// </summary>
public record ExecutionContextId(int Value)
{
    public static implicit operator int(ExecutionContextId contextId) => contextId.Value;
    public static implicit operator ExecutionContextId(int value) => new(value);
}

/// <summary>
/// Unique object identifier
/// </summary>
public record RemoteObjectId(string Value)
{
    public static implicit operator string(RemoteObjectId objectId) => objectId.Value;
    public static implicit operator RemoteObjectId(string value) => new(value);
}

/// <summary>
/// Unique target identifier
/// </summary>
public record TargetId(string Value)
{
    public static implicit operator string(TargetId targetId) => targetId.Value;
    public static implicit operator TargetId(string value) => new(value);
}

/// <summary>
/// Unique session identifier
/// </summary>
public record SessionId(string Value)
{
    public static implicit operator string(SessionId sessionId) => sessionId.Value;
    public static implicit operator SessionId(string value) => new(value);
}

/// <summary>
/// Timestamp in seconds since epoch
/// </summary>
public record Timestamp(double Value)
{
    public static implicit operator double(Timestamp timestamp) => timestamp.Value;
    public static implicit operator Timestamp(double value) => new(value);
    
    public DateTime ToDateTime() => DateTimeOffset.FromUnixTimeMilliseconds((long)(Value * 1000)).DateTime;
    public static Timestamp FromDateTime(DateTime dateTime) => new(((DateTimeOffset)dateTime).ToUnixTimeMilliseconds() / 1000.0);
}

/// <summary>
/// Rectangle coordinates
/// </summary>
public class Rect
{
    [JsonPropertyName("x")]
    public double X { get; set; }

    [JsonPropertyName("y")]
    public double Y { get; set; }

    [JsonPropertyName("width")]
    public double Width { get; set; }

    [JsonPropertyName("height")]
    public double Height { get; set; }
}

/// <summary>
/// Viewport for capturing screenshot
/// </summary>
public class Viewport
{
    [JsonPropertyName("x")]
    public double X { get; set; }

    [JsonPropertyName("y")]
    public double Y { get; set; }

    [JsonPropertyName("width")]
    public double Width { get; set; }

    [JsonPropertyName("height")]
    public double Height { get; set; }

    [JsonPropertyName("scale")]
    public double Scale { get; set; }
}
