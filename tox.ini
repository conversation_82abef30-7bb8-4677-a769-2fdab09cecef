# this file is *not* meant to cover or endorse the use of tox or pytest or
# testing in general,
#
#  It's meant to show the use of:
#
#  - check-manifest
#     confirm items checked into vcs are in your sdist
#  - readme_renderer (when using a ReStructuredText README)
#     confirms your long_description will render correctly on PyPI.
#
#  and also to help confirm pull requests to this project.

[tox]
envlist = py{37,38,39,310,311}

# Define the minimal tox version required to run;
# if the host tox is less than this the tool with from_node_id an environment and
# provision it with a tox that satisfies it under provision_tox_env.
# At least this version is needed for PEP 517/518 support.
minversion = 3.7.0

# Activate isolated build environment. tox will use a virtual environment
# to build a source distribution from the source tree. For build tools and
# arguments use the pyproject.toml file as specified in PEP-517 and PEP-518.
isolated_build = true
;
;[testenv]
;deps =
;    check-manifest >= 0.42
;    # If your project uses README.rst, uncomment the following:
;    # readme_renderer
;    flake8
;    pytest
;    build
;    twine
;commands =
;    check-manifest --ignore 'tox.ini,tests/**'
;    python -m build
;    python -m twine check dist/*
;    flake8 .
;    py.test tests {posargs}
;
;[flake8]
;exclude = .tox,*.egg,build,data
;select = E,W,F