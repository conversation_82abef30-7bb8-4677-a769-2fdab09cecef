
Some words about some helpers

Config class containing all settings for the browser.
For best performance, a lot of parameters are
already configured. In normal use cases, you won't initiate
this object. It is taken care of automatically, mostly by
keyword arguments to the :class:`nodriver.Browser` class.


- --remote-allow-origins=*
- --no-first-run
- --no-service-autorun
- --no-default-browser-check
- --homepage=about:blank
- --no-pings
- --password-store=basic
- --disable-infobars
- --disable-breakpad
- --disable-component-update
- --disable-backgrounding-occluded-windows
- --disable-renderer-backgrounding
- --disable-background-networking
- --disable-dev-shm-usage


