<!doctype html>
<html class="no-js" lang="en" data-content_root="../../../">
  <head><meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width,initial-scale=1"/>
    <meta name="color-scheme" content="light dark"><link rel="index" title="Index" href="../../../genindex.html" /><link rel="search" title="Search" href="../../../search.html" />

    <!-- Generated with Sphinx 7.2.6 and Furo 2023.09.10 -->
        <title>nodriver.core.browser - nodriver documentation</title>
      <link rel="stylesheet" type="text/css" href="../../../_static/pygments.css?v=fa44fd50" />
    <link rel="stylesheet" type="text/css" href="../../../_static/styles/furo.css?v=135e06be" />
    <link rel="stylesheet" type="text/css" href="../../../_static/styles/furo-extensions.css?v=36a5483c" />
    <link rel="stylesheet" type="text/css" href="../../../_static/./custom.css?v=3934642d" />
    
    


<style>
  body {
    --color-code-background: #eeffcc;
  --color-code-foreground: black;
  
  }
  @media not print {
    body[data-theme="dark"] {
      --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8eefba;
  --color-background-primary: #111112;
  --color-problematic: #e7aeae;
  
    }
    @media (prefers-color-scheme: dark) {
      body:not([data-theme="light"]) {
        --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8eefba;
  --color-background-primary: #111112;
  --color-problematic: #e7aeae;
  
      }
    }
  }
</style></head>
  <body>
    
    <script>
      document.body.dataset.theme = localStorage.getItem("theme") || "auto";
    </script>
    

<svg xmlns="http://www.w3.org/2000/svg" style="display: none;">
  <symbol id="svg-toc" viewBox="0 0 24 24">
    <title>Contents</title>
    <svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 1024 1024">
      <path d="M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM115.4 518.9L271.7 642c5.8 4.6 14.4.5 14.4-6.9V388.9c0-7.4-8.5-11.5-14.4-6.9L115.4 505.1a8.74 8.74 0 0 0 0 13.8z"/>
    </svg>
  </symbol>
  <symbol id="svg-menu" viewBox="0 0 24 24">
    <title>Menu</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-menu">
      <line x1="3" y1="12" x2="21" y2="12"></line>
      <line x1="3" y1="6" x2="21" y2="6"></line>
      <line x1="3" y1="18" x2="21" y2="18"></line>
    </svg>
  </symbol>
  <symbol id="svg-arrow-right" viewBox="0 0 24 24">
    <title>Expand</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-chevron-right">
      <polyline points="9 18 15 12 9 6"></polyline>
    </svg>
  </symbol>
  <symbol id="svg-sun" viewBox="0 0 24 24">
    <title>Light mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="feather-sun">
      <circle cx="12" cy="12" r="5"></circle>
      <line x1="12" y1="1" x2="12" y2="3"></line>
      <line x1="12" y1="21" x2="12" y2="23"></line>
      <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
      <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
      <line x1="1" y1="12" x2="3" y2="12"></line>
      <line x1="21" y1="12" x2="23" y2="12"></line>
      <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
      <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
    </svg>
  </symbol>
  <symbol id="svg-moon" viewBox="0 0 24 24">
    <title>Dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-moon">
      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
      <path d="M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z" />
    </svg>
  </symbol>
  <symbol id="svg-sun-half" viewBox="0 0 24 24">
    <title>Auto light/dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-shadow">
      <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
      <circle cx="12" cy="12" r="9" />
      <path d="M13 12h5" />
      <path d="M13 15h4" />
      <path d="M13 18h1" />
      <path d="M13 9h4" />
      <path d="M13 6h1" />
    </svg>
  </symbol>
</svg>

<input type="checkbox" class="sidebar-toggle" name="__navigation" id="__navigation">
<input type="checkbox" class="sidebar-toggle" name="__toc" id="__toc">
<label class="overlay sidebar-overlay" for="__navigation">
  <div class="visually-hidden">Hide navigation sidebar</div>
</label>
<label class="overlay toc-overlay" for="__toc">
  <div class="visually-hidden">Hide table of contents sidebar</div>
</label>



<div class="page">
  <header class="mobile-header">
    <div class="header-left">
      <label class="nav-overlay-icon" for="__navigation">
        <div class="visually-hidden">Toggle site navigation sidebar</div>
        <i class="icon"><svg><use href="#svg-menu"></use></svg></i>
      </label>
    </div>
    <div class="header-center">
      <a href="../../../index.html"><div class="brand">nodriver  documentation</div></a>
    </div>
    <div class="header-right">
      <div class="theme-toggle-container theme-toggle-header">
        <button class="theme-toggle">
          <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
          <svg class="theme-icon-when-auto"><use href="#svg-sun-half"></use></svg>
          <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
          <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
        </button>
      </div>
      <label class="toc-overlay-icon toc-header-icon no-toc" for="__toc">
        <div class="visually-hidden">Toggle table of contents sidebar</div>
        <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
      </label>
    </div>
  </header>
  <aside class="sidebar-drawer">
    <div class="sidebar-container">
      
      <div class="sidebar-sticky"><div class="sidebar-scroll"><a class="sidebar-brand" href="../../../index.html">
  
  
  <span class="sidebar-brand-text">nodriver  documentation</span>
  
</a><form class="sidebar-search-container" method="get" action="../../../search.html" role="search">
  <input class="sidebar-search" placeholder="Search" name="q" aria-label="Search">
  <input type="hidden" name="check_keywords" value="yes">
  <input type="hidden" name="area" value="default">
</form>
<div id="searchbox"></div><div class="sidebar-tree">
  <ul>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html">Quickstart guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#usage-example">usage example</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#more-complete-example">More complete example</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#custom-starting-options">Custom starting options</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#alternative-custom-options">Alternative custom options</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#proxies-socks5-authenticated-too">Proxies (socks5, authenticated too)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#concrete-example">Concrete example</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/classes/browser.html">Browser class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/classes/tab.html">Tab class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/classes/element.html">Element class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/classes/others_and_helpers.html">Other classes and Helper classes</a></li>
</ul>
<ul>
<li class="toctree-l1 has-children"><a class="reference internal" href="../../../nodriver/cdp.html">CDP object</a><input class="toctree-checkbox" id="toctree-checkbox-1" name="toctree-checkbox-1" role="switch" type="checkbox"/><label for="toctree-checkbox-1"><div class="visually-hidden">Toggle navigation of CDP object</div><i class="icon"><svg><use href="#svg-arrow-right"></use></svg></i></label><ul>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/accessibility.html">Accessibility</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/animation.html">Animation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/audits.html">Audits</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/autofill.html">Autofill</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/background_service.html">BackgroundService</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/bluetooth_emulation.html">BluetoothEmulation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/browser.html">Browser</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/cache_storage.html">CacheStorage</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/cast.html">Cast</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/console.html">Console</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/css.html">CSS</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/debugger.html">Debugger</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/device_access.html">DeviceAccess</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/device_orientation.html">DeviceOrientation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/dom.html">DOM</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/dom_debugger.html">DOMDebugger</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/dom_snapshot.html">DOMSnapshot</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/dom_storage.html">DOMStorage</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/emulation.html">Emulation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/event_breakpoints.html">EventBreakpoints</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/extensions.html">Extensions</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/fed_cm.html">FedCm</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/fetch.html">Fetch</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/file_system.html">FileSystem</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/headless_experimental.html">HeadlessExperimental</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/heap_profiler.html">HeapProfiler</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/indexed_db.html">IndexedDB</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/input_.html">Input</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/inspector.html">Inspector</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/io.html">IO</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/layer_tree.html">LayerTree</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/log.html">Log</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/media.html">Media</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/memory.html">Memory</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/network.html">Network</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/overlay.html">Overlay</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/page.html">Page</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/performance.html">Performance</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/performance_timeline.html">PerformanceTimeline</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/preload.html">Preload</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/profiler.html">Profiler</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/pwa.html">PWA</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/runtime.html">Runtime</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/schema.html">Schema</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/security.html">Security</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/service_worker.html">ServiceWorker</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/storage.html">Storage</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/system_info.html">SystemInfo</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/target.html">Target</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/tethering.html">Tethering</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/tracing.html">Tracing</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/web_audio.html">WebAudio</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/web_authn.html">WebAuthn</a></li>
</ul>
</li>
</ul>

</div>
</div>
      </div>
      
    </div>
  </aside>
  <div class="main">
    <div class="content">
      <div class="article-container">
        <a href="#" class="back-to-top muted-link">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
            <path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8v12z"></path>
          </svg>
          <span>Back to top</span>
        </a>
        <div class="content-icon-container">
          <div class="theme-toggle-container theme-toggle-content">
            <button class="theme-toggle">
              <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
              <svg class="theme-icon-when-auto"><use href="#svg-sun-half"></use></svg>
              <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
              <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
            </button>
          </div>
          <label class="toc-overlay-icon toc-content-icon no-toc" for="__toc">
            <div class="visually-hidden">Toggle table of contents sidebar</div>
            <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
          </label>
        </div>
        <article role="main">
          <h1>Source code for nodriver.core.browser</h1><div class="highlight"><pre>
<span></span><span class="c1"># Copyright 2024 by UltrafunkAmsterdam (https://github.com/UltrafunkAmsterdam)</span>
<span class="c1"># All rights reserved.</span>
<span class="c1"># This file is part of the nodriver package.</span>
<span class="c1"># and is released under the &quot;GNU AFFERO GENERAL PUBLIC LICENSE&quot;.</span>
<span class="c1"># Please see the LICENSE.txt file that should have been included as part of this package.</span>

<span class="kn">from</span> <span class="nn">__future__</span> <span class="kn">import</span> <span class="n">annotations</span>

<span class="kn">import</span> <span class="nn">asyncio</span>
<span class="kn">import</span> <span class="nn">atexit</span>
<span class="kn">import</span> <span class="nn">json</span>
<span class="kn">import</span> <span class="nn">logging</span>
<span class="kn">import</span> <span class="nn">os</span>
<span class="kn">import</span> <span class="nn">pathlib</span>
<span class="kn">import</span> <span class="nn">pickle</span>
<span class="kn">import</span> <span class="nn">urllib.parse</span>
<span class="kn">import</span> <span class="nn">urllib.request</span>
<span class="kn">import</span> <span class="nn">warnings</span>
<span class="kn">from</span> <span class="nn">collections</span> <span class="kn">import</span> <span class="n">defaultdict</span>
<span class="kn">from</span> <span class="nn">typing</span> <span class="kn">import</span> <span class="n">List</span><span class="p">,</span> <span class="n">Tuple</span><span class="p">,</span> <span class="n">Union</span>

<span class="kn">from</span> <span class="nn">..</span> <span class="kn">import</span> <span class="n">cdp</span>
<span class="kn">from</span> <span class="nn">.</span> <span class="kn">import</span> <span class="n">tab</span><span class="p">,</span> <span class="n">util</span>
<span class="kn">from</span> <span class="nn">._contradict</span> <span class="kn">import</span> <span class="n">ContraDict</span>
<span class="kn">from</span> <span class="nn">.config</span> <span class="kn">import</span> <span class="n">Config</span><span class="p">,</span> <span class="n">PathLike</span><span class="p">,</span> <span class="n">is_posix</span>
<span class="kn">from</span> <span class="nn">.connection</span> <span class="kn">import</span> <span class="n">Connection</span>

<span class="n">logger</span> <span class="o">=</span> <span class="n">logging</span><span class="o">.</span><span class="n">getLogger</span><span class="p">(</span><span class="vm">__name__</span><span class="p">)</span>


<div class="viewcode-block" id="Browser">
<a class="viewcode-back" href="../../../nodriver/classes/browser.html#nodriver.Browser">[docs]</a>
<span class="k">class</span> <span class="nc">Browser</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    The Browser object is the &quot;root&quot; of the hierarchy and contains a reference</span>
<span class="sd">    to the browser parent process.</span>
<span class="sd">    there should usually be only 1 instance of this.</span>

<span class="sd">    All opened tabs, extra browser screens and resources will not cause a new Browser process,</span>
<span class="sd">    but rather create additional :class:`nodriver.Tab` objects.</span>

<span class="sd">    So, besides starting your instance and first/additional tabs, you don&#39;t actively use it a lot under normal conditions.</span>

<span class="sd">    Tab objects will represent and control</span>
<span class="sd">     - tabs (as you know them)</span>
<span class="sd">     - browser windows (new window)</span>
<span class="sd">     - iframe</span>
<span class="sd">     - background processes</span>

<span class="sd">    note:</span>
<span class="sd">    the Browser object is not instantiated by __init__ but using the asynchronous :meth:`nodriver.Browser.create` method.</span>

<span class="sd">    note:</span>
<span class="sd">    in Chromium based browsers, there is a parent process which keeps running all the time, even if</span>
<span class="sd">    there are no visible browser windows. sometimes it&#39;s stubborn to close it, so make sure after using</span>
<span class="sd">    this library, the browser is correctly and fully closed/exited/killed.</span>

<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">_process</span><span class="p">:</span> <span class="n">asyncio</span><span class="o">.</span><span class="n">subprocess</span><span class="o">.</span><span class="n">Process</span>
    <span class="n">_process_pid</span><span class="p">:</span> <span class="nb">int</span>
    <span class="n">_http</span><span class="p">:</span> <span class="n">HTTPApi</span> <span class="o">=</span> <span class="kc">None</span>
    <span class="n">_cookies</span><span class="p">:</span> <span class="n">CookieJar</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="n">config</span><span class="p">:</span> <span class="n">Config</span>
    <span class="n">connection</span><span class="p">:</span> <span class="n">Connection</span>

<div class="viewcode-block" id="Browser.create">
<a class="viewcode-back" href="../../../nodriver/classes/browser.html#nodriver.Browser.create">[docs]</a>
    <span class="nd">@classmethod</span>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">create</span><span class="p">(</span>
        <span class="bp">cls</span><span class="p">,</span>
        <span class="n">config</span><span class="p">:</span> <span class="n">Config</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
        <span class="o">*</span><span class="p">,</span>
        <span class="n">user_data_dir</span><span class="p">:</span> <span class="n">PathLike</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
        <span class="n">headless</span><span class="p">:</span> <span class="nb">bool</span> <span class="o">=</span> <span class="kc">False</span><span class="p">,</span>
        <span class="n">browser_executable_path</span><span class="p">:</span> <span class="n">PathLike</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
        <span class="n">browser_args</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
        <span class="n">sandbox</span><span class="p">:</span> <span class="nb">bool</span> <span class="o">=</span> <span class="kc">True</span><span class="p">,</span>
        <span class="n">host</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
        <span class="n">port</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
        <span class="o">**</span><span class="n">kwargs</span><span class="p">,</span>
    <span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Browser</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        entry point for creating an instance</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="n">config</span><span class="p">:</span>
            <span class="n">config</span> <span class="o">=</span> <span class="n">Config</span><span class="p">(</span>
                <span class="n">user_data_dir</span><span class="o">=</span><span class="n">user_data_dir</span><span class="p">,</span>
                <span class="n">headless</span><span class="o">=</span><span class="n">headless</span><span class="p">,</span>
                <span class="n">browser_executable_path</span><span class="o">=</span><span class="n">browser_executable_path</span><span class="p">,</span>
                <span class="n">browser_args</span><span class="o">=</span><span class="n">browser_args</span> <span class="ow">or</span> <span class="p">[],</span>
                <span class="n">sandbox</span><span class="o">=</span><span class="n">sandbox</span><span class="p">,</span>
                <span class="n">host</span><span class="o">=</span><span class="n">host</span><span class="p">,</span>
                <span class="n">port</span><span class="o">=</span><span class="n">port</span><span class="p">,</span>
                <span class="o">**</span><span class="n">kwargs</span><span class="p">,</span>
            <span class="p">)</span>
        <span class="n">instance</span> <span class="o">=</span> <span class="bp">cls</span><span class="p">(</span><span class="n">config</span><span class="p">)</span>
        <span class="k">await</span> <span class="n">instance</span><span class="o">.</span><span class="n">start</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">instance</span></div>


    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">config</span><span class="p">:</span> <span class="n">Config</span><span class="p">,</span> <span class="o">**</span><span class="n">kwargs</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        constructor. to create a instance, use :py:meth:`Browser.create(...)`</span>

<span class="sd">        :param config:</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">try</span><span class="p">:</span>
            <span class="n">asyncio</span><span class="o">.</span><span class="n">get_running_loop</span><span class="p">()</span>
        <span class="k">except</span> <span class="ne">RuntimeError</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">RuntimeError</span><span class="p">(</span>
                <span class="s2">&quot;</span><span class="si">{0}</span><span class="s2"> objects of this class are created using await </span><span class="si">{0}</span><span class="s2">.create()&quot;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span>
                    <span class="bp">self</span><span class="o">.</span><span class="vm">__class__</span><span class="o">.</span><span class="vm">__name__</span>
                <span class="p">)</span>
            <span class="p">)</span>
        <span class="c1"># weakref.finalize(self, self._quit, self)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">config</span> <span class="o">=</span> <span class="n">config</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">targets</span><span class="p">:</span> <span class="n">List</span> <span class="o">=</span> <span class="p">[]</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;current targets (all types&quot;&quot;&quot;</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">info</span> <span class="o">=</span> <span class="kc">None</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_target</span> <span class="o">=</span> <span class="kc">None</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_process</span> <span class="o">=</span> <span class="kc">None</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_process_pid</span> <span class="o">=</span> <span class="kc">None</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_keep_user_data_dir</span> <span class="o">=</span> <span class="kc">None</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_is_updating</span> <span class="o">=</span> <span class="n">asyncio</span><span class="o">.</span><span class="n">Event</span><span class="p">()</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">connection</span><span class="p">:</span> <span class="n">Connection</span> <span class="o">=</span> <span class="kc">None</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span><span class="s2">&quot;Session object initialized: </span><span class="si">%s</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="nb">vars</span><span class="p">(</span><span class="bp">self</span><span class="p">))</span>

    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">websocket_url</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">info</span><span class="o">.</span><span class="n">webSocketDebuggerUrl</span>

    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">main_tab</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">tab</span><span class="o">.</span><span class="n">Tab</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;returns the target which was launched with the browser&quot;&quot;&quot;</span>
        <span class="k">return</span> <span class="nb">sorted</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">targets</span><span class="p">,</span> <span class="n">key</span><span class="o">=</span><span class="k">lambda</span> <span class="n">x</span><span class="p">:</span> <span class="n">x</span><span class="o">.</span><span class="n">type_</span> <span class="o">==</span> <span class="s2">&quot;page&quot;</span><span class="p">,</span> <span class="n">reverse</span><span class="o">=</span><span class="kc">True</span><span class="p">)[</span><span class="mi">0</span><span class="p">]</span>

    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">tabs</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">List</span><span class="p">[</span><span class="n">tab</span><span class="o">.</span><span class="n">Tab</span><span class="p">]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;returns the current targets which are of type &quot;page&quot;</span>
<span class="sd">        :return:</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">tabs</span> <span class="o">=</span> <span class="nb">filter</span><span class="p">(</span><span class="k">lambda</span> <span class="n">item</span><span class="p">:</span> <span class="n">item</span><span class="o">.</span><span class="n">type_</span> <span class="o">==</span> <span class="s2">&quot;page&quot;</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">targets</span><span class="p">)</span>
        <span class="k">return</span> <span class="nb">list</span><span class="p">(</span><span class="n">tabs</span><span class="p">)</span>

    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">cookies</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">CookieJar</span><span class="p">:</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">_cookies</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_cookies</span> <span class="o">=</span> <span class="n">CookieJar</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_cookies</span>

    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">stopped</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">_process</span> <span class="ow">and</span> <span class="bp">self</span><span class="o">.</span><span class="n">_process</span><span class="o">.</span><span class="n">returncode</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">return</span> <span class="kc">False</span>
        <span class="k">return</span> <span class="kc">True</span>
        <span class="c1"># return (self._process and self._process.returncode) or False</span>

<div class="viewcode-block" id="Browser.wait">
<a class="viewcode-back" href="../../../nodriver/classes/browser.html#nodriver.Browser.wait">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">wait</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">time</span><span class="p">:</span> <span class="n">Union</span><span class="p">[</span><span class="nb">float</span><span class="p">,</span> <span class="nb">int</span><span class="p">]</span> <span class="o">=</span> <span class="mf">0.1</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;wait for &lt;time&gt; seconds. important to use, especially in between page navigation</span>

<span class="sd">        :param time:</span>
<span class="sd">        :return:</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">try</span><span class="p">:</span>
            <span class="k">await</span> <span class="n">asyncio</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="n">time</span><span class="p">)</span>
        <span class="k">except</span> <span class="n">asyncio</span><span class="o">.</span><span class="n">TimeoutError</span><span class="p">:</span>
            <span class="k">pass</span></div>


    <span class="n">sleep</span> <span class="o">=</span> <span class="n">wait</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;alias for wait&quot;&quot;&quot;</span>

    <span class="k">def</span> <span class="nf">_handle_target_update</span><span class="p">(</span>
        <span class="bp">self</span><span class="p">,</span>
        <span class="n">event</span><span class="p">:</span> <span class="n">Union</span><span class="p">[</span>
            <span class="n">cdp</span><span class="o">.</span><span class="n">target</span><span class="o">.</span><span class="n">TargetInfoChanged</span><span class="p">,</span>
            <span class="n">cdp</span><span class="o">.</span><span class="n">target</span><span class="o">.</span><span class="n">TargetDestroyed</span><span class="p">,</span>
            <span class="n">cdp</span><span class="o">.</span><span class="n">target</span><span class="o">.</span><span class="n">TargetCreated</span><span class="p">,</span>
            <span class="n">cdp</span><span class="o">.</span><span class="n">target</span><span class="o">.</span><span class="n">TargetCrashed</span><span class="p">,</span>
        <span class="p">],</span>
    <span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;this is an internal handler which updates the targets when chrome emits the corresponding event&quot;&quot;&quot;</span>

        <span class="k">if</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">event</span><span class="p">,</span> <span class="n">cdp</span><span class="o">.</span><span class="n">target</span><span class="o">.</span><span class="n">TargetInfoChanged</span><span class="p">):</span>
            <span class="n">target_info</span> <span class="o">=</span> <span class="n">event</span><span class="o">.</span><span class="n">target_info</span>

            <span class="n">current_tab</span> <span class="o">=</span> <span class="nb">next</span><span class="p">(</span>
                <span class="nb">filter</span><span class="p">(</span>
                    <span class="k">lambda</span> <span class="n">item</span><span class="p">:</span> <span class="n">item</span><span class="o">.</span><span class="n">target_id</span> <span class="o">==</span> <span class="n">target_info</span><span class="o">.</span><span class="n">target_id</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">targets</span>
                <span class="p">)</span>
            <span class="p">)</span>
            <span class="n">current_target</span> <span class="o">=</span> <span class="n">current_tab</span><span class="o">.</span><span class="n">target</span>

            <span class="k">if</span> <span class="n">logger</span><span class="o">.</span><span class="n">getEffectiveLevel</span><span class="p">()</span> <span class="o">&lt;=</span> <span class="mi">10</span><span class="p">:</span>
                <span class="n">changes</span> <span class="o">=</span> <span class="n">util</span><span class="o">.</span><span class="n">compare_target_info</span><span class="p">(</span><span class="n">current_target</span><span class="p">,</span> <span class="n">target_info</span><span class="p">)</span>
                <span class="n">changes_string</span> <span class="o">=</span> <span class="s2">&quot;&quot;</span>
                <span class="k">for</span> <span class="n">change</span> <span class="ow">in</span> <span class="n">changes</span><span class="p">:</span>
                    <span class="n">key</span><span class="p">,</span> <span class="n">old</span><span class="p">,</span> <span class="n">new</span> <span class="o">=</span> <span class="n">change</span>
                    <span class="n">changes_string</span> <span class="o">+=</span> <span class="sa">f</span><span class="s2">&quot;</span><span class="se">\n</span><span class="si">{</span><span class="n">key</span><span class="si">}</span><span class="s2">: </span><span class="si">{</span><span class="n">old</span><span class="si">}</span><span class="s2"> =&gt; </span><span class="si">{</span><span class="n">new</span><span class="si">}</span><span class="se">\n</span><span class="s2">&quot;</span>
                <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span>
                    <span class="s2">&quot;target #</span><span class="si">%d</span><span class="s2"> has changed: </span><span class="si">%s</span><span class="s2">&quot;</span>
                    <span class="o">%</span> <span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">targets</span><span class="o">.</span><span class="n">index</span><span class="p">(</span><span class="n">current_tab</span><span class="p">),</span> <span class="n">changes_string</span><span class="p">)</span>
                <span class="p">)</span>

                <span class="n">current_tab</span><span class="o">.</span><span class="n">_target</span> <span class="o">=</span> <span class="n">target_info</span>

        <span class="k">elif</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">event</span><span class="p">,</span> <span class="n">cdp</span><span class="o">.</span><span class="n">target</span><span class="o">.</span><span class="n">TargetCreated</span><span class="p">):</span>
            <span class="n">target_info</span><span class="p">:</span> <span class="n">cdp</span><span class="o">.</span><span class="n">target</span><span class="o">.</span><span class="n">TargetInfo</span> <span class="o">=</span> <span class="n">event</span><span class="o">.</span><span class="n">target_info</span>
            <span class="kn">from</span> <span class="nn">.tab</span> <span class="kn">import</span> <span class="n">Tab</span>

            <span class="n">new_target</span> <span class="o">=</span> <span class="n">Tab</span><span class="p">(</span>
                <span class="p">(</span>
                    <span class="sa">f</span><span class="s2">&quot;ws://</span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">config</span><span class="o">.</span><span class="n">host</span><span class="si">}</span><span class="s2">:</span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">config</span><span class="o">.</span><span class="n">port</span><span class="si">}</span><span class="s2">&quot;</span>
                    <span class="sa">f</span><span class="s2">&quot;/devtools/</span><span class="si">{</span><span class="n">target_info</span><span class="o">.</span><span class="n">type_</span><span class="w"> </span><span class="ow">or</span><span class="w"> </span><span class="s1">&#39;page&#39;</span><span class="si">}</span><span class="s2">&quot;</span>  <span class="c1"># all types are &#39;page&#39; internally in chrome apparently</span>
                    <span class="sa">f</span><span class="s2">&quot;/</span><span class="si">{</span><span class="n">target_info</span><span class="o">.</span><span class="n">target_id</span><span class="si">}</span><span class="s2">&quot;</span>
                <span class="p">),</span>
                <span class="n">target</span><span class="o">=</span><span class="n">target_info</span><span class="p">,</span>
                <span class="n">browser</span><span class="o">=</span><span class="bp">self</span><span class="p">,</span>
            <span class="p">)</span>

            <span class="bp">self</span><span class="o">.</span><span class="n">targets</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">new_target</span><span class="p">)</span>

            <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span><span class="s2">&quot;target #</span><span class="si">%d</span><span class="s2"> created =&gt; </span><span class="si">%s</span><span class="s2">&quot;</span><span class="p">,</span> <span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">targets</span><span class="p">),</span> <span class="n">new_target</span><span class="p">)</span>

        <span class="k">elif</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">event</span><span class="p">,</span> <span class="n">cdp</span><span class="o">.</span><span class="n">target</span><span class="o">.</span><span class="n">TargetDestroyed</span><span class="p">):</span>
            <span class="n">current_tab</span> <span class="o">=</span> <span class="nb">next</span><span class="p">(</span>
                <span class="nb">filter</span><span class="p">(</span><span class="k">lambda</span> <span class="n">item</span><span class="p">:</span> <span class="n">item</span><span class="o">.</span><span class="n">target_id</span> <span class="o">==</span> <span class="n">event</span><span class="o">.</span><span class="n">target_id</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">targets</span><span class="p">)</span>
            <span class="p">)</span>
            <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span>
                <span class="s2">&quot;target removed. id # </span><span class="si">%d</span><span class="s2"> =&gt; </span><span class="si">%s</span><span class="s2">&quot;</span>
                <span class="o">%</span> <span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">targets</span><span class="o">.</span><span class="n">index</span><span class="p">(</span><span class="n">current_tab</span><span class="p">),</span> <span class="n">current_tab</span><span class="p">)</span>
            <span class="p">)</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">targets</span><span class="o">.</span><span class="n">remove</span><span class="p">(</span><span class="n">current_tab</span><span class="p">)</span>

        <span class="n">asyncio</span><span class="o">.</span><span class="n">create_task</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">update_targets</span><span class="p">())</span>

<div class="viewcode-block" id="Browser.get">
<a class="viewcode-back" href="../../../nodriver/classes/browser.html#nodriver.Browser.get">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">get</span><span class="p">(</span>
        <span class="bp">self</span><span class="p">,</span> <span class="n">url</span><span class="o">=</span><span class="s2">&quot;chrome://welcome&quot;</span><span class="p">,</span> <span class="n">new_tab</span><span class="p">:</span> <span class="nb">bool</span> <span class="o">=</span> <span class="kc">False</span><span class="p">,</span> <span class="n">new_window</span><span class="p">:</span> <span class="nb">bool</span> <span class="o">=</span> <span class="kc">False</span>
    <span class="p">)</span> <span class="o">-&gt;</span> <span class="n">tab</span><span class="o">.</span><span class="n">Tab</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;top level get. utilizes the first tab to retrieve given url.</span>

<span class="sd">        convenience function known from selenium.</span>
<span class="sd">        this function handles waits/sleeps and detects when DOM events fired, so it&#39;s the safest</span>
<span class="sd">        way of navigating.</span>

<span class="sd">        :param url: the url to navigate to</span>
<span class="sd">        :param new_tab: open new tab</span>
<span class="sd">        :param new_window:  open new window</span>
<span class="sd">        :return: Page</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="n">new_tab</span> <span class="ow">or</span> <span class="n">new_window</span><span class="p">:</span>
            <span class="c1"># creat new target using the browser session</span>
            <span class="n">target_id</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">connection</span><span class="o">.</span><span class="n">send</span><span class="p">(</span>
                <span class="n">cdp</span><span class="o">.</span><span class="n">target</span><span class="o">.</span><span class="n">create_target</span><span class="p">(</span>
                    <span class="n">url</span><span class="p">,</span> <span class="n">new_window</span><span class="o">=</span><span class="n">new_window</span><span class="p">,</span> <span class="n">enable_begin_frame_control</span><span class="o">=</span><span class="kc">True</span>
                <span class="p">)</span>
            <span class="p">)</span>
            <span class="c1"># get the connection matching the new target_id from our inventory</span>
            <span class="n">connection</span><span class="p">:</span> <span class="n">tab</span><span class="o">.</span><span class="n">Tab</span> <span class="o">=</span> <span class="nb">next</span><span class="p">(</span>
                <span class="nb">filter</span><span class="p">(</span>
                    <span class="k">lambda</span> <span class="n">item</span><span class="p">:</span> <span class="n">item</span><span class="o">.</span><span class="n">type_</span> <span class="o">==</span> <span class="s2">&quot;page&quot;</span> <span class="ow">and</span> <span class="n">item</span><span class="o">.</span><span class="n">target_id</span> <span class="o">==</span> <span class="n">target_id</span><span class="p">,</span>
                    <span class="bp">self</span><span class="o">.</span><span class="n">targets</span><span class="p">,</span>
                <span class="p">)</span>
            <span class="p">)</span>
            <span class="n">connection</span><span class="o">.</span><span class="n">_browser</span> <span class="o">=</span> <span class="bp">self</span>

        <span class="k">else</span><span class="p">:</span>
            <span class="c1"># first tab from browser.tabs</span>
            <span class="n">connection</span><span class="p">:</span> <span class="n">tab</span><span class="o">.</span><span class="n">Tab</span> <span class="o">=</span> <span class="nb">next</span><span class="p">(</span>
                <span class="nb">filter</span><span class="p">(</span><span class="k">lambda</span> <span class="n">item</span><span class="p">:</span> <span class="n">item</span><span class="o">.</span><span class="n">type_</span> <span class="o">==</span> <span class="s2">&quot;page&quot;</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">targets</span><span class="p">)</span>
            <span class="p">)</span>
            <span class="c1"># use the tab to navigate to new url</span>
            <span class="n">frame_id</span><span class="p">,</span> <span class="n">loader_id</span><span class="p">,</span> <span class="o">*</span><span class="n">_</span> <span class="o">=</span> <span class="k">await</span> <span class="n">connection</span><span class="o">.</span><span class="n">send</span><span class="p">(</span><span class="n">cdp</span><span class="o">.</span><span class="n">page</span><span class="o">.</span><span class="n">navigate</span><span class="p">(</span><span class="n">url</span><span class="p">))</span>
            <span class="c1"># update the frame_id on the tab</span>
            <span class="n">connection</span><span class="o">.</span><span class="n">frame_id</span> <span class="o">=</span> <span class="n">frame_id</span>
            <span class="n">connection</span><span class="o">.</span><span class="n">_browser</span> <span class="o">=</span> <span class="bp">self</span>

        <span class="k">await</span> <span class="bp">self</span>
        <span class="k">return</span> <span class="n">connection</span></div>


<div class="viewcode-block" id="Browser.create_context">
<a class="viewcode-back" href="../../../nodriver/classes/browser.html#nodriver.Browser.create_context">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">create_context</span><span class="p">(</span>
        <span class="bp">self</span><span class="p">,</span>
        <span class="n">url</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="s2">&quot;chrome://welcome&quot;</span><span class="p">,</span>
        <span class="n">new_tab</span><span class="p">:</span> <span class="nb">bool</span> <span class="o">=</span> <span class="kc">False</span><span class="p">,</span>
        <span class="n">new_window</span><span class="p">:</span> <span class="nb">bool</span> <span class="o">=</span> <span class="kc">True</span><span class="p">,</span>
        <span class="n">dispose_on_detach</span><span class="p">:</span> <span class="nb">bool</span> <span class="o">=</span> <span class="kc">True</span><span class="p">,</span>
        <span class="n">proxy_server</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
        <span class="n">proxy_bypass_list</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
        <span class="n">origins_with_universal_network_access</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
    <span class="p">)</span> <span class="o">-&gt;</span> <span class="n">tab</span><span class="o">.</span><span class="n">Tab</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        creates a new browser context - mostly useful if you want to use proxies for different browser instances</span>
<span class="sd">        since chrome usually can only use 1 proxy per browser.</span>
<span class="sd">        socks5 with authentication is supported by using a forwarder proxy, the</span>
<span class="sd">        correct string to use socks proxy with username/password auth is socks://USERNAME:PASSWORD@SERVER:PORT</span>

<span class="sd">        dispose_on_detach – (EXPERIMENTAL) (Optional) If specified, disposes this context when debugging session disconnects.</span>
<span class="sd">        proxy_server – (EXPERIMENTAL) (Optional) Proxy server, similar to the one passed to –proxy-server</span>
<span class="sd">        proxy_bypass_list – (EXPERIMENTAL) (Optional) Proxy bypass list, similar to the one passed to –proxy-bypass-list</span>
<span class="sd">        origins_with_universal_network_access – (EXPERIMENTAL) (Optional) An optional list of origins to grant unlimited cross-origin access to. Parts of the URL other than those constituting origin are ignored.</span>

<span class="sd">        :param new_window:</span>
<span class="sd">        :type new_window:</span>
<span class="sd">        :param new_tab:</span>
<span class="sd">        :type new_tab:</span>
<span class="sd">        :param url:</span>
<span class="sd">        :type url:</span>
<span class="sd">        :param dispose_on_detach:</span>
<span class="sd">        :type dispose_on_detach:</span>
<span class="sd">        :param proxy_server:</span>
<span class="sd">        :type proxy_server:</span>
<span class="sd">        :param proxy_bypass_list:</span>
<span class="sd">        :type proxy_bypass_list:</span>
<span class="sd">        :param origins_with_universal_network_access:</span>
<span class="sd">        :type origins_with_universal_network_access:</span>
<span class="sd">        :return:</span>
<span class="sd">        :rtype:</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="n">proxy_server</span><span class="p">:</span>
            <span class="n">fw</span> <span class="o">=</span> <span class="n">util</span><span class="o">.</span><span class="n">ProxyForwarder</span><span class="p">(</span><span class="n">proxy_server</span><span class="o">=</span><span class="n">proxy_server</span><span class="p">)</span>
            <span class="n">proxy_server</span> <span class="o">=</span> <span class="n">fw</span><span class="o">.</span><span class="n">proxy_server</span>

        <span class="n">ctx</span><span class="p">:</span> <span class="n">cdp</span><span class="o">.</span><span class="n">browser</span><span class="o">.</span><span class="n">BrowserContextID</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">connection</span><span class="o">.</span><span class="n">send</span><span class="p">(</span>
            <span class="n">cdp</span><span class="o">.</span><span class="n">target</span><span class="o">.</span><span class="n">create_browser_context</span><span class="p">(</span>
                <span class="n">dispose_on_detach</span><span class="o">=</span><span class="n">dispose_on_detach</span><span class="p">,</span>
                <span class="n">proxy_server</span><span class="o">=</span><span class="n">proxy_server</span><span class="p">,</span>
                <span class="n">proxy_bypass_list</span><span class="o">=</span><span class="n">proxy_bypass_list</span><span class="p">,</span>
                <span class="n">origins_with_universal_network_access</span><span class="o">=</span><span class="n">origins_with_universal_network_access</span><span class="p">,</span>
            <span class="p">)</span>
        <span class="p">)</span>
        <span class="n">target_id</span><span class="p">:</span> <span class="n">cdp</span><span class="o">.</span><span class="n">target</span><span class="o">.</span><span class="n">TargetID</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">connection</span><span class="o">.</span><span class="n">send</span><span class="p">(</span>
            <span class="n">cdp</span><span class="o">.</span><span class="n">target</span><span class="o">.</span><span class="n">create_target</span><span class="p">(</span>
                <span class="n">url</span><span class="p">,</span> <span class="n">browser_context_id</span><span class="o">=</span><span class="n">ctx</span><span class="p">,</span> <span class="n">new_window</span><span class="o">=</span><span class="n">new_window</span><span class="p">,</span> <span class="n">for_tab</span><span class="o">=</span><span class="n">new_tab</span>
            <span class="p">)</span>
        <span class="p">)</span>
        <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.5</span><span class="p">)</span>
        <span class="n">connection</span><span class="p">:</span> <span class="n">tab</span><span class="o">.</span><span class="n">Tab</span> <span class="o">=</span> <span class="nb">next</span><span class="p">(</span>
            <span class="nb">filter</span><span class="p">(</span>
                <span class="k">lambda</span> <span class="n">item</span><span class="p">:</span> <span class="n">item</span><span class="o">.</span><span class="n">type_</span> <span class="o">==</span> <span class="s2">&quot;page&quot;</span> <span class="ow">and</span> <span class="n">item</span><span class="o">.</span><span class="n">target_id</span> <span class="o">==</span> <span class="n">target_id</span><span class="p">,</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">targets</span><span class="p">,</span>
            <span class="p">)</span>
        <span class="p">)</span>
        <span class="k">return</span> <span class="n">connection</span></div>


<div class="viewcode-block" id="Browser.start">
<a class="viewcode-back" href="../../../nodriver/classes/browser.html#nodriver.Browser.start">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">start</span><span class="p">(</span><span class="bp">self</span><span class="o">=</span><span class="kc">None</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Browser</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;launches the actual browser&quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="p">:</span>
            <span class="n">warnings</span><span class="o">.</span><span class="n">warn</span><span class="p">(</span><span class="s2">&quot;use ``await Browser.create()`` to create a new instance&quot;</span><span class="p">)</span>
            <span class="k">return</span>

        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">_process</span> <span class="ow">or</span> <span class="bp">self</span><span class="o">.</span><span class="n">_process_pid</span><span class="p">:</span>
            <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">_process</span><span class="o">.</span><span class="n">returncode</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
                <span class="k">return</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">create</span><span class="p">(</span><span class="n">config</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">config</span><span class="p">)</span>
            <span class="n">warnings</span><span class="o">.</span><span class="n">warn</span><span class="p">(</span><span class="s2">&quot;ignored! this call has no effect when already running.&quot;</span><span class="p">)</span>
            <span class="k">return</span>

        <span class="c1"># self.config.update(kwargs)</span>
        <span class="n">connect_existing</span> <span class="o">=</span> <span class="kc">False</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">config</span><span class="o">.</span><span class="n">host</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span> <span class="ow">and</span> <span class="bp">self</span><span class="o">.</span><span class="n">config</span><span class="o">.</span><span class="n">port</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">connect_existing</span> <span class="o">=</span> <span class="kc">True</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">config</span><span class="o">.</span><span class="n">host</span> <span class="o">=</span> <span class="s2">&quot;127.0.0.1&quot;</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">config</span><span class="o">.</span><span class="n">port</span> <span class="o">=</span> <span class="n">util</span><span class="o">.</span><span class="n">free_port</span><span class="p">()</span>

        <span class="k">if</span> <span class="ow">not</span> <span class="n">connect_existing</span><span class="p">:</span>
            <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span>
                <span class="s2">&quot;BROWSER EXECUTABLE PATH: </span><span class="si">%s</span><span class="s2">&quot;</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">config</span><span class="o">.</span><span class="n">browser_executable_path</span>
            <span class="p">)</span>
            <span class="k">if</span> <span class="ow">not</span> <span class="n">pathlib</span><span class="o">.</span><span class="n">Path</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">config</span><span class="o">.</span><span class="n">browser_executable_path</span><span class="p">)</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
                <span class="k">raise</span> <span class="ne">FileNotFoundError</span><span class="p">(</span>
                    <span class="p">(</span>
<span class="w">                        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">                    ---------------------</span>
<span class="sd">                    Could not determine browser executable.</span>
<span class="sd">                    ---------------------</span>
<span class="sd">                    Make sure your browser is installed in the default location (path).</span>
<span class="sd">                    If you are sure about the browser executable, you can specify it using</span>
<span class="sd">                    the `browser_executable_path=&#39;{}` parameter.&quot;&quot;&quot;</span>
                    <span class="p">)</span><span class="o">.</span><span class="n">format</span><span class="p">(</span>
                        <span class="s2">&quot;/path/to/browser/executable&quot;</span>
                        <span class="k">if</span> <span class="n">is_posix</span>
                        <span class="k">else</span> <span class="s2">&quot;c:/path/to/your/browser.exe&quot;</span>
                    <span class="p">)</span>
                <span class="p">)</span>

        <span class="k">if</span> <span class="nb">getattr</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">config</span><span class="p">,</span> <span class="s2">&quot;_extensions&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">):</span>  <span class="c1"># noqa</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">config</span><span class="o">.</span><span class="n">add_argument</span><span class="p">(</span>
                <span class="s2">&quot;--load-extension=</span><span class="si">%s</span><span class="s2">&quot;</span>
                <span class="o">%</span> <span class="s2">&quot;,&quot;</span><span class="o">.</span><span class="n">join</span><span class="p">(</span><span class="nb">str</span><span class="p">(</span><span class="n">_</span><span class="p">)</span> <span class="k">for</span> <span class="n">_</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">config</span><span class="o">.</span><span class="n">_extensions</span><span class="p">)</span>
            <span class="p">)</span>  <span class="c1"># noqa</span>

        <span class="n">exe</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">config</span><span class="o">.</span><span class="n">browser_executable_path</span>
        <span class="n">params</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">config</span><span class="p">()</span>

        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span>
            <span class="s2">&quot;starting</span><span class="se">\n\t</span><span class="s2">executable :</span><span class="si">%s</span><span class="se">\n\n</span><span class="s2">arguments:</span><span class="se">\n</span><span class="si">%s</span><span class="s2">&quot;</span><span class="p">,</span> <span class="n">exe</span><span class="p">,</span> <span class="s2">&quot;</span><span class="se">\n\t</span><span class="s2">&quot;</span><span class="o">.</span><span class="n">join</span><span class="p">(</span><span class="n">params</span><span class="p">)</span>
        <span class="p">)</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="n">connect_existing</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_process</span><span class="p">:</span> <span class="n">asyncio</span><span class="o">.</span><span class="n">subprocess</span><span class="o">.</span><span class="n">Process</span> <span class="o">=</span> <span class="p">(</span>
                <span class="k">await</span> <span class="n">asyncio</span><span class="o">.</span><span class="n">create_subprocess_exec</span><span class="p">(</span>
                    <span class="c1"># self.config.browser_executable_path,</span>
                    <span class="c1"># *cmdparams,</span>
                    <span class="n">exe</span><span class="p">,</span>
                    <span class="o">*</span><span class="n">params</span><span class="p">,</span>
                    <span class="n">stdin</span><span class="o">=</span><span class="n">asyncio</span><span class="o">.</span><span class="n">subprocess</span><span class="o">.</span><span class="n">PIPE</span><span class="p">,</span>
                    <span class="n">stdout</span><span class="o">=</span><span class="n">asyncio</span><span class="o">.</span><span class="n">subprocess</span><span class="o">.</span><span class="n">PIPE</span><span class="p">,</span>
                    <span class="n">stderr</span><span class="o">=</span><span class="n">asyncio</span><span class="o">.</span><span class="n">subprocess</span><span class="o">.</span><span class="n">PIPE</span><span class="p">,</span>
                    <span class="n">close_fds</span><span class="o">=</span><span class="n">is_posix</span><span class="p">,</span>
                <span class="p">)</span>
            <span class="p">)</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_process_pid</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_process</span><span class="o">.</span><span class="n">pid</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">_http</span> <span class="o">=</span> <span class="n">HTTPApi</span><span class="p">((</span><span class="bp">self</span><span class="o">.</span><span class="n">config</span><span class="o">.</span><span class="n">host</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">config</span><span class="o">.</span><span class="n">port</span><span class="p">))</span>
        <span class="n">util</span><span class="o">.</span><span class="n">get_registered_instances</span><span class="p">()</span><span class="o">.</span><span class="n">add</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span>
        <span class="k">await</span> <span class="n">asyncio</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.25</span><span class="p">)</span>
        <span class="k">for</span> <span class="n">_</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">5</span><span class="p">):</span>
            <span class="k">try</span><span class="p">:</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">info</span> <span class="o">=</span> <span class="n">ContraDict</span><span class="p">(</span><span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">_http</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;version&quot;</span><span class="p">),</span> <span class="n">silent</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
            <span class="k">except</span> <span class="p">(</span><span class="ne">Exception</span><span class="p">,):</span>
                <span class="k">if</span> <span class="n">_</span> <span class="o">==</span> <span class="mi">4</span><span class="p">:</span>
                    <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span><span class="s2">&quot;could not start&quot;</span><span class="p">,</span> <span class="n">exc_info</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
                <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.5</span><span class="p">)</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="k">break</span>

        <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">info</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">Exception</span><span class="p">(</span>
                <span class="p">(</span>
<span class="w">                    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">                ---------------------</span>
<span class="sd">                Failed to connect to browser</span>
<span class="sd">                ---------------------</span>
<span class="sd">                One of the causes could be when you are running as root.</span>
<span class="sd">                In that case you need to pass no_sandbox=True </span>
<span class="sd">                &quot;&quot;&quot;</span>
                <span class="p">)</span>
            <span class="p">)</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">connection</span> <span class="o">=</span> <span class="n">Connection</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">info</span><span class="o">.</span><span class="n">webSocketDebuggerUrl</span><span class="p">,</span> <span class="n">browser</span><span class="o">=</span><span class="bp">self</span><span class="p">)</span>

        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">config</span><span class="o">.</span><span class="n">autodiscover_targets</span><span class="p">:</span>
            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;enabling autodiscover targets&quot;</span><span class="p">)</span>

            <span class="bp">self</span><span class="o">.</span><span class="n">connection</span><span class="o">.</span><span class="n">handlers</span><span class="p">[</span><span class="n">cdp</span><span class="o">.</span><span class="n">target</span><span class="o">.</span><span class="n">TargetInfoChanged</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">_handle_target_update</span>
            <span class="p">]</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">connection</span><span class="o">.</span><span class="n">handlers</span><span class="p">[</span><span class="n">cdp</span><span class="o">.</span><span class="n">target</span><span class="o">.</span><span class="n">TargetCreated</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">_handle_target_update</span>
            <span class="p">]</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">connection</span><span class="o">.</span><span class="n">handlers</span><span class="p">[</span><span class="n">cdp</span><span class="o">.</span><span class="n">target</span><span class="o">.</span><span class="n">TargetDestroyed</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">_handle_target_update</span>
            <span class="p">]</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">connection</span><span class="o">.</span><span class="n">handlers</span><span class="p">[</span><span class="n">cdp</span><span class="o">.</span><span class="n">target</span><span class="o">.</span><span class="n">TargetCrashed</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">_handle_target_update</span>
            <span class="p">]</span>
            <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">connection</span><span class="o">.</span><span class="n">send</span><span class="p">(</span><span class="n">cdp</span><span class="o">.</span><span class="n">target</span><span class="o">.</span><span class="n">set_discover_targets</span><span class="p">(</span><span class="n">discover</span><span class="o">=</span><span class="kc">True</span><span class="p">))</span>

        <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">update_targets</span><span class="p">()</span></div>


        <span class="c1"># await self</span>

        <span class="c1"># self.connection.handlers[cdp.inspector.Detached] = [self.stop]</span>
        <span class="c1"># return self</span>

<div class="viewcode-block" id="Browser.grant_all_permissions">
<a class="viewcode-back" href="../../../nodriver/classes/browser.html#nodriver.Browser.grant_all_permissions">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">grant_all_permissions</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        grant permissions for:</span>
<span class="sd">            accessibilityEvents</span>
<span class="sd">            audioCapture</span>
<span class="sd">            backgroundSync</span>
<span class="sd">            backgroundFetch</span>
<span class="sd">            clipboardReadWrite</span>
<span class="sd">            clipboardSanitizedWrite</span>
<span class="sd">            displayCapture</span>
<span class="sd">            durableStorage</span>
<span class="sd">            geolocation</span>
<span class="sd">            idleDetection</span>
<span class="sd">            localFonts</span>
<span class="sd">            midi</span>
<span class="sd">            midiSysex</span>
<span class="sd">            nfc</span>
<span class="sd">            notifications</span>
<span class="sd">            paymentHandler</span>
<span class="sd">            periodicBackgroundSync</span>
<span class="sd">            protectedMediaIdentifier</span>
<span class="sd">            sensors</span>
<span class="sd">            storageAccess</span>
<span class="sd">            topLevelStorageAccess</span>
<span class="sd">            videoCapture</span>
<span class="sd">            videoCapturePanTiltZoom</span>
<span class="sd">            wakeLockScreen</span>
<span class="sd">            wakeLockSystem</span>
<span class="sd">            windowManagement</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">permissions</span> <span class="o">=</span> <span class="nb">list</span><span class="p">(</span><span class="n">cdp</span><span class="o">.</span><span class="n">browser</span><span class="o">.</span><span class="n">PermissionType</span><span class="p">)</span>
        <span class="n">permissions</span><span class="o">.</span><span class="n">remove</span><span class="p">(</span><span class="n">cdp</span><span class="o">.</span><span class="n">browser</span><span class="o">.</span><span class="n">PermissionType</span><span class="o">.</span><span class="n">FLASH</span><span class="p">)</span>
        <span class="n">permissions</span><span class="o">.</span><span class="n">remove</span><span class="p">(</span><span class="n">cdp</span><span class="o">.</span><span class="n">browser</span><span class="o">.</span><span class="n">PermissionType</span><span class="o">.</span><span class="n">CAPTURED_SURFACE_CONTROL</span><span class="p">)</span>
        <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">connection</span><span class="o">.</span><span class="n">send</span><span class="p">(</span><span class="n">cdp</span><span class="o">.</span><span class="n">browser</span><span class="o">.</span><span class="n">grant_permissions</span><span class="p">(</span><span class="n">permissions</span><span class="p">))</span></div>


<div class="viewcode-block" id="Browser.tile_windows">
<a class="viewcode-back" href="../../../nodriver/classes/browser.html#nodriver.Browser.tile_windows">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">tile_windows</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">windows</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span> <span class="n">max_columns</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="mi">0</span><span class="p">):</span>
        <span class="kn">import</span> <span class="nn">math</span>

        <span class="kn">import</span> <span class="nn">mss</span>

        <span class="n">m</span> <span class="o">=</span> <span class="n">mss</span><span class="o">.</span><span class="n">mss</span><span class="p">()</span>
        <span class="n">screen</span><span class="p">,</span> <span class="n">screen_width</span><span class="p">,</span> <span class="n">screen_height</span> <span class="o">=</span> <span class="mi">3</span> <span class="o">*</span> <span class="p">(</span><span class="kc">None</span><span class="p">,)</span>
        <span class="k">if</span> <span class="n">m</span><span class="o">.</span><span class="n">monitors</span> <span class="ow">and</span> <span class="nb">len</span><span class="p">(</span><span class="n">m</span><span class="o">.</span><span class="n">monitors</span><span class="p">)</span> <span class="o">&gt;=</span> <span class="mi">1</span><span class="p">:</span>
            <span class="n">screen</span> <span class="o">=</span> <span class="n">m</span><span class="o">.</span><span class="n">monitors</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span>
            <span class="n">screen_width</span> <span class="o">=</span> <span class="n">screen</span><span class="p">[</span><span class="s2">&quot;width&quot;</span><span class="p">]</span>
            <span class="n">screen_height</span> <span class="o">=</span> <span class="n">screen</span><span class="p">[</span><span class="s2">&quot;height&quot;</span><span class="p">]</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="n">screen</span> <span class="ow">or</span> <span class="ow">not</span> <span class="n">screen_width</span> <span class="ow">or</span> <span class="ow">not</span> <span class="n">screen_height</span><span class="p">:</span>
            <span class="n">warnings</span><span class="o">.</span><span class="n">warn</span><span class="p">(</span><span class="s2">&quot;no monitors detected&quot;</span><span class="p">)</span>
            <span class="k">return</span>
        <span class="k">await</span> <span class="bp">self</span>
        <span class="n">distinct_windows</span> <span class="o">=</span> <span class="n">defaultdict</span><span class="p">(</span><span class="nb">list</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">windows</span><span class="p">:</span>
            <span class="n">tabs</span> <span class="o">=</span> <span class="n">windows</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">tabs</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">tabs</span>
        <span class="k">for</span> <span class="n">tab</span> <span class="ow">in</span> <span class="n">tabs</span><span class="p">:</span>
            <span class="n">window_id</span><span class="p">,</span> <span class="n">bounds</span> <span class="o">=</span> <span class="k">await</span> <span class="n">tab</span><span class="o">.</span><span class="n">get_window</span><span class="p">()</span>
            <span class="n">distinct_windows</span><span class="p">[</span><span class="n">window_id</span><span class="p">]</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">tab</span><span class="p">)</span>

        <span class="n">num_windows</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="n">distinct_windows</span><span class="p">)</span>
        <span class="n">req_cols</span> <span class="o">=</span> <span class="n">max_columns</span> <span class="ow">or</span> <span class="nb">int</span><span class="p">(</span><span class="n">num_windows</span> <span class="o">*</span> <span class="p">(</span><span class="mi">19</span> <span class="o">/</span> <span class="mi">6</span><span class="p">))</span>
        <span class="n">req_rows</span> <span class="o">=</span> <span class="nb">int</span><span class="p">(</span><span class="n">num_windows</span> <span class="o">/</span> <span class="n">req_cols</span><span class="p">)</span>

        <span class="k">while</span> <span class="n">req_cols</span> <span class="o">*</span> <span class="n">req_rows</span> <span class="o">&lt;</span> <span class="n">num_windows</span><span class="p">:</span>
            <span class="n">req_rows</span> <span class="o">+=</span> <span class="mi">1</span>

        <span class="n">box_w</span> <span class="o">=</span> <span class="n">math</span><span class="o">.</span><span class="n">floor</span><span class="p">((</span><span class="n">screen_width</span> <span class="o">/</span> <span class="n">req_cols</span><span class="p">)</span> <span class="o">-</span> <span class="mi">1</span><span class="p">)</span>
        <span class="n">box_h</span> <span class="o">=</span> <span class="n">math</span><span class="o">.</span><span class="n">floor</span><span class="p">(</span><span class="n">screen_height</span> <span class="o">/</span> <span class="n">req_rows</span><span class="p">)</span>

        <span class="n">distinct_windows_iter</span> <span class="o">=</span> <span class="nb">iter</span><span class="p">(</span><span class="n">distinct_windows</span><span class="o">.</span><span class="n">values</span><span class="p">())</span>
        <span class="n">grid</span> <span class="o">=</span> <span class="p">[]</span>
        <span class="k">for</span> <span class="n">x</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">req_cols</span><span class="p">):</span>
            <span class="k">for</span> <span class="n">y</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">req_rows</span><span class="p">):</span>
                <span class="n">num</span> <span class="o">=</span> <span class="n">x</span> <span class="o">+</span> <span class="n">y</span>
                <span class="k">try</span><span class="p">:</span>
                    <span class="n">tabs</span> <span class="o">=</span> <span class="nb">next</span><span class="p">(</span><span class="n">distinct_windows_iter</span><span class="p">)</span>
                <span class="k">except</span> <span class="ne">StopIteration</span><span class="p">:</span>
                    <span class="k">continue</span>
                <span class="k">if</span> <span class="ow">not</span> <span class="n">tabs</span><span class="p">:</span>
                    <span class="k">continue</span>
                <span class="n">tab</span> <span class="o">=</span> <span class="n">tabs</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span>

                <span class="k">try</span><span class="p">:</span>
                    <span class="n">pos</span> <span class="o">=</span> <span class="p">[</span><span class="n">x</span> <span class="o">*</span> <span class="n">box_w</span><span class="p">,</span> <span class="n">y</span> <span class="o">*</span> <span class="n">box_h</span><span class="p">,</span> <span class="n">box_w</span><span class="p">,</span> <span class="n">box_h</span><span class="p">]</span>
                    <span class="n">grid</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">pos</span><span class="p">)</span>
                    <span class="k">await</span> <span class="n">tab</span><span class="o">.</span><span class="n">set_window_size</span><span class="p">(</span><span class="o">*</span><span class="n">pos</span><span class="p">)</span>
                <span class="k">except</span> <span class="ne">Exception</span><span class="p">:</span>
                    <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span>
                        <span class="s2">&quot;could not set window size. exception =&gt; &quot;</span><span class="p">,</span> <span class="n">exc_info</span><span class="o">=</span><span class="kc">True</span>
                    <span class="p">)</span>
                    <span class="k">continue</span>
        <span class="k">return</span> <span class="n">grid</span></div>


    <span class="k">async</span> <span class="k">def</span> <span class="nf">_get_targets</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">List</span><span class="p">[</span><span class="n">cdp</span><span class="o">.</span><span class="n">target</span><span class="o">.</span><span class="n">TargetInfo</span><span class="p">]:</span>
        <span class="n">info</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">connection</span><span class="o">.</span><span class="n">send</span><span class="p">(</span><span class="n">cdp</span><span class="o">.</span><span class="n">target</span><span class="o">.</span><span class="n">get_targets</span><span class="p">(),</span> <span class="n">_is_update</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
        <span class="k">return</span> <span class="n">info</span>

<div class="viewcode-block" id="Browser.update_targets">
<a class="viewcode-back" href="../../../nodriver/classes/browser.html#nodriver.Browser.update_targets">[docs]</a>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">update_targets</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>

        <span class="n">targets</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="n">cdp</span><span class="o">.</span><span class="n">target</span><span class="o">.</span><span class="n">TargetInfo</span><span class="p">]</span>
        <span class="n">targets</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_targets</span><span class="p">()</span>
        <span class="n">target_ids</span> <span class="o">=</span> <span class="p">[</span><span class="n">t</span><span class="o">.</span><span class="n">target_id</span> <span class="k">for</span> <span class="n">t</span> <span class="ow">in</span> <span class="n">targets</span><span class="p">]</span>
        <span class="n">existing_target_ids</span> <span class="o">=</span> <span class="p">[</span><span class="n">t</span><span class="o">.</span><span class="n">target_id</span> <span class="k">for</span> <span class="n">t</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">targets</span><span class="p">]</span>
        <span class="k">for</span> <span class="n">t</span> <span class="ow">in</span> <span class="n">targets</span><span class="p">:</span>
            <span class="k">for</span> <span class="n">existing_tab</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">targets</span><span class="p">:</span>
                <span class="n">existing_target</span> <span class="o">=</span> <span class="n">existing_tab</span><span class="o">.</span><span class="n">target</span>
                <span class="k">if</span> <span class="n">existing_target</span><span class="o">.</span><span class="n">target_id</span> <span class="o">==</span> <span class="n">t</span><span class="o">.</span><span class="n">target_id</span><span class="p">:</span>
                    <span class="n">existing_tab</span><span class="o">.</span><span class="n">target</span><span class="o">.</span><span class="vm">__dict__</span><span class="o">.</span><span class="n">update</span><span class="p">(</span><span class="n">t</span><span class="o">.</span><span class="vm">__dict__</span><span class="p">)</span>
                    <span class="k">break</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">targets</span><span class="o">.</span><span class="n">append</span><span class="p">(</span>
                    <span class="n">Connection</span><span class="p">(</span>
                        <span class="p">(</span>
                            <span class="sa">f</span><span class="s2">&quot;ws://</span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">config</span><span class="o">.</span><span class="n">host</span><span class="si">}</span><span class="s2">:</span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">config</span><span class="o">.</span><span class="n">port</span><span class="si">}</span><span class="s2">&quot;</span>
                            <span class="sa">f</span><span class="s2">&quot;/devtools/page&quot;</span>  <span class="c1"># all types are &#39;page&#39; somehow</span>
                            <span class="sa">f</span><span class="s2">&quot;/</span><span class="si">{</span><span class="n">t</span><span class="o">.</span><span class="n">target_id</span><span class="si">}</span><span class="s2">&quot;</span>
                        <span class="p">),</span>
                        <span class="n">target</span><span class="o">=</span><span class="n">t</span><span class="p">,</span>
                        <span class="n">browser</span><span class="o">=</span><span class="bp">self</span><span class="p">,</span>
                    <span class="p">)</span>
                <span class="p">)</span>

        <span class="k">await</span> <span class="n">asyncio</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mi">0</span><span class="p">)</span></div>


    <span class="k">async</span> <span class="k">def</span> <span class="fm">__aenter__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="bp">self</span>

    <span class="k">async</span> <span class="k">def</span> <span class="fm">__aexit__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">exc_type</span><span class="p">,</span> <span class="n">exc_val</span><span class="p">,</span> <span class="n">exc_tb</span><span class="p">):</span>
        <span class="k">if</span> <span class="n">exc_type</span> <span class="ow">and</span> <span class="n">exc_val</span><span class="p">:</span>
            <span class="k">raise</span> <span class="n">exc_type</span><span class="p">(</span><span class="n">exc_val</span><span class="p">)</span>

    <span class="k">def</span> <span class="fm">__iter__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_i</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">tabs</span><span class="o">.</span><span class="n">index</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">main_tab</span><span class="p">)</span>
        <span class="k">return</span> <span class="bp">self</span>

    <span class="k">def</span> <span class="fm">__getitem__</span><span class="p">(</span>
        <span class="bp">self</span><span class="p">,</span> <span class="n">item</span><span class="p">:</span> <span class="n">Union</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="nb">int</span><span class="p">,</span> <span class="nb">slice</span><span class="p">]</span>
    <span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Union</span><span class="p">[</span><span class="n">tab</span><span class="o">.</span><span class="n">Tab</span><span class="p">,</span> <span class="n">List</span><span class="p">[</span><span class="n">tab</span><span class="o">.</span><span class="n">Tab</span><span class="p">]]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        allows to get py:obj:`tab.Tab` instances by using browser[0], browser[1], etc.</span>
<span class="sd">        a string is also allowed. it will then return the first tab where the py:obj:`cdp.target.TargetInfo` object</span>
<span class="sd">        (as json string) contains the given key, or the first tab in case no matches are found. eg:</span>
<span class="sd">        `browser[&quot;google&quot;]` gives the first tab which has &quot;google&quot; in it&#39;s serialized target object.</span>

<span class="sd">        :param item:</span>
<span class="sd">        :type item:</span>
<span class="sd">        :return:</span>
<span class="sd">        :rtype: tab.Tab</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">item</span><span class="p">,</span> <span class="nb">int</span><span class="p">):</span>
            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">tabs</span><span class="p">[</span><span class="n">item</span><span class="p">]</span>
        <span class="k">elif</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">item</span><span class="p">,</span> <span class="nb">slice</span><span class="p">):</span>
            <span class="n">tabs</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="n">tab</span><span class="o">.</span><span class="n">Tab</span><span class="p">]</span> <span class="o">=</span> <span class="p">[]</span>
            <span class="n">sta</span><span class="p">,</span> <span class="n">sto</span><span class="p">,</span> <span class="n">ste</span> <span class="o">=</span> <span class="n">item</span><span class="o">.</span><span class="n">start</span><span class="p">,</span> <span class="n">item</span><span class="o">.</span><span class="n">stop</span><span class="p">,</span> <span class="n">item</span><span class="o">.</span><span class="n">step</span>
            <span class="k">if</span> <span class="ow">not</span> <span class="n">ste</span><span class="p">:</span>
                <span class="n">ste</span> <span class="o">=</span> <span class="mi">1</span>
            <span class="k">if</span> <span class="ow">not</span> <span class="n">sto</span><span class="p">:</span>
                <span class="n">sto</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">tabs</span><span class="p">)</span> <span class="o">-</span> <span class="mi">1</span>
            <span class="k">if</span> <span class="ow">not</span> <span class="n">sta</span><span class="p">:</span>
                <span class="n">sta</span> <span class="o">=</span> <span class="mi">0</span>
            <span class="k">for</span> <span class="n">x</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">sta</span><span class="p">,</span> <span class="n">sto</span><span class="p">,</span> <span class="n">ste</span><span class="p">):</span>
                <span class="k">try</span><span class="p">:</span>
                    <span class="n">tabs</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">tabs</span><span class="p">[</span><span class="n">x</span><span class="p">])</span>
                <span class="k">except</span> <span class="ne">IndexError</span><span class="p">:</span>
                    <span class="k">pass</span>
            <span class="k">return</span> <span class="n">tabs</span>
        <span class="k">elif</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">item</span><span class="p">,</span> <span class="nb">tuple</span><span class="p">):</span>
            <span class="n">r</span> <span class="o">=</span> <span class="nb">range</span><span class="p">(</span><span class="o">*</span><span class="n">item</span><span class="p">)</span>
            <span class="n">tabs</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="n">tab</span><span class="o">.</span><span class="n">Tab</span><span class="p">]</span> <span class="o">=</span> <span class="p">[]</span>
            <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">r</span><span class="p">:</span>
                <span class="k">try</span><span class="p">:</span>
                    <span class="n">tabs</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">tabs</span><span class="p">[</span><span class="n">i</span><span class="p">])</span>
                <span class="k">except</span> <span class="ne">IndexError</span><span class="p">:</span>
                    <span class="k">pass</span>
            <span class="k">return</span> <span class="n">tabs</span>
        <span class="k">elif</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">item</span><span class="p">,</span> <span class="nb">str</span><span class="p">):</span>
            <span class="k">for</span> <span class="n">t</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">tabs</span><span class="p">:</span>
                <span class="k">if</span> <span class="n">item</span><span class="o">.</span><span class="n">lower</span><span class="p">()</span> <span class="ow">in</span> <span class="nb">str</span><span class="p">(</span><span class="n">t</span><span class="o">.</span><span class="n">target</span><span class="o">.</span><span class="n">to_json</span><span class="p">())</span><span class="o">.</span><span class="n">lower</span><span class="p">():</span>
                    <span class="k">return</span> <span class="n">t</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">tabs</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span>

    <span class="k">def</span> <span class="fm">__reversed__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="nb">reversed</span><span class="p">(</span><span class="nb">list</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">tabs</span><span class="p">))</span>

    <span class="k">def</span> <span class="fm">__next__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">try</span><span class="p">:</span>
            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">tabs</span><span class="p">[</span><span class="bp">self</span><span class="o">.</span><span class="n">_i</span><span class="p">]</span>
        <span class="k">except</span> <span class="ne">IndexError</span><span class="p">:</span>
            <span class="k">del</span> <span class="bp">self</span><span class="o">.</span><span class="n">_i</span>
            <span class="k">raise</span> <span class="ne">StopIteration</span>
        <span class="k">except</span> <span class="ne">AttributeError</span><span class="p">:</span>
            <span class="k">del</span> <span class="bp">self</span><span class="o">.</span><span class="n">_i</span>
            <span class="k">raise</span> <span class="ne">StopIteration</span>
        <span class="k">finally</span><span class="p">:</span>
            <span class="k">if</span> <span class="nb">hasattr</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="s2">&quot;_i&quot;</span><span class="p">):</span>
                <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">_i</span> <span class="o">!=</span> <span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">tabs</span><span class="p">):</span>
                    <span class="bp">self</span><span class="o">.</span><span class="n">_i</span> <span class="o">+=</span> <span class="mi">1</span>
                <span class="k">else</span><span class="p">:</span>
                    <span class="k">del</span> <span class="bp">self</span><span class="o">.</span><span class="n">_i</span>

<div class="viewcode-block" id="Browser.stop">
<a class="viewcode-back" href="../../../nodriver/classes/browser.html#nodriver.Browser.stop">[docs]</a>
    <span class="k">def</span> <span class="nf">stop</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">try</span><span class="p">:</span>
            <span class="c1"># asyncio.get_running_loop().create_task(self.connection.send(cdp.browser.close()))</span>

            <span class="n">asyncio</span><span class="o">.</span><span class="n">get_event_loop</span><span class="p">()</span><span class="o">.</span><span class="n">create_task</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">connection</span><span class="o">.</span><span class="n">disconnect</span><span class="p">())</span>
            <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span><span class="s2">&quot;closed the connection using get_event_loop().create_task()&quot;</span><span class="p">)</span>
        <span class="k">except</span> <span class="ne">RuntimeError</span><span class="p">:</span>
            <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">connection</span><span class="p">:</span>
                <span class="k">try</span><span class="p">:</span>
                    <span class="c1"># asyncio.run(self.connection.send(cdp.browser.close()))</span>
                    <span class="n">asyncio</span><span class="o">.</span><span class="n">run</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">connection</span><span class="o">.</span><span class="n">disconnect</span><span class="p">())</span>
                    <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span><span class="s2">&quot;closed the connection using asyncio.run()&quot;</span><span class="p">)</span>
                <span class="k">except</span> <span class="ne">Exception</span><span class="p">:</span>
                    <span class="k">pass</span>

        <span class="k">for</span> <span class="n">_</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">3</span><span class="p">):</span>
            <span class="k">try</span><span class="p">:</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">_process</span><span class="o">.</span><span class="n">terminate</span><span class="p">()</span>
                <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span>
                    <span class="s2">&quot;terminated browser with pid </span><span class="si">%d</span><span class="s2"> successfully&quot;</span> <span class="o">%</span> <span class="bp">self</span><span class="o">.</span><span class="n">_process</span><span class="o">.</span><span class="n">pid</span>
                <span class="p">)</span>
                <span class="k">break</span>
            <span class="k">except</span> <span class="p">(</span><span class="ne">Exception</span><span class="p">,):</span>
                <span class="k">try</span><span class="p">:</span>
                    <span class="bp">self</span><span class="o">.</span><span class="n">_process</span><span class="o">.</span><span class="n">kill</span><span class="p">()</span>
                    <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span>
                        <span class="s2">&quot;killed browser with pid </span><span class="si">%d</span><span class="s2"> successfully&quot;</span> <span class="o">%</span> <span class="bp">self</span><span class="o">.</span><span class="n">_process</span><span class="o">.</span><span class="n">pid</span>
                    <span class="p">)</span>
                    <span class="k">break</span>
                <span class="k">except</span> <span class="p">(</span><span class="ne">Exception</span><span class="p">,):</span>
                    <span class="k">try</span><span class="p">:</span>
                        <span class="k">if</span> <span class="nb">hasattr</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="s2">&quot;browser_process_pid&quot;</span><span class="p">):</span>
                            <span class="n">os</span><span class="o">.</span><span class="n">kill</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_process_pid</span><span class="p">,</span> <span class="mi">15</span><span class="p">)</span>
                            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span>
                                <span class="s2">&quot;killed browser with pid </span><span class="si">%d</span><span class="s2"> using signal 15 successfully&quot;</span>
                                <span class="o">%</span> <span class="bp">self</span><span class="o">.</span><span class="n">_process</span><span class="o">.</span><span class="n">pid</span>
                            <span class="p">)</span>
                            <span class="k">break</span>
                    <span class="k">except</span> <span class="p">(</span><span class="ne">TypeError</span><span class="p">,):</span>
                        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;typerror&quot;</span><span class="p">,</span> <span class="n">exc_info</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
                        <span class="k">pass</span>
                    <span class="k">except</span> <span class="p">(</span><span class="ne">PermissionError</span><span class="p">,):</span>
                        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span>
                            <span class="s2">&quot;browser already stopped, or no permission to kill. skip&quot;</span>
                        <span class="p">)</span>
                        <span class="k">pass</span>
                    <span class="k">except</span> <span class="p">(</span><span class="ne">ProcessLookupError</span><span class="p">,):</span>
                        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;process lookup failure&quot;</span><span class="p">)</span>
                        <span class="k">pass</span>
                    <span class="k">except</span> <span class="p">(</span><span class="ne">Exception</span><span class="p">,):</span>
                        <span class="k">raise</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_process</span> <span class="o">=</span> <span class="kc">None</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_process_pid</span> <span class="o">=</span> <span class="kc">None</span></div>


    <span class="k">def</span> <span class="fm">__await__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="c1"># return ( asyncio.sleep(0)).__await__()</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">update_targets</span><span class="p">()</span><span class="o">.</span><span class="fm">__await__</span><span class="p">()</span>

    <span class="k">def</span> <span class="fm">__del__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">pass</span></div>



<span class="k">class</span> <span class="nc">CookieJar</span><span class="p">:</span>
    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">browser</span><span class="p">:</span> <span class="n">Browser</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_browser</span> <span class="o">=</span> <span class="n">browser</span>
        <span class="c1"># self._connection = connection</span>

    <span class="k">async</span> <span class="k">def</span> <span class="nf">get_all</span><span class="p">(</span>
        <span class="bp">self</span><span class="p">,</span> <span class="n">requests_cookie_format</span><span class="p">:</span> <span class="nb">bool</span> <span class="o">=</span> <span class="kc">False</span>
    <span class="p">)</span> <span class="o">-&gt;</span> <span class="n">List</span><span class="p">[</span><span class="n">Union</span><span class="p">[</span><span class="n">cdp</span><span class="o">.</span><span class="n">network</span><span class="o">.</span><span class="n">Cookie</span><span class="p">,</span> <span class="s2">&quot;http.cookiejar.Cookie&quot;</span><span class="p">]]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        get all cookies</span>

<span class="sd">        :param requests_cookie_format: when True, returns python http.cookiejar.Cookie objects, compatible  with requests library and many others.</span>
<span class="sd">        :type requests_cookie_format: bool</span>
<span class="sd">        :return:</span>
<span class="sd">        :rtype:</span>

<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">connection</span> <span class="o">=</span> <span class="kc">None</span>
        <span class="k">for</span> <span class="n">tab</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">_browser</span><span class="o">.</span><span class="n">tabs</span><span class="p">:</span>
            <span class="k">if</span> <span class="n">tab</span><span class="o">.</span><span class="n">closed</span><span class="p">:</span>
                <span class="k">continue</span>
            <span class="n">connection</span> <span class="o">=</span> <span class="n">tab</span>
            <span class="k">break</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">connection</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_browser</span><span class="o">.</span><span class="n">connection</span>
        <span class="n">cookies</span> <span class="o">=</span> <span class="k">await</span> <span class="n">connection</span><span class="o">.</span><span class="n">send</span><span class="p">(</span><span class="n">cdp</span><span class="o">.</span><span class="n">storage</span><span class="o">.</span><span class="n">get_cookies</span><span class="p">())</span>
        <span class="k">if</span> <span class="n">requests_cookie_format</span><span class="p">:</span>
            <span class="kn">import</span> <span class="nn">requests.cookies</span>

            <span class="k">return</span> <span class="p">[</span>
                <span class="n">requests</span><span class="o">.</span><span class="n">cookies</span><span class="o">.</span><span class="n">create_cookie</span><span class="p">(</span>
                    <span class="n">name</span><span class="o">=</span><span class="n">c</span><span class="o">.</span><span class="n">name</span><span class="p">,</span>
                    <span class="n">value</span><span class="o">=</span><span class="n">c</span><span class="o">.</span><span class="n">value</span><span class="p">,</span>
                    <span class="n">domain</span><span class="o">=</span><span class="n">c</span><span class="o">.</span><span class="n">domain</span><span class="p">,</span>
                    <span class="n">path</span><span class="o">=</span><span class="n">c</span><span class="o">.</span><span class="n">path</span><span class="p">,</span>
                    <span class="n">expires</span><span class="o">=</span><span class="n">c</span><span class="o">.</span><span class="n">expires</span><span class="p">,</span>
                    <span class="n">secure</span><span class="o">=</span><span class="n">c</span><span class="o">.</span><span class="n">secure</span><span class="p">,</span>
                <span class="p">)</span>
                <span class="k">for</span> <span class="n">c</span> <span class="ow">in</span> <span class="n">cookies</span>
            <span class="p">]</span>
        <span class="k">return</span> <span class="n">cookies</span>

    <span class="k">async</span> <span class="k">def</span> <span class="nf">set_all</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">cookies</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="n">cdp</span><span class="o">.</span><span class="n">network</span><span class="o">.</span><span class="n">CookieParam</span><span class="p">]):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        set cookies</span>

<span class="sd">        :param cookies: list of cookies</span>
<span class="sd">        :type cookies:</span>
<span class="sd">        :return:</span>
<span class="sd">        :rtype:</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">connection</span> <span class="o">=</span> <span class="kc">None</span>
        <span class="k">for</span> <span class="n">tab</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">_browser</span><span class="o">.</span><span class="n">tabs</span><span class="p">:</span>
            <span class="k">if</span> <span class="n">tab</span><span class="o">.</span><span class="n">closed</span><span class="p">:</span>
                <span class="k">continue</span>
            <span class="n">connection</span> <span class="o">=</span> <span class="n">tab</span>
            <span class="k">break</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">connection</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_browser</span><span class="o">.</span><span class="n">connection</span>
        <span class="n">cookies</span> <span class="o">=</span> <span class="k">await</span> <span class="n">connection</span><span class="o">.</span><span class="n">send</span><span class="p">(</span><span class="n">cdp</span><span class="o">.</span><span class="n">storage</span><span class="o">.</span><span class="n">get_cookies</span><span class="p">())</span>
        <span class="k">await</span> <span class="n">connection</span><span class="o">.</span><span class="n">send</span><span class="p">(</span><span class="n">cdp</span><span class="o">.</span><span class="n">storage</span><span class="o">.</span><span class="n">set_cookies</span><span class="p">(</span><span class="n">cookies</span><span class="p">))</span>

    <span class="k">async</span> <span class="k">def</span> <span class="nf">save</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">file</span><span class="p">:</span> <span class="n">PathLike</span> <span class="o">=</span> <span class="s2">&quot;.session.dat&quot;</span><span class="p">,</span> <span class="n">pattern</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="s2">&quot;.*&quot;</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        save all cookies (or a subset, controlled by `pattern`) to a file to be restored later</span>

<span class="sd">        :param file:</span>
<span class="sd">        :type file:</span>
<span class="sd">        :param pattern: regex style pattern string.</span>
<span class="sd">               any cookie that has a  domain, key or value field which matches the pattern will be included.</span>
<span class="sd">               default = &quot;.*&quot;  (all)</span>

<span class="sd">               eg: the pattern &quot;(cf|.com|nowsecure)&quot; will include those cookies which:</span>
<span class="sd">                    - have a string &quot;cf&quot; (cloudflare)</span>
<span class="sd">                    - have &quot;.com&quot; in them, in either domain, key or value field.</span>
<span class="sd">                    - contain &quot;nowsecure&quot;</span>
<span class="sd">        :type pattern: str</span>
<span class="sd">        :return:</span>
<span class="sd">        :rtype:</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="kn">import</span> <span class="nn">re</span>

        <span class="n">pattern</span> <span class="o">=</span> <span class="n">re</span><span class="o">.</span><span class="n">compile</span><span class="p">(</span><span class="n">pattern</span><span class="p">)</span>
        <span class="n">save_path</span> <span class="o">=</span> <span class="n">pathlib</span><span class="o">.</span><span class="n">Path</span><span class="p">(</span><span class="n">file</span><span class="p">)</span><span class="o">.</span><span class="n">resolve</span><span class="p">()</span>
        <span class="n">connection</span> <span class="o">=</span> <span class="kc">None</span>
        <span class="k">for</span> <span class="n">tab</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">_browser</span><span class="o">.</span><span class="n">tabs</span><span class="p">:</span>
            <span class="k">if</span> <span class="n">tab</span><span class="o">.</span><span class="n">closed</span><span class="p">:</span>
                <span class="k">continue</span>
            <span class="n">connection</span> <span class="o">=</span> <span class="n">tab</span>
            <span class="k">break</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">connection</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_browser</span><span class="o">.</span><span class="n">connection</span>

        <span class="n">cookies</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_all</span><span class="p">(</span><span class="n">requests_cookie_format</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span>
        <span class="n">included_cookies</span> <span class="o">=</span> <span class="p">[]</span>
        <span class="k">for</span> <span class="n">cookie</span> <span class="ow">in</span> <span class="n">cookies</span><span class="p">:</span>
            <span class="k">for</span> <span class="n">match</span> <span class="ow">in</span> <span class="n">pattern</span><span class="o">.</span><span class="n">finditer</span><span class="p">(</span><span class="nb">str</span><span class="p">(</span><span class="n">cookie</span><span class="o">.</span><span class="vm">__dict__</span><span class="p">)):</span>
                <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span>
                    <span class="s2">&quot;saved cookie for matching pattern &#39;</span><span class="si">%s</span><span class="s2">&#39; =&gt; (</span><span class="si">%s</span><span class="s2">: </span><span class="si">%s</span><span class="s2">)&quot;</span><span class="p">,</span>
                    <span class="n">pattern</span><span class="o">.</span><span class="n">pattern</span><span class="p">,</span>
                    <span class="n">cookie</span><span class="o">.</span><span class="n">name</span><span class="p">,</span>
                    <span class="n">cookie</span><span class="o">.</span><span class="n">value</span><span class="p">,</span>
                <span class="p">)</span>
                <span class="n">included_cookies</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">cookie</span><span class="p">)</span>
                <span class="k">break</span>
        <span class="n">pickle</span><span class="o">.</span><span class="n">dump</span><span class="p">(</span><span class="n">cookies</span><span class="p">,</span> <span class="n">save_path</span><span class="o">.</span><span class="n">open</span><span class="p">(</span><span class="s2">&quot;w+b&quot;</span><span class="p">))</span>

    <span class="k">async</span> <span class="k">def</span> <span class="nf">load</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">file</span><span class="p">:</span> <span class="n">PathLike</span> <span class="o">=</span> <span class="s2">&quot;.session.dat&quot;</span><span class="p">,</span> <span class="n">pattern</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="s2">&quot;.*&quot;</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        load all cookies (or a subset, controlled by `pattern`) from a file created by :py:meth:`~save_cookies`.</span>

<span class="sd">        :param file:</span>
<span class="sd">        :type file:</span>
<span class="sd">        :param pattern: regex style pattern string.</span>
<span class="sd">               any cookie that has a  domain, key or value field which matches the pattern will be included.</span>
<span class="sd">               default = &quot;.*&quot;  (all)</span>

<span class="sd">               eg: the pattern &quot;(cf|.com|nowsecure)&quot; will include those cookies which:</span>
<span class="sd">                    - have a string &quot;cf&quot; (cloudflare)</span>
<span class="sd">                    - have &quot;.com&quot; in them, in either domain, key or value field.</span>
<span class="sd">                    - contain &quot;nowsecure&quot;</span>
<span class="sd">        :type pattern: str</span>
<span class="sd">        :return:</span>
<span class="sd">        :rtype:</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="kn">import</span> <span class="nn">re</span>

        <span class="n">pattern</span> <span class="o">=</span> <span class="n">re</span><span class="o">.</span><span class="n">compile</span><span class="p">(</span><span class="n">pattern</span><span class="p">)</span>
        <span class="n">save_path</span> <span class="o">=</span> <span class="n">pathlib</span><span class="o">.</span><span class="n">Path</span><span class="p">(</span><span class="n">file</span><span class="p">)</span><span class="o">.</span><span class="n">resolve</span><span class="p">()</span>
        <span class="n">cookies</span> <span class="o">=</span> <span class="n">pickle</span><span class="o">.</span><span class="n">load</span><span class="p">(</span><span class="n">save_path</span><span class="o">.</span><span class="n">open</span><span class="p">(</span><span class="s2">&quot;r+b&quot;</span><span class="p">))</span>
        <span class="n">included_cookies</span> <span class="o">=</span> <span class="p">[]</span>
        <span class="n">connection</span> <span class="o">=</span> <span class="kc">None</span>
        <span class="k">for</span> <span class="n">tab</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">_browser</span><span class="o">.</span><span class="n">tabs</span><span class="p">:</span>
            <span class="k">if</span> <span class="n">tab</span><span class="o">.</span><span class="n">closed</span><span class="p">:</span>
                <span class="k">continue</span>
            <span class="n">connection</span> <span class="o">=</span> <span class="n">tab</span>
            <span class="k">break</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">connection</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_browser</span><span class="o">.</span><span class="n">connection</span>
        <span class="k">for</span> <span class="n">cookie</span> <span class="ow">in</span> <span class="n">cookies</span><span class="p">:</span>
            <span class="k">for</span> <span class="n">match</span> <span class="ow">in</span> <span class="n">pattern</span><span class="o">.</span><span class="n">finditer</span><span class="p">(</span><span class="nb">str</span><span class="p">(</span><span class="n">cookie</span><span class="o">.</span><span class="vm">__dict__</span><span class="p">)):</span>
                <span class="n">included_cookies</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">cookie</span><span class="p">)</span>
                <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span>
                    <span class="s2">&quot;loaded cookie for matching pattern &#39;</span><span class="si">%s</span><span class="s2">&#39; =&gt; (</span><span class="si">%s</span><span class="s2">: </span><span class="si">%s</span><span class="s2">)&quot;</span><span class="p">,</span>
                    <span class="n">pattern</span><span class="o">.</span><span class="n">pattern</span><span class="p">,</span>
                    <span class="n">cookie</span><span class="o">.</span><span class="n">name</span><span class="p">,</span>
                    <span class="n">cookie</span><span class="o">.</span><span class="n">value</span><span class="p">,</span>
                <span class="p">)</span>
                <span class="k">break</span>
        <span class="k">await</span> <span class="n">connection</span><span class="o">.</span><span class="n">send</span><span class="p">(</span><span class="n">cdp</span><span class="o">.</span><span class="n">storage</span><span class="o">.</span><span class="n">set_cookies</span><span class="p">(</span><span class="n">included_cookies</span><span class="p">))</span>

    <span class="k">async</span> <span class="k">def</span> <span class="nf">clear</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        clear current cookies</span>

<span class="sd">        note: this includes all open tabs/windows for this browser</span>

<span class="sd">        :return:</span>
<span class="sd">        :rtype:</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">connection</span> <span class="o">=</span> <span class="kc">None</span>
        <span class="k">for</span> <span class="n">tab</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">_browser</span><span class="o">.</span><span class="n">tabs</span><span class="p">:</span>
            <span class="k">if</span> <span class="n">tab</span><span class="o">.</span><span class="n">closed</span><span class="p">:</span>
                <span class="k">continue</span>
            <span class="n">connection</span> <span class="o">=</span> <span class="n">tab</span>
            <span class="k">break</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">connection</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_browser</span><span class="o">.</span><span class="n">connection</span>

        <span class="k">await</span> <span class="n">connection</span><span class="o">.</span><span class="n">send</span><span class="p">(</span><span class="n">cdp</span><span class="o">.</span><span class="n">storage</span><span class="o">.</span><span class="n">clear_cookies</span><span class="p">())</span>


<span class="k">class</span> <span class="nc">HTTPApi</span><span class="p">:</span>
    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">addr</span><span class="p">:</span> <span class="n">Tuple</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="nb">int</span><span class="p">]):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">host</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">port</span> <span class="o">=</span> <span class="n">addr</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">api</span> <span class="o">=</span> <span class="s2">&quot;http://</span><span class="si">%s</span><span class="s2">:</span><span class="si">%d</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">host</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">port</span><span class="p">)</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_target</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">target</span><span class="p">:</span> <span class="s2">&quot;Target&quot;</span><span class="p">):</span>
        <span class="n">ws_url</span> <span class="o">=</span> <span class="n">urllib</span><span class="o">.</span><span class="n">parse</span><span class="o">.</span><span class="n">urlparse</span><span class="p">(</span><span class="n">target</span><span class="o">.</span><span class="n">websocket_url</span><span class="p">)</span>
        <span class="n">inst</span> <span class="o">=</span> <span class="bp">cls</span><span class="p">((</span><span class="n">ws_url</span><span class="o">.</span><span class="n">hostname</span><span class="p">,</span> <span class="n">ws_url</span><span class="o">.</span><span class="n">port</span><span class="p">))</span>
        <span class="k">return</span> <span class="n">inst</span>

    <span class="k">async</span> <span class="k">def</span> <span class="nf">get</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">endpoint</span><span class="p">:</span> <span class="nb">str</span><span class="p">):</span>
        <span class="k">return</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">_request</span><span class="p">(</span><span class="n">endpoint</span><span class="p">)</span>

    <span class="k">async</span> <span class="k">def</span> <span class="nf">post</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">endpoint</span><span class="p">,</span> <span class="n">data</span><span class="p">):</span>
        <span class="k">return</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">_request</span><span class="p">(</span><span class="n">endpoint</span><span class="p">,</span> <span class="n">data</span><span class="p">)</span>

    <span class="k">async</span> <span class="k">def</span> <span class="nf">_request</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">endpoint</span><span class="p">,</span> <span class="n">method</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="s2">&quot;get&quot;</span><span class="p">,</span> <span class="n">data</span><span class="p">:</span> <span class="nb">dict</span> <span class="o">=</span> <span class="kc">None</span><span class="p">):</span>
        <span class="n">url</span> <span class="o">=</span> <span class="n">urllib</span><span class="o">.</span><span class="n">parse</span><span class="o">.</span><span class="n">urljoin</span><span class="p">(</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">api</span><span class="p">,</span> <span class="sa">f</span><span class="s2">&quot;json/</span><span class="si">{</span><span class="n">endpoint</span><span class="si">}</span><span class="s2">&quot;</span> <span class="k">if</span> <span class="n">endpoint</span> <span class="k">else</span> <span class="s2">&quot;/json&quot;</span>
        <span class="p">)</span>
        <span class="k">if</span> <span class="n">data</span> <span class="ow">and</span> <span class="n">method</span><span class="o">.</span><span class="n">lower</span><span class="p">()</span> <span class="o">==</span> <span class="s2">&quot;get&quot;</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;get requests cannot contain data&quot;</span><span class="p">)</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="n">url</span><span class="p">:</span>
            <span class="n">url</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">api</span> <span class="o">+</span> <span class="n">endpoint</span>
        <span class="n">request</span> <span class="o">=</span> <span class="n">urllib</span><span class="o">.</span><span class="n">request</span><span class="o">.</span><span class="n">Request</span><span class="p">(</span><span class="n">url</span><span class="p">)</span>
        <span class="n">request</span><span class="o">.</span><span class="n">method</span> <span class="o">=</span> <span class="n">method</span>
        <span class="n">request</span><span class="o">.</span><span class="n">data</span> <span class="o">=</span> <span class="kc">None</span>
        <span class="k">if</span> <span class="n">data</span><span class="p">:</span>
            <span class="n">request</span><span class="o">.</span><span class="n">data</span> <span class="o">=</span> <span class="n">json</span><span class="o">.</span><span class="n">dumps</span><span class="p">(</span><span class="n">data</span><span class="p">)</span><span class="o">.</span><span class="n">encode</span><span class="p">(</span><span class="s2">&quot;utf-8&quot;</span><span class="p">)</span>

        <span class="n">response</span> <span class="o">=</span> <span class="k">await</span> <span class="n">asyncio</span><span class="o">.</span><span class="n">get_running_loop</span><span class="p">()</span><span class="o">.</span><span class="n">run_in_executor</span><span class="p">(</span>
            <span class="kc">None</span><span class="p">,</span> <span class="k">lambda</span><span class="p">:</span> <span class="n">urllib</span><span class="o">.</span><span class="n">request</span><span class="o">.</span><span class="n">urlopen</span><span class="p">(</span><span class="n">request</span><span class="p">,</span> <span class="n">timeout</span><span class="o">=</span><span class="mi">10</span><span class="p">)</span>
        <span class="p">)</span>
        <span class="k">return</span> <span class="n">json</span><span class="o">.</span><span class="n">loads</span><span class="p">(</span><span class="n">response</span><span class="o">.</span><span class="n">read</span><span class="p">())</span>


<span class="n">atexit</span><span class="o">.</span><span class="n">register</span><span class="p">(</span><span class="n">util</span><span class="o">.</span><span class="n">deconstruct_browser</span><span class="p">)</span>
</pre></div>
        </article>
      </div>
      <footer>
        
        <div class="related-pages">
          
          
        </div>
        <div class="bottom-of-page">
          <div class="left-details">
            <div class="copyright">
                Copyright &#169; 2023, Author
            </div>
            Made with <a href="https://www.sphinx-doc.org/">Sphinx</a> and <a class="muted-link" href="https://pradyunsg.me">@pradyunsg</a>'s
            
            <a href="https://github.com/pradyunsg/furo">Furo</a>
            
          </div>
          <div class="right-details">
            
          </div>
        </div>
        
      </footer>
    </div>
    <aside class="toc-drawer no-toc">
      
      
      
    </aside>
  </div>
</div><script src="../../../_static/documentation_options.js?v=5929fcd5"></script>
    <script src="../../../_static/doctools.js?v=888ff710"></script>
    <script src="../../../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../../../_static/scripts/furo.js?v=32e29ea5"></script>
    </body>
</html>