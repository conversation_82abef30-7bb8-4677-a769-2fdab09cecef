<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <LangVersion>latest</LangVersion>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <PackageId>NoDriverSharp.Core</PackageId>
    <Version>1.0.0</Version>
    <Authors>NoDriverSharp Contributors</Authors>
    <Description>C# port of the Python nodriver library for browser automation using Chrome DevTools Protocol</Description>
    <PackageTags>browser automation selenium chromedriver cdp devtools</PackageTags>
    <RepositoryUrl>https://github.com/NoDriverSharp/NoDriverSharp</RepositoryUrl>
    <PackageLicenseExpression>MIT</PackageLicenseExpression>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.0" />
    <PackageReference Include="System.Text.Json" Version="8.0.5" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\NoDriverSharp.CDP\NoDriverSharp.CDP.csproj" />
  </ItemGroup>

</Project>
