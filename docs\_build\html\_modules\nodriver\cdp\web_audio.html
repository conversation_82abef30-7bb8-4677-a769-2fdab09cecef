<!doctype html>
<html class="no-js" lang="en" data-content_root="../../../">
  <head><meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width,initial-scale=1"/>
    <meta name="color-scheme" content="light dark"><link rel="index" title="Index" href="../../../genindex.html" /><link rel="search" title="Search" href="../../../search.html" />

    <!-- Generated with Sphinx 7.2.6 and Furo 2023.09.10 -->
        <title>nodriver.cdp.web_audio - nodriver documentation</title>
      <link rel="stylesheet" type="text/css" href="../../../_static/pygments.css?v=fa44fd50" />
    <link rel="stylesheet" type="text/css" href="../../../_static/styles/furo.css?v=135e06be" />
    <link rel="stylesheet" type="text/css" href="../../../_static/styles/furo-extensions.css?v=36a5483c" />
    <link rel="stylesheet" type="text/css" href="../../../_static/./custom.css?v=3934642d" />
    
    


<style>
  body {
    --color-code-background: #eeffcc;
  --color-code-foreground: black;
  
  }
  @media not print {
    body[data-theme="dark"] {
      --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8eefba;
  --color-background-primary: #111112;
  --color-problematic: #e7aeae;
  
    }
    @media (prefers-color-scheme: dark) {
      body:not([data-theme="light"]) {
        --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8eefba;
  --color-background-primary: #111112;
  --color-problematic: #e7aeae;
  
      }
    }
  }
</style></head>
  <body>
    
    <script>
      document.body.dataset.theme = localStorage.getItem("theme") || "auto";
    </script>
    

<svg xmlns="http://www.w3.org/2000/svg" style="display: none;">
  <symbol id="svg-toc" viewBox="0 0 24 24">
    <title>Contents</title>
    <svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 1024 1024">
      <path d="M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM115.4 518.9L271.7 642c5.8 4.6 14.4.5 14.4-6.9V388.9c0-7.4-8.5-11.5-14.4-6.9L115.4 505.1a8.74 8.74 0 0 0 0 13.8z"/>
    </svg>
  </symbol>
  <symbol id="svg-menu" viewBox="0 0 24 24">
    <title>Menu</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-menu">
      <line x1="3" y1="12" x2="21" y2="12"></line>
      <line x1="3" y1="6" x2="21" y2="6"></line>
      <line x1="3" y1="18" x2="21" y2="18"></line>
    </svg>
  </symbol>
  <symbol id="svg-arrow-right" viewBox="0 0 24 24">
    <title>Expand</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-chevron-right">
      <polyline points="9 18 15 12 9 6"></polyline>
    </svg>
  </symbol>
  <symbol id="svg-sun" viewBox="0 0 24 24">
    <title>Light mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="feather-sun">
      <circle cx="12" cy="12" r="5"></circle>
      <line x1="12" y1="1" x2="12" y2="3"></line>
      <line x1="12" y1="21" x2="12" y2="23"></line>
      <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
      <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
      <line x1="1" y1="12" x2="3" y2="12"></line>
      <line x1="21" y1="12" x2="23" y2="12"></line>
      <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
      <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
    </svg>
  </symbol>
  <symbol id="svg-moon" viewBox="0 0 24 24">
    <title>Dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-moon">
      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
      <path d="M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z" />
    </svg>
  </symbol>
  <symbol id="svg-sun-half" viewBox="0 0 24 24">
    <title>Auto light/dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-shadow">
      <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
      <circle cx="12" cy="12" r="9" />
      <path d="M13 12h5" />
      <path d="M13 15h4" />
      <path d="M13 18h1" />
      <path d="M13 9h4" />
      <path d="M13 6h1" />
    </svg>
  </symbol>
</svg>

<input type="checkbox" class="sidebar-toggle" name="__navigation" id="__navigation">
<input type="checkbox" class="sidebar-toggle" name="__toc" id="__toc">
<label class="overlay sidebar-overlay" for="__navigation">
  <div class="visually-hidden">Hide navigation sidebar</div>
</label>
<label class="overlay toc-overlay" for="__toc">
  <div class="visually-hidden">Hide table of contents sidebar</div>
</label>



<div class="page">
  <header class="mobile-header">
    <div class="header-left">
      <label class="nav-overlay-icon" for="__navigation">
        <div class="visually-hidden">Toggle site navigation sidebar</div>
        <i class="icon"><svg><use href="#svg-menu"></use></svg></i>
      </label>
    </div>
    <div class="header-center">
      <a href="../../../index.html"><div class="brand">nodriver  documentation</div></a>
    </div>
    <div class="header-right">
      <div class="theme-toggle-container theme-toggle-header">
        <button class="theme-toggle">
          <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
          <svg class="theme-icon-when-auto"><use href="#svg-sun-half"></use></svg>
          <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
          <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
        </button>
      </div>
      <label class="toc-overlay-icon toc-header-icon no-toc" for="__toc">
        <div class="visually-hidden">Toggle table of contents sidebar</div>
        <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
      </label>
    </div>
  </header>
  <aside class="sidebar-drawer">
    <div class="sidebar-container">
      
      <div class="sidebar-sticky"><div class="sidebar-scroll"><a class="sidebar-brand" href="../../../index.html">
  
  
  <span class="sidebar-brand-text">nodriver  documentation</span>
  
</a><form class="sidebar-search-container" method="get" action="../../../search.html" role="search">
  <input class="sidebar-search" placeholder="Search" name="q" aria-label="Search">
  <input type="hidden" name="check_keywords" value="yes">
  <input type="hidden" name="area" value="default">
</form>
<div id="searchbox"></div><div class="sidebar-tree">
  <ul>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html">Quickstart guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#usage-example">usage example</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#more-complete-example">More complete example</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#custom-starting-options">Custom starting options</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#alternative-custom-options">Alternative custom options</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#proxies-socks5-authenticated-too">Proxies (socks5, authenticated too)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/quickstart.html#concrete-example">Concrete example</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/classes/browser.html">Browser class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/classes/tab.html">Tab class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/classes/element.html">Element class</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../nodriver/classes/others_and_helpers.html">Other classes and Helper classes</a></li>
</ul>
<ul>
<li class="toctree-l1 has-children"><a class="reference internal" href="../../../nodriver/cdp.html">CDP object</a><input class="toctree-checkbox" id="toctree-checkbox-1" name="toctree-checkbox-1" role="switch" type="checkbox"/><label for="toctree-checkbox-1"><div class="visually-hidden">Toggle navigation of CDP object</div><i class="icon"><svg><use href="#svg-arrow-right"></use></svg></i></label><ul>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/accessibility.html">Accessibility</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/animation.html">Animation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/audits.html">Audits</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/autofill.html">Autofill</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/background_service.html">BackgroundService</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/bluetooth_emulation.html">BluetoothEmulation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/browser.html">Browser</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/cache_storage.html">CacheStorage</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/cast.html">Cast</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/console.html">Console</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/css.html">CSS</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/debugger.html">Debugger</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/device_access.html">DeviceAccess</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/device_orientation.html">DeviceOrientation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/dom.html">DOM</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/dom_debugger.html">DOMDebugger</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/dom_snapshot.html">DOMSnapshot</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/dom_storage.html">DOMStorage</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/emulation.html">Emulation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/event_breakpoints.html">EventBreakpoints</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/extensions.html">Extensions</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/fed_cm.html">FedCm</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/fetch.html">Fetch</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/file_system.html">FileSystem</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/headless_experimental.html">HeadlessExperimental</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/heap_profiler.html">HeapProfiler</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/indexed_db.html">IndexedDB</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/input_.html">Input</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/inspector.html">Inspector</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/io.html">IO</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/layer_tree.html">LayerTree</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/log.html">Log</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/media.html">Media</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/memory.html">Memory</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/network.html">Network</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/overlay.html">Overlay</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/page.html">Page</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/performance.html">Performance</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/performance_timeline.html">PerformanceTimeline</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/preload.html">Preload</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/profiler.html">Profiler</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/pwa.html">PWA</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/runtime.html">Runtime</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/schema.html">Schema</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/security.html">Security</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/service_worker.html">ServiceWorker</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/storage.html">Storage</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/system_info.html">SystemInfo</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/target.html">Target</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/tethering.html">Tethering</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/tracing.html">Tracing</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/web_audio.html">WebAudio</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../../nodriver/cdp/web_authn.html">WebAuthn</a></li>
</ul>
</li>
</ul>

</div>
</div>
      </div>
      
    </div>
  </aside>
  <div class="main">
    <div class="content">
      <div class="article-container">
        <a href="#" class="back-to-top muted-link">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
            <path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8v12z"></path>
          </svg>
          <span>Back to top</span>
        </a>
        <div class="content-icon-container">
          <div class="theme-toggle-container theme-toggle-content">
            <button class="theme-toggle">
              <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
              <svg class="theme-icon-when-auto"><use href="#svg-sun-half"></use></svg>
              <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
              <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
            </button>
          </div>
          <label class="toc-overlay-icon toc-content-icon no-toc" for="__toc">
            <div class="visually-hidden">Toggle table of contents sidebar</div>
            <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
          </label>
        </div>
        <article role="main">
          <h1>Source code for nodriver.cdp.web_audio</h1><div class="highlight"><pre>
<span></span><span class="c1"># DO NOT EDIT THIS FILE!</span>
<span class="c1">#</span>
<span class="c1"># This file is generated from the CDP specification. If you need to make</span>
<span class="c1"># changes, edit the generator and regenerate all of the modules.</span>
<span class="c1">#</span>
<span class="c1"># CDP domain: WebAudio (experimental)</span>

<span class="kn">from</span> <span class="nn">__future__</span> <span class="kn">import</span> <span class="n">annotations</span>

<span class="kn">import</span> <span class="nn">enum</span>
<span class="kn">import</span> <span class="nn">typing</span>
<span class="kn">from</span> <span class="nn">dataclasses</span> <span class="kn">import</span> <span class="n">dataclass</span>

<span class="kn">from</span> <span class="nn">.util</span> <span class="kn">import</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">event_class</span>


<div class="viewcode-block" id="GraphObjectId">
<a class="viewcode-back" href="../../../nodriver/cdp/web_audio.html#nodriver.cdp.web_audio.GraphObjectId">[docs]</a>
<span class="k">class</span> <span class="nc">GraphObjectId</span><span class="p">(</span><span class="nb">str</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    An unique ID for a graph object (AudioContext, AudioNode, AudioParam) in Web Audio API</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">GraphObjectId</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span>

    <span class="k">def</span> <span class="fm">__repr__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="s2">&quot;GraphObjectId(</span><span class="si">{}</span><span class="s2">)&quot;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="fm">__repr__</span><span class="p">())</span></div>



<div class="viewcode-block" id="ContextType">
<a class="viewcode-back" href="../../../nodriver/cdp/web_audio.html#nodriver.cdp.web_audio.ContextType">[docs]</a>
<span class="k">class</span> <span class="nc">ContextType</span><span class="p">(</span><span class="n">enum</span><span class="o">.</span><span class="n">Enum</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Enum of BaseAudioContext types</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">REALTIME</span> <span class="o">=</span> <span class="s2">&quot;realtime&quot;</span>
    <span class="n">OFFLINE</span> <span class="o">=</span> <span class="s2">&quot;offline&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">ContextType</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span></div>



<div class="viewcode-block" id="ContextState">
<a class="viewcode-back" href="../../../nodriver/cdp/web_audio.html#nodriver.cdp.web_audio.ContextState">[docs]</a>
<span class="k">class</span> <span class="nc">ContextState</span><span class="p">(</span><span class="n">enum</span><span class="o">.</span><span class="n">Enum</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Enum of AudioContextState from the spec</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">SUSPENDED</span> <span class="o">=</span> <span class="s2">&quot;suspended&quot;</span>
    <span class="n">RUNNING</span> <span class="o">=</span> <span class="s2">&quot;running&quot;</span>
    <span class="n">CLOSED</span> <span class="o">=</span> <span class="s2">&quot;closed&quot;</span>
    <span class="n">INTERRUPTED</span> <span class="o">=</span> <span class="s2">&quot;interrupted&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">ContextState</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span></div>



<div class="viewcode-block" id="NodeType">
<a class="viewcode-back" href="../../../nodriver/cdp/web_audio.html#nodriver.cdp.web_audio.NodeType">[docs]</a>
<span class="k">class</span> <span class="nc">NodeType</span><span class="p">(</span><span class="nb">str</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Enum of AudioNode types</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">NodeType</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span>

    <span class="k">def</span> <span class="fm">__repr__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="s2">&quot;NodeType(</span><span class="si">{}</span><span class="s2">)&quot;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="fm">__repr__</span><span class="p">())</span></div>



<div class="viewcode-block" id="ChannelCountMode">
<a class="viewcode-back" href="../../../nodriver/cdp/web_audio.html#nodriver.cdp.web_audio.ChannelCountMode">[docs]</a>
<span class="k">class</span> <span class="nc">ChannelCountMode</span><span class="p">(</span><span class="n">enum</span><span class="o">.</span><span class="n">Enum</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Enum of AudioNode::ChannelCountMode from the spec</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">CLAMPED_MAX</span> <span class="o">=</span> <span class="s2">&quot;clamped-max&quot;</span>
    <span class="n">EXPLICIT</span> <span class="o">=</span> <span class="s2">&quot;explicit&quot;</span>
    <span class="n">MAX_</span> <span class="o">=</span> <span class="s2">&quot;max&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">ChannelCountMode</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span></div>



<div class="viewcode-block" id="ChannelInterpretation">
<a class="viewcode-back" href="../../../nodriver/cdp/web_audio.html#nodriver.cdp.web_audio.ChannelInterpretation">[docs]</a>
<span class="k">class</span> <span class="nc">ChannelInterpretation</span><span class="p">(</span><span class="n">enum</span><span class="o">.</span><span class="n">Enum</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Enum of AudioNode::ChannelInterpretation from the spec</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">DISCRETE</span> <span class="o">=</span> <span class="s2">&quot;discrete&quot;</span>
    <span class="n">SPEAKERS</span> <span class="o">=</span> <span class="s2">&quot;speakers&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">ChannelInterpretation</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span></div>



<div class="viewcode-block" id="ParamType">
<a class="viewcode-back" href="../../../nodriver/cdp/web_audio.html#nodriver.cdp.web_audio.ParamType">[docs]</a>
<span class="k">class</span> <span class="nc">ParamType</span><span class="p">(</span><span class="nb">str</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Enum of AudioParam types</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">ParamType</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span>

    <span class="k">def</span> <span class="fm">__repr__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="s2">&quot;ParamType(</span><span class="si">{}</span><span class="s2">)&quot;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="fm">__repr__</span><span class="p">())</span></div>



<div class="viewcode-block" id="AutomationRate">
<a class="viewcode-back" href="../../../nodriver/cdp/web_audio.html#nodriver.cdp.web_audio.AutomationRate">[docs]</a>
<span class="k">class</span> <span class="nc">AutomationRate</span><span class="p">(</span><span class="n">enum</span><span class="o">.</span><span class="n">Enum</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Enum of AudioParam::AutomationRate from the spec</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">A_RATE</span> <span class="o">=</span> <span class="s2">&quot;a-rate&quot;</span>
    <span class="n">K_RATE</span> <span class="o">=</span> <span class="s2">&quot;k-rate&quot;</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">AutomationRate</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">json</span><span class="p">)</span></div>



<div class="viewcode-block" id="ContextRealtimeData">
<a class="viewcode-back" href="../../../nodriver/cdp/web_audio.html#nodriver.cdp.web_audio.ContextRealtimeData">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">ContextRealtimeData</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Fields in AudioContext that change in real-time.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1">#: The current context time in second in BaseAudioContext.</span>
    <span class="n">current_time</span><span class="p">:</span> <span class="nb">float</span>

    <span class="c1">#: The time spent on rendering graph divided by render quantum duration,</span>
    <span class="c1">#: and multiplied by 100. 100 means the audio renderer reached the full</span>
    <span class="c1">#: capacity and glitch may occur.</span>
    <span class="n">render_capacity</span><span class="p">:</span> <span class="nb">float</span>

    <span class="c1">#: A running mean of callback interval.</span>
    <span class="n">callback_interval_mean</span><span class="p">:</span> <span class="nb">float</span>

    <span class="c1">#: A running variance of callback interval.</span>
    <span class="n">callback_interval_variance</span><span class="p">:</span> <span class="nb">float</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;currentTime&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">current_time</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;renderCapacity&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">render_capacity</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;callbackIntervalMean&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">callback_interval_mean</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;callbackIntervalVariance&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">callback_interval_variance</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">ContextRealtimeData</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">current_time</span><span class="o">=</span><span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;currentTime&quot;</span><span class="p">]),</span>
            <span class="n">render_capacity</span><span class="o">=</span><span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;renderCapacity&quot;</span><span class="p">]),</span>
            <span class="n">callback_interval_mean</span><span class="o">=</span><span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;callbackIntervalMean&quot;</span><span class="p">]),</span>
            <span class="n">callback_interval_variance</span><span class="o">=</span><span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;callbackIntervalVariance&quot;</span><span class="p">]),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="BaseAudioContext">
<a class="viewcode-back" href="../../../nodriver/cdp/web_audio.html#nodriver.cdp.web_audio.BaseAudioContext">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">BaseAudioContext</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Protocol object for BaseAudioContext</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">context_id</span><span class="p">:</span> <span class="n">GraphObjectId</span>

    <span class="n">context_type</span><span class="p">:</span> <span class="n">ContextType</span>

    <span class="n">context_state</span><span class="p">:</span> <span class="n">ContextState</span>

    <span class="c1">#: Platform-dependent callback buffer size.</span>
    <span class="n">callback_buffer_size</span><span class="p">:</span> <span class="nb">float</span>

    <span class="c1">#: Number of output channels supported by audio hardware in use.</span>
    <span class="n">max_output_channel_count</span><span class="p">:</span> <span class="nb">float</span>

    <span class="c1">#: Context sample rate.</span>
    <span class="n">sample_rate</span><span class="p">:</span> <span class="nb">float</span>

    <span class="n">realtime_data</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="n">ContextRealtimeData</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;contextId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">context_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;contextType&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">context_type</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;contextState&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">context_state</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;callbackBufferSize&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">callback_buffer_size</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;maxOutputChannelCount&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">max_output_channel_count</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;sampleRate&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">sample_rate</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">realtime_data</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">json</span><span class="p">[</span><span class="s2">&quot;realtimeData&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">realtime_data</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">BaseAudioContext</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">context_id</span><span class="o">=</span><span class="n">GraphObjectId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;contextId&quot;</span><span class="p">]),</span>
            <span class="n">context_type</span><span class="o">=</span><span class="n">ContextType</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;contextType&quot;</span><span class="p">]),</span>
            <span class="n">context_state</span><span class="o">=</span><span class="n">ContextState</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;contextState&quot;</span><span class="p">]),</span>
            <span class="n">callback_buffer_size</span><span class="o">=</span><span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;callbackBufferSize&quot;</span><span class="p">]),</span>
            <span class="n">max_output_channel_count</span><span class="o">=</span><span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;maxOutputChannelCount&quot;</span><span class="p">]),</span>
            <span class="n">sample_rate</span><span class="o">=</span><span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;sampleRate&quot;</span><span class="p">]),</span>
            <span class="n">realtime_data</span><span class="o">=</span><span class="p">(</span>
                <span class="n">ContextRealtimeData</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;realtimeData&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;realtimeData&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="AudioListener">
<a class="viewcode-back" href="../../../nodriver/cdp/web_audio.html#nodriver.cdp.web_audio.AudioListener">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">AudioListener</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Protocol object for AudioListener</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">listener_id</span><span class="p">:</span> <span class="n">GraphObjectId</span>

    <span class="n">context_id</span><span class="p">:</span> <span class="n">GraphObjectId</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;listenerId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">listener_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;contextId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">context_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">AudioListener</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">listener_id</span><span class="o">=</span><span class="n">GraphObjectId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;listenerId&quot;</span><span class="p">]),</span>
            <span class="n">context_id</span><span class="o">=</span><span class="n">GraphObjectId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;contextId&quot;</span><span class="p">]),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="AudioNode">
<a class="viewcode-back" href="../../../nodriver/cdp/web_audio.html#nodriver.cdp.web_audio.AudioNode">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">AudioNode</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Protocol object for AudioNode</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">node_id</span><span class="p">:</span> <span class="n">GraphObjectId</span>

    <span class="n">context_id</span><span class="p">:</span> <span class="n">GraphObjectId</span>

    <span class="n">node_type</span><span class="p">:</span> <span class="n">NodeType</span>

    <span class="n">number_of_inputs</span><span class="p">:</span> <span class="nb">float</span>

    <span class="n">number_of_outputs</span><span class="p">:</span> <span class="nb">float</span>

    <span class="n">channel_count</span><span class="p">:</span> <span class="nb">float</span>

    <span class="n">channel_count_mode</span><span class="p">:</span> <span class="n">ChannelCountMode</span>

    <span class="n">channel_interpretation</span><span class="p">:</span> <span class="n">ChannelInterpretation</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;nodeId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">node_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;contextId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">context_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;nodeType&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">node_type</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;numberOfInputs&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">number_of_inputs</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;numberOfOutputs&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">number_of_outputs</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;channelCount&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">channel_count</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;channelCountMode&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">channel_count_mode</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;channelInterpretation&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">channel_interpretation</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">AudioNode</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">node_id</span><span class="o">=</span><span class="n">GraphObjectId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;nodeId&quot;</span><span class="p">]),</span>
            <span class="n">context_id</span><span class="o">=</span><span class="n">GraphObjectId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;contextId&quot;</span><span class="p">]),</span>
            <span class="n">node_type</span><span class="o">=</span><span class="n">NodeType</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;nodeType&quot;</span><span class="p">]),</span>
            <span class="n">number_of_inputs</span><span class="o">=</span><span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;numberOfInputs&quot;</span><span class="p">]),</span>
            <span class="n">number_of_outputs</span><span class="o">=</span><span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;numberOfOutputs&quot;</span><span class="p">]),</span>
            <span class="n">channel_count</span><span class="o">=</span><span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;channelCount&quot;</span><span class="p">]),</span>
            <span class="n">channel_count_mode</span><span class="o">=</span><span class="n">ChannelCountMode</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;channelCountMode&quot;</span><span class="p">]),</span>
            <span class="n">channel_interpretation</span><span class="o">=</span><span class="n">ChannelInterpretation</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span>
                <span class="n">json</span><span class="p">[</span><span class="s2">&quot;channelInterpretation&quot;</span><span class="p">]</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="AudioParam">
<a class="viewcode-back" href="../../../nodriver/cdp/web_audio.html#nodriver.cdp.web_audio.AudioParam">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">AudioParam</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Protocol object for AudioParam</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">param_id</span><span class="p">:</span> <span class="n">GraphObjectId</span>

    <span class="n">node_id</span><span class="p">:</span> <span class="n">GraphObjectId</span>

    <span class="n">context_id</span><span class="p">:</span> <span class="n">GraphObjectId</span>

    <span class="n">param_type</span><span class="p">:</span> <span class="n">ParamType</span>

    <span class="n">rate</span><span class="p">:</span> <span class="n">AutomationRate</span>

    <span class="n">default_value</span><span class="p">:</span> <span class="nb">float</span>

    <span class="n">min_value</span><span class="p">:</span> <span class="nb">float</span>

    <span class="n">max_value</span><span class="p">:</span> <span class="nb">float</span>

    <span class="k">def</span> <span class="nf">to_json</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">T_JSON_DICT</span><span class="p">:</span>
        <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;paramId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">param_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;nodeId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">node_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;contextId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">context_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;paramType&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">param_type</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;rate&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">rate</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;defaultValue&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">default_value</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;minValue&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">min_value</span>
        <span class="n">json</span><span class="p">[</span><span class="s2">&quot;maxValue&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">max_value</span>
        <span class="k">return</span> <span class="n">json</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">AudioParam</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">param_id</span><span class="o">=</span><span class="n">GraphObjectId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;paramId&quot;</span><span class="p">]),</span>
            <span class="n">node_id</span><span class="o">=</span><span class="n">GraphObjectId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;nodeId&quot;</span><span class="p">]),</span>
            <span class="n">context_id</span><span class="o">=</span><span class="n">GraphObjectId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;contextId&quot;</span><span class="p">]),</span>
            <span class="n">param_type</span><span class="o">=</span><span class="n">ParamType</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;paramType&quot;</span><span class="p">]),</span>
            <span class="n">rate</span><span class="o">=</span><span class="n">AutomationRate</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;rate&quot;</span><span class="p">]),</span>
            <span class="n">default_value</span><span class="o">=</span><span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;defaultValue&quot;</span><span class="p">]),</span>
            <span class="n">min_value</span><span class="o">=</span><span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;minValue&quot;</span><span class="p">]),</span>
            <span class="n">max_value</span><span class="o">=</span><span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;maxValue&quot;</span><span class="p">]),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="enable">
<a class="viewcode-back" href="../../../nodriver/cdp/web_audio.html#nodriver.cdp.web_audio.enable">[docs]</a>
<span class="k">def</span> <span class="nf">enable</span><span class="p">()</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Enables the WebAudio domain and starts sending context lifetime events.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;WebAudio.enable&quot;</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span></div>



<div class="viewcode-block" id="disable">
<a class="viewcode-back" href="../../../nodriver/cdp/web_audio.html#nodriver.cdp.web_audio.disable">[docs]</a>
<span class="k">def</span> <span class="nf">disable</span><span class="p">()</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="kc">None</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Disables the WebAudio domain.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;WebAudio.disable&quot;</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span></div>



<div class="viewcode-block" id="get_realtime_data">
<a class="viewcode-back" href="../../../nodriver/cdp/web_audio.html#nodriver.cdp.web_audio.get_realtime_data">[docs]</a>
<span class="k">def</span> <span class="nf">get_realtime_data</span><span class="p">(</span>
        <span class="n">context_id</span><span class="p">:</span> <span class="n">GraphObjectId</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">typing</span><span class="o">.</span><span class="n">Generator</span><span class="p">[</span><span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">T_JSON_DICT</span><span class="p">,</span> <span class="n">ContextRealtimeData</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Fetch the realtime data from the registered contexts.</span>

<span class="sd">    :param context_id:</span>
<span class="sd">    :returns:</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">params</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
    <span class="n">params</span><span class="p">[</span><span class="s2">&quot;contextId&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">context_id</span><span class="o">.</span><span class="n">to_json</span><span class="p">()</span>
    <span class="n">cmd_dict</span><span class="p">:</span> <span class="n">T_JSON_DICT</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;WebAudio.getRealtimeData&quot;</span><span class="p">,</span>
        <span class="s2">&quot;params&quot;</span><span class="p">:</span> <span class="n">params</span><span class="p">,</span>
    <span class="p">}</span>
    <span class="n">json</span> <span class="o">=</span> <span class="k">yield</span> <span class="n">cmd_dict</span>
    <span class="k">return</span> <span class="n">ContextRealtimeData</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;realtimeData&quot;</span><span class="p">])</span></div>



<div class="viewcode-block" id="ContextCreated">
<a class="viewcode-back" href="../../../nodriver/cdp/web_audio.html#nodriver.cdp.web_audio.ContextCreated">[docs]</a>
<span class="nd">@event_class</span><span class="p">(</span><span class="s2">&quot;WebAudio.contextCreated&quot;</span><span class="p">)</span>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">ContextCreated</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Notifies that a new BaseAudioContext has been created.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">context</span><span class="p">:</span> <span class="n">BaseAudioContext</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">ContextCreated</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">context</span><span class="o">=</span><span class="n">BaseAudioContext</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;context&quot;</span><span class="p">]))</span></div>



<div class="viewcode-block" id="ContextWillBeDestroyed">
<a class="viewcode-back" href="../../../nodriver/cdp/web_audio.html#nodriver.cdp.web_audio.ContextWillBeDestroyed">[docs]</a>
<span class="nd">@event_class</span><span class="p">(</span><span class="s2">&quot;WebAudio.contextWillBeDestroyed&quot;</span><span class="p">)</span>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">ContextWillBeDestroyed</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Notifies that an existing BaseAudioContext will be destroyed.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">context_id</span><span class="p">:</span> <span class="n">GraphObjectId</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">ContextWillBeDestroyed</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">context_id</span><span class="o">=</span><span class="n">GraphObjectId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;contextId&quot;</span><span class="p">]))</span></div>



<div class="viewcode-block" id="ContextChanged">
<a class="viewcode-back" href="../../../nodriver/cdp/web_audio.html#nodriver.cdp.web_audio.ContextChanged">[docs]</a>
<span class="nd">@event_class</span><span class="p">(</span><span class="s2">&quot;WebAudio.contextChanged&quot;</span><span class="p">)</span>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">ContextChanged</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Notifies that existing BaseAudioContext has changed some properties (id stays the same)..</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">context</span><span class="p">:</span> <span class="n">BaseAudioContext</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">ContextChanged</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">context</span><span class="o">=</span><span class="n">BaseAudioContext</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;context&quot;</span><span class="p">]))</span></div>



<div class="viewcode-block" id="AudioListenerCreated">
<a class="viewcode-back" href="../../../nodriver/cdp/web_audio.html#nodriver.cdp.web_audio.AudioListenerCreated">[docs]</a>
<span class="nd">@event_class</span><span class="p">(</span><span class="s2">&quot;WebAudio.audioListenerCreated&quot;</span><span class="p">)</span>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">AudioListenerCreated</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Notifies that the construction of an AudioListener has finished.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">listener</span><span class="p">:</span> <span class="n">AudioListener</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">AudioListenerCreated</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">listener</span><span class="o">=</span><span class="n">AudioListener</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;listener&quot;</span><span class="p">]))</span></div>



<div class="viewcode-block" id="AudioListenerWillBeDestroyed">
<a class="viewcode-back" href="../../../nodriver/cdp/web_audio.html#nodriver.cdp.web_audio.AudioListenerWillBeDestroyed">[docs]</a>
<span class="nd">@event_class</span><span class="p">(</span><span class="s2">&quot;WebAudio.audioListenerWillBeDestroyed&quot;</span><span class="p">)</span>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">AudioListenerWillBeDestroyed</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Notifies that a new AudioListener has been created.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">context_id</span><span class="p">:</span> <span class="n">GraphObjectId</span>
    <span class="n">listener_id</span><span class="p">:</span> <span class="n">GraphObjectId</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">AudioListenerWillBeDestroyed</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">context_id</span><span class="o">=</span><span class="n">GraphObjectId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;contextId&quot;</span><span class="p">]),</span>
            <span class="n">listener_id</span><span class="o">=</span><span class="n">GraphObjectId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;listenerId&quot;</span><span class="p">]),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="AudioNodeCreated">
<a class="viewcode-back" href="../../../nodriver/cdp/web_audio.html#nodriver.cdp.web_audio.AudioNodeCreated">[docs]</a>
<span class="nd">@event_class</span><span class="p">(</span><span class="s2">&quot;WebAudio.audioNodeCreated&quot;</span><span class="p">)</span>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">AudioNodeCreated</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Notifies that a new AudioNode has been created.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">node</span><span class="p">:</span> <span class="n">AudioNode</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">AudioNodeCreated</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">node</span><span class="o">=</span><span class="n">AudioNode</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;node&quot;</span><span class="p">]))</span></div>



<div class="viewcode-block" id="AudioNodeWillBeDestroyed">
<a class="viewcode-back" href="../../../nodriver/cdp/web_audio.html#nodriver.cdp.web_audio.AudioNodeWillBeDestroyed">[docs]</a>
<span class="nd">@event_class</span><span class="p">(</span><span class="s2">&quot;WebAudio.audioNodeWillBeDestroyed&quot;</span><span class="p">)</span>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">AudioNodeWillBeDestroyed</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Notifies that an existing AudioNode has been destroyed.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">context_id</span><span class="p">:</span> <span class="n">GraphObjectId</span>
    <span class="n">node_id</span><span class="p">:</span> <span class="n">GraphObjectId</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">AudioNodeWillBeDestroyed</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">context_id</span><span class="o">=</span><span class="n">GraphObjectId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;contextId&quot;</span><span class="p">]),</span>
            <span class="n">node_id</span><span class="o">=</span><span class="n">GraphObjectId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;nodeId&quot;</span><span class="p">]),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="AudioParamCreated">
<a class="viewcode-back" href="../../../nodriver/cdp/web_audio.html#nodriver.cdp.web_audio.AudioParamCreated">[docs]</a>
<span class="nd">@event_class</span><span class="p">(</span><span class="s2">&quot;WebAudio.audioParamCreated&quot;</span><span class="p">)</span>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">AudioParamCreated</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Notifies that a new AudioParam has been created.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">param</span><span class="p">:</span> <span class="n">AudioParam</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">AudioParamCreated</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">param</span><span class="o">=</span><span class="n">AudioParam</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;param&quot;</span><span class="p">]))</span></div>



<div class="viewcode-block" id="AudioParamWillBeDestroyed">
<a class="viewcode-back" href="../../../nodriver/cdp/web_audio.html#nodriver.cdp.web_audio.AudioParamWillBeDestroyed">[docs]</a>
<span class="nd">@event_class</span><span class="p">(</span><span class="s2">&quot;WebAudio.audioParamWillBeDestroyed&quot;</span><span class="p">)</span>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">AudioParamWillBeDestroyed</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Notifies that an existing AudioParam has been destroyed.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">context_id</span><span class="p">:</span> <span class="n">GraphObjectId</span>
    <span class="n">node_id</span><span class="p">:</span> <span class="n">GraphObjectId</span>
    <span class="n">param_id</span><span class="p">:</span> <span class="n">GraphObjectId</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">AudioParamWillBeDestroyed</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">context_id</span><span class="o">=</span><span class="n">GraphObjectId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;contextId&quot;</span><span class="p">]),</span>
            <span class="n">node_id</span><span class="o">=</span><span class="n">GraphObjectId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;nodeId&quot;</span><span class="p">]),</span>
            <span class="n">param_id</span><span class="o">=</span><span class="n">GraphObjectId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;paramId&quot;</span><span class="p">]),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="NodesConnected">
<a class="viewcode-back" href="../../../nodriver/cdp/web_audio.html#nodriver.cdp.web_audio.NodesConnected">[docs]</a>
<span class="nd">@event_class</span><span class="p">(</span><span class="s2">&quot;WebAudio.nodesConnected&quot;</span><span class="p">)</span>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">NodesConnected</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Notifies that two AudioNodes are connected.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">context_id</span><span class="p">:</span> <span class="n">GraphObjectId</span>
    <span class="n">source_id</span><span class="p">:</span> <span class="n">GraphObjectId</span>
    <span class="n">destination_id</span><span class="p">:</span> <span class="n">GraphObjectId</span>
    <span class="n">source_output_index</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">float</span><span class="p">]</span>
    <span class="n">destination_input_index</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">float</span><span class="p">]</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">NodesConnected</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">context_id</span><span class="o">=</span><span class="n">GraphObjectId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;contextId&quot;</span><span class="p">]),</span>
            <span class="n">source_id</span><span class="o">=</span><span class="n">GraphObjectId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;sourceId&quot;</span><span class="p">]),</span>
            <span class="n">destination_id</span><span class="o">=</span><span class="n">GraphObjectId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;destinationId&quot;</span><span class="p">]),</span>
            <span class="n">source_output_index</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;sourceOutputIndex&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;sourceOutputIndex&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">destination_input_index</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;destinationInputIndex&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;destinationInputIndex&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="NodesDisconnected">
<a class="viewcode-back" href="../../../nodriver/cdp/web_audio.html#nodriver.cdp.web_audio.NodesDisconnected">[docs]</a>
<span class="nd">@event_class</span><span class="p">(</span><span class="s2">&quot;WebAudio.nodesDisconnected&quot;</span><span class="p">)</span>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">NodesDisconnected</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Notifies that AudioNodes are disconnected. The destination can be null, and it means all the outgoing connections from the source are disconnected.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">context_id</span><span class="p">:</span> <span class="n">GraphObjectId</span>
    <span class="n">source_id</span><span class="p">:</span> <span class="n">GraphObjectId</span>
    <span class="n">destination_id</span><span class="p">:</span> <span class="n">GraphObjectId</span>
    <span class="n">source_output_index</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">float</span><span class="p">]</span>
    <span class="n">destination_input_index</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">float</span><span class="p">]</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">NodesDisconnected</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">context_id</span><span class="o">=</span><span class="n">GraphObjectId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;contextId&quot;</span><span class="p">]),</span>
            <span class="n">source_id</span><span class="o">=</span><span class="n">GraphObjectId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;sourceId&quot;</span><span class="p">]),</span>
            <span class="n">destination_id</span><span class="o">=</span><span class="n">GraphObjectId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;destinationId&quot;</span><span class="p">]),</span>
            <span class="n">source_output_index</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;sourceOutputIndex&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;sourceOutputIndex&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
            <span class="n">destination_input_index</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;destinationInputIndex&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;destinationInputIndex&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="NodeParamConnected">
<a class="viewcode-back" href="../../../nodriver/cdp/web_audio.html#nodriver.cdp.web_audio.NodeParamConnected">[docs]</a>
<span class="nd">@event_class</span><span class="p">(</span><span class="s2">&quot;WebAudio.nodeParamConnected&quot;</span><span class="p">)</span>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">NodeParamConnected</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Notifies that an AudioNode is connected to an AudioParam.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">context_id</span><span class="p">:</span> <span class="n">GraphObjectId</span>
    <span class="n">source_id</span><span class="p">:</span> <span class="n">GraphObjectId</span>
    <span class="n">destination_id</span><span class="p">:</span> <span class="n">GraphObjectId</span>
    <span class="n">source_output_index</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">float</span><span class="p">]</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">NodeParamConnected</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">context_id</span><span class="o">=</span><span class="n">GraphObjectId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;contextId&quot;</span><span class="p">]),</span>
            <span class="n">source_id</span><span class="o">=</span><span class="n">GraphObjectId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;sourceId&quot;</span><span class="p">]),</span>
            <span class="n">destination_id</span><span class="o">=</span><span class="n">GraphObjectId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;destinationId&quot;</span><span class="p">]),</span>
            <span class="n">source_output_index</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;sourceOutputIndex&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;sourceOutputIndex&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>



<div class="viewcode-block" id="NodeParamDisconnected">
<a class="viewcode-back" href="../../../nodriver/cdp/web_audio.html#nodriver.cdp.web_audio.NodeParamDisconnected">[docs]</a>
<span class="nd">@event_class</span><span class="p">(</span><span class="s2">&quot;WebAudio.nodeParamDisconnected&quot;</span><span class="p">)</span>
<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">NodeParamDisconnected</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Notifies that an AudioNode is disconnected to an AudioParam.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">context_id</span><span class="p">:</span> <span class="n">GraphObjectId</span>
    <span class="n">source_id</span><span class="p">:</span> <span class="n">GraphObjectId</span>
    <span class="n">destination_id</span><span class="p">:</span> <span class="n">GraphObjectId</span>
    <span class="n">source_output_index</span><span class="p">:</span> <span class="n">typing</span><span class="o">.</span><span class="n">Optional</span><span class="p">[</span><span class="nb">float</span><span class="p">]</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">from_json</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">json</span><span class="p">:</span> <span class="n">T_JSON_DICT</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">NodeParamDisconnected</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span>
            <span class="n">context_id</span><span class="o">=</span><span class="n">GraphObjectId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;contextId&quot;</span><span class="p">]),</span>
            <span class="n">source_id</span><span class="o">=</span><span class="n">GraphObjectId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;sourceId&quot;</span><span class="p">]),</span>
            <span class="n">destination_id</span><span class="o">=</span><span class="n">GraphObjectId</span><span class="o">.</span><span class="n">from_json</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;destinationId&quot;</span><span class="p">]),</span>
            <span class="n">source_output_index</span><span class="o">=</span><span class="p">(</span>
                <span class="nb">float</span><span class="p">(</span><span class="n">json</span><span class="p">[</span><span class="s2">&quot;sourceOutputIndex&quot;</span><span class="p">])</span>
                <span class="k">if</span> <span class="n">json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;sourceOutputIndex&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
                <span class="k">else</span> <span class="kc">None</span>
            <span class="p">),</span>
        <span class="p">)</span></div>

</pre></div>
        </article>
      </div>
      <footer>
        
        <div class="related-pages">
          
          
        </div>
        <div class="bottom-of-page">
          <div class="left-details">
            <div class="copyright">
                Copyright &#169; 2023, Author
            </div>
            Made with <a href="https://www.sphinx-doc.org/">Sphinx</a> and <a class="muted-link" href="https://pradyunsg.me">@pradyunsg</a>'s
            
            <a href="https://github.com/pradyunsg/furo">Furo</a>
            
          </div>
          <div class="right-details">
            
          </div>
        </div>
        
      </footer>
    </div>
    <aside class="toc-drawer no-toc">
      
      
      
    </aside>
  </div>
</div><script src="../../../_static/documentation_options.js?v=5929fcd5"></script>
    <script src="../../../_static/doctools.js?v=888ff710"></script>
    <script src="../../../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../../../_static/scripts/furo.js?v=32e29ea5"></script>
    </body>
</html>